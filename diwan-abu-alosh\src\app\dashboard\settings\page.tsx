'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Settings,
  Palette,
  Bell,
  Shield,
  Database,
  RotateCcw,
  Save,
  AlertCircle,
  Info
} from 'lucide-react'
import { toast } from 'sonner'

// مكونات الإعدادات
import GeneralSettings from '@/components/settings/general-settings'
import AppearanceSettings from '@/components/settings/appearance-settings'
import NotificationSettings from '@/components/settings/notification-settings'
import SecuritySettings from '@/components/settings/security-settings'
import BackupSettings from '@/components/settings/backup-settings'
import AdvancedSettings from '@/components/settings/advanced-settings'

interface SettingsData {
  general: Record<string, unknown>
  appearance: Record<string, unknown>
  notifications: Record<string, unknown>
  security: Record<string, unknown>
  backup: Record<string, unknown>
  advanced: Record<string, unknown>
}

export default function SettingsPage() {
  const { data: session } = useSession()
  const [activeTab, setActiveTab] = useState('general')
  const [settings, setSettings] = useState<SettingsData>({
    general: {},
    appearance: {},
    notifications: {},
    security: {},
    backup: {},
    advanced: {}
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)

  // التحقق من الصلاحيات
  const canEdit = session?.user?.role === 'ADMIN'

  // تحميل الإعدادات
  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/settings')
      if (response.ok) {
        const data = await response.json()
        setSettings(data)
      }
    } catch (error) {
      console.error('خطأ في تحميل الإعدادات:', error)
      toast.error('فشل في تحميل الإعدادات')
    } finally {
      setLoading(false)
    }
  }

  const saveSettings = async () => {
    if (!canEdit) {
      toast.error('ليس لديك صلاحية لحفظ الإعدادات')
      return
    }

    try {
      setSaving(true)
      const response = await fetch('/api/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      })

      if (response.ok) {
        toast.success('تم حفظ الإعدادات بنجاح')
        setHasChanges(false)
        // إعادة تحميل الصفحة لتطبيق التغييرات
        window.location.reload()
      } else {
        throw new Error('فشل في حفظ الإعدادات')
      }
    } catch (error) {
      console.error('خطأ في حفظ الإعدادات:', error)
      toast.error('فشل في حفظ الإعدادات')
    } finally {
      setSaving(false)
    }
  }

  const resetSettings = async () => {
    if (!canEdit) {
      toast.error('ليس لديك صلاحية لإعادة تعيين الإعدادات')
      return
    }

    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟')) {
      try {
        const response = await fetch('/api/settings/reset', {
          method: 'POST',
        })

        if (response.ok) {
          toast.success('تم إعادة تعيين الإعدادات بنجاح')
          loadSettings()
          setHasChanges(false)
        } else {
          throw new Error('فشل في إعادة تعيين الإعدادات')
        }
      } catch (error) {
        console.error('خطأ في إعادة تعيين الإعدادات:', error)
        toast.error('فشل في إعادة تعيين الإعدادات')
      }
    }
  }

  const handleSettingsChange = (category: keyof SettingsData, newSettings: Record<string, unknown>) => {
    setSettings(prev => ({
      ...prev,
      [category]: newSettings
    }))
    setHasChanges(true)
  }

  const handleExportSettings = () => {
    const dataStr = JSON.stringify(settings, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `diwan-settings-${new Date().toISOString().split('T')[0]}.json`
    link.click()
    URL.revokeObjectURL(url)
  }

  const handleImportSettings = (file: File) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const importedSettings = JSON.parse(e.target?.result as string)
        setSettings(importedSettings)
        setHasChanges(true)
        toast.success('تم استيراد الإعدادات بنجاح')
      } catch {
        toast.error('خطأ في ملف الإعدادات')
      }
    }
    reader.readAsText(file)
  }

  const tabs = [
    {
      id: 'general',
      label: 'الإعدادات العامة',
      icon: Settings,
      component: GeneralSettings,
      description: 'إعدادات النظام الأساسية'
    },
    {
      id: 'appearance',
      label: 'المظهر والواجهة',
      icon: Palette,
      component: AppearanceSettings,
      description: 'تخصيص الألوان والخطوط'
    },
    {
      id: 'notifications',
      label: 'الإشعارات',
      icon: Bell,
      component: NotificationSettings,
      description: 'إعدادات الإشعارات والتنبيهات'
    },
    {
      id: 'security',
      label: 'الأمان والصلاحيات',
      icon: Shield,
      component: SecuritySettings,
      description: 'إعدادات الأمان وكلمات المرور',
      adminOnly: true
    },
    {
      id: 'backup',
      label: 'النسخ الاحتياطي',
      icon: Database,
      component: BackupSettings,
      description: 'إدارة النسخ الاحتياطية والاستيراد/التصدير',
      adminOnly: true
    },
    {
      id: 'advanced',
      label: 'إعدادات متقدمة',
      icon: Settings,
      component: AdvancedSettings,
      description: 'إعدادات متقدمة وسجل التغييرات',
      adminOnly: true
    }
  ]

  const availableTabs = tabs.filter(tab => !tab.adminOnly || canEdit)

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">الإعدادات</h1>
          <p className="text-gray-600">إدارة إعدادات النظام والتخصيص</p>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="settings-page space-y-6 p-6">
      {/* Header and buttons */}
      <div className="settings-card diwan-card">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="settings-header-icon">
              <Settings className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">إعدادات النظام</h1>
              <p className="text-gray-600 mt-1">إدارة وتخصيص إعدادات ديوان آل أبو علوش</p>
            </div>
          </div>

          {canEdit && (
            <div className="flex items-center gap-3">
              {hasChanges && (
                <Badge className="bg-amber-100 text-amber-800 border-amber-200">
                  <AlertCircle className="w-3 h-3 ml-1" />
                  تغييرات غير محفوظة
                </Badge>
              )}

              <Button
                variant="outline"
                onClick={resetSettings}
                disabled={saving}
                className="text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300"
              >
                <RotateCcw className="w-4 h-4 ml-2" />
                إعادة تعيين
              </Button>

              <Button
                onClick={saveSettings}
                disabled={saving || !hasChanges}
                className="settings-button bg-gradient-to-r from-sky-500 to-sky-600 hover:from-sky-600 hover:to-sky-700 text-white shadow-lg"
              >
                {saving ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                ) : (
                  <Save className="w-4 h-4 ml-2" />
                )}
                حفظ التغييرات
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* No permissions message */}
      {!canEdit && (
        <div className="diwan-card border-amber-200 bg-gradient-to-r from-amber-50 to-orange-50">
          <div className="flex items-center gap-4">
            <div className="p-2 bg-amber-100 rounded-lg">
              <Info className="w-5 h-5 text-amber-600" />
            </div>
            <div>
              <p className="text-amber-800 font-semibold">وضع العرض فقط</p>
              <p className="text-amber-700 text-sm mt-1">
                ليس لديك صلاحية لتعديل الإعدادات. يمكنك عرض الإعدادات الحالية فقط.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="settings-card diwan-card">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="settings-tabs grid w-full grid-cols-3 lg:grid-cols-6 gap-2 p-2">
            {availableTabs.map((tab) => {
              const Icon = tab.icon
              return (
                <TabsTrigger
                  key={tab.id}
                  value={tab.id}
                  className="settings-tab-trigger flex items-center gap-2 text-sm px-4 py-3"
                >
                  <Icon className="w-4 h-4" />
                  <span className="hidden sm:inline font-medium">{tab.label}</span>
                </TabsTrigger>
              )
            })}
          </TabsList>

          {availableTabs.map((tab) => {
            const Component = tab.component
            return (
              <TabsContent key={tab.id} value={tab.id} className="mt-6">
                <div className="space-y-6">
                  {/* Tab header */}
                  <div className="flex items-center gap-4 pb-4 border-b border-gray-200">
                    <div className="p-2 bg-gradient-to-r from-sky-500 to-sky-600 rounded-lg">
                      <tab.icon className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-gray-900">{tab.label}</h2>
                      <p className="text-gray-600 text-sm">{tab.description}</p>
                    </div>
                  </div>

                  {/* Tab content */}
                  <div>
                    <Component
                      settings={settings[tab.id as keyof SettingsData]}
                      onChange={(newSettings: Record<string, unknown>) => handleSettingsChange(tab.id as keyof SettingsData, newSettings)}
                      onExportSettings={handleExportSettings}
                      onImportSettings={handleImportSettings}
                      onResetSettings={resetSettings}
                      canEdit={canEdit}
                    />
                  </div>
                </div>
              </TabsContent>
            )
          })}
        </Tabs>
      </div>
    </div>
  )
}
