'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Users, Camera, Settings, User } from 'lucide-react'

interface User {
  id: string
  name: string
  email: string
  role: string
}

interface Member {
  id: string
  name: string
}

interface UserPermissions {
  id?: string
  userId: string
  // صلاحيات عامة
  canViewAllMembers: boolean
  canEditMembers: boolean
  canDeleteMembers: boolean
  canViewAllIncomes: boolean
  canEditIncomes: boolean
  canDeleteIncomes: boolean
  canViewAllExpenses: boolean
  canEditExpenses: boolean
  canDeleteExpenses: boolean
  canViewGallery: boolean
  canUploadToGallery: boolean
  canDeleteFromGallery: boolean
  canViewReports: boolean
  canExportData: boolean
  canManageUsers: boolean
  canManageSettings: boolean
  // صلاحيات محددة
  specificMemberId?: string
  canViewMemberAccount: boolean
  canViewMemberDetails: boolean
  galleryReadOnly: boolean
  canCreateGalleryFolders: boolean
  user?: User
  specificMember?: Member
}

interface DetailedPermissionsProps {
  users: User[]
  onClose: () => void
}

export default function DetailedPermissions({ users, onClose }: DetailedPermissionsProps) {
  const [selectedUserId, setSelectedUserId] = useState<string>('')
  const [members, setMembers] = useState<Member[]>([])
  const [permissions, setPermissions] = useState<UserPermissions>({
    userId: '',
    canViewAllMembers: false,
    canEditMembers: false,
    canDeleteMembers: false,
    canViewAllIncomes: false,
    canEditIncomes: false,
    canDeleteIncomes: false,
    canViewAllExpenses: false,
    canEditExpenses: false,
    canDeleteExpenses: false,
    canViewGallery: false,
    canUploadToGallery: false,
    canDeleteFromGallery: false,
    canViewReports: false,
    canExportData: false,
    canManageUsers: false,
    canManageSettings: false,
    canViewMemberAccount: false,
    canViewMemberDetails: false,
    galleryReadOnly: true,
    canCreateGalleryFolders: false
  })
  const [loading, setLoading] = useState(false)

  // جلب قائمة الأعضاء
  useEffect(() => {
    const fetchMembers = async () => {
      try {
        const response = await fetch('/api/members?limit=1000')
        if (response.ok) {
          const data = await response.json()
          setMembers(data.members || data)
        }
      } catch (error) {
        console.error('خطأ في جلب الأعضاء:', error)
      }
    }
    fetchMembers()
  }, [])

  // جلب صلاحيات المستخدم المحدد
  useEffect(() => {
    if (selectedUserId) {
      fetchUserPermissions(selectedUserId)
    }
  }, [selectedUserId])

  const fetchUserPermissions = async (userId: string) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/user-permissions?userId=${userId}`)
      if (response.ok) {
        const data = await response.json()
        if (data) {
          setPermissions(data)
        } else {
          // إنشاء صلاحيات افتراضية
          setPermissions({
            userId,
            canViewAllMembers: false,
            canEditMembers: false,
            canDeleteMembers: false,
            canViewAllIncomes: false,
            canEditIncomes: false,
            canDeleteIncomes: false,
            canViewAllExpenses: false,
            canEditExpenses: false,
            canDeleteExpenses: false,
            canViewGallery: false,
            canUploadToGallery: false,
            canDeleteFromGallery: false,
            canViewReports: false,
            canExportData: false,
            canManageUsers: false,
            canManageSettings: false,
            canViewMemberAccount: false,
            canViewMemberDetails: false,
            galleryReadOnly: true,
            canCreateGalleryFolders: false
          })
        }
      }
    } catch (error) {
      console.error('خطأ في جلب صلاحيات المستخدم:', error)
    } finally {
      setLoading(false)
    }
  }

  const handlePermissionChange = (key: keyof UserPermissions, value: boolean | string) => {
    setPermissions(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleSave = async () => {
    if (!selectedUserId) {
      alert('يرجى اختيار مستخدم')
      return
    }

    try {
      setLoading(true)
      const response = await fetch('/api/admin/user-permissions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: selectedUserId,
          permissions: {
            canViewAllMembers: permissions.canViewAllMembers,
            canEditMembers: permissions.canEditMembers,
            canDeleteMembers: permissions.canDeleteMembers,
            canViewAllIncomes: permissions.canViewAllIncomes,
            canEditIncomes: permissions.canEditIncomes,
            canDeleteIncomes: permissions.canDeleteIncomes,
            canViewAllExpenses: permissions.canViewAllExpenses,
            canEditExpenses: permissions.canEditExpenses,
            canDeleteExpenses: permissions.canDeleteExpenses,
            canViewGallery: permissions.canViewGallery,
            canUploadToGallery: permissions.canUploadToGallery,
            canDeleteFromGallery: permissions.canDeleteFromGallery,
            canViewReports: permissions.canViewReports,
            canExportData: permissions.canExportData,
            canManageUsers: permissions.canManageUsers,
            canManageSettings: permissions.canManageSettings,
            specificMemberId: permissions.specificMemberId || null,
            canViewMemberAccount: permissions.canViewMemberAccount,
            canViewMemberDetails: permissions.canViewMemberDetails,
            galleryReadOnly: permissions.galleryReadOnly,
            canCreateGalleryFolders: permissions.canCreateGalleryFolders
          }
        }),
      })

      if (response.ok) {
        alert('تم حفظ الصلاحيات بنجاح')
      } else {
        const error = await response.json()
        alert(error.message || 'فشل في حفظ الصلاحيات')
      }
    } catch (error) {
      console.error('خطأ في حفظ الصلاحيات:', error)
      alert('حدث خطأ أثناء حفظ الصلاحيات')
    } finally {
      setLoading(false)
    }
  }

  const selectedUser = users.find(u => u.id === selectedUserId)

  return (
    <Card className="border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-indigo-50 shadow-xl">
      <CardHeader className="bg-gradient-to-r from-purple-500 to-indigo-600 text-white rounded-t-lg">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-white bg-opacity-20 rounded-lg">
              <Settings className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold">إدارة الصلاحيات المفصلة</h3>
              <p className="text-purple-100 text-sm mt-1">تحديد صلاحيات دقيقة لكل مستخدم</p>
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={onClose}
            className="bg-white bg-opacity-20 border-white border-opacity-30 text-white hover:bg-white hover:bg-opacity-30"
          >
            إغلاق
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6 p-6">
        {/* اختيار المستخدم */}
        <div className="space-y-2">
          <Label>اختيار المستخدم</Label>
          <Select value={selectedUserId} onValueChange={setSelectedUserId}>
            <SelectTrigger>
              <SelectValue placeholder="اختر مستخدماً لتعديل صلاحياته" />
            </SelectTrigger>
            <SelectContent>
              {users.filter(u => u.role !== 'ADMIN').map((user) => (
                <SelectItem key={user.id} value={user.id}>
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4" />
                    <span>{user.name}</span>
                    <Badge variant="outline" className="text-xs">
                      {user.role === 'DATA_ENTRY' ? 'مدخل بيانات' : 
                       user.role === 'VIEWER' ? 'مطلع' :
                       user.role === 'MEMBER_VIEWER' ? 'مطلع على عضو' :
                       user.role === 'GALLERY_VIEWER' ? 'مطلع على المعرض' : user.role}
                    </Badge>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {selectedUser && (
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <h4 className="font-semibold text-gray-900 mb-2">المستخدم المحدد:</h4>
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                <User className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <p className="font-medium">{selectedUser.name}</p>
                <p className="text-sm text-gray-600">{selectedUser.email}</p>
              </div>
            </div>
          </div>
        )}

        {selectedUserId && !loading && (
          <div className="space-y-6">
            {/* صلاحيات الأعضاء */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Users className="w-5 h-5 text-blue-600" />
                  صلاحيات الأعضاء
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center justify-between">
                    <Label>عرض جميع الأعضاء</Label>
                    <Switch
                      checked={permissions.canViewAllMembers}
                      onCheckedChange={(checked) => handlePermissionChange('canViewAllMembers', checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label>تعديل الأعضاء</Label>
                    <Switch
                      checked={permissions.canEditMembers}
                      onCheckedChange={(checked) => handlePermissionChange('canEditMembers', checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label>حذف الأعضاء</Label>
                    <Switch
                      checked={permissions.canDeleteMembers}
                      onCheckedChange={(checked) => handlePermissionChange('canDeleteMembers', checked)}
                    />
                  </div>
                </div>

                {/* صلاحية عضو محدد */}
                <div className="border-t pt-4">
                  <h5 className="font-medium mb-3">صلاحيات عضو محدد</h5>
                  <div className="space-y-3">
                    <div className="space-y-2">
                      <Label>اختيار عضو محدد (اختياري)</Label>
                      <Select 
                        value={permissions.specificMemberId || ''} 
                        onValueChange={(value) => handlePermissionChange('specificMemberId', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="اختر عضواً محدداً" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">لا يوجد عضو محدد</SelectItem>
                          {members.map((member) => (
                            <SelectItem key={member.id} value={member.id}>
                              {member.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center justify-between">
                        <Label>عرض كشف حساب العضو</Label>
                        <Switch
                          checked={permissions.canViewMemberAccount}
                          onCheckedChange={(checked) => handlePermissionChange('canViewMemberAccount', checked)}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label>عرض تفاصيل العضو</Label>
                        <Switch
                          checked={permissions.canViewMemberDetails}
                          onCheckedChange={(checked) => handlePermissionChange('canViewMemberDetails', checked)}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* صلاحيات المعرض */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Camera className="w-5 h-5 text-green-600" />
                  صلاحيات المعرض
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center justify-between">
                    <Label>عرض المعرض</Label>
                    <Switch
                      checked={permissions.canViewGallery}
                      onCheckedChange={(checked) => handlePermissionChange('canViewGallery', checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label>المعرض للقراءة فقط</Label>
                    <Switch
                      checked={permissions.galleryReadOnly}
                      onCheckedChange={(checked) => handlePermissionChange('galleryReadOnly', checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label>رفع صور للمعرض</Label>
                    <Switch
                      checked={permissions.canUploadToGallery}
                      onCheckedChange={(checked) => handlePermissionChange('canUploadToGallery', checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label>إنشاء مجلدات</Label>
                    <Switch
                      checked={permissions.canCreateGalleryFolders}
                      onCheckedChange={(checked) => handlePermissionChange('canCreateGalleryFolders', checked)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* أزرار الحفظ */}
            <div className="flex justify-end gap-3 pt-4">
              <Button variant="outline" onClick={onClose}>
                إلغاء
              </Button>
              <Button 
                onClick={handleSave} 
                disabled={loading}
                className="bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 text-white"
              >
                {loading ? 'جاري الحفظ...' : 'حفظ الصلاحيات'}
              </Button>
            </div>
          </div>
        )}

        {loading && (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
