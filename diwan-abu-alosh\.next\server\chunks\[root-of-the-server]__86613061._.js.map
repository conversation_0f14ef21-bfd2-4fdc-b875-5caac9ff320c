{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Cred<PERSON><PERSON><PERSON><PERSON>ider from 'next-auth/providers/credentials'\nimport bcrypt from 'bcryptjs'\nimport { prisma } from './prisma'\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'البريد الإلكتروني', type: 'email' },\n        password: { label: 'كلمة المرور', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          role: user.role,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAqB,MAAM;gBAAQ;gBACnD,UAAU;oBAAE,OAAO;oBAAe,MAAM;gBAAW;YACrD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;YACxB;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;YAChC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/app/api/gallery-folders/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\nimport { z } from 'zod'\n\n// Schema للتحقق من صحة البيانات\nconst galleryFolderSchema = z.object({\n  title: z.string().min(1, 'العنوان مطلوب'),\n  description: z.string().optional(),\n  location: z.string().optional(),\n  startDate: z.string().optional(),\n  endDate: z.string().optional(),\n})\n\n// GET - جلب جميع المجلدات\nexport async function GET() {\n  try {\n    const session = await getServerSession(authOptions)\n    if (!session) {\n      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })\n    }\n\n    const folders = await prisma.galleryFolder.findMany({\n      include: {\n        creator: {\n          select: {\n            name: true,\n          },\n        },\n        photos: {\n          take: 1,\n          orderBy: { createdAt: 'desc' },\n        },\n        _count: {\n          select: {\n            photos: true,\n          },\n        },\n      },\n      orderBy: { createdAt: 'desc' },\n    })\n\n    // تحويل البيانات لتتناسب مع واجهة المعرض\n    const formattedFolders = folders.map(folder => ({\n      id: folder.id,\n      title: folder.title,\n      description: folder.description,\n      photosCount: folder._count.photos,\n      coverPhoto: folder.photos[0] || null,\n      type: 'folder' as const,\n      location: folder.location,\n      startDate: folder.startDate,\n      endDate: folder.endDate,\n      createdAt: folder.createdAt,\n      creator: folder.creator,\n    }))\n\n    return NextResponse.json({ folders: formattedFolders })\n  } catch (error) {\n    console.error('خطأ في جلب المجلدات:', error)\n    return NextResponse.json(\n      { error: 'حدث خطأ في جلب المجلدات' },\n      { status: 500 }\n    )\n  }\n}\n\n// POST - إنشاء مجلد جديد\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    if (!session) {\n      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })\n    }\n\n    // التحقق من الصلاحيات\n    if (session.user.role === 'VIEWER') {\n      return NextResponse.json({ error: 'ليس لديك صلاحية لإنشاء مجلدات' }, { status: 403 })\n    }\n\n    const body = await request.json()\n    \n    // التحقق من صحة البيانات\n    const validatedData = galleryFolderSchema.parse(body)\n\n    // تحويل التواريخ إذا كانت موجودة\n    const folderData: Record<string, unknown> = {\n      title: validatedData.title,\n      description: validatedData.description,\n      location: validatedData.location,\n      createdBy: session.user.id,\n    }\n\n    if (validatedData.startDate) {\n      folderData.startDate = new Date(validatedData.startDate)\n    }\n\n    if (validatedData.endDate) {\n      folderData.endDate = new Date(validatedData.endDate)\n    } else if (validatedData.startDate) {\n      // إذا لم يتم تحديد تاريخ النهاية، استخدم تاريخ البداية\n      folderData.endDate = new Date(validatedData.startDate)\n    }\n\n    // إنشاء المجلد في قاعدة البيانات\n    const folder = await prisma.galleryFolder.create({\n      data: folderData,\n      include: {\n        creator: {\n          select: {\n            name: true,\n          },\n        },\n        _count: {\n          select: {\n            photos: true,\n          },\n        },\n      },\n    })\n\n    // تحويل البيانات لتتناسب مع واجهة المعرض\n    const formattedFolder = {\n      id: folder.id,\n      title: folder.title,\n      description: folder.description,\n      photosCount: folder._count.photos,\n      coverPhoto: null,\n      type: 'folder' as const,\n      location: folder.location,\n      startDate: folder.startDate,\n      endDate: folder.endDate,\n      createdAt: folder.createdAt,\n      creator: folder.creator,\n    }\n\n    return NextResponse.json(formattedFolder, { status: 201 })\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { error: 'بيانات غير صحيحة', details: error.errors },\n        { status: 400 }\n      )\n    }\n\n    console.error('خطأ في إنشاء المجلد:', error)\n    return NextResponse.json(\n      { error: 'حدث خطأ في إنشاء المجلد' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;;;;;;AAEA,gCAAgC;AAChC,MAAM,sBAAsB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC9B;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAW,GAAG;gBAAE,QAAQ;YAAI;QAChE;QAEA,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;YAClD,SAAS;gBACP,SAAS;oBACP,QAAQ;wBACN,MAAM;oBACR;gBACF;gBACA,QAAQ;oBACN,MAAM;oBACN,SAAS;wBAAE,WAAW;oBAAO;gBAC/B;gBACA,QAAQ;oBACN,QAAQ;wBACN,QAAQ;oBACV;gBACF;YACF;YACA,SAAS;gBAAE,WAAW;YAAO;QAC/B;QAEA,yCAAyC;QACzC,MAAM,mBAAmB,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;gBAC9C,IAAI,OAAO,EAAE;gBACb,OAAO,OAAO,KAAK;gBACnB,aAAa,OAAO,WAAW;gBAC/B,aAAa,OAAO,MAAM,CAAC,MAAM;gBACjC,YAAY,OAAO,MAAM,CAAC,EAAE,IAAI;gBAChC,MAAM;gBACN,UAAU,OAAO,QAAQ;gBACzB,WAAW,OAAO,SAAS;gBAC3B,SAAS,OAAO,OAAO;gBACvB,WAAW,OAAO,SAAS;gBAC3B,SAAS,OAAO,OAAO;YACzB,CAAC;QAED,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAiB;IACvD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA0B,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAW,GAAG;gBAAE,QAAQ;YAAI;QAChE;QAEA,sBAAsB;QACtB,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,UAAU;YAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAgC,GAAG;gBAAE,QAAQ;YAAI;QACrF;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,yBAAyB;QACzB,MAAM,gBAAgB,oBAAoB,KAAK,CAAC;QAEhD,iCAAiC;QACjC,MAAM,aAAsC;YAC1C,OAAO,cAAc,KAAK;YAC1B,aAAa,cAAc,WAAW;YACtC,UAAU,cAAc,QAAQ;YAChC,WAAW,QAAQ,IAAI,CAAC,EAAE;QAC5B;QAEA,IAAI,cAAc,SAAS,EAAE;YAC3B,WAAW,SAAS,GAAG,IAAI,KAAK,cAAc,SAAS;QACzD;QAEA,IAAI,cAAc,OAAO,EAAE;YACzB,WAAW,OAAO,GAAG,IAAI,KAAK,cAAc,OAAO;QACrD,OAAO,IAAI,cAAc,SAAS,EAAE;YAClC,uDAAuD;YACvD,WAAW,OAAO,GAAG,IAAI,KAAK,cAAc,SAAS;QACvD;QAEA,iCAAiC;QACjC,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YAC/C,MAAM;YACN,SAAS;gBACP,SAAS;oBACP,QAAQ;wBACN,MAAM;oBACR;gBACF;gBACA,QAAQ;oBACN,QAAQ;wBACN,QAAQ;oBACV;gBACF;YACF;QACF;QAEA,yCAAyC;QACzC,MAAM,kBAAkB;YACtB,IAAI,OAAO,EAAE;YACb,OAAO,OAAO,KAAK;YACnB,aAAa,OAAO,WAAW;YAC/B,aAAa,OAAO,MAAM,CAAC,MAAM;YACjC,YAAY;YACZ,MAAM;YACN,UAAU,OAAO,QAAQ;YACzB,WAAW,OAAO,SAAS;YAC3B,SAAS,OAAO,OAAO;YACvB,WAAW,OAAO,SAAS;YAC3B,SAAS,OAAO,OAAO;QACzB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,iBAAiB;YAAE,QAAQ;QAAI;IAC1D,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,mLAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAoB,SAAS,MAAM,MAAM;YAAC,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA0B,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}