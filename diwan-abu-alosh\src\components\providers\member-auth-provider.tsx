'use client'

// import { createContext, useContext } from 'react'
import { MemberAuthContext, useMemberAuthState } from '@/hooks/use-member-auth'

interface MemberAuthProviderProps {
  children: React.ReactNode
}

export function MemberAuthProvider({ children }: MemberAuthProviderProps) {
  const authState = useMemberAuthState()

  return (
    <MemberAuthContext.Provider value={authState}>
      {children}
    </MemberAuthContext.Provider>
  )
}
