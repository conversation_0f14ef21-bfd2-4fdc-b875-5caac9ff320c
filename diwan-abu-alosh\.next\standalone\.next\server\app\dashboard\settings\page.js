(()=>{var e={};e.id=4631,e.ids=[4631],e.modules={3004:(e,s,a)=>{Promise.resolve().then(a.bind(a,62623))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},8819:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},13861:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},16023:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34729:(e,s,a)=>{"use strict";a.d(s,{T:()=>r});var l=a(60687),i=a(43210),t=a(4780);let r=i.forwardRef(({className:e,...s},a)=>(0,l.jsx)("textarea",{className:(0,t.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...s}));r.displayName="Textarea"},41550:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},41760:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>eW});var l=a(60687),i=a(43210),t=a(82136),r=a(85763),c=a(29523),n=a(96834),d=a(84027),o=a(62688);let x=(0,o.A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]);var h=a(97051),m=a(99891);let j=(0,o.A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);var p=a(93613);let u=(0,o.A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);var g=a(8819),b=a(96882),y=a(52581),v=a(89667),N=a(80013),f=a(34729),w=a(15079),C=a(4780);let k=i.forwardRef(({className:e,checked:s,onCheckedChange:a,...i},t)=>(0,l.jsx)("button",{ref:t,type:"button",role:"switch","aria-checked":s,onClick:()=>a?.(!s),className:(0,C.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50",s?"bg-primary":"bg-input",e),...i,children:(0,l.jsx)("span",{className:(0,C.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform",s?"translate-x-5":"translate-x-0")})}));k.displayName="Switch";var S=a(17313);let A=(0,o.A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var E=a(10022),J=a(40228);let R={diwanName:"ديوان آل أبو علوش",diwanDescription:"نظام إدارة ديوان آل أبو علوش",diwanAddress:"",diwanPhone:"",diwanEmail:"",diwanWebsite:"",defaultCurrency:"JOD",currencySymbol:"د.أ",timezone:"Asia/Amman",dateFormat:"DD/MM/YYYY",timeFormat:"24h",language:"ar",itemsPerPage:10,autoSave:!0,enableAuditLog:!0,sessionTimeout:30,showWelcomeMessage:!0,welcomeMessage:"مرحباً بك في ديوان آل أبو علوش",enableDashboardStats:!0,enableQuickActions:!0},D=[{value:"JOD",label:"دينار أردني (JOD)",symbol:"د.أ"},{value:"USD",label:"دولار أمريكي (USD)",symbol:"$"},{value:"EUR",label:"يورو (EUR)",symbol:"€"},{value:"SAR",label:"ريال سعودي (SAR)",symbol:"ر.س"},{value:"AED",label:"درهم إماراتي (AED)",symbol:"د.إ"},{value:"KWD",label:"دينار كويتي (KWD)",symbol:"د.ك"},{value:"QAR",label:"ريال قطري (QAR)",symbol:"ر.ق"},{value:"BHD",label:"دينار بحريني (BHD)",symbol:"د.ب"}],M=[{value:"Asia/Amman",label:"عمان (UTC+3)"},{value:"Asia/Riyadh",label:"الرياض (UTC+3)"},{value:"Asia/Dubai",label:"دبي (UTC+4)"},{value:"Asia/Kuwait",label:"الكويت (UTC+3)"},{value:"Asia/Qatar",label:"الدوحة (UTC+3)"},{value:"Asia/Bahrain",label:"المنامة (UTC+3)"}],P=[{value:"DD/MM/YYYY",label:"يوم/شهر/سنة (31/12/2024)"},{value:"MM/DD/YYYY",label:"شهر/يوم/سنة (12/31/2024)"},{value:"YYYY-MM-DD",label:"سنة-شهر-يوم (2024-12-31)"},{value:"DD-MM-YYYY",label:"يوم-شهر-سنة (31-12-2024)"}];function q({settings:e,onChange:s,canEdit:a}){let[t,r]=(0,i.useState)(R),c=(e,a)=>{let l={...t,[e]:a};r(l),s(l)};return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"diwan-card",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,l.jsx)("div",{className:"p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg",children:(0,l.jsx)(S.A,{className:"w-5 h-5 text-white"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"معلومات الديوان الأساسية"})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"diwanName",children:"اسم الديوان"}),(0,l.jsx)(v.p,{id:"diwanName",value:t.diwanName,onChange:e=>c("diwanName",e.target.value),disabled:!a,placeholder:"اسم الديوان"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"diwanEmail",children:"البريد الإلكتروني"}),(0,l.jsx)(v.p,{id:"diwanEmail",type:"email",value:t.diwanEmail,onChange:e=>c("diwanEmail",e.target.value),disabled:!a,placeholder:"<EMAIL>"})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"diwanDescription",children:"وصف الديوان"}),(0,l.jsx)(f.T,{id:"diwanDescription",value:t.diwanDescription,onChange:e=>c("diwanDescription",e.target.value),disabled:!a,placeholder:"وصف مختصر عن الديوان",rows:3})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"diwanPhone",children:"رقم الهاتف"}),(0,l.jsx)(v.p,{id:"diwanPhone",value:t.diwanPhone,onChange:e=>c("diwanPhone",e.target.value),disabled:!a,placeholder:"+962 6 1234567"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"diwanWebsite",children:"الموقع الإلكتروني"}),(0,l.jsx)(v.p,{id:"diwanWebsite",value:t.diwanWebsite,onChange:e=>c("diwanWebsite",e.target.value),disabled:!a,placeholder:"https://example.com"})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"diwanAddress",children:"العنوان"}),(0,l.jsx)(f.T,{id:"diwanAddress",value:t.diwanAddress,onChange:e=>c("diwanAddress",e.target.value),disabled:!a,placeholder:"العنوان الكامل للديوان",rows:2})]})]})]}),(0,l.jsxs)("div",{className:"diwan-card",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,l.jsx)("div",{className:"p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg",children:(0,l.jsx)(A,{className:"w-5 h-5 text-white"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"الإعدادات الإقليمية"})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"العملة الافتراضية"}),(0,l.jsxs)(w.l6,{value:t.defaultCurrency,onValueChange:e=>{let s=D.find(s=>s.value===e);s&&(c("defaultCurrency",e),c("currencySymbol",s.symbol))},disabled:!a,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{placeholder:"اختر العملة"})}),(0,l.jsx)(w.gC,{children:D.map(e=>(0,l.jsx)(w.eb,{value:e.value,children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("span",{children:e.symbol}),(0,l.jsx)("span",{children:e.label})]})},e.value))})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"المنطقة الزمنية"}),(0,l.jsxs)(w.l6,{value:t.timezone,onValueChange:e=>c("timezone",e),disabled:!a,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{placeholder:"اختر المنطقة الزمنية"})}),(0,l.jsx)(w.gC,{children:M.map(e=>(0,l.jsx)(w.eb,{value:e.value,children:e.label},e.value))})]})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"تنسيق التاريخ"}),(0,l.jsxs)(w.l6,{value:t.dateFormat,onValueChange:e=>c("dateFormat",e),disabled:!a,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{placeholder:"اختر تنسيق التاريخ"})}),(0,l.jsx)(w.gC,{children:P.map(e=>(0,l.jsx)(w.eb,{value:e.value,children:e.label},e.value))})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"تنسيق الوقت"}),(0,l.jsxs)(w.l6,{value:t.timeFormat,onValueChange:e=>c("timeFormat",e),disabled:!a,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{placeholder:"اختر تنسيق الوقت"})}),(0,l.jsxs)(w.gC,{children:[(0,l.jsx)(w.eb,{value:"12h",children:"12 ساعة (1:30 PM)"}),(0,l.jsx)(w.eb,{value:"24h",children:"24 ساعة (13:30)"})]})]})]})]})]})]}),(0,l.jsxs)("div",{className:"diwan-card",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,l.jsx)("div",{className:"p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg",children:(0,l.jsx)(E.A,{className:"w-5 h-5 text-white"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"إعدادات النظام"})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"itemsPerPage",children:"عدد العناصر في الصفحة"}),(0,l.jsxs)(w.l6,{value:t.itemsPerPage.toString(),onValueChange:e=>c("itemsPerPage",parseInt(e)),disabled:!a,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{})}),(0,l.jsxs)(w.gC,{children:[(0,l.jsx)(w.eb,{value:"5",children:"5 عناصر"}),(0,l.jsx)(w.eb,{value:"10",children:"10 عناصر"}),(0,l.jsx)(w.eb,{value:"20",children:"20 عنصر"}),(0,l.jsx)(w.eb,{value:"50",children:"50 عنصر"}),(0,l.jsx)(w.eb,{value:"100",children:"100 عنصر"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"sessionTimeout",children:"مدة انتهاء الجلسة (دقيقة)"}),(0,l.jsxs)(w.l6,{value:t.sessionTimeout.toString(),onValueChange:e=>c("sessionTimeout",parseInt(e)),disabled:!a,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{})}),(0,l.jsxs)(w.gC,{children:[(0,l.jsx)(w.eb,{value:"15",children:"15 دقيقة"}),(0,l.jsx)(w.eb,{value:"30",children:"30 دقيقة"}),(0,l.jsx)(w.eb,{value:"60",children:"ساعة واحدة"}),(0,l.jsx)(w.eb,{value:"120",children:"ساعتان"}),(0,l.jsx)(w.eb,{value:"480",children:"8 ساعات"})]})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"الحفظ التلقائي"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"حفظ التغييرات تلقائياً أثناء الكتابة"})]}),(0,l.jsx)(k,{checked:t.autoSave,onCheckedChange:e=>c("autoSave",e),disabled:!a})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"سجل العمليات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"تسجيل جميع العمليات والتغييرات"})]}),(0,l.jsx)(k,{checked:t.enableAuditLog,onCheckedChange:e=>c("enableAuditLog",e),disabled:!a})]})]})]})]}),(0,l.jsxs)("div",{className:"diwan-card",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,l.jsx)("div",{className:"p-2 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg",children:(0,l.jsx)(J.A,{className:"w-5 h-5 text-white"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"إعدادات العرض"})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"رسالة الترحيب"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"عرض رسالة ترحيب في لوحة التحكم"})]}),(0,l.jsx)(k,{checked:t.showWelcomeMessage,onCheckedChange:e=>c("showWelcomeMessage",e),disabled:!a})]}),t.showWelcomeMessage&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"welcomeMessage",children:"نص رسالة الترحيب"}),(0,l.jsx)(v.p,{id:"welcomeMessage",value:t.welcomeMessage,onChange:e=>c("welcomeMessage",e.target.value),disabled:!a,placeholder:"مرحباً بك في ديوان آل أبو علوش"})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"إحصائيات لوحة التحكم"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"عرض الإحصائيات في لوحة التحكم"})]}),(0,l.jsx)(k,{checked:t.enableDashboardStats,onCheckedChange:e=>c("enableDashboardStats",e),disabled:!a})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"الإجراءات السريعة"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"عرض أزرار الإجراءات السريعة"})]}),(0,l.jsx)(k,{checked:t.enableQuickActions,onCheckedChange:e=>c("enableQuickActions",e),disabled:!a})]})]})]})]})}var V=a(44493),F=a(56085);let I=(0,o.A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),L=(0,o.A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]),T=(0,o.A)("monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]),U=(0,o.A)("type",[["path",{d:"M12 4v16",key:"1654pz"}],["path",{d:"M4 7V5a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v2",key:"e0r10z"}],["path",{d:"M9 20h6",key:"s66wpe"}]]);var W=a(9005),B=a(16023);let Z=(0,o.A)("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]);var z=a(13861);let $={theme:"light",primaryColor:"#3b82f6",secondaryColor:"#64748b",accentColor:"#f59e0b",backgroundColor:"#ffffff",textColor:"#1f2937",fontFamily:"Cairo",fontSize:"14px",fontWeight:"normal",lineHeight:"1.5",logo:"",favicon:"",brandName:"ديوان آل أبو علوش",brandColors:{primary:"#3b82f6",secondary:"#64748b"},sidebarStyle:"default",headerStyle:"default",cardStyle:"default",buttonStyle:"default",enableAnimations:!0,enableTransitions:!0,enableShadows:!0,enableGradients:!1,customCSS:"",enableCustomCSS:!1},O=[{name:"الأزرق الافتراضي",primary:"#3b82f6",secondary:"#64748b"},{name:"الأخضر الطبيعي",primary:"#10b981",secondary:"#6b7280"},{name:"البنفسجي الملكي",primary:"#8b5cf6",secondary:"#6b7280"},{name:"الأحمر الكلاسيكي",primary:"#ef4444",secondary:"#6b7280"},{name:"البرتقالي الدافئ",primary:"#f97316",secondary:"#6b7280"},{name:"الوردي الناعم",primary:"#ec4899",secondary:"#6b7280"},{name:"الذهبي الفاخر",primary:"#f59e0b",secondary:"#78716c"},{name:"الأزرق الداكن",primary:"#1e40af",secondary:"#4b5563"}],G=[{value:"Cairo",label:"Cairo (الافتراضي)"},{value:"Almarai",label:"Almarai"},{value:"Tajawal",label:"Tajawal"},{value:"Amiri",label:"Amiri"},{value:"Scheherazade",label:"Scheherazade"},{value:"Noto Sans Arabic",label:"Noto Sans Arabic"},{value:"IBM Plex Sans Arabic",label:"IBM Plex Sans Arabic"}],Y=[{value:"12px",label:"صغير (12px)"},{value:"14px",label:"متوسط (14px)"},{value:"16px",label:"كبير (16px)"},{value:"18px",label:"كبير جداً (18px)"}];function _({settings:e,onChange:s,canEdit:a}){let[t,r]=(0,i.useState)($),[n,d]=(0,i.useState)(!1),[o,h]=(0,i.useState)({logo:!1,favicon:!1}),m=(e,a)=>{let l={...t,[e]:a};r(l),s(l),p(e,a),j(e,a)},j=(e,s)=>{try{let a=JSON.parse(localStorage.getItem("diwan-appearance-settings")||"{}");a[e]=s,localStorage.setItem("diwan-appearance-settings",JSON.stringify(a))}catch(e){console.error("خطأ في حفظ الإعدادات:",e)}},p=(e,s)=>{let a=document.documentElement;switch(e){case"primaryColor":a.style.setProperty("--theme-primary-color",s),a.style.setProperty("--primary",s);break;case"secondaryColor":a.style.setProperty("--theme-secondary-color",s),a.style.setProperty("--secondary",s);break;case"accentColor":a.style.setProperty("--theme-accent-color",s),a.style.setProperty("--accent",s);break;case"backgroundColor":a.style.setProperty("--theme-background-color",s),a.style.setProperty("--background",s);break;case"textColor":a.style.setProperty("--theme-text-color",s),a.style.setProperty("--foreground",s);break;case"fontFamily":a.style.setProperty("--theme-font-family",s),document.body.style.fontFamily=s;break;case"fontSize":a.style.setProperty("--theme-font-size",s),document.body.style.fontSize=s;break;case"fontWeight":a.style.setProperty("--theme-font-weight",s),document.body.style.fontWeight=s;break;case"lineHeight":a.style.setProperty("--theme-line-height",s),document.body.style.lineHeight=s;break;case"theme":"dark"===s?(document.documentElement.classList.add("dark"),document.documentElement.classList.add("dark-theme"),document.documentElement.classList.remove("light")):(document.documentElement.classList.add("light"),document.documentElement.classList.remove("dark"),document.documentElement.classList.remove("dark-theme"));break;case"brandName":document.title=s||"ديوان آل أبو علوش"}document.body.classList.add("theme-applied")},g=e=>{let a={...t,primaryColor:e.primary,secondaryColor:e.secondary,brandColors:{primary:e.primary,secondary:e.secondary}};r(a),s(a),p("primaryColor",e.primary),p("secondaryColor",e.secondary)},b=async(e,s)=>{if(!a)return;if(!e.type.startsWith("image/"))return void alert("يرجى اختيار ملف صورة صحيح");if(e.size>5242880)return void alert("حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت");h(e=>({...e,[s]:!0}));let l=new FormData;l.append("file",e),l.append("type",s);try{let e=await fetch("/api/upload",{method:"POST",body:l});if(e.ok){let a=await e.json();m(s,a.url);let l=new CustomEvent("showToast",{detail:{message:"تم رفع الملف بنجاح",type:"success"}});window.dispatchEvent(l)}else{let s=await e.json();alert(s.error||"فشل في رفع الملف")}}catch(e){console.error("خطأ في رفع الملف:",e),alert("حدث خطأ في رفع الملف")}finally{h(e=>({...e,[s]:!1}))}},y=e=>{let s=document.createElement("input");s.type="file",s.accept="image/*",s.onchange=s=>{let a=s.target.files?.[0];a&&b(a,e)},s.click()},f=e=>{e.preventDefault(),e.stopPropagation()},C=(e,s)=>{e.preventDefault(),e.stopPropagation();let a=e.dataTransfer.files;if(a.length>0){let e=a[0];e.type.startsWith("image/")&&b(e,s)}};return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsx)(V.Zp,{className:"border-green-200 bg-gradient-to-r from-green-50 to-emerald-50 animate-pulse",children:(0,l.jsx)(V.Wu,{className:"p-4",children:(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("div",{className:"p-2 bg-green-100 rounded-lg animate-bounce",children:(0,l.jsx)(F.A,{className:"w-5 h-5 text-green-600"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-green-800 font-semibold",children:"\uD83C\uDFA8 المعاينة المباشرة مفعلة"}),(0,l.jsx)("p",{className:"text-green-700 text-sm",children:"جميع التغييرات ستظهر فوراً في الواجهة! جرب تغيير الألوان أو الخطوط لترى النتيجة مباشرة."})]}),(0,l.jsx)("div",{className:"mr-auto",children:(0,l.jsxs)("div",{className:"flex gap-1",children:[(0,l.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full animate-ping"}),(0,l.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full animate-ping",style:{animationDelay:"0.2s"}}),(0,l.jsx)("div",{className:"w-3 h-3 bg-yellow-500 rounded-full animate-ping",style:{animationDelay:"0.4s"}})]})})]})})}),(0,l.jsxs)(V.Zp,{children:[(0,l.jsx)(V.aR,{children:(0,l.jsxs)(V.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(x,{className:"w-5 h-5 text-blue-600"}),"الثيم والألوان"]})}),(0,l.jsxs)(V.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)(N.J,{className:"text-sm font-medium text-gray-700",children:"نمط الثيم"}),(0,l.jsxs)("div",{className:"grid grid-cols-3 gap-3",children:[(0,l.jsxs)(c.$,{variant:"light"===t.theme?"default":"outline",onClick:()=>m("theme","light"),disabled:!a,className:`flex items-center gap-2 py-3 ${"light"===t.theme?"bg-gradient-to-r from-sky-500 to-sky-600 text-white shadow-lg":"hover:bg-sky-50 hover:border-sky-300"}`,children:[(0,l.jsx)(I,{className:"w-4 h-4"}),"فاتح"]}),(0,l.jsxs)(c.$,{variant:"dark"===t.theme?"default":"outline",onClick:()=>m("theme","dark"),disabled:!a,className:`flex items-center gap-2 py-3 ${"dark"===t.theme?"bg-gradient-to-r from-gray-700 to-gray-800 text-white shadow-lg":"hover:bg-gray-50 hover:border-gray-300"}`,children:[(0,l.jsx)(L,{className:"w-4 h-4"}),"داكن"]}),(0,l.jsxs)(c.$,{variant:"system"===t.theme?"default":"outline",onClick:()=>m("theme","system"),disabled:!a,className:`flex items-center gap-2 py-3 ${"system"===t.theme?"bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-lg":"hover:bg-purple-50 hover:border-purple-300"}`,children:[(0,l.jsx)(T,{className:"w-4 h-4"}),"النظام"]})]})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)(N.J,{children:"الألوان المحددة مسبقاً"}),(0,l.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2",children:O.map((e,s)=>(0,l.jsxs)(c.$,{variant:"outline",onClick:()=>g(e),disabled:!a,className:"h-auto p-3 flex flex-col items-center gap-2",children:[(0,l.jsxs)("div",{className:"flex gap-1",children:[(0,l.jsx)("div",{className:"w-4 h-4 rounded-full border",style:{backgroundColor:e.primary}}),(0,l.jsx)("div",{className:"w-4 h-4 rounded-full border",style:{backgroundColor:e.secondary}})]}),(0,l.jsx)("span",{className:"text-xs text-center",children:e.name})]},s))})]}),(0,l.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"primaryColor",children:"اللون الأساسي"}),(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsx)(v.p,{id:"primaryColor",type:"color",value:t.primaryColor,onChange:e=>m("primaryColor",e.target.value),disabled:!a,className:"w-12 h-10 p-1 border rounded"}),(0,l.jsx)(v.p,{value:t.primaryColor,onChange:e=>m("primaryColor",e.target.value),disabled:!a,placeholder:"#3b82f6"})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"secondaryColor",children:"اللون الثانوي"}),(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsx)(v.p,{id:"secondaryColor",type:"color",value:t.secondaryColor,onChange:e=>m("secondaryColor",e.target.value),disabled:!a,className:"w-12 h-10 p-1 border rounded"}),(0,l.jsx)(v.p,{value:t.secondaryColor,onChange:e=>m("secondaryColor",e.target.value),disabled:!a,placeholder:"#64748b"})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"accentColor",children:"لون التمييز"}),(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsx)(v.p,{id:"accentColor",type:"color",value:t.accentColor,onChange:e=>m("accentColor",e.target.value),disabled:!a,className:"w-12 h-10 p-1 border rounded"}),(0,l.jsx)(v.p,{value:t.accentColor,onChange:e=>m("accentColor",e.target.value),disabled:!a,placeholder:"#f59e0b"})]})]})]})]})]}),(0,l.jsxs)(V.Zp,{children:[(0,l.jsx)(V.aR,{children:(0,l.jsxs)(V.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(U,{className:"w-5 h-5 text-green-600"}),"الخطوط والنصوص"]})}),(0,l.jsxs)(V.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"نوع الخط"}),(0,l.jsxs)(w.l6,{value:t.fontFamily,onValueChange:e=>m("fontFamily",e),disabled:!a,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{placeholder:"اختر نوع الخط"})}),(0,l.jsx)(w.gC,{children:G.map(e=>(0,l.jsx)(w.eb,{value:e.value,children:(0,l.jsx)("span",{style:{fontFamily:e.value},children:e.label})},e.value))})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"حجم الخط"}),(0,l.jsxs)(w.l6,{value:t.fontSize,onValueChange:e=>m("fontSize",e),disabled:!a,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{placeholder:"اختر حجم الخط"})}),(0,l.jsx)(w.gC,{children:Y.map(e=>(0,l.jsx)(w.eb,{value:e.value,children:e.label},e.value))})]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"معاينة الخط"}),(0,l.jsxs)("div",{className:"p-4 border rounded-lg bg-gray-50",style:{fontFamily:t.fontFamily,fontSize:t.fontSize,lineHeight:t.lineHeight},children:[(0,l.jsx)("p",{className:"text-lg font-bold mb-2",children:"ديوان آل أبو علوش"}),(0,l.jsx)("p",{className:"mb-2",children:"هذا نص تجريبي لمعاينة الخط المختار. يمكنك رؤية كيف سيظهر النص في النظام."}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"الأرقام: 1234567890"})]})]})]})]}),(0,l.jsxs)(V.Zp,{children:[(0,l.jsx)(V.aR,{children:(0,l.jsxs)(V.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(W.A,{className:"w-5 h-5 text-purple-600"}),"الشعار والعلامة التجارية"]})}),(0,l.jsxs)(V.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"brandName",children:"اسم العلامة التجارية"}),(0,l.jsx)(v.p,{id:"brandName",value:t.brandName,onChange:e=>m("brandName",e.target.value),disabled:!a,placeholder:"ديوان آل أبو علوش"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"الشعار الرئيسي"}),(0,l.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors cursor-pointer",onDragOver:f,onDrop:e=>C(e,"logo"),onClick:()=>y("logo"),children:t.logo?(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("img",{src:t.logo,alt:"الشعار",className:"max-h-16 mx-auto"}),(0,l.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>m("logo",""),disabled:!a,children:"إزالة الشعار"})]}):(0,l.jsx)("div",{className:"space-y-2",children:o.logo?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,l.jsx)("p",{className:"text-sm text-blue-600",children:"جاري رفع الشعار..."})]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(B.A,{className:"w-8 h-8 text-gray-400 mx-auto"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"اسحب الشعار هنا أو انقر للرفع"}),(0,l.jsx)(c.$,{variant:"outline",size:"sm",disabled:!a||o.logo,onClick:e=>{e.stopPropagation(),y("logo")},children:"رفع شعار"})]})})})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"أيقونة الموقع (Favicon)"}),(0,l.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors cursor-pointer",onDragOver:f,onDrop:e=>C(e,"favicon"),onClick:()=>y("favicon"),children:t.favicon?(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("img",{src:t.favicon,alt:"الأيقونة",className:"w-8 h-8 mx-auto"}),(0,l.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>m("favicon",""),disabled:!a,children:"إزالة الأيقونة"})]}):(0,l.jsx)("div",{className:"space-y-2",children:o.favicon?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"}),(0,l.jsx)("p",{className:"text-xs text-blue-600",children:"جاري رفع الأيقونة..."})]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(B.A,{className:"w-6 h-6 text-gray-400 mx-auto"}),(0,l.jsx)("p",{className:"text-xs text-gray-600",children:"32x32 px"}),(0,l.jsx)(c.$,{variant:"outline",size:"sm",disabled:!a||o.favicon,onClick:e=>{e.stopPropagation(),y("favicon")},children:"رفع أيقونة"})]})})})]})]})]})]}),(0,l.jsxs)(V.Zp,{children:[(0,l.jsx)(V.aR,{children:(0,l.jsxs)(V.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(Z,{className:"w-5 h-5 text-orange-600"}),"تخصيص الواجهة"]})}),(0,l.jsxs)(V.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"نمط الشريط الجانبي"}),(0,l.jsxs)(w.l6,{value:t.sidebarStyle,onValueChange:e=>m("sidebarStyle",e),disabled:!a,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{})}),(0,l.jsxs)(w.gC,{children:[(0,l.jsx)(w.eb,{value:"default",children:"افتراضي"}),(0,l.jsx)(w.eb,{value:"compact",children:"مضغوط"}),(0,l.jsx)(w.eb,{value:"minimal",children:"بسيط"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"نمط الرأس"}),(0,l.jsxs)(w.l6,{value:t.headerStyle,onValueChange:e=>m("headerStyle",e),disabled:!a,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{})}),(0,l.jsxs)(w.gC,{children:[(0,l.jsx)(w.eb,{value:"default",children:"افتراضي"}),(0,l.jsx)(w.eb,{value:"compact",children:"مضغوط"}),(0,l.jsx)(w.eb,{value:"transparent",children:"شفاف"})]})]})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"نمط البطاقات"}),(0,l.jsxs)(w.l6,{value:t.cardStyle,onValueChange:e=>m("cardStyle",e),disabled:!a,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{})}),(0,l.jsxs)(w.gC,{children:[(0,l.jsx)(w.eb,{value:"default",children:"افتراضي"}),(0,l.jsx)(w.eb,{value:"bordered",children:"محدد"}),(0,l.jsx)(w.eb,{value:"shadow",children:"ظلال"}),(0,l.jsx)(w.eb,{value:"flat",children:"مسطح"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"نمط الأزرار"}),(0,l.jsxs)(w.l6,{value:t.buttonStyle,onValueChange:e=>m("buttonStyle",e),disabled:!a,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{})}),(0,l.jsxs)(w.gC,{children:[(0,l.jsx)(w.eb,{value:"default",children:"افتراضي"}),(0,l.jsx)(w.eb,{value:"rounded",children:"دائري"}),(0,l.jsx)(w.eb,{value:"square",children:"مربع"})]})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"الحركات والانتقالات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"تفعيل الحركات المتحركة"})]}),(0,l.jsx)(k,{checked:t.enableAnimations,onCheckedChange:e=>m("enableAnimations",e),disabled:!a})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"الظلال"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إضافة ظلال للعناصر"})]}),(0,l.jsx)(k,{checked:t.enableShadows,onCheckedChange:e=>m("enableShadows",e),disabled:!a})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"التدرجات اللونية"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"استخدام التدرجات في الخلفيات"})]}),(0,l.jsx)(k,{checked:t.enableGradients,onCheckedChange:e=>m("enableGradients",e),disabled:!a})]})]})]})]}),(0,l.jsxs)(V.Zp,{children:[(0,l.jsx)(V.aR,{children:(0,l.jsxs)(V.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(z.A,{className:"w-5 h-5 text-red-600"}),"CSS مخصص"]})}),(0,l.jsxs)(V.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"تفعيل CSS مخصص"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إضافة أنماط CSS مخصصة"})]}),(0,l.jsx)(k,{checked:t.enableCustomCSS,onCheckedChange:e=>m("enableCustomCSS",e),disabled:!a})]}),t.enableCustomCSS&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"customCSS",children:"كود CSS المخصص"}),(0,l.jsx)("textarea",{id:"customCSS",value:t.customCSS,onChange:e=>m("customCSS",e.target.value),disabled:!a,placeholder:"/* أضف كود CSS المخصص هنا */",className:"w-full h-32 p-3 border rounded-lg font-mono text-sm"}),(0,l.jsx)("p",{className:"text-xs text-gray-500",children:"تحذير: استخدم CSS مخصص بحذر. قد يؤثر على مظهر النظام."})]})]})]}),a&&(0,l.jsx)(V.Zp,{children:(0,l.jsx)(V.Wu,{className:"p-4",children:(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsxs)(c.$,{variant:"outline",onClick:()=>{r($),s($),Object.entries($).forEach(([e,s])=>{p(e,s)})},className:"text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300",children:[(0,l.jsx)(u,{className:"w-4 h-4 ml-2"}),"إعادة تعيين الافتراضي"]}),(0,l.jsxs)(c.$,{onClick:()=>d(!n),className:"bg-gradient-to-r from-sky-500 to-sky-600 hover:from-sky-600 hover:to-sky-700 text-white",children:[(0,l.jsx)(z.A,{className:"w-4 h-4 ml-2"}),n?"إخفاء المعاينة":"معاينة التغييرات"]})]})})})]})}var H=a(41312),K=a(23928),Q=a(43649),X=a(48730);let ee={enableNotifications:!0,enableSounds:!0,enableDesktopNotifications:!0,enableEmailNotifications:!1,enableSMSNotifications:!1,memberNotifications:{newMember:!0,memberUpdate:!0,memberStatusChange:!0,memberPayment:!0},incomeNotifications:{newIncome:!0,incomeUpdate:!0,paymentReminder:!0,paymentOverdue:!0},expenseNotifications:{newExpense:!0,expenseUpdate:!0,budgetAlert:!0,expenseApproval:!0},systemNotifications:{systemUpdate:!0,securityAlert:!0,backupComplete:!0,errorAlert:!0},quietHours:{enabled:!1,startTime:"22:00",endTime:"08:00"},emailSettings:{smtpServer:"",smtpPort:587,smtpUsername:"",smtpPassword:"",fromEmail:"",fromName:"ديوان آل أبو علوش",enableSSL:!0},smsSettings:{provider:"",apiKey:"",senderName:"ديوان آل أبو علوش"},templates:{welcomeMessage:"مرحباً بك في ديوان آل أبو علوش",paymentReminder:"تذكير: يرجى دفع الاشتراك الشهري",paymentConfirmation:"تم استلام دفعتك بنجاح",systemAlert:"تنبيه من النظام"}};function es({settings:e,onChange:s,canEdit:a}){let[t,r]=(0,i.useState)(ee),[c,n]=(0,i.useState)(!1),[o,x]=(0,i.useState)(!1),m=(e,a)=>{let l=e.split("."),i={...t};1===l.length?i={...i,[l[0]]:a}:2===l.length&&(i={...i,[l[0]]:{...i[l[0]],[l[1]]:a}}),r(i),s(i)};return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)(V.Zp,{children:[(0,l.jsx)(V.aR,{children:(0,l.jsxs)(V.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(h.A,{className:"w-5 h-5 text-blue-600"}),"الإعدادات العامة للإشعارات"]})}),(0,l.jsx)(V.Wu,{className:"space-y-4",children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"تفعيل الإشعارات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"تفعيل أو إلغاء جميع الإشعارات"})]}),(0,l.jsx)(k,{checked:t.enableNotifications,onCheckedChange:e=>m("enableNotifications",e),disabled:!a})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"الأصوات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"تشغيل أصوات الإشعارات"})]}),(0,l.jsx)(k,{checked:t.enableSounds,onCheckedChange:e=>m("enableSounds",e),disabled:!a||!t.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"إشعارات سطح المكتب"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"عرض إشعارات في المتصفح"})]}),(0,l.jsx)(k,{checked:t.enableDesktopNotifications,onCheckedChange:e=>m("enableDesktopNotifications",e),disabled:!a||!t.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"إشعارات البريد الإلكتروني"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إرسال إشعارات عبر البريد الإلكتروني"})]}),(0,l.jsx)(k,{checked:t.enableEmailNotifications,onCheckedChange:e=>m("enableEmailNotifications",e),disabled:!a||!t.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"الرسائل النصية"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إرسال إشعارات عبر الرسائل النصية"})]}),(0,l.jsx)(k,{checked:t.enableSMSNotifications,onCheckedChange:e=>m("enableSMSNotifications",e),disabled:!a||!t.enableNotifications})]})]})})]}),(0,l.jsxs)(V.Zp,{children:[(0,l.jsx)(V.aR,{children:(0,l.jsxs)(V.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(H.A,{className:"w-5 h-5 text-green-600"}),"إشعارات الأعضاء"]})}),(0,l.jsx)(V.Wu,{className:"space-y-4",children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"عضو جديد"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إشعار عند إضافة عضو جديد"})]}),(0,l.jsx)(k,{checked:t.memberNotifications.newMember,onCheckedChange:e=>m("memberNotifications.newMember",e),disabled:!a||!t.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"تحديث بيانات العضو"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إشعار عند تحديث بيانات عضو"})]}),(0,l.jsx)(k,{checked:t.memberNotifications.memberUpdate,onCheckedChange:e=>m("memberNotifications.memberUpdate",e),disabled:!a||!t.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"تغيير حالة العضو"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إشعار عند تغيير حالة العضو"})]}),(0,l.jsx)(k,{checked:t.memberNotifications.memberStatusChange,onCheckedChange:e=>m("memberNotifications.memberStatusChange",e),disabled:!a||!t.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"دفعات الأعضاء"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إشعار عند دفع الاشتراكات"})]}),(0,l.jsx)(k,{checked:t.memberNotifications.memberPayment,onCheckedChange:e=>m("memberNotifications.memberPayment",e),disabled:!a||!t.enableNotifications})]})]})})]}),(0,l.jsxs)(V.Zp,{children:[(0,l.jsx)(V.aR,{children:(0,l.jsxs)(V.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(K.A,{className:"w-5 h-5 text-emerald-600"}),"إشعارات الإيرادات"]})}),(0,l.jsx)(V.Wu,{className:"space-y-4",children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"إيراد جديد"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إشعار عند إضافة إيراد جديد"})]}),(0,l.jsx)(k,{checked:t.incomeNotifications.newIncome,onCheckedChange:e=>m("incomeNotifications.newIncome",e),disabled:!a||!t.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"تحديث الإيراد"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إشعار عند تحديث إيراد"})]}),(0,l.jsx)(k,{checked:t.incomeNotifications.incomeUpdate,onCheckedChange:e=>m("incomeNotifications.incomeUpdate",e),disabled:!a||!t.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"تذكير الدفع"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"تذكير الأعضاء بموعد الدفع"})]}),(0,l.jsx)(k,{checked:t.incomeNotifications.paymentReminder,onCheckedChange:e=>m("incomeNotifications.paymentReminder",e),disabled:!a||!t.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"تأخير الدفع"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"تنبيه عند تأخر الدفع"})]}),(0,l.jsx)(k,{checked:t.incomeNotifications.paymentOverdue,onCheckedChange:e=>m("incomeNotifications.paymentOverdue",e),disabled:!a||!t.enableNotifications})]})]})})]}),(0,l.jsxs)(V.Zp,{children:[(0,l.jsx)(V.aR,{children:(0,l.jsxs)(V.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(Q.A,{className:"w-5 h-5 text-red-600"}),"إشعارات المصروفات"]})}),(0,l.jsx)(V.Wu,{className:"space-y-4",children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"مصروف جديد"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إشعار عند إضافة مصروف جديد"})]}),(0,l.jsx)(k,{checked:t.expenseNotifications.newExpense,onCheckedChange:e=>m("expenseNotifications.newExpense",e),disabled:!a||!t.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"تحديث المصروف"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إشعار عند تحديث مصروف"})]}),(0,l.jsx)(k,{checked:t.expenseNotifications.expenseUpdate,onCheckedChange:e=>m("expenseNotifications.expenseUpdate",e),disabled:!a||!t.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"تنبيه الميزانية"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"تنبيه عند تجاوز حد الميزانية"})]}),(0,l.jsx)(k,{checked:t.expenseNotifications.budgetAlert,onCheckedChange:e=>m("expenseNotifications.budgetAlert",e),disabled:!a||!t.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"موافقة المصروف"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إشعار عند الحاجة لموافقة على مصروف"})]}),(0,l.jsx)(k,{checked:t.expenseNotifications.expenseApproval,onCheckedChange:e=>m("expenseNotifications.expenseApproval",e),disabled:!a||!t.enableNotifications})]})]})})]}),(0,l.jsxs)(V.Zp,{children:[(0,l.jsx)(V.aR,{children:(0,l.jsxs)(V.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(d.A,{className:"w-5 h-5 text-purple-600"}),"إشعارات النظام"]})}),(0,l.jsx)(V.Wu,{className:"space-y-4",children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"تحديثات النظام"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إشعار عند توفر تحديثات"})]}),(0,l.jsx)(k,{checked:t.systemNotifications.systemUpdate,onCheckedChange:e=>m("systemNotifications.systemUpdate",e),disabled:!a||!t.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"تنبيهات الأمان"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"تنبيهات أمنية مهمة"})]}),(0,l.jsx)(k,{checked:t.systemNotifications.securityAlert,onCheckedChange:e=>m("systemNotifications.securityAlert",e),disabled:!a||!t.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"اكتمال النسخ الاحتياطي"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إشعار عند اكتمال النسخ الاحتياطي"})]}),(0,l.jsx)(k,{checked:t.systemNotifications.backupComplete,onCheckedChange:e=>m("systemNotifications.backupComplete",e),disabled:!a||!t.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"تنبيهات الأخطاء"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إشعار عند حدوث أخطاء في النظام"})]}),(0,l.jsx)(k,{checked:t.systemNotifications.errorAlert,onCheckedChange:e=>m("systemNotifications.errorAlert",e),disabled:!a||!t.enableNotifications})]})]})})]}),(0,l.jsxs)(V.Zp,{children:[(0,l.jsx)(V.aR,{children:(0,l.jsxs)(V.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(X.A,{className:"w-5 h-5 text-orange-600"}),"ساعات الهدوء"]})}),(0,l.jsxs)(V.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"تفعيل ساعات الهدوء"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إيقاف الإشعارات في أوقات محددة"})]}),(0,l.jsx)(k,{checked:t.quietHours.enabled,onCheckedChange:e=>m("quietHours.enabled",e),disabled:!a||!t.enableNotifications})]}),t.quietHours.enabled&&(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"startTime",children:"وقت البداية"}),(0,l.jsx)(v.p,{id:"startTime",type:"time",value:t.quietHours.startTime,onChange:e=>m("quietHours.startTime",e.target.value),disabled:!a})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"endTime",children:"وقت النهاية"}),(0,l.jsx)(v.p,{id:"endTime",type:"time",value:t.quietHours.endTime,onChange:e=>m("quietHours.endTime",e.target.value),disabled:!a})]})]})]})]})]})}var ea=a(58869),el=a(51361);function ei({users:e,onClose:s}){let[a,t]=(0,i.useState)(""),[r,o]=(0,i.useState)([]),[x,h]=(0,i.useState)({userId:"",canViewAllMembers:!1,canEditMembers:!1,canDeleteMembers:!1,canViewAllIncomes:!1,canEditIncomes:!1,canDeleteIncomes:!1,canViewAllExpenses:!1,canEditExpenses:!1,canDeleteExpenses:!1,canViewGallery:!1,canUploadToGallery:!1,canDeleteFromGallery:!1,canViewReports:!1,canExportData:!1,canManageUsers:!1,canManageSettings:!1,canViewMemberAccount:!1,canViewMemberDetails:!1,galleryReadOnly:!0,canCreateGalleryFolders:!1}),[m,j]=(0,i.useState)(!1),p=(e,s)=>{h(a=>({...a,[e]:s}))},u=async()=>{if(!a)return void alert("يرجى اختيار مستخدم");try{j(!0);let e=await fetch("/api/admin/user-permissions",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:a,permissions:{canViewAllMembers:x.canViewAllMembers,canEditMembers:x.canEditMembers,canDeleteMembers:x.canDeleteMembers,canViewAllIncomes:x.canViewAllIncomes,canEditIncomes:x.canEditIncomes,canDeleteIncomes:x.canDeleteIncomes,canViewAllExpenses:x.canViewAllExpenses,canEditExpenses:x.canEditExpenses,canDeleteExpenses:x.canDeleteExpenses,canViewGallery:x.canViewGallery,canUploadToGallery:x.canUploadToGallery,canDeleteFromGallery:x.canDeleteFromGallery,canViewReports:x.canViewReports,canExportData:x.canExportData,canManageUsers:x.canManageUsers,canManageSettings:x.canManageSettings,specificMemberId:x.specificMemberId||null,canViewMemberAccount:x.canViewMemberAccount,canViewMemberDetails:x.canViewMemberDetails,galleryReadOnly:x.galleryReadOnly,canCreateGalleryFolders:x.canCreateGalleryFolders}})});if(e.ok)alert("تم حفظ الصلاحيات بنجاح");else{let s=await e.json();alert(s.message||"فشل في حفظ الصلاحيات")}}catch(e){console.error("خطأ في حفظ الصلاحيات:",e),alert("حدث خطأ أثناء حفظ الصلاحيات")}finally{j(!1)}},g=e.find(e=>e.id===a);return(0,l.jsxs)(V.Zp,{className:"border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-indigo-50 shadow-xl",children:[(0,l.jsx)(V.aR,{className:"bg-gradient-to-r from-purple-500 to-indigo-600 text-white rounded-t-lg",children:(0,l.jsxs)(V.ZB,{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("div",{className:"p-2 bg-white bg-opacity-20 rounded-lg",children:(0,l.jsx)(d.A,{className:"w-6 h-6 text-white"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-xl font-bold",children:"إدارة الصلاحيات المفصلة"}),(0,l.jsx)("p",{className:"text-purple-100 text-sm mt-1",children:"تحديد صلاحيات دقيقة لكل مستخدم"})]})]}),(0,l.jsx)(c.$,{variant:"outline",size:"sm",onClick:s,className:"bg-white bg-opacity-20 border-white border-opacity-30 text-white hover:bg-white hover:bg-opacity-30",children:"إغلاق"})]})}),(0,l.jsxs)(V.Wu,{className:"space-y-6 p-6",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"اختيار المستخدم"}),(0,l.jsxs)(w.l6,{value:a,onValueChange:t,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{placeholder:"اختر مستخدماً لتعديل صلاحياته"})}),(0,l.jsx)(w.gC,{children:e.filter(e=>"ADMIN"!==e.role).map(e=>(0,l.jsx)(w.eb,{value:e.id,children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(ea.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:e.name}),(0,l.jsx)(n.E,{variant:"outline",className:"text-xs",children:"DATA_ENTRY"===e.role?"مدخل بيانات":"VIEWER"===e.role?"مطلع":"MEMBER_VIEWER"===e.role?"مطلع على عضو":"GALLERY_VIEWER"===e.role?"مطلع على المعرض":e.role})]})},e.id))})]})]}),g&&(0,l.jsxs)("div",{className:"bg-white rounded-lg p-4 border border-gray-200",children:[(0,l.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"المستخدم المحدد:"}),(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("div",{className:"w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center",children:(0,l.jsx)(ea.A,{className:"w-5 h-5 text-purple-600"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium",children:g.name}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:g.email})]})]})]}),a&&!m&&(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)(V.Zp,{children:[(0,l.jsx)(V.aR,{children:(0,l.jsxs)(V.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,l.jsx)(H.A,{className:"w-5 h-5 text-blue-600"}),"صلاحيات الأعضاء"]})}),(0,l.jsxs)(V.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)(N.J,{children:"عرض جميع الأعضاء"}),(0,l.jsx)(k,{checked:x.canViewAllMembers,onCheckedChange:e=>p("canViewAllMembers",e)})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)(N.J,{children:"تعديل الأعضاء"}),(0,l.jsx)(k,{checked:x.canEditMembers,onCheckedChange:e=>p("canEditMembers",e)})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)(N.J,{children:"حذف الأعضاء"}),(0,l.jsx)(k,{checked:x.canDeleteMembers,onCheckedChange:e=>p("canDeleteMembers",e)})]})]}),(0,l.jsxs)("div",{className:"border-t pt-4",children:[(0,l.jsx)("h5",{className:"font-medium mb-3",children:"صلاحيات عضو محدد"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"اختيار عضو محدد (اختياري)"}),(0,l.jsxs)(w.l6,{value:x.specificMemberId||"",onValueChange:e=>p("specificMemberId",e),children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{placeholder:"اختر عضواً محدداً"})}),(0,l.jsxs)(w.gC,{children:[(0,l.jsx)(w.eb,{value:"",children:"لا يوجد عضو محدد"}),r.map(e=>(0,l.jsx)(w.eb,{value:e.id,children:e.name},e.id))]})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)(N.J,{children:"عرض كشف حساب العضو"}),(0,l.jsx)(k,{checked:x.canViewMemberAccount,onCheckedChange:e=>p("canViewMemberAccount",e)})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)(N.J,{children:"عرض تفاصيل العضو"}),(0,l.jsx)(k,{checked:x.canViewMemberDetails,onCheckedChange:e=>p("canViewMemberDetails",e)})]})]})]})]})]})]}),(0,l.jsxs)(V.Zp,{children:[(0,l.jsx)(V.aR,{children:(0,l.jsxs)(V.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,l.jsx)(el.A,{className:"w-5 h-5 text-green-600"}),"صلاحيات المعرض"]})}),(0,l.jsx)(V.Wu,{className:"space-y-4",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)(N.J,{children:"عرض المعرض"}),(0,l.jsx)(k,{checked:x.canViewGallery,onCheckedChange:e=>p("canViewGallery",e)})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)(N.J,{children:"المعرض للقراءة فقط"}),(0,l.jsx)(k,{checked:x.galleryReadOnly,onCheckedChange:e=>p("galleryReadOnly",e)})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)(N.J,{children:"رفع صور للمعرض"}),(0,l.jsx)(k,{checked:x.canUploadToGallery,onCheckedChange:e=>p("canUploadToGallery",e)})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)(N.J,{children:"إنشاء مجلدات"}),(0,l.jsx)(k,{checked:x.canCreateGalleryFolders,onCheckedChange:e=>p("canCreateGalleryFolders",e)})]})]})})]}),(0,l.jsxs)("div",{className:"flex justify-end gap-3 pt-4",children:[(0,l.jsx)(c.$,{variant:"outline",onClick:s,children:"إلغاء"}),(0,l.jsx)(c.$,{onClick:u,disabled:m,className:"bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 text-white",children:m?"جاري الحفظ...":"حفظ الصلاحيات"})]})]}),m&&(0,l.jsx)("div",{className:"flex justify-center py-8",children:(0,l.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"})})]})]})}var et=a(64021),er=a(93508);let ec=(0,o.A)("history",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]),en=(0,o.A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);var ed=a(41550),eo=a(48340),ex=a(63143),eh=a(88233),em=a(5336);let ej={passwordPolicy:{minLength:8,requireUppercase:!0,requireLowercase:!0,requireNumbers:!0,requireSpecialChars:!1,preventReuse:5,expirationDays:90},sessionSettings:{timeout:30,maxConcurrentSessions:3,requireReauth:!1,rememberMe:!0,rememberMeDuration:30},loginSettings:{maxFailedAttempts:5,lockoutDuration:15,enableCaptcha:!1,enableTwoFactor:!1,allowedIPs:[],blockedIPs:[]},auditSettings:{enableAuditLog:!0,logLoginAttempts:!0,logDataChanges:!0,logSystemEvents:!0,retentionDays:365,enableRealTimeAlerts:!0},permissionSettings:{defaultRole:"VIEWER",allowSelfRegistration:!1,requireAdminApproval:!0,enableRoleHierarchy:!0,maxUsersPerRole:{ADMIN:3,DATA_ENTRY:10,VIEWER:100}},advancedSecurity:{enableEncryption:!0,enableSSL:!0,enableCSRF:!0,enableXSS:!0,enableSQLInjection:!0,enableRateLimit:!0,rateLimitRequests:100,rateLimitWindow:15}};function ep({settings:e,onChange:s,canEdit:a}){let[t,r]=(0,i.useState)(ej),[o,x]=(0,i.useState)(!1),[h,j]=(0,i.useState)(!1),[p,u]=(0,i.useState)([]),[g,b]=(0,i.useState)(!1),[y,f]=(0,i.useState)({name:"",email:"",phone:"",role:"VIEWER",password:"",confirmPassword:""}),[C,S]=(0,i.useState)(null),[A,E]=(0,i.useState)(!1),[J,R]=(0,i.useState)(!1),D=async()=>{try{E(!0);let e=await fetch("/api/admin/users");if(e.ok){let s=await e.json();u(s)}}catch(e){console.error("خطأ في تحميل المستخدمين:",e)}finally{E(!1)}},M=(e,a)=>{let l=e.split("."),i={...t};1===l.length?i={...i,[l[0]]:a}:2===l.length?i={...i,[l[0]]:{...i[l[0]],[l[1]]:a}}:3===l.length&&(i={...i,[l[0]]:{...i[l[0]],[l[1]]:{...i[l[0]][l[1]],[l[2]]:a}}}),r(i),s(i)},P=async()=>{if(!y.name||!y.email||!y.password)return void alert("يرجى ملء جميع الحقول المطلوبة");if(y.password!==y.confirmPassword)return void alert("كلمات المرور غير متطابقة");try{E(!0);let e=await fetch("/api/admin/users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:y.name,email:y.email,phone:y.phone,role:y.role,password:y.password})});if(e.ok)alert("تم إضافة المستخدم بنجاح"),f({name:"",email:"",phone:"",role:"VIEWER",password:"",confirmPassword:""}),b(!1),D();else{let s=await e.json();alert(s.message||"فشل في إضافة المستخدم")}}catch(e){console.error("خطأ في إضافة المستخدم:",e),alert("حدث خطأ أثناء إضافة المستخدم")}finally{E(!1)}},q=async(e,s)=>{try{E(!0);let a=await fetch(`/api/admin/users/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(a.ok)alert("تم تحديث المستخدم بنجاح"),S(null),D();else{let e=await a.json();alert(e.message||"فشل في تحديث المستخدم")}}catch(e){console.error("خطأ في تحديث المستخدم:",e),alert("حدث خطأ أثناء تحديث المستخدم")}finally{E(!1)}},F=async(e,s)=>{if(confirm(`هل أنت متأكد من حذف المستخدم "${s}"؟

تحذير: سيتم حذف جميع البيانات المرتبطة بهذا المستخدم نهائياً ولا يمكن التراجع عن هذا الإجراء.`))try{E(!0);let s=await fetch(`/api/admin/users/${e}`,{method:"DELETE"});if(s.ok)alert("تم حذف المستخدم بنجاح"),D();else{let e=await s.json();alert(e.message||"فشل في حذف المستخدم")}}catch(e){console.error("خطأ في حذف المستخدم:",e),alert("حدث خطأ أثناء حذف المستخدم")}finally{E(!1)}},I=e=>{switch(e){case"ADMIN":return"مدير";case"DATA_ENTRY":return"مدخل بيانات";case"VIEWER":return"مطلع";case"MEMBER_VIEWER":return"مطلع على عضو";case"GALLERY_VIEWER":return"مطلع على المعرض";case"MEMBER":return"عضو";default:return e}},L=e=>{switch(e){case"ADMIN":return"bg-red-100 text-red-800";case"DATA_ENTRY":return"bg-blue-100 text-blue-800";case"VIEWER":return"bg-green-100 text-green-800";case"MEMBER_VIEWER":return"bg-purple-100 text-purple-800";case"GALLERY_VIEWER":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}};return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)(V.Zp,{children:[(0,l.jsx)(V.aR,{children:(0,l.jsxs)(V.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(et.A,{className:"w-5 h-5 text-blue-600"}),"سياسة كلمات المرور"]})}),(0,l.jsxs)(V.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"minLength",children:"الحد الأدنى لطول كلمة المرور"}),(0,l.jsxs)(w.l6,{value:t.passwordPolicy.minLength.toString(),onValueChange:e=>M("passwordPolicy.minLength",parseInt(e)),disabled:!a,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{})}),(0,l.jsxs)(w.gC,{children:[(0,l.jsx)(w.eb,{value:"6",children:"6 أحرف"}),(0,l.jsx)(w.eb,{value:"8",children:"8 أحرف"}),(0,l.jsx)(w.eb,{value:"10",children:"10 أحرف"}),(0,l.jsx)(w.eb,{value:"12",children:"12 حرف"}),(0,l.jsx)(w.eb,{value:"16",children:"16 حرف"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"expirationDays",children:"انتهاء صلاحية كلمة المرور (يوم)"}),(0,l.jsxs)(w.l6,{value:t.passwordPolicy.expirationDays.toString(),onValueChange:e=>M("passwordPolicy.expirationDays",parseInt(e)),disabled:!a,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{})}),(0,l.jsxs)(w.gC,{children:[(0,l.jsx)(w.eb,{value:"30",children:"30 يوم"}),(0,l.jsx)(w.eb,{value:"60",children:"60 يوم"}),(0,l.jsx)(w.eb,{value:"90",children:"90 يوم"}),(0,l.jsx)(w.eb,{value:"180",children:"180 يوم"}),(0,l.jsx)(w.eb,{value:"365",children:"سنة واحدة"}),(0,l.jsx)(w.eb,{value:"0",children:"بدون انتهاء"})]})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"يجب أن تحتوي على أحرف كبيرة"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"A-Z"})]}),(0,l.jsx)(k,{checked:t.passwordPolicy.requireUppercase,onCheckedChange:e=>M("passwordPolicy.requireUppercase",e),disabled:!a})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"يجب أن تحتوي على أحرف صغيرة"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"a-z"})]}),(0,l.jsx)(k,{checked:t.passwordPolicy.requireLowercase,onCheckedChange:e=>M("passwordPolicy.requireLowercase",e),disabled:!a})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"يجب أن تحتوي على أرقام"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"0-9"})]}),(0,l.jsx)(k,{checked:t.passwordPolicy.requireNumbers,onCheckedChange:e=>M("passwordPolicy.requireNumbers",e),disabled:!a})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"يجب أن تحتوي على رموز خاصة"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"!@#$%^&*"})]}),(0,l.jsx)(k,{checked:t.passwordPolicy.requireSpecialChars,onCheckedChange:e=>M("passwordPolicy.requireSpecialChars",e),disabled:!a})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"preventReuse",children:"منع إعادة استخدام كلمات المرور السابقة"}),(0,l.jsxs)(w.l6,{value:t.passwordPolicy.preventReuse.toString(),onValueChange:e=>M("passwordPolicy.preventReuse",parseInt(e)),disabled:!a,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{})}),(0,l.jsxs)(w.gC,{children:[(0,l.jsx)(w.eb,{value:"0",children:"السماح بإعادة الاستخدام"}),(0,l.jsx)(w.eb,{value:"3",children:"آخر 3 كلمات مرور"}),(0,l.jsx)(w.eb,{value:"5",children:"آخر 5 كلمات مرور"}),(0,l.jsx)(w.eb,{value:"10",children:"آخر 10 كلمات مرور"})]})]})]})]})]}),(0,l.jsxs)(V.Zp,{children:[(0,l.jsx)(V.aR,{children:(0,l.jsxs)(V.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(X.A,{className:"w-5 h-5 text-green-600"}),"إعدادات الجلسة"]})}),(0,l.jsxs)(V.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"مدة انتهاء الجلسة (دقيقة)"}),(0,l.jsxs)(w.l6,{value:t.sessionSettings.timeout.toString(),onValueChange:e=>M("sessionSettings.timeout",parseInt(e)),disabled:!a,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{})}),(0,l.jsxs)(w.gC,{children:[(0,l.jsx)(w.eb,{value:"15",children:"15 دقيقة"}),(0,l.jsx)(w.eb,{value:"30",children:"30 دقيقة"}),(0,l.jsx)(w.eb,{value:"60",children:"ساعة واحدة"}),(0,l.jsx)(w.eb,{value:"120",children:"ساعتان"}),(0,l.jsx)(w.eb,{value:"480",children:"8 ساعات"}),(0,l.jsx)(w.eb,{value:"1440",children:"24 ساعة"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"الحد الأقصى للجلسات المتزامنة"}),(0,l.jsxs)(w.l6,{value:t.sessionSettings.maxConcurrentSessions.toString(),onValueChange:e=>M("sessionSettings.maxConcurrentSessions",parseInt(e)),disabled:!a,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{})}),(0,l.jsxs)(w.gC,{children:[(0,l.jsx)(w.eb,{value:"1",children:"جلسة واحدة فقط"}),(0,l.jsx)(w.eb,{value:"2",children:"جلستان"}),(0,l.jsx)(w.eb,{value:"3",children:"3 جلسات"}),(0,l.jsx)(w.eb,{value:"5",children:"5 جلسات"}),(0,l.jsx)(w.eb,{value:"10",children:"10 جلسات"}),(0,l.jsx)(w.eb,{value:"0",children:"بدون حد"})]})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"تذكرني"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"السماح بحفظ بيانات تسجيل الدخول"})]}),(0,l.jsx)(k,{checked:t.sessionSettings.rememberMe,onCheckedChange:e=>M("sessionSettings.rememberMe",e),disabled:!a})]}),t.sessionSettings.rememberMe&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:'مدة "تذكرني" (يوم)'}),(0,l.jsxs)(w.l6,{value:t.sessionSettings.rememberMeDuration.toString(),onValueChange:e=>M("sessionSettings.rememberMeDuration",parseInt(e)),disabled:!a,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{})}),(0,l.jsxs)(w.gC,{children:[(0,l.jsx)(w.eb,{value:"7",children:"أسبوع واحد"}),(0,l.jsx)(w.eb,{value:"30",children:"شهر واحد"}),(0,l.jsx)(w.eb,{value:"90",children:"3 أشهر"}),(0,l.jsx)(w.eb,{value:"365",children:"سنة واحدة"})]})]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"إعادة المصادقة للعمليات الحساسة"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"طلب كلمة المرور مرة أخرى"})]}),(0,l.jsx)(k,{checked:t.sessionSettings.requireReauth,onCheckedChange:e=>M("sessionSettings.requireReauth",e),disabled:!a})]})]})]})]}),(0,l.jsxs)(V.Zp,{children:[(0,l.jsx)(V.aR,{children:(0,l.jsxs)(V.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(er.A,{className:"w-5 h-5 text-purple-600"}),"إعدادات تسجيل الدخول"]})}),(0,l.jsxs)(V.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"الحد الأقصى لمحاولات الدخول الفاشلة"}),(0,l.jsxs)(w.l6,{value:t.loginSettings.maxFailedAttempts.toString(),onValueChange:e=>M("loginSettings.maxFailedAttempts",parseInt(e)),disabled:!a,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{})}),(0,l.jsxs)(w.gC,{children:[(0,l.jsx)(w.eb,{value:"3",children:"3 محاولات"}),(0,l.jsx)(w.eb,{value:"5",children:"5 محاولات"}),(0,l.jsx)(w.eb,{value:"10",children:"10 محاولات"}),(0,l.jsx)(w.eb,{value:"0",children:"بدون حد"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"مدة الحظر (دقيقة)"}),(0,l.jsxs)(w.l6,{value:t.loginSettings.lockoutDuration.toString(),onValueChange:e=>M("loginSettings.lockoutDuration",parseInt(e)),disabled:!a,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{})}),(0,l.jsxs)(w.gC,{children:[(0,l.jsx)(w.eb,{value:"5",children:"5 دقائق"}),(0,l.jsx)(w.eb,{value:"15",children:"15 دقيقة"}),(0,l.jsx)(w.eb,{value:"30",children:"30 دقيقة"}),(0,l.jsx)(w.eb,{value:"60",children:"ساعة واحدة"}),(0,l.jsx)(w.eb,{value:"1440",children:"24 ساعة"})]})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"تفعيل CAPTCHA"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"التحقق من أنك لست روبوت"})]}),(0,l.jsx)(k,{checked:t.loginSettings.enableCaptcha,onCheckedChange:e=>M("loginSettings.enableCaptcha",e),disabled:!a})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"المصادقة الثنائية"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"طبقة أمان إضافية"})]}),(0,l.jsx)(k,{checked:t.loginSettings.enableTwoFactor,onCheckedChange:e=>M("loginSettings.enableTwoFactor",e),disabled:!a})]})]})]})]}),(0,l.jsxs)(V.Zp,{children:[(0,l.jsx)(V.aR,{children:(0,l.jsxs)(V.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(ec,{className:"w-5 h-5 text-orange-600"}),"إعدادات التدقيق والسجلات"]})}),(0,l.jsxs)(V.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"تفعيل سجل التدقيق"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"تسجيل جميع العمليات"})]}),(0,l.jsx)(k,{checked:t.auditSettings.enableAuditLog,onCheckedChange:e=>M("auditSettings.enableAuditLog",e),disabled:!a})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"تسجيل محاولات الدخول"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"الناجحة والفاشلة"})]}),(0,l.jsx)(k,{checked:t.auditSettings.logLoginAttempts,onCheckedChange:e=>M("auditSettings.logLoginAttempts",e),disabled:!a||!t.auditSettings.enableAuditLog})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"تسجيل تغييرات البيانات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إضافة، تعديل، حذف"})]}),(0,l.jsx)(k,{checked:t.auditSettings.logDataChanges,onCheckedChange:e=>M("auditSettings.logDataChanges",e),disabled:!a||!t.auditSettings.enableAuditLog})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"تسجيل أحداث النظام"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"بدء التشغيل، الأخطاء، التحديثات"})]}),(0,l.jsx)(k,{checked:t.auditSettings.logSystemEvents,onCheckedChange:e=>M("auditSettings.logSystemEvents",e),disabled:!a||!t.auditSettings.enableAuditLog})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"التنبيهات الفورية"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إشعار فوري للأحداث المهمة"})]}),(0,l.jsx)(k,{checked:t.auditSettings.enableRealTimeAlerts,onCheckedChange:e=>M("auditSettings.enableRealTimeAlerts",e),disabled:!a||!t.auditSettings.enableAuditLog})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"مدة الاحتفاظ بالسجلات (يوم)"}),(0,l.jsxs)(w.l6,{value:t.auditSettings.retentionDays.toString(),onValueChange:e=>M("auditSettings.retentionDays",parseInt(e)),disabled:!a||!t.auditSettings.enableAuditLog,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{})}),(0,l.jsxs)(w.gC,{children:[(0,l.jsx)(w.eb,{value:"30",children:"30 يوم"}),(0,l.jsx)(w.eb,{value:"90",children:"90 يوم"}),(0,l.jsx)(w.eb,{value:"180",children:"180 يوم"}),(0,l.jsx)(w.eb,{value:"365",children:"سنة واحدة"}),(0,l.jsx)(w.eb,{value:"1095",children:"3 سنوات"}),(0,l.jsx)(w.eb,{value:"0",children:"دائماً"})]})]})]})]})]}),(0,l.jsxs)(V.Zp,{className:"border-2 border-indigo-100 shadow-lg",children:[(0,l.jsx)(V.aR,{className:"bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-t-lg",children:(0,l.jsxs)(V.ZB,{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("div",{className:"p-2 bg-white bg-opacity-20 rounded-lg",children:(0,l.jsx)(en,{className:"w-6 h-6 text-white"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-xl font-bold",children:"إدارة المستخدمين"}),(0,l.jsx)("p",{className:"text-indigo-100 text-sm mt-1",children:"إضافة وتعديل وحذف حسابات المستخدمين"})]})]}),(0,l.jsxs)(c.$,{variant:"outline",size:"sm",onClick:()=>j(!h),disabled:!a,className:"bg-white bg-opacity-20 border-white border-opacity-30 text-white hover:bg-white hover:bg-opacity-30",children:[h?"إخفاء":"عرض"," المستخدمين"]})]})}),h&&(0,l.jsxs)(V.Wu,{className:"space-y-6 bg-gradient-to-br from-gray-50 to-indigo-50",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center p-6 bg-white rounded-xl shadow-sm border border-gray-100",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("h3",{className:"text-xl font-bold text-gray-900 flex items-center gap-2",children:[(0,l.jsx)(H.A,{className:"w-5 h-5 text-indigo-600"}),"قائمة المستخدمين"]}),(0,l.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"إدارة حسابات المستخدمين وصلاحياتهم في النظام"}),(0,l.jsxs)("div",{className:"flex items-center gap-4 mt-2 text-xs text-gray-500",children:[(0,l.jsxs)("span",{children:["إجمالي المستخدمين: ",p.length]}),(0,l.jsx)("span",{children:"•"}),(0,l.jsxs)("span",{children:["المديرون: ",p.filter(e=>"ADMIN"===e.role).length]}),(0,l.jsx)("span",{children:"•"}),(0,l.jsxs)("span",{children:["مدخلو البيانات: ",p.filter(e=>"DATA_ENTRY"===e.role).length]}),(0,l.jsx)("span",{children:"•"}),(0,l.jsxs)("span",{children:["المطلعون: ",p.filter(e=>"VIEWER"===e.role).length]})]})]}),(0,l.jsxs)("div",{className:"flex gap-3",children:[(0,l.jsxs)(c.$,{onClick:()=>R(!0),disabled:!a||A,variant:"outline",className:"border-indigo-300 text-indigo-600 hover:bg-indigo-50",children:[(0,l.jsx)(d.A,{className:"w-4 h-4 ml-2"}),"الصلاحيات المفصلة"]}),(0,l.jsxs)(c.$,{onClick:()=>b(!0),disabled:!a||A,className:"bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300",children:[(0,l.jsx)(en,{className:"w-4 h-4 ml-2"}),"إضافة مستخدم"]})]})]}),A?(0,l.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,l.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"})}):(0,l.jsx)("div",{className:"space-y-4",children:0===p.length?(0,l.jsxs)("div",{className:"text-center py-12 bg-white rounded-xl border-2 border-dashed border-gray-200",children:[(0,l.jsx)("div",{className:"w-20 h-20 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,l.jsx)(H.A,{className:"w-10 h-10 text-indigo-400"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"لا توجد مستخدمين مسجلين"}),(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:"ابدأ بإضافة أول مستخدم في النظام"}),(0,l.jsxs)(c.$,{onClick:()=>b(!0),disabled:!a||A,className:"bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white",children:[(0,l.jsx)(en,{className:"w-4 h-4 ml-2"}),"إضافة أول مستخدم"]})]}):p.map(e=>(0,l.jsxs)("div",{className:"group flex items-center justify-between p-6 border border-gray-200 rounded-xl bg-white hover:bg-gradient-to-r hover:from-indigo-50 hover:to-blue-50 hover:border-indigo-200 transition-all duration-300 shadow-sm hover:shadow-md",children:[(0,l.jsx)("div",{className:"flex-1",children:(0,l.jsxs)("div",{className:"flex items-center gap-4",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300",children:(0,l.jsx)("span",{className:"text-white font-bold text-lg",children:e.name.charAt(0).toUpperCase()})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-bold text-gray-900 text-lg group-hover:text-indigo-700 transition-colors duration-300",children:e.name}),(0,l.jsxs)("div",{className:"flex items-center gap-6 text-sm text-gray-600 mt-1",children:[(0,l.jsxs)("span",{className:"flex items-center gap-2",children:[(0,l.jsx)(ed.A,{className:"w-4 h-4 text-indigo-500"}),e.email]}),e.phone&&(0,l.jsxs)("span",{className:"flex items-center gap-2",children:[(0,l.jsx)(eo.A,{className:"w-4 h-4 text-green-500"}),e.phone]}),(0,l.jsxs)("span",{className:"text-xs text-gray-500",children:["انضم في ",new Date(e.createdAt).toLocaleDateString("ar-SA")]})]})]})]})}),(0,l.jsxs)("div",{className:"flex items-center gap-4",children:[(0,l.jsx)(n.E,{className:`${L(e.role)} px-3 py-1 text-sm font-semibold`,children:I(e.role)}),a&&(0,l.jsxs)("div",{className:"flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:[(0,l.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>S(e),disabled:A,className:"bg-blue-50 border-blue-200 text-blue-600 hover:bg-blue-100 hover:border-blue-300",children:(0,l.jsx)(ex.A,{className:"w-4 h-4"})}),(0,l.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>F(e.id,e.name),disabled:A,className:"bg-red-50 border-red-200 text-red-600 hover:bg-red-100 hover:border-red-300",children:(0,l.jsx)(eh.A,{className:"w-4 h-4"})})]})]})]},e.id))})]})]}),(0,l.jsxs)(V.Zp,{children:[(0,l.jsx)(V.aR,{children:(0,l.jsxs)(V.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(H.A,{className:"w-5 h-5 text-red-600"}),"إعدادات الصلاحيات"]})}),(0,l.jsxs)(V.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"الدور الافتراضي للمستخدمين الجدد"}),(0,l.jsxs)(w.l6,{value:t.permissionSettings.defaultRole,onValueChange:e=>M("permissionSettings.defaultRole",e),disabled:!a,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{})}),(0,l.jsxs)(w.gC,{children:[(0,l.jsx)(w.eb,{value:"VIEWER",children:"مطلع (عرض فقط)"}),(0,l.jsx)(w.eb,{value:"DATA_ENTRY",children:"مدخل بيانات"}),(0,l.jsx)(w.eb,{value:"ADMIN",children:"مدير (غير مستحسن)"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"السماح بالتسجيل الذاتي"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"المستخدمون يمكنهم إنشاء حسابات"})]}),(0,l.jsx)(k,{checked:t.permissionSettings.allowSelfRegistration,onCheckedChange:e=>M("permissionSettings.allowSelfRegistration",e),disabled:!a})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"يتطلب موافقة المدير"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"الحسابات الجديدة تحتاج موافقة"})]}),(0,l.jsx)(k,{checked:t.permissionSettings.requireAdminApproval,onCheckedChange:e=>M("permissionSettings.requireAdminApproval",e),disabled:!a||!t.permissionSettings.allowSelfRegistration})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"تفعيل التسلسل الهرمي للأدوار"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"المدير يمكنه إدارة جميع الأدوار"})]}),(0,l.jsx)(k,{checked:t.permissionSettings.enableRoleHierarchy,onCheckedChange:e=>M("permissionSettings.enableRoleHierarchy",e),disabled:!a})]})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)(N.J,{children:"الحد الأقصى للمستخدمين لكل دور"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"maxAdmins",children:"المديرون"}),(0,l.jsx)(v.p,{id:"maxAdmins",type:"number",min:"1",max:"10",value:t.permissionSettings.maxUsersPerRole.ADMIN,onChange:e=>M("permissionSettings.maxUsersPerRole.ADMIN",parseInt(e.target.value)),disabled:!a})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"maxDataEntry",children:"مدخلو البيانات"}),(0,l.jsx)(v.p,{id:"maxDataEntry",type:"number",min:"1",max:"50",value:t.permissionSettings.maxUsersPerRole.DATA_ENTRY,onChange:e=>M("permissionSettings.maxUsersPerRole.DATA_ENTRY",parseInt(e.target.value)),disabled:!a})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"maxViewers",children:"المطلعون"}),(0,l.jsx)(v.p,{id:"maxViewers",type:"number",min:"1",max:"1000",value:t.permissionSettings.maxUsersPerRole.VIEWER,onChange:e=>M("permissionSettings.maxUsersPerRole.VIEWER",parseInt(e.target.value)),disabled:!a})]})]})]})]})]}),(0,l.jsxs)(V.Zp,{children:[(0,l.jsx)(V.aR,{children:(0,l.jsxs)(V.ZB,{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(m.A,{className:"w-5 h-5 text-red-600"}),"إعدادات الأمان المتقدمة"]}),(0,l.jsxs)(c.$,{variant:"outline",size:"sm",onClick:()=>x(!o),children:[o?"إخفاء":"عرض"," المتقدم"]})]})}),o&&(0,l.jsxs)(V.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"تشفير البيانات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"تشفير البيانات الحساسة"})]}),(0,l.jsx)(k,{checked:t.advancedSecurity.enableEncryption,onCheckedChange:e=>M("advancedSecurity.enableEncryption",e),disabled:!a})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"إجبار SSL/HTTPS"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"اتصال آمن فقط"})]}),(0,l.jsx)(k,{checked:t.advancedSecurity.enableSSL,onCheckedChange:e=>M("advancedSecurity.enableSSL",e),disabled:!a})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"حماية CSRF"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"منع هجمات التزوير"})]}),(0,l.jsx)(k,{checked:t.advancedSecurity.enableCSRF,onCheckedChange:e=>M("advancedSecurity.enableCSRF",e),disabled:!a})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"حماية XSS"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"منع هجمات البرمجة النصية"})]}),(0,l.jsx)(k,{checked:t.advancedSecurity.enableXSS,onCheckedChange:e=>M("advancedSecurity.enableXSS",e),disabled:!a})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"حماية SQL Injection"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"منع هجمات قواعد البيانات"})]}),(0,l.jsx)(k,{checked:t.advancedSecurity.enableSQLInjection,onCheckedChange:e=>M("advancedSecurity.enableSQLInjection",e),disabled:!a})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"تحديد معدل الطلبات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"منع الهجمات المكثفة"})]}),(0,l.jsx)(k,{checked:t.advancedSecurity.enableRateLimit,onCheckedChange:e=>M("advancedSecurity.enableRateLimit",e),disabled:!a})]})]}),t.advancedSecurity.enableRateLimit&&(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"rateLimitRequests",children:"عدد الطلبات المسموحة"}),(0,l.jsx)(v.p,{id:"rateLimitRequests",type:"number",min:"10",max:"1000",value:t.advancedSecurity.rateLimitRequests,onChange:e=>M("advancedSecurity.rateLimitRequests",parseInt(e.target.value)),disabled:!a})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"rateLimitWindow",children:"النافزة الزمنية (دقيقة)"}),(0,l.jsx)(v.p,{id:"rateLimitWindow",type:"number",min:"1",max:"60",value:t.advancedSecurity.rateLimitWindow,onChange:e=>M("advancedSecurity.rateLimitWindow",parseInt(e.target.value)),disabled:!a})]})]})]})]}),g&&(0,l.jsxs)(V.Zp,{className:"border-2 border-indigo-200 bg-gradient-to-br from-indigo-50 to-blue-50 shadow-xl",children:[(0,l.jsx)(V.aR,{className:"bg-gradient-to-r from-indigo-500 to-indigo-600 text-white rounded-t-lg",children:(0,l.jsxs)(V.ZB,{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("div",{className:"p-2 bg-white bg-opacity-20 rounded-lg",children:(0,l.jsx)(en,{className:"w-6 h-6 text-white"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-xl font-bold",children:"إضافة مستخدم جديد"}),(0,l.jsx)("p",{className:"text-indigo-100 text-sm mt-1",children:"إنشاء حساب مستخدم جديد في النظام"})]})]}),(0,l.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>b(!1),className:"bg-white bg-opacity-20 border-white border-opacity-30 text-white hover:bg-white hover:bg-opacity-30",children:"إلغاء"})]})}),(0,l.jsxs)(V.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"newUserName",children:"الاسم الكامل *"}),(0,l.jsx)(v.p,{id:"newUserName",value:y.name,onChange:e=>f({...y,name:e.target.value}),placeholder:"أدخل الاسم الكامل",disabled:A})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"newUserEmail",children:"البريد الإلكتروني *"}),(0,l.jsx)(v.p,{id:"newUserEmail",type:"email",value:y.email,onChange:e=>f({...y,email:e.target.value}),placeholder:"أدخل البريد الإلكتروني",disabled:A})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"newUserPhone",children:"رقم الهاتف"}),(0,l.jsx)(v.p,{id:"newUserPhone",value:y.phone,onChange:e=>f({...y,phone:e.target.value}),placeholder:"أدخل رقم الهاتف",disabled:A})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"newUserRole",children:"الصلاحية *"}),(0,l.jsxs)(w.l6,{value:y.role,onValueChange:e=>f({...y,role:e}),disabled:A,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{})}),(0,l.jsxs)(w.gC,{children:[(0,l.jsx)(w.eb,{value:"VIEWER",children:"مطلع (عرض فقط)"}),(0,l.jsx)(w.eb,{value:"DATA_ENTRY",children:"مدخل بيانات"}),(0,l.jsx)(w.eb,{value:"MEMBER_VIEWER",children:"مطلع على عضو معين"}),(0,l.jsx)(w.eb,{value:"GALLERY_VIEWER",children:"مطلع على المعرض فقط"}),(0,l.jsx)(w.eb,{value:"ADMIN",children:"مدير"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"newUserPassword",children:"كلمة المرور *"}),(0,l.jsx)(v.p,{id:"newUserPassword",type:"password",value:y.password,onChange:e=>f({...y,password:e.target.value}),placeholder:"أدخل كلمة المرور",disabled:A})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"newUserConfirmPassword",children:"تأكيد كلمة المرور *"}),(0,l.jsx)(v.p,{id:"newUserConfirmPassword",type:"password",value:y.confirmPassword,onChange:e=>f({...y,confirmPassword:e.target.value}),placeholder:"أعد إدخال كلمة المرور",disabled:A})]})]}),(0,l.jsxs)("div",{className:"flex justify-end gap-3 pt-4",children:[(0,l.jsx)(c.$,{variant:"outline",onClick:()=>b(!1),disabled:A,children:"إلغاء"}),(0,l.jsxs)(c.$,{onClick:P,disabled:A||!y.name||!y.email||!y.password,className:"bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white",children:[A?(0,l.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"}):(0,l.jsx)(en,{className:"w-4 h-4 ml-2"}),"إضافة المستخدم"]})]})]})]}),C&&(0,l.jsxs)(V.Zp,{className:"border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50 shadow-xl",children:[(0,l.jsx)(V.aR,{className:"bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-t-lg",children:(0,l.jsxs)(V.ZB,{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("div",{className:"p-2 bg-white bg-opacity-20 rounded-lg",children:(0,l.jsx)(ex.A,{className:"w-6 h-6 text-white"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-xl font-bold",children:"تعديل المستخدم"}),(0,l.jsx)("p",{className:"text-blue-100 text-sm mt-1",children:C.name})]})]}),(0,l.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>S(null),className:"bg-white bg-opacity-20 border-white border-opacity-30 text-white hover:bg-white hover:bg-opacity-30",children:"إلغاء"})]})}),(0,l.jsxs)(V.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"editUserName",children:"الاسم الكامل"}),(0,l.jsx)(v.p,{id:"editUserName",value:C.name,onChange:e=>S({...C,name:e.target.value}),disabled:A})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"editUserEmail",children:"البريد الإلكتروني"}),(0,l.jsx)(v.p,{id:"editUserEmail",type:"email",value:C.email,onChange:e=>S({...C,email:e.target.value}),disabled:A})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"editUserPhone",children:"رقم الهاتف"}),(0,l.jsx)(v.p,{id:"editUserPhone",value:C.phone||"",onChange:e=>S({...C,phone:e.target.value}),disabled:A})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"editUserRole",children:"الصلاحية"}),(0,l.jsxs)(w.l6,{value:C.role,onValueChange:e=>S({...C,role:e}),disabled:A,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{})}),(0,l.jsxs)(w.gC,{children:[(0,l.jsx)(w.eb,{value:"VIEWER",children:"مطلع (عرض فقط)"}),(0,l.jsx)(w.eb,{value:"DATA_ENTRY",children:"مدخل بيانات"}),(0,l.jsx)(w.eb,{value:"MEMBER_VIEWER",children:"مطلع على عضو معين"}),(0,l.jsx)(w.eb,{value:"GALLERY_VIEWER",children:"مطلع على المعرض فقط"}),(0,l.jsx)(w.eb,{value:"ADMIN",children:"مدير"})]})]})]})]}),(0,l.jsxs)("div",{className:"flex justify-end gap-3 pt-4",children:[(0,l.jsx)(c.$,{variant:"outline",onClick:()=>S(null),disabled:A,children:"إلغاء"}),(0,l.jsxs)(c.$,{onClick:()=>q(C.id,{name:C.name,email:C.email,phone:C.phone,role:C.role}),disabled:A,className:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white",children:[A?(0,l.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"}):(0,l.jsx)(em.A,{className:"w-4 h-4 ml-2"}),"حفظ التغييرات"]})]})]})]}),J&&(0,l.jsx)(ei,{users:p,onClose:()=>R(!1)})]})}var eu=a(11273),eg=a(14163),eb="Progress",[ey,ev]=(0,eu.A)(eb),[eN,ef]=ey(eb),ew=i.forwardRef((e,s)=>{var a,i;let{__scopeProgress:t,value:r=null,max:c,getValueLabel:n=eS,...d}=e;(c||0===c)&&!eJ(c)&&console.error((a=`${c}`,`Invalid prop \`max\` of value \`${a}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let o=eJ(c)?c:100;null===r||eR(r,o)||console.error((i=`${r}`,`Invalid prop \`value\` of value \`${i}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let x=eR(r,o)?r:null,h=eE(x)?n(x,o):void 0;return(0,l.jsx)(eN,{scope:t,value:x,max:o,children:(0,l.jsx)(eg.sG.div,{"aria-valuemax":o,"aria-valuemin":0,"aria-valuenow":eE(x)?x:void 0,"aria-valuetext":h,role:"progressbar","data-state":eA(x,o),"data-value":x??void 0,"data-max":o,...d,ref:s})})});ew.displayName=eb;var eC="ProgressIndicator",ek=i.forwardRef((e,s)=>{let{__scopeProgress:a,...i}=e,t=ef(eC,a);return(0,l.jsx)(eg.sG.div,{"data-state":eA(t.value,t.max),"data-value":t.value??void 0,"data-max":t.max,...i,ref:s})});function eS(e,s){return`${Math.round(e/s*100)}%`}function eA(e,s){return null==e?"indeterminate":e===s?"complete":"loading"}function eE(e){return"number"==typeof e}function eJ(e){return eE(e)&&!isNaN(e)&&e>0}function eR(e,s){return eE(e)&&!isNaN(e)&&e<=s&&e>=0}ek.displayName=eC;let eD=i.forwardRef(({className:e,value:s,...a},i)=>(0,l.jsx)(ew,{ref:i,className:(0,C.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...a,children:(0,l.jsx)(ek,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(s||0)}%)`}})}));eD.displayName=ew.displayName;let eM=(0,o.A)("hard-drive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]]);var eP=a(78122),eq=a(77026);let eV=(0,o.A)("cloud",[["path",{d:"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z",key:"p7xjir"}]]);var eF=a(31158);let eI={autoBackup:{enabled:!0,frequency:"daily",time:"02:00",retentionDays:30,includeFiles:!0,includeDatabase:!0,includeSettings:!0},storage:{location:"local",localPath:"./backups",cloudProvider:"",cloudCredentials:{accessKey:"",secretKey:"",bucket:"",region:""}},importExport:{allowDataExport:!0,allowDataImport:!0,exportFormats:["json","csv","xlsx"],maxFileSize:100,requireConfirmation:!0},database:{enableOptimization:!0,autoVacuum:!0,compressionLevel:6,encryptBackups:!0}};function eL({settings:e,onChange:s,canEdit:a}){let[t,r]=(0,i.useState)(eI),[o,x]=(0,i.useState)(0),[h,m]=(0,i.useState)(!1),[p,u]=(0,i.useState)(null),[g,b]=(0,i.useState)("0 MB"),y=(e,a)=>{let l=e.split("."),i={...t};1===l.length?i={...i,[l[0]]:a}:2===l.length?i={...i,[l[0]]:{...i[l[0]],[l[1]]:a}}:3===l.length&&(i={...i,[l[0]]:{...i[l[0]],[l[1]]:{...i[l[0]][l[1]],[l[2]]:a}}}),r(i),s(i)},f=async()=>{if(a){m(!0),x(0);try{for(let e=0;e<=100;e+=10)x(e),await new Promise(e=>setTimeout(e,200));u(new Date),alert("تم إنشاء النسخة الاحتياطية بنجاح!")}catch(e){alert("فشل في إنشاء النسخة الاحتياطية")}finally{m(!1),x(0)}}},C=async()=>{if(a&&confirm("هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية."))try{alert("تم استعادة النسخة الاحتياطية بنجاح!")}catch(e){alert("فشل في استعادة النسخة الاحتياطية")}},S=async e=>{if(a)try{let s=document.createElement("a");s.href=`/api/export?format=${e}`,s.download=`diwan-data.${e}`,s.click()}catch(e){alert("فشل في تصدير البيانات")}};return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)(V.Zp,{children:[(0,l.jsx)(V.aR,{children:(0,l.jsxs)(V.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(j,{className:"w-5 h-5 text-blue-600"}),"معلومات النسخ الاحتياطية"]})}),(0,l.jsxs)(V.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,l.jsx)(J.A,{className:"w-8 h-8 text-blue-600 mx-auto mb-2"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"آخر نسخة احتياطية"}),(0,l.jsx)("p",{className:"font-semibold",children:p?p.toLocaleDateString("ar-SA"):"لا توجد"})]}),(0,l.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,l.jsx)(eM,{className:"w-8 h-8 text-green-600 mx-auto mb-2"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"حجم النسخة الاحتياطية"}),(0,l.jsx)("p",{className:"font-semibold",children:g})]}),(0,l.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,l.jsx)(em.A,{className:"w-8 h-8 text-green-600 mx-auto mb-2"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"الحالة"}),(0,l.jsx)(n.E,{variant:"outline",className:"text-green-600 border-green-200",children:"جاهز"})]})]}),h&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("span",{className:"text-sm text-gray-600",children:"جاري إنشاء النسخة الاحتياطية..."}),(0,l.jsxs)("span",{className:"text-sm font-medium",children:[o,"%"]})]}),(0,l.jsx)(eD,{value:o,className:"w-full"})]}),(0,l.jsxs)("div",{className:"flex gap-3",children:[(0,l.jsxs)(c.$,{onClick:f,disabled:!a||h,className:"bg-blue-600 hover:bg-blue-700",children:[h?(0,l.jsx)(eP.A,{className:"w-4 h-4 ml-2 animate-spin"}):(0,l.jsx)(eq.A,{className:"w-4 h-4 ml-2"}),"إنشاء نسخة احتياطية"]}),(0,l.jsxs)(c.$,{variant:"outline",onClick:C,disabled:!a||!p,children:[(0,l.jsx)(B.A,{className:"w-4 h-4 ml-2"}),"استعادة النسخة الاحتياطية"]})]})]})]}),(0,l.jsxs)(V.Zp,{children:[(0,l.jsx)(V.aR,{children:(0,l.jsxs)(V.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(X.A,{className:"w-5 h-5 text-green-600"}),"النسخ الاحتياطي التلقائي"]})}),(0,l.jsxs)(V.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"تفعيل النسخ الاحتياطي التلقائي"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إنشاء نسخ احتياطية تلقائياً"})]}),(0,l.jsx)(k,{checked:t.autoBackup.enabled,onCheckedChange:e=>y("autoBackup.enabled",e),disabled:!a})]}),t.autoBackup.enabled&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"تكرار النسخ الاحتياطي"}),(0,l.jsxs)(w.l6,{value:t.autoBackup.frequency,onValueChange:e=>y("autoBackup.frequency",e),disabled:!a,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{})}),(0,l.jsxs)(w.gC,{children:[(0,l.jsx)(w.eb,{value:"daily",children:"يومياً"}),(0,l.jsx)(w.eb,{value:"weekly",children:"أسبوعياً"}),(0,l.jsx)(w.eb,{value:"monthly",children:"شهرياً"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"backupTime",children:"وقت النسخ الاحتياطي"}),(0,l.jsx)(v.p,{id:"backupTime",type:"time",value:t.autoBackup.time,onChange:e=>y("autoBackup.time",e.target.value),disabled:!a})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"مدة الاحتفاظ بالنسخ الاحتياطية (يوم)"}),(0,l.jsxs)(w.l6,{value:t.autoBackup.retentionDays.toString(),onValueChange:e=>y("autoBackup.retentionDays",parseInt(e)),disabled:!a,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{})}),(0,l.jsxs)(w.gC,{children:[(0,l.jsx)(w.eb,{value:"7",children:"أسبوع واحد"}),(0,l.jsx)(w.eb,{value:"30",children:"شهر واحد"}),(0,l.jsx)(w.eb,{value:"90",children:"3 أشهر"}),(0,l.jsx)(w.eb,{value:"365",children:"سنة واحدة"}),(0,l.jsx)(w.eb,{value:"0",children:"دائماً"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)(N.J,{children:"محتويات النسخة الاحتياطية"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"قاعدة البيانات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"جميع البيانات والجداول"})]}),(0,l.jsx)(k,{checked:t.autoBackup.includeDatabase,onCheckedChange:e=>y("autoBackup.includeDatabase",e),disabled:!a})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"الملفات المرفوعة"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"الصور والمستندات"})]}),(0,l.jsx)(k,{checked:t.autoBackup.includeFiles,onCheckedChange:e=>y("autoBackup.includeFiles",e),disabled:!a})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"إعدادات النظام"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"التكوينات والإعدادات"})]}),(0,l.jsx)(k,{checked:t.autoBackup.includeSettings,onCheckedChange:e=>y("autoBackup.includeSettings",e),disabled:!a})]})]})]})]})]})]}),(0,l.jsxs)(V.Zp,{children:[(0,l.jsx)(V.aR,{children:(0,l.jsxs)(V.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(eV,{className:"w-5 h-5 text-purple-600"}),"إعدادات التخزين"]})}),(0,l.jsxs)(V.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"موقع التخزين"}),(0,l.jsxs)(w.l6,{value:t.storage.location,onValueChange:e=>y("storage.location",e),disabled:!a,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{})}),(0,l.jsxs)(w.gC,{children:[(0,l.jsx)(w.eb,{value:"local",children:"محلي فقط"}),(0,l.jsx)(w.eb,{value:"cloud",children:"سحابي فقط"}),(0,l.jsx)(w.eb,{value:"both",children:"محلي وسحابي"})]})]})]}),("local"===t.storage.location||"both"===t.storage.location)&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"localPath",children:"مسار التخزين المحلي"}),(0,l.jsx)(v.p,{id:"localPath",value:t.storage.localPath,onChange:e=>y("storage.localPath",e.target.value),disabled:!a,placeholder:"./backups"})]}),("cloud"===t.storage.location||"both"===t.storage.location)&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"مزود الخدمة السحابية"}),(0,l.jsxs)(w.l6,{value:t.storage.cloudProvider,onValueChange:e=>y("storage.cloudProvider",e),disabled:!a,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{placeholder:"اختر مزود الخدمة"})}),(0,l.jsxs)(w.gC,{children:[(0,l.jsx)(w.eb,{value:"aws",children:"Amazon S3"}),(0,l.jsx)(w.eb,{value:"google",children:"Google Cloud Storage"}),(0,l.jsx)(w.eb,{value:"azure",children:"Microsoft Azure"}),(0,l.jsx)(w.eb,{value:"digitalocean",children:"DigitalOcean Spaces"})]})]})]}),t.storage.cloudProvider&&(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"accessKey",children:"مفتاح الوصول"}),(0,l.jsx)(v.p,{id:"accessKey",type:"password",value:t.storage.cloudCredentials.accessKey,onChange:e=>y("storage.cloudCredentials.accessKey",e.target.value),disabled:!a,placeholder:"Access Key"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"secretKey",children:"المفتاح السري"}),(0,l.jsx)(v.p,{id:"secretKey",type:"password",value:t.storage.cloudCredentials.secretKey,onChange:e=>y("storage.cloudCredentials.secretKey",e.target.value),disabled:!a,placeholder:"Secret Key"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"bucket",children:"اسم الحاوية"}),(0,l.jsx)(v.p,{id:"bucket",value:t.storage.cloudCredentials.bucket,onChange:e=>y("storage.cloudCredentials.bucket",e.target.value),disabled:!a,placeholder:"bucket-name"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"region",children:"المنطقة"}),(0,l.jsx)(v.p,{id:"region",value:t.storage.cloudCredentials.region,onChange:e=>y("storage.cloudCredentials.region",e.target.value),disabled:!a,placeholder:"us-east-1"})]})]})]})]})]}),(0,l.jsxs)(V.Zp,{children:[(0,l.jsx)(V.aR,{children:(0,l.jsxs)(V.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(E.A,{className:"w-5 h-5 text-orange-600"}),"الاستيراد والتصدير"]})}),(0,l.jsxs)(V.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"السماح بتصدير البيانات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"تمكين المستخدمين من تصدير البيانات"})]}),(0,l.jsx)(k,{checked:t.importExport.allowDataExport,onCheckedChange:e=>y("importExport.allowDataExport",e),disabled:!a})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"السماح باستيراد البيانات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"تمكين المستخدمين من استيراد البيانات"})]}),(0,l.jsx)(k,{checked:t.importExport.allowDataImport,onCheckedChange:e=>y("importExport.allowDataImport",e),disabled:!a})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"يتطلب تأكيد"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"طلب تأكيد قبل الاستيراد/التصدير"})]}),(0,l.jsx)(k,{checked:t.importExport.requireConfirmation,onCheckedChange:e=>y("importExport.requireConfirmation",e),disabled:!a})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{htmlFor:"maxFileSize",children:"الحد الأقصى لحجم الملف (MB)"}),(0,l.jsx)(v.p,{id:"maxFileSize",type:"number",min:"1",max:"1000",value:t.importExport.maxFileSize,onChange:e=>y("importExport.maxFileSize",parseInt(e.target.value)),disabled:!a})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)(N.J,{children:"تصدير البيانات"}),(0,l.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2",children:[(0,l.jsxs)(c.$,{variant:"outline",onClick:()=>S("json"),disabled:!a||!t.importExport.allowDataExport,className:"flex items-center gap-2",children:[(0,l.jsx)(eF.A,{className:"w-4 h-4"}),"JSON"]}),(0,l.jsxs)(c.$,{variant:"outline",onClick:()=>S("csv"),disabled:!a||!t.importExport.allowDataExport,className:"flex items-center gap-2",children:[(0,l.jsx)(eF.A,{className:"w-4 h-4"}),"CSV"]}),(0,l.jsxs)(c.$,{variant:"outline",onClick:()=>S("xlsx"),disabled:!a||!t.importExport.allowDataExport,className:"flex items-center gap-2",children:[(0,l.jsx)(eF.A,{className:"w-4 h-4"}),"Excel"]}),(0,l.jsxs)(c.$,{variant:"outline",onClick:()=>S("pdf"),disabled:!a||!t.importExport.allowDataExport,className:"flex items-center gap-2",children:[(0,l.jsx)(eF.A,{className:"w-4 h-4"}),"PDF"]})]})]})]})]}),(0,l.jsxs)(V.Zp,{children:[(0,l.jsx)(V.aR,{children:(0,l.jsxs)(V.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(d.A,{className:"w-5 h-5 text-red-600"}),"إعدادات قاعدة البيانات"]})}),(0,l.jsxs)(V.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"تحسين قاعدة البيانات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"تحسين الأداء تلقائياً"})]}),(0,l.jsx)(k,{checked:t.database.enableOptimization,onCheckedChange:e=>y("database.enableOptimization",e),disabled:!a})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"التنظيف التلقائي"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إزالة البيانات المحذوفة تلقائياً"})]}),(0,l.jsx)(k,{checked:t.database.autoVacuum,onCheckedChange:e=>y("database.autoVacuum",e),disabled:!a})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(N.J,{children:"تشفير النسخ الاحتياطية"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"حماية النسخ الاحتياطية بالتشفير"})]}),(0,l.jsx)(k,{checked:t.database.encryptBackups,onCheckedChange:e=>y("database.encryptBackups",e),disabled:!a})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"مستوى الضغط"}),(0,l.jsxs)(w.l6,{value:t.database.compressionLevel.toString(),onValueChange:e=>y("database.compressionLevel",parseInt(e)),disabled:!a,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{})}),(0,l.jsxs)(w.gC,{children:[(0,l.jsx)(w.eb,{value:"1",children:"منخفض (سريع)"}),(0,l.jsx)(w.eb,{value:"3",children:"متوسط"}),(0,l.jsx)(w.eb,{value:"6",children:"عالي (افتراضي)"}),(0,l.jsx)(w.eb,{value:"9",children:"أقصى (بطيء)"})]})]})]}),(0,l.jsx)("div",{className:"p-4 bg-amber-50 border border-amber-200 rounded-lg",children:(0,l.jsxs)("div",{className:"flex items-start gap-3",children:[(0,l.jsx)(Q.A,{className:"w-5 h-5 text-amber-600 mt-0.5"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-amber-800 font-medium",children:"تحذير مهم"}),(0,l.jsx)("p",{className:"text-amber-700 text-sm mt-1",children:"تأكد من إنشاء نسخة احتياطية قبل تغيير إعدادات قاعدة البيانات. بعض التغييرات قد تتطلب إعادة تشغيل النظام."})]})]})})]})]})]})}var eT=a(63503);function eU({onExportSettings:e,onImportSettings:s,onResetSettings:a,canEdit:t}){let[r,d]=(0,i.useState)(null),[o,x]=(0,i.useState)(!1),[h,m]=(0,i.useState)([{id:1,timestamp:new Date(Date.now()-72e5),user:"أحمد محمد",action:"تحديث إعدادات المظهر",details:"تغيير اللون الأساسي إلى الأزرق",category:"appearance"},{id:2,timestamp:new Date(Date.now()-864e5),user:"سارة أحمد",action:"تحديث إعدادات الإشعارات",details:"تفعيل إشعارات البريد الإلكتروني",category:"notifications"},{id:3,timestamp:new Date(Date.now()-2592e5),user:"محمد علي",action:"تحديث إعدادات الأمان",details:"تغيير سياسة كلمات المرور",category:"security"}]),p=e=>{let s={general:{label:"عام",color:"bg-blue-100 text-blue-800"},appearance:{label:"مظهر",color:"bg-purple-100 text-purple-800"},notifications:{label:"إشعارات",color:"bg-green-100 text-green-800"},security:{label:"أمان",color:"bg-red-100 text-red-800"},backup:{label:"نسخ احتياطي",color:"bg-orange-100 text-orange-800"}},a=s[e]||s.general;return(0,l.jsx)(n.E,{className:a.color,children:a.label})};return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"diwan-card",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,l.jsx)("div",{className:"p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg",children:(0,l.jsx)(E.A,{className:"w-5 h-5 text-white"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"استيراد وتصدير الإعدادات"})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("h4",{className:"font-medium",children:"تصدير الإعدادات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"احفظ نسخة من إعداداتك الحالية كملف JSON"}),(0,l.jsxs)(c.$,{onClick:()=>{e(),y.oR.success("تم تصدير الإعدادات بنجاح")},disabled:!t,className:"w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg",children:[(0,l.jsx)(eF.A,{className:"w-4 h-4 ml-2"}),"تصدير الإعدادات"]})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("h4",{className:"font-medium",children:"استيراد الإعدادات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"استعد إعداداتك من ملف JSON محفوظ مسبقاً"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(v.p,{type:"file",accept:".json",onChange:e=>{let s=e.target.files?.[0];s&&d(s)},disabled:!t}),(0,l.jsxs)(c.$,{onClick:()=>{r&&(s(r),d(null),y.oR.success("تم استيراد الإعدادات بنجاح"))},disabled:!t||!r,className:"w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-lg disabled:opacity-50",children:[(0,l.jsx)(B.A,{className:"w-4 h-4 ml-2"}),"استيراد الإعدادات"]})]})]})]}),(0,l.jsx)("div",{className:"p-4 bg-amber-50 border border-amber-200 rounded-lg",children:(0,l.jsxs)("div",{className:"flex items-start gap-3",children:[(0,l.jsx)(Q.A,{className:"w-5 h-5 text-amber-600 mt-0.5"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-amber-800 font-medium",children:"تحذير"}),(0,l.jsx)("p",{className:"text-amber-700 text-sm mt-1",children:"استيراد الإعدادات سيستبدل جميع الإعدادات الحالية. تأكد من تصدير إعداداتك الحالية أولاً كنسخة احتياطية."})]})]})})]})]}),(0,l.jsxs)("div",{className:"diwan-card",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,l.jsx)("div",{className:"p-2 bg-gradient-to-r from-red-500 to-red-600 rounded-lg",children:(0,l.jsx)(u,{className:"w-5 h-5 text-white"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"إعادة تعيين الإعدادات"})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("p",{className:"text-gray-600",children:"إعادة تعيين جميع الإعدادات إلى القيم الافتراضية. هذا الإجراء لا يمكن التراجع عنه."}),(0,l.jsx)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,l.jsxs)("div",{className:"flex items-start gap-3",children:[(0,l.jsx)(Q.A,{className:"w-5 h-5 text-red-600 mt-0.5"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-red-800 font-medium",children:"تحذير شديد"}),(0,l.jsx)("p",{className:"text-red-700 text-sm mt-1",children:"سيتم حذف جميع الإعدادات المخصصة وإعادة تعيينها إلى القيم الافتراضية. تأكد من تصدير إعداداتك أولاً إذا كنت تريد الاحتفاظ بها."})]})]})}),(0,l.jsxs)(c.$,{onClick:()=>{confirm("هل أنت متأكد من إعادة تعيين جميع الإعدادات؟ لا يمكن التراجع عن هذا الإجراء.")&&(a(),y.oR.success("تم إعادة تعيين الإعدادات بنجاح"))},disabled:!t,variant:"destructive",className:"w-full md:w-auto",children:[(0,l.jsx)(u,{className:"w-4 h-4 ml-2"}),"إعادة تعيين جميع الإعدادات"]})]})]}),(0,l.jsxs)("div",{className:"diwan-card",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("div",{className:"p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg",children:(0,l.jsx)(ec,{className:"w-5 h-5 text-white"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"سجل التغييرات"})]}),(0,l.jsxs)(eT.lG,{open:o,onOpenChange:x,children:[(0,l.jsx)(eT.zM,{asChild:!0,children:(0,l.jsx)(c.$,{variant:"outline",size:"sm",children:"عرض السجل الكامل"})}),(0,l.jsxs)(eT.Cf,{className:"max-w-[50vw] max-h-[80vh] overflow-y-auto",children:[(0,l.jsx)(eT.c7,{children:(0,l.jsx)(eT.L3,{children:"سجل التغييرات الكامل"})}),(0,l.jsx)("div",{className:"space-y-4",children:h.map(e=>(0,l.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[p(e.category),(0,l.jsx)("span",{className:"font-medium",children:e.action})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-500",children:[(0,l.jsx)(X.A,{className:"w-4 h-4"}),e.timestamp.toLocaleString("ar-SA")]})]}),(0,l.jsx)("p",{className:"text-gray-600 mb-2",children:e.details}),(0,l.jsxs)("p",{className:"text-sm text-gray-500",children:["بواسطة: ",e.user]})]},e.id))})]})]})]}),(0,l.jsx)("div",{children:(0,l.jsx)("div",{className:"space-y-3",children:h.slice(0,3).map(e=>(0,l.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[p(e.category),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium text-sm",children:e.action}),(0,l.jsx)("p",{className:"text-xs text-gray-600",children:e.details})]})]}),(0,l.jsxs)("div",{className:"text-right",children:[(0,l.jsx)("p",{className:"text-xs text-gray-500",children:e.user}),(0,l.jsx)("p",{className:"text-xs text-gray-400",children:e.timestamp.toLocaleDateString("ar-SA")})]})]},e.id))})})]}),(0,l.jsxs)("div",{className:"diwan-card",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,l.jsx)("div",{className:"p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg",children:(0,l.jsx)(j,{className:"w-5 h-5 text-white"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"معلومات النظام"})]}),(0,l.jsx)("div",{children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"إصدار النظام"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"1.0.0"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"آخر تحديث"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"2024-12-21"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"قاعدة البيانات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"SQLite"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(N.J,{children:"حالة النظام"}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(em.A,{className:"w-4 h-4 text-green-600"}),(0,l.jsx)("span",{className:"text-sm text-green-600",children:"يعمل بشكل طبيعي"})]})]})]})})]})]})}function eW(){let{data:e}=(0,t.useSession)(),[s,a]=(0,i.useState)("general"),[o,v]=(0,i.useState)({general:{},appearance:{},notifications:{},security:{},backup:{},advanced:{}}),[N,f]=(0,i.useState)(!0),[w,C]=(0,i.useState)(!1),[k,S]=(0,i.useState)(!1),A=e?.user?.role==="ADMIN",E=async()=>{try{f(!0);let e=await fetch("/api/settings");if(e.ok){let s=await e.json();v(s)}}catch(e){console.error("خطأ في تحميل الإعدادات:",e),y.oR.error("فشل في تحميل الإعدادات")}finally{f(!1)}},J=async()=>{if(!A)return void y.oR.error("ليس لديك صلاحية لحفظ الإعدادات");try{if(C(!0),(await fetch("/api/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)})).ok)y.oR.success("تم حفظ الإعدادات بنجاح"),S(!1),window.location.reload();else throw Error("فشل في حفظ الإعدادات")}catch(e){console.error("خطأ في حفظ الإعدادات:",e),y.oR.error("فشل في حفظ الإعدادات")}finally{C(!1)}},R=async()=>{if(!A)return void y.oR.error("ليس لديك صلاحية لإعادة تعيين الإعدادات");if(confirm("هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟"))try{if((await fetch("/api/settings/reset",{method:"POST"})).ok)y.oR.success("تم إعادة تعيين الإعدادات بنجاح"),E(),S(!1);else throw Error("فشل في إعادة تعيين الإعدادات")}catch(e){console.error("خطأ في إعادة تعيين الإعدادات:",e),y.oR.error("فشل في إعادة تعيين الإعدادات")}},D=(e,s)=>{v(a=>({...a,[e]:s})),S(!0)},M=()=>{let e=new Blob([JSON.stringify(o,null,2)],{type:"application/json"}),s=URL.createObjectURL(e),a=document.createElement("a");a.href=s,a.download=`diwan-settings-${new Date().toISOString().split("T")[0]}.json`,a.click(),URL.revokeObjectURL(s)},P=e=>{let s=new FileReader;s.onload=e=>{try{let s=JSON.parse(e.target?.result);v(s),S(!0),y.oR.success("تم استيراد الإعدادات بنجاح")}catch{y.oR.error("خطأ في ملف الإعدادات")}},s.readAsText(e)},V=[{id:"general",label:"الإعدادات العامة",icon:d.A,component:q,description:"إعدادات النظام الأساسية"},{id:"appearance",label:"المظهر والواجهة",icon:x,component:_,description:"تخصيص الألوان والخطوط"},{id:"notifications",label:"الإشعارات",icon:h.A,component:es,description:"إعدادات الإشعارات والتنبيهات"},{id:"security",label:"الأمان والصلاحيات",icon:m.A,component:ep,description:"إعدادات الأمان وكلمات المرور",adminOnly:!0},{id:"backup",label:"النسخ الاحتياطي",icon:j,component:eL,description:"إدارة النسخ الاحتياطية والاستيراد/التصدير",adminOnly:!0},{id:"advanced",label:"إعدادات متقدمة",icon:d.A,component:eU,description:"إعدادات متقدمة وسجل التغييرات",adminOnly:!0}].filter(e=>!e.adminOnly||A);return N?(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"الإعدادات"}),(0,l.jsx)("p",{className:"text-gray-600",children:"إدارة إعدادات النظام والتخصيص"})]}),(0,l.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,l.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})})]}):(0,l.jsxs)("div",{className:"settings-page space-y-6 p-6",children:[(0,l.jsx)("div",{className:"settings-card diwan-card",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-4",children:[(0,l.jsx)("div",{className:"settings-header-icon",children:(0,l.jsx)(d.A,{className:"w-8 h-8 text-white"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"إعدادات النظام"}),(0,l.jsx)("p",{className:"text-gray-600 mt-1",children:"إدارة وتخصيص إعدادات ديوان آل أبو علوش"})]})]}),A&&(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[k&&(0,l.jsxs)(n.E,{className:"bg-amber-100 text-amber-800 border-amber-200",children:[(0,l.jsx)(p.A,{className:"w-3 h-3 ml-1"}),"تغييرات غير محفوظة"]}),(0,l.jsxs)(c.$,{variant:"outline",onClick:R,disabled:w,className:"text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300",children:[(0,l.jsx)(u,{className:"w-4 h-4 ml-2"}),"إعادة تعيين"]}),(0,l.jsxs)(c.$,{onClick:J,disabled:w||!k,className:"settings-button bg-gradient-to-r from-sky-500 to-sky-600 hover:from-sky-600 hover:to-sky-700 text-white shadow-lg",children:[w?(0,l.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"}):(0,l.jsx)(g.A,{className:"w-4 h-4 ml-2"}),"حفظ التغييرات"]})]})]})}),!A&&(0,l.jsx)("div",{className:"diwan-card border-amber-200 bg-gradient-to-r from-amber-50 to-orange-50",children:(0,l.jsxs)("div",{className:"flex items-center gap-4",children:[(0,l.jsx)("div",{className:"p-2 bg-amber-100 rounded-lg",children:(0,l.jsx)(b.A,{className:"w-5 h-5 text-amber-600"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-amber-800 font-semibold",children:"وضع العرض فقط"}),(0,l.jsx)("p",{className:"text-amber-700 text-sm mt-1",children:"ليس لديك صلاحية لتعديل الإعدادات. يمكنك عرض الإعدادات الحالية فقط."})]})]})}),(0,l.jsx)("div",{className:"settings-card diwan-card",children:(0,l.jsxs)(r.tU,{value:s,onValueChange:a,className:"space-y-6",children:[(0,l.jsx)(r.j7,{className:"settings-tabs grid w-full grid-cols-3 lg:grid-cols-6 gap-2 p-2",children:V.map(e=>{let s=e.icon;return(0,l.jsxs)(r.Xi,{value:e.id,className:"settings-tab-trigger flex items-center gap-2 text-sm px-4 py-3",children:[(0,l.jsx)(s,{className:"w-4 h-4"}),(0,l.jsx)("span",{className:"hidden sm:inline font-medium",children:e.label})]},e.id)})}),V.map(e=>{let s=e.component;return(0,l.jsx)(r.av,{value:e.id,className:"mt-6",children:(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex items-center gap-4 pb-4 border-b border-gray-200",children:[(0,l.jsx)("div",{className:"p-2 bg-gradient-to-r from-sky-500 to-sky-600 rounded-lg",children:(0,l.jsx)(e.icon,{className:"w-5 h-5 text-white"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:e.label}),(0,l.jsx)("p",{className:"text-gray-600 text-sm",children:e.description})]})]}),(0,l.jsx)("div",{children:(0,l.jsx)(s,{settings:o[e.id],onChange:s=>D(e.id,s),onExportSettings:M,onImportSettings:P,onResetSettings:R,canEdit:A})})]})},e.id)})]})})]})}},48340:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(62688).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},48730:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},51361:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(62688).A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56085:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(62688).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},61148:(e,s,a)=>{Promise.resolve().then(a.bind(a,41760))},62623:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>l});let l=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\ابوعلوش\\\\diwan-abu-alosh\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\settings\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},64021:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},74075:e=>{"use strict";e.exports=require("zlib")},77026:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(62688).A)("archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80013:(e,s,a)=>{"use strict";a.d(s,{J:()=>r});var l=a(60687),i=a(43210),t=a(4780);let r=i.forwardRef(({className:e,...s},a)=>(0,l.jsx)("label",{ref:a,className:(0,t.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e),...s}));r.displayName="Label"},81362:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>r.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d});var l=a(65239),i=a(48088),t=a(88170),r=a.n(t),c=a(30893),n={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>c[e]);a.d(s,n);let d={children:["",{children:["dashboard",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,62623)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\settings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,63144)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\settings\\page.tsx"],x={require:a,loadChunk:()=>Promise.resolve()},h=new l.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/dashboard/settings/page",pathname:"/dashboard/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},81630:e=>{"use strict";e.exports=require("http")},85763:(e,s,a)=>{"use strict";a.d(s,{Xi:()=>d,av:()=>o,j7:()=>n,tU:()=>c});var l=a(60687),i=a(43210),t=a(55146),r=a(4780);let c=t.bL,n=i.forwardRef(({className:e,...s},a)=>(0,l.jsx)(t.B8,{ref:a,className:(0,r.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));n.displayName=t.B8.displayName;let d=i.forwardRef(({className:e,...s},a)=>(0,l.jsx)(t.l9,{ref:a,className:(0,r.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));d.displayName=t.l9.displayName;let o=i.forwardRef(({className:e,...s},a)=>(0,l.jsx)(t.UC,{ref:a,className:(0,r.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));o.displayName=t.UC.displayName},88233:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},93508:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(62688).A)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},93613:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96882:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(62688).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},99891:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),l=s.X(0,[4243,5663,4999,3412,5442,7934,5498,1726,2131,5662,2635,9472,5977,6154],()=>a(81362));module.exports=l})();