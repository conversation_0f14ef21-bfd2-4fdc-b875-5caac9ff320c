{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "WsUnalvfLC8q6thQbM7XpflHInutTa69tutWo9d2/Nc=", "__NEXT_PREVIEW_MODE_ID": "7255b0f55eca88055cb07bcae285832a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "343b3f7f9ea460eef043a00acd03bf9d90d9e540de536652612fc31824a1ee3f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bb3c3c6948d4c0c677451861a7f0fa58027c90ad05c08e163c85bbd0e367f524"}}}, "sortedMiddleware": ["/"], "functions": {}}