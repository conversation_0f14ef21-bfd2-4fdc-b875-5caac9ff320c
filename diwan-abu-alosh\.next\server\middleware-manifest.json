{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "WsUnalvfLC8q6thQbM7XpflHInutTa69tutWo9d2/Nc=", "__NEXT_PREVIEW_MODE_ID": "090e221a5a988146df96f279f7a06f15", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "89e079aed29d7ec696df9c20c003aa62489cb2f2a321815c36e8f633fe43bebc", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e0cb56946c9efb3a15f991a11568aad88742e285c171cf8a6cbd3403bf3477c0"}}}, "sortedMiddleware": ["/"], "functions": {}}