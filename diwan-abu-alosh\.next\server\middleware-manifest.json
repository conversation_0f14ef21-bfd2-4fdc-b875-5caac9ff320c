{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "6qshpUJv37OXQaPKHlsiB", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "WsUnalvfLC8q6thQbM7XpflHInutTa69tutWo9d2/Nc=", "__NEXT_PREVIEW_MODE_ID": "fd70bc5b5ae7056458a6a276d6783b3b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c756d67e49be2e9d5962072aac7736d0f5270ced0157a0eaf7302b44cb1fba9b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fb6cc83d62edee9ff80d69393b01686f473c5540441a5401e37adf49ef749759"}}}, "functions": {}, "sortedMiddleware": ["/"]}