(()=>{var e={};e.id=1673,e.ids=[1673],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{N:()=>i});var s=t(13581),n=t(85663),a=t(31183);let i={providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"البريد الإلكتروني",type:"email"},password:{label:"كلمة المرور",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await a.z.user.findUnique({where:{email:e.email}});return r&&await n.Ay.compare(e.password,r.password)?{id:r.id,email:r.email,name:r.name,role:r.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>n});var s=t(96330);let n=globalThis.prisma??new s.PrismaClient},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59051:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>g,serverHooks:()=>v,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>w});var s={};t.r(s),t.d(s,{GET:()=>x,POST:()=>m});var n=t(96559),a=t(48088),i=t(37719),o=t(32190),u=t(19854),p=t(12909),l=t(31183),c=t(45697);let d=c.z.object({amount:c.z.number().positive("المبلغ يجب أن يكون أكبر من صفر"),date:c.z.string().transform(e=>new Date(e)),description:c.z.string().min(1,"الوصف مطلوب"),category:c.z.enum(["MEETINGS","EVENTS","MAINTENANCE","SOCIAL","GENERAL"]),recipient:c.z.string().optional().nullable(),notes:c.z.string().optional().nullable()});async function x(e){try{if(!await (0,u.getServerSession)(p.N))return o.NextResponse.json({error:"غير مصرح"},{status:401});let{searchParams:r}=new URL(e.url),t=r.get("search")||"",s=r.get("category")||"all",n=parseInt(r.get("page")||"1"),a=parseInt(r.get("limit")||"10"),i=(n-1)*a,c={};t&&(c.OR=[{description:{contains:t}},{recipient:{contains:t}},{notes:{contains:t}}]),"all"!==s&&(c.category=s);let[d,x]=await Promise.all([l.z.expense.findMany({where:c,skip:i,take:a,orderBy:{date:"desc"},include:{createdBy:{select:{name:!0}}}}),l.z.expense.count({where:c})]);return o.NextResponse.json({expenses:d,pagination:{page:n,limit:a,total:x,pages:Math.ceil(x/a)}})}catch(e){return console.error("خطأ في جلب المصروفات:",e),o.NextResponse.json({error:"حدث خطأ في جلب المصروفات"},{status:500})}}async function m(e){try{let r=await (0,u.getServerSession)(p.N);if(!r)return o.NextResponse.json({error:"غير مصرح"},{status:401});if("VIEWER"===r.user.role)return o.NextResponse.json({error:"ليس لديك صلاحية لإضافة المصروفات"},{status:403});let t=await e.json(),s=d.parse(t),n=await l.z.expense.create({data:{...s,createdById:r.user.id},include:{createdBy:{select:{name:!0}}}});return o.NextResponse.json(n,{status:201})}catch(e){if(console.error("خطأ في إضافة المصروف:",e),"ZodError"===e.name)return o.NextResponse.json({error:"بيانات غير صحيحة",details:e.errors},{status:400});return o.NextResponse.json({error:"حدث خطأ في إضافة المصروف"},{status:500})}}let g=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/expenses/route",pathname:"/api/expenses",filename:"route",bundlePath:"app/api/expenses/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\expenses\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:y,workUnitAsyncStorage:w,serverHooks:v}=g;function h(){return(0,i.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:w})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,5663,4999,3412,580,5697],()=>t(59051));module.exports=s})();