'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Button } from '@/components/ui/button'
// import { Badge } from '@/components/ui/badge'
// import { useTheme } from '@/components/theme-provider'
import {
  Palette,
  Type,
  Image,
  Monitor,
  Sun,
  Moon,
  Smartphone,
  Eye,
  RotateCcw,
  Upload,
  Sparkles
} from 'lucide-react'

interface AppearanceSettingsProps {
  settings: any
  onChange: (settings: any) => void
  canEdit: boolean
}

interface AppearanceSettingsData {
  // الثيم والألوان
  theme: 'light' | 'dark' | 'system'
  primaryColor: string
  secondaryColor: string
  accentColor: string
  backgroundColor: string
  textColor: string
  
  // الخطوط
  fontFamily: string
  fontSize: string
  fontWeight: string
  lineHeight: string
  
  // الشعار والعلامة التجارية
  logo: string
  favicon: string
  brandName: string
  brandColors: {
    primary: string
    secondary: string
  }
  
  // تخصيص الواجهة
  sidebarStyle: 'default' | 'compact' | 'minimal'
  headerStyle: 'default' | 'compact' | 'transparent'
  cardStyle: 'default' | 'bordered' | 'shadow' | 'flat'
  buttonStyle: 'default' | 'rounded' | 'square'
  
  // إعدادات الاستجابة
  enableAnimations: boolean
  enableTransitions: boolean
  enableShadows: boolean
  enableGradients: boolean
  
  // إعدادات إضافية
  customCSS: string
  enableCustomCSS: boolean
}

const defaultSettings: AppearanceSettingsData = {
  theme: 'light',
  primaryColor: '#3b82f6',
  secondaryColor: '#64748b',
  accentColor: '#f59e0b',
  backgroundColor: '#ffffff',
  textColor: '#1f2937',
  fontFamily: 'Cairo',
  fontSize: '14px',
  fontWeight: 'normal',
  lineHeight: '1.5',
  logo: '',
  favicon: '',
  brandName: 'ديوان آل أبو علوش',
  brandColors: {
    primary: '#3b82f6',
    secondary: '#64748b'
  },
  sidebarStyle: 'default',
  headerStyle: 'default',
  cardStyle: 'default',
  buttonStyle: 'default',
  enableAnimations: true,
  enableTransitions: true,
  enableShadows: true,
  enableGradients: false,
  customCSS: '',
  enableCustomCSS: false
}

const colorPresets = [
  { name: 'الأزرق الافتراضي', primary: '#3b82f6', secondary: '#64748b' },
  { name: 'الأخضر الطبيعي', primary: '#10b981', secondary: '#6b7280' },
  { name: 'البنفسجي الملكي', primary: '#8b5cf6', secondary: '#6b7280' },
  { name: 'الأحمر الكلاسيكي', primary: '#ef4444', secondary: '#6b7280' },
  { name: 'البرتقالي الدافئ', primary: '#f97316', secondary: '#6b7280' },
  { name: 'الوردي الناعم', primary: '#ec4899', secondary: '#6b7280' },
  { name: 'الذهبي الفاخر', primary: '#f59e0b', secondary: '#78716c' },
  { name: 'الأزرق الداكن', primary: '#1e40af', secondary: '#4b5563' }
]

const fontFamilies = [
  { value: 'Cairo', label: 'Cairo (الافتراضي)' },
  { value: 'Almarai', label: 'Almarai' },
  { value: 'Tajawal', label: 'Tajawal' },
  { value: 'Amiri', label: 'Amiri' },
  { value: 'Scheherazade', label: 'Scheherazade' },
  { value: 'Noto Sans Arabic', label: 'Noto Sans Arabic' },
  { value: 'IBM Plex Sans Arabic', label: 'IBM Plex Sans Arabic' }
]

const fontSizes = [
  { value: '12px', label: 'صغير (12px)' },
  { value: '14px', label: 'متوسط (14px)' },
  { value: '16px', label: 'كبير (16px)' },
  { value: '18px', label: 'كبير جداً (18px)' }
]

export default function AppearanceSettings({ settings, onChange, canEdit }: AppearanceSettingsProps) {
  const [localSettings, setLocalSettings] = useState<AppearanceSettingsData>(defaultSettings)
  const [previewMode, setPreviewMode] = useState(false)
  const [uploading, setUploading] = useState<{ logo: boolean; favicon: boolean }>({ logo: false, favicon: false })
  // const { updateSettings, applyTheme } = useTheme()

  useEffect(() => {
    if (settings) {
      setLocalSettings({ ...defaultSettings, ...settings })
      // تطبيق الإعدادات المحفوظة عند التحميل
      Object.entries({ ...defaultSettings, ...settings }).forEach(([key, value]) => {
        applyThemeChanges(key, value)
      })
    }
  }, [settings])

  // تطبيق الإعدادات الافتراضية عند التحميل الأول
  useEffect(() => {
    // تحميل الإعدادات المحفوظة أولاً
    const savedSettings = loadFromLocalStorage()
    const finalSettings = { ...defaultSettings, ...savedSettings }

    // تطبيق الإعدادات
    Object.entries(finalSettings).forEach(([key, value]) => {
      applyThemeChanges(key, value)
    })

    // تحديث الحالة المحلية
    setLocalSettings(finalSettings)
  }, [])

  const handleChange = (key: keyof AppearanceSettingsData, value: any) => {
    const newSettings = { ...localSettings, [key]: value }
    setLocalSettings(newSettings)
    onChange(newSettings)

    // تطبيق التغييرات فوراً في الواجهة
    applyThemeChanges(key, value)

    // حفظ في localStorage للاحتفاظ بالتغييرات
    saveToLocalStorage(key, value)
  }

  // دالة لحفظ الإعدادات في localStorage
  const saveToLocalStorage = (key: string, value: any) => {
    try {
      const savedSettings = JSON.parse(localStorage.getItem('diwan-appearance-settings') || '{}')
      savedSettings[key] = value
      localStorage.setItem('diwan-appearance-settings', JSON.stringify(savedSettings))
    } catch (error) {
      console.error('خطأ في حفظ الإعدادات:', error)
    }
  }

  // دالة لتحميل الإعدادات من localStorage
  const loadFromLocalStorage = () => {
    try {
      const savedSettings = JSON.parse(localStorage.getItem('diwan-appearance-settings') || '{}')
      return savedSettings
    } catch (error) {
      console.error('خطأ في تحميل الإعدادات:', error)
      return {}
    }
  }

  // دالة لتطبيق التغييرات فوراً
  const applyThemeChanges = (key: string, value: any) => {
    const root = document.documentElement

    switch (key) {
      case 'primaryColor':
        root.style.setProperty('--theme-primary-color', value)
        root.style.setProperty('--primary', value)
        break
      case 'secondaryColor':
        root.style.setProperty('--theme-secondary-color', value)
        root.style.setProperty('--secondary', value)
        break
      case 'accentColor':
        root.style.setProperty('--theme-accent-color', value)
        root.style.setProperty('--accent', value)
        break
      case 'backgroundColor':
        root.style.setProperty('--theme-background-color', value)
        root.style.setProperty('--background', value)
        break
      case 'textColor':
        root.style.setProperty('--theme-text-color', value)
        root.style.setProperty('--foreground', value)
        break
      case 'fontFamily':
        root.style.setProperty('--theme-font-family', value)
        document.body.style.fontFamily = value
        break
      case 'fontSize':
        root.style.setProperty('--theme-font-size', value)
        document.body.style.fontSize = value
        break
      case 'fontWeight':
        root.style.setProperty('--theme-font-weight', value)
        document.body.style.fontWeight = value
        break
      case 'lineHeight':
        root.style.setProperty('--theme-line-height', value)
        document.body.style.lineHeight = value
        break
      case 'theme':
        if (value === 'dark') {
          document.documentElement.classList.add('dark')
          document.documentElement.classList.add('dark-theme')
          document.documentElement.classList.remove('light')
        } else {
          document.documentElement.classList.add('light')
          document.documentElement.classList.remove('dark')
          document.documentElement.classList.remove('dark-theme')
        }
        break
      case 'brandName':
        document.title = value || 'ديوان آل أبو علوش'
        break
    }

    // إضافة كلاس theme-applied للتطبيق
    document.body.classList.add('theme-applied')
  }

  const applyColorPreset = (preset: typeof colorPresets[0]) => {
    const newSettings = {
      ...localSettings,
      primaryColor: preset.primary,
      secondaryColor: preset.secondary,
      brandColors: {
        primary: preset.primary,
        secondary: preset.secondary
      }
    }
    setLocalSettings(newSettings)
    onChange(newSettings)

    // تطبيق التغييرات فوراً
    applyThemeChanges('primaryColor', preset.primary)
    applyThemeChanges('secondaryColor', preset.secondary)
  }

  const resetToDefaults = () => {
    setLocalSettings(defaultSettings)
    onChange(defaultSettings)

    // تطبيق الإعدادات الافتراضية
    Object.entries(defaultSettings).forEach(([key, value]) => {
      applyThemeChanges(key, value)
    })
  }

  // رفع الملفات
  const handleFileUpload = async (file: File, type: 'logo' | 'favicon') => {
    if (!canEdit) return

    // التحقق من نوع الملف
    if (!file.type.startsWith('image/')) {
      alert('يرجى اختيار ملف صورة صحيح')
      return
    }

    // التحقق من حجم الملف (5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت')
      return
    }

    setUploading(prev => ({ ...prev, [type]: true }))

    const formData = new FormData()
    formData.append('file', file)
    formData.append('type', type)

    try {
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      })

      if (response.ok) {
        const data = await response.json()
        handleChange(type, data.url)

        // إشعار نجاح
        const event = new CustomEvent('showToast', {
          detail: { message: 'تم رفع الملف بنجاح', type: 'success' }
        })
        window.dispatchEvent(event)
      } else {
        const errorData = await response.json()
        alert(errorData.error || 'فشل في رفع الملف')
      }
    } catch (error) {
      console.error('خطأ في رفع الملف:', error)
      alert('حدث خطأ في رفع الملف')
    } finally {
      setUploading(prev => ({ ...prev, [type]: false }))
    }
  }

  // معالج اختيار الملف
  const handleFileSelect = (type: 'logo' | 'favicon') => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'image/*'
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (file) {
        handleFileUpload(file, type)
      }
    }
    input.click()
  }

  // معالج السحب والإفلات
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }

  const handleDrop = (e: React.DragEvent, type: 'logo' | 'favicon') => {
    e.preventDefault()
    e.stopPropagation()

    const files = e.dataTransfer.files
    if (files.length > 0) {
      const file = files[0]
      if (file.type.startsWith('image/')) {
        handleFileUpload(file, type)
      }
    }
  }

  return (
    <div className="space-y-6">
      {/* مؤشر المعاينة المباشرة */}
      <Card className="border-green-200 bg-gradient-to-r from-green-50 to-emerald-50 animate-pulse">
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg animate-bounce">
              <Sparkles className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-green-800 font-semibold">🎨 المعاينة المباشرة مفعلة</p>
              <p className="text-green-700 text-sm">
                جميع التغييرات ستظهر فوراً في الواجهة! جرب تغيير الألوان أو الخطوط لترى النتيجة مباشرة.
              </p>
            </div>
            <div className="mr-auto">
              <div className="flex gap-1">
                <div className="w-3 h-3 bg-blue-500 rounded-full animate-ping"></div>
                <div className="w-3 h-3 bg-green-500 rounded-full animate-ping" style={{animationDelay: '0.2s'}}></div>
                <div className="w-3 h-3 bg-yellow-500 rounded-full animate-ping" style={{animationDelay: '0.4s'}}></div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* الثيم والألوان الأساسية */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="w-5 h-5 text-blue-600" />
            الثيم والألوان
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <Label className="text-sm font-medium text-gray-700">نمط الثيم</Label>
            <div className="grid grid-cols-3 gap-3">
              <Button
                variant={localSettings.theme === 'light' ? 'default' : 'outline'}
                onClick={() => handleChange('theme', 'light')}
                disabled={!canEdit}
                className={`flex items-center gap-2 py-3 ${
                  localSettings.theme === 'light'
                    ? 'bg-gradient-to-r from-sky-500 to-sky-600 text-white shadow-lg'
                    : 'hover:bg-sky-50 hover:border-sky-300'
                }`}
              >
                <Sun className="w-4 h-4" />
                فاتح
              </Button>
              <Button
                variant={localSettings.theme === 'dark' ? 'default' : 'outline'}
                onClick={() => handleChange('theme', 'dark')}
                disabled={!canEdit}
                className={`flex items-center gap-2 py-3 ${
                  localSettings.theme === 'dark'
                    ? 'bg-gradient-to-r from-gray-700 to-gray-800 text-white shadow-lg'
                    : 'hover:bg-gray-50 hover:border-gray-300'
                }`}
              >
                <Moon className="w-4 h-4" />
                داكن
              </Button>
              <Button
                variant={localSettings.theme === 'system' ? 'default' : 'outline'}
                onClick={() => handleChange('theme', 'system')}
                disabled={!canEdit}
                className={`flex items-center gap-2 py-3 ${
                  localSettings.theme === 'system'
                    ? 'bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-lg'
                    : 'hover:bg-purple-50 hover:border-purple-300'
                }`}
              >
                <Monitor className="w-4 h-4" />
                النظام
              </Button>
            </div>
          </div>

          <div className="space-y-3">
            <Label>الألوان المحددة مسبقاً</Label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {colorPresets.map((preset, index) => (
                <Button
                  key={index}
                  variant="outline"
                  onClick={() => applyColorPreset(preset)}
                  disabled={!canEdit}
                  className="h-auto p-3 flex flex-col items-center gap-2"
                >
                  <div className="flex gap-1">
                    <div
                      className="w-4 h-4 rounded-full border"
                      style={{ backgroundColor: preset.primary }}
                    />
                    <div
                      className="w-4 h-4 rounded-full border"
                      style={{ backgroundColor: preset.secondary }}
                    />
                  </div>
                  <span className="text-xs text-center">{preset.name}</span>
                </Button>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="primaryColor">اللون الأساسي</Label>
              <div className="flex gap-2">
                <Input
                  id="primaryColor"
                  type="color"
                  value={localSettings.primaryColor}
                  onChange={(e) => handleChange('primaryColor', e.target.value)}
                  disabled={!canEdit}
                  className="w-12 h-10 p-1 border rounded"
                />
                <Input
                  value={localSettings.primaryColor}
                  onChange={(e) => handleChange('primaryColor', e.target.value)}
                  disabled={!canEdit}
                  placeholder="#3b82f6"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="secondaryColor">اللون الثانوي</Label>
              <div className="flex gap-2">
                <Input
                  id="secondaryColor"
                  type="color"
                  value={localSettings.secondaryColor}
                  onChange={(e) => handleChange('secondaryColor', e.target.value)}
                  disabled={!canEdit}
                  className="w-12 h-10 p-1 border rounded"
                />
                <Input
                  value={localSettings.secondaryColor}
                  onChange={(e) => handleChange('secondaryColor', e.target.value)}
                  disabled={!canEdit}
                  placeholder="#64748b"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="accentColor">لون التمييز</Label>
              <div className="flex gap-2">
                <Input
                  id="accentColor"
                  type="color"
                  value={localSettings.accentColor}
                  onChange={(e) => handleChange('accentColor', e.target.value)}
                  disabled={!canEdit}
                  className="w-12 h-10 p-1 border rounded"
                />
                <Input
                  value={localSettings.accentColor}
                  onChange={(e) => handleChange('accentColor', e.target.value)}
                  disabled={!canEdit}
                  placeholder="#f59e0b"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* الخطوط والنصوص */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Type className="w-5 h-5 text-green-600" />
            الخطوط والنصوص
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>نوع الخط</Label>
              <Select
                value={localSettings.fontFamily}
                onValueChange={(value) => handleChange('fontFamily', value)}
                disabled={!canEdit}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر نوع الخط" />
                </SelectTrigger>
                <SelectContent>
                  {fontFamilies.map((font) => (
                    <SelectItem key={font.value} value={font.value}>
                      <span style={{ fontFamily: font.value }}>{font.label}</span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>حجم الخط</Label>
              <Select
                value={localSettings.fontSize}
                onValueChange={(value) => handleChange('fontSize', value)}
                disabled={!canEdit}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر حجم الخط" />
                </SelectTrigger>
                <SelectContent>
                  {fontSizes.map((size) => (
                    <SelectItem key={size.value} value={size.value}>
                      {size.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label>معاينة الخط</Label>
            <div
              className="p-4 border rounded-lg bg-gray-50"
              style={{
                fontFamily: localSettings.fontFamily,
                fontSize: localSettings.fontSize,
                lineHeight: localSettings.lineHeight
              }}
            >
              <p className="text-lg font-bold mb-2">ديوان آل أبو علوش</p>
              <p className="mb-2">هذا نص تجريبي لمعاينة الخط المختار. يمكنك رؤية كيف سيظهر النص في النظام.</p>
              <p className="text-sm text-gray-600">الأرقام: 1234567890</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* الشعار والعلامة التجارية */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Image className="w-5 h-5 text-purple-600" />
            الشعار والعلامة التجارية
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="brandName">اسم العلامة التجارية</Label>
            <Input
              id="brandName"
              value={localSettings.brandName}
              onChange={(e) => handleChange('brandName', e.target.value)}
              disabled={!canEdit}
              placeholder="ديوان آل أبو علوش"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>الشعار الرئيسي</Label>
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors cursor-pointer"
                onDragOver={handleDragOver}
                onDrop={(e) => handleDrop(e, 'logo')}
                onClick={() => handleFileSelect('logo')}
              >
                {localSettings.logo ? (
                  <div className="space-y-2">
                    <img
                      src={localSettings.logo}
                      alt="الشعار"
                      className="max-h-16 mx-auto"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleChange('logo', '')}
                      disabled={!canEdit}
                    >
                      إزالة الشعار
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {uploading.logo ? (
                      <>
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                        <p className="text-sm text-blue-600">جاري رفع الشعار...</p>
                      </>
                    ) : (
                      <>
                        <Upload className="w-8 h-8 text-gray-400 mx-auto" />
                        <p className="text-sm text-gray-600">اسحب الشعار هنا أو انقر للرفع</p>
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={!canEdit || uploading.logo}
                          onClick={(e) => {
                            e.stopPropagation()
                            handleFileSelect('logo')
                          }}
                        >
                          رفع شعار
                        </Button>
                      </>
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label>أيقونة الموقع (Favicon)</Label>
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors cursor-pointer"
                onDragOver={handleDragOver}
                onDrop={(e) => handleDrop(e, 'favicon')}
                onClick={() => handleFileSelect('favicon')}
              >
                {localSettings.favicon ? (
                  <div className="space-y-2">
                    <img
                      src={localSettings.favicon}
                      alt="الأيقونة"
                      className="w-8 h-8 mx-auto"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleChange('favicon', '')}
                      disabled={!canEdit}
                    >
                      إزالة الأيقونة
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {uploading.favicon ? (
                      <>
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                        <p className="text-xs text-blue-600">جاري رفع الأيقونة...</p>
                      </>
                    ) : (
                      <>
                        <Upload className="w-6 h-6 text-gray-400 mx-auto" />
                        <p className="text-xs text-gray-600">32x32 px</p>
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={!canEdit || uploading.favicon}
                          onClick={(e) => {
                            e.stopPropagation()
                            handleFileSelect('favicon')
                          }}
                        >
                          رفع أيقونة
                        </Button>
                      </>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* تخصيص الواجهة */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Smartphone className="w-5 h-5 text-orange-600" />
            تخصيص الواجهة
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>نمط الشريط الجانبي</Label>
              <Select
                value={localSettings.sidebarStyle}
                onValueChange={(value) => handleChange('sidebarStyle', value)}
                disabled={!canEdit}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="default">افتراضي</SelectItem>
                  <SelectItem value="compact">مضغوط</SelectItem>
                  <SelectItem value="minimal">بسيط</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>نمط الرأس</Label>
              <Select
                value={localSettings.headerStyle}
                onValueChange={(value) => handleChange('headerStyle', value)}
                disabled={!canEdit}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="default">افتراضي</SelectItem>
                  <SelectItem value="compact">مضغوط</SelectItem>
                  <SelectItem value="transparent">شفاف</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>نمط البطاقات</Label>
              <Select
                value={localSettings.cardStyle}
                onValueChange={(value) => handleChange('cardStyle', value)}
                disabled={!canEdit}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="default">افتراضي</SelectItem>
                  <SelectItem value="bordered">محدد</SelectItem>
                  <SelectItem value="shadow">ظلال</SelectItem>
                  <SelectItem value="flat">مسطح</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>نمط الأزرار</Label>
              <Select
                value={localSettings.buttonStyle}
                onValueChange={(value) => handleChange('buttonStyle', value)}
                disabled={!canEdit}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="default">افتراضي</SelectItem>
                  <SelectItem value="rounded">دائري</SelectItem>
                  <SelectItem value="square">مربع</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>الحركات والانتقالات</Label>
                <p className="text-sm text-gray-600">تفعيل الحركات المتحركة</p>
              </div>
              <Switch
                checked={localSettings.enableAnimations}
                onCheckedChange={(checked) => handleChange('enableAnimations', checked)}
                disabled={!canEdit}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>الظلال</Label>
                <p className="text-sm text-gray-600">إضافة ظلال للعناصر</p>
              </div>
              <Switch
                checked={localSettings.enableShadows}
                onCheckedChange={(checked) => handleChange('enableShadows', checked)}
                disabled={!canEdit}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>التدرجات اللونية</Label>
                <p className="text-sm text-gray-600">استخدام التدرجات في الخلفيات</p>
              </div>
              <Switch
                checked={localSettings.enableGradients}
                onCheckedChange={(checked) => handleChange('enableGradients', checked)}
                disabled={!canEdit}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* CSS مخصص */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="w-5 h-5 text-red-600" />
            CSS مخصص
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label>تفعيل CSS مخصص</Label>
              <p className="text-sm text-gray-600">إضافة أنماط CSS مخصصة</p>
            </div>
            <Switch
              checked={localSettings.enableCustomCSS}
              onCheckedChange={(checked) => handleChange('enableCustomCSS', checked)}
              disabled={!canEdit}
            />
          </div>

          {localSettings.enableCustomCSS && (
            <div className="space-y-2">
              <Label htmlFor="customCSS">كود CSS المخصص</Label>
              <textarea
                id="customCSS"
                value={localSettings.customCSS}
                onChange={(e) => handleChange('customCSS', e.target.value)}
                disabled={!canEdit}
                placeholder="/* أضف كود CSS المخصص هنا */"
                className="w-full h-32 p-3 border rounded-lg font-mono text-sm"
              />
              <p className="text-xs text-gray-500">
                تحذير: استخدم CSS مخصص بحذر. قد يؤثر على مظهر النظام.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* أزرار الإجراءات */}
      {canEdit && (
        <Card>
          <CardContent className="p-4">
            <div className="flex justify-between">
              <Button
                variant="outline"
                onClick={resetToDefaults}
                className="text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300"
              >
                <RotateCcw className="w-4 h-4 ml-2" />
                إعادة تعيين الافتراضي
              </Button>

              <Button
                onClick={() => setPreviewMode(!previewMode)}
                className="bg-gradient-to-r from-sky-500 to-sky-600 hover:from-sky-600 hover:to-sky-700 text-white"
              >
                <Eye className="w-4 h-4 ml-2" />
                {previewMode ? 'إخفاء المعاينة' : 'معاينة التغييرات'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
