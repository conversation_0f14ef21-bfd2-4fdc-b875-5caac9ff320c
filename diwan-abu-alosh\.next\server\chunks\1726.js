"use strict";exports.id=1726,exports.ids=[1726],exports.modules={43:(e,t,r)=>{r.d(t,{jH:()=>l});var n=r(43210);r(60687);var o=n.createContext(void 0);function l(e){let t=n.useContext(o);return e||t||"ltr"}},8730:(e,t,r)=>{r.d(t,{TL:()=>u});var n=r(43210),o=r(98599),l=r(60687);function u(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...l}=e;if(n.isValidElement(r)){var u;let e,i,c=(u=r,(i=(e=Object.getOwnPropertyDescriptor(u.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?u.ref:(i=(e=Object.getOwnPropertyDescriptor(u,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?u.props.ref:u.props.ref||u.ref),f=function(e,t){let r={...t};for(let n in t){let o=e[n],l=t[n];/^on[A-Z]/.test(n)?o&&l?r[n]=(...e)=>{let t=l(...e);return o(...e),t}:o&&(r[n]=o):"style"===n?r[n]={...o,...l}:"className"===n&&(r[n]=[o,l].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==n.Fragment&&(f.ref=t?(0,o.t)(t,c):c),n.cloneElement(r,f)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:o,...u}=e,i=n.Children.toArray(o),f=i.find(c);if(f){let e=f.props.children,o=i.map(t=>t!==f?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...u,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,l.jsx)(t,{...u,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}var i=Symbol("radix.slottable");function c(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}},9510:(e,t,r)=>{r.d(t,{N:()=>c});var n=r(43210),o=r(11273),l=r(98599),u=r(8730),i=r(60687);function c(e){let t=e+"CollectionProvider",[r,c]=(0,o.A)(t),[f,a]=r(t,{collectionRef:{current:null},itemMap:new Map}),s=e=>{let{scope:t,children:r}=e,o=n.useRef(null),l=n.useRef(new Map).current;return(0,i.jsx)(f,{scope:t,itemMap:l,collectionRef:o,children:r})};s.displayName=t;let d=e+"CollectionSlot",p=(0,u.TL)(d),m=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=a(d,r),u=(0,l.s)(t,o.collectionRef);return(0,i.jsx)(p,{ref:u,children:n})});m.displayName=d;let v=e+"CollectionItemSlot",y="data-radix-collection-item",h=(0,u.TL)(v),x=n.forwardRef((e,t)=>{let{scope:r,children:o,...u}=e,c=n.useRef(null),f=(0,l.s)(t,c),s=a(v,r);return n.useEffect(()=>(s.itemMap.set(c,{ref:c,...u}),()=>void s.itemMap.delete(c))),(0,i.jsx)(h,{...{[y]:""},ref:f,children:o})});return x.displayName=v,[{Provider:s,Slot:m,ItemSlot:x},function(t){let r=a(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${y}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},c]}var f=new WeakMap;function a(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=s(t),o=n>=0?n:r+n;return o<0||o>=r?-1:o}(e,t);return -1===r?void 0:e[r]}function s(e){return e!=e||0===e?0:Math.trunc(e)}},11273:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(43210),o=r(60687);function l(e,t=[]){let r=[],u=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return u.scopeName=e,[function(t,l){let u=n.createContext(l),i=r.length;r=[...r,l];let c=t=>{let{scope:r,children:l,...c}=t,f=r?.[e]?.[i]||u,a=n.useMemo(()=>c,Object.values(c));return(0,o.jsx)(f.Provider,{value:a,children:l})};return c.displayName=t+"Provider",[c,function(r,o){let c=o?.[e]?.[i]||u,f=n.useContext(c);if(f)return f;if(void 0!==l)return l;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(u,...t)]}},13495:(e,t,r)=>{r.d(t,{c:()=>o});var n=r(43210);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},14163:(e,t,r)=>{r.d(t,{hO:()=>c,sG:()=>i});var n=r(43210),o=r(51215),l=r(8730),u=r(60687),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,l.TL)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(o?r:t,{...l,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function c(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},65551:(e,t,r)=>{r.d(t,{i:()=>i});var n,o=r(43210),l=r(66156),u=(n||(n=r.t(o,2)))[" useInsertionEffect ".trim().toString()]||l.N;function i({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[l,i,c]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),l=o.useRef(r),i=o.useRef(t);return u(()=>{i.current=t},[t]),o.useEffect(()=>{l.current!==r&&(i.current?.(r),l.current=r)},[r,l]),[r,n,i]}({defaultProp:t,onChange:r}),f=void 0!==e,a=f?e:l;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==f){let t=f?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=f},[f,n])}return[a,o.useCallback(t=>{if(f){let r="function"==typeof t?t(e):t;r!==e&&c.current?.(r)}else i(t)},[f,e,i,c])]}Symbol("RADIX:SYNC_STATE")},66156:(e,t,r)=>{r.d(t,{N:()=>o});var n=r(43210),o=globalThis?.document?n.useLayoutEffect:()=>{}},70569:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},96963:(e,t,r)=>{r.d(t,{B:()=>c});var n,o=r(43210),l=r(66156),u=(n||(n=r.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),i=0;function c(e){let[t,r]=o.useState(u());return(0,l.N)(()=>{e||r(e=>e??String(i++))},[e]),e||(t?`radix-${t}`:"")}},98599:(e,t,r)=>{r.d(t,{s:()=>u,t:()=>l});var n=r(43210);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function u(...e){return n.useCallback(l(...e),e)}}};