(()=>{var e={};e.id=971,e.ids=[971],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>i});var s=r(13581),a=r(85663),n=r(31183);let i={providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"البريد الإلكتروني",type:"email"},password:{label:"كلمة المرور",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let t=await n.z.user.findUnique({where:{email:e.email}});return t&&await a.Ay.compare(e.password,t.password)?{id:t.id,email:t.email,name:t.name,role:t.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});var s=r(96330);let a=globalThis.prisma??new s.PrismaClient},41491:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>d,serverHooks:()=>x,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>c});var a=r(96559),n=r(48088),i=r(37719),o=r(32190),u=r(19854),l=r(12909),p=r(31183);async function c(e,{params:t}){try{if(!await (0,u.getServerSession)(l.N))return o.NextResponse.json({error:"غير مصرح"},{status:401});let{id:r}=await t,{searchParams:s}=new URL(e.url),a=s.get("startDate"),n=s.get("endDate"),i=s.get("year"),c=await p.z.member.findUnique({where:{id:r},select:{id:!0,name:!0,phone:!0,email:!0,address:!0,photo:!0,status:!0,createdAt:!0}});if(!c)return o.NextResponse.json({error:"العضو غير موجود"},{status:404});let d={};if(a&&n)d={date:{gte:new Date(a),lte:new Date(n)}};else if(i){let e=parseInt(i);d={date:{gte:new Date(e,0,1),lte:new Date(e,11,31)}}}let m=await p.z.income.findMany({where:{memberId:r,...d},orderBy:{date:"desc"},include:{createdBy:{select:{name:!0}}}}),g=m.reduce((e,t)=>e+t.amount,0),x=m.length,w=m.reduce((e,t)=>(e[t.type]||(e[t.type]={count:0,amount:0}),e[t.type].count++,e[t.type].amount+=t.amount,e),{}),h=i?parseInt(i):new Date().getFullYear(),y=Array.from({length:12},(e,t)=>{var r;let s=t+1,a=m.filter(e=>{let r=new Date(e.date);return r.getFullYear()===h&&r.getMonth()===t});return{month:s,monthName:(r=s,`شهر${r}`),count:a.length,amount:a.reduce((e,t)=>e+t.amount,0)}}),v=m.slice(0,5),f=m.length>0?m[m.length-1]:null,q=m.length>0?m[0]:null,b=y.filter(e=>e.count>0).length,D={member:c,summary:{totalAmount:g,transactionCount:x,averageMonthlyContribution:b>0?g/b:0,firstTransactionDate:f?.date||null,lastTransactionDate:q?.date||null},byType:w,monthlyData:y,recentTransactions:v,allTransactions:m,period:{startDate:a||`${h}-01-01`,endDate:n||`${h}-12-31`,year:h}};return o.NextResponse.json(D)}catch(e){return console.error("خطأ في جلب كشف الحساب:",e),o.NextResponse.json({error:"حدث خطأ في جلب كشف الحساب"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/members/[id]/account-statement/route",pathname:"/api/members/[id]/account-statement",filename:"route",bundlePath:"app/api/members/[id]/account-statement/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\[id]\\account-statement\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:x}=d;function w(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,5663,4999,3412,580],()=>r(41491));module.exports=s})();