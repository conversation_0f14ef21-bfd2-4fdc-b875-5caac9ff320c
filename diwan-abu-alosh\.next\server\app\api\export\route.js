(()=>{var e={};e.id=5620,e.ids=[5620],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>i});var s=r(13581),a=r(85663),n=r(31183);let i={providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"البريد الإلكتروني",type:"email"},password:{label:"كلمة المرور",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let t=await n.z.user.findUnique({where:{email:e.email}});return t&&await a.Ay.compare(e.password,t.password)?{id:t.id,email:t.email,name:t.name,role:t.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});var s=r(96330);let a=globalThis.prisma??new s.PrismaClient},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77516:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>w,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>y});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>m});var a=r(96559),n=r(48088),i=r(37719),o=r(32190),l=r(19854),c=r(12909),u=r(31183);async function p(e){try{let t,r,s,a=await (0,l.getServerSession)(c.N);if(!a||a.user?.role!=="ADMIN")return o.NextResponse.json({error:"غير مصرح"},{status:401});let{searchParams:n}=new URL(e.url),i=n.get("format")||"json",p=n.get("table")||"all",m={};("all"===p||"members"===p)&&(m.members=await u.z.member.findMany({include:{createdBy:{select:{name:!0,email:!0}}}})),("all"===p||"incomes"===p)&&(m.incomes=await u.z.income.findMany({include:{member:{select:{name:!0}},createdBy:{select:{name:!0,email:!0}}}})),("all"===p||"expenses"===p)&&(m.expenses=await u.z.expense.findMany({include:{createdBy:{select:{name:!0,email:!0}}}})),("all"===p||"activities"===p)&&(m.activities=await u.z.activity.findMany({include:{createdBy:{select:{name:!0,email:!0}},participants:{include:{member:{select:{name:!0}}}}}})),("all"===p||"gallery"===p)&&(m.gallery={folders:await u.z.galleryFolder.findMany({include:{creator:{select:{name:!0,email:!0}}}}),photos:await u.z.galleryPhoto.findMany({include:{uploader:{select:{name:!0,email:!0}},folder:{select:{title:!0}},activity:{select:{title:!0}}}})}),("all"===p||"notifications"===p)&&(m.notifications=await u.z.notification.findMany({include:{user:{select:{name:!0,email:!0}}}})),("all"===p||"settings"===p)&&(m.settings=await u.z.settings.findMany());let d=new Date().toISOString().split("T")[0];switch(i.toLowerCase()){case"json":t=JSON.stringify(m,null,2),r="application/json",s=`diwan-data-${d}.json`;break;case"csv":if(m.members){let e=m.members.map(e=>`"${e.name}","${e.phone||""}","${e.email||""}","${e.address||""}","${e.status}","${new Date(e.createdAt).toLocaleDateString("ar-SA")}"`).join("\n");t="الاسم,الهاتف,البريد الإلكتروني,العنوان,الحالة,تاريخ الإنشاء\n"+e}else t="لا توجد بيانات للتصدير";r="text/csv; charset=utf-8",s=`diwan-members-${d}.csv`;break;case"xlsx":t=JSON.stringify(m,null,2),r="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",s=`diwan-data-${d}.xlsx`;break;case"pdf":t=`تقرير ديوان آل أبو علوش - ${d}

${JSON.stringify(m,null,2)}`,r="application/pdf",s=`diwan-report-${d}.pdf`;break;default:return o.NextResponse.json({error:"تنسيق غير مدعوم"},{status:400})}return new o.NextResponse(t,{status:200,headers:{"Content-Type":r,"Content-Disposition":`attachment; filename="${s}"`,"Cache-Control":"no-cache"}})}catch(e){return console.error("خطأ في تصدير البيانات:",e),o.NextResponse.json({error:"خطأ في الخادم"},{status:500})}}async function m(e){try{let t=await (0,l.getServerSession)(c.N);if(!t||t.user?.role!=="ADMIN")return o.NextResponse.json({error:"غير مصرح"},{status:401});let{tables:r,format:s,filters:a,dateRange:n}=await e.json(),i={},p={};for(let e of(n?.start&&n?.end&&(p.createdAt={gte:new Date(n.start),lte:new Date(n.end)}),r))switch(e){case"members":i.members=await u.z.member.findMany({where:{...p,...a?.memberStatus&&{status:a.memberStatus}},include:{createdBy:{select:{name:!0,email:!0}}}});break;case"incomes":i.incomes=await u.z.income.findMany({where:{...p,...a?.incomeType&&{type:a.incomeType},...a?.memberId&&{memberId:a.memberId}},include:{member:{select:{name:!0}},createdBy:{select:{name:!0,email:!0}}}});break;case"expenses":i.expenses=await u.z.expense.findMany({where:{...p,...a?.expenseCategory&&{category:a.expenseCategory}},include:{createdBy:{select:{name:!0,email:!0}}}})}return o.NextResponse.json({success:!0,data:i,exportInfo:{timestamp:new Date().toISOString(),tables:r,format:s,recordCount:Object.values(i).reduce((e,t)=>e+(Array.isArray(t)?t.length:0),0)}})}catch(e){return console.error("خطأ في التصدير المخصص:",e),o.NextResponse.json({error:"خطأ في الخادم"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/export/route",pathname:"/api/export",filename:"route",bundlePath:"app/api/export/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\export\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:y,serverHooks:w}=d;function f(){return(0,i.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:y})}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,5663,4999,3412,580],()=>r(77516));module.exports=s})();