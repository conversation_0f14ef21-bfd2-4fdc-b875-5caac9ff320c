"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3068],{26126:(e,r,t)=>{t.d(r,{E:()=>d});var a=t(95155);t(12115);var o=t(74466),s=t(59434);let n=(0,o.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-100 text-green-800 hover:bg-green-200",warning:"border-transparent bg-slate-100 text-slate-800 hover:bg-slate-200"}},defaultVariants:{variant:"default"}});function d(e){let{className:r,variant:t,...o}=e;return(0,a.jsx)("div",{className:(0,s.cn)(n({variant:t}),r),...o})}},30285:(e,r,t)=>{t.d(r,{$:()=>l});var a=t(95155),o=t(12115),s=t(74466),n=t(59434);let d=(0,s.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-semibold transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-95",{variants:{variant:{default:"bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-primary-600 hover:to-primary-700",destructive:"bg-gradient-to-r from-danger-500 to-danger-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-danger-600 hover:to-danger-700",outline:"border-2 border-primary-300 bg-transparent text-primary-700 hover:bg-primary-50 hover:border-primary-400 hover:shadow-md",secondary:"bg-gradient-to-r from-secondary-100 to-secondary-200 text-secondary-800 shadow-md hover:shadow-lg hover:-translate-y-0.5 hover:from-secondary-200 hover:to-secondary-300",ghost:"text-primary-600 hover:bg-primary-100 hover:text-primary-800",link:"text-primary-600 underline-offset-4 hover:underline hover:text-primary-800",success:"bg-gradient-to-r from-success-500 to-success-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-success-600 hover:to-success-700",warning:"bg-gradient-to-r from-warning-500 to-warning-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-warning-600 hover:to-warning-700",info:"bg-gradient-to-r from-info-500 to-info-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-info-600 hover:to-info-700",accent:"bg-gradient-to-r from-gold-500 to-gold-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-gold-600 hover:to-gold-700"},size:{default:"h-11 px-6 py-2.5",sm:"h-9 rounded-lg px-4 text-xs",lg:"h-13 rounded-xl px-8 text-base",icon:"h-11 w-11",xs:"h-8 rounded-md px-3 text-xs"}},defaultVariants:{variant:"default",size:"default"}}),l=o.forwardRef((e,r)=>{let{className:t,variant:o,size:s,...l}=e;return(0,a.jsx)("button",{className:(0,n.cn)(d({variant:o,size:s,className:t})),ref:r,...l})});l.displayName="Button"},54165:(e,r,t)=>{t.d(r,{Cf:()=>l,Es:()=>c,HM:()=>x,L3:()=>f,c7:()=>i,lG:()=>d,rr:()=>m,zM:()=>u});var a=t(95155),o=t(12115),s=t(59434),n=t(54416);let d=e=>{let{open:r,onOpenChange:t,children:s}=e;return(o.useEffect(()=>{let e=e=>{"Escape"===e.key&&(null==t||t(!1))};return r&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[r,t]),r)?(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-black/60 backdrop-blur-md transition-opacity duration-300",onClick:()=>null==t?void 0:t(!1)}),(0,a.jsx)("div",{className:"relative z-50 max-h-[90vh] overflow-y-auto animate-in fade-in-0 zoom-in-95 duration-300",children:s})]}):null},l=o.forwardRef((e,r)=>{let{className:t,children:o,...n}=e;return(0,a.jsx)("div",{ref:r,className:(0,s.cn)("relative w-full max-w-[50vw] mx-4 bg-white rounded-2xl shadow-2xl border border-slate-200 p-0 overflow-hidden",t),...n,children:o})});l.displayName="DialogContent";let i=e=>{let{className:r,...t}=e;return(0,a.jsx)("div",{className:(0,s.cn)("flex flex-col space-y-2 text-center sm:text-right p-6 bg-gradient-to-r from-slate-50 to-slate-100 border-b border-slate-200",r),...t})};i.displayName="DialogHeader";let c=e=>{let{className:r,...t}=e;return(0,a.jsx)("div",{className:(0,s.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 sm:space-x-reverse p-6 bg-gradient-to-r from-slate-50 to-slate-100 border-t border-slate-200",r),...t})};c.displayName="DialogFooter";let f=o.forwardRef((e,r)=>{let{className:t,...o}=e;return(0,a.jsx)("h3",{ref:r,className:(0,s.cn)("text-xl font-bold leading-none tracking-tight text-slate-800",t),...o})});f.displayName="DialogTitle";let m=o.forwardRef((e,r)=>{let{className:t,...o}=e;return(0,a.jsx)("p",{ref:r,className:(0,s.cn)("text-sm text-slate-600 font-medium",t),...o})});m.displayName="DialogDescription";let u=o.forwardRef((e,r)=>{let{className:t,...o}=e;return(0,a.jsx)("button",{ref:r,className:t,...o})});u.displayName="DialogTrigger";let x=o.forwardRef((e,r)=>{let{className:t,onOpenChange:o,...d}=e;return(0,a.jsxs)("button",{ref:r,type:"button",onClick:()=>null==o?void 0:o(!1),className:(0,s.cn)("absolute left-4 top-4 rounded-full p-2 bg-white shadow-lg opacity-80 ring-offset-background transition-all duration-200 hover:opacity-100 hover:shadow-xl hover:scale-110 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 disabled:pointer-events-none z-10",t),...d,children:[(0,a.jsx)(n.A,{className:"h-4 w-4 text-slate-600"}),(0,a.jsx)("span",{className:"sr-only",children:"إغلاق"})]})});x.displayName="DialogClose"},59409:(e,r,t)=>{t.d(r,{bq:()=>m,eb:()=>y,gC:()=>p,l6:()=>c,yv:()=>f});var a=t(95155),o=t(12115),s=t(78893),n=t(66474),d=t(47863),l=t(5196),i=t(59434);let c=s.bL;s.YJ;let f=s.WT,m=o.forwardRef((e,r)=>{let{className:t,children:o,...d}=e;return(0,a.jsxs)(s.l9,{ref:r,className:(0,i.cn)("flex h-11 w-full items-center justify-between rounded-xl border-2 border-secondary-200 bg-white px-4 py-3 text-base font-medium text-secondary-700 transition-all duration-200 hover:border-secondary-300 focus:border-primary-500 focus:outline-none focus:ring-4 focus:ring-primary-500/20 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...d,children:[o,(0,a.jsx)(s.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"h-5 w-5 text-secondary-400 transition-transform duration-200 data-[state=open]:rotate-180"})})]})});m.displayName=s.l9.displayName;let u=o.forwardRef((e,r)=>{let{className:t,...o}=e;return(0,a.jsx)(s.PP,{ref:r,className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...o,children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})});u.displayName=s.PP.displayName;let x=o.forwardRef((e,r)=>{let{className:t,...o}=e;return(0,a.jsx)(s.wn,{ref:r,className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...o,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})});x.displayName=s.wn.displayName;let p=o.forwardRef((e,r)=>{let{className:t,children:o,position:n="popper",...d}=e;return(0,a.jsx)(s.ZL,{children:(0,a.jsxs)(s.UC,{ref:r,className:(0,i.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-xl border border-secondary-200 bg-white text-secondary-700 shadow-xl backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...d,children:[(0,a.jsx)(u,{}),(0,a.jsx)(s.LM,{className:(0,i.cn)("p-2","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:o}),(0,a.jsx)(x,{})]})})});p.displayName=s.UC.displayName,o.forwardRef((e,r)=>{let{className:t,...o}=e;return(0,a.jsx)(s.JU,{ref:r,className:(0,i.cn)("py-2 pl-10 pr-3 text-sm font-bold text-secondary-600 bg-secondary-50",t),...o})}).displayName=s.JU.displayName;let y=o.forwardRef((e,r)=>{let{className:t,children:o,...n}=e;return(0,a.jsxs)(s.q7,{ref:r,className:(0,i.cn)("relative flex w-full cursor-pointer select-none items-center rounded-lg py-3 pl-10 pr-3 text-sm font-medium text-secondary-700 transition-colors hover:bg-primary-50 focus:bg-primary-100 focus:text-primary-800 data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[state=checked]:bg-primary-100 data-[state=checked]:text-primary-800 data-[state=checked]:font-semibold",t),...n,children:[(0,a.jsx)("span",{className:"absolute left-3 flex h-4 w-4 items-center justify-center",children:(0,a.jsx)(s.VF,{children:(0,a.jsx)(l.A,{className:"h-4 w-4 text-primary-600"})})}),(0,a.jsx)(s.p4,{children:o})]})});y.displayName=s.q7.displayName,o.forwardRef((e,r)=>{let{className:t,...o}=e;return(0,a.jsx)(s.wv,{ref:r,className:(0,i.cn)("-mx-1 my-1 h-px bg-muted",t),...o})}).displayName=s.wv.displayName},59434:(e,r,t)=>{t.d(r,{OR:()=>m,Tk:()=>i,WK:()=>f,Yq:()=>d,cn:()=>s,gr:()=>l,uF:()=>c,vv:()=>n});var a=t(52596),o=t(39688);function s(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,o.QP)((0,a.$)(r))}function n(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"JOD",minimumFractionDigits:2}).format(e).replace("JOD","د.أ")}function d(e){let r="string"==typeof e?new Date(e):e;return isNaN(r.getTime())?"تاريخ غير صحيح":new Intl.DateTimeFormat("en-GB",{year:"numeric",month:"2-digit",day:"2-digit"}).format(r)}function l(e){return({ADMIN:"مدير",DATA_ENTRY:"مدخل بيانات",VIEWER:"مطلع"})[e]||e}function i(e){return({SUBSCRIPTION:"اشتراكات",DONATION:"تبرعات",EVENT:"فعاليات",OTHER:"أخرى"})[e]||e}function c(e){return({MEETINGS:"اجتماعات",EVENTS:"مناسبات",MAINTENANCE:"إصلاحات",SOCIAL:"اجتماعية",GENERAL:"عامة"})[e]||e}function f(e){return({ACTIVE:"نشط",LATE:"متأخر",INACTIVE:"غير ملتزم",SUSPENDED:"موقوف مؤقتاً",ARCHIVED:"مؤرشف"})[e]||e}function m(e){return({ACTIVE:"bg-green-100 text-green-800",LATE:"bg-yellow-100 text-yellow-800",INACTIVE:"bg-red-100 text-red-800",SUSPENDED:"bg-orange-100 text-orange-800",ARCHIVED:"bg-gray-100 text-gray-800"})[e]||"bg-gray-100 text-gray-800"}},62523:(e,r,t)=>{t.d(r,{p:()=>n});var a=t(95155),o=t(12115),s=t(59434);let n=o.forwardRef((e,r)=>{let{className:t,type:o,...n}=e;return(0,a.jsx)("input",{type:o,className:(0,s.cn)("flex h-11 w-full rounded-xl border-2 border-secondary-200 bg-white px-4 py-3 text-sm font-medium text-secondary-700 transition-all duration-200 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-secondary-400 focus-visible:outline-none focus-visible:border-primary-500 focus-visible:ring-4 focus-visible:ring-primary-500/20 disabled:cursor-not-allowed disabled:opacity-50 hover:border-secondary-300",t),ref:r,...n})});n.displayName="Input"},66695:(e,r,t)=>{t.d(r,{BT:()=>i,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>d});var a=t(95155),o=t(12115),s=t(59434);let n=o.forwardRef((e,r)=>{let{className:t,...o}=e;return(0,a.jsx)("div",{ref:r,className:(0,s.cn)("rounded-xl border border-secondary-200 bg-white text-secondary-700 shadow-lg hover:shadow-xl transition-all duration-200 hover:-translate-y-1 backdrop-blur-sm",t),...o})});n.displayName="Card";let d=o.forwardRef((e,r)=>{let{className:t,...o}=e;return(0,a.jsx)("div",{ref:r,className:(0,s.cn)("flex flex-col space-y-1.5 p-6 bg-gradient-to-r from-secondary-50 to-secondary-100 rounded-t-xl border-b border-secondary-200",t),...o})});d.displayName="CardHeader";let l=o.forwardRef((e,r)=>{let{className:t,...o}=e;return(0,a.jsx)("h3",{ref:r,className:(0,s.cn)("text-xl font-bold leading-none tracking-tight text-secondary-800",t),...o})});l.displayName="CardTitle";let i=o.forwardRef((e,r)=>{let{className:t,...o}=e;return(0,a.jsx)("p",{ref:r,className:(0,s.cn)("text-sm text-secondary-600 font-medium",t),...o})});i.displayName="CardDescription";let c=o.forwardRef((e,r)=>{let{className:t,...o}=e;return(0,a.jsx)("div",{ref:r,className:(0,s.cn)("p-6 pt-4",t),...o})});c.displayName="CardContent",o.forwardRef((e,r)=>{let{className:t,...o}=e;return(0,a.jsx)("div",{ref:r,className:(0,s.cn)("flex items-center p-6 pt-0 bg-gradient-to-r from-secondary-50 to-secondary-100 rounded-b-xl border-t border-secondary-200",t),...o})}).displayName="CardFooter"}}]);