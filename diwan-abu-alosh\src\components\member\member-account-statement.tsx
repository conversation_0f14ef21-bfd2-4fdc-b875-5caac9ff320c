'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DollarSign,
  TrendingUp,
  Calendar,
  FileText,
  Download,
  User,
  Phone,
  Mail,
  MapPin,
  // CreditCard,
  BarChart3,
  // Clock,
  // CheckCircle,
  Loader2
} from 'lucide-react'
import { formatCurrency, formatDate, getIncomeTypeText } from '@/lib/utils'

interface Member {
  id: string
  name: string
  phone?: string
  email?: string
  address?: string
  photo?: string
  status: string
  createdAt: string
}

interface Transaction {
  id: string
  amount: number
  date: string
  source: string
  type: string
  description?: string
  createdBy: {
    name: string
  }
}

interface AccountStatement {
  member: Member
  summary: {
    totalAmount: number
    transactionCount: number
    averageMonthlyContribution: number
    firstTransactionDate?: string
    lastTransactionDate?: string
  }
  byType: Record<string, { count: number; amount: number }>
  monthlyData: Array<{
    month: number
    monthName: string
    count: number
    amount: number
  }>
  recentTransactions: Transaction[]
  allTransactions: Transaction[]
  period: {
    startDate: string
    endDate: string
    year: number
  }
}

export default function MemberAccountStatement() {
  const [loading, setLoading] = useState(true)
  const [data, setData] = useState<AccountStatement | null>(null)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchAccountStatement()
  }, [])

  const fetchAccountStatement = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/member/account-statement')
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'فشل في جلب كشف الحساب')
      }

      const result = await response.json()
      setData(result)
    } catch (error: any) {
      console.error('خطأ في جلب كشف الحساب:', error)
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  const handleExport = () => {
    if (!data) return

    const printContent = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
        <head>
          <meta charset="UTF-8">
          <title>كشف حساب العضو - ${data.member.name}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
            .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
            .member-info { margin-bottom: 20px; }
            .summary { background: #f5f5f5; padding: 15px; margin-bottom: 20px; border-radius: 5px; }
            .monthly-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px; margin-bottom: 20px; }
            .month-item { padding: 10px; border: 1px solid #ddd; text-align: center; }
            .transactions { margin-top: 20px; }
            table { width: 100%; border-collapse: collapse; margin-top: 10px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
            th { background-color: #f5f5f5; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>كشف حساب العضو</h1>
            <h2>${data.member.name}</h2>
            <p>عام ${data.period.year}</p>
            <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
          </div>
          
          <div class="member-info">
            <h3>معلومات العضو</h3>
            <p><strong>الاسم:</strong> ${data.member.name}</p>
            ${data.member.phone ? `<p><strong>الهاتف:</strong> ${data.member.phone}</p>` : ''}
            ${data.member.email ? `<p><strong>البريد الإلكتروني:</strong> ${data.member.email}</p>` : ''}
            ${data.member.address ? `<p><strong>العنوان:</strong> ${data.member.address}</p>` : ''}
          </div>

          <div class="summary">
            <h3>ملخص الحساب</h3>
            <p><strong>إجمالي المساهمات:</strong> ${formatCurrency(data.summary.totalAmount)}</p>
            <p><strong>عدد المعاملات:</strong> ${data.summary.transactionCount}</p>
          </div>

          <div class="monthly-data">
            <h3>المساهمات الشهرية - ${data.period.year}</h3>
            <div class="monthly-grid">
              ${data.monthlyData.map(month => `
                <div class="month-item">
                  <strong>${month.monthName}</strong><br>
                  ${formatCurrency(month.amount)}<br>
                  <small>${month.count} معاملة</small>
                </div>
              `).join('')}
            </div>
          </div>

          <div class="transactions">
            <h3>المعاملات الأخيرة</h3>
            <table>
              <thead>
                <tr>
                  <th>التاريخ</th>
                  <th>المبلغ</th>
                  <th>النوع</th>
                  <th>المصدر</th>
                  <th>الوصف</th>
                </tr>
              </thead>
              <tbody>
                ${data.recentTransactions.map(transaction => `
                  <tr>
                    <td>${formatDate(transaction.date)}</td>
                    <td>${formatCurrency(transaction.amount)}</td>
                    <td>${getIncomeTypeText(transaction.type)}</td>
                    <td>${transaction.source}</td>
                    <td>${transaction.description || '-'}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        </body>
      </html>
    `

    const printWindow = window.open('', '_blank')
    if (printWindow) {
      printWindow.document.write(printContent)
      printWindow.document.close()
      printWindow.print()
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">جاري تحميل كشف الحساب...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
          <h3 className="text-lg font-semibold text-red-800 mb-2">خطأ في تحميل البيانات</h3>
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchAccountStatement} variant="outline">
            إعادة المحاولة
          </Button>
        </div>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">لا توجد بيانات متاحة</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">كشف حساب العضو</h1>
          <p className="text-gray-600 mt-1">{data.member.name} - عام {data.period.year}</p>
        </div>
        <Button onClick={handleExport} className="flex items-center gap-2">
          <Download className="w-4 h-4" />
          طباعة كشف الحساب
        </Button>
      </div>

      {/* معلومات العضو */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="w-5 h-5 text-blue-600" />
            معلومات العضو
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="flex items-center gap-2">
              <User className="w-4 h-4 text-gray-500" />
              <span className="font-medium">{data.member.name}</span>
            </div>
            {data.member.phone && (
              <div className="flex items-center gap-2">
                <Phone className="w-4 h-4 text-gray-500" />
                <span>{data.member.phone}</span>
              </div>
            )}
            {data.member.email && (
              <div className="flex items-center gap-2">
                <Mail className="w-4 h-4 text-gray-500" />
                <span>{data.member.email}</span>
              </div>
            )}
            {data.member.address && (
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4 text-gray-500" />
                <span>{data.member.address}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* ملخص الحساب */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي المساهمات</p>
                <p className="text-2xl font-bold text-green-600">
                  {formatCurrency(data.summary.totalAmount)}
                </p>
              </div>
              <DollarSign className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">عدد المعاملات</p>
                <p className="text-2xl font-bold text-blue-600">
                  {data.summary.transactionCount}
                </p>
              </div>
              <BarChart3 className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">متوسط المساهمة الشهرية</p>
                <p className="text-2xl font-bold text-purple-600">
                  {formatCurrency(data.summary.averageMonthlyContribution)}
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* المساهمات الشهرية */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5 text-blue-600" />
            المساهمات الشهرية - {data.period.year}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {data.monthlyData.map((month) => (
              <div
                key={month.month}
                className={`p-4 rounded-lg border text-center ${
                  month.count > 0
                    ? 'bg-green-50 border-green-200'
                    : 'bg-gray-50 border-gray-200'
                }`}
              >
                <div className="font-medium text-sm text-gray-700">
                  {month.monthName}
                </div>
                <div className={`text-lg font-bold mt-1 ${
                  month.count > 0 ? 'text-green-600' : 'text-gray-400'
                }`}>
                  {formatCurrency(month.amount)}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {month.count} معاملة
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* المعاملات الأخيرة */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5 text-blue-600" />
            المعاملات الأخيرة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>التاريخ</TableHead>
                  <TableHead>المبلغ</TableHead>
                  <TableHead>النوع</TableHead>
                  <TableHead>المصدر</TableHead>
                  <TableHead>الوصف</TableHead>
                  <TableHead>المسجل بواسطة</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.recentTransactions.map((transaction) => (
                  <TableRow key={transaction.id}>
                    <TableCell>{formatDate(transaction.date)}</TableCell>
                    <TableCell className="font-medium text-green-600">
                      {formatCurrency(transaction.amount)}
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">
                        {getIncomeTypeText(transaction.type)}
                      </Badge>
                    </TableCell>
                    <TableCell>{transaction.source}</TableCell>
                    <TableCell>{transaction.description || '-'}</TableCell>
                    <TableCell>{transaction.createdBy.name}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
