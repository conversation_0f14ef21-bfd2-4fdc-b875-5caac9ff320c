import { withAuth } from 'next-auth/middleware'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export default function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // التعامل مع مسارات NextAuth والمشرفين
  if (pathname.startsWith('/dashboard') ||
      (pathname.startsWith('/api/') &&
       !pathname.startsWith('/api/auth') &&
       !pathname.startsWith('/api/member/'))) {

    // استخدام NextAuth middleware للمشرفين
    return withAuth(
      function authMiddleware() {
        return NextResponse.next()
      },
      {
        callbacks: {
          authorized: ({ token }) => !!token,
        },
      }
    )(request as NextRequest, {} as Record<string, unknown>)
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (public folder)
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$).*)',
  ],
}
