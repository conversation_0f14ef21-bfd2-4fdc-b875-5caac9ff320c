{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// تحويل الأرقام العربية إلى إنجليزية\nexport function convertArabicToEnglishNumbers(text: string): string {\n  const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩']\n  const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']\n\n  let result = text\n  for (let i = 0; i < arabicNumbers.length; i++) {\n    result = result.replace(new RegExp(arabicNumbers[i], 'g'), englishNumbers[i])\n  }\n  return result\n}\n\n// تنسيق الأرقام بالفواصل (بأرقام إنجليزية)\nexport function formatNumber(num: number): string {\n  return new Intl.NumberFormat('en-US').format(num)\n}\n\n// تنسيق العملة بالدينار الأردني (بأرقام إنجليزية)\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'JOD',\n    minimumFractionDigits: 2,\n  }).format(amount).replace('JOD', 'د.أ')\n}\n\n// تنسيق التاريخ بالعربية (بأرقام إنجليزية)\nexport function formatDate(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date\n\n  // التحقق من صحة التاريخ\n  if (isNaN(dateObj.getTime())) {\n    return 'تاريخ غير صحيح'\n  }\n\n  return new Intl.DateTimeFormat('en-GB', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n  }).format(dateObj)\n}\n\n// تنسيق التاريخ والوقت (بأرقام إنجليزية)\nexport function formatDateTime(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date\n\n  // التحقق من صحة التاريخ\n  if (isNaN(dateObj.getTime())) {\n    return 'تاريخ غير صحيح'\n  }\n\n  return new Intl.DateTimeFormat('en-GB', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: false,\n  }).format(dateObj)\n}\n\n// تحويل أدوار المستخدمين إلى نص عربي\nexport function getRoleText(role: string): string {\n  const roles: Record<string, string> = {\n    ADMIN: 'مدير',\n    DATA_ENTRY: 'مدخل بيانات',\n    VIEWER: 'مطلع',\n  }\n  return roles[role] || role\n}\n\n// تحويل أنواع الإيرادات إلى نص عربي\nexport function getIncomeTypeText(type: string): string {\n  const types: Record<string, string> = {\n    SUBSCRIPTION: 'اشتراكات',\n    DONATION: 'تبرعات',\n    EVENT: 'فعاليات',\n    OTHER: 'أخرى',\n  }\n  return types[type] || type\n}\n\n// تحويل فئات المصروفات إلى نص عربي\nexport function getExpenseCategoryText(category: string): string {\n  const categories: Record<string, string> = {\n    MEETINGS: 'اجتماعات',\n    EVENTS: 'مناسبات',\n    MAINTENANCE: 'إصلاحات',\n    SOCIAL: 'اجتماعية',\n    GENERAL: 'عامة',\n  }\n  return categories[category] || category\n}\n\n// تحويل حالات الأعضاء إلى نص عربي\nexport function getMemberStatusText(status: string): string {\n  const statuses: Record<string, string> = {\n    ACTIVE: 'نشط',\n    LATE: 'متأخر',\n    INACTIVE: 'غير ملتزم',\n    SUSPENDED: 'موقوف مؤقتاً',\n    ARCHIVED: 'مؤرشف',\n  }\n  return statuses[status] || status\n}\n\n// الحصول على لون حالة العضو\nexport function getMemberStatusColor(status: string): string {\n  const colors: Record<string, string> = {\n    ACTIVE: 'bg-green-100 text-green-800',\n    LATE: 'bg-yellow-100 text-yellow-800',\n    INACTIVE: 'bg-red-100 text-red-800',\n    SUSPENDED: 'bg-orange-100 text-orange-800',\n    ARCHIVED: 'bg-gray-100 text-gray-800',\n  }\n  return colors[status] || 'bg-gray-100 text-gray-800'\n}\n\n// الحصول على أيقونة حالة العضو\nexport function getMemberStatusIcon(status: string): string {\n  const icons: Record<string, string> = {\n    ACTIVE: '✅',\n    LATE: '⏰',\n    INACTIVE: '❌',\n    SUSPENDED: '⏸️',\n    ARCHIVED: '📁',\n  }\n  return icons[status] || '❓'\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,8BAA8B,IAAY;IACxD,MAAM,gBAAgB;QAAC;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;KAAI;IACxE,MAAM,iBAAiB;QAAC;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;KAAI;IAEzE,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAK;QAC7C,SAAS,OAAO,OAAO,CAAC,IAAI,OAAO,aAAa,CAAC,EAAE,EAAE,MAAM,cAAc,CAAC,EAAE;IAC9E;IACA,OAAO;AACT;AAGO,SAAS,aAAa,GAAW;IACtC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAGO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;IACzB,GAAG,MAAM,CAAC,QAAQ,OAAO,CAAC,OAAO;AACnC;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAE5D,wBAAwB;IACxB,IAAI,MAAM,QAAQ,OAAO,KAAK;QAC5B,OAAO;IACT;IAEA,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,eAAe,IAAmB;IAChD,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAE5D,wBAAwB;IACxB,IAAI,MAAM,QAAQ,OAAO,KAAK;QAC5B,OAAO;IACT;IAEA,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;QACR,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,YAAY,IAAY;IACtC,MAAM,QAAgC;QACpC,OAAO;QACP,YAAY;QACZ,QAAQ;IACV;IACA,OAAO,KAAK,CAAC,KAAK,IAAI;AACxB;AAGO,SAAS,kBAAkB,IAAY;IAC5C,MAAM,QAAgC;QACpC,cAAc;QACd,UAAU;QACV,OAAO;QACP,OAAO;IACT;IACA,OAAO,KAAK,CAAC,KAAK,IAAI;AACxB;AAGO,SAAS,uBAAuB,QAAgB;IACrD,MAAM,aAAqC;QACzC,UAAU;QACV,QAAQ;QACR,aAAa;QACb,QAAQ;QACR,SAAS;IACX;IACA,OAAO,UAAU,CAAC,SAAS,IAAI;AACjC;AAGO,SAAS,oBAAoB,MAAc;IAChD,MAAM,WAAmC;QACvC,QAAQ;QACR,MAAM;QACN,UAAU;QACV,WAAW;QACX,UAAU;IACZ;IACA,OAAO,QAAQ,CAAC,OAAO,IAAI;AAC7B;AAGO,SAAS,qBAAqB,MAAc;IACjD,MAAM,SAAiC;QACrC,QAAQ;QACR,MAAM;QACN,UAAU;QACV,WAAW;QACX,UAAU;IACZ;IACA,OAAO,MAAM,CAAC,OAAO,IAAI;AAC3B;AAGO,SAAS,oBAAoB,MAAc;IAChD,MAAM,QAAgC;QACpC,QAAQ;QACR,MAAM;QACN,UAAU;QACV,WAAW;QACX,UAAU;IACZ;IACA,OAAO,KAAK,CAAC,OAAO,IAAI;AAC1B", "debugId": null}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border border-secondary-200 bg-white text-secondary-700 shadow-lg hover:shadow-xl transition-all duration-200 hover:-translate-y-1 backdrop-blur-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6 bg-gradient-to-r from-secondary-50 to-secondary-100 rounded-t-xl border-b border-secondary-200\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-xl font-bold leading-none tracking-tight text-secondary-800\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-secondary-600 font-medium\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-4\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0 bg-gradient-to-r from-secondary-50 to-secondary-100 rounded-b-xl border-t border-secondary-200\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gIAAgI;QAC7I,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6HAA6H;QAC1I,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/app/dashboard/page.tsx"], "sourcesContent": ["import { prisma } from '@/lib/prisma'\nimport { formatCurrency } from '@/lib/utils'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Users, TrendingUp, TrendingDown, DollarSign, Image, Bell, Home } from 'lucide-react'\n\nasync function getDashboardStats() {\n  const [\n    totalMembers,\n    activeMembers,\n    totalIncomes,\n    totalExpenses,\n    recentIncomes,\n    recentExpenses,\n    totalPhotos,\n    unreadNotifications,\n  ] = await Promise.all([\n    prisma.member.count(),\n    prisma.member.count({ where: { status: 'ACTIVE' } }),\n    prisma.income.aggregate({ _sum: { amount: true } }),\n    prisma.expense.aggregate({ _sum: { amount: true } }),\n    prisma.income.findMany({\n      take: 5,\n      orderBy: { createdAt: 'desc' },\n      include: { member: true },\n    }),\n    prisma.expense.findMany({\n      take: 5,\n      orderBy: { createdAt: 'desc' },\n    }),\n    prisma.galleryPhoto.count().catch(() => 0), // في حالة عدم وجود جدول الصور بعد\n    prisma.notification.count({ where: { isRead: false } }).catch(() => 0), // في حالة عدم وجود جدول الإشعارات بعد\n  ])\n\n  const totalIncomesAmount = totalIncomes._sum.amount || 0\n  const totalExpensesAmount = totalExpenses._sum.amount || 0\n  const balance = totalIncomesAmount - totalExpensesAmount\n\n  return {\n    totalMembers,\n    activeMembers,\n    totalIncomesAmount,\n    totalExpensesAmount,\n    balance,\n    recentIncomes,\n    recentExpenses,\n    totalPhotos,\n    unreadNotifications,\n  }\n}\n\nexport default async function DashboardPage() {\n  const stats = await getDashboardStats()\n\n  return (\n    <div className=\"space-y-8\">\n      {/* رأس الصفحة المحسن */}\n      <div className=\"text-center mb-8\">\n        <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl shadow-2xl p-10 text-white relative overflow-hidden\">\n          {/* خلفية متحركة */}\n          <div className=\"absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-30 animate-pulse\"></div>\n\n          {/* المحتوى */}\n          <div className=\"relative z-10\">\n            <div className=\"inline-flex items-center justify-center w-20 h-20 rounded-full mb-6 bg-white bg-opacity-20 backdrop-blur-sm\">\n              <Home className=\"w-10 h-10 text-white\" />\n            </div>\n\n            <h1 className=\"text-5xl font-black mb-4 text-white\">\n              لوحة التحكم\n            </h1>\n\n            <p className=\"text-xl font-semibold mb-6 text-blue-100\">\n              نظرة عامة شاملة على إدارة ديوان أبو علوش\n            </p>\n\n            <div className=\"flex items-center justify-center space-x-2 space-x-reverse\">\n              <div className=\"h-1 w-16 rounded-full bg-white bg-opacity-60\"></div>\n              <div className=\"h-1 w-8 rounded-full bg-white bg-opacity-40\"></div>\n              <div className=\"h-1 w-16 rounded-full bg-white bg-opacity-60\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* إحصائيات سريعة محسنة */}\n      <div className=\"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4\">\n        {/* بطاقة الأعضاء */}\n        <Card className=\"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative\">\n          <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500 to-blue-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500\"></div>\n\n          <div className=\"bg-gradient-to-r from-blue-500 to-blue-600 p-1 rounded-t-xl\"></div>\n\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10\">\n            <CardTitle className=\"text-sm font-bold\" style={{ color: '#333333' }}>إجمالي الأعضاء</CardTitle>\n            <div className=\"p-4 rounded-2xl shadow-lg\" style={{ backgroundColor: '#007bff' }}>\n              <Users className=\"h-7 w-7 text-white\" />\n            </div>\n          </CardHeader>\n\n          <CardContent className=\"relative z-10\">\n            <div className=\"text-4xl font-black mb-2\" style={{ color: '#191970' }}>\n              {stats.totalMembers}\n            </div>\n            <p className=\"text-sm font-semibold\" style={{ color: '#6c757d' }}>\n              {stats.activeMembers} عضو نشط\n            </p>\n          </CardContent>\n        </Card>\n\n        {/* بطاقة الإيرادات */}\n        <Card className=\"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative\">\n          <div className=\"absolute inset-0 bg-gradient-to-br from-green-500 to-green-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500\"></div>\n\n          <div className=\"bg-gradient-to-r from-green-500 to-green-600 p-1 rounded-t-xl\"></div>\n\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10\">\n            <CardTitle className=\"text-sm font-bold\" style={{ color: '#333333' }}>إجمالي الإيرادات</CardTitle>\n            <div className=\"p-4 rounded-2xl shadow-lg\" style={{ backgroundColor: '#28a745' }}>\n              <TrendingUp className=\"h-7 w-7 text-white\" />\n            </div>\n          </CardHeader>\n\n          <CardContent className=\"relative z-10\">\n            <div className=\"text-4xl font-black mb-2\" style={{ color: '#191970' }}>\n              {formatCurrency(stats.totalIncomesAmount)}\n            </div>\n            <p className=\"text-sm font-semibold\" style={{ color: '#6c757d' }}>\n              إجمالي المبالغ المحصلة\n            </p>\n          </CardContent>\n        </Card>\n\n        {/* بطاقة المصروفات */}\n        <Card className=\"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative\">\n          <div className=\"absolute inset-0 bg-gradient-to-br from-red-500 to-red-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500\"></div>\n\n          <div className=\"bg-gradient-to-r from-red-500 to-red-600 p-1 rounded-t-xl\"></div>\n\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10\">\n            <CardTitle className=\"text-sm font-bold\" style={{ color: '#333333' }}>إجمالي المصروفات</CardTitle>\n            <div className=\"p-4 rounded-2xl shadow-lg\" style={{ backgroundColor: '#dc3545' }}>\n              <TrendingDown className=\"h-7 w-7 text-white\" />\n            </div>\n          </CardHeader>\n\n          <CardContent className=\"relative z-10\">\n            <div className=\"text-4xl font-black mb-2\" style={{ color: '#191970' }}>\n              {formatCurrency(stats.totalExpensesAmount)}\n            </div>\n            <p className=\"text-sm font-semibold\" style={{ color: '#6c757d' }}>\n              إجمالي المبالغ المصروفة\n            </p>\n          </CardContent>\n        </Card>\n\n        {/* بطاقة الرصيد */}\n        <Card className=\"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative\">\n          <div className={`absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-10 transition-opacity duration-500 ${stats.balance >= 0 ? 'from-green-500 to-green-600' : 'from-slate-700 to-slate-800'}`}></div>\n\n          <div className={`bg-gradient-to-r p-1 rounded-t-xl ${stats.balance >= 0 ? 'from-green-500 to-green-600' : 'from-slate-700 to-slate-800'}`}></div>\n\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10\">\n            <CardTitle className=\"text-sm font-bold\" style={{ color: '#333333' }}>الرصيد الحالي</CardTitle>\n            <div className=\"p-4 rounded-2xl shadow-lg\" style={{ backgroundColor: stats.balance >= 0 ? '#28a745' : '#191970' }}>\n              <DollarSign className=\"h-7 w-7 text-white\" />\n            </div>\n          </CardHeader>\n\n          <CardContent className=\"relative z-10\">\n            <div className=\"text-4xl font-black mb-2\" style={{ color: '#191970' }}>\n              {formatCurrency(stats.balance)}\n            </div>\n            <p className=\"text-sm font-semibold\" style={{ color: '#6c757d' }}>\n              {stats.balance >= 0 ? 'رصيد إيجابي' : 'عجز في الرصيد'}\n            </p>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* إحصائيات إضافية محسنة */}\n      <div className=\"grid grid-cols-1 gap-8 sm:grid-cols-2\">\n        {/* بطاقة معرض الصور */}\n        <Card className=\"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative\">\n          <div className=\"absolute inset-0 bg-gradient-to-br from-purple-500 to-purple-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500\"></div>\n\n          <div className=\"bg-gradient-to-r from-purple-500 to-purple-600 p-1 rounded-t-xl\"></div>\n\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10\">\n            <CardTitle className=\"text-sm font-bold\" style={{ color: '#333333' }}>معرض الصور</CardTitle>\n            <div className=\"p-4 rounded-2xl shadow-lg\" style={{ backgroundColor: '#800020' }}>\n              <Image className=\"h-7 w-7 text-white\" alt=\"Gallery icon\" />\n            </div>\n          </CardHeader>\n\n          <CardContent className=\"relative z-10\">\n            <div className=\"text-4xl font-black mb-2\" style={{ color: '#191970' }}>\n              {stats.totalPhotos || 0}\n            </div>\n            <p className=\"text-sm font-semibold\" style={{ color: '#6c757d' }}>\n              صورة في المعرض\n            </p>\n          </CardContent>\n        </Card>\n\n        {/* بطاقة الإشعارات */}\n        <Card className=\"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative\">\n          <div className=\"absolute inset-0 bg-gradient-to-br from-indigo-500 to-indigo-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500\"></div>\n\n          <div className=\"bg-gradient-to-r from-indigo-500 to-indigo-600 p-1 rounded-t-xl\"></div>\n\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10\">\n            <CardTitle className=\"text-sm font-bold\" style={{ color: '#333333' }}>الإشعارات</CardTitle>\n            <div className=\"p-4 rounded-2xl shadow-lg\" style={{ backgroundColor: '#007bff' }}>\n              <Bell className=\"h-7 w-7 text-white\" />\n            </div>\n          </CardHeader>\n\n          <CardContent className=\"relative z-10\">\n            <div className=\"text-4xl font-black mb-2\" style={{ color: '#191970' }}>\n              {stats.unreadNotifications || 0}\n            </div>\n            <p className=\"text-sm font-semibold\" style={{ color: '#6c757d' }}>\n              إشعار غير مقروء\n            </p>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* قسم النشاطات الحديثة المحسن */}\n      <div className=\"grid grid-cols-1 gap-8 lg:grid-cols-2\">\n        {/* آخر الإيرادات */}\n        <Card className=\"border-0 shadow-2xl bg-white overflow-hidden\">\n          <CardHeader className=\"border-b-0 bg-gradient-to-r from-green-500 to-green-600 text-white p-6\">\n            <CardTitle className=\"text-xl font-bold flex items-center gap-3\">\n              <div className=\"p-3 rounded-xl bg-white bg-opacity-20 backdrop-blur-sm\">\n                <TrendingUp className=\"h-6 w-6 text-white\" />\n              </div>\n              آخر الإيرادات\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"p-6\">\n            <div className=\"space-y-4\">\n              {stats.recentIncomes.length > 0 ? (\n                stats.recentIncomes.map((income) => (\n                  <div key={income.id} className=\"group flex items-center justify-between p-5 rounded-2xl border-2 border-gray-100 hover:border-green-200 hover:shadow-lg transition-all duration-300 bg-gradient-to-r from-white to-green-50\">\n                    <div className=\"flex-1\">\n                      <p className=\"text-base font-bold mb-1\" style={{ color: '#333333' }}>{income.source}</p>\n                      <p className=\"text-sm font-medium\" style={{ color: '#6c757d' }}>\n                        {income.member?.name || 'غير محدد'}\n                      </p>\n                    </div>\n                    <div className=\"text-base font-black text-white px-4 py-2 rounded-xl shadow-lg\" style={{ backgroundColor: '#28a745' }}>\n                      {formatCurrency(income.amount)}\n                    </div>\n                  </div>\n                ))\n              ) : (\n                <div className=\"text-center py-12\">\n                  <div className=\"inline-flex items-center justify-center w-16 h-16 rounded-full mb-4\" style={{ backgroundColor: '#f1f5f9' }}>\n                    <TrendingUp className=\"h-8 w-8\" style={{ color: '#cbd5e1' }} />\n                  </div>\n                  <p className=\"text-base font-semibold\" style={{ color: '#6c757d' }}>لا توجد إيرادات حديثة</p>\n                </div>\n              )}\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* آخر المصروفات */}\n        <Card className=\"border-0 shadow-2xl bg-white overflow-hidden\">\n          <CardHeader className=\"border-b-0 bg-gradient-to-r from-red-500 to-red-600 text-white p-6\">\n            <CardTitle className=\"text-xl font-bold flex items-center gap-3\">\n              <div className=\"p-3 rounded-xl bg-white bg-opacity-20 backdrop-blur-sm\">\n                <TrendingDown className=\"h-6 w-6 text-white\" />\n              </div>\n              آخر المصروفات\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"p-6\">\n            <div className=\"space-y-4\">\n              {stats.recentExpenses.length > 0 ? (\n                stats.recentExpenses.map((expense) => (\n                  <div key={expense.id} className=\"group flex items-center justify-between p-5 rounded-2xl border-2 border-gray-100 hover:border-red-200 hover:shadow-lg transition-all duration-300 bg-gradient-to-r from-white to-red-50\">\n                    <div className=\"flex-1\">\n                      <p className=\"text-base font-bold mb-1\" style={{ color: '#333333' }}>{expense.description}</p>\n                      <p className=\"text-sm font-medium\" style={{ color: '#6c757d' }}>\n                        {expense.recipient || 'غير محدد'}\n                      </p>\n                    </div>\n                    <div className=\"text-base font-black text-white px-4 py-2 rounded-xl shadow-lg\" style={{ backgroundColor: '#dc3545' }}>\n                      {formatCurrency(expense.amount)}\n                    </div>\n                  </div>\n                ))\n              ) : (\n                <div className=\"text-center py-12\">\n                  <div className=\"inline-flex items-center justify-center w-16 h-16 rounded-full mb-4\" style={{ backgroundColor: '#f1f5f9' }}>\n                    <TrendingDown className=\"h-8 w-8\" style={{ color: '#cbd5e1' }} />\n                  </div>\n                  <p className=\"text-base font-semibold\" style={{ color: '#6c757d' }}>لا توجد مصروفات حديثة</p>\n                </div>\n              )}\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AAEA,eAAe;IACb,MAAM,CACJ,cACA,eACA,cACA,eACA,eACA,gBACA,aACA,oBACD,GAAG,MAAM,QAAQ,GAAG,CAAC;QACpB,oHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,KAAK;QACnB,oHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,KAAK,CAAC;YAAE,OAAO;gBAAE,QAAQ;YAAS;QAAE;QAClD,oHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,SAAS,CAAC;YAAE,MAAM;gBAAE,QAAQ;YAAK;QAAE;QACjD,oHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,SAAS,CAAC;YAAE,MAAM;gBAAE,QAAQ;YAAK;QAAE;QAClD,oHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;YACrB,MAAM;YACN,SAAS;gBAAE,WAAW;YAAO;YAC7B,SAAS;gBAAE,QAAQ;YAAK;QAC1B;QACA,oHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YACtB,MAAM;YACN,SAAS;gBAAE,WAAW;YAAO;QAC/B;QACA,oHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC,IAAM;QACxC,oHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,KAAK,CAAC;YAAE,OAAO;gBAAE,QAAQ;YAAM;QAAE,GAAG,KAAK,CAAC,IAAM;KACrE;IAED,MAAM,qBAAqB,aAAa,IAAI,CAAC,MAAM,IAAI;IACvD,MAAM,sBAAsB,cAAc,IAAI,CAAC,MAAM,IAAI;IACzD,MAAM,UAAU,qBAAqB;IAErC,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAEe,eAAe;IAC5B,MAAM,QAAQ,MAAM;IAEpB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;;;;;sCAGf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,mMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAGlB,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAIpD,8OAAC;oCAAE,WAAU;8CAA2C;;;;;;8CAIxD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;wCAAoB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;kDACtE,8OAAC;wCAAI,WAAU;wCAA4B,OAAO;4CAAE,iBAAiB;wCAAU;kDAC7E,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIrB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;wCAA2B,OAAO;4CAAE,OAAO;wCAAU;kDACjE,MAAM,YAAY;;;;;;kDAErB,8OAAC;wCAAE,WAAU;wCAAwB,OAAO;4CAAE,OAAO;wCAAU;;4CAC5D,MAAM,aAAa;4CAAC;;;;;;;;;;;;;;;;;;;kCAM3B,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;wCAAoB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;kDACtE,8OAAC;wCAAI,WAAU;wCAA4B,OAAO;4CAAE,iBAAiB;wCAAU;kDAC7E,cAAA,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAI1B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;wCAA2B,OAAO;4CAAE,OAAO;wCAAU;kDACjE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,kBAAkB;;;;;;kDAE1C,8OAAC;wCAAE,WAAU;wCAAwB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;;;;;;;;;;;;;kCAOtE,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;wCAAoB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;kDACtE,8OAAC;wCAAI,WAAU;wCAA4B,OAAO;4CAAE,iBAAiB;wCAAU;kDAC7E,cAAA,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAI5B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;wCAA2B,OAAO;4CAAE,OAAO;wCAAU;kDACjE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,mBAAmB;;;;;;kDAE3C,8OAAC;wCAAE,WAAU;wCAAwB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;;;;;;;;;;;;;kCAOtE,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC;gCAAI,WAAW,CAAC,oGAAoG,EAAE,MAAM,OAAO,IAAI,IAAI,gCAAgC,+BAA+B;;;;;;0CAE3M,8OAAC;gCAAI,WAAW,CAAC,kCAAkC,EAAE,MAAM,OAAO,IAAI,IAAI,gCAAgC,+BAA+B;;;;;;0CAEzI,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;wCAAoB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;kDACtE,8OAAC;wCAAI,WAAU;wCAA4B,OAAO;4CAAE,iBAAiB,MAAM,OAAO,IAAI,IAAI,YAAY;wCAAU;kDAC9G,cAAA,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAI1B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;wCAA2B,OAAO;4CAAE,OAAO;wCAAU;kDACjE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,OAAO;;;;;;kDAE/B,8OAAC;wCAAE,WAAU;wCAAwB,OAAO;4CAAE,OAAO;wCAAU;kDAC5D,MAAM,OAAO,IAAI,IAAI,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;0BAO9C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;wCAAoB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;kDACtE,8OAAC;wCAAI,WAAU;wCAA4B,OAAO;4CAAE,iBAAiB;wCAAU;kDAC7E,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;4CAAqB,KAAI;;;;;;;;;;;;;;;;;0CAI9C,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;wCAA2B,OAAO;4CAAE,OAAO;wCAAU;kDACjE,MAAM,WAAW,IAAI;;;;;;kDAExB,8OAAC;wCAAE,WAAU;wCAAwB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;;;;;;;;;;;;;kCAOtE,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;wCAAoB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;kDACtE,8OAAC;wCAAI,WAAU;wCAA4B,OAAO;4CAAE,iBAAiB;wCAAU;kDAC7E,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIpB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;wCAA2B,OAAO;4CAAE,OAAO;wCAAU;kDACjE,MAAM,mBAAmB,IAAI;;;;;;kDAEhC,8OAAC;wCAAE,WAAU;wCAAwB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;0BAQxE,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;wCAClB;;;;;;;;;;;;0CAIV,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;8CACZ,MAAM,aAAa,CAAC,MAAM,GAAG,IAC5B,MAAM,aAAa,CAAC,GAAG,CAAC,CAAC,uBACvB,8OAAC;4CAAoB,WAAU;;8DAC7B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;4DAA2B,OAAO;gEAAE,OAAO;4DAAU;sEAAI,OAAO,MAAM;;;;;;sEACnF,8OAAC;4DAAE,WAAU;4DAAsB,OAAO;gEAAE,OAAO;4DAAU;sEAC1D,OAAO,MAAM,EAAE,QAAQ;;;;;;;;;;;;8DAG5B,8OAAC;oDAAI,WAAU;oDAAiE,OAAO;wDAAE,iBAAiB;oDAAU;8DACjH,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,MAAM;;;;;;;2CARvB,OAAO,EAAE;;;;kEAarB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;gDAAsE,OAAO;oDAAE,iBAAiB;gDAAU;0DACvH,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;oDAAU,OAAO;wDAAE,OAAO;oDAAU;;;;;;;;;;;0DAE5D,8OAAC;gDAAE,WAAU;gDAA0B,OAAO;oDAAE,OAAO;gDAAU;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ9E,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;wCACpB;;;;;;;;;;;;0CAIV,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;8CACZ,MAAM,cAAc,CAAC,MAAM,GAAG,IAC7B,MAAM,cAAc,CAAC,GAAG,CAAC,CAAC,wBACxB,8OAAC;4CAAqB,WAAU;;8DAC9B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;4DAA2B,OAAO;gEAAE,OAAO;4DAAU;sEAAI,QAAQ,WAAW;;;;;;sEACzF,8OAAC;4DAAE,WAAU;4DAAsB,OAAO;gEAAE,OAAO;4DAAU;sEAC1D,QAAQ,SAAS,IAAI;;;;;;;;;;;;8DAG1B,8OAAC;oDAAI,WAAU;oDAAiE,OAAO;wDAAE,iBAAiB;oDAAU;8DACjH,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,MAAM;;;;;;;2CARxB,QAAQ,EAAE;;;;kEAatB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;gDAAsE,OAAO;oDAAE,iBAAiB;gDAAU;0DACvH,cAAA,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;oDAAU,OAAO;wDAAE,OAAO;oDAAU;;;;;;;;;;;0DAE9D,8OAAC;gDAAE,WAAU;gDAA0B,OAAO;oDAAE,OAAO;gDAAU;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtF", "debugId": null}}]}