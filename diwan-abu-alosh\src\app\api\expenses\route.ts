import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Schema للتحقق من البيانات الواردة من النموذج
const expenseInputSchema = z.object({
  amount: z.number().positive('المبلغ يجب أن يكون أكبر من صفر'),
  date: z.string().transform((str) => new Date(str)),
  description: z.string().min(1, 'الوصف مطلوب'),
  category: z.enum(['MEETINGS', 'EVENTS', 'MAINTENANCE', 'SOCIAL', 'GENERAL']),
  recipient: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
})

// GET - جلب جميع المصروفات مع البحث والتصفية
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search') || ''
    const category = searchParams.get('category') || 'all'
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    // بناء شروط البحث
    const where: Record<string, unknown> = {}
    
    if (search) {
      where.OR = [
        { description: { contains: search } },
        { recipient: { contains: search } },
        { notes: { contains: search } },
      ]
    }

    if (category !== 'all') {
      where.category = category
    }

    // جلب المصروفات مع العد الكلي
    const [expenses, total] = await Promise.all([
      prisma.expense.findMany({
        where,
        skip,
        take: limit,
        orderBy: { date: 'desc' },
        include: {
          createdBy: {
            select: {
              name: true,
            },
          },
        },
      }),
      prisma.expense.count({ where }),
    ])

    return NextResponse.json({
      expenses,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('خطأ في جلب المصروفات:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في جلب المصروفات' },
      { status: 500 }
    )
  }
}

// POST - إضافة مصروف جديد
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // التحقق من الصلاحيات
    if (session.user.role === 'VIEWER') {
      return NextResponse.json(
        { error: 'ليس لديك صلاحية لإضافة المصروفات' },
        { status: 403 }
      )
    }

    const body = await request.json()

    // التحقق من صحة البيانات
    const validatedData = expenseInputSchema.parse(body)

    // إنشاء المصروف
    const expense = await prisma.expense.create({
      data: {
        ...validatedData,
        createdById: session.user.id,
      },
      include: {
        createdBy: {
          select: {
            name: true,
          },
        },
      },
    })

    return NextResponse.json(expense, { status: 201 })
  } catch (error: unknown) {
    console.error('خطأ في إضافة المصروف:', error)
    
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'بيانات غير صحيحة', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'حدث خطأ في إضافة المصروف' },
      { status: 500 }
    )
  }
}
