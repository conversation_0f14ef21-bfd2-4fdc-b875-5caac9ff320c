{"version": 4, "routes": {"/favicon.ico": {"initialHeaders": {"cache-control": "public, max-age=0, must-revalidate", "content-type": "image/x-icon", "x-next-cache-tags": "_N_T_/layout,_N_T_/favicon.ico/layout,_N_T_/favicon.ico/route,_N_T_/favicon.ico"}, "experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/favicon.ico", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "5bd43d2d0500003b34d09348f387e864", "previewModeSigningKey": "9b9d69690b768c129da4d1d97404e8d756b184ab3acb0723ee38dd8b506440a9", "previewModeEncryptionKey": "a4a7b3d3c82be15aeaea49a2db2277757d99cfed846a6807339afc0ed54c7262"}}