(()=>{var e={};e.id=7222,e.ids=[7222],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{N:()=>n});var s=t(13581),o=t(85663),i=t(31183);let n={providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"البريد الإلكتروني",type:"email"},password:{label:"كلمة المرور",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await i.z.user.findUnique({where:{email:e.email}});return r&&await o.Ay.compare(e.password,r.password)?{id:r.id,email:r.email,name:r.name,role:r.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>o});var s=t(96330);let o=globalThis.prisma??new s.PrismaClient},38458:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>j,routeModule:()=>f,serverHooks:()=>h,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{DELETE:()=>g,GET:()=>x,PUT:()=>w});var o=t(96559),i=t(48088),n=t(37719),a=t(32190),u=t(19854),l=t(12909),p=t(31183),d=t(45697);let c=d.z.object({title:d.z.string().min(1,"العنوان مطلوب").optional(),description:d.z.string().optional(),location:d.z.string().optional(),startDate:d.z.string().optional(),endDate:d.z.string().optional()});async function x(e,{params:r}){try{if(!await (0,u.getServerSession)(l.N))return a.NextResponse.json({error:"غير مصرح"},{status:401});let e=await p.z.galleryFolder.findUnique({where:{id:r.id},include:{creator:{select:{name:!0}},photos:{include:{uploader:{select:{name:!0}}},orderBy:{createdAt:"desc"}},_count:{select:{photos:!0}}}});if(!e)return a.NextResponse.json({error:"المجلد غير موجود"},{status:404});return a.NextResponse.json(e)}catch(e){return console.error("خطأ في جلب المجلد:",e),a.NextResponse.json({error:"حدث خطأ في جلب المجلد"},{status:500})}}async function w(e,{params:r}){try{let t=await (0,u.getServerSession)(l.N);if(!t)return a.NextResponse.json({error:"غير مصرح"},{status:401});if("VIEWER"===t.user.role)return a.NextResponse.json({error:"ليس لديك صلاحية لتعديل المجلدات"},{status:403});let s=await e.json(),o=c.parse(s);if(!await p.z.galleryFolder.findUnique({where:{id:r.id}}))return a.NextResponse.json({error:"المجلد غير موجود"},{status:404});let i={};void 0!==o.title&&(i.title=o.title),void 0!==o.description&&(i.description=o.description),void 0!==o.location&&(i.location=o.location),void 0!==o.startDate&&(i.startDate=o.startDate?new Date(o.startDate):null),void 0!==o.endDate&&(i.endDate=o.endDate?new Date(o.endDate):null);let n=await p.z.galleryFolder.update({where:{id:r.id},data:i,include:{creator:{select:{name:!0}},_count:{select:{photos:!0}}}});return a.NextResponse.json(n)}catch(e){if(e instanceof d.z.ZodError)return a.NextResponse.json({error:"بيانات غير صحيحة",details:e.errors},{status:400});return console.error("خطأ في تحديث المجلد:",e),a.NextResponse.json({error:"حدث خطأ في تحديث المجلد"},{status:500})}}async function g(e,{params:r}){try{let e=await (0,u.getServerSession)(l.N);if(!e)return a.NextResponse.json({error:"غير مصرح"},{status:401});if("ADMIN"!==e.user.role)return a.NextResponse.json({error:"ليس لديك صلاحية لحذف المجلدات"},{status:403});let t=await p.z.galleryFolder.findUnique({where:{id:r.id},include:{_count:{select:{photos:!0}}}});if(!t)return a.NextResponse.json({error:"المجلد غير موجود"},{status:404});if(t._count.photos>0)return a.NextResponse.json({error:"لا يمكن حذف المجلد لأنه يحتوي على صور. يرجى حذف الصور أولاً أو نقلها إلى مجلد آخر."},{status:400});return await p.z.galleryFolder.delete({where:{id:r.id}}),a.NextResponse.json({message:"تم حذف المجلد بنجاح"})}catch(e){return console.error("خطأ في حذف المجلد:",e),a.NextResponse.json({error:"حدث خطأ في حذف المجلد"},{status:500})}}let f=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/gallery-folders/[id]/route",pathname:"/api/gallery-folders/[id]",filename:"route",bundlePath:"app/api/gallery-folders/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery-folders\\[id]\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:y,workUnitAsyncStorage:m,serverHooks:h}=f;function j(){return(0,n.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:m})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,5663,4999,3412,580,5697],()=>t(38458));module.exports=s})();