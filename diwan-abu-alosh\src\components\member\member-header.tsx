'use client'

// import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>, User } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface MemberHeaderProps {
  user: any
  onSignOut: () => void
  onToggleSidebar?: () => void
}

export default function MemberHeader({ user, onSignOut, onToggleSidebar }: MemberHeaderProps) {
  return (
    <header className="bg-white shadow-sm border-b border-gray-200 lg:pr-64">
      <div className="flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8">
        {/* زر القائمة للشاشات الصغيرة */}
        <div className="lg:hidden">
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleSidebar}
          >
            <Menu className="h-5 w-5" />
          </Button>
        </div>

        {/* العنوان */}
        <div className="flex-1 text-center lg:text-right">
          <h1 className="text-lg font-semibold text-gray-900">
            مرحباً، {user?.member?.name}
          </h1>
        </div>

        {/* قائمة المستخدم */}
        <div className="flex items-center space-x-4 space-x-reverse">
          {/* الإشعارات */}
          <Button variant="ghost" size="sm" className="relative">
            <Bell className="h-5 w-5" />
            <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
              0
            </span>
          </Button>

          {/* قائمة المستخدم */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="flex items-center space-x-2 space-x-reverse">
                {user?.member?.photo ? (
                  <img
                    src={user.member.photo}
                    alt={user.member.name}
                    className="h-6 w-6 rounded-full object-cover"
                  />
                ) : (
                  <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center">
                    <User className="h-3 w-3 text-blue-600" />
                  </div>
                )}
                <span className="text-sm font-medium">{user?.member?.name}</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <div className="px-2 py-1.5">
                <p className="text-sm font-medium">{user?.member?.name}</p>
                <p className="text-xs text-gray-500">{user?.member?.email}</p>
              </div>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={onSignOut}>
                تسجيل الخروج
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  )
}
