exports.id=6434,exports.ids=[6434],exports.modules={38336:(e,s,t)=>{Promise.resolve().then(t.bind(t,66868))},50894:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\ابوعلوش\\\\diwan-abu-alosh\\\\src\\\\app\\\\member\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\layout.tsx","default")},66868:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>T});var a=t(60687),r=t(16189),n=t(43210),i=t(84931),l=t(85814),o=t.n(l),c=t(4780),d=t(58869),m=t(10022),x=t(9005),u=t(17313),h=t(40083),f=t(29523);let p=[{name:"لوحة التحكم",href:"/member/dashboard",icon:d.A,permission:null},{name:"كشف الحساب",href:"/member/account-statement",icon:m.A,permission:"canViewAccountStatement"},{name:"معرض الصور",href:"/member/gallery",icon:x.A,permission:"canViewGallery"}];function b({user:e,onSignOut:s}){let t=(0,r.usePathname)(),{hasPermission:n}=function(){let{user:e}=(0,i.k$)(),s=s=>!!e&&!!e.permissions&&e.permissions[s];return{hasPermission:s,canViewAccountStatement:()=>s("canViewAccountStatement"),canViewGallery:()=>s("canViewGallery"),permissions:e?.permissions||{canViewAccountStatement:!1,canViewGallery:!1}}}();return(0,a.jsxs)("div",{className:"fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg lg:block hidden",children:[(0,a.jsx)("div",{className:"flex h-16 items-center justify-center border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(u.A,{className:"h-8 w-8 text-blue-600"}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("h1",{className:"text-lg font-bold text-gray-900",children:"ديوان آل أبو علوش"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"بوابة الأعضاء"})]})]})}),(0,a.jsx)("div",{className:"p-4 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[e?.member?.photo?(0,a.jsx)("img",{src:e.member.photo,alt:e.member.name,className:"h-10 w-10 rounded-full object-cover"}):(0,a.jsx)("div",{className:"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center",children:(0,a.jsx)(d.A,{className:"h-5 w-5 text-blue-600"})}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e?.member?.name}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"عضو"})]})]})}),(0,a.jsx)("nav",{className:"mt-6 px-3",children:(0,a.jsx)("ul",{className:"space-y-1",children:p.map(e=>{let s=t===e.href;return e.permission&&!n(e.permission)?null:(0,a.jsx)("li",{children:(0,a.jsxs)(o(),{href:e.href,className:(0,c.cn)("group flex items-center rounded-md px-3 py-2 text-sm font-medium transition-colors",s?"bg-blue-100 text-blue-700":"text-gray-700 hover:bg-gray-100 hover:text-gray-900"),children:[(0,a.jsx)(e.icon,{className:(0,c.cn)("ml-3 h-5 w-5 flex-shrink-0",s?"text-blue-500":"text-gray-400 group-hover:text-gray-500")}),e.name]})},e.name)})})}),(0,a.jsxs)("div",{className:"absolute bottom-0 w-full p-4 border-t border-gray-200",children:[(0,a.jsxs)(f.$,{onClick:s,variant:"outline",className:"w-full justify-start",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"تسجيل الخروج"]}),(0,a.jsxs)("div",{className:"mt-4 text-center text-xs text-gray-500",children:[(0,a.jsx)("p",{children:"الإصدار 1.0.0"}),(0,a.jsx)("p",{className:"mt-1",children:"\xa9 2024 ديوان آل أبو علوش"})]})]})]})}var j=t(12941),g=t(97051),N=t(26312),y=t(14952),v=t(13964),w=t(65822);let S=N.bL,A=N.l9;N.YJ,N.ZL,N.Pb,N.z6,n.forwardRef(({className:e,inset:s,children:t,...r},n)=>(0,a.jsxs)(N.ZP,{ref:n,className:(0,c.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",s&&"pl-8",e),...r,children:[t,(0,a.jsx)(y.A,{className:"ml-auto h-4 w-4"})]})).displayName=N.ZP.displayName,n.forwardRef(({className:e,...s},t)=>(0,a.jsx)(N.G5,{ref:t,className:(0,c.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...s})).displayName=N.G5.displayName;let k=n.forwardRef(({className:e,sideOffset:s=4,...t},r)=>(0,a.jsx)(N.ZL,{children:(0,a.jsx)(N.UC,{ref:r,sideOffset:s,className:(0,c.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})}));k.displayName=N.UC.displayName;let C=n.forwardRef(({className:e,inset:s,...t},r)=>(0,a.jsx)(N.q7,{ref:r,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s&&"pl-8",e),...t}));C.displayName=N.q7.displayName,n.forwardRef(({className:e,children:s,checked:t,...r},n)=>(0,a.jsxs)(N.H_,{ref:n,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:t,...r,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(N.VF,{children:(0,a.jsx)(v.A,{className:"h-4 w-4"})})}),s]})).displayName=N.H_.displayName,n.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(N.hN,{ref:r,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(N.VF,{children:(0,a.jsx)(w.A,{className:"h-2 w-2 fill-current"})})}),s]})).displayName=N.hN.displayName,n.forwardRef(({className:e,inset:s,...t},r)=>(0,a.jsx)(N.JU,{ref:r,className:(0,c.cn)("px-2 py-1.5 text-sm font-semibold",s&&"pl-8",e),...t})).displayName=N.JU.displayName;let z=n.forwardRef(({className:e,...s},t)=>(0,a.jsx)(N.wv,{ref:t,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",e),...s}));function O({user:e,onSignOut:s,onToggleSidebar:t}){return(0,a.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200 lg:pr-64",children:(0,a.jsxs)("div",{className:"flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8",children:[(0,a.jsx)("div",{className:"lg:hidden",children:(0,a.jsx)(f.$,{variant:"ghost",size:"sm",onClick:t,children:(0,a.jsx)(j.A,{className:"h-5 w-5"})})}),(0,a.jsx)("div",{className:"flex-1 text-center lg:text-right",children:(0,a.jsxs)("h1",{className:"text-lg font-semibold text-gray-900",children:["مرحباً، ",e?.member?.name]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 space-x-reverse",children:[(0,a.jsxs)(f.$,{variant:"ghost",size:"sm",className:"relative",children:[(0,a.jsx)(g.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{className:"absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center",children:"0"})]}),(0,a.jsxs)(S,{children:[(0,a.jsx)(A,{asChild:!0,children:(0,a.jsxs)(f.$,{variant:"ghost",size:"sm",className:"flex items-center space-x-2 space-x-reverse",children:[e?.member?.photo?(0,a.jsx)("img",{src:e.member.photo,alt:e.member.name,className:"h-6 w-6 rounded-full object-cover"}):(0,a.jsx)("div",{className:"h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center",children:(0,a.jsx)(d.A,{className:"h-3 w-3 text-blue-600"})}),(0,a.jsx)("span",{className:"text-sm font-medium",children:e?.member?.name})]})}),(0,a.jsxs)(k,{align:"end",className:"w-56",children:[(0,a.jsxs)("div",{className:"px-2 py-1.5",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:e?.member?.name}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:e?.member?.email})]}),(0,a.jsx)(z,{}),(0,a.jsx)(C,{onClick:s,children:"تسجيل الخروج"})]})]})]})]})})}z.displayName=N.wv.displayName;var R=t(41862);function P({children:e}){let{user:s,loading:t,signOut:l}=(0,i.R8)(),[o,c]=(0,n.useState)(!1);return((0,r.useRouter)(),t)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(R.A,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600"}),(0,a.jsx)("p",{className:"text-gray-600",children:"جاري التحميل..."})]})}):s?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(b,{user:s,onSignOut:l}),o&&(0,a.jsxs)("div",{className:"fixed inset-0 z-50 lg:hidden",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50",onClick:()=>c(!1)}),(0,a.jsx)("div",{className:"fixed inset-y-0 right-0 w-64 bg-white shadow-lg",children:(0,a.jsx)(b,{user:s,onSignOut:l})})]}),(0,a.jsxs)("div",{className:"lg:pr-64",children:[(0,a.jsx)(O,{user:s,onSignOut:l,onToggleSidebar:()=>c(!o)}),(0,a.jsx)("main",{className:"py-6",children:(0,a.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:e})})]}),!1]}):null}function V({children:e}){let s=(0,i.R8)();return(0,a.jsx)(i.WA.Provider,{value:s,children:e})}function T({children:e}){return["/member/signin","/member/login","/member/test-login","/member/dashboard","/member/account-statement"].includes((0,r.usePathname)())?(0,a.jsx)(a.Fragment,{children:e}):(0,a.jsx)(V,{children:(0,a.jsx)(P,{children:e})})}},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74784:(e,s,t)=>{Promise.resolve().then(t.bind(t,50894))},84931:(e,s,t)=>{"use strict";t.d(s,{R8:()=>i,WA:()=>r,k$:()=>n});var a=t(43210);let r=(0,a.createContext)(void 0);function n(){let e=(0,a.useContext)(r);if(void 0===e)throw Error("useMemberAuth must be used within a MemberAuthProvider");return e}function i(){let[e,s]=(0,a.useState)(null),[t,r]=(0,a.useState)(!0),[n,i]=(0,a.useState)(!1),[l,o]=(0,a.useState)(0);return{user:e,loading:t,signingOut:n,signIn:async(e,t)=>{try{let a=await fetch("/api/auth/member/signin",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})}),r=a.headers.get("content-type");if(!r||!r.includes("application/json"))return console.error("استجابة غير متوقعة من خادم تسجيل الدخول"),{success:!1,error:"حدث خطأ في الخادم"};let n=await a.json();if(a.ok)return s(n.user),{success:!0};return{success:!1,error:n.error}}catch(e){if(e instanceof SyntaxError&&e.message.includes("Unexpected token"))return console.error("خادم تسجيل الدخول أرجع HTML بدلاً من JSON"),{success:!1,error:"حدث خطأ في الخادم"};return console.error("خطأ في تسجيل الدخول:",e),{success:!1,error:"حدث خطأ في تسجيل الدخول"}}},signOut:async()=>{i(!0),s(null),document.cookie="member-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;",window.location.href="/member/signin",fetch("/api/auth/member/signout",{method:"POST"}).catch(e=>{console.error("خطأ في تسجيل الخروج من الخادم:",e)})},checkSession:async(e=!1)=>{let t=Date.now();if(!e&&t-l<3e5)return void r(!1);try{o(t);let e=await fetch("/api/auth/member/session",{cache:"no-cache"}),a=e.headers.get("content-type");if(e.ok)if(a&&a.includes("application/json")){let t=await e.json();s(t.user)}else console.error("استجابة غير متوقعة من الخادم - ليست JSON"),s(null);else{if(a&&a.includes("application/json"))try{let s=await e.json();401!==e.status&&console.error("خطأ من الخادم:",s.error)}catch{}s(null)}}catch(e){e instanceof TypeError&&e.message.includes("fetch")||(e instanceof SyntaxError&&e.message.includes("Unexpected token")?console.error("الخادم أرجع HTML بدلاً من JSON - قد تكون هناك مشكلة في الخادم"):console.error("خطأ في التحقق من الجلسة:",e)),s(null)}finally{r(!1)}}}}}};