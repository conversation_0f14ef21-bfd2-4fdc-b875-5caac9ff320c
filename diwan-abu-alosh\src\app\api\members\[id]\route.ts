import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { memberSchema } from '@/lib/validations'

// GET - جلب عضو محدد
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    const { id } = await params
    const member = await prisma.member.findUnique({
      where: { id },
      include: {
        incomes: {
          orderBy: { date: 'desc' },
          take: 20,
          select: {
            id: true,
            amount: true,
            date: true,
            source: true,
            type: true,
            description: true,
          },
        },
        activityParticipants: {
          include: {
            activity: {
              select: {
                id: true,
                title: true,
                startDate: true,
                location: true,
              },
            },
          },
          orderBy: {
            activity: {
              startDate: 'desc',
            },
          },
          take: 20,
        },
        _count: {
          select: {
            incomes: true,
            activityParticipants: true,
          },
        },
      },
    })

    if (!member) {
      return NextResponse.json({ error: 'العضو غير موجود' }, { status: 404 })
    }

    return NextResponse.json(member)
  } catch (error) {
    console.error('خطأ في جلب العضو:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في جلب العضو' },
      { status: 500 }
    )
  }
}

// PUT - تحديث عضو
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // التحقق من الصلاحيات
    if (session.user.role === 'VIEWER') {
      return NextResponse.json(
        { error: 'ليس لديك صلاحية لتعديل الأعضاء' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { id } = await params

    // التحقق من صحة البيانات
    const validatedData = memberSchema.parse(body)

    // التحقق من وجود العضو
    const existingMember = await prisma.member.findUnique({
      where: { id },
    })

    if (!existingMember) {
      return NextResponse.json({ error: 'العضو غير موجود' }, { status: 404 })
    }

    // التحقق من عدم تكرار البريد الإلكتروني
    if (validatedData.email && validatedData.email !== existingMember.email) {
      const emailExists = await prisma.member.findFirst({
        where: {
          email: validatedData.email,
          id: { not: id },
        },
      })
      if (emailExists) {
        return NextResponse.json(
          { error: 'البريد الإلكتروني مستخدم بالفعل' },
          { status: 400 }
        )
      }
    }

    // تحديث العضو
    const updatedMember = await prisma.member.update({
      where: { id },
      data: validatedData,
    })

    return NextResponse.json(updatedMember)
  } catch (error: unknown) {
    console.error('خطأ في تحديث العضو:', error)
    
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'بيانات غير صحيحة', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'حدث خطأ في تحديث العضو' },
      { status: 500 }
    )
  }
}

// DELETE - حذف عضو
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // التحقق من الصلاحيات - فقط المدير يمكنه الحذف
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'ليس لديك صلاحية لحذف الأعضاء' },
        { status: 403 }
      )
    }

    const { id } = await params

    // التحقق من وجود العضو
    const existingMember = await prisma.member.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            incomes: true,
            activityParticipants: true,
          },
        },
      },
    })

    if (!existingMember) {
      return NextResponse.json({ error: 'العضو غير موجود' }, { status: 404 })
    }

    // التحقق من وجود بيانات مرتبطة
    if (existingMember._count.incomes > 0) {
      return NextResponse.json(
        { error: 'لا يمكن حذف العضو لوجود إيرادات مرتبطة به' },
        { status: 400 }
      )
    }

    // حذف العضو (سيتم حذف المشاركات في الأنشطة تلقائياً بسبب onDelete: Cascade)
    await prisma.member.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'تم حذف العضو بنجاح' })
  } catch (error) {
    console.error('خطأ في حذف العضو:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في حذف العضو' },
      { status: 500 }
    )
  }
}
