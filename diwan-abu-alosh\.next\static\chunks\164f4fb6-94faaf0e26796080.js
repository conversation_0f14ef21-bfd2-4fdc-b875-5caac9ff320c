"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3930],{26597:(t,e,r)=>{r.r(e),r.d(e,{AcroForm:()=>tS,AcroFormAppearance:()=>tA,AcroFormButton:()=>tm,AcroFormCheckBox:()=>tw,AcroFormChoiceField:()=>tf,AcroFormComboBox:()=>tp,AcroFormEditBox:()=>tg,AcroFormListBox:()=>td,AcroFormPasswordField:()=>tN,AcroFormPushButton:()=>tv,AcroFormRadioButton:()=>tb,AcroFormTextField:()=>tx,GState:()=>j,ShadingPattern:()=>E,TilingPattern:()=>M,default:()=>q,jsPDF:()=>q});var n=r(86608),i=r(61260),a=function(){return"undefined"!=typeof window?window:void 0!==r.g?r.g:"undefined"!=typeof self?self:this}();function o(){a.console&&"function"==typeof a.console.log&&a.console.log.apply(a.console,arguments)}var s={log:o,warn:function(t){a.console&&("function"==typeof a.console.warn?a.console.warn.apply(a.console,arguments):o.call(null,arguments))},error:function(t){a.console&&("function"==typeof a.console.error?a.console.error.apply(a.console,arguments):o(t))}};function c(t,e,r){var n=new XMLHttpRequest;n.open("GET",t),n.responseType="blob",n.onload=function(){d(n.response,e,r)},n.onerror=function(){s.error("could not download file")},n.send()}function u(t){var e=new XMLHttpRequest;e.open("HEAD",t,!1);try{e.send()}catch(t){}return e.status>=200&&e.status<=299}function h(t){try{t.dispatchEvent(new MouseEvent("click"))}catch(r){var e=document.createEvent("MouseEvents");e.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),t.dispatchEvent(e)}}var l,f,d=a.saveAs||("object"!==("undefined"==typeof window?"undefined":(0,n.A)(window))||window!==a?function(){}:"undefined"!=typeof HTMLAnchorElement&&"download"in HTMLAnchorElement.prototype?function(t,e,r){var n=a.URL||a.webkitURL,i=document.createElement("a");i.download=e=e||t.name||"download",i.rel="noopener","string"==typeof t?(i.href=t,i.origin!==location.origin?u(i.href)?c(t,e,r):h(i,i.target="_blank"):h(i)):(i.href=n.createObjectURL(t),setTimeout(function(){n.revokeObjectURL(i.href)},4e4),setTimeout(function(){h(i)},0))}:"msSaveOrOpenBlob"in navigator?function(t,e,r){if(e=e||t.name||"download","string"==typeof t)if(u(t))c(t,e,r);else{var i,a=document.createElement("a");a.href=t,a.target="_blank",setTimeout(function(){h(a)})}else navigator.msSaveOrOpenBlob((void 0===(i=r)?i={autoBom:!1}:"object"!==(0,n.A)(i)&&(s.warn("Deprecated: Expected third argument to be a object"),i={autoBom:!i}),i.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(t.type)?new Blob([String.fromCharCode(65279),t],{type:t.type}):t),e)}:function(t,e,r,i){if((i=i||open("","_blank"))&&(i.document.title=i.document.body.innerText="downloading..."),"string"==typeof t)return c(t,e,r);var o="application/octet-stream"===t.type,s=/constructor/i.test(a.HTMLElement)||a.safari,u=/CriOS\/[\d]+/.test(navigator.userAgent);if((u||o&&s)&&"object"===("undefined"==typeof FileReader?"undefined":(0,n.A)(FileReader))){var h=new FileReader;h.onloadend=function(){var t=h.result;t=u?t:t.replace(/^data:[^;]*;/,"data:attachment/file;"),i?i.location.href=t:location=t,i=null},h.readAsDataURL(t)}else{var l=a.URL||a.webkitURL,f=l.createObjectURL(t);i?i.location=f:location.href=f,i=null,setTimeout(function(){l.revokeObjectURL(f)},4e4)}});function p(t){var e;t=t||"",this.ok=!1,"#"==t.charAt(0)&&(t=t.substr(1,6)),t=({aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"})[t=(t=t.replace(/ /g,"")).toLowerCase()]||t;for(var r=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3])]}},{re:/^(\w{2})(\w{2})(\w{2})$/,example:["#00ff00","336699"],process:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,example:["#fb0","f0f"],process:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16)]}}],n=0;n<r.length;n++){var i=r[n].re,a=r[n].process,o=i.exec(t);o&&(e=a(o),this.r=e[0],this.g=e[1],this.b=e[2],this.ok=!0)}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toHex=function(){var t=this.r.toString(16),e=this.g.toString(16),r=this.b.toString(16);return 1==t.length&&(t="0"+t),1==e.length&&(e="0"+e),1==r.length&&(r="0"+r),"#"+t+e+r}}function g(t,e){var r=t[0],n=t[1],i=t[2],a=t[3];r=v(r,n,i,a,e[0],7,-0x28955b88),a=v(a,r,n,i,e[1],12,-0x173848aa),i=v(i,a,r,n,e[2],17,0x242070db),n=v(n,i,a,r,e[3],22,-0x3e423112),r=v(r,n,i,a,e[4],7,-0xa83f051),a=v(a,r,n,i,e[5],12,0x4787c62a),i=v(i,a,r,n,e[6],17,-0x57cfb9ed),n=v(n,i,a,r,e[7],22,-0x2b96aff),r=v(r,n,i,a,e[8],7,0x698098d8),a=v(a,r,n,i,e[9],12,-0x74bb0851),i=v(i,a,r,n,e[10],17,-42063),n=v(n,i,a,r,e[11],22,-0x76a32842),r=v(r,n,i,a,e[12],7,0x6b901122),a=v(a,r,n,i,e[13],12,-0x2678e6d),i=v(i,a,r,n,e[14],17,-0x5986bc72),r=b(r,n=v(n,i,a,r,e[15],22,0x49b40821),i,a,e[1],5,-0x9e1da9e),a=b(a,r,n,i,e[6],9,-0x3fbf4cc0),i=b(i,a,r,n,e[11],14,0x265e5a51),n=b(n,i,a,r,e[0],20,-0x16493856),r=b(r,n,i,a,e[5],5,-0x29d0efa3),a=b(a,r,n,i,e[10],9,0x2441453),i=b(i,a,r,n,e[15],14,-0x275e197f),n=b(n,i,a,r,e[4],20,-0x182c0438),r=b(r,n,i,a,e[9],5,0x21e1cde6),a=b(a,r,n,i,e[14],9,-0x3cc8f82a),i=b(i,a,r,n,e[3],14,-0xb2af279),n=b(n,i,a,r,e[8],20,0x455a14ed),r=b(r,n,i,a,e[13],5,-0x561c16fb),a=b(a,r,n,i,e[2],9,-0x3105c08),i=b(i,a,r,n,e[7],14,0x676f02d9),r=y(r,n=b(n,i,a,r,e[12],20,-0x72d5b376),i,a,e[5],4,-378558),a=y(a,r,n,i,e[8],11,-0x788e097f),i=y(i,a,r,n,e[11],16,0x6d9d6122),n=y(n,i,a,r,e[14],23,-0x21ac7f4),r=y(r,n,i,a,e[1],4,-0x5b4115bc),a=y(a,r,n,i,e[4],11,0x4bdecfa9),i=y(i,a,r,n,e[7],16,-0x944b4a0),n=y(n,i,a,r,e[10],23,-0x41404390),r=y(r,n,i,a,e[13],4,0x289b7ec6),a=y(a,r,n,i,e[0],11,-0x155ed806),i=y(i,a,r,n,e[3],16,-0x2b10cf7b),n=y(n,i,a,r,e[6],23,0x4881d05),r=y(r,n,i,a,e[9],4,-0x262b2fc7),a=y(a,r,n,i,e[12],11,-0x1924661b),i=y(i,a,r,n,e[15],16,0x1fa27cf8),r=w(r,n=y(n,i,a,r,e[2],23,-0x3b53a99b),i,a,e[0],6,-0xbd6ddbc),a=w(a,r,n,i,e[7],10,0x432aff97),i=w(i,a,r,n,e[14],15,-0x546bdc59),n=w(n,i,a,r,e[5],21,-0x36c5fc7),r=w(r,n,i,a,e[12],6,0x655b59c3),a=w(a,r,n,i,e[3],10,-0x70f3336e),i=w(i,a,r,n,e[10],15,-1051523),n=w(n,i,a,r,e[1],21,-0x7a7ba22f),r=w(r,n,i,a,e[8],6,0x6fa87e4f),a=w(a,r,n,i,e[15],10,-0x1d31920),i=w(i,a,r,n,e[6],15,-0x5cfebcec),n=w(n,i,a,r,e[13],21,0x4e0811a1),r=w(r,n,i,a,e[4],6,-0x8ac817e),a=w(a,r,n,i,e[11],10,-0x42c50dcb),i=w(i,a,r,n,e[2],15,0x2ad7d2bb),n=w(n,i,a,r,e[9],21,-0x14792c6f),t[0]=_(r,t[0]),t[1]=_(n,t[1]),t[2]=_(i,t[2]),t[3]=_(a,t[3])}function m(t,e,r,n,i,a){return e=_(_(e,t),_(n,a)),_(e<<i|e>>>32-i,r)}function v(t,e,r,n,i,a,o){return m(e&r|~e&n,t,e,i,a,o)}function b(t,e,r,n,i,a,o){return m(e&n|r&~n,t,e,i,a,o)}function y(t,e,r,n,i,a,o){return m(e^r^n,t,e,i,a,o)}function w(t,e,r,n,i,a,o){return m(r^(e|~n),t,e,i,a,o)}function x(t){var e,r=t.length,n=[0x67452301,-0x10325477,-0x67452302,0x10325476];for(e=64;e<=t.length;e+=64)g(n,function(t){var e,r=[];for(e=0;e<64;e+=4)r[e>>2]=t.charCodeAt(e)+(t.charCodeAt(e+1)<<8)+(t.charCodeAt(e+2)<<16)+(t.charCodeAt(e+3)<<24);return r}(t.substring(e-64,e)));t=t.substring(e-64);var i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(e=0;e<t.length;e++)i[e>>2]|=t.charCodeAt(e)<<(e%4<<3);if(i[e>>2]|=128<<(e%4<<3),e>55)for(g(n,i),e=0;e<16;e++)i[e]=0;return i[14]=8*r,g(n,i),n}l=a.atob.bind(a),f=a.btoa.bind(a);var N="0123456789abcdef".split("");function A(t){return String.fromCharCode(255&t,(65280&t)>>8,(0xff0000&t)>>16,(0xff000000&t)>>24)}function L(t){return x(t).map(A).join("")}var S="5d41402abc4b2a76b9719d911017c592"!=function(t){for(var e=0;e<t.length;e++)t[e]=function(t){for(var e="",r=0;r<4;r++)e+=N[t>>8*r+4&15]+N[t>>8*r&15];return e}(t[e]);return t.join("")}(x("hello"));function _(t,e){if(S){var r=(65535&t)+(65535&e);return(t>>16)+(e>>16)+(r>>16)<<16|65535&r}return t+e|0}function P(t,e){if(t!==r){for(var r,n,i=Array(1+(256/t.length|0)+1).join(t),a=[],o=0;o<256;o++)a[o]=o;var s=0;for(o=0;o<256;o++){var c=a[o];s=(s+c+i.charCodeAt(o))%256,a[o]=a[s],a[s]=c}r=t,n=a}else a=n;var u=e.length,h=0,l=0,f="";for(o=0;o<u;o++)l=(l+(c=a[h=(h+1)%256]))%256,a[h]=a[l],a[l]=c,i=a[(a[h]+a[l])%256],f+=String.fromCharCode(e.charCodeAt(o)^i);return f}var k={print:4,modify:8,copy:16,"annot-forms":32};function F(t,e,r,n){this.v=1,this.r=2;var i=192;t.forEach(function(t){if(void 0!==k.perm)throw Error("Invalid permission: "+t);i+=k[t]}),this.padding="(\xbfN^Nu\x8aAd\0NV\xff\xfa\x01\b..\0\xb6\xd0h>\x80/\f\xa9\xfedSiz";var a=(e+this.padding).substr(0,32),o=(r+this.padding).substr(0,32);this.O=this.processOwnerPassword(a,o),this.P=-(1+(255^i)),this.encryptionKey=L(a+this.O+this.lsbFirstWord(this.P)+this.hexToBytes(n)).substr(0,5),this.U=P(this.encryptionKey,this.padding)}function I(t){if(/[^\u0000-\u00ff]/.test(t))throw Error("Invalid PDF Name Object: "+t+", Only accept ASCII characters.");for(var e="",r=t.length,n=0;n<r;n++){var i=t.charCodeAt(n);i<33||35===i||37===i||40===i||41===i||47===i||60===i||62===i||91===i||93===i||123===i||125===i||i>126?e+="#"+("0"+i.toString(16)).slice(-2):e+=t[n]}return e}function C(t){if("object"!==(0,n.A)(t))throw Error("Invalid Context passed to initialize PubSub (jsPDF-module)");var e={};this.subscribe=function(t,r,n){if(n=n||!1,"string"!=typeof t||"function"!=typeof r||"boolean"!=typeof n)throw Error("Invalid arguments passed to PubSub.subscribe (jsPDF-module)");e.hasOwnProperty(t)||(e[t]={});var i=Math.random().toString(35);return e[t][i]=[r,!!n],i},this.unsubscribe=function(t){for(var r in e)if(e[r][t])return delete e[r][t],0===Object.keys(e[r]).length&&delete e[r],!0;return!1},this.publish=function(r){if(e.hasOwnProperty(r)){var n=Array.prototype.slice.call(arguments,1),i=[];for(var o in e[r]){var c=e[r][o];try{c[0].apply(t,n)}catch(t){a.console&&s.error("jsPDF PubSub Error",t.message,t)}c[1]&&i.push(o)}i.length&&i.forEach(this.unsubscribe)}},this.getTopics=function(){return e}}function j(t){if(!(this instanceof j))return new j(t);var e="opacity,stroke-opacity".split(",");for(var r in t)t.hasOwnProperty(r)&&e.indexOf(r)>=0&&(this[r]=t[r]);this.id="",this.objectNumber=-1}function O(t,e){this.gState=t,this.matrix=e,this.id="",this.objectNumber=-1}function E(t,e,r,n,i){if(!(this instanceof E))return new E(t,e,r,n,i);this.type="axial"===t?2:3,this.coords=e,this.colors=r,O.call(this,n,i)}function M(t,e,r,n,i){if(!(this instanceof M))return new M(t,e,r,n,i);this.boundingBox=t,this.xStep=e,this.yStep=r,this.stream="",this.cloneIndex=0,O.call(this,n,i)}function q(t){var e,r="string"==typeof arguments[0]?arguments[0]:"p",i=arguments[1],o=arguments[2],c=arguments[3],u=[],h=1,l=16,g="S",m=null;"object"===(0,n.A)(t=t||{})&&(r=t.orientation,i=t.unit||i,o=t.format||o,c=t.compress||t.compressPdf||c,null!==(m=t.encryption||null)&&(m.userPassword=m.userPassword||"",m.ownerPassword=m.ownerPassword||"",m.userPermissions=m.userPermissions||[]),h="number"==typeof t.userUnit?Math.abs(t.userUnit):1,void 0!==t.precision&&(e=t.precision),void 0!==t.floatPrecision&&(l=t.floatPrecision),g=t.defaultPathOperation||"S"),u=t.filters||(!0===c?["FlateEncode"]:u),i=i||"mm",r=(""+(r||"P")).toLowerCase();var v=t.putOnlyUsedFonts||!1,b={},y={internal:{},__private__:{}};y.__private__.PubSub=C;var w="1.3",x=y.__private__.getPdfVersion=function(){return w};y.__private__.setPdfVersion=function(t){w=t};var N={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};y.__private__.getPageFormats=function(){return N};var A=y.__private__.getPageFormat=function(t){return N[t]};o=o||"a4";var L={COMPAT:"compat",ADVANCED:"advanced"},S=L.COMPAT;function _(){this.saveGraphicsState(),th(new tW(tS,0,0,-tS,0,rm()*tS).toString()+" cm"),this.setFontSize(this.getFontSize()/tS),g="n",S=L.ADVANCED}function P(){this.restoreGraphicsState(),g="S",S=L.COMPAT}var k=y.__private__.combineFontStyleAndFontWeight=function(t,e){if("bold"==t&&"normal"==e||"bold"==t&&400==e||"normal"==t&&"italic"==e||"bold"==t&&"italic"==e)throw Error("Invalid Combination of fontweight and fontstyle");return e&&(t=400==e||"normal"===e?"italic"===t?"italic":"normal":700!=e&&"bold"!==e||"normal"!==t?(700==e?"bold":e)+""+t:"bold"),t};y.advancedAPI=function(t){var e=S===L.COMPAT;return e&&_.call(this),"function"!=typeof t||(t(this),e&&P.call(this)),this},y.compatAPI=function(t){var e=S===L.ADVANCED;return e&&P.call(this),"function"!=typeof t||(t(this),e&&_.call(this)),this},y.isAdvancedAPI=function(){return S===L.ADVANCED};var O,B=function(t){if(S!==L.ADVANCED)throw Error(t+" is only available in 'advanced' API mode. You need to call advancedAPI() first.")},D=y.roundToPrecision=y.__private__.roundToPrecision=function(t,r){var n=e||r;if(isNaN(t)||isNaN(n))throw Error("Invalid argument passed to jsPDF.roundToPrecision");return t.toFixed(n).replace(/0+$/,"")};O=y.hpf=y.__private__.hpf="number"==typeof l?function(t){if(isNaN(t))throw Error("Invalid argument passed to jsPDF.hpf");return D(t,l)}:"smart"===l?function(t){if(isNaN(t))throw Error("Invalid argument passed to jsPDF.hpf");return D(t,t>-1&&t<1?16:5)}:function(t){if(isNaN(t))throw Error("Invalid argument passed to jsPDF.hpf");return D(t,16)};var R=y.f2=y.__private__.f2=function(t){if(isNaN(t))throw Error("Invalid argument passed to jsPDF.f2");return D(t,2)},T=y.__private__.f3=function(t){if(isNaN(t))throw Error("Invalid argument passed to jsPDF.f3");return D(t,3)},z=y.scale=y.__private__.scale=function(t){if(isNaN(t))throw Error("Invalid argument passed to jsPDF.scale");return S===L.COMPAT?t*tS:S===L.ADVANCED?t:void 0},U=function(t){return z(S===L.COMPAT?rm()-t:S===L.ADVANCED?t:void 0)};y.__private__.setPrecision=y.setPrecision=function(t){"number"==typeof parseInt(t,10)&&(e=parseInt(t,10))};var H,W="00000000000000000000000000000000",V=y.__private__.getFileId=function(){return W},G=y.__private__.setFileId=function(t){return W=void 0!==t&&/^[a-fA-F0-9]{32}$/.test(t)?t.toUpperCase():W.split("").map(function(){return"ABCDEF0123456789".charAt(Math.floor(16*Math.random()))}).join(""),null!==m&&(eE=new F(m.userPermissions,m.userPassword,m.ownerPassword,W)),W};y.setFileId=function(t){return G(t),this},y.getFileId=function(){return V()};var Y=y.__private__.convertDateToPDFDate=function(t){var e=t.getTimezoneOffset(),r=Math.floor(Math.abs(e/60)),n=Math.abs(e%60),i=[e<0?"+":"-",$(r),"'",$(n),"'"].join("");return["D:",t.getFullYear(),$(t.getMonth()+1),$(t.getDate()),$(t.getHours()),$(t.getMinutes()),$(t.getSeconds()),i].join("")},J=y.__private__.convertPDFDateToDate=function(t){return new Date(parseInt(t.substr(2,4),10),parseInt(t.substr(6,2),10)-1,parseInt(t.substr(8,2),10),parseInt(t.substr(10,2),10),parseInt(t.substr(12,2),10),parseInt(t.substr(14,2),10),0)},X=y.__private__.setCreationDate=function(t){var e;if(void 0===t&&(t=new Date),t instanceof Date)e=Y(t);else{if(!/^D:(20[0-2][0-9]|203[0-7]|19[7-9][0-9])(0[0-9]|1[0-2])([0-2][0-9]|3[0-1])(0[0-9]|1[0-9]|2[0-3])(0[0-9]|[1-5][0-9])(0[0-9]|[1-5][0-9])(\+0[0-9]|\+1[0-4]|-0[0-9]|-1[0-1])'(0[0-9]|[1-5][0-9])'?$/.test(t))throw Error("Invalid argument passed to jsPDF.setCreationDate");e=t}return H=e},K=y.__private__.getCreationDate=function(t){var e=H;return"jsDate"===t&&(e=J(H)),e};y.setCreationDate=function(t){return X(t),this},y.getCreationDate=function(t){return K(t)};var Z,$=y.__private__.padd2=function(t){return("0"+parseInt(t)).slice(-2)},Q=y.__private__.padd2Hex=function(t){return("00"+(t=t.toString())).substr(t.length)},tt=0,te=[],tr=[],tn=0,ti=[],ta=[],to=!1,ts=tr,tc=function(){tt=0,tn=0,tr=[],te=[],ti=[],t$=tX(),tQ=tX()};y.__private__.setCustomOutputDestination=function(t){to=!0,ts=t};var tu=function(t){to||(ts=t)};y.__private__.resetCustomOutputDestination=function(){to=!1,ts=tr};var th=y.__private__.out=function(t){return t=t.toString(),tn+=t.length+1,ts.push(t),ts},tl=y.__private__.write=function(t){return th(1==arguments.length?t.toString():Array.prototype.join.call(arguments," "))},tf=y.__private__.getArrayBuffer=function(t){for(var e=t.length,r=new ArrayBuffer(e),n=new Uint8Array(r);e--;)n[e]=t.charCodeAt(e);return r},td=[["Helvetica","helvetica","normal","WinAnsiEncoding"],["Helvetica-Bold","helvetica","bold","WinAnsiEncoding"],["Helvetica-Oblique","helvetica","italic","WinAnsiEncoding"],["Helvetica-BoldOblique","helvetica","bolditalic","WinAnsiEncoding"],["Courier","courier","normal","WinAnsiEncoding"],["Courier-Bold","courier","bold","WinAnsiEncoding"],["Courier-Oblique","courier","italic","WinAnsiEncoding"],["Courier-BoldOblique","courier","bolditalic","WinAnsiEncoding"],["Times-Roman","times","normal","WinAnsiEncoding"],["Times-Bold","times","bold","WinAnsiEncoding"],["Times-Italic","times","italic","WinAnsiEncoding"],["Times-BoldItalic","times","bolditalic","WinAnsiEncoding"],["ZapfDingbats","zapfdingbats","normal",null],["Symbol","symbol","normal",null]];y.__private__.getStandardFonts=function(){return td};var tp=t.fontSize||16;y.__private__.setFontSize=y.setFontSize=function(t){return tp=S===L.ADVANCED?t/tS:t,this};var tg,tm=y.__private__.getFontSize=y.getFontSize=function(){return S===L.COMPAT?tp:tp*tS},tv=t.R2L||!1;y.__private__.setR2L=y.setR2L=function(t){return tv=t,this},y.__private__.getR2L=y.getR2L=function(){return tv};var tb,ty=y.__private__.setZoomMode=function(t){if(/^(?:\d+\.\d*|\d*\.\d+|\d+)%$/.test(t))tg=t;else if(isNaN(t)){if(-1===[void 0,null,"fullwidth","fullheight","fullpage","original"].indexOf(t))throw Error('zoom must be Integer (e.g. 2), a percentage Value (e.g. 300%) or fullwidth, fullheight, fullpage, original. "'+t+'" is not recognized.');tg=t}else tg=parseInt(t,10)};y.__private__.getZoomMode=function(){return tg};var tw,tx=y.__private__.setPageMode=function(t){if(-1==[void 0,null,"UseNone","UseOutlines","UseThumbs","FullScreen"].indexOf(t))throw Error('Page mode must be one of UseNone, UseOutlines, UseThumbs, or FullScreen. "'+t+'" is not recognized.');tb=t};y.__private__.getPageMode=function(){return tb};var tN=y.__private__.setLayoutMode=function(t){if(-1==[void 0,null,"continuous","single","twoleft","tworight","two"].indexOf(t))throw Error('Layout mode must be one of continuous, single, twoleft, tworight. "'+t+'" is not recognized.');tw=t};y.__private__.getLayoutMode=function(){return tw},y.__private__.setDisplayMode=y.setDisplayMode=function(t,e,r){return ty(t),tN(e),tx(r),this};var tA={title:"",subject:"",author:"",keywords:"",creator:""};y.__private__.getDocumentProperty=function(t){if(-1===Object.keys(tA).indexOf(t))throw Error("Invalid argument passed to jsPDF.getDocumentProperty");return tA[t]},y.__private__.getDocumentProperties=function(){return tA},y.__private__.setDocumentProperties=y.setProperties=y.setDocumentProperties=function(t){for(var e in tA)tA.hasOwnProperty(e)&&t[e]&&(tA[e]=t[e]);return this},y.__private__.setDocumentProperty=function(t,e){if(-1===Object.keys(tA).indexOf(t))throw Error("Invalid arguments passed to jsPDF.setDocumentProperty");return tA[t]=e};var tL,tS,t_,tP,tk,tF={},tI={},tC=[],tj={},tO={},tE={},tM={},tq=null,tB=0,tD=[],tR=new C(y),tT=t.hotfixes||[],tz={},tU={},tH=[],tW=function t(e,r,n,i,a,o){if(!(this instanceof t))return new t(e,r,n,i,a,o);isNaN(e)&&(e=1),isNaN(r)&&(r=0),isNaN(n)&&(n=0),isNaN(i)&&(i=1),isNaN(a)&&(a=0),isNaN(o)&&(o=0),this._matrix=[e,r,n,i,a,o]};Object.defineProperty(tW.prototype,"sx",{get:function(){return this._matrix[0]},set:function(t){this._matrix[0]=t}}),Object.defineProperty(tW.prototype,"shy",{get:function(){return this._matrix[1]},set:function(t){this._matrix[1]=t}}),Object.defineProperty(tW.prototype,"shx",{get:function(){return this._matrix[2]},set:function(t){this._matrix[2]=t}}),Object.defineProperty(tW.prototype,"sy",{get:function(){return this._matrix[3]},set:function(t){this._matrix[3]=t}}),Object.defineProperty(tW.prototype,"tx",{get:function(){return this._matrix[4]},set:function(t){this._matrix[4]=t}}),Object.defineProperty(tW.prototype,"ty",{get:function(){return this._matrix[5]},set:function(t){this._matrix[5]=t}}),Object.defineProperty(tW.prototype,"a",{get:function(){return this._matrix[0]},set:function(t){this._matrix[0]=t}}),Object.defineProperty(tW.prototype,"b",{get:function(){return this._matrix[1]},set:function(t){this._matrix[1]=t}}),Object.defineProperty(tW.prototype,"c",{get:function(){return this._matrix[2]},set:function(t){this._matrix[2]=t}}),Object.defineProperty(tW.prototype,"d",{get:function(){return this._matrix[3]},set:function(t){this._matrix[3]=t}}),Object.defineProperty(tW.prototype,"e",{get:function(){return this._matrix[4]},set:function(t){this._matrix[4]=t}}),Object.defineProperty(tW.prototype,"f",{get:function(){return this._matrix[5]},set:function(t){this._matrix[5]=t}}),Object.defineProperty(tW.prototype,"rotation",{get:function(){return Math.atan2(this.shx,this.sx)}}),Object.defineProperty(tW.prototype,"scaleX",{get:function(){return this.decompose().scale.sx}}),Object.defineProperty(tW.prototype,"scaleY",{get:function(){return this.decompose().scale.sy}}),Object.defineProperty(tW.prototype,"isIdentity",{get:function(){return 1===this.sx&&0===this.shy&&0===this.shx&&1===this.sy&&0===this.tx&&0===this.ty}}),tW.prototype.join=function(t){return[this.sx,this.shy,this.shx,this.sy,this.tx,this.ty].map(O).join(t)},tW.prototype.multiply=function(t){return new tW(t.sx*this.sx+t.shy*this.shx,t.sx*this.shy+t.shy*this.sy,t.shx*this.sx+t.sy*this.shx,t.shx*this.shy+t.sy*this.sy,t.tx*this.sx+t.ty*this.shx+this.tx,t.tx*this.shy+t.ty*this.sy+this.ty)},tW.prototype.decompose=function(){var t=this.sx,e=this.shy,r=this.shx,n=this.sy,i=this.tx,a=this.ty,o=Math.sqrt(t*t+e*e),s=(t/=o)*r+(e/=o)*n,c=Math.sqrt((r-=t*s)*r+(n-=e*s)*n);return s/=c,t*(n/=c)<e*(r/=c)&&(t=-t,e=-e,s=-s,o=-o),{scale:new tW(o,0,0,c,0,0),translate:new tW(1,0,0,1,i,a),rotate:new tW(t,e,-e,t,0,0),skew:new tW(1,0,s,1,0,0)}},tW.prototype.toString=function(t){return this.join(" ")},tW.prototype.inversed=function(){var t=this.sx,e=this.shy,r=this.shx,n=this.sy,i=this.tx,a=this.ty,o=1/(t*n-e*r),s=n*o,c=-e*o,u=-r*o,h=t*o;return new tW(s,c,u,h,-s*i-u*a,-c*i-h*a)},tW.prototype.applyToPoint=function(t){return new rc(t.x*this.sx+t.y*this.shx+this.tx,t.x*this.shy+t.y*this.sy+this.ty)},tW.prototype.applyToRectangle=function(t){var e=this.applyToPoint(t),r=this.applyToPoint(new rc(t.x+t.w,t.y+t.h));return new ru(e.x,e.y,r.x-e.x,r.y-e.y)},tW.prototype.clone=function(){return new tW(this.sx,this.shy,this.shx,this.sy,this.tx,this.ty)},y.Matrix=tW;var tV=y.matrixMult=function(t,e){return e.multiply(t)},tG=new tW(1,0,0,1,0,0);y.unitMatrix=y.identityMatrix=tG;var tY=function(t,e){if(!tO[t]){var r=(e instanceof E?"Sh":"P")+(Object.keys(tj).length+1).toString(10);e.id=r,tO[t]=r,tj[r]=e,tR.publish("addPattern",e)}};y.ShadingPattern=E,y.TilingPattern=M,y.addShadingPattern=function(t,e){return B("addShadingPattern()"),tY(t,e),this},y.beginTilingPattern=function(t){B("beginTilingPattern()"),rl(t.boundingBox[0],t.boundingBox[1],t.boundingBox[2]-t.boundingBox[0],t.boundingBox[3]-t.boundingBox[1],t.matrix)},y.endTilingPattern=function(t,e){B("endTilingPattern()"),e.stream=ta[Z].join("\n"),tY(t,e),tR.publish("endTilingPattern",e),tH.pop().restore()};var tJ=y.__private__.newObject=function(){var t=tX();return tK(t,!0),t},tX=y.__private__.newObjectDeferred=function(){return te[++tt]=function(){return tn},tt},tK=function(t,e){return e="boolean"==typeof e&&e,te[t]=tn,e&&th(t+" 0 obj"),t},tZ=y.__private__.newAdditionalObject=function(){var t={objId:tX(),content:""};return ti.push(t),t},t$=tX(),tQ=tX(),t1=y.__private__.decodeColorString=function(t){var e=t.split(" ");if(2!==e.length||"g"!==e[1]&&"G"!==e[1])5===e.length&&("k"===e[4]||"K"===e[4])&&(e=[(1-e[0])*(1-e[3]),(1-e[1])*(1-e[3]),(1-e[2])*(1-e[3]),"r"]);else{var r=parseFloat(e[0]);e=[r,r,r,"r"]}for(var n="#",i=0;i<3;i++)n+=("0"+Math.floor(255*parseFloat(e[i])).toString(16)).slice(-2);return n},t2=y.__private__.encodeColorString=function(t){"string"==typeof t&&(t={ch1:t});var e,r=t.ch1,i=t.ch2,a=t.ch3,o=t.ch4,s="draw"===t.pdfColorType?["G","RG","K"]:["g","rg","k"];if("string"==typeof r&&"#"!==r.charAt(0)){var c=new p(r);if(c.ok)r=c.toHex();else if(!/^\d*\.?\d*$/.test(r))throw Error('Invalid color "'+r+'" passed to jsPDF.encodeColorString.')}if("string"==typeof r&&/^#[0-9A-Fa-f]{3}$/.test(r)&&(r="#"+r[1]+r[1]+r[2]+r[2]+r[3]+r[3]),"string"==typeof r&&/^#[0-9A-Fa-f]{6}$/.test(r)){var u=parseInt(r.substr(1),16);r=u>>16&255,i=u>>8&255,a=255&u}if(void 0===i||void 0===o&&r===i&&i===a)e="string"==typeof r?r+" "+s[0]:2===t.precision?R(r/255)+" "+s[0]:T(r/255)+" "+s[0];else if(void 0===o||"object"===(0,n.A)(o)){if(o&&!isNaN(o.a)&&0===o.a)return["1.","1.","1.",s[1]].join(" ");e="string"==typeof r?[r,i,a,s[1]].join(" "):2===t.precision?[R(r/255),R(i/255),R(a/255),s[1]].join(" "):[T(r/255),T(i/255),T(a/255),s[1]].join(" ")}else e="string"==typeof r?[r,i,a,o,s[2]].join(" "):2===t.precision?[R(r),R(i),R(a),R(o),s[2]].join(" "):[T(r),T(i),T(a),T(o),s[2]].join(" ");return e},t5=y.__private__.getFilters=function(){return u},t0=y.__private__.putStream=function(t){var e=(t=t||{}).data||"",r=t.filters||t5(),n=t.alreadyAppliedFilters||[],i=t.addLength1||!1,a=e.length,o=t.objectId,s=function(t){return t};if(null!==m&&void 0===o)throw Error("ObjectId must be passed to putStream for file encryption");null!==m&&(s=eE.encryptor(o,0));var c={};!0===r&&(r=["FlateEncode"]);var u=t.additionalKeyValues||[],h=(c=void 0!==q.API.processDataByFilters?q.API.processDataByFilters(e,r):{data:e,reverseChain:[]}).reverseChain+(Array.isArray(n)?n.join(" "):n.toString());if(0!==c.data.length&&(u.push({key:"Length",value:c.data.length}),!0===i&&u.push({key:"Length1",value:a})),0!=h.length)if(h.split("/").length-1==1)u.push({key:"Filter",value:h});else{u.push({key:"Filter",value:"["+h+"]"});for(var l=0;l<u.length;l+=1)if("DecodeParms"===u[l].key){for(var f=[],d=0;d<c.reverseChain.split("/").length-1;d+=1)f.push("null");f.push(u[l].value),u[l].value="["+f.join(" ")+"]"}}th("<<");for(var p=0;p<u.length;p++)th("/"+u[p].key+" "+u[p].value);th(">>"),0!==c.data.length&&(th("stream"),th(s(c.data)),th("endstream"))},t3=y.__private__.putPage=function(t){var e=t.number,r=t.data,n=t.objId,i=t.contentsObjId;tK(n,!0),th("<</Type /Page"),th("/Parent "+t.rootDictionaryObjId+" 0 R"),th("/Resources "+t.resourceDictionaryObjId+" 0 R"),th("/MediaBox ["+parseFloat(O(t.mediaBox.bottomLeftX))+" "+parseFloat(O(t.mediaBox.bottomLeftY))+" "+O(t.mediaBox.topRightX)+" "+O(t.mediaBox.topRightY)+"]"),null!==t.cropBox&&th("/CropBox ["+O(t.cropBox.bottomLeftX)+" "+O(t.cropBox.bottomLeftY)+" "+O(t.cropBox.topRightX)+" "+O(t.cropBox.topRightY)+"]"),null!==t.bleedBox&&th("/BleedBox ["+O(t.bleedBox.bottomLeftX)+" "+O(t.bleedBox.bottomLeftY)+" "+O(t.bleedBox.topRightX)+" "+O(t.bleedBox.topRightY)+"]"),null!==t.trimBox&&th("/TrimBox ["+O(t.trimBox.bottomLeftX)+" "+O(t.trimBox.bottomLeftY)+" "+O(t.trimBox.topRightX)+" "+O(t.trimBox.topRightY)+"]"),null!==t.artBox&&th("/ArtBox ["+O(t.artBox.bottomLeftX)+" "+O(t.artBox.bottomLeftY)+" "+O(t.artBox.topRightX)+" "+O(t.artBox.topRightY)+"]"),"number"==typeof t.userUnit&&1!==t.userUnit&&th("/UserUnit "+t.userUnit),tR.publish("putPage",{objId:n,pageContext:tD[e],pageNumber:e,page:r}),th("/Contents "+i+" 0 R"),th(">>"),th("endobj");var a=r.join("\n");return S===L.ADVANCED&&(a+="\nQ"),tK(i,!0),t0({data:a,filters:t5(),objectId:i}),th("endobj"),n},t4=y.__private__.putPages=function(){var t,e,r=[];for(t=1;t<=tB;t++)tD[t].objId=tX(),tD[t].contentsObjId=tX();for(t=1;t<=tB;t++)r.push(t3({number:t,data:ta[t],objId:tD[t].objId,contentsObjId:tD[t].contentsObjId,mediaBox:tD[t].mediaBox,cropBox:tD[t].cropBox,bleedBox:tD[t].bleedBox,trimBox:tD[t].trimBox,artBox:tD[t].artBox,userUnit:tD[t].userUnit,rootDictionaryObjId:t$,resourceDictionaryObjId:tQ}));tK(t$,!0),th("<</Type /Pages");var n="/Kids [";for(e=0;e<tB;e++)n+=r[e]+" 0 R ";th(n+"]"),th("/Count "+tB),th(">>"),th("endobj"),tR.publish("postPutPages")},t6=function(t){tR.publish("putFont",{font:t,out:th,newObject:tJ,putStream:t0}),!0!==t.isAlreadyPutted&&(t.objectNumber=tJ(),th("<<"),th("/Type /Font"),th("/BaseFont /"+I(t.postScriptName)),th("/Subtype /Type1"),"string"==typeof t.encoding&&th("/Encoding /"+t.encoding),th("/FirstChar 32"),th("/LastChar 255"),th(">>"),th("endobj"))},t8=function(){for(var t in tF)tF.hasOwnProperty(t)&&(!1===v||!0===v&&b.hasOwnProperty(t))&&t6(tF[t])},t7=function(t){t.objectNumber=tJ();var e=[];e.push({key:"Type",value:"/XObject"}),e.push({key:"Subtype",value:"/Form"}),e.push({key:"BBox",value:"["+[O(t.x),O(t.y),O(t.x+t.width),O(t.y+t.height)].join(" ")+"]"}),e.push({key:"Matrix",value:"["+t.matrix.toString()+"]"}),t0({data:t.pages[1].join("\n"),additionalKeyValues:e,objectId:t.objectNumber}),th("endobj")},t9=function(){for(var t in tz)tz.hasOwnProperty(t)&&t7(tz[t])},et=function(t,e){var r,n=[],i=1/(e-1);for(r=0;r<1;r+=i)n.push(r);if(n.push(1),0!=t[0].offset){var a={offset:0,color:t[0].color};t.unshift(a)}if(1!=t[t.length-1].offset){var o={offset:1,color:t[t.length-1].color};t.push(o)}for(var s="",c=0,u=0;u<n.length;u++){for(r=n[u];r>t[c+1].offset;)c++;var h=t[c].offset,l=(r-h)/(t[c+1].offset-h),f=t[c].color,d=t[c+1].color;s+=Q(Math.round((1-l)*f[0]+l*d[0]).toString(16))+Q(Math.round((1-l)*f[1]+l*d[1]).toString(16))+Q(Math.round((1-l)*f[2]+l*d[2]).toString(16))}return s.trim()},ee=function(t,e){e||(e=21);var r=tJ(),n=et(t.colors,e),i=[];i.push({key:"FunctionType",value:"0"}),i.push({key:"Domain",value:"[0.0 1.0]"}),i.push({key:"Size",value:"["+e+"]"}),i.push({key:"BitsPerSample",value:"8"}),i.push({key:"Range",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),i.push({key:"Decode",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),t0({data:n,additionalKeyValues:i,alreadyAppliedFilters:["/ASCIIHexDecode"],objectId:r}),th("endobj"),t.objectNumber=tJ(),th("<< /ShadingType "+t.type),th("/ColorSpace /DeviceRGB");var a="/Coords ["+O(parseFloat(t.coords[0]))+" "+O(parseFloat(t.coords[1]))+" ";2===t.type?a+=O(parseFloat(t.coords[2]))+" "+O(parseFloat(t.coords[3])):a+=O(parseFloat(t.coords[2]))+" "+O(parseFloat(t.coords[3]))+" "+O(parseFloat(t.coords[4]))+" "+O(parseFloat(t.coords[5])),th(a+="]"),t.matrix&&th("/Matrix ["+t.matrix.toString()+"]"),th("/Function "+r+" 0 R"),th("/Extend [true true]"),th(">>"),th("endobj")},er=function(t,e){var r=tX(),n=tJ();e.push({resourcesOid:r,objectOid:n}),t.objectNumber=n;var i=[];i.push({key:"Type",value:"/Pattern"}),i.push({key:"PatternType",value:"1"}),i.push({key:"PaintType",value:"1"}),i.push({key:"TilingType",value:"1"}),i.push({key:"BBox",value:"["+t.boundingBox.map(O).join(" ")+"]"}),i.push({key:"XStep",value:O(t.xStep)}),i.push({key:"YStep",value:O(t.yStep)}),i.push({key:"Resources",value:r+" 0 R"}),t.matrix&&i.push({key:"Matrix",value:"["+t.matrix.toString()+"]"}),t0({data:t.stream,additionalKeyValues:i,objectId:t.objectNumber}),th("endobj")},en=function(t){var e;for(e in tj)tj.hasOwnProperty(e)&&(tj[e]instanceof E?ee(tj[e]):tj[e]instanceof M&&er(tj[e],t))},ei=function(t){for(var e in t.objectNumber=tJ(),th("<<"),t)switch(e){case"opacity":th("/ca "+R(t[e]));break;case"stroke-opacity":th("/CA "+R(t[e]))}th(">>"),th("endobj")},ea=function(){var t;for(t in tE)tE.hasOwnProperty(t)&&ei(tE[t])},eo=function(){for(var t in th("/XObject <<"),tz)tz.hasOwnProperty(t)&&tz[t].objectNumber>=0&&th("/"+t+" "+tz[t].objectNumber+" 0 R");tR.publish("putXobjectDict"),th(">>")},es=function(){eE.oid=tJ(),th("<<"),th("/Filter /Standard"),th("/V "+eE.v),th("/R "+eE.r),th("/U <"+eE.toHexString(eE.U)+">"),th("/O <"+eE.toHexString(eE.O)+">"),th("/P "+eE.P),th(">>"),th("endobj")},ec=function(){for(var t in th("/Font <<"),tF)tF.hasOwnProperty(t)&&(!1===v||!0===v&&b.hasOwnProperty(t))&&th("/"+t+" "+tF[t].objectNumber+" 0 R");th(">>")},eu=function(){if(Object.keys(tj).length>0){for(var t in th("/Shading <<"),tj)tj.hasOwnProperty(t)&&tj[t]instanceof E&&tj[t].objectNumber>=0&&th("/"+t+" "+tj[t].objectNumber+" 0 R");tR.publish("putShadingPatternDict"),th(">>")}},eh=function(t){if(Object.keys(tj).length>0){for(var e in th("/Pattern <<"),tj)tj.hasOwnProperty(e)&&tj[e]instanceof y.TilingPattern&&tj[e].objectNumber>=0&&tj[e].objectNumber<t&&th("/"+e+" "+tj[e].objectNumber+" 0 R");tR.publish("putTilingPatternDict"),th(">>")}},el=function(){if(Object.keys(tE).length>0){var t;for(t in th("/ExtGState <<"),tE)tE.hasOwnProperty(t)&&tE[t].objectNumber>=0&&th("/"+t+" "+tE[t].objectNumber+" 0 R");tR.publish("putGStateDict"),th(">>")}},ef=function(t){tK(t.resourcesOid,!0),th("<<"),th("/ProcSet [/PDF /Text /ImageB /ImageC /ImageI]"),ec(),eu(),eh(t.objectOid),el(),eo(),th(">>"),th("endobj")},ed=function(){var t=[];t8(),ea(),t9(),en(t),tR.publish("putResources"),t.forEach(ef),ef({resourcesOid:tQ,objectOid:Number.MAX_SAFE_INTEGER}),tR.publish("postPutResources")},ep=function(){tR.publish("putAdditionalObjects");for(var t=0;t<ti.length;t++){var e=ti[t];tK(e.objId,!0),th(e.content),th("endobj")}tR.publish("postPutAdditionalObjects")},eg=function(t){tI[t.fontName]=tI[t.fontName]||{},tI[t.fontName][t.fontStyle]=t.id},em=function(t,e,r,n,i){var a={id:"F"+(Object.keys(tF).length+1).toString(10),postScriptName:t,fontName:e,fontStyle:r,encoding:n,isStandardFont:i||!1,metadata:{}};return tR.publish("addFont",{font:a,instance:this}),tF[a.id]=a,eg(a),a.id},ev=function(t,e){var r,n,i,a,o,s,c,u,h;if(i=(e=e||{}).sourceEncoding||"Unicode",o=e.outputEncoding,(e.autoencode||o)&&tF[tL].metadata&&tF[tL].metadata[i]&&tF[tL].metadata[i].encoding&&(a=tF[tL].metadata[i].encoding,!o&&tF[tL].encoding&&(o=tF[tL].encoding),!o&&a.codePages&&(o=a.codePages[0]),"string"==typeof o&&(o=a[o]),o)){for(c=!1,s=[],r=0,n=t.length;r<n;r++)(u=o[t.charCodeAt(r)])?s.push(String.fromCharCode(u)):s.push(t[r]),s[r].charCodeAt(0)>>8&&(c=!0);t=s.join("")}for(r=t.length;void 0===c&&0!==r;)t.charCodeAt(r-1)>>8&&(c=!0),r--;if(!c)return t;for(s=e.noBOM?[]:[254,255],r=0,n=t.length;r<n;r++){if((h=(u=t.charCodeAt(r))>>8)>>8)throw Error("Character at position "+r+" of string '"+t+"' exceeds 16bits. Cannot be encoded into UCS-2 BE");s.push(h),s.push(u-(h<<8))}return String.fromCharCode.apply(void 0,s)},eb=y.__private__.pdfEscape=y.pdfEscape=function(t,e){return ev(t,e).replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},ey=y.__private__.beginPage=function(t){ta[++tB]=[],tD[tB]={objId:0,contentsObjId:0,userUnit:Number(h),artBox:null,bleedBox:null,cropBox:null,trimBox:null,mediaBox:{bottomLeftX:0,bottomLeftY:0,topRightX:Number(t[0]),topRightY:Number(t[1])}},eN(tB),tu(ta[Z])},ew=function(t,e){var n,i,a;switch(r=e||r,"string"==typeof t&&Array.isArray(n=A(t.toLowerCase()))&&(i=n[0],a=n[1]),Array.isArray(t)&&(i=t[0]*tS,a=t[1]*tS),isNaN(i)&&(i=o[0],a=o[1]),(i>14400||a>14400)&&(s.warn("A page in a PDF can not be wider or taller than 14400 userUnit. jsPDF limits the width/height to 14400"),i=Math.min(14400,i),a=Math.min(14400,a)),o=[i,a],r.substr(0,1)){case"l":a>i&&(o=[a,i]);break;case"p":i>a&&(o=[a,i])}ey(o),e2(eQ),th(e9),0!==ra&&th(ra+" J"),0!==ro&&th(ro+" j"),tR.publish("addPage",{pageNumber:tB})},ex=function(t){t>0&&t<=tB&&(ta.splice(t,1),tD.splice(t,1),tB--,Z>tB&&(Z=tB),this.setPage(Z))},eN=function(t){t>0&&t<=tB&&(Z=t)},eA=y.__private__.getNumberOfPages=y.getNumberOfPages=function(){return ta.length-1},eL=function(t,e,r){var n,i=void 0;return r=r||{},t=void 0!==t?t:tF[tL].fontName,e=void 0!==e?e:tF[tL].fontStyle,void 0!==tI[n=t.toLowerCase()]&&void 0!==tI[n][e]?i=tI[n][e]:void 0!==tI[t]&&void 0!==tI[t][e]?i=tI[t][e]:!1===r.disableWarning&&s.warn("Unable to look up font label for font '"+t+"', '"+e+"'. Refer to getFontList() for available fonts."),i||r.noFallback||null==(i=tI.times[e])&&(i=tI.times.normal),i},eS=y.__private__.putInfo=function(){var t=tJ(),e=function(t){return t};for(var r in null!==m&&(e=eE.encryptor(t,0)),th("<<"),th("/Producer ("+eb(e("jsPDF "+q.version))+")"),tA)tA.hasOwnProperty(r)&&tA[r]&&th("/"+r.substr(0,1).toUpperCase()+r.substr(1)+" ("+eb(e(tA[r]))+")");th("/CreationDate ("+eb(e(H))+")"),th(">>"),th("endobj")},e_=y.__private__.putCatalog=function(t){var e=(t=t||{}).rootDictionaryObjId||t$;switch(tJ(),th("<<"),th("/Type /Catalog"),th("/Pages "+e+" 0 R"),tg||(tg="fullwidth"),tg){case"fullwidth":th("/OpenAction [3 0 R /FitH null]");break;case"fullheight":th("/OpenAction [3 0 R /FitV null]");break;case"fullpage":th("/OpenAction [3 0 R /Fit]");break;case"original":th("/OpenAction [3 0 R /XYZ null null 1]");break;default:var r=""+tg;"%"===r.substr(r.length-1)&&(tg=parseInt(tg)/100),"number"==typeof tg&&th("/OpenAction [3 0 R /XYZ null null "+R(tg)+"]")}switch(tw||(tw="continuous"),tw){case"continuous":th("/PageLayout /OneColumn");break;case"single":th("/PageLayout /SinglePage");break;case"two":case"twoleft":th("/PageLayout /TwoColumnLeft");break;case"tworight":th("/PageLayout /TwoColumnRight")}tb&&th("/PageMode /"+tb),tR.publish("putCatalog"),th(">>"),th("endobj")},eP=y.__private__.putTrailer=function(){th("trailer"),th("<<"),th("/Size "+(tt+1)),th("/Root "+tt+" 0 R"),th("/Info "+(tt-1)+" 0 R"),null!==m&&th("/Encrypt "+eE.oid+" 0 R"),th("/ID [ <"+W+"> <"+W+"> ]"),th(">>")},ek=y.__private__.putHeader=function(){th("%PDF-"+w),th("%\xba\xdf\xac\xe0")},eF=y.__private__.putXRef=function(){var t="0000000000";th("xref"),th("0 "+(tt+1)),th("0000000000 65535 f ");for(var e=1;e<=tt;e++)"function"==typeof te[e]?th((t+te[e]()).slice(-10)+" 00000 n "):void 0!==te[e]?th((t+te[e]).slice(-10)+" 00000 n "):th("0000000000 00000 n ")},eI=y.__private__.buildDocument=function(){tc(),tu(tr),tR.publish("buildDocument"),ek(),t4(),ep(),ed(),null!==m&&es(),eS(),e_();var t=tn;return eF(),eP(),th("startxref"),th(""+t),th("%%EOF"),tu(ta[Z]),tr.join("\n")},eC=y.__private__.getBlob=function(t){return new Blob([tf(t)],{type:"application/pdf"})},ej=y.output=y.__private__.output=((eZ=function(t,e){switch("string"==typeof(e=e||{})?e={filename:e}:e.filename=e.filename||"generated.pdf",t){case void 0:return eI();case"save":y.save(e.filename);break;case"arraybuffer":return tf(eI());case"blob":return eC(eI());case"bloburi":case"bloburl":if(void 0!==a.URL&&"function"==typeof a.URL.createObjectURL)return a.URL&&a.URL.createObjectURL(eC(eI()))||void 0;s.warn("bloburl is not supported by your system, because URL.createObjectURL is not supported by your browser.");break;case"datauristring":case"dataurlstring":var r="",n=eI();try{r=f(n)}catch(t){r=f(unescape(encodeURIComponent(n)))}return"data:application/pdf;filename="+e.filename+";base64,"+r;case"pdfobjectnewwindow":if("[object Window]"===Object.prototype.toString.call(a)){var i="https://cdnjs.cloudflare.com/ajax/libs/pdfobject/2.1.1/pdfobject.min.js",o=' integrity="sha512-4ze/a9/4jqu+tX9dfOqJYSvyYd5M6qum/3HpCLr+/Jqf0whc37VUbkpNGHR7/8pSnCFw47T1fmIpwBV7UySh3g==" crossorigin="anonymous"';e.pdfObjectUrl&&(i=e.pdfObjectUrl,o="");var c='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><script src="'+i+'"'+o+'><\/script><script >PDFObject.embed("'+this.output("dataurlstring")+'", '+JSON.stringify(e)+");<\/script></body></html>",u=a.open();return null!==u&&u.document.write(c),u}throw Error("The option pdfobjectnewwindow just works in a browser-environment.");case"pdfjsnewwindow":if("[object Window]"===Object.prototype.toString.call(a)){var h='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe id="pdfViewer" src="'+(e.pdfJsUrl||"examples/PDF.js/web/viewer.html")+"?file=&downloadName="+e.filename+'" width="500px" height="400px" /></body></html>',l=a.open();if(null!==l){l.document.write(h);var d=this;l.document.documentElement.querySelector("#pdfViewer").onload=function(){l.document.title=e.filename,l.document.documentElement.querySelector("#pdfViewer").contentWindow.PDFViewerApplication.open(d.output("bloburl"))}}return l}throw Error("The option pdfjsnewwindow just works in a browser-environment.");case"dataurlnewwindow":if("[object Window]"!==Object.prototype.toString.call(a))throw Error("The option dataurlnewwindow just works in a browser-environment.");var p='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe src="'+this.output("datauristring",e)+'"></iframe></body></html>',g=a.open();if(null!==g&&(g.document.write(p),g.document.title=e.filename),g||"undefined"==typeof safari)return g;break;case"datauri":case"dataurl":return a.document.location.href=this.output("datauristring",e);default:return null}}).foo=function(){try{return eZ.apply(this,arguments)}catch(r){var t=r.stack||"";~t.indexOf(" at ")&&(t=t.split(" at ")[1]);var e="Error in function "+t.split("\n")[0].split("<")[0]+": "+r.message;if(!a.console)throw Error(e);a.console.error(e,r),a.alert&&alert(e)}},eZ.foo.bar=eZ,eZ.foo),eO=function(t){return!0===Array.isArray(tT)&&tT.indexOf(t)>-1};switch(i){case"pt":tS=1;break;case"mm":tS=72/25.4;break;case"cm":tS=72/2.54;break;case"in":tS=72;break;case"px":tS=1==eO("px_scaling")?.75:96/72;break;case"pc":case"em":tS=12;break;case"ex":tS=6;break;default:if("number"!=typeof i)throw Error("Invalid unit: "+i);tS=i}var eE=null;X(),G();var eM=y.__private__.getPageInfo=y.getPageInfo=function(t){if(isNaN(t)||t%1!=0)throw Error("Invalid argument passed to jsPDF.getPageInfo");return{objId:tD[t].objId,pageNumber:t,pageContext:tD[t]}},eq=y.__private__.getPageInfoByObjId=function(t){if(isNaN(t)||t%1!=0)throw Error("Invalid argument passed to jsPDF.getPageInfoByObjId");for(var e in tD)if(tD[e].objId===t)break;return eM(e)},eB=y.__private__.getCurrentPageInfo=y.getCurrentPageInfo=function(){return{objId:tD[Z].objId,pageNumber:Z,pageContext:tD[Z]}};y.addPage=function(){return ew.apply(this,arguments),this},y.setPage=function(){return eN.apply(this,arguments),tu.call(this,ta[Z]),this},y.insertPage=function(t){return this.addPage(),this.movePage(Z,t),this},y.movePage=function(t,e){var r,n;if(t>e){r=ta[t],n=tD[t];for(var i=t;i>e;i--)ta[i]=ta[i-1],tD[i]=tD[i-1];ta[e]=r,tD[e]=n,this.setPage(e)}else if(t<e){r=ta[t],n=tD[t];for(var a=t;a<e;a++)ta[a]=ta[a+1],tD[a]=tD[a+1];ta[e]=r,tD[e]=n,this.setPage(e)}return this},y.deletePage=function(){return ex.apply(this,arguments),this},y.__private__.text=y.text=function(t,e,r,i,a){var o,s,c,u,h,l,f,d,p,g=(i=i||{}).scope||this;if("number"==typeof t&&"number"==typeof e&&("string"==typeof r||Array.isArray(r))){var m=r;r=e,e=t,t=m}if(arguments[3]instanceof tW==!1?(c=arguments[4],u=arguments[5],"object"===(0,n.A)(f=arguments[3])&&null!==f||("string"==typeof c&&(u=c,c=null),"string"==typeof f&&(u=f,f=null),"number"==typeof f&&(c=f,f=null),i={flags:f,angle:c,align:u})):(B("The transform parameter of text() with a Matrix value"),p=a),isNaN(e)||isNaN(r)||null==t)throw Error("Invalid arguments passed to jsPDF.text");if(0===t.length)return g;var v="",y=!1,w="number"==typeof i.lineHeightFactor?i.lineHeightFactor:e$,x=g.internal.scaleFactor;function N(t){for(var e,r=t.concat(),n=[],i=r.length;i--;)"string"==typeof(e=r.shift())?n.push(e):Array.isArray(t)&&(1===e.length||void 0===e[1]&&void 0===e[2])?n.push(e[0]):n.push([e[0],e[1],e[2]]);return n}function A(t,e){var r;if("string"==typeof t)r=e(t)[0];else if(Array.isArray(t)){for(var n,i,a=t.concat(),o=[],s=a.length;s--;)"string"==typeof(n=a.shift())?o.push(e(n)[0]):Array.isArray(n)&&"string"==typeof n[0]&&o.push([(i=e(n[0],n[1],n[2]))[0],i[1],i[2]]);r=o}return r}var _=!1,P=!0;if("string"==typeof t)_=!0;else if(Array.isArray(t)){var k=t.concat();s=[];for(var F,I=k.length;I--;)("string"!=typeof(F=k.shift())||Array.isArray(F)&&"string"!=typeof F[0])&&(P=!1);_=P}if(!1===_)throw Error('Type of text must be string or Array. "'+t+'" is not recognized.');"string"==typeof t&&(t=t.match(/[\r?\n]/)?t.split(/\r\n|\r|\n/g):[t]);var C=tp/g.internal.scaleFactor,j=C*(w-1);switch(i.baseline){case"bottom":r-=j;break;case"top":r+=C-j;break;case"hanging":r+=C-2*j;break;case"middle":r+=C/2-j}if((l=i.maxWidth||0)>0&&("string"==typeof t?t=g.splitTextToSize(t,l):"[object Array]"===Object.prototype.toString.call(t)&&(t=t.reduce(function(t,e){return t.concat(g.splitTextToSize(e,l))},[]))),o={text:t,x:e,y:r,options:i,mutex:{pdfEscape:eb,activeFontKey:tL,fonts:tF,activeFontSize:tp}},tR.publish("preProcessText",o),t=o.text,c=(i=o.options).angle,p instanceof tW==!1&&c&&"number"==typeof c){c*=Math.PI/180,0===i.rotationDirection&&(c=-c),S===L.ADVANCED&&(c=-c);var E=Math.cos(c),M=Math.sin(c);p=new tW(E,M,-M,E,0,0)}else c&&c instanceof tW&&(p=c);S!==L.ADVANCED||p||(p=tG),void 0!==(h=i.charSpace||rn)&&(v+=O(z(h))+" Tc\n",this.setCharSpace(this.getCharSpace()||0)),void 0!==(d=i.horizontalScale)&&(v+=O(100*d)+" Tz\n"),i.lang;var q=-1,D=void 0!==i.renderingMode?i.renderingMode:i.stroke,R=g.internal.getCurrentPageInfo().pageContext;switch(D){case 0:case!1:case"fill":q=0;break;case 1:case!0:case"stroke":q=1;break;case 2:case"fillThenStroke":q=2;break;case 3:case"invisible":q=3;break;case 4:case"fillAndAddForClipping":q=4;break;case 5:case"strokeAndAddPathForClipping":q=5;break;case 6:case"fillThenStrokeAndAddToPathForClipping":q=6;break;case 7:case"addToPathForClipping":q=7}var T=void 0!==R.usedRenderingMode?R.usedRenderingMode:-1;-1!==q?v+=q+" Tr\n":-1!==T&&(v+="0 Tr\n"),-1!==q&&(R.usedRenderingMode=q),u=i.align||"left";var U,H=tp*w,W=g.internal.pageSize.getWidth(),V=tF[tL];h=i.charSpace||rn,l=i.maxWidth||0,f=Object.assign({autoencode:!0,noBOM:!0},i.flags);var G=[],Y=function(t){return g.getStringUnitWidth(t,{font:V,charSpace:h,fontSize:tp,doKerning:!1})*tp/x};if("[object Array]"===Object.prototype.toString.call(t)){s=N(t),"left"!==u&&(U=s.map(Y));var J,X,K=0;if("right"===u){e-=U[0],t=[],I=s.length;for(var Z=0;Z<I;Z++)0===Z?(X=e4(e),J=e6(r)):(X=z(K-U[Z]),J=-H),t.push([s[Z],X,J]),K=U[Z]}else if("center"===u){e-=U[0]/2,t=[],I=s.length;for(var $=0;$<I;$++)0===$?(X=e4(e),J=e6(r)):(X=z((K-U[$])/2),J=-H),t.push([s[$],X,J]),K=U[$]}else if("left"===u){t=[],I=s.length;for(var Q=0;Q<I;Q++)t.push(s[Q])}else if("justify"===u&&"Identity-H"===V.encoding){t=[],I=s.length,l=0!==l?l:W;for(var tt=0,te=0;te<I;te++)if(J=0===te?e6(r):-H,X=0===te?e4(e):tt,te<I-1){var tr=z((l-U[te])/(s[te].split(" ").length-1)),tn=s[te].split(" ");t.push([tn[0]+" ",X,J]),tt=0;for(var ti=1;ti<tn.length;ti++){var ta=(Y(tn[ti-1]+" "+tn[ti])-Y(tn[ti]))*x+tr;ti==tn.length-1?t.push([tn[ti],ta,0]):t.push([tn[ti]+" ",ta,0]),tt-=ta}}else t.push([s[te],X,J]);t.push(["",tt,0])}else{if("justify"!==u)throw Error('Unrecognized alignment option, use "left", "center", "right" or "justify".');for(t=[],I=s.length,l=0!==l?l:W,te=0;te<I;te++)J=0===te?e6(r):-H,X=0===te?e4(e):0,te<I-1?G.push(O(z((l-U[te])/(s[te].split(" ").length-1)))):G.push(0),t.push([s[te],X,J])}}!0===("boolean"==typeof i.R2L?i.R2L:tv)&&(t=A(t,function(t,e,r){return[t.split("").reverse().join(""),e,r]})),o={text:t,x:e,y:r,options:i,mutex:{pdfEscape:eb,activeFontKey:tL,fonts:tF,activeFontSize:tp}},tR.publish("postProcessText",o),t=o.text,y=o.mutex.isHex||!1;var to=tF[tL].encoding;"WinAnsiEncoding"!==to&&"StandardEncoding"!==to||(t=A(t,function(t,e,r){var n;return[eb(t.split("	").join(Array(i.TabLen||9).join(" ")),f),e,r]})),s=N(t),t=[];for(var ts,tc,tu,tl=+!!Array.isArray(s[0]),tf="",td=function(t,e,r){var n="";return r instanceof tW?(r="number"==typeof i.angle?tV(r,new tW(1,0,0,1,t,e)):tV(new tW(1,0,0,1,t,e),r),S===L.ADVANCED&&(r=tV(new tW(1,0,0,-1,0,0),r)),n=r.join(" ")+" Tm\n"):n=O(t)+" "+O(e)+" Td\n",n},tg=0;tg<s.length;tg++){switch(tf="",tl){case 1:tu=(y?"<":"(")+s[tg][0]+(y?">":")"),ts=parseFloat(s[tg][1]),tc=parseFloat(s[tg][2]);break;case 0:tu=(y?"<":"(")+s[tg]+(y?">":")"),ts=e4(e),tc=e6(r)}void 0!==G&&void 0!==G[tg]&&(tf=G[tg]+" Tw\n"),0===tg?t.push(tf+td(ts,tc,p)+tu):0===tl?t.push(tf+tu):1===tl&&t.push(tf+td(ts,tc,p)+tu)}t=(0===tl?t.join(" Tj\nT* "):t.join(" Tj\n"))+" Tj\n";var tm="BT\n/";return tm+=tL+" "+tp+" Tf\n",tm+=O(tp*w)+" TL\n",tm+=re+"\n",tm+=v,tm+=t,th(tm+="ET"),b[tL]=!0,g};var eD=y.__private__.clip=y.clip=function(t){return th("evenodd"===t?"W*":"W"),this};y.clipEvenOdd=function(){return eD("evenodd")},y.__private__.discardPath=y.discardPath=function(){return th("n"),this};var eR=y.__private__.isValidStyle=function(t){var e=!1;return -1!==[void 0,null,"S","D","F","DF","FD","f","f*","B","B*","n"].indexOf(t)&&(e=!0),e};y.__private__.setDefaultPathOperation=y.setDefaultPathOperation=function(t){return eR(t)&&(g=t),this};var eT=y.__private__.getStyle=y.getStyle=function(t){var e=g;switch(t){case"D":case"S":e="S";break;case"F":e="f";break;case"FD":case"DF":e="B";break;case"f":case"f*":case"B":case"B*":e=t}return e},ez=y.close=function(){return th("h"),this};y.stroke=function(){return th("S"),this},y.fill=function(t){return eU("f",t),this},y.fillEvenOdd=function(t){return eU("f*",t),this},y.fillStroke=function(t){return eU("B",t),this},y.fillStrokeEvenOdd=function(t){return eU("B*",t),this};var eU=function(t,e){"object"===(0,n.A)(e)?eV(e,t):th(t)},eH=function(t){null===t||S===L.ADVANCED&&void 0===t||th(t=eT(t))};function eW(t,e,r,n,i){var a=new M(e||this.boundingBox,r||this.xStep,n||this.yStep,this.gState,i||this.matrix);return a.stream=this.stream,tY(t+"$$"+this.cloneIndex+++"$$",a),a}var eV=function(t,e){var r=tO[t.key],n=tj[r];if(n instanceof E)th("q"),th(eG(e)),n.gState&&y.setGState(n.gState),th(t.matrix.toString()+" cm"),th("/"+r+" sh"),th("Q");else if(n instanceof M){var i=new tW(1,0,0,-1,0,rm());t.matrix&&(i=i.multiply(t.matrix||tG),r=eW.call(n,t.key,t.boundingBox,t.xStep,t.yStep,i).id),th("q"),th("/Pattern cs"),th("/"+r+" scn"),n.gState&&y.setGState(n.gState),th(e),th("Q")}},eG=function(t){switch(t){case"f":case"F":case"n":return"W n";case"f*":return"W* n";case"B":case"S":return"W S";case"B*":return"W* S"}},eY=y.moveTo=function(t,e){return th(O(z(t))+" "+O(U(e))+" m"),this},eJ=y.lineTo=function(t,e){return th(O(z(t))+" "+O(U(e))+" l"),this},eX=y.curveTo=function(t,e,r,n,i,a){return th([O(z(t)),O(U(e)),O(z(r)),O(U(n)),O(z(i)),O(U(a)),"c"].join(" ")),this};y.__private__.line=y.line=function(t,e,r,n,i){if(isNaN(t)||isNaN(e)||isNaN(r)||isNaN(n)||!eR(i))throw Error("Invalid arguments passed to jsPDF.line");return S===L.COMPAT?this.lines([[r-t,n-e]],t,e,[1,1],i||"S"):this.lines([[r-t,n-e]],t,e,[1,1]).stroke()},y.__private__.lines=y.lines=function(t,e,r,n,i,a){var o,s,c,u,h,l,f,d,p,g,m,v;if("number"==typeof t&&(v=r,r=e,e=t,t=v),n=n||[1,1],a=a||!1,isNaN(e)||isNaN(r)||!Array.isArray(t)||!Array.isArray(n)||!eR(i)||"boolean"!=typeof a)throw Error("Invalid arguments passed to jsPDF.lines");for(eY(e,r),o=n[0],s=n[1],u=t.length,g=e,m=r,c=0;c<u;c++)2===(h=t[c]).length?eJ(g=h[0]*o+g,m=h[1]*s+m):(l=h[0]*o+g,f=h[1]*s+m,d=h[2]*o+g,p=h[3]*s+m,eX(l,f,d,p,g=h[4]*o+g,m=h[5]*s+m));return a&&ez(),eH(i),this},y.path=function(t){for(var e=0;e<t.length;e++){var r=t[e],n=r.c;switch(r.op){case"m":eY(n[0],n[1]);break;case"l":eJ(n[0],n[1]);break;case"c":eX.apply(this,n);break;case"h":ez()}}return this},y.__private__.rect=y.rect=function(t,e,r,n,i){if(isNaN(t)||isNaN(e)||isNaN(r)||isNaN(n)||!eR(i))throw Error("Invalid arguments passed to jsPDF.rect");return S===L.COMPAT&&(n=-n),th([O(z(t)),O(U(e)),O(z(r)),O(z(n)),"re"].join(" ")),eH(i),this},y.__private__.triangle=y.triangle=function(t,e,r,n,i,a,o){if(isNaN(t)||isNaN(e)||isNaN(r)||isNaN(n)||isNaN(i)||isNaN(a)||!eR(o))throw Error("Invalid arguments passed to jsPDF.triangle");return this.lines([[r-t,n-e],[i-r,a-n],[t-i,e-a]],t,e,[1,1],o,!0),this},y.__private__.roundedRect=y.roundedRect=function(t,e,r,n,i,a,o){if(isNaN(t)||isNaN(e)||isNaN(r)||isNaN(n)||isNaN(i)||isNaN(a)||!eR(o))throw Error("Invalid arguments passed to jsPDF.roundedRect");var s=4/3*(Math.SQRT2-1);return i=Math.min(i,.5*r),a=Math.min(a,.5*n),this.lines([[r-2*i,0],[i*s,0,i,a-a*s,i,a],[0,n-2*a],[0,a*s,-i*s,a,-i,a],[2*i-r,0],[-i*s,0,-i,-a*s,-i,-a],[0,2*a-n],[0,-a*s,i*s,-a,i,-a]],t+i,e,[1,1],o,!0),this},y.__private__.ellipse=y.ellipse=function(t,e,r,n,i){if(isNaN(t)||isNaN(e)||isNaN(r)||isNaN(n)||!eR(i))throw Error("Invalid arguments passed to jsPDF.ellipse");var a=4/3*(Math.SQRT2-1)*r,o=4/3*(Math.SQRT2-1)*n;return eY(t+r,e),eX(t+r,e-o,t+a,e-n,t,e-n),eX(t-a,e-n,t-r,e-o,t-r,e),eX(t-r,e+o,t-a,e+n,t,e+n),eX(t+a,e+n,t+r,e+o,t+r,e),eH(i),this},y.__private__.circle=y.circle=function(t,e,r,n){if(isNaN(t)||isNaN(e)||isNaN(r)||!eR(n))throw Error("Invalid arguments passed to jsPDF.circle");return this.ellipse(t,e,r,r,n)},y.setFont=function(t,e,r){return r&&(e=k(e,r)),tL=eL(t,e,{disableWarning:!1}),this};var eK=y.__private__.getFont=y.getFont=function(){return tF[eL.apply(y,arguments)]};y.__private__.getFontList=y.getFontList=function(){var t,e,r={};for(t in tI)if(tI.hasOwnProperty(t))for(e in r[t]=[],tI[t])tI[t].hasOwnProperty(e)&&r[t].push(e);return r},y.addFont=function(t,e,r,n,i){var a=["StandardEncoding","MacRomanEncoding","Identity-H","WinAnsiEncoding"];return arguments[3]&&-1!==a.indexOf(arguments[3])?i=arguments[3]:arguments[3]&&-1==a.indexOf(arguments[3])&&(r=k(r,n)),i=i||"Identity-H",em.call(this,t,e,r,i)};var eZ,e$,eQ=t.lineWidth||.200025,e1=y.__private__.getLineWidth=y.getLineWidth=function(){return eQ},e2=y.__private__.setLineWidth=y.setLineWidth=function(t){return eQ=t,th(O(z(t))+" w"),this};y.__private__.setLineDash=q.API.setLineDash=q.API.setLineDashPattern=function(t,e){if(t=t||[],isNaN(e=e||0)||!Array.isArray(t))throw Error("Invalid arguments passed to jsPDF.setLineDash");return th("["+(t=t.map(function(t){return O(z(t))}).join(" "))+"] "+(e=O(z(e)))+" d"),this};var e5=y.__private__.getLineHeight=y.getLineHeight=function(){return tp*e$};y.__private__.getLineHeight=y.getLineHeight=function(){return tp*e$};var e0=y.__private__.setLineHeightFactor=y.setLineHeightFactor=function(t){return"number"==typeof(t=t||1.15)&&(e$=t),this},e3=y.__private__.getLineHeightFactor=y.getLineHeightFactor=function(){return e$};e0(t.lineHeight);var e4=y.__private__.getHorizontalCoordinate=function(t){return z(t)},e6=y.__private__.getVerticalCoordinate=function(t){return S===L.ADVANCED?t:tD[Z].mediaBox.topRightY-tD[Z].mediaBox.bottomLeftY-z(t)},e8=y.__private__.getHorizontalCoordinateString=y.getHorizontalCoordinateString=function(t){return O(e4(t))},e7=y.__private__.getVerticalCoordinateString=y.getVerticalCoordinateString=function(t){return O(e6(t))},e9=t.strokeColor||"0 G";y.__private__.getStrokeColor=y.getDrawColor=function(){return t1(e9)},y.__private__.setStrokeColor=y.setDrawColor=function(t,e,r,n){return th(e9=t2({ch1:t,ch2:e,ch3:r,ch4:n,pdfColorType:"draw",precision:2})),this};var rt=t.fillColor||"0 g";y.__private__.getFillColor=y.getFillColor=function(){return t1(rt)},y.__private__.setFillColor=y.setFillColor=function(t,e,r,n){return th(rt=t2({ch1:t,ch2:e,ch3:r,ch4:n,pdfColorType:"fill",precision:2})),this};var re=t.textColor||"0 g",rr=y.__private__.getTextColor=y.getTextColor=function(){return t1(re)};y.__private__.setTextColor=y.setTextColor=function(t,e,r,n){return re=t2({ch1:t,ch2:e,ch3:r,ch4:n,pdfColorType:"text",precision:3}),this};var rn=t.charSpace,ri=y.__private__.getCharSpace=y.getCharSpace=function(){return parseFloat(rn||0)};y.__private__.setCharSpace=y.setCharSpace=function(t){if(isNaN(t))throw Error("Invalid argument passed to jsPDF.setCharSpace");return rn=t,this};var ra=0;y.CapJoinStyles={0:0,butt:0,but:0,miter:0,1:1,round:1,rounded:1,circle:1,2:2,projecting:2,project:2,square:2,bevel:2},y.__private__.setLineCap=y.setLineCap=function(t){var e=y.CapJoinStyles[t];if(void 0===e)throw Error("Line cap style of '"+t+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return ra=e,th(e+" J"),this};var ro=0;y.__private__.setLineJoin=y.setLineJoin=function(t){var e=y.CapJoinStyles[t];if(void 0===e)throw Error("Line join style of '"+t+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return ro=e,th(e+" j"),this},y.__private__.setLineMiterLimit=y.__private__.setMiterLimit=y.setLineMiterLimit=y.setMiterLimit=function(t){if(isNaN(t=t||0))throw Error("Invalid argument passed to jsPDF.setLineMiterLimit");return th(O(z(t))+" M"),this},y.GState=j,y.setGState=function(t){(t="string"==typeof t?tE[tM[t]]:rs(null,t)).equals(tq)||(th("/"+t.id+" gs"),tq=t)};var rs=function(t,e){if(!t||!tM[t]){var r=!1;for(var n in tE)if(tE.hasOwnProperty(n)&&tE[n].equals(e)){r=!0;break}if(r)e=tE[n];else{var i="GS"+(Object.keys(tE).length+1).toString(10);tE[i]=e,e.id=i}return t&&(tM[t]=e.id),tR.publish("addGState",e),e}};y.addGState=function(t,e){return rs(t,e),this},y.saveGraphicsState=function(){return th("q"),tC.push({key:tL,size:tp,color:re}),this},y.restoreGraphicsState=function(){th("Q");var t=tC.pop();return tL=t.key,tp=t.size,re=t.color,tq=null,this},y.setCurrentTransformationMatrix=function(t){return th(t.toString()+" cm"),this},y.comment=function(t){return th("#"+t),this};var rc=function(t,e){var r=t||0;Object.defineProperty(this,"x",{enumerable:!0,get:function(){return r},set:function(t){isNaN(t)||(r=parseFloat(t))}});var n=e||0;Object.defineProperty(this,"y",{enumerable:!0,get:function(){return n},set:function(t){isNaN(t)||(n=parseFloat(t))}});var i="pt";return Object.defineProperty(this,"type",{enumerable:!0,get:function(){return i},set:function(t){i=t.toString()}}),this},ru=function(t,e,r,n){rc.call(this,t,e),this.type="rect";var i=r||0;Object.defineProperty(this,"w",{enumerable:!0,get:function(){return i},set:function(t){isNaN(t)||(i=parseFloat(t))}});var a=n||0;return Object.defineProperty(this,"h",{enumerable:!0,get:function(){return a},set:function(t){isNaN(t)||(a=parseFloat(t))}}),this},rh=function(){this.page=tB,this.currentPage=Z,this.pages=ta.slice(0),this.pagesContext=tD.slice(0),this.x=t_,this.y=tP,this.matrix=tk,this.width=rp(Z),this.height=rm(Z),this.outputDestination=ts,this.id="",this.objectNumber=-1};rh.prototype.restore=function(){tB=this.page,Z=this.currentPage,tD=this.pagesContext,ta=this.pages,t_=this.x,tP=this.y,tk=this.matrix,rg(Z,this.width),rv(Z,this.height),ts=this.outputDestination};var rl=function(t,e,r,n,i){tH.push(new rh),tB=Z=0,ta=[],t_=t,tP=e,tk=i,ey([r,n])},rf=function(t){if(tU[t])tH.pop().restore();else{var e=new rh,r="Xo"+(Object.keys(tz).length+1).toString(10);e.id=r,tU[t]=r,tz[r]=e,tR.publish("addFormObject",e),tH.pop().restore()}};for(var rd in y.beginFormObject=function(t,e,r,n,i){return rl(t,e,r,n,i),this},y.endFormObject=function(t){return rf(t),this},y.doFormObject=function(t,e){var r=tz[tU[t]];return th("q"),th(e.toString()+" cm"),th("/"+r.id+" Do"),th("Q"),this},y.getFormObject=function(t){var e=tz[tU[t]];return{x:e.x,y:e.y,width:e.width,height:e.height,matrix:e.matrix}},y.save=function(t,e){return t=t||"generated.pdf",(e=e||{}).returnPromise=e.returnPromise||!1,!1===e.returnPromise?(d(eC(eI()),t),"function"==typeof d.unload&&a.setTimeout&&setTimeout(d.unload,911),this):new Promise(function(e,r){try{var n=d(eC(eI()),t);"function"==typeof d.unload&&a.setTimeout&&setTimeout(d.unload,911),e(n)}catch(t){r(t.message)}})},q.API)q.API.hasOwnProperty(rd)&&("events"===rd&&q.API.events.length?function(t,e){var r,n,i;for(i=e.length-1;-1!==i;i--)r=e[i][0],n=e[i][1],t.subscribe.apply(t,[r].concat("function"==typeof n?[n]:n))}(tR,q.API.events):y[rd]=q.API[rd]);var rp=y.getPageWidth=function(t){return(tD[t=t||Z].mediaBox.topRightX-tD[t].mediaBox.bottomLeftX)/tS},rg=y.setPageWidth=function(t,e){tD[t].mediaBox.topRightX=e*tS+tD[t].mediaBox.bottomLeftX},rm=y.getPageHeight=function(t){return(tD[t=t||Z].mediaBox.topRightY-tD[t].mediaBox.bottomLeftY)/tS},rv=y.setPageHeight=function(t,e){tD[t].mediaBox.topRightY=e*tS+tD[t].mediaBox.bottomLeftY};return y.internal={pdfEscape:eb,getStyle:eT,getFont:eK,getFontSize:tm,getCharSpace:ri,getTextColor:rr,getLineHeight:e5,getLineHeightFactor:e3,getLineWidth:e1,write:tl,getHorizontalCoordinate:e4,getVerticalCoordinate:e6,getCoordinateString:e8,getVerticalCoordinateString:e7,collections:{},newObject:tJ,newAdditionalObject:tZ,newObjectDeferred:tX,newObjectDeferredBegin:tK,getFilters:t5,putStream:t0,events:tR,scaleFactor:tS,pageSize:{getWidth:function(){return rp(Z)},setWidth:function(t){rg(Z,t)},getHeight:function(){return rm(Z)},setHeight:function(t){rv(Z,t)}},encryptionOptions:m,encryption:eE,getEncryptor:function(t){return null!==m?eE.encryptor(t,0):function(t){return t}},output:ej,getNumberOfPages:eA,pages:ta,out:th,f2:R,f3:T,getPageInfo:eM,getPageInfoByObjId:eq,getCurrentPageInfo:eB,getPDFVersion:x,Point:rc,Rectangle:ru,Matrix:tW,hasHotfix:eO},Object.defineProperty(y.internal.pageSize,"width",{get:function(){return rp(Z)},set:function(t){rg(Z,t)},enumerable:!0,configurable:!0}),Object.defineProperty(y.internal.pageSize,"height",{get:function(){return rm(Z)},set:function(t){rv(Z,t)},enumerable:!0,configurable:!0}),(function(t){for(var e=0,r=td.length;e<r;e++){var n=em.call(this,t[e][0],t[e][1],t[e][2],td[e][3],!0);!1===v&&(b[n]=!0);var i=t[e][0].split("-");eg({id:n,fontName:i[0],fontStyle:i[1]||""})}tR.publish("addFonts",{fonts:tF,dictionary:tI})}).call(y,td),tL="F1",ew(o,r),tR.publish("initialized"),y}F.prototype.lsbFirstWord=function(t){return String.fromCharCode((0|t)&255,t>>8&255,t>>16&255,t>>24&255)},F.prototype.toHexString=function(t){return t.split("").map(function(t){return("0"+(255&t.charCodeAt(0)).toString(16)).slice(-2)}).join("")},F.prototype.hexToBytes=function(t){for(var e=[],r=0;r<t.length;r+=2)e.push(String.fromCharCode(parseInt(t.substr(r,2),16)));return e.join("")},F.prototype.processOwnerPassword=function(t,e){return P(L(e).substr(0,5),t)},F.prototype.encryptor=function(t,e){var r=L(this.encryptionKey+String.fromCharCode(255&t,t>>8&255,t>>16&255,255&e,e>>8&255)).substr(0,10);return function(t){return P(r,t)}},j.prototype.equals=function(t){var e,r="id,objectNumber,equals";if(!t||(0,n.A)(t)!==(0,n.A)(this))return!1;var i=0;for(e in this)if(!(r.indexOf(e)>=0)){if(this.hasOwnProperty(e)&&!t.hasOwnProperty(e)||this[e]!==t[e])return!1;i++}for(e in t)t.hasOwnProperty(e)&&0>r.indexOf(e)&&i--;return 0===i},q.API={events:[]},q.version="3.0.1";var B=q.API,D=1,R=function(t){return t.replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},T=function(t){return t.replace(/\\\\/g,"\\").replace(/\\\(/g,"(").replace(/\\\)/g,")")},z=function(t){return t.toFixed(2)},U=function(t){return t.toFixed(5)};B.__acroform__={};var H=function(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t},W=function(t){return t*D},V=function(t){var e=new tu,r=tA.internal.getHeight(t)||0;return e.BBox=[0,0,Number(z(tA.internal.getWidth(t)||0)),Number(z(r))],e},G=B.__acroform__.setBit=function(t,e){if(e=e||0,isNaN(t=t||0)||isNaN(e))throw Error("Invalid arguments passed to jsPDF.API.__acroform__.setBit");return t|1<<e},Y=B.__acroform__.clearBit=function(t,e){if(e=e||0,isNaN(t=t||0)||isNaN(e))throw Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBit");return t&~(1<<e)},J=B.__acroform__.getBit=function(t,e){if(isNaN(t)||isNaN(e))throw Error("Invalid arguments passed to jsPDF.API.__acroform__.getBit");return+(0!=(t&1<<e))},X=B.__acroform__.getBitForPdf=function(t,e){if(isNaN(t)||isNaN(e))throw Error("Invalid arguments passed to jsPDF.API.__acroform__.getBitForPdf");return J(t,e-1)},K=B.__acroform__.setBitForPdf=function(t,e){if(isNaN(t)||isNaN(e))throw Error("Invalid arguments passed to jsPDF.API.__acroform__.setBitForPdf");return G(t,e-1)},Z=B.__acroform__.clearBitForPdf=function(t,e){if(isNaN(t)||isNaN(e))throw Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBitForPdf");return Y(t,e-1)},$=B.__acroform__.calculateCoordinates=function(t,e){var r=e.internal.getHorizontalCoordinate,n=e.internal.getVerticalCoordinate,i=t[0],a=t[1],o=t[2],s=t[3],c={};return c.lowerLeft_X=r(i)||0,c.lowerLeft_Y=n(a+s)||0,c.upperRight_X=r(i+o)||0,c.upperRight_Y=n(a)||0,[Number(z(c.lowerLeft_X)),Number(z(c.lowerLeft_Y)),Number(z(c.upperRight_X)),Number(z(c.upperRight_Y))]},Q=function(t){if(t.appearanceStreamContent)return t.appearanceStreamContent;if(t.V||t.DV){var e=[],r=t._V||t.DV,n=tt(t,r),i=t.scope.internal.getFont(t.fontName,t.fontStyle).id;e.push("/Tx BMC"),e.push("q"),e.push("BT"),e.push(t.scope.__private__.encodeColorString(t.color)),e.push("/"+i+" "+z(n.fontSize)+" Tf"),e.push("1 0 0 1 0 0 Tm"),e.push(n.text),e.push("ET"),e.push("Q"),e.push("EMC");var a=V(t);return a.scope=t.scope,a.stream=e.join("\n"),a}},tt=function(t,e){var r=0===t.fontSize?t.maxFontSize:t.fontSize,n={text:"",fontSize:""},i=(e=")"==(e="("==e.substr(0,1)?e.substr(1):e).substr(e.length-1)?e.substr(0,e.length-1):e).split(" ");i=t.multiline?i.map(function(t){return t.split("\n")}):i.map(function(t){return[t]});var a=r,o=tA.internal.getHeight(t)||0;o=o<0?-o:o;var s=tA.internal.getWidth(t)||0;s=s<0?-s:s,a++;t:for(;a>0;){e="";var c,u,h=te("3",t,--a).height,l=t.multiline?o-a:(o-h)/2,f=l+=2,d=0,p=0,g=0;if(a<=0){e="(...) Tj\n",e+="% Width of Text: "+te(e,t,a=12).width+", FieldWidth:"+s+"\n";break}for(var m="",v=0,b=0;b<i.length;b++)if(i.hasOwnProperty(b)){var y=!1;if(1!==i[b].length&&g!==i[b].length-1){if((h+2)*(v+2)+2>o)continue t;m+=i[b][g],y=!0,p=b,b--}else{m=" "==(m+=i[b][g]+" ").substr(m.length-1)?m.substr(0,m.length-1):m;var w,x,N=parseInt(b),A=(w=m,x=a,N+1<i.length&&te(w+" "+i[N+1][0],t,x).width<=s-4),L=b>=i.length-1;if(A&&!L){m+=" ",g=0;continue}if(A||L){if(L)p=N;else if(t.multiline&&(h+2)*(v+2)+2>o)continue t}else{if(!t.multiline||(h+2)*(v+2)+2>o)continue t;p=N}}for(var S="",_=d;_<=p;_++){var P=i[_];if(t.multiline){if(_===p){S+=P[g]+" ",g=(g+1)%P.length;continue}if(_===d){S+=P[P.length-1]+" ";continue}}S+=P[0]+" "}switch(u=te(S=" "==S.substr(S.length-1)?S.substr(0,S.length-1):S,t,a).width,t.textAlign){case"right":c=s-u-2;break;case"center":c=(s-u)/2;break;default:c=2}e+=z(c)+" "+z(f)+" Td\n",e+="("+R(S)+") Tj\n",e+=-z(c)+" 0 Td\n",f=-(a+2),u=0,d=y?p:p+1,v++,m=""}break}return n.text=e,n.fontSize=a,n},te=function(t,e,r){var n=e.scope.internal.getFont(e.fontName,e.fontStyle),i=e.scope.getStringUnitWidth(t,{font:n,fontSize:parseFloat(r),charSpace:0})*parseFloat(r);return{height:e.scope.getStringUnitWidth("3",{font:n,fontSize:parseFloat(r),charSpace:0})*parseFloat(r)*1.5,width:i}},tr={fields:[],xForms:[],acroFormDictionaryRoot:null,printedOut:!1,internal:null,isInitialized:!1},tn=function(t,e){var r={type:"reference",object:t};void 0===e.internal.getPageInfo(t.page).pageContext.annotations.find(function(t){return t.type===r.type&&t.object===r.object})&&e.internal.getPageInfo(t.page).pageContext.annotations.push(r)},ti=function(t,e){for(var r in t)if(t.hasOwnProperty(r)){var i=t[r];e.internal.newObjectDeferredBegin(i.objId,!0),"object"===(0,n.A)(i)&&"function"==typeof i.putStream&&i.putStream(),delete t[r]}},ta=function(t,e){if(e.scope=t,void 0!==t.internal&&(void 0===t.internal.acroformPlugin||!1===t.internal.acroformPlugin.isInitialized)){if(tl.FieldNum=0,t.internal.acroformPlugin=JSON.parse(JSON.stringify(tr)),t.internal.acroformPlugin.acroFormDictionaryRoot)throw Error("Exception while creating AcroformDictionary");D=t.internal.scaleFactor,t.internal.acroformPlugin.acroFormDictionaryRoot=new th,t.internal.acroformPlugin.acroFormDictionaryRoot.scope=t,t.internal.acroformPlugin.acroFormDictionaryRoot._eventID=t.internal.events.subscribe("postPutResources",function(){t.internal.events.unsubscribe(t.internal.acroformPlugin.acroFormDictionaryRoot._eventID),delete t.internal.acroformPlugin.acroFormDictionaryRoot._eventID,t.internal.acroformPlugin.printedOut=!0}),t.internal.events.subscribe("buildDocument",function(){t.internal.acroformPlugin.acroFormDictionaryRoot.objId=void 0;var e=t.internal.acroformPlugin.acroFormDictionaryRoot.Fields;for(var r in e)if(e.hasOwnProperty(r)){var n=e[r];n.objId=void 0,n.hasAnnotation&&tn(n,t)}}),t.internal.events.subscribe("putCatalog",function(){if(void 0===t.internal.acroformPlugin.acroFormDictionaryRoot)throw Error("putCatalogCallback: Root missing.");t.internal.write("/AcroForm "+t.internal.acroformPlugin.acroFormDictionaryRoot.objId+" 0 R")}),t.internal.events.subscribe("postPutPages",function(e){!function(t,e){var r=!t;for(var i in t||(e.internal.newObjectDeferredBegin(e.internal.acroformPlugin.acroFormDictionaryRoot.objId,!0),e.internal.acroformPlugin.acroFormDictionaryRoot.putStream()),t=t||e.internal.acroformPlugin.acroFormDictionaryRoot.Kids)if(t.hasOwnProperty(i)){var a=t[i],o=[],s=a.Rect;if(a.Rect&&(a.Rect=$(a.Rect,e)),e.internal.newObjectDeferredBegin(a.objId,!0),a.DA=tA.createDefaultAppearanceStream(a),"object"===(0,n.A)(a)&&"function"==typeof a.getKeyValueListForStream&&(o=a.getKeyValueListForStream()),a.Rect=s,a.hasAppearanceStream&&!a.appearanceStreamContent){var c=Q(a);o.push({key:"AP",value:"<</N "+c+">>"}),e.internal.acroformPlugin.xForms.push(c)}if(a.appearanceStreamContent){var u="";for(var h in a.appearanceStreamContent)if(a.appearanceStreamContent.hasOwnProperty(h)){var l=a.appearanceStreamContent[h];if(u+="/"+h+" <<",Object.keys(l).length>=1||Array.isArray(l)){for(var i in l)if(l.hasOwnProperty(i)){var f=l[i];"function"==typeof f&&(f=f.call(e,a)),u+="/"+i+" "+f+" ",e.internal.acroformPlugin.xForms.indexOf(f)>=0||e.internal.acroformPlugin.xForms.push(f)}}else"function"==typeof(f=l)&&(f=f.call(e,a)),u+="/"+i+" "+f,e.internal.acroformPlugin.xForms.indexOf(f)>=0||e.internal.acroformPlugin.xForms.push(f);u+=">>"}o.push({key:"AP",value:"<<\n"+u+">>"})}e.internal.putStream({additionalKeyValues:o,objectId:a.objId}),e.internal.out("endobj")}r&&ti(e.internal.acroformPlugin.xForms,e)}(e,t)}),t.internal.acroformPlugin.isInitialized=!0}},to=B.__acroform__.arrayToPdfArray=function(t,e,r){var i=function(t){return t};if(Array.isArray(t)){for(var a="[",o=0;o<t.length;o++)switch(0!==o&&(a+=" "),(0,n.A)(t[o])){case"boolean":case"number":case"object":a+=t[o].toString();break;case"string":"/"!==t[o].substr(0,1)?(void 0!==e&&r&&(i=r.internal.getEncryptor(e)),a+="("+R(i(t[o].toString()))+")"):a+=t[o].toString()}return a+"]"}throw Error("Invalid argument passed to jsPDF.__acroform__.arrayToPdfArray")},ts=function(t,e,r){var n=function(t){return t};return void 0!==e&&r&&(n=r.internal.getEncryptor(e)),(t=t||"").toString(),t="("+R(n(t))+")"},tc=function(){this._objId=void 0,this._scope=void 0,Object.defineProperty(this,"objId",{get:function(){if(void 0===this._objId){if(void 0===this.scope)return;this._objId=this.scope.internal.newObjectDeferred()}return this._objId},set:function(t){this._objId=t}}),Object.defineProperty(this,"scope",{value:this._scope,writable:!0})};tc.prototype.toString=function(){return this.objId+" 0 R"},tc.prototype.putStream=function(){var t=this.getKeyValueListForStream();this.scope.internal.putStream({data:this.stream,additionalKeyValues:t,objectId:this.objId}),this.scope.internal.out("endobj")},tc.prototype.getKeyValueListForStream=function(){var t=[],e=Object.getOwnPropertyNames(this).filter(function(t){return"content"!=t&&"appearanceStreamContent"!=t&&"scope"!=t&&"objId"!=t&&"_"!=t.substring(0,1)});for(var r in e)if(!1===Object.getOwnPropertyDescriptor(this,e[r]).configurable){var n=e[r],i=this[n];i&&(Array.isArray(i)?t.push({key:n,value:to(i,this.objId,this.scope)}):i instanceof tc?(i.scope=this.scope,t.push({key:n,value:i.objId+" 0 R"})):"function"!=typeof i&&t.push({key:n,value:i}))}return t};var tu=function(){tc.call(this),Object.defineProperty(this,"Type",{value:"/XObject",configurable:!1,writable:!0}),Object.defineProperty(this,"Subtype",{value:"/Form",configurable:!1,writable:!0}),Object.defineProperty(this,"FormType",{value:1,configurable:!1,writable:!0});var t,e=[];Object.defineProperty(this,"BBox",{configurable:!1,get:function(){return e},set:function(t){e=t}}),Object.defineProperty(this,"Resources",{value:"2 0 R",configurable:!1,writable:!0}),Object.defineProperty(this,"stream",{enumerable:!1,configurable:!0,set:function(e){t=e.trim()},get:function(){return t||null}})};H(tu,tc);var th=function(){tc.call(this);var t,e=[];Object.defineProperty(this,"Kids",{enumerable:!1,configurable:!0,get:function(){return e.length>0?e:void 0}}),Object.defineProperty(this,"Fields",{enumerable:!1,configurable:!1,get:function(){return e}}),Object.defineProperty(this,"DA",{enumerable:!1,configurable:!1,get:function(){if(t){var e=function(t){return t};return this.scope&&(e=this.scope.internal.getEncryptor(this.objId)),"("+R(e(t))+")"}},set:function(e){t=e}})};H(th,tc);var tl=function t(){tc.call(this);var e=4;Object.defineProperty(this,"F",{enumerable:!1,configurable:!1,get:function(){return e},set:function(t){if(isNaN(t))throw Error('Invalid value "'+t+'" for attribute F supplied.');e=t}}),Object.defineProperty(this,"showWhenPrinted",{enumerable:!0,configurable:!0,get:function(){return!!X(e,3)},set:function(t){!0==!!t?this.F=K(e,3):this.F=Z(e,3)}});var r=0;Object.defineProperty(this,"Ff",{enumerable:!1,configurable:!1,get:function(){return r},set:function(t){if(isNaN(t))throw Error('Invalid value "'+t+'" for attribute Ff supplied.');r=t}});var n=[];Object.defineProperty(this,"Rect",{enumerable:!1,configurable:!1,get:function(){if(0!==n.length)return n},set:function(t){n=void 0!==t?t:[]}}),Object.defineProperty(this,"x",{enumerable:!0,configurable:!0,get:function(){return!n||isNaN(n[0])?0:n[0]},set:function(t){n[0]=t}}),Object.defineProperty(this,"y",{enumerable:!0,configurable:!0,get:function(){return!n||isNaN(n[1])?0:n[1]},set:function(t){n[1]=t}}),Object.defineProperty(this,"width",{enumerable:!0,configurable:!0,get:function(){return!n||isNaN(n[2])?0:n[2]},set:function(t){n[2]=t}}),Object.defineProperty(this,"height",{enumerable:!0,configurable:!0,get:function(){return!n||isNaN(n[3])?0:n[3]},set:function(t){n[3]=t}});var i="";Object.defineProperty(this,"FT",{enumerable:!0,configurable:!1,get:function(){return i},set:function(t){switch(t){case"/Btn":case"/Tx":case"/Ch":case"/Sig":i=t;break;default:throw Error('Invalid value "'+t+'" for attribute FT supplied.')}}});var a=null;Object.defineProperty(this,"T",{enumerable:!0,configurable:!1,get:function(){if(!a||a.length<1){if(this instanceof ty)return;a="FieldObject"+t.FieldNum++}var e=function(t){return t};return this.scope&&(e=this.scope.internal.getEncryptor(this.objId)),"("+R(e(a))+")"},set:function(t){a=t.toString()}}),Object.defineProperty(this,"fieldName",{configurable:!0,enumerable:!0,get:function(){return a},set:function(t){a=t}});var o="helvetica";Object.defineProperty(this,"fontName",{enumerable:!0,configurable:!0,get:function(){return o},set:function(t){o=t}});var s="normal";Object.defineProperty(this,"fontStyle",{enumerable:!0,configurable:!0,get:function(){return s},set:function(t){s=t}});var c=0;Object.defineProperty(this,"fontSize",{enumerable:!0,configurable:!0,get:function(){return c},set:function(t){c=t}});var u=void 0;Object.defineProperty(this,"maxFontSize",{enumerable:!0,configurable:!0,get:function(){return void 0===u?50/D:u},set:function(t){u=t}});var h="black";Object.defineProperty(this,"color",{enumerable:!0,configurable:!0,get:function(){return h},set:function(t){h=t}});var l="/F1 0 Tf 0 g";Object.defineProperty(this,"DA",{enumerable:!0,configurable:!1,get:function(){if(!(!l||this instanceof ty||this instanceof tx))return ts(l,this.objId,this.scope)},set:function(t){l=t=t.toString()}});var f=null;Object.defineProperty(this,"DV",{enumerable:!1,configurable:!1,get:function(){if(f)return this instanceof tm==!1?ts(f,this.objId,this.scope):f},set:function(t){t=t.toString(),f=this instanceof tm==!1?"("===t.substr(0,1)?T(t.substr(1,t.length-2)):T(t):t}}),Object.defineProperty(this,"defaultValue",{enumerable:!0,configurable:!0,get:function(){return this instanceof tm==!0?T(f.substr(1,f.length-1)):f},set:function(t){t=t.toString(),f=this instanceof tm==!0?"/"+t:t}});var d=null;Object.defineProperty(this,"_V",{enumerable:!1,configurable:!1,get:function(){if(d)return d},set:function(t){this.V=t}}),Object.defineProperty(this,"V",{enumerable:!1,configurable:!1,get:function(){if(d)return this instanceof tm==!1?ts(d,this.objId,this.scope):d},set:function(t){t=t.toString(),d=this instanceof tm==!1?"("===t.substr(0,1)?T(t.substr(1,t.length-2)):T(t):t}}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,get:function(){return this instanceof tm==!0?T(d.substr(1,d.length-1)):d},set:function(t){t=t.toString(),d=this instanceof tm==!0?"/"+t:t}}),Object.defineProperty(this,"hasAnnotation",{enumerable:!0,configurable:!0,get:function(){return this.Rect}}),Object.defineProperty(this,"Type",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Annot":null}}),Object.defineProperty(this,"Subtype",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Widget":null}});var p,g=!1;Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return g},set:function(t){g=t=!!t}}),Object.defineProperty(this,"page",{enumerable:!0,configurable:!0,get:function(){if(p)return p},set:function(t){p=t}}),Object.defineProperty(this,"readOnly",{enumerable:!0,configurable:!0,get:function(){return!!X(this.Ff,1)},set:function(t){!0==!!t?this.Ff=K(this.Ff,1):this.Ff=Z(this.Ff,1)}}),Object.defineProperty(this,"required",{enumerable:!0,configurable:!0,get:function(){return!!X(this.Ff,2)},set:function(t){!0==!!t?this.Ff=K(this.Ff,2):this.Ff=Z(this.Ff,2)}}),Object.defineProperty(this,"noExport",{enumerable:!0,configurable:!0,get:function(){return!!X(this.Ff,3)},set:function(t){!0==!!t?this.Ff=K(this.Ff,3):this.Ff=Z(this.Ff,3)}});var m=null;Object.defineProperty(this,"Q",{enumerable:!0,configurable:!1,get:function(){if(null!==m)return m},set:function(t){if(-1===[0,1,2].indexOf(t))throw Error('Invalid value "'+t+'" for attribute Q supplied.');m=t}}),Object.defineProperty(this,"textAlign",{get:function(){var t;switch(m){case 0:default:t="left";break;case 1:t="center";break;case 2:t="right"}return t},configurable:!0,enumerable:!0,set:function(t){switch(t){case"right":case 2:m=2;break;case"center":case 1:m=1;break;default:m=0}}})};H(tl,tc);var tf=function(){tl.call(this),this.FT="/Ch",this.V="()",this.fontName="zapfdingbats";var t=0;Object.defineProperty(this,"TI",{enumerable:!0,configurable:!1,get:function(){return t},set:function(e){t=e}}),Object.defineProperty(this,"topIndex",{enumerable:!0,configurable:!0,get:function(){return t},set:function(e){t=e}});var e=[];Object.defineProperty(this,"Opt",{enumerable:!0,configurable:!1,get:function(){return to(e,this.objId,this.scope)},set:function(t){var r;r=[],"string"==typeof t&&(r=function(t,e,r){r||(r=1);for(var n,i=[];n=e.exec(t);)i.push(n[r]);return i}(t,/\((.*?)\)/g)),e=r}}),this.getOptions=function(){return e},this.setOptions=function(t){e=t,this.sort&&e.sort()},this.addOption=function(t){t=(t=t||"").toString(),e.push(t),this.sort&&e.sort()},this.removeOption=function(t,r){for(r=r||!1,t=(t=t||"").toString();-1!==e.indexOf(t)&&(e.splice(e.indexOf(t),1),!1!==r););},Object.defineProperty(this,"combo",{enumerable:!0,configurable:!0,get:function(){return!!X(this.Ff,18)},set:function(t){!0==!!t?this.Ff=K(this.Ff,18):this.Ff=Z(this.Ff,18)}}),Object.defineProperty(this,"edit",{enumerable:!0,configurable:!0,get:function(){return!!X(this.Ff,19)},set:function(t){!0===this.combo&&(!0==!!t?this.Ff=K(this.Ff,19):this.Ff=Z(this.Ff,19))}}),Object.defineProperty(this,"sort",{enumerable:!0,configurable:!0,get:function(){return!!X(this.Ff,20)},set:function(t){!0==!!t?(this.Ff=K(this.Ff,20),e.sort()):this.Ff=Z(this.Ff,20)}}),Object.defineProperty(this,"multiSelect",{enumerable:!0,configurable:!0,get:function(){return!!X(this.Ff,22)},set:function(t){!0==!!t?this.Ff=K(this.Ff,22):this.Ff=Z(this.Ff,22)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!X(this.Ff,23)},set:function(t){!0==!!t?this.Ff=K(this.Ff,23):this.Ff=Z(this.Ff,23)}}),Object.defineProperty(this,"commitOnSelChange",{enumerable:!0,configurable:!0,get:function(){return!!X(this.Ff,27)},set:function(t){!0==!!t?this.Ff=K(this.Ff,27):this.Ff=Z(this.Ff,27)}}),this.hasAppearanceStream=!1};H(tf,tl);var td=function(){tf.call(this),this.fontName="helvetica",this.combo=!1};H(td,tf);var tp=function(){td.call(this),this.combo=!0};H(tp,td);var tg=function(){tp.call(this),this.edit=!0};H(tg,tp);var tm=function(){tl.call(this),this.FT="/Btn",Object.defineProperty(this,"noToggleToOff",{enumerable:!0,configurable:!0,get:function(){return!!X(this.Ff,15)},set:function(t){!0==!!t?this.Ff=K(this.Ff,15):this.Ff=Z(this.Ff,15)}}),Object.defineProperty(this,"radio",{enumerable:!0,configurable:!0,get:function(){return!!X(this.Ff,16)},set:function(t){!0==!!t?this.Ff=K(this.Ff,16):this.Ff=Z(this.Ff,16)}}),Object.defineProperty(this,"pushButton",{enumerable:!0,configurable:!0,get:function(){return!!X(this.Ff,17)},set:function(t){!0==!!t?this.Ff=K(this.Ff,17):this.Ff=Z(this.Ff,17)}}),Object.defineProperty(this,"radioIsUnison",{enumerable:!0,configurable:!0,get:function(){return!!X(this.Ff,26)},set:function(t){!0==!!t?this.Ff=K(this.Ff,26):this.Ff=Z(this.Ff,26)}});var t,e={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var t=function(t){return t};if(this.scope&&(t=this.scope.internal.getEncryptor(this.objId)),0!==Object.keys(e).length){var r,n=[];for(r in n.push("<<"),e)n.push("/"+r+" ("+R(t(e[r]))+")");return n.push(">>"),n.join("\n")}},set:function(t){"object"===(0,n.A)(t)&&(e=t)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return e.CA||""},set:function(t){"string"==typeof t&&(e.CA=t)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return t},set:function(e){t=e}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return t.substr(1,t.length-1)},set:function(e){t="/"+e}})};H(tm,tl);var tv=function(){tm.call(this),this.pushButton=!0};H(tv,tm);var tb=function(){tm.call(this),this.radio=!0,this.pushButton=!1;var t=[];Object.defineProperty(this,"Kids",{enumerable:!0,configurable:!1,get:function(){return t},set:function(e){t=void 0!==e?e:[]}})};H(tb,tm);var ty=function(){tl.call(this),Object.defineProperty(this,"Parent",{enumerable:!1,configurable:!1,get:function(){return t},set:function(e){t=e}}),Object.defineProperty(this,"optionName",{enumerable:!1,configurable:!0,get:function(){return e},set:function(t){e=t}});var t,e,r,i={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var t=function(t){return t};this.scope&&(t=this.scope.internal.getEncryptor(this.objId));var e,r=[];for(e in r.push("<<"),i)r.push("/"+e+" ("+R(t(i[e]))+")");return r.push(">>"),r.join("\n")},set:function(t){"object"===(0,n.A)(t)&&(i=t)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return i.CA||""},set:function(t){"string"==typeof t&&(i.CA=t)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return r},set:function(t){r=t}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return r.substr(1,r.length-1)},set:function(t){r="/"+t}}),this.caption="l",this.appearanceState="Off",this._AppearanceType=tA.RadioButton.Circle,this.appearanceStreamContent=this._AppearanceType.createAppearanceStream(this.optionName)};H(ty,tl),tb.prototype.setAppearance=function(t){if(!("createAppearanceStream"in t)||!("getCA"in t))throw Error("Couldn't assign Appearance to RadioButton. Appearance was Invalid!");for(var e in this.Kids)if(this.Kids.hasOwnProperty(e)){var r=this.Kids[e];r.appearanceStreamContent=t.createAppearanceStream(r.optionName),r.caption=t.getCA()}},tb.prototype.createOption=function(t){var e=new ty;return e.Parent=this,e.optionName=t,this.Kids.push(e),tL.call(this.scope,e),e};var tw=function(){tm.call(this),this.fontName="zapfdingbats",this.caption="3",this.appearanceState="On",this.value="On",this.textAlign="center",this.appearanceStreamContent=tA.CheckBox.createAppearanceStream()};H(tw,tm);var tx=function(){tl.call(this),this.FT="/Tx",Object.defineProperty(this,"multiline",{enumerable:!0,configurable:!0,get:function(){return!!X(this.Ff,13)},set:function(t){!0==!!t?this.Ff=K(this.Ff,13):this.Ff=Z(this.Ff,13)}}),Object.defineProperty(this,"fileSelect",{enumerable:!0,configurable:!0,get:function(){return!!X(this.Ff,21)},set:function(t){!0==!!t?this.Ff=K(this.Ff,21):this.Ff=Z(this.Ff,21)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!X(this.Ff,23)},set:function(t){!0==!!t?this.Ff=K(this.Ff,23):this.Ff=Z(this.Ff,23)}}),Object.defineProperty(this,"doNotScroll",{enumerable:!0,configurable:!0,get:function(){return!!X(this.Ff,24)},set:function(t){!0==!!t?this.Ff=K(this.Ff,24):this.Ff=Z(this.Ff,24)}}),Object.defineProperty(this,"comb",{enumerable:!0,configurable:!0,get:function(){return!!X(this.Ff,25)},set:function(t){!0==!!t?this.Ff=K(this.Ff,25):this.Ff=Z(this.Ff,25)}}),Object.defineProperty(this,"richText",{enumerable:!0,configurable:!0,get:function(){return!!X(this.Ff,26)},set:function(t){!0==!!t?this.Ff=K(this.Ff,26):this.Ff=Z(this.Ff,26)}});var t=null;Object.defineProperty(this,"MaxLen",{enumerable:!0,configurable:!1,get:function(){return t},set:function(e){t=e}}),Object.defineProperty(this,"maxLength",{enumerable:!0,configurable:!0,get:function(){return t},set:function(e){Number.isInteger(e)&&(t=e)}}),Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return this.V||this.DV}})};H(tx,tl);var tN=function(){tx.call(this),Object.defineProperty(this,"password",{enumerable:!0,configurable:!0,get:function(){return!!X(this.Ff,14)},set:function(t){!0==!!t?this.Ff=K(this.Ff,14):this.Ff=Z(this.Ff,14)}}),this.password=!0};H(tN,tx);var tA={CheckBox:{createAppearanceStream:function(){return{N:{On:tA.CheckBox.YesNormal},D:{On:tA.CheckBox.YesPushDown,Off:tA.CheckBox.OffPushDown}}},YesPushDown:function(t){var e=V(t);e.scope=t.scope;var r=[],n=t.scope.internal.getFont(t.fontName,t.fontStyle).id,i=t.scope.__private__.encodeColorString(t.color),a=tt(t,t.caption);return r.push("0.749023 g"),r.push("0 0 "+z(tA.internal.getWidth(t))+" "+z(tA.internal.getHeight(t))+" re"),r.push("f"),r.push("BMC"),r.push("q"),r.push("0 0 1 rg"),r.push("/"+n+" "+z(a.fontSize)+" Tf "+i),r.push("BT"),r.push(a.text),r.push("ET"),r.push("Q"),r.push("EMC"),e.stream=r.join("\n"),e},YesNormal:function(t){var e=V(t);e.scope=t.scope;var r=t.scope.internal.getFont(t.fontName,t.fontStyle).id,n=t.scope.__private__.encodeColorString(t.color),i=[],a=tA.internal.getHeight(t),o=tA.internal.getWidth(t),s=tt(t,t.caption);return i.push("1 g"),i.push("0 0 "+z(o)+" "+z(a)+" re"),i.push("f"),i.push("q"),i.push("0 0 1 rg"),i.push("0 0 "+z(o-1)+" "+z(a-1)+" re"),i.push("W"),i.push("n"),i.push("0 g"),i.push("BT"),i.push("/"+r+" "+z(s.fontSize)+" Tf "+n),i.push(s.text),i.push("ET"),i.push("Q"),e.stream=i.join("\n"),e},OffPushDown:function(t){var e=V(t);e.scope=t.scope;var r=[];return r.push("0.749023 g"),r.push("0 0 "+z(tA.internal.getWidth(t))+" "+z(tA.internal.getHeight(t))+" re"),r.push("f"),e.stream=r.join("\n"),e}},RadioButton:{Circle:{createAppearanceStream:function(t){var e={D:{Off:tA.RadioButton.Circle.OffPushDown},N:{}};return e.N[t]=tA.RadioButton.Circle.YesNormal,e.D[t]=tA.RadioButton.Circle.YesPushDown,e},getCA:function(){return"l"},YesNormal:function(t){var e=V(t);e.scope=t.scope;var r=[],n=tA.internal.getWidth(t)<=tA.internal.getHeight(t)?tA.internal.getWidth(t)/4:tA.internal.getHeight(t)/4,i=Number(((n=Number((.9*n).toFixed(5)))*tA.internal.Bezier_C).toFixed(5));return r.push("q"),r.push("1 0 0 1 "+U(tA.internal.getWidth(t)/2)+" "+U(tA.internal.getHeight(t)/2)+" cm"),r.push(n+" 0 m"),r.push(n+" "+i+" "+i+" "+n+" 0 "+n+" c"),r.push("-"+i+" "+n+" -"+n+" "+i+" -"+n+" 0 c"),r.push("-"+n+" -"+i+" -"+i+" -"+n+" 0 -"+n+" c"),r.push(i+" -"+n+" "+n+" -"+i+" "+n+" 0 c"),r.push("f"),r.push("Q"),e.stream=r.join("\n"),e},YesPushDown:function(t){var e=V(t);e.scope=t.scope;var r=[],n=tA.internal.getWidth(t)<=tA.internal.getHeight(t)?tA.internal.getWidth(t)/4:tA.internal.getHeight(t)/4,i=Number((2*(n=Number((.9*n).toFixed(5)))).toFixed(5)),a=Number((i*tA.internal.Bezier_C).toFixed(5)),o=Number((n*tA.internal.Bezier_C).toFixed(5));return r.push("0.749023 g"),r.push("q"),r.push("1 0 0 1 "+U(tA.internal.getWidth(t)/2)+" "+U(tA.internal.getHeight(t)/2)+" cm"),r.push(i+" 0 m"),r.push(i+" "+a+" "+a+" "+i+" 0 "+i+" c"),r.push("-"+a+" "+i+" -"+i+" "+a+" -"+i+" 0 c"),r.push("-"+i+" -"+a+" -"+a+" -"+i+" 0 -"+i+" c"),r.push(a+" -"+i+" "+i+" -"+a+" "+i+" 0 c"),r.push("f"),r.push("Q"),r.push("0 g"),r.push("q"),r.push("1 0 0 1 "+U(tA.internal.getWidth(t)/2)+" "+U(tA.internal.getHeight(t)/2)+" cm"),r.push(n+" 0 m"),r.push(n+" "+o+" "+o+" "+n+" 0 "+n+" c"),r.push("-"+o+" "+n+" -"+n+" "+o+" -"+n+" 0 c"),r.push("-"+n+" -"+o+" -"+o+" -"+n+" 0 -"+n+" c"),r.push(o+" -"+n+" "+n+" -"+o+" "+n+" 0 c"),r.push("f"),r.push("Q"),e.stream=r.join("\n"),e},OffPushDown:function(t){var e=V(t);e.scope=t.scope;var r=[],n=tA.internal.getWidth(t)<=tA.internal.getHeight(t)?tA.internal.getWidth(t)/4:tA.internal.getHeight(t)/4,i=Number((2*(n=Number((.9*n).toFixed(5)))).toFixed(5)),a=Number((i*tA.internal.Bezier_C).toFixed(5));return r.push("0.749023 g"),r.push("q"),r.push("1 0 0 1 "+U(tA.internal.getWidth(t)/2)+" "+U(tA.internal.getHeight(t)/2)+" cm"),r.push(i+" 0 m"),r.push(i+" "+a+" "+a+" "+i+" 0 "+i+" c"),r.push("-"+a+" "+i+" -"+i+" "+a+" -"+i+" 0 c"),r.push("-"+i+" -"+a+" -"+a+" -"+i+" 0 -"+i+" c"),r.push(a+" -"+i+" "+i+" -"+a+" "+i+" 0 c"),r.push("f"),r.push("Q"),e.stream=r.join("\n"),e}},Cross:{createAppearanceStream:function(t){var e={D:{Off:tA.RadioButton.Cross.OffPushDown},N:{}};return e.N[t]=tA.RadioButton.Cross.YesNormal,e.D[t]=tA.RadioButton.Cross.YesPushDown,e},getCA:function(){return"8"},YesNormal:function(t){var e=V(t);e.scope=t.scope;var r=[],n=tA.internal.calculateCross(t);return r.push("q"),r.push("1 1 "+z(tA.internal.getWidth(t)-2)+" "+z(tA.internal.getHeight(t)-2)+" re"),r.push("W"),r.push("n"),r.push(z(n.x1.x)+" "+z(n.x1.y)+" m"),r.push(z(n.x2.x)+" "+z(n.x2.y)+" l"),r.push(z(n.x4.x)+" "+z(n.x4.y)+" m"),r.push(z(n.x3.x)+" "+z(n.x3.y)+" l"),r.push("s"),r.push("Q"),e.stream=r.join("\n"),e},YesPushDown:function(t){var e=V(t);e.scope=t.scope;var r=tA.internal.calculateCross(t),n=[];return n.push("0.749023 g"),n.push("0 0 "+z(tA.internal.getWidth(t))+" "+z(tA.internal.getHeight(t))+" re"),n.push("f"),n.push("q"),n.push("1 1 "+z(tA.internal.getWidth(t)-2)+" "+z(tA.internal.getHeight(t)-2)+" re"),n.push("W"),n.push("n"),n.push(z(r.x1.x)+" "+z(r.x1.y)+" m"),n.push(z(r.x2.x)+" "+z(r.x2.y)+" l"),n.push(z(r.x4.x)+" "+z(r.x4.y)+" m"),n.push(z(r.x3.x)+" "+z(r.x3.y)+" l"),n.push("s"),n.push("Q"),e.stream=n.join("\n"),e},OffPushDown:function(t){var e=V(t);e.scope=t.scope;var r=[];return r.push("0.749023 g"),r.push("0 0 "+z(tA.internal.getWidth(t))+" "+z(tA.internal.getHeight(t))+" re"),r.push("f"),e.stream=r.join("\n"),e}}},createDefaultAppearanceStream:function(t){var e=t.scope.internal.getFont(t.fontName,t.fontStyle).id,r=t.scope.__private__.encodeColorString(t.color);return"/"+e+" "+t.fontSize+" Tf "+r}};tA.internal={Bezier_C:.551915024494,calculateCross:function(t){var e=tA.internal.getWidth(t),r=tA.internal.getHeight(t),n=Math.min(e,r);return{x1:{x:(e-n)/2,y:(r-n)/2+n},x2:{x:(e-n)/2+n,y:(r-n)/2},x3:{x:(e-n)/2,y:(r-n)/2},x4:{x:(e-n)/2+n,y:(r-n)/2+n}}}},tA.internal.getWidth=function(t){var e=0;return"object"===(0,n.A)(t)&&(e=W(t.Rect[2])),e},tA.internal.getHeight=function(t){var e=0;return"object"===(0,n.A)(t)&&(e=W(t.Rect[3])),e};var tL=B.addField=function(t){if(ta(this,t),!(t instanceof tl))throw Error("Invalid argument passed to jsPDF.addField.");return t.scope.internal.acroformPlugin.printedOut&&(t.scope.internal.acroformPlugin.printedOut=!1,t.scope.internal.acroformPlugin.acroFormDictionaryRoot=null),t.scope.internal.acroformPlugin.acroFormDictionaryRoot.Fields.push(t),t.page=t.scope.internal.getCurrentPageInfo().pageNumber,this};B.AcroFormChoiceField=tf,B.AcroFormListBox=td,B.AcroFormComboBox=tp,B.AcroFormEditBox=tg,B.AcroFormButton=tm,B.AcroFormPushButton=tv,B.AcroFormRadioButton=tb,B.AcroFormCheckBox=tw,B.AcroFormTextField=tx,B.AcroFormPasswordField=tN,B.AcroFormAppearance=tA,B.AcroForm={ChoiceField:tf,ListBox:td,ComboBox:tp,EditBox:tg,Button:tm,PushButton:tv,RadioButton:tb,CheckBox:tw,TextField:tx,PasswordField:tN,Appearance:tA},q.AcroForm={ChoiceField:tf,ListBox:td,ComboBox:tp,EditBox:tg,Button:tm,PushButton:tv,RadioButton:tb,CheckBox:tw,TextField:tx,PasswordField:tN,Appearance:tA};var tS=q.AcroForm;function t_(t){return t.reduce(function(t,e,r){return t[e]=r,t},{})}!function(t){t.__addimage__={};var e="UNKNOWN",r={PNG:[[137,80,78,71]],TIFF:[[77,77,0,42],[73,73,42,0]],JPEG:[[255,216,255,224,void 0,void 0,74,70,73,70,0],[255,216,255,225,void 0,void 0,69,120,105,102,0,0],[255,216,255,219],[255,216,255,238]],JPEG2000:[[0,0,0,12,106,80,32,32]],GIF87a:[[71,73,70,56,55,97]],GIF89a:[[71,73,70,56,57,97]],WEBP:[[82,73,70,70,void 0,void 0,void 0,void 0,87,69,66,80]],BMP:[[66,77],[66,65],[67,73],[67,80],[73,67],[80,84]]},i=t.__addimage__.getImageFileTypeByImageData=function(t,n){var i,a,o,s,c,u=e;if("RGBA"===(n=n||e)||void 0!==t.data&&t.data instanceof Uint8ClampedArray&&"height"in t&&"width"in t)return"RGBA";if(L(t))for(c in r)for(o=r[c],i=0;i<o.length;i+=1){for(s=!0,a=0;a<o[i].length;a+=1)if(void 0!==o[i][a]&&o[i][a]!==t[a]){s=!1;break}if(!0===s){u=c;break}}else for(c in r)for(o=r[c],i=0;i<o.length;i+=1){for(s=!0,a=0;a<o[i].length;a+=1)if(void 0!==o[i][a]&&o[i][a]!==t.charCodeAt(a)){s=!1;break}if(!0===s){u=c;break}}return u===e&&n!==e&&(u=n),u},a=function t(e){for(var r=this.internal.write,n=this.internal.putStream,i=(0,this.internal.getFilters)();-1!==i.indexOf("FlateEncode");)i.splice(i.indexOf("FlateEncode"),1);e.objectId=this.internal.newObject();var a=[];if(a.push({key:"Type",value:"/XObject"}),a.push({key:"Subtype",value:"/Image"}),a.push({key:"Width",value:e.width}),a.push({key:"Height",value:e.height}),e.colorSpace===b.INDEXED?a.push({key:"ColorSpace",value:"[/Indexed /DeviceRGB "+(e.palette.length/3-1)+" "+("sMask"in e&&void 0!==e.sMask?e.objectId+2:e.objectId+1)+" 0 R]"}):(a.push({key:"ColorSpace",value:"/"+e.colorSpace}),e.colorSpace===b.DEVICE_CMYK&&a.push({key:"Decode",value:"[1 0 1 0 1 0 1 0]"})),a.push({key:"BitsPerComponent",value:e.bitsPerComponent}),"decodeParameters"in e&&void 0!==e.decodeParameters&&a.push({key:"DecodeParms",value:"<<"+e.decodeParameters+">>"}),"transparency"in e&&Array.isArray(e.transparency)){for(var o="",s=0,c=e.transparency.length;s<c;s++)o+=e.transparency[s]+" "+e.transparency[s]+" ";a.push({key:"Mask",value:"["+o+"]"})}void 0!==e.sMask&&a.push({key:"SMask",value:e.objectId+1+" 0 R"});var u=void 0!==e.filter?["/"+e.filter]:void 0;if(n({data:e.data,additionalKeyValues:a,alreadyAppliedFilters:u,objectId:e.objectId}),r("endobj"),"sMask"in e&&void 0!==e.sMask){var h="/Predictor "+e.predictor+" /Colors 1 /BitsPerComponent "+e.bitsPerComponent+" /Columns "+e.width,l={width:e.width,height:e.height,colorSpace:"DeviceGray",bitsPerComponent:e.bitsPerComponent,decodeParameters:h,data:e.sMask};"filter"in e&&(l.filter=e.filter),t.call(this,l)}if(e.colorSpace===b.INDEXED){var f=this.internal.newObject();n({data:_(new Uint8Array(e.palette)),objectId:f}),r("endobj")}},o=function(){var t=this.internal.collections.addImage_images;for(var e in t)a.call(this,t[e])},s=function(){var t,e=this.internal.collections.addImage_images,r=this.internal.write;for(var n in e)r("/I"+(t=e[n]).index,t.objectId,"0","R")},c=function(){this.internal.collections.addImage_images||(this.internal.collections.addImage_images={},this.internal.events.subscribe("putResources",o),this.internal.events.subscribe("putXobjectDict",s))},u=function(){var t=this.internal.collections.addImage_images;return c.call(this),t},h=function(){return Object.keys(this.internal.collections.addImage_images).length},f=function(e){return"function"==typeof t["process"+e.toUpperCase()]},d=function(t){return"object"===(0,n.A)(t)&&1===t.nodeType},p=function(e,r){if("IMG"===e.nodeName&&e.hasAttribute("src")){var n,i=""+e.getAttribute("src");if(0===i.indexOf("data:image/"))return l(unescape(i).split("base64,").pop());var a=t.loadFile(i,!0);if(void 0!==a)return a}if("CANVAS"===e.nodeName){if(0===e.width||0===e.height)throw Error("Given canvas must have data. Canvas width: "+e.width+", height: "+e.height);switch(r){case"PNG":n="image/png";break;case"WEBP":n="image/webp";break;default:n="image/jpeg"}return l(e.toDataURL(n,1).split("base64,").pop())}},g=function(t){var e=this.internal.collections.addImage_images;if(e){for(var r in e)if(t===e[r].alias)return e[r]}},m=function(t,e,r){return t||e||(t=-96,e=-96),t<0&&(t=-1*r.width*72/t/this.internal.scaleFactor),e<0&&(e=-1*r.height*72/e/this.internal.scaleFactor),0===t&&(t=e*r.width/r.height),0===e&&(e=t*r.height/r.width),[t,e]},v=function(t,e,r,n,i,a){var o=m.call(this,r,n,i),s=this.internal.getCoordinateString,c=this.internal.getVerticalCoordinateString,h=u.call(this);if(r=o[0],n=o[1],h[i.index]=i,a)var l=Math.cos(a*=Math.PI/180),f=Math.sin(a),d=function(t){return t.toFixed(4)},p=[d(l),d(f),d(-1*f),d(l),0,0,"cm"];this.internal.write("q"),a?(this.internal.write([1,"0","0",1,s(t),c(e+n),"cm"].join(" ")),this.internal.write(p.join(" ")),this.internal.write([s(r),"0","0",s(n),"0","0","cm"].join(" "))):this.internal.write([s(r),"0","0",s(n),s(t),c(e+n),"cm"].join(" ")),this.isAdvancedAPI()&&this.internal.write("1 0 0 -1 0 0 cm"),this.internal.write("/I"+i.index+" Do"),this.internal.write("Q")},b=t.color_spaces={DEVICE_RGB:"DeviceRGB",DEVICE_GRAY:"DeviceGray",DEVICE_CMYK:"DeviceCMYK",CAL_GREY:"CalGray",CAL_RGB:"CalRGB",LAB:"Lab",ICC_BASED:"ICCBased",INDEXED:"Indexed",PATTERN:"Pattern",SEPARATION:"Separation",DEVICE_N:"DeviceN"};t.decode={DCT_DECODE:"DCTDecode",FLATE_DECODE:"FlateDecode",LZW_DECODE:"LZWDecode",JPX_DECODE:"JPXDecode",JBIG2_DECODE:"JBIG2Decode",ASCII85_DECODE:"ASCII85Decode",ASCII_HEX_DECODE:"ASCIIHexDecode",RUN_LENGTH_DECODE:"RunLengthDecode",CCITT_FAX_DECODE:"CCITTFaxDecode"};var y=t.image_compression={NONE:"NONE",FAST:"FAST",MEDIUM:"MEDIUM",SLOW:"SLOW"},w=t.__addimage__.sHashCode=function(t){var e,r,n=0;if("string"==typeof t)for(r=t.length,e=0;e<r;e++)n=(n<<5)-n+t.charCodeAt(e)|0;else if(L(t))for(r=t.byteLength/2,e=0;e<r;e++)n=(n<<5)-n+t[e]|0;return n},x=t.__addimage__.validateStringAsBase64=function(t){(t=t||"").toString().trim();var e=!0;return 0===t.length&&(e=!1),t.length%4!=0&&(e=!1),!1===/^[A-Za-z0-9+/]+$/.test(t.substr(0,t.length-2))&&(e=!1),!1===/^[A-Za-z0-9/][A-Za-z0-9+/]|[A-Za-z0-9+/]=|==$/.test(t.substr(-2))&&(e=!1),e},N=t.__addimage__.extractImageFromDataUrl=function(t){if(null==t||!(t=t.trim()).startsWith("data:"))return null;var e=t.indexOf(",");return e<0?null:t.substring(0,e).trim().endsWith("base64")?t.substring(e+1):null},A=t.__addimage__.supportsArrayBuffer=function(){return"undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array};t.__addimage__.isArrayBuffer=function(t){return A()&&t instanceof ArrayBuffer};var L=t.__addimage__.isArrayBufferView=function(t){return A()&&"undefined"!=typeof Uint32Array&&(t instanceof Int8Array||t instanceof Uint8Array||"undefined"!=typeof Uint8ClampedArray&&t instanceof Uint8ClampedArray||t instanceof Int16Array||t instanceof Uint16Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Float32Array||t instanceof Float64Array)},S=t.__addimage__.binaryStringToUint8Array=function(t){for(var e=t.length,r=new Uint8Array(e),n=0;n<e;n++)r[n]=t.charCodeAt(n);return r},_=t.__addimage__.arrayBufferToBinaryString=function(t){for(var e="",r=L(t)?t:new Uint8Array(t),n=0;n<r.length;n+=8192)e+=String.fromCharCode.apply(null,r.subarray(n,n+8192));return e};t.addImage=function(){if("number"==typeof arguments[1]?(r=e,i=arguments[1],a=arguments[2],o=arguments[3],s=arguments[4],u=arguments[5],h=arguments[6],l=arguments[7]):(r=arguments[1],i=arguments[2],a=arguments[3],o=arguments[4],s=arguments[5],u=arguments[6],h=arguments[7],l=arguments[8]),"object"===(0,n.A)(t=arguments[0])&&!d(t)&&"imageData"in t){var t,r,i,a,o,s,u,h,l,f=t;t=f.imageData,r=f.format||r||e,i=f.x||i||0,a=f.y||a||0,o=f.w||f.width||o,s=f.h||f.height||s,u=f.alias||u,h=f.compression||h,l=f.rotation||f.angle||l}var p=this.internal.getFilters();if(void 0===h&&-1!==p.indexOf("FlateEncode")&&(h="SLOW"),isNaN(i)||isNaN(a))throw Error("Invalid coordinates passed to jsPDF.addImage");c.call(this);var g=P.call(this,t,r,u,h);return v.call(this,i,a,o,s,g,l),this};var P=function(r,n,a,o){if("string"==typeof r&&i(r)===e){var s,c,u,l,m,v=k(r=unescape(r),!1);(""!==v||void 0!==(v=t.loadFile(r,!0)))&&(r=v)}if(d(r)&&(r=p(r,n)),!f(n=i(r,n)))throw Error("addImage does not support files of type '"+n+"', please ensure that a plugin for '"+n+"' support is added.");if((null==(u=a)||0===u.length)&&(a="string"==typeof(l=r)||L(l)?w(l):L(l.data)?w(l.data):null),(s=g.call(this,a))||(A()&&(r instanceof Uint8Array||"RGBA"===n||(c=r,r=S(r))),s=this["process"+n.toUpperCase()](r,h.call(this),a,((m=o)&&"string"==typeof m&&(m=m.toUpperCase()),m in t.image_compression?m:y.NONE),c)),!s)throw Error("An unknown error occurred whilst processing the image.");return s},k=t.__addimage__.convertBase64ToBinaryString=function(t,e){e="boolean"!=typeof e||e;var r,n,i="";if("string"==typeof t){n=null!=(r=N(t))?r:t;try{i=l(n)}catch(t){if(e)throw x(n)?Error("atob-Error in jsPDF.convertBase64ToBinaryString "+t.message):Error("Supplied Data is not a valid base64-String jsPDF.convertBase64ToBinaryString ")}}return i};t.getImageProperties=function(r){var n,a,o="";if(d(r)&&(r=p(r)),"string"==typeof r&&i(r)===e&&(""===(o=k(r,!1))&&(o=t.loadFile(r)||""),r=o),!f(a=i(r)))throw Error("addImage does not support files of type '"+a+"', please ensure that a plugin for '"+a+"' support is added.");if(!A()||r instanceof Uint8Array||(r=S(r)),!(n=this["process"+a.toUpperCase()](r)))throw Error("An unknown error occurred whilst processing the image");return n.fileType=a,n}}(q.API),function(t){var e=function(t){if(void 0!==t&&""!=t)return!0};q.API.events.push(["addPage",function(t){this.internal.getPageInfo(t.pageNumber).pageContext.annotations=[]}]),t.events.push(["putPage",function(t){for(var r,n,i,a=this.internal.getCoordinateString,o=this.internal.getVerticalCoordinateString,s=this.internal.getPageInfoByObjId(t.objId),c=t.pageContext.annotations,u=!1,h=0;h<c.length&&!u;h++)switch((r=c[h]).type){case"link":(e(r.options.url)||e(r.options.pageNumber))&&(u=!0);break;case"reference":case"text":case"freetext":u=!0}if(0!=u){this.internal.write("/Annots [");for(var l=0;l<c.length;l++){r=c[l];var f=this.internal.pdfEscape,d=this.internal.getEncryptor(t.objId);switch(r.type){case"reference":this.internal.write(" "+r.object.objId+" 0 R ");break;case"text":var p=this.internal.newAdditionalObject(),g=this.internal.newAdditionalObject(),m=this.internal.getEncryptor(p.objId),v=r.title||"Note";p.content=i="<</Type /Annot /Subtype /Text "+(n="/Rect ["+a(r.bounds.x)+" "+o(r.bounds.y+r.bounds.h)+" "+a(r.bounds.x+r.bounds.w)+" "+o(r.bounds.y)+"] ")+"/Contents ("+f(m(r.contents))+")"+(" /Popup "+g.objId)+" 0 R"+(" /P "+s.objId)+" 0 R"+(" /T ("+f(m(v)))+") >>";var b=p.objId+" 0 R";i="<</Type /Annot /Subtype /Popup "+(n="/Rect ["+a(r.bounds.x+30)+" "+o(r.bounds.y+r.bounds.h)+" "+a(r.bounds.x+r.bounds.w+30)+" "+o(r.bounds.y)+"] ")+" /Parent "+b,r.open&&(i+=" /Open true"),g.content=i+=" >>",this.internal.write(p.objId,"0 R",g.objId,"0 R");break;case"freetext":n="/Rect ["+a(r.bounds.x)+" "+o(r.bounds.y)+" "+a(r.bounds.x+r.bounds.w)+" "+o(r.bounds.y+r.bounds.h)+"] ";var y=r.color||"#000000";i="<</Type /Annot /Subtype /FreeText "+n+"/Contents ("+f(d(r.contents))+")"+(" /DS(font: Helvetica,sans-serif 12.0pt; text-align:left; color:#"+y)+") /Border [0 0 0] >>",this.internal.write(i);break;case"link":if(r.options.name){var w=this.annotations._nameMap[r.options.name];r.options.pageNumber=w.page,r.options.top=w.y}else r.options.top||(r.options.top=0);if(n="/Rect ["+r.finalBounds.x+" "+r.finalBounds.y+" "+r.finalBounds.w+" "+r.finalBounds.h+"] ",i="",r.options.url)i="<</Type /Annot /Subtype /Link "+n+"/Border [0 0 0] /A <</S /URI /URI ("+f(d(r.options.url))+") >>";else if(r.options.pageNumber)switch(i="<</Type /Annot /Subtype /Link "+n+"/Border [0 0 0] /Dest ["+this.internal.getPageInfo(r.options.pageNumber).objId+" 0 R",r.options.magFactor=r.options.magFactor||"XYZ",r.options.magFactor){case"Fit":i+=" /Fit]";break;case"FitH":i+=" /FitH "+r.options.top+"]";break;case"FitV":r.options.left=r.options.left||0,i+=" /FitV "+r.options.left+"]";break;default:var x=o(r.options.top);r.options.left=r.options.left||0,void 0===r.options.zoom&&(r.options.zoom=0),i+=" /XYZ "+r.options.left+" "+x+" "+r.options.zoom+"]"}""!=i&&(i+=" >>",this.internal.write(i))}}this.internal.write("]")}}]),t.createAnnotation=function(t){var e=this.internal.getCurrentPageInfo();switch(t.type){case"link":this.link(t.bounds.x,t.bounds.y,t.bounds.w,t.bounds.h,t);break;case"text":case"freetext":e.pageContext.annotations.push(t)}},t.link=function(t,e,r,n,i){var a=this.internal.getCurrentPageInfo(),o=this.internal.getCoordinateString,s=this.internal.getVerticalCoordinateString;a.pageContext.annotations.push({finalBounds:{x:o(t),y:s(e),w:o(t+r),h:s(e+n)},options:i,type:"link"})},t.textWithLink=function(t,e,r,n){var i,a,o=this.getTextWidth(t),s=this.internal.getLineHeight()/this.internal.scaleFactor;return void 0!==n.maxWidth?(a=n.maxWidth,i=Math.ceil(s*this.splitTextToSize(t,a).length)):(a=o,i=s),this.text(t,e,r,n),r+=.2*s,"center"===n.align&&(e-=o/2),"right"===n.align&&(e-=o),this.link(e,r-s,a,i,n),o},t.getTextWidth=function(t){var e=this.internal.getFontSize();return this.getStringUnitWidth(t)*e/this.internal.scaleFactor}}(q.API),function(t){var e={1569:[65152],1570:[65153,65154],1571:[65155,65156],1572:[65157,65158],1573:[65159,65160],1574:[65161,65162,65163,65164],1575:[65165,65166],1576:[65167,65168,65169,65170],1577:[65171,65172],1578:[65173,65174,65175,65176],1579:[65177,65178,65179,65180],1580:[65181,65182,65183,65184],1581:[65185,65186,65187,65188],1582:[65189,65190,65191,65192],1583:[65193,65194],1584:[65195,65196],1585:[65197,65198],1586:[65199,65200],1587:[65201,65202,65203,65204],1588:[65205,65206,65207,65208],1589:[65209,65210,65211,65212],1590:[65213,65214,65215,65216],1591:[65217,65218,65219,65220],1592:[65221,65222,65223,65224],1593:[65225,65226,65227,65228],1594:[65229,65230,65231,65232],1601:[65233,65234,65235,65236],1602:[65237,65238,65239,65240],1603:[65241,65242,65243,65244],1604:[65245,65246,65247,65248],1605:[65249,65250,65251,65252],1606:[65253,65254,65255,65256],1607:[65257,65258,65259,65260],1608:[65261,65262],1609:[65263,65264,64488,64489],1610:[65265,65266,65267,65268],1649:[64336,64337],1655:[64477],1657:[64358,64359,64360,64361],1658:[64350,64351,64352,64353],1659:[64338,64339,64340,64341],1662:[64342,64343,64344,64345],1663:[64354,64355,64356,64357],1664:[64346,64347,64348,64349],1667:[64374,64375,64376,64377],1668:[64370,64371,64372,64373],1670:[64378,64379,64380,64381],1671:[64382,64383,64384,64385],1672:[64392,64393],1676:[64388,64389],1677:[64386,64387],1678:[64390,64391],1681:[64396,64397],1688:[64394,64395],1700:[64362,64363,64364,64365],1702:[64366,64367,64368,64369],1705:[64398,64399,64400,64401],1709:[64467,64468,64469,64470],1711:[64402,64403,64404,64405],1713:[64410,64411,64412,64413],1715:[64406,64407,64408,64409],1722:[64414,64415],1723:[64416,64417,64418,64419],1726:[64426,64427,64428,64429],1728:[64420,64421],1729:[64422,64423,64424,64425],1733:[64480,64481],1734:[64473,64474],1735:[64471,64472],1736:[64475,64476],1737:[64482,64483],1739:[64478,64479],1740:[64508,64509,64510,64511],1744:[64484,64485,64486,64487],1746:[64430,64431],1747:[64432,64433]},r={65247:{65154:65269,65156:65271,65160:65273,65166:65275},65248:{65154:65270,65156:65272,65160:65274,65166:65276},65165:{65247:{65248:{65258:65010}}},1617:{1612:64606,1613:64607,1614:64608,1615:64609,1616:64610}},n={1612:64606,1613:64607,1614:64608,1615:64609,1616:64610},i=[1570,1571,1573,1575];t.__arabicParser__={};var a=t.__arabicParser__.isInArabicSubstitutionA=function(t){return void 0!==e[t.charCodeAt(0)]},o=t.__arabicParser__.isArabicLetter=function(t){return"string"==typeof t&&/^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+$/.test(t)},s=t.__arabicParser__.isArabicEndLetter=function(t){return o(t)&&a(t)&&e[t.charCodeAt(0)].length<=2},c=t.__arabicParser__.isArabicAlfLetter=function(t){return o(t)&&i.indexOf(t.charCodeAt(0))>=0};t.__arabicParser__.arabicLetterHasIsolatedForm=function(t){return o(t)&&a(t)&&e[t.charCodeAt(0)].length>=1};var u=t.__arabicParser__.arabicLetterHasFinalForm=function(t){return o(t)&&a(t)&&e[t.charCodeAt(0)].length>=2};t.__arabicParser__.arabicLetterHasInitialForm=function(t){return o(t)&&a(t)&&e[t.charCodeAt(0)].length>=3};var h=t.__arabicParser__.arabicLetterHasMedialForm=function(t){return o(t)&&a(t)&&4==e[t.charCodeAt(0)].length},l=t.__arabicParser__.resolveLigatures=function(t){var e=0,n=r,i="",a=0;for(e=0;e<t.length;e+=1)void 0!==n[t.charCodeAt(e)]?(a++,"number"==typeof(n=n[t.charCodeAt(e)])&&(i+=String.fromCharCode(n),n=r,a=0),e===t.length-1&&(n=r,i+=t.charAt(e-(a-1)),e-=a-1,a=0)):(n=r,i+=t.charAt(e-a),e-=a,a=0);return i};t.__arabicParser__.isArabicDiacritic=function(t){return void 0!==t&&void 0!==n[t.charCodeAt(0)]};var f=t.__arabicParser__.getCorrectForm=function(t,e,r){return o(t)?!1===a(t)?-1:!u(t)||!o(e)&&!o(r)||!o(r)&&s(e)||s(t)&&!o(e)||s(t)&&c(e)||s(t)&&s(e)?0:h(t)&&o(e)&&!s(e)&&o(r)&&u(r)?3:s(t)||!o(r)?1:2:-1},d=function(t){var r=0,n=0,i=0,a="",s="",c="",u=(t=t||"").split("\\s+"),h=[];for(r=0;r<u.length;r+=1){for(h.push(""),n=0;n<u[r].length;n+=1)a=u[r][n],s=u[r][n-1],c=u[r][n+1],o(a)?(i=f(a,s,c),h[r]+=-1!==i?String.fromCharCode(e[a.charCodeAt(0)][i]):a):h[r]+=a;h[r]=l(h[r])}return h.join(" ")},p=t.__arabicParser__.processArabic=t.processArabic=function(){var t,e="string"==typeof arguments[0]?arguments[0]:arguments[0].text,r=[];if(Array.isArray(e)){var n=0;for(r=[],n=0;n<e.length;n+=1)Array.isArray(e[n])?r.push([d(e[n][0]),e[n][1],e[n][2]]):r.push([d(e[n])]);t=r}else t=d(e);return"string"==typeof arguments[0]?t:(arguments[0].text=t,arguments[0])};t.events.push(["preProcessText",p])}(q.API),q.API.autoPrint=function(t){var e;return((t=t||{}).variant=t.variant||"non-conform","javascript"===t.variant)?this.addJS("print({});"):(this.internal.events.subscribe("postPutResources",function(){e=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /Named"),this.internal.out("/Type /Action"),this.internal.out("/N /Print"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){this.internal.out("/OpenAction "+e+" 0 R")})),this},function(t){var e=function(){var t=void 0;Object.defineProperty(this,"pdf",{get:function(){return t},set:function(e){t=e}});var e=150;Object.defineProperty(this,"width",{get:function(){return e},set:function(t){e=isNaN(t)||!1===Number.isInteger(t)||t<0?150:t,this.getContext("2d").pageWrapXEnabled&&(this.getContext("2d").pageWrapX=e+1)}});var r=300;Object.defineProperty(this,"height",{get:function(){return r},set:function(t){r=isNaN(t)||!1===Number.isInteger(t)||t<0?300:t,this.getContext("2d").pageWrapYEnabled&&(this.getContext("2d").pageWrapY=r+1)}});var n=[];Object.defineProperty(this,"childNodes",{get:function(){return n},set:function(t){n=t}});var i={};Object.defineProperty(this,"style",{get:function(){return i},set:function(t){i=t}}),Object.defineProperty(this,"parentNode",{})};e.prototype.getContext=function(t,e){var r;if("2d"!==(t=t||"2d"))return null;for(r in e)this.pdf.context2d.hasOwnProperty(r)&&(this.pdf.context2d[r]=e[r]);return this.pdf.context2d._canvas=this,this.pdf.context2d},e.prototype.toDataURL=function(){throw Error("toDataURL is not implemented.")},t.events.push(["initialized",function(){this.canvas=new e,this.canvas.pdf=this}])}(q.API),function(t){var e={left:0,top:0,bottom:0,right:0},r=!1,i=function(){void 0===this.internal.__cell__&&(this.internal.__cell__={},this.internal.__cell__.padding=3,this.internal.__cell__.headerFunction=void 0,this.internal.__cell__.margins=Object.assign({},e),this.internal.__cell__.margins.width=this.getPageWidth(),a.call(this))},a=function(){this.internal.__cell__.lastCell=new o,this.internal.__cell__.pages=1},o=function(){var t=arguments[0];Object.defineProperty(this,"x",{enumerable:!0,get:function(){return t},set:function(e){t=e}});var e=arguments[1];Object.defineProperty(this,"y",{enumerable:!0,get:function(){return e},set:function(t){e=t}});var r=arguments[2];Object.defineProperty(this,"width",{enumerable:!0,get:function(){return r},set:function(t){r=t}});var n=arguments[3];Object.defineProperty(this,"height",{enumerable:!0,get:function(){return n},set:function(t){n=t}});var i=arguments[4];Object.defineProperty(this,"text",{enumerable:!0,get:function(){return i},set:function(t){i=t}});var a=arguments[5];Object.defineProperty(this,"lineNumber",{enumerable:!0,get:function(){return a},set:function(t){a=t}});var o=arguments[6];return Object.defineProperty(this,"align",{enumerable:!0,get:function(){return o},set:function(t){o=t}}),this};o.prototype.clone=function(){return new o(this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align)},o.prototype.toArray=function(){return[this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align]},t.setHeaderFunction=function(t){return i.call(this),this.internal.__cell__.headerFunction="function"==typeof t?t:void 0,this},t.getTextDimensions=function(t,e){i.call(this);var r=(e=e||{}).fontSize||this.getFontSize(),n=e.font||this.getFont(),a=e.scaleFactor||this.internal.scaleFactor,o=0,s=0,c=0,u=this;if(!Array.isArray(t)&&"string"!=typeof t){if("number"!=typeof t)throw Error("getTextDimensions expects text-parameter to be of type String or type Number or an Array of Strings.");t=String(t)}var h=e.maxWidth;h>0?"string"==typeof t?t=this.splitTextToSize(t,h):"[object Array]"===Object.prototype.toString.call(t)&&(t=t.reduce(function(t,e){return t.concat(u.splitTextToSize(e,h))},[])):t=Array.isArray(t)?t:[t];for(var l=0;l<t.length;l++)o<(c=this.getStringUnitWidth(t[l],{font:n})*r)&&(o=c);return 0!==o&&(s=t.length),{w:o/=a,h:Math.max((s*r*this.getLineHeightFactor()-r*(this.getLineHeightFactor()-1))/a,0)}},t.cellAddPage=function(){i.call(this),this.addPage();var t=this.internal.__cell__.margins||e;return this.internal.__cell__.lastCell=new o(t.left,t.top,void 0,void 0),this.internal.__cell__.pages+=1,this};var s=t.cell=function(){t=arguments[0]instanceof o?arguments[0]:new o(arguments[0],arguments[1],arguments[2],arguments[3],arguments[4],arguments[5]),i.call(this);var t,n=this.internal.__cell__.lastCell,a=this.internal.__cell__.padding,s=this.internal.__cell__.margins||e,c=this.internal.__cell__.tableHeaderRow,u=this.internal.__cell__.printHeaders;return void 0!==n.lineNumber&&(n.lineNumber===t.lineNumber?(t.x=(n.x||0)+(n.width||0),t.y=n.y||0):n.y+n.height+t.height+s.bottom>this.getPageHeight()?(this.cellAddPage(),t.y=s.top,u&&c&&(this.printHeaderRow(t.lineNumber,!0),t.y+=c[0].height)):t.y=n.y+n.height||t.y),void 0!==t.text[0]&&(this.rect(t.x,t.y,t.width,t.height,!0===r?"FD":void 0),"right"===t.align?this.text(t.text,t.x+t.width-a,t.y+a,{align:"right",baseline:"top"}):"center"===t.align?this.text(t.text,t.x+t.width/2,t.y+a,{align:"center",baseline:"top",maxWidth:t.width-a-a}):this.text(t.text,t.x+a,t.y+a,{align:"left",baseline:"top",maxWidth:t.width-a-a})),this.internal.__cell__.lastCell=t,this};t.table=function(t,r,u,h,l){if(i.call(this),!u)throw Error("No data for PDF table.");var f,d,p,g,m=[],v=[],b=[],y={},w={},x=[],N=[],A=(l=l||{}).autoSize||!1,L=!1!==l.printHeaders,S=l.css&&void 0!==l.css["font-size"]?16*l.css["font-size"]:l.fontSize||12,_=l.margins||Object.assign({width:this.getPageWidth()},e),P="number"==typeof l.padding?l.padding:3,k=l.headerBackgroundColor||"#c8c8c8",F=l.headerTextColor||"#000";if(a.call(this),this.internal.__cell__.printHeaders=L,this.internal.__cell__.margins=_,this.internal.__cell__.table_font_size=S,this.internal.__cell__.padding=P,this.internal.__cell__.headerBackgroundColor=k,this.internal.__cell__.headerTextColor=F,this.setFontSize(S),null==h)v=m=Object.keys(u[0]),b=m.map(function(){return"left"});else if(Array.isArray(h)&&"object"===(0,n.A)(h[0]))for(m=h.map(function(t){return t.name}),v=h.map(function(t){return t.prompt||t.name||""}),b=h.map(function(t){return t.align||"left"}),f=0;f<h.length;f+=1)w[h[f].name]=h[f].width*(19.049976/25.4);else Array.isArray(h)&&"string"==typeof h[0]&&(v=m=h,b=m.map(function(){return"left"}));if(A||Array.isArray(h)&&"string"==typeof h[0])for(f=0;f<m.length;f+=1){for(y[g=m[f]]=u.map(function(t){return t[g]}),this.setFont(void 0,"bold"),x.push(this.getTextDimensions(v[f],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w),d=y[g],this.setFont(void 0,"normal"),p=0;p<d.length;p+=1)x.push(this.getTextDimensions(d[p],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w);w[g]=Math.max.apply(null,x)+P+P,x=[]}if(L){var I={};for(f=0;f<m.length;f+=1)I[m[f]]={},I[m[f]].text=v[f],I[m[f]].align=b[f];var C=c.call(this,I,w);N=m.map(function(e){return new o(t,r,w[e],C,I[e].text,void 0,I[e].align)}),this.setTableHeaderRow(N),this.printHeaderRow(1,!1)}var j=h.reduce(function(t,e){return t[e.name]=e.align,t},{});for(f=0;f<u.length;f+=1){"rowStart"in l&&l.rowStart instanceof Function&&l.rowStart({row:f,data:u[f]},this);var O=c.call(this,u[f],w);for(p=0;p<m.length;p+=1){var E=u[f][m[p]];"cellStart"in l&&l.cellStart instanceof Function&&l.cellStart({row:f,col:p,data:E},this),s.call(this,new o(t,r,w[m[p]],O,E,f+2,j[m[p]]))}}return this.internal.__cell__.table_x=t,this.internal.__cell__.table_y=r,this};var c=function(t,e){var r=this.internal.__cell__.padding,n=this.internal.__cell__.table_font_size,i=this.internal.scaleFactor;return Object.keys(t).map(function(n){var i=t[n];return this.splitTextToSize(i.hasOwnProperty("text")?i.text:i,e[n]-r-r)},this).map(function(t){return this.getLineHeightFactor()*t.length*n/i+r+r},this).reduce(function(t,e){return Math.max(t,e)},0)};t.setTableHeaderRow=function(t){i.call(this),this.internal.__cell__.tableHeaderRow=t},t.printHeaderRow=function(t,e){if(i.call(this),!this.internal.__cell__.tableHeaderRow)throw Error("Property tableHeaderRow does not exist.");if(r=!0,"function"==typeof this.internal.__cell__.headerFunction){var n,a=this.internal.__cell__.headerFunction(this,this.internal.__cell__.pages);this.internal.__cell__.lastCell=new o(a[0],a[1],a[2],a[3],void 0,-1)}this.setFont(void 0,"bold");for(var c=[],u=0;u<this.internal.__cell__.tableHeaderRow.length;u+=1){n=this.internal.__cell__.tableHeaderRow[u].clone(),e&&(n.y=this.internal.__cell__.margins.top||0,c.push(n)),n.lineNumber=t;var h=this.getTextColor();this.setTextColor(this.internal.__cell__.headerTextColor),this.setFillColor(this.internal.__cell__.headerBackgroundColor),s.call(this,n),this.setTextColor(h)}c.length>0&&this.setTableHeaderRow(c),this.setFont(void 0,"normal"),r=!1}}(q.API);var tP={italic:["italic","oblique","normal"],oblique:["oblique","italic","normal"],normal:["normal","oblique","italic"]},tk=["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded"],tF=t_(tk),tI=[100,200,300,400,500,600,700,800,900],tC=t_(tI);function tj(t){var e,r,n,i=t.family.replace(/"|'/g,"").toLowerCase(),a=tP[e=(e=t.style)||"normal"]?e:"normal",o=(r=t.weight)?"number"==typeof r?r>=100&&r<=900&&r%100==0?r:400:/^\d00$/.test(r)?parseInt(r):"bold"===r?700:400:400,s="number"==typeof tF[n=(n=t.stretch)||"normal"]?n:"normal";return{family:i,style:a,weight:o,stretch:s,src:t.src||[],ref:t.ref||{name:i,style:[s,a,o].join(" ")}}}function tO(t,e,r,n){var i;for(i=r;i>=0&&i<e.length;i+=n)if(t[e[i]])return t[e[i]];for(i=r;i>=0&&i<e.length;i-=n)if(t[e[i]])return t[e[i]]}var tE={"sans-serif":"helvetica",fixed:"courier",monospace:"courier",terminal:"courier",cursive:"times",fantasy:"times",serif:"times"},tM={caption:"times",icon:"times",menu:"times","message-box":"times","small-caption":"times","status-bar":"times"};function tq(t){return[t.stretch,t.style,t.weight,t.family].join(" ")}function tB(t){return t.trimLeft()}var tD,tR,tT,tz=["times"];!function(t){var e,r,i,a,o,c,u,h,l,f=function(t){return t=t||{},this.isStrokeTransparent=t.isStrokeTransparent||!1,this.strokeOpacity=t.strokeOpacity||1,this.strokeStyle=t.strokeStyle||"#000000",this.fillStyle=t.fillStyle||"#000000",this.isFillTransparent=t.isFillTransparent||!1,this.fillOpacity=t.fillOpacity||1,this.font=t.font||"10px sans-serif",this.textBaseline=t.textBaseline||"alphabetic",this.textAlign=t.textAlign||"left",this.lineWidth=t.lineWidth||1,this.lineJoin=t.lineJoin||"miter",this.lineCap=t.lineCap||"butt",this.path=t.path||[],this.transform=void 0!==t.transform?t.transform.clone():new h,this.globalCompositeOperation=t.globalCompositeOperation||"normal",this.globalAlpha=t.globalAlpha||1,this.clip_path=t.clip_path||[],this.currentPoint=t.currentPoint||new c,this.miterLimit=t.miterLimit||10,this.lastPoint=t.lastPoint||new c,this.lineDashOffset=t.lineDashOffset||0,this.lineDash=t.lineDash||[],this.margin=t.margin||[0,0,0,0],this.prevPageLastElemOffset=t.prevPageLastElemOffset||0,this.ignoreClearRect="boolean"!=typeof t.ignoreClearRect||t.ignoreClearRect,this};t.events.push(["initialized",function(){this.context2d=new d(this),e=this.internal.f2,r=this.internal.getCoordinateString,i=this.internal.getVerticalCoordinateString,a=this.internal.getHorizontalCoordinate,o=this.internal.getVerticalCoordinate,c=this.internal.Point,u=this.internal.Rectangle,h=this.internal.Matrix,l=new f}]);var d=function(t){Object.defineProperty(this,"canvas",{get:function(){return{parentNode:!1,style:!1}}}),Object.defineProperty(this,"pdf",{get:function(){return t}});var e=!1;Object.defineProperty(this,"pageWrapXEnabled",{get:function(){return e},set:function(t){e=!!t}});var r=!1;Object.defineProperty(this,"pageWrapYEnabled",{get:function(){return r},set:function(t){r=!!t}});var n=0;Object.defineProperty(this,"posX",{get:function(){return n},set:function(t){isNaN(t)||(n=t)}});var i=0;Object.defineProperty(this,"posY",{get:function(){return i},set:function(t){isNaN(t)||(i=t)}}),Object.defineProperty(this,"margin",{get:function(){return l.margin},set:function(t){var e;"number"==typeof t?e=[t,t,t,t]:((e=[,,,,])[0]=t[0],e[1]=t.length>=2?t[1]:e[0],e[2]=t.length>=3?t[2]:e[0],e[3]=t.length>=4?t[3]:e[1]),l.margin=e}});var a=!1;Object.defineProperty(this,"autoPaging",{get:function(){return a},set:function(t){a=t}});var o=0;Object.defineProperty(this,"lastBreak",{get:function(){return o},set:function(t){o=t}});var s=[];Object.defineProperty(this,"pageBreaks",{get:function(){return s},set:function(t){s=t}}),Object.defineProperty(this,"ctx",{get:function(){return l},set:function(t){t instanceof f&&(l=t)}}),Object.defineProperty(this,"path",{get:function(){return l.path},set:function(t){l.path=t}});var c=[];Object.defineProperty(this,"ctxStack",{get:function(){return c},set:function(t){c=t}}),Object.defineProperty(this,"fillStyle",{get:function(){return this.ctx.fillStyle},set:function(t){var e;e=g(t),this.ctx.fillStyle=e.style,this.ctx.isFillTransparent=0===e.a,this.ctx.fillOpacity=e.a,this.pdf.setFillColor(e.r,e.g,e.b,{a:e.a}),this.pdf.setTextColor(e.r,e.g,e.b,{a:e.a})}}),Object.defineProperty(this,"strokeStyle",{get:function(){return this.ctx.strokeStyle},set:function(t){var e=g(t);this.ctx.strokeStyle=e.style,this.ctx.isStrokeTransparent=0===e.a,this.ctx.strokeOpacity=e.a,0===e.a?this.pdf.setDrawColor(255,255,255):(e.a,this.pdf.setDrawColor(e.r,e.g,e.b))}}),Object.defineProperty(this,"lineCap",{get:function(){return this.ctx.lineCap},set:function(t){-1!==["butt","round","square"].indexOf(t)&&(this.ctx.lineCap=t,this.pdf.setLineCap(t))}}),Object.defineProperty(this,"lineWidth",{get:function(){return this.ctx.lineWidth},set:function(t){isNaN(t)||(this.ctx.lineWidth=t,this.pdf.setLineWidth(t))}}),Object.defineProperty(this,"lineJoin",{get:function(){return this.ctx.lineJoin},set:function(t){-1!==["bevel","round","miter"].indexOf(t)&&(this.ctx.lineJoin=t,this.pdf.setLineJoin(t))}}),Object.defineProperty(this,"miterLimit",{get:function(){return this.ctx.miterLimit},set:function(t){isNaN(t)||(this.ctx.miterLimit=t,this.pdf.setMiterLimit(t))}}),Object.defineProperty(this,"textBaseline",{get:function(){return this.ctx.textBaseline},set:function(t){this.ctx.textBaseline=t}}),Object.defineProperty(this,"textAlign",{get:function(){return this.ctx.textAlign},set:function(t){-1!==["right","end","center","left","start"].indexOf(t)&&(this.ctx.textAlign=t)}});var u=null,h=null;Object.defineProperty(this,"fontFaces",{get:function(){return h},set:function(t){u=null,h=t}}),Object.defineProperty(this,"font",{get:function(){return this.ctx.font},set:function(t){var e;if(this.ctx.font=t,null!==(e=/^\s*(?=(?:(?:[-a-z]+\s*){0,2}(italic|oblique))?)(?=(?:(?:[-a-z]+\s*){0,2}(small-caps))?)(?=(?:(?:[-a-z]+\s*){0,2}(bold(?:er)?|lighter|[1-9]00))?)(?:(?:normal|\1|\2|\3)\s*){0,3}((?:xx?-)?(?:small|large)|medium|smaller|larger|[.\d]+(?:\%|in|[cem]m|ex|p[ctx]))(?:\s*\/\s*(normal|[.\d]+(?:\%|in|[cem]m|ex|p[ctx])))?\s*([-_,\"\'\sa-z]+?)\s*$/i.exec(t))){var r=e[1];e[2];var n=e[3],i=e[4];e[5];var a=e[6],o=/^([.\d]+)((?:%|in|[cem]m|ex|p[ctx]))$/i.exec(i)[2];i="px"===o?Math.floor(parseFloat(i)*this.pdf.internal.scaleFactor):"em"===o?Math.floor(parseFloat(i)*this.pdf.getFontSize()):Math.floor(parseFloat(i)*this.pdf.internal.scaleFactor),this.pdf.setFontSize(i);var s=function(t){var e,r,n=[],i=t.trim();if(""===i)return tz;if(i in tM)return[tM[i]];for(;""!==i;){switch(r=null,e=(i=tB(i)).charAt(0)){case'"':case"'":r=function(t,e){for(var r=0;r<t.length;){if(t.charAt(r)===e)return[t.substring(0,r),t.substring(r+1)];r+=1}return null}(i.substring(1),e);break;default:r=function(t){var e=t.match(/^(-[a-z_]|[a-z_])[a-z0-9_-]*/i);return null===e?null:[e[0],t.substring(e[0].length)]}(i)}if(null===r||(n.push(r[0]),""!==(i=tB(r[1]))&&","!==i.charAt(0)))return tz;i=i.replace(/^,/,"")}return n}(a);if(this.fontFaces){var c=function(t,e,r){for(var n=(r=r||{}).defaultFontFamily||"times",i=Object.assign({},tE,r.genericFontFamilies||{}),a=null,o=null,s=0;s<e.length;++s)if(i[(a=tj(e[s])).family]&&(a.family=i[a.family]),t.hasOwnProperty(a.family)){o=t[a.family];break}if(!(o=o||t[n]))throw Error("Could not find a font-family for the rule '"+tq(a)+"' and default family '"+n+"'.");if(o=function(t,e){if(e[t])return e[t];var r=tF[t],n=r<=tF.normal?-1:1,i=tO(e,tk,r,n);if(!i)throw Error("Could not find a matching font-stretch value for "+t);return i}(a.stretch,o),o=function(t,e){if(e[t])return e[t];for(var r=tP[t],n=0;n<r.length;++n)if(e[r[n]])return e[r[n]];throw Error("Could not find a matching font-style for "+t)}(a.style,o),!(o=function(t,e){if(e[t])return e[t];if(400===t&&e[500])return e[500];if(500===t&&e[400])return e[400];var r=tO(e,tI,tC[t],t<400?-1:1);if(!r)throw Error("Could not find a matching font-weight for value "+t);return r}(a.weight,o)))throw Error("Failed to resolve a font for the rule '"+tq(a)+"'.");return o}(function(t,e){if(null===u){var r,n;u=function(t){for(var e={},r=0;r<t.length;++r){var n=tj(t[r]),i=n.family,a=n.stretch,o=n.style,s=n.weight;e[i]=e[i]||{},e[i][a]=e[i][a]||{},e[i][a][o]=e[i][a][o]||{},e[i][a][o][s]=n}return e}((r=t.getFontList(),n=[],Object.keys(r).forEach(function(t){r[t].forEach(function(e){var r=null;switch(e){case"bold":r={family:t,weight:"bold"};break;case"italic":r={family:t,style:"italic"};break;case"bolditalic":r={family:t,weight:"bold",style:"italic"};break;case"":case"normal":r={family:t}}null!==r&&(r.ref={name:t,style:e},n.push(r))})}),n).concat(e))}return u}(this.pdf,this.fontFaces),s.map(function(t){return{family:t,stretch:"normal",weight:n,style:r}}));this.pdf.setFont(c.ref.name,c.ref.style)}else{var h="";("bold"===n||parseInt(n,10)>=700||"bold"===r)&&(h="bold"),"italic"===r&&(h+="italic"),0===h.length&&(h="normal");for(var l="",f={arial:"Helvetica",Arial:"Helvetica",verdana:"Helvetica",Verdana:"Helvetica",helvetica:"Helvetica",Helvetica:"Helvetica","sans-serif":"Helvetica",fixed:"Courier",monospace:"Courier",terminal:"Courier",cursive:"Times",fantasy:"Times",serif:"Times"},d=0;d<s.length;d++){if(void 0!==this.pdf.internal.getFont(s[d],h,{noFallback:!0,disableWarning:!0})){l=s[d];break}if("bolditalic"===h&&void 0!==this.pdf.internal.getFont(s[d],"bold",{noFallback:!0,disableWarning:!0}))l=s[d],h="bold";else if(void 0!==this.pdf.internal.getFont(s[d],"normal",{noFallback:!0,disableWarning:!0})){l=s[d],h="normal";break}}if(""===l){for(var p=0;p<s.length;p++)if(f[s[p]]){l=f[s[p]];break}}l=""===l?"Times":l,this.pdf.setFont(l,h)}}}}),Object.defineProperty(this,"globalCompositeOperation",{get:function(){return this.ctx.globalCompositeOperation},set:function(t){this.ctx.globalCompositeOperation=t}}),Object.defineProperty(this,"globalAlpha",{get:function(){return this.ctx.globalAlpha},set:function(t){this.ctx.globalAlpha=t}}),Object.defineProperty(this,"lineDashOffset",{get:function(){return this.ctx.lineDashOffset},set:function(t){this.ctx.lineDashOffset=t,T.call(this)}}),Object.defineProperty(this,"lineDash",{get:function(){return this.ctx.lineDash},set:function(t){this.ctx.lineDash=t,T.call(this)}}),Object.defineProperty(this,"ignoreClearRect",{get:function(){return this.ctx.ignoreClearRect},set:function(t){this.ctx.ignoreClearRect=!!t}})};d.prototype.setLineDash=function(t){this.lineDash=t},d.prototype.getLineDash=function(){return this.lineDash.length%2?this.lineDash.concat(this.lineDash):this.lineDash.slice()},d.prototype.fill=function(){A.call(this,"fill",!1)},d.prototype.stroke=function(){A.call(this,"stroke",!1)},d.prototype.beginPath=function(){this.path=[{type:"begin"}]},d.prototype.moveTo=function(t,e){if(isNaN(t)||isNaN(e))throw s.error("jsPDF.context2d.moveTo: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.moveTo");var r=this.ctx.transform.applyToPoint(new c(t,e));this.path.push({type:"mt",x:r.x,y:r.y}),this.ctx.lastPoint=new c(t,e)},d.prototype.closePath=function(){var t=new c(0,0),e=0;for(e=this.path.length-1;-1!==e;e--)if("begin"===this.path[e].type&&"object"===(0,n.A)(this.path[e+1])&&"number"==typeof this.path[e+1].x){t=new c(this.path[e+1].x,this.path[e+1].y);break}this.path.push({type:"close"}),this.ctx.lastPoint=new c(t.x,t.y)},d.prototype.lineTo=function(t,e){if(isNaN(t)||isNaN(e))throw s.error("jsPDF.context2d.lineTo: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.lineTo");var r=this.ctx.transform.applyToPoint(new c(t,e));this.path.push({type:"lt",x:r.x,y:r.y}),this.ctx.lastPoint=new c(r.x,r.y)},d.prototype.clip=function(){this.ctx.clip_path=JSON.parse(JSON.stringify(this.path)),A.call(this,null,!0)},d.prototype.quadraticCurveTo=function(t,e,r,n){if(isNaN(r)||isNaN(n)||isNaN(t)||isNaN(e))throw s.error("jsPDF.context2d.quadraticCurveTo: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.quadraticCurveTo");var i=this.ctx.transform.applyToPoint(new c(r,n)),a=this.ctx.transform.applyToPoint(new c(t,e));this.path.push({type:"qct",x1:a.x,y1:a.y,x:i.x,y:i.y}),this.ctx.lastPoint=new c(i.x,i.y)},d.prototype.bezierCurveTo=function(t,e,r,n,i,a){if(isNaN(i)||isNaN(a)||isNaN(t)||isNaN(e)||isNaN(r)||isNaN(n))throw s.error("jsPDF.context2d.bezierCurveTo: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.bezierCurveTo");var o=this.ctx.transform.applyToPoint(new c(i,a)),u=this.ctx.transform.applyToPoint(new c(t,e)),h=this.ctx.transform.applyToPoint(new c(r,n));this.path.push({type:"bct",x1:u.x,y1:u.y,x2:h.x,y2:h.y,x:o.x,y:o.y}),this.ctx.lastPoint=new c(o.x,o.y)},d.prototype.arc=function(t,e,r,n,i,a){if(isNaN(t)||isNaN(e)||isNaN(r)||isNaN(n)||isNaN(i))throw s.error("jsPDF.context2d.arc: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.arc");if(a=!!a,!this.ctx.transform.isIdentity){var o=this.ctx.transform.applyToPoint(new c(t,e));t=o.x,e=o.y;var u=this.ctx.transform.applyToPoint(new c(0,r)),h=this.ctx.transform.applyToPoint(new c(0,0));r=Math.sqrt(Math.pow(u.x-h.x,2)+Math.pow(u.y-h.y,2))}Math.abs(i-n)>=2*Math.PI&&(n=0,i=2*Math.PI),this.path.push({type:"arc",x:t,y:e,radius:r,startAngle:n,endAngle:i,counterclockwise:a})},d.prototype.arcTo=function(t,e,r,n,i){throw Error("arcTo not implemented.")},d.prototype.rect=function(t,e,r,n){if(isNaN(t)||isNaN(e)||isNaN(r)||isNaN(n))throw s.error("jsPDF.context2d.rect: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.rect");this.moveTo(t,e),this.lineTo(t+r,e),this.lineTo(t+r,e+n),this.lineTo(t,e+n),this.lineTo(t,e),this.lineTo(t+r,e),this.lineTo(t,e)},d.prototype.fillRect=function(t,e,r,n){if(isNaN(t)||isNaN(e)||isNaN(r)||isNaN(n))throw s.error("jsPDF.context2d.fillRect: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.fillRect");if(!m.call(this)){var i={};"butt"!==this.lineCap&&(i.lineCap=this.lineCap,this.lineCap="butt"),"miter"!==this.lineJoin&&(i.lineJoin=this.lineJoin,this.lineJoin="miter"),this.beginPath(),this.rect(t,e,r,n),this.fill(),i.hasOwnProperty("lineCap")&&(this.lineCap=i.lineCap),i.hasOwnProperty("lineJoin")&&(this.lineJoin=i.lineJoin)}},d.prototype.strokeRect=function(t,e,r,n){if(isNaN(t)||isNaN(e)||isNaN(r)||isNaN(n))throw s.error("jsPDF.context2d.strokeRect: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.strokeRect");v.call(this)||(this.beginPath(),this.rect(t,e,r,n),this.stroke())},d.prototype.clearRect=function(t,e,r,n){if(isNaN(t)||isNaN(e)||isNaN(r)||isNaN(n))throw s.error("jsPDF.context2d.clearRect: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.clearRect");this.ignoreClearRect||(this.fillStyle="#ffffff",this.fillRect(t,e,r,n))},d.prototype.save=function(t){t="boolean"!=typeof t||t;for(var e=this.pdf.internal.getCurrentPageInfo().pageNumber,r=0;r<this.pdf.internal.getNumberOfPages();r++)this.pdf.setPage(r+1),this.pdf.internal.out("q");if(this.pdf.setPage(e),t){this.ctx.fontSize=this.pdf.internal.getFontSize();var n=new f(this.ctx);this.ctxStack.push(this.ctx),this.ctx=n}},d.prototype.restore=function(t){t="boolean"!=typeof t||t;for(var e=this.pdf.internal.getCurrentPageInfo().pageNumber,r=0;r<this.pdf.internal.getNumberOfPages();r++)this.pdf.setPage(r+1),this.pdf.internal.out("Q");this.pdf.setPage(e),t&&0!==this.ctxStack.length&&(this.ctx=this.ctxStack.pop(),this.fillStyle=this.ctx.fillStyle,this.strokeStyle=this.ctx.strokeStyle,this.font=this.ctx.font,this.lineCap=this.ctx.lineCap,this.lineWidth=this.ctx.lineWidth,this.lineJoin=this.ctx.lineJoin,this.lineDash=this.ctx.lineDash,this.lineDashOffset=this.ctx.lineDashOffset)},d.prototype.toDataURL=function(){throw Error("toDataUrl not implemented.")};var g=function(t){var e,r,n,i;if(!0===t.isCanvasGradient&&(t=t.getColor()),!t)return{r:0,g:0,b:0,a:0,style:t};if(/transparent|rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*0+\s*\)/.test(t))e=0,r=0,n=0,i=0;else{var a=/rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/.exec(t);if(null!==a)e=parseInt(a[1]),r=parseInt(a[2]),n=parseInt(a[3]),i=1;else if(null!==(a=/rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)/.exec(t)))e=parseInt(a[1]),r=parseInt(a[2]),n=parseInt(a[3]),i=parseFloat(a[4]);else{if(i=1,"string"==typeof t&&"#"!==t.charAt(0)){var o=new p(t);t=o.ok?o.toHex():"#000000"}4===t.length?(e=t.substring(1,2),e+=e,r=t.substring(2,3),r+=r,n=t.substring(3,4),n+=n):(e=t.substring(1,3),r=t.substring(3,5),n=t.substring(5,7)),e=parseInt(e,16),r=parseInt(r,16),n=parseInt(n,16)}}return{r:e,g:r,b:n,a:i,style:t}},m=function(){return this.ctx.isFillTransparent||0==this.globalAlpha},v=function(){return!!(this.ctx.isStrokeTransparent||0==this.globalAlpha)};d.prototype.fillText=function(t,e,r,n){if(isNaN(e)||isNaN(r)||"string"!=typeof t)throw s.error("jsPDF.context2d.fillText: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.fillText");if(n=isNaN(n)?void 0:n,!m.call(this)){var i=B(this.ctx.transform.rotation);C.call(this,{text:t,x:e,y:r,scale:this.ctx.transform.scaleX,angle:i,align:this.textAlign,maxWidth:n})}},d.prototype.strokeText=function(t,e,r,n){if(isNaN(e)||isNaN(r)||"string"!=typeof t)throw s.error("jsPDF.context2d.strokeText: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.strokeText");if(!v.call(this)){n=isNaN(n)?void 0:n;var i=B(this.ctx.transform.rotation);C.call(this,{text:t,x:e,y:r,scale:this.ctx.transform.scaleX,renderingMode:"stroke",angle:i,align:this.textAlign,maxWidth:n})}},d.prototype.measureText=function(t){if("string"!=typeof t)throw s.error("jsPDF.context2d.measureText: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.measureText");var e=this.pdf,r=this.pdf.internal.scaleFactor,n=e.internal.getFontSize(),i=e.getStringUnitWidth(t)*n/e.internal.scaleFactor;return new function(t){var e=(t=t||{}).width||0;return Object.defineProperty(this,"width",{get:function(){return e}}),this}({width:i*=Math.round(96*r/72*1e4)/1e4})},d.prototype.scale=function(t,e){if(isNaN(t)||isNaN(e))throw s.error("jsPDF.context2d.scale: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.scale");var r=new h(t,0,0,e,0,0);this.ctx.transform=this.ctx.transform.multiply(r)},d.prototype.rotate=function(t){if(isNaN(t))throw s.error("jsPDF.context2d.rotate: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.rotate");var e=new h(Math.cos(t),Math.sin(t),-Math.sin(t),Math.cos(t),0,0);this.ctx.transform=this.ctx.transform.multiply(e)},d.prototype.translate=function(t,e){if(isNaN(t)||isNaN(e))throw s.error("jsPDF.context2d.translate: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.translate");var r=new h(1,0,0,1,t,e);this.ctx.transform=this.ctx.transform.multiply(r)},d.prototype.transform=function(t,e,r,n,i,a){if(isNaN(t)||isNaN(e)||isNaN(r)||isNaN(n)||isNaN(i)||isNaN(a))throw s.error("jsPDF.context2d.transform: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.transform");var o=new h(t,e,r,n,i,a);this.ctx.transform=this.ctx.transform.multiply(o)},d.prototype.setTransform=function(t,e,r,n,i,a){t=isNaN(t)?1:t,e=isNaN(e)?0:e,r=isNaN(r)?0:r,n=isNaN(n)?1:n,i=isNaN(i)?0:i,a=isNaN(a)?0:a,this.ctx.transform=new h(t,e,r,n,i,a)};var b=function(){return this.margin[0]>0||this.margin[1]>0||this.margin[2]>0||this.margin[3]>0};d.prototype.drawImage=function(t,e,r,n,i,a,o,s,c){var l=this.pdf.getImageProperties(t),f=1,d=1,p=1,g=1;void 0!==n&&void 0!==s&&(p=s/n,g=c/i,f=l.width/n*s/n,d=l.height/i*c/i),void 0===a&&(a=e,o=r,e=0,r=0),void 0!==n&&void 0===s&&(s=n,c=i),void 0===n&&void 0===s&&(s=l.width,c=l.height);for(var m,v=this.ctx.transform.decompose(),w=B(v.rotate.shx),A=new h,S=(A=(A=(A=A.multiply(v.translate)).multiply(v.skew)).multiply(v.scale)).applyToRectangle(new u(a-e*p,o-r*g,n*f,i*d)),_=y.call(this,S),P=[],k=0;k<_.length;k+=1)-1===P.indexOf(_[k])&&P.push(_[k]);if(N(P),this.autoPaging)for(var F=P[0],I=P[P.length-1],C=F;C<I+1;C++){this.pdf.setPage(C);var j=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],O=1===C?this.posY+this.margin[0]:this.margin[0],E=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],M=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],q=1===C?0:E+(C-2)*M;if(0!==this.ctx.clip_path.length){var D=this.path;m=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=x(m,this.posX+this.margin[3],-q+O+this.ctx.prevPageLastElemOffset),L.call(this,"fill",!0),this.path=D}var R=JSON.parse(JSON.stringify(S));R=x([R],this.posX+this.margin[3],-q+O+this.ctx.prevPageLastElemOffset)[0];var T=(C>F||C<I)&&b.call(this);T&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],j,M,null).clip().discardPath()),this.pdf.addImage(t,"JPEG",R.x,R.y,R.w,R.h,null,null,w),T&&this.pdf.restoreGraphicsState()}else this.pdf.addImage(t,"JPEG",S.x,S.y,S.w,S.h,null,null,w)};var y=function(t,e,r){var n=[];e=e||this.pdf.internal.pageSize.width,r=r||this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2];var i=this.posY+this.ctx.prevPageLastElemOffset;switch(t.type){default:case"mt":case"lt":n.push(Math.floor((t.y+i)/r)+1);break;case"arc":n.push(Math.floor((t.y+i-t.radius)/r)+1),n.push(Math.floor((t.y+i+t.radius)/r)+1);break;case"qct":var a=D(this.ctx.lastPoint.x,this.ctx.lastPoint.y,t.x1,t.y1,t.x,t.y);n.push(Math.floor((a.y+i)/r)+1),n.push(Math.floor((a.y+a.h+i)/r)+1);break;case"bct":var o=R(this.ctx.lastPoint.x,this.ctx.lastPoint.y,t.x1,t.y1,t.x2,t.y2,t.x,t.y);n.push(Math.floor((o.y+i)/r)+1),n.push(Math.floor((o.y+o.h+i)/r)+1);break;case"rect":n.push(Math.floor((t.y+i)/r)+1),n.push(Math.floor((t.y+t.h+i)/r)+1)}for(var s=0;s<n.length;s+=1)for(;this.pdf.internal.getNumberOfPages()<n[s];)w.call(this);return n},w=function(){var t=this.fillStyle,e=this.strokeStyle,r=this.font,n=this.lineCap,i=this.lineWidth,a=this.lineJoin;this.pdf.addPage(),this.fillStyle=t,this.strokeStyle=e,this.font=r,this.lineCap=n,this.lineWidth=i,this.lineJoin=a},x=function(t,e,r){for(var n=0;n<t.length;n++)switch(t[n].type){case"bct":t[n].x2+=e,t[n].y2+=r;case"qct":t[n].x1+=e,t[n].y1+=r;default:t[n].x+=e,t[n].y+=r}return t},N=function(t){return t.sort(function(t,e){return t-e})},A=function(t,e){for(var r,n,i=this.fillStyle,a=this.strokeStyle,o=this.lineCap,s=this.lineWidth,c=Math.abs(s*this.ctx.transform.scaleX),u=this.lineJoin,h=JSON.parse(JSON.stringify(this.path)),l=JSON.parse(JSON.stringify(this.path)),f=[],d=0;d<l.length;d++)if(void 0!==l[d].x)for(var p=y.call(this,l[d]),g=0;g<p.length;g+=1)-1===f.indexOf(p[g])&&f.push(p[g]);for(var m=0;m<f.length;m++)for(;this.pdf.internal.getNumberOfPages()<f[m];)w.call(this);if(N(f),this.autoPaging)for(var v=f[0],A=f[f.length-1],S=v;S<A+1;S++){this.pdf.setPage(S),this.fillStyle=i,this.strokeStyle=a,this.lineCap=o,this.lineWidth=c,this.lineJoin=u;var _=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],P=1===S?this.posY+this.margin[0]:this.margin[0],k=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],F=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],I=1===S?0:k+(S-2)*F;if(0!==this.ctx.clip_path.length){var C=this.path;r=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=x(r,this.posX+this.margin[3],-I+P+this.ctx.prevPageLastElemOffset),L.call(this,t,!0),this.path=C}if(n=JSON.parse(JSON.stringify(h)),this.path=x(n,this.posX+this.margin[3],-I+P+this.ctx.prevPageLastElemOffset),!1===e||0===S){var j=(S>v||S<A)&&b.call(this);j&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],_,F,null).clip().discardPath()),L.call(this,t,e),j&&this.pdf.restoreGraphicsState()}this.lineWidth=s}else this.lineWidth=c,L.call(this,t,e),this.lineWidth=s;this.path=h},L=function(t,e){if(("stroke"!==t||e||!v.call(this))&&("stroke"===t||e||!m.call(this))){for(var r,n,i=[],a=this.path,o=0;o<a.length;o++){var s=a[o];switch(s.type){case"begin":i.push({begin:!0});break;case"close":i.push({close:!0});break;case"mt":i.push({start:s,deltas:[],abs:[]});break;case"lt":var c=i.length;if(a[o-1]&&!isNaN(a[o-1].x)&&(r=[s.x-a[o-1].x,s.y-a[o-1].y],c>0)){for(;c>=0;c--)if(!0!==i[c-1].close&&!0!==i[c-1].begin){i[c-1].deltas.push(r),i[c-1].abs.push(s);break}}break;case"bct":r=[s.x1-a[o-1].x,s.y1-a[o-1].y,s.x2-a[o-1].x,s.y2-a[o-1].y,s.x-a[o-1].x,s.y-a[o-1].y],i[i.length-1].deltas.push(r);break;case"qct":var u=a[o-1].x+2/3*(s.x1-a[o-1].x),h=a[o-1].y+2/3*(s.y1-a[o-1].y),l=s.x+2/3*(s.x1-s.x),f=s.y+2/3*(s.y1-s.y),d=s.x,p=s.y;r=[u-a[o-1].x,h-a[o-1].y,l-a[o-1].x,f-a[o-1].y,d-a[o-1].x,p-a[o-1].y],i[i.length-1].deltas.push(r);break;case"arc":i.push({deltas:[],abs:[],arc:!0}),Array.isArray(i[i.length-1].abs)&&i[i.length-1].abs.push(s)}}n=e?null:"stroke"===t?"stroke":"fill";for(var g=!1,b=0;b<i.length;b++)if(i[b].arc)for(var y=i[b].abs,w=0;w<y.length;w++){var x=y[w];"arc"===x.type?P.call(this,x.x,x.y,x.radius,x.startAngle,x.endAngle,x.counterclockwise,void 0,e,!g):j.call(this,x.x,x.y),g=!0}else if(!0===i[b].close)this.pdf.internal.out("h"),g=!1;else if(!0!==i[b].begin){var N=i[b].start.x,A=i[b].start.y;O.call(this,i[b].deltas,N,A),g=!0}n&&k.call(this,n),e&&F.call(this)}},S=function(t){var e=this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor,r=e*(this.pdf.internal.getLineHeightFactor()-1);switch(this.ctx.textBaseline){case"bottom":return t-r;case"top":return t+e-r;case"hanging":return t+e-2*r;case"middle":return t+e/2-r;default:return t}},_=function(t){return t+this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor*(this.pdf.internal.getLineHeightFactor()-1)};d.prototype.createLinearGradient=function(){var t=function(){};return t.colorStops=[],t.addColorStop=function(t,e){this.colorStops.push([t,e])},t.getColor=function(){return 0===this.colorStops.length?"#000000":this.colorStops[0][1]},t.isCanvasGradient=!0,t},d.prototype.createPattern=function(){return this.createLinearGradient()},d.prototype.createRadialGradient=function(){return this.createLinearGradient()};var P=function(t,e,r,n,i,a,o,s,c){for(var u=M.call(this,r,n,i,a),h=0;h<u.length;h++){var l=u[h];0===h&&(c?I.call(this,l.x1+t,l.y1+e):j.call(this,l.x1+t,l.y1+e)),E.call(this,t,e,l.x2,l.y2,l.x3,l.y3,l.x4,l.y4)}s?F.call(this):k.call(this,o)},k=function(t){switch(t){case"stroke":this.pdf.internal.out("S");break;case"fill":this.pdf.internal.out("f")}},F=function(){this.pdf.clip(),this.pdf.discardPath()},I=function(t,e){this.pdf.internal.out(r(t)+" "+i(e)+" m")},C=function(t){switch(t.align){case"right":case"end":s="right";break;case"center":s="center";break;default:s="left"}var e=this.pdf.getTextDimensions(t.text),r=S.call(this,t.y),n=_.call(this,r)-e.h,i=this.ctx.transform.applyToPoint(new c(t.x,r)),a=this.ctx.transform.decompose(),o=new h;o=(o=(o=o.multiply(a.translate)).multiply(a.skew)).multiply(a.scale);for(var s,l,f,d,p=this.ctx.transform.applyToRectangle(new u(t.x,r,e.w,e.h)),g=o.applyToRectangle(new u(t.x,n,e.w,e.h)),m=y.call(this,g),v=[],w=0;w<m.length;w+=1)-1===v.indexOf(m[w])&&v.push(m[w]);if(N(v),this.autoPaging)for(var A=v[0],P=v[v.length-1],k=A;k<P+1;k++){this.pdf.setPage(k);var F=1===k?this.posY+this.margin[0]:this.margin[0],I=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],C=this.pdf.internal.pageSize.height-this.margin[2],j=C-this.margin[0],O=this.pdf.internal.pageSize.width-this.margin[1],E=O-this.margin[3],M=1===k?0:I+(k-2)*j;if(0!==this.ctx.clip_path.length){var q=this.path;l=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=x(l,this.posX+this.margin[3],-1*M+F),L.call(this,"fill",!0),this.path=q}var B=x([JSON.parse(JSON.stringify(g))],this.posX+this.margin[3],-M+F+this.ctx.prevPageLastElemOffset)[0];t.scale>=.01&&(f=this.pdf.internal.getFontSize(),this.pdf.setFontSize(f*t.scale),d=this.lineWidth,this.lineWidth=d*t.scale);var D="text"!==this.autoPaging;if(D||B.y+B.h<=C){if(D||B.y>=F&&B.x<=O){var R=D?t.text:this.pdf.splitTextToSize(t.text,t.maxWidth||O-B.x)[0],T=x([JSON.parse(JSON.stringify(p))],this.posX+this.margin[3],-M+F+this.ctx.prevPageLastElemOffset)[0],z=D&&(k>A||k<P)&&b.call(this);z&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],E,j,null).clip().discardPath()),this.pdf.text(R,T.x,T.y,{angle:t.angle,align:s,renderingMode:t.renderingMode}),z&&this.pdf.restoreGraphicsState()}}else B.y<C&&(this.ctx.prevPageLastElemOffset+=C-B.y);t.scale>=.01&&(this.pdf.setFontSize(f),this.lineWidth=d)}else t.scale>=.01&&(f=this.pdf.internal.getFontSize(),this.pdf.setFontSize(f*t.scale),d=this.lineWidth,this.lineWidth=d*t.scale),this.pdf.text(t.text,i.x+this.posX,i.y+this.posY,{angle:t.angle,align:s,renderingMode:t.renderingMode,maxWidth:t.maxWidth}),t.scale>=.01&&(this.pdf.setFontSize(f),this.lineWidth=d)},j=function(t,e,n,a){n=n||0,a=a||0,this.pdf.internal.out(r(t+n)+" "+i(e+a)+" l")},O=function(t,e,r){return this.pdf.lines(t,e,r,null,null)},E=function(t,r,n,i,s,c,u,h){this.pdf.internal.out([e(a(n+t)),e(o(i+r)),e(a(s+t)),e(o(c+r)),e(a(u+t)),e(o(h+r)),"c"].join(" "))},M=function(t,e,r,n){for(var i=2*Math.PI,a=Math.PI/2;e>r;)e-=i;var o=Math.abs(r-e);o<i&&n&&(o=i-o);for(var s=[],c=n?-1:1,u=e;o>1e-5;){var h=u+c*Math.min(o,a);s.push(q.call(this,t,u,h)),o-=Math.abs(h-u),u=h}return s},q=function(t,e,r){var n=(r-e)/2,i=t*Math.cos(n),a=t*Math.sin(n),o=-a,s=i*i+o*o,c=s+i*i+o*a,u=4/3*(Math.sqrt(2*s*c)-c)/(i*a-o*i),h=i-u*o,l=o+u*i,f=-l,d=n+e,p=Math.cos(d),g=Math.sin(d);return{x1:t*Math.cos(e),y1:t*Math.sin(e),x2:h*p-l*g,y2:h*g+l*p,x3:h*p-f*g,y3:h*g+f*p,x4:t*Math.cos(r),y4:t*Math.sin(r)}},B=function(t){return 180*t/Math.PI},D=function(t,e,r,n,i,a){var o=t+.5*(r-t),s=e+.5*(n-e),c=i+.5*(r-i),h=a+.5*(n-a),l=Math.min(t,i,o,c),f=Math.min(e,a,s,h);return new u(l,f,Math.max(t,i,o,c)-l,Math.max(e,a,s,h)-f)},R=function(t,e,r,n,i,a,o,s){var c,h,l,f,d,p,g,m,v,b,y,w,x,N,A=r-t,L=n-e,S=i-r,_=a-n,P=o-i,k=s-a;for(h=0;h<41;h++)v=(g=(l=t+(c=h/40)*A)+c*((d=r+c*S)-l))+c*(d+c*(i+c*P-d)-g),b=(m=(f=e+c*L)+c*((p=n+c*_)-f))+c*(p+c*(a+c*k-p)-m),0==h?(y=v,w=b,x=v,N=b):(y=Math.min(y,v),w=Math.min(w,b),x=Math.max(x,v),N=Math.max(N,b));return new u(Math.round(y),Math.round(w),Math.round(x-y),Math.round(N-w))},T=function(){if(this.prevLineDash||this.ctx.lineDash.length||this.ctx.lineDashOffset){var t=JSON.stringify({lineDash:this.ctx.lineDash,lineDashOffset:this.ctx.lineDashOffset});this.prevLineDash!==t&&(this.pdf.setLineDash(this.ctx.lineDash,this.ctx.lineDashOffset),this.prevLineDash=t)}}}(q.API),function(t){var e=function(t){var e,r,n,i,a,o,s,c,u,h;for(/[^\x00-\xFF]/.test(t),r=[],n=0,i=(t+=e="\0\0\0\0".slice(t.length%4||4)).length;i>n;n+=4)0!==(a=(t.charCodeAt(n)<<24)+(t.charCodeAt(n+1)<<16)+(t.charCodeAt(n+2)<<8)+t.charCodeAt(n+3))?(o=(a=((a=((a=((a=(a-(h=a%85))/85)-(u=a%85))/85)-(c=a%85))/85)-(s=a%85))/85)%85,r.push(o+33,s+33,c+33,u+33,h+33)):r.push(122);return function(t,e){for(var r=e;r>0;r--)t.pop()}(r,e.length),String.fromCharCode.apply(String,r)+"~>"},r=function(t){var e,r,n,i,a,o=String,s="length",c="charCodeAt",u="slice",h="replace";for(t[u](-2),t=t[u](0,-2)[h](/\s/g,"")[h]("z","!!!!!"),n=[],i=0,a=(t+=e="uuuuu"[u](t[s]%5||5))[s];a>i;i+=5)r=0x31c84b1*(t[c](i)-33)+614125*(t[c](i+1)-33)+7225*(t[c](i+2)-33)+85*(t[c](i+3)-33)+(t[c](i+4)-33),n.push(255&r>>24,255&r>>16,255&r>>8,255&r);return function(t,e){for(var r=e;r>0;r--)t.pop()}(n,e[s]),o.fromCharCode.apply(o,n)},n=function(t){var e=new RegExp(/^([0-9A-Fa-f]{2})+$/);if(-1!==(t=t.replace(/\s/g,"")).indexOf(">")&&(t=t.substr(0,t.indexOf(">"))),t.length%2&&(t+="0"),!1===e.test(t))return"";for(var r="",n=0;n<t.length;n+=2)r+=String.fromCharCode("0x"+(t[n]+t[n+1]));return r},a=function(t){for(var e=new Uint8Array(t.length),r=t.length;r--;)e[r]=t.charCodeAt(r);return t=(e=(0,i.$)(e)).reduce(function(t,e){return t+String.fromCharCode(e)},"")};t.processDataByFilters=function(t,i){var o=0,s=t||"",c=[];for("string"==typeof(i=i||[])&&(i=[i]),o=0;o<i.length;o+=1)switch(i[o]){case"ASCII85Decode":case"/ASCII85Decode":s=r(s),c.push("/ASCII85Encode");break;case"ASCII85Encode":case"/ASCII85Encode":s=e(s),c.push("/ASCII85Decode");break;case"ASCIIHexDecode":case"/ASCIIHexDecode":s=n(s),c.push("/ASCIIHexEncode");break;case"ASCIIHexEncode":case"/ASCIIHexEncode":s=s.split("").map(function(t){return("0"+t.charCodeAt().toString(16)).slice(-2)}).join("")+">",c.push("/ASCIIHexDecode");break;case"FlateEncode":case"/FlateEncode":s=a(s),c.push("/FlateDecode");break;default:throw Error('The filter: "'+i[o]+'" is not implemented')}return{data:s,reverseChain:c.reverse().join(" ")}}}(q.API),function(t){t.loadFile=function(t,e,r){var n=e,i=r;n=!1!==n,i="function"==typeof i?i:function(){};var a=void 0;try{a=function(t,e,r){var n=new XMLHttpRequest,i=0,a=function(t){var e=t.length,r=[],n=String.fromCharCode;for(i=0;i<e;i+=1)r.push(n(255&t.charCodeAt(i)));return r.join("")};if(n.open("GET",t,!e),n.overrideMimeType("text/plain; charset=x-user-defined"),!1===e&&(n.onload=function(){200===n.status?r(a(this.responseText)):r(void 0)}),n.send(null),e&&200===n.status)return a(n.responseText)}(t,n,i)}catch(t){}return a},t.loadImageFile=t.loadFile}(q.API),function(t){function e(){return(a.html2canvas?Promise.resolve(a.html2canvas):Promise.resolve().then(r.t.bind(r,52699,23))).catch(function(t){return Promise.reject(Error("Could not load html2canvas: "+t))}).then(function(t){return t.default?t.default:t})}function i(){return(a.DOMPurify?Promise.resolve(a.DOMPurify):r.e(822).then(r.bind(r,10822))).catch(function(t){return Promise.reject(Error("Could not load dompurify: "+t))}).then(function(t){return t.default?t.default:t})}var o=function(t){var e=(0,n.A)(t);return"undefined"===e?"undefined":"string"===e||t instanceof String?"string":"number"===e||t instanceof Number?"number":"function"===e||t instanceof Function?"function":t&&t.constructor===Array?"array":t&&1===t.nodeType?"element":"object"===e?"object":"unknown"},s=function(t,e){var r=document.createElement(t);for(var n in e.className&&(r.className=e.className),e.innerHTML&&e.dompurify&&(r.innerHTML=e.dompurify.sanitize(e.innerHTML)),e.style)r.style[n]=e.style[n];return r},c=function t(e){var r=Object.assign(t.convert(Promise.resolve()),JSON.parse(JSON.stringify(t.template))),n=t.convert(Promise.resolve(),r);return(n=n.setProgress(1,t,1,[t])).set(e)};(c.prototype=Object.create(Promise.prototype)).constructor=c,c.convert=function(t,e){return t.__proto__=e||c.prototype,t},c.template={prop:{src:null,container:null,overlay:null,canvas:null,img:null,pdf:null,pageSize:null,callback:function(){}},progress:{val:0,state:null,n:0,stack:[]},opt:{filename:"file.pdf",margin:[0,0,0,0],enableLinks:!0,x:0,y:0,html2canvas:{},jsPDF:{},backgroundColor:"transparent"}},c.prototype.from=function(t,e){return this.then(function(){switch(e=e||function(t){switch(o(t)){case"string":return"string";case"element":return"canvas"===t.nodeName.toLowerCase()?"canvas":"element";default:return"unknown"}}(t)){case"string":return this.then(i).then(function(e){return this.set({src:s("div",{innerHTML:t,dompurify:e})})});case"element":return this.set({src:t});case"canvas":return this.set({canvas:t});case"img":return this.set({img:t});default:return this.error("Unknown source type.")}})},c.prototype.to=function(t){switch(t){case"container":return this.toContainer();case"canvas":return this.toCanvas();case"img":return this.toImg();case"pdf":return this.toPdf();default:return this.error("Invalid target.")}},c.prototype.toContainer=function(){return this.thenList([function(){return this.prop.src||this.error("Cannot duplicate - no source HTML.")},function(){return this.prop.pageSize||this.setPageSize()}]).then(function(){var t={position:"relative",display:"inline-block",width:("number"!=typeof this.opt.width||isNaN(this.opt.width)||"number"!=typeof this.opt.windowWidth||isNaN(this.opt.windowWidth)?Math.max(this.prop.src.clientWidth,this.prop.src.scrollWidth,this.prop.src.offsetWidth):this.opt.windowWidth)+"px",left:0,right:0,top:0,margin:"auto",backgroundColor:this.opt.backgroundColor},e=function t(e,r){for(var n=3===e.nodeType?document.createTextNode(e.nodeValue):e.cloneNode(!1),i=e.firstChild;i;i=i.nextSibling)!0!==r&&1===i.nodeType&&"SCRIPT"===i.nodeName||n.appendChild(t(i,r));return 1===e.nodeType&&("CANVAS"===e.nodeName?(n.width=e.width,n.height=e.height,n.getContext("2d").drawImage(e,0,0)):"TEXTAREA"!==e.nodeName&&"SELECT"!==e.nodeName||(n.value=e.value),n.addEventListener("load",function(){n.scrollTop=e.scrollTop,n.scrollLeft=e.scrollLeft},!0)),n}(this.prop.src,this.opt.html2canvas.javascriptEnabled);"BODY"===e.tagName&&(t.height=Math.max(document.body.scrollHeight,document.body.offsetHeight,document.documentElement.clientHeight,document.documentElement.scrollHeight,document.documentElement.offsetHeight)+"px"),this.prop.overlay=s("div",{className:"html2pdf__overlay",style:{position:"fixed",overflow:"hidden",zIndex:1e3,left:"-100000px",right:0,bottom:0,top:0}}),this.prop.container=s("div",{className:"html2pdf__container",style:t}),this.prop.container.appendChild(e),this.prop.container.firstChild.appendChild(s("div",{style:{clear:"both",border:"0 none transparent",margin:0,padding:0,height:0}})),this.prop.container.style.float="none",this.prop.overlay.appendChild(this.prop.container),document.body.appendChild(this.prop.overlay),this.prop.container.firstChild.style.position="relative",this.prop.container.height=Math.max(this.prop.container.firstChild.clientHeight,this.prop.container.firstChild.scrollHeight,this.prop.container.firstChild.offsetHeight)+"px"})},c.prototype.toCanvas=function(){var t=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(t).then(e).then(function(t){var e=Object.assign({},this.opt.html2canvas);return delete e.onrendered,t(this.prop.container,e)}).then(function(t){(this.opt.html2canvas.onrendered||function(){})(t),this.prop.canvas=t,document.body.removeChild(this.prop.overlay)})},c.prototype.toContext2d=function(){var t=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(t).then(e).then(function(t){var e=this.opt.jsPDF,r=this.opt.fontFaces,n=Object.assign({async:!0,allowTaint:!0,scale:"number"!=typeof this.opt.width||isNaN(this.opt.width)||"number"!=typeof this.opt.windowWidth||isNaN(this.opt.windowWidth)?1:this.opt.width/this.opt.windowWidth,scrollX:this.opt.scrollX||0,scrollY:this.opt.scrollY||0,backgroundColor:"#ffffff",imageTimeout:15e3,logging:!0,proxy:null,removeContainer:!0,foreignObjectRendering:!1,useCORS:!1},this.opt.html2canvas);if(delete n.onrendered,e.context2d.autoPaging=void 0===this.opt.autoPaging||this.opt.autoPaging,e.context2d.posX=this.opt.x,e.context2d.posY=this.opt.y,e.context2d.margin=this.opt.margin,e.context2d.fontFaces=r,r)for(var i=0;i<r.length;++i){var a=r[i],o=a.src.find(function(t){return"truetype"===t.format});o&&e.addFont(o.url,a.ref.name,a.ref.style)}return n.windowHeight=n.windowHeight||0,n.windowHeight=0==n.windowHeight?Math.max(this.prop.container.clientHeight,this.prop.container.scrollHeight,this.prop.container.offsetHeight):n.windowHeight,e.context2d.save(!0),t(this.prop.container,n)}).then(function(t){this.opt.jsPDF.context2d.restore(!0),(this.opt.html2canvas.onrendered||function(){})(t),this.prop.canvas=t,document.body.removeChild(this.prop.overlay)})},c.prototype.toImg=function(){return this.thenList([function(){return this.prop.canvas||this.toCanvas()}]).then(function(){var t=this.prop.canvas.toDataURL("image/"+this.opt.image.type,this.opt.image.quality);this.prop.img=document.createElement("img"),this.prop.img.src=t})},c.prototype.toPdf=function(){return this.thenList([function(){return this.toContext2d()}]).then(function(){this.prop.pdf=this.prop.pdf||this.opt.jsPDF})},c.prototype.output=function(t,e,r){return"img"===(r=r||"pdf").toLowerCase()||"image"===r.toLowerCase()?this.outputImg(t,e):this.outputPdf(t,e)},c.prototype.outputPdf=function(t,e){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){return this.prop.pdf.output(t,e)})},c.prototype.outputImg=function(t){return this.thenList([function(){return this.prop.img||this.toImg()}]).then(function(){switch(t){case void 0:case"img":return this.prop.img;case"datauristring":case"dataurlstring":return this.prop.img.src;case"datauri":case"dataurl":return document.location.href=this.prop.img.src;default:throw'Image output type "'+t+'" is not supported.'}})},c.prototype.save=function(t){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).set(t?{filename:t}:null).then(function(){this.prop.pdf.save(this.opt.filename)})},c.prototype.doCallback=function(){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){this.prop.callback(this.prop.pdf)})},c.prototype.set=function(t){if("object"!==o(t))return this;var e=Object.keys(t||{}).map(function(e){if(e in c.template.prop)return function(){this.prop[e]=t[e]};switch(e){case"margin":return this.setMargin.bind(this,t.margin);case"jsPDF":return function(){return this.opt.jsPDF=t.jsPDF,this.setPageSize()};case"pageSize":return this.setPageSize.bind(this,t.pageSize);default:return function(){this.opt[e]=t[e]}}},this);return this.then(function(){return this.thenList(e)})},c.prototype.get=function(t,e){return this.then(function(){var r=t in c.template.prop?this.prop[t]:this.opt[t];return e?e(r):r})},c.prototype.setMargin=function(t){return this.then(function(){switch(o(t)){case"number":t=[t,t,t,t];case"array":if(2===t.length&&(t=[t[0],t[1],t[0],t[1]]),4===t.length)break;default:return this.error("Invalid margin array.")}this.opt.margin=t}).then(this.setPageSize)},c.prototype.setPageSize=function(t){function e(t,e){return Math.floor(t*e/72*96)}return this.then(function(){(t=t||q.getPageSize(this.opt.jsPDF)).hasOwnProperty("inner")||(t.inner={width:t.width-this.opt.margin[1]-this.opt.margin[3],height:t.height-this.opt.margin[0]-this.opt.margin[2]},t.inner.px={width:e(t.inner.width,t.k),height:e(t.inner.height,t.k)},t.inner.ratio=t.inner.height/t.inner.width),this.prop.pageSize=t})},c.prototype.setProgress=function(t,e,r,n){return null!=t&&(this.progress.val=t),null!=e&&(this.progress.state=e),null!=r&&(this.progress.n=r),null!=n&&(this.progress.stack=n),this.progress.ratio=this.progress.val/this.progress.state,this},c.prototype.updateProgress=function(t,e,r,n){return this.setProgress(t?this.progress.val+t:null,e||null,r?this.progress.n+r:null,n?this.progress.stack.concat(n):null)},c.prototype.then=function(t,e){var r=this;return this.thenCore(t,e,function(t,e){return r.updateProgress(null,null,1,[t]),Promise.prototype.then.call(this,function(e){return r.updateProgress(null,t),e}).then(t,e).then(function(t){return r.updateProgress(1),t})})},c.prototype.thenCore=function(t,e,r){r=r||Promise.prototype.then,t&&(t=t.bind(this)),e&&(e=e.bind(this));var n=-1!==Promise.toString().indexOf("[native code]")&&"Promise"===Promise.name?this:c.convert(Object.assign({},this),Promise.prototype),i=r.call(n,t,e);return c.convert(i,this.__proto__)},c.prototype.thenExternal=function(t,e){return Promise.prototype.then.call(this,t,e)},c.prototype.thenList=function(t){var e=this;return t.forEach(function(t){e=e.thenCore(t)}),e},c.prototype.catch=function(t){t&&(t=t.bind(this));var e=Promise.prototype.catch.call(this,t);return c.convert(e,this)},c.prototype.catchExternal=function(t){return Promise.prototype.catch.call(this,t)},c.prototype.error=function(t){return this.then(function(){throw Error(t)})},c.prototype.using=c.prototype.set,c.prototype.saveAs=c.prototype.save,c.prototype.export=c.prototype.output,c.prototype.run=c.prototype.then,q.getPageSize=function(t,e,r){if("object"===(0,n.A)(t)){var i=t;t=i.orientation,e=i.unit||e,r=i.format||r}e=e||"mm",r=r||"a4",t=(""+(t||"P")).toLowerCase();var a,o=(""+r).toLowerCase(),s={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};switch(e){case"pt":a=1;break;case"mm":a=72/25.4;break;case"cm":a=72/2.54;break;case"in":a=72;break;case"px":a=.75;break;case"pc":case"em":a=12;break;case"ex":a=6;break;default:throw"Invalid unit: "+e}var c,u=0,h=0;if(s.hasOwnProperty(o))u=s[o][1]/a,h=s[o][0]/a;else try{u=r[1],h=r[0]}catch(t){throw Error("Invalid format: "+r)}if("p"===t||"portrait"===t)t="p",h>u&&(c=h,h=u,u=c);else{if("l"!==t&&"landscape"!==t)throw"Invalid orientation: "+t;t="l",u>h&&(c=h,h=u,u=c)}return{width:h,height:u,unit:e,k:a,orientation:t}},t.html=function(t,e){(e=e||{}).callback=e.callback||function(){},e.html2canvas=e.html2canvas||{},e.html2canvas.canvas=e.html2canvas.canvas||this.canvas,e.jsPDF=e.jsPDF||this,e.fontFaces=e.fontFaces?e.fontFaces.map(tj):null;var r=new c(e);return e.worker?r:r.from(t).doCallback()}}(q.API),q.API.addJS=function(t){return tT=t,this.internal.events.subscribe("postPutResources",function(){tD=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/Names [(EmbeddedJS) "+(tD+1)+" 0 R]"),this.internal.out(">>"),this.internal.out("endobj"),tR=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /JavaScript"),this.internal.out("/JS ("+tT+")"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){void 0!==tD&&void 0!==tR&&this.internal.out("/Names <</JavaScript "+tD+" 0 R>>")}),this},function(t){var e;t.events.push(["postPutResources",function(){var t=/^(\d+) 0 obj$/;if(this.outline.root.children.length>0)for(var r=this.outline.render().split(/\r\n/),n=0;n<r.length;n++){var i=r[n],a=t.exec(i);if(null!=a){var o=a[1];this.internal.newObjectDeferredBegin(o,!1)}this.internal.write(i)}if(this.outline.createNamedDestinations){var s=this.internal.pages.length,c=[];for(n=0;n<s;n++){var u=this.internal.newObject();c.push(u);var h=this.internal.getPageInfo(n+1);this.internal.write("<< /D["+h.objId+" 0 R /XYZ null null null]>> endobj")}var l=this.internal.newObject();for(this.internal.write("<< /Names [ "),n=0;n<c.length;n++)this.internal.write("(page_"+(n+1)+")"+c[n]+" 0 R");this.internal.write(" ] >>","endobj"),e=this.internal.newObject(),this.internal.write("<< /Dests "+l+" 0 R"),this.internal.write(">>","endobj")}}]),t.events.push(["putCatalog",function(){this.outline.root.children.length>0&&(this.internal.write("/Outlines",this.outline.makeRef(this.outline.root)),this.outline.createNamedDestinations&&this.internal.write("/Names "+e+" 0 R"))}]),t.events.push(["initialized",function(){var t=this;t.outline={createNamedDestinations:!1,root:{children:[]}},t.outline.add=function(t,e,r){var n={title:e,options:r,children:[]};return null==t&&(t=this.root),t.children.push(n),n},t.outline.render=function(){return this.ctx={},this.ctx.val="",this.ctx.pdf=t,this.genIds_r(this.root),this.renderRoot(this.root),this.renderItems(this.root),this.ctx.val},t.outline.genIds_r=function(e){e.id=t.internal.newObjectDeferred();for(var r=0;r<e.children.length;r++)this.genIds_r(e.children[r])},t.outline.renderRoot=function(t){this.objStart(t),this.line("/Type /Outlines"),t.children.length>0&&(this.line("/First "+this.makeRef(t.children[0])),this.line("/Last "+this.makeRef(t.children[t.children.length-1]))),this.line("/Count "+this.count_r({count:0},t)),this.objEnd()},t.outline.renderItems=function(e){for(var r=this.ctx.pdf.internal.getVerticalCoordinateString,n=0;n<e.children.length;n++){var i=e.children[n];this.objStart(i),this.line("/Title "+this.makeString(i.title)),this.line("/Parent "+this.makeRef(e)),n>0&&this.line("/Prev "+this.makeRef(e.children[n-1])),n<e.children.length-1&&this.line("/Next "+this.makeRef(e.children[n+1])),i.children.length>0&&(this.line("/First "+this.makeRef(i.children[0])),this.line("/Last "+this.makeRef(i.children[i.children.length-1])));var a=this.count=this.count_r({count:0},i);if(a>0&&this.line("/Count "+a),i.options&&i.options.pageNumber){var o=t.internal.getPageInfo(i.options.pageNumber);this.line("/Dest ["+o.objId+" 0 R /XYZ 0 "+r(0)+" 0]")}this.objEnd()}for(var s=0;s<e.children.length;s++)this.renderItems(e.children[s])},t.outline.line=function(t){this.ctx.val+=t+"\r\n"},t.outline.makeRef=function(t){return t.id+" 0 R"},t.outline.makeString=function(e){return"("+t.internal.pdfEscape(e)+")"},t.outline.objStart=function(t){this.ctx.val+="\r\n"+t.id+" 0 obj\r\n<<\r\n"},t.outline.objEnd=function(){this.ctx.val+=">> \r\nendobj\r\n"},t.outline.count_r=function(t,e){for(var r=0;r<e.children.length;r++)t.count++,this.count_r(t,e.children[r]);return t.count}}])}(q.API),function(t){var e=[192,193,194,195,196,197,198,199];t.processJPEG=function(t,r,n,i,a,o){var s,c=this.decode.DCT_DECODE,u=null;if("string"==typeof t||this.__addimage__.isArrayBuffer(t)||this.__addimage__.isArrayBufferView(t)){switch(t=a||t,t=this.__addimage__.isArrayBuffer(t)?new Uint8Array(t):t,(s=function(t){for(var r,n=256*t.charCodeAt(4)+t.charCodeAt(5),i=t.length,a={width:0,height:0,numcomponents:1},o=4;o<i;o+=2){if(o+=n,-1!==e.indexOf(t.charCodeAt(o+1))){r=256*t.charCodeAt(o+5)+t.charCodeAt(o+6),a={width:256*t.charCodeAt(o+7)+t.charCodeAt(o+8),height:r,numcomponents:t.charCodeAt(o+9)};break}n=256*t.charCodeAt(o+2)+t.charCodeAt(o+3)}return a}(t=this.__addimage__.isArrayBufferView(t)?this.__addimage__.arrayBufferToBinaryString(t):t)).numcomponents){case 1:o=this.color_spaces.DEVICE_GRAY;break;case 4:o=this.color_spaces.DEVICE_CMYK;break;case 3:o=this.color_spaces.DEVICE_RGB}u={data:t,width:s.width,height:s.height,colorSpace:o,bitsPerComponent:8,filter:c,index:r,alias:n}}return u}}(q.API);var tU,tH,tW,tV,tG,tY=function(){function t(t){var e,r,n,i,a,o,s,c,u,h,l,f,d,p;for(this.data=t,this.pos=8,this.palette=[],this.imgData=[],this.transparency={},this.animation=null,this.text={},o=null;;){switch(e=this.readUInt32(),u=(function(){var t,e;for(e=[],t=0;t<4;++t)e.push(String.fromCharCode(this.data[this.pos++]));return e}).call(this).join("")){case"IHDR":this.width=this.readUInt32(),this.height=this.readUInt32(),this.bits=this.data[this.pos++],this.colorType=this.data[this.pos++],this.compressionMethod=this.data[this.pos++],this.filterMethod=this.data[this.pos++],this.interlaceMethod=this.data[this.pos++];break;case"acTL":this.animation={numFrames:this.readUInt32(),numPlays:this.readUInt32()||1/0,frames:[]};break;case"PLTE":this.palette=this.read(e);break;case"fcTL":o&&this.animation.frames.push(o),this.pos+=4,o={width:this.readUInt32(),height:this.readUInt32(),xOffset:this.readUInt32(),yOffset:this.readUInt32()},a=this.readUInt16(),i=this.readUInt16()||100,o.delay=1e3*a/i,o.disposeOp=this.data[this.pos++],o.blendOp=this.data[this.pos++],o.data=[];break;case"IDAT":case"fdAT":for("fdAT"===u&&(this.pos+=4,e-=4),t=(null!=o?o.data:void 0)||this.imgData,f=0;0<=e?f<e:f>e;0<=e?++f:--f)t.push(this.data[this.pos++]);break;case"tRNS":switch(this.transparency={},this.colorType){case 3:if(n=this.palette.length/3,this.transparency.indexed=this.read(e),this.transparency.indexed.length>n)throw Error("More transparent colors than palette size");if((h=n-this.transparency.indexed.length)>0)for(d=0;0<=h?d<h:d>h;0<=h?++d:--d)this.transparency.indexed.push(255);break;case 0:this.transparency.grayscale=this.read(e)[0];break;case 2:this.transparency.rgb=this.read(e)}break;case"tEXt":s=(l=this.read(e)).indexOf(0),c=String.fromCharCode.apply(String,l.slice(0,s)),this.text[c]=String.fromCharCode.apply(String,l.slice(s+1));break;case"IEND":return o&&this.animation.frames.push(o),this.colors=(function(){switch(this.colorType){case 0:case 3:case 4:return 1;case 2:case 6:return 3}}).call(this),this.hasAlphaChannel=4===(p=this.colorType)||6===p,r=this.colors+ +!!this.hasAlphaChannel,this.pixelBitlength=this.bits*r,this.colorSpace=(function(){switch(this.colors){case 1:return"DeviceGray";case 3:return"DeviceRGB"}}).call(this),void(this.imgData=new Uint8Array(this.imgData));default:this.pos+=e}if(this.pos+=4,this.pos>this.data.length)throw Error("Incomplete or corrupt PNG file")}}t.prototype.read=function(t){var e,r;for(r=[],e=0;0<=t?e<t:e>t;0<=t?++e:--e)r.push(this.data[this.pos++]);return r},t.prototype.readUInt32=function(){return this.data[this.pos++]<<24|this.data[this.pos++]<<16|this.data[this.pos++]<<8|this.data[this.pos++]},t.prototype.readUInt16=function(){return this.data[this.pos++]<<8|this.data[this.pos++]},t.prototype.decodePixels=function(t){var e=this.pixelBitlength/8,r=new Uint8Array(this.width*this.height*e),n=0,a=this;if(null==t&&(t=this.imgData),0===t.length)return new Uint8Array(0);function o(i,o,s,c){var u,h,l,f,d,p,g,m,v,b,y,w,x,N,A,L,S,_,P,k,F,I=Math.ceil((a.width-i)/s),C=Math.ceil((a.height-o)/c),j=a.width==I&&a.height==C;for(N=e*I,w=j?r:new Uint8Array(N*C),p=t.length,x=0,h=0;x<C&&n<p;){switch(t[n++]){case 0:for(f=S=0;S<N;f=S+=1)w[h++]=t[n++];break;case 1:for(f=_=0;_<N;f=_+=1)u=t[n++],d=f<e?0:w[h-e],w[h++]=(u+d)%256;break;case 2:for(f=P=0;P<N;f=P+=1)u=t[n++],l=(f-f%e)/e,A=x&&w[(x-1)*N+l*e+f%e],w[h++]=(A+u)%256;break;case 3:for(f=k=0;k<N;f=k+=1)u=t[n++],l=(f-f%e)/e,d=f<e?0:w[h-e],A=x&&w[(x-1)*N+l*e+f%e],w[h++]=(u+Math.floor((d+A)/2))%256;break;case 4:for(f=F=0;F<N;f=F+=1)u=t[n++],l=(f-f%e)/e,d=f<e?0:w[h-e],0===x?A=L=0:(A=w[(x-1)*N+l*e+f%e],L=l&&w[(x-1)*N+(l-1)*e+f%e]),m=Math.abs((g=d+A-L)-d),b=Math.abs(g-A),y=Math.abs(g-L),v=m<=b&&m<=y?d:b<=y?A:L,w[h++]=(u+v)%256;break;default:throw Error("Invalid filter algorithm: "+t[n-1])}if(!j){var O=((o+x*c)*a.width+i)*e,E=x*N;for(f=0;f<I;f+=1){for(var M=0;M<e;M+=1)r[O++]=w[E++];O+=(s-1)*e}}x++}}return t=(0,i.a8)(t),1==a.interlaceMethod?(o(0,0,8,8),o(4,0,8,8),o(0,4,4,8),o(2,0,4,4),o(0,2,2,4),o(1,0,2,2),o(0,1,1,2)):o(0,0,1,1),r},t.prototype.decodePalette=function(){var t,e,r,n,i,a,o,s,c;for(r=this.palette,i=new Uint8Array(((a=this.transparency.indexed||[]).length||0)+r.length),n=0,t=0,e=o=0,s=r.length;o<s;e=o+=3)i[n++]=r[e],i[n++]=r[e+1],i[n++]=r[e+2],i[n++]=null!=(c=a[t++])?c:255;return i},t.prototype.copyToImageData=function(t,e){var r,n,i,a,o,s,c,u,h,l,f;if(n=this.colors,h=null,r=this.hasAlphaChannel,this.palette.length&&(h=null!=(f=this._decodedPalette)?f:this._decodedPalette=this.decodePalette(),n=4,r=!0),u=(i=t.data||t).length,o=h||e,a=s=0,1===n)for(;a<u;)c=h?4*e[a/4]:s,l=o[c++],i[a++]=l,i[a++]=l,i[a++]=l,i[a++]=r?o[c++]:255,s=c;else for(;a<u;)c=h?4*e[a/4]:s,i[a++]=o[c++],i[a++]=o[c++],i[a++]=o[c++],i[a++]=r?o[c++]:255,s=c},t.prototype.decode=function(){var t;return t=new Uint8Array(this.width*this.height*4),this.copyToImageData(t,this.decodePixels()),t};var e,r,n,o=function(){if("[object Window]"===Object.prototype.toString.call(a)){try{n=(r=a.document.createElement("canvas")).getContext("2d")}catch(t){return!1}return!0}return!1};return o(),e=function(t){var e;if(!0===o())return n.width=t.width,n.height=t.height,n.clearRect(0,0,t.width,t.height),n.putImageData(t,0,0),(e=new Image).src=r.toDataURL(),e;throw Error("This method requires a Browser with Canvas-capability.")},t.prototype.decodeFrames=function(t){var r,n,i,a,o,s,c,u;if(this.animation){for(u=[],n=o=0,s=(c=this.animation.frames).length;o<s;n=++o)r=c[n],i=t.createImageData(r.width,r.height),a=this.decodePixels(new Uint8Array(r.data)),this.copyToImageData(i,a),r.imageData=i,u.push(r.image=e(i));return u}},t.prototype.renderFrame=function(t,e){var r,n,i;return r=(n=this.animation.frames)[e],i=n[e-1],0===e&&t.clearRect(0,0,this.width,this.height),1===(null!=i?i.disposeOp:void 0)?t.clearRect(i.xOffset,i.yOffset,i.width,i.height):2===(null!=i?i.disposeOp:void 0)&&t.putImageData(i.imageData,i.xOffset,i.yOffset),0===r.blendOp&&t.clearRect(r.xOffset,r.yOffset,r.width,r.height),t.drawImage(r.image,r.xOffset,r.yOffset)},t.prototype.animate=function(t){var e,r,n,i,a,o,s=this;return r=0,i=(o=this.animation).numFrames,n=o.frames,a=o.numPlays,(e=function(){var o,c;if(c=n[o=r++%i],s.renderFrame(t,o),i>1&&r/i<a)return s.animation._timeout=setTimeout(e,c.delay)})()},t.prototype.stopAnimation=function(){var t;return clearTimeout(null!=(t=this.animation)?t._timeout:void 0)},t.prototype.render=function(t){var e,r;return t._png&&t._png.stopAnimation(),t._png=this,t.width=this.width,t.height=this.height,e=t.getContext("2d"),this.animation?(this.decodeFrames(e),this.animate(e)):(r=e.createImageData(this.width,this.height),this.copyToImageData(r,this.decodePixels()),e.putImageData(r,0,0))},t}();function tJ(t){var e=0;if(71!==t[e++]||73!==t[e++]||70!==t[e++]||56!==t[e++]||56!=(t[e++]+1&253)||97!==t[e++])throw Error("Invalid GIF 87a/89a header.");var r=t[e++]|t[e++]<<8,n=t[e++]|t[e++]<<8,i=t[e++],a=1<<(7&i)+1;t[e++],t[e++];var o=null,s=null;i>>7&&(o=e,s=a,e+=3*a);var c=!0,u=[],h=0,l=null,f=0,d=null;for(this.width=r,this.height=n;c&&e<t.length;)switch(t[e++]){case 33:switch(t[e++]){case 255:if(11!==t[e]||78==t[e+1]&&69==t[e+2]&&84==t[e+3]&&83==t[e+4]&&67==t[e+5]&&65==t[e+6]&&80==t[e+7]&&69==t[e+8]&&50==t[e+9]&&46==t[e+10]&&48==t[e+11]&&3==t[e+12]&&1==t[e+13]&&0==t[e+16])e+=14,d=t[e++]|t[e++]<<8,e++;else for(e+=12;;){if(!((g=t[e++])>=0))throw Error("Invalid block size");if(0===g)break;e+=g}break;case 249:if(4!==t[e++]||0!==t[e+4])throw Error("Invalid graphics extension block.");var p=t[e++];h=t[e++]|t[e++]<<8,l=t[e++],0==(1&p)&&(l=null),f=p>>2&7,e++;break;case 254:for(;;){if(!((g=t[e++])>=0))throw Error("Invalid block size");if(0===g)break;e+=g}break;default:throw Error("Unknown graphic control label: 0x"+t[e-1].toString(16))}break;case 44:var g,m=t[e++]|t[e++]<<8,v=t[e++]|t[e++]<<8,b=t[e++]|t[e++]<<8,y=t[e++]|t[e++]<<8,w=t[e++],x=w>>6&1,N=1<<(7&w)+1,A=o,L=s,S=!1;w>>7&&(S=!0,A=e,L=N,e+=3*N);var _=e;for(e++;;){if(!((g=t[e++])>=0))throw Error("Invalid block size");if(0===g)break;e+=g}u.push({x:m,y:v,width:b,height:y,has_local_palette:S,palette_offset:A,palette_size:L,data_offset:_,data_length:e-_,transparent_index:l,interlaced:!!x,delay:h,disposal:f});break;case 59:c=!1;break;default:throw Error("Unknown gif block: 0x"+t[e-1].toString(16))}this.numFrames=function(){return u.length},this.loopCount=function(){return d},this.frameInfo=function(t){if(t<0||t>=u.length)throw Error("Frame index out of range.");return u[t]},this.decodeAndBlitFrameBGRA=function(e,n){var i=this.frameInfo(e),a=i.width*i.height,o=new Uint8Array(a);tX(t,i.data_offset,o,a);var s=i.palette_offset,c=i.transparent_index;null===c&&(c=256);var u=i.width,h=r-u,l=u,f=4*(i.y*r+i.x),d=4*((i.y+i.height)*r+i.x),p=f,g=4*h;!0===i.interlaced&&(g+=4*r*7);for(var m=8,v=0,b=o.length;v<b;++v){var y=o[v];if(0===l&&(l=u,(p+=g)>=d&&(g=4*h+4*r*(m-1),p=f+(u+h)*(m<<1),m>>=1)),y===c)p+=4;else{var w=t[s+3*y],x=t[s+3*y+1],N=t[s+3*y+2];n[p++]=N,n[p++]=x,n[p++]=w,n[p++]=255}--l}},this.decodeAndBlitFrameRGBA=function(e,n){var i=this.frameInfo(e),a=i.width*i.height,o=new Uint8Array(a);tX(t,i.data_offset,o,a);var s=i.palette_offset,c=i.transparent_index;null===c&&(c=256);var u=i.width,h=r-u,l=u,f=4*(i.y*r+i.x),d=4*((i.y+i.height)*r+i.x),p=f,g=4*h;!0===i.interlaced&&(g+=4*r*7);for(var m=8,v=0,b=o.length;v<b;++v){var y=o[v];if(0===l&&(l=u,(p+=g)>=d&&(g=4*h+4*r*(m-1),p=f+(u+h)*(m<<1),m>>=1)),y===c)p+=4;else{var w=t[s+3*y],x=t[s+3*y+1],N=t[s+3*y+2];n[p++]=w,n[p++]=x,n[p++]=N,n[p++]=255}--l}}}function tX(t,e,r,n){for(var i=t[e++],a=1<<i,o=a+1,c=o+1,u=i+1,h=(1<<u)-1,l=0,f=0,d=0,p=t[e++],g=new Int32Array(4096),m=null;;){for(;l<16&&0!==p;)f|=t[e++]<<l,l+=8,1===p?p=t[e++]:--p;if(l<u)break;var v=f&h;if(f>>=u,l-=u,v!==a){if(v===o)break;for(var b=v<c?v:m,y=0,w=b;w>a;)w=g[w]>>8,++y;var x=w;if(d+y+ +(b!==v)>n)return void s.log("Warning, gif stream longer than expected.");r[d++]=x;var N=d+=y;for(b!==v&&(r[d++]=x),w=b;y--;)w=g[w],r[--N]=255&w,w>>=8;null!==m&&c<4096&&(g[c++]=m<<8|x,c>=h+1&&u<12&&(++u,h=h<<1|1)),m=v}else c=o+1,h=(1<<(u=i+1))-1,m=null}return d!==n&&s.log("Warning, gif stream shorter than expected."),r}function tK(t){var e,r,n,i,a,o=Math.floor,s=Array(64),c=Array(64),u=Array(64),h=Array(64),l=Array(65535),f=Array(65535),d=Array(64),p=Array(64),g=[],m=0,v=7,b=Array(64),y=Array(64),w=Array(64),x=Array(256),N=Array(2048),A=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],L=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],S=[0,1,2,3,4,5,6,7,8,9,10,11],_=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],P=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],k=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],F=[0,1,2,3,4,5,6,7,8,9,10,11],I=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],C=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function j(t,e){for(var r=0,n=0,i=[],a=1;a<=16;a++){for(var o=1;o<=t[a];o++)i[e[n]]=[],i[e[n]][0]=r,i[e[n]][1]=a,n++,r++;r*=2}return i}function O(t){for(var e=t[0],r=t[1]-1;r>=0;)e&1<<r&&(m|=1<<v),r--,--v<0&&(255==m?(E(255),E(0)):E(m),v=7,m=0)}function E(t){g.push(t)}function M(t){E(t>>8&255),E(255&t)}function q(t,e,r,n,i){for(var a,o=i[0],s=i[240],c=function(t,e){var r,n,i,a,o,s,c,u,h,l,f=0;for(h=0;h<8;++h){r=t[f],n=t[f+1],i=t[f+2],a=t[f+3],o=t[f+4],s=t[f+5],c=t[f+6];var p=r+(u=t[f+7]),g=r-u,m=n+c,v=n-c,b=i+s,y=i-s,w=a+o,x=a-o,N=p+w,A=p-w,L=m+b,S=m-b;t[f]=N+L,t[f+4]=N-L;var _=.707106781*(S+A);t[f+2]=A+_,t[f+6]=A-_;var P=.382683433*((N=x+y)-(S=v+g)),k=.5411961*N+P,F=1.306562965*S+P,I=.707106781*(L=y+v),C=g+I,j=g-I;t[f+5]=j+k,t[f+3]=j-k,t[f+1]=C+F,t[f+7]=C-F,f+=8}for(f=0,h=0;h<8;++h){r=t[f],n=t[f+8],i=t[f+16],a=t[f+24],o=t[f+32],s=t[f+40],c=t[f+48];var O=r+(u=t[f+56]),E=r-u,M=n+c,q=n-c,B=i+s,D=i-s,R=a+o,T=a-o,z=O+R,U=O-R,H=M+B,W=M-B;t[f]=z+H,t[f+32]=z-H;var V=.707106781*(W+U);t[f+16]=U+V,t[f+48]=U-V;var G=.382683433*((z=T+D)-(W=q+E)),Y=.5411961*z+G,J=1.306562965*W+G,X=.707106781*(H=D+q),K=E+X,Z=E-X;t[f+40]=Z+Y,t[f+24]=Z-Y,t[f+8]=K+J,t[f+56]=K-J,f++}for(h=0;h<64;++h)l=t[h]*e[h],d[h]=l>0?l+.5|0:l-.5|0;return d}(t,e),u=0;u<64;++u)p[A[u]]=c[u];var h=p[0]-r;r=p[0],0==h?O(n[0]):(O(n[f[a=32767+h]]),O(l[a]));for(var g=63;g>0&&0==p[g];)g--;if(0==g)return O(o),r;for(var m,v=1;v<=g;){for(var b=v;0==p[v]&&v<=g;)++v;var y=v-b;if(y>=16){m=y>>4;for(var w=1;w<=m;++w)O(s);y&=15}O(i[(y<<4)+f[a=32767+p[v]]]),O(l[a]),v++}return 63!=g&&O(o),r}function B(t){a!=(t=Math.min(Math.max(t,1),100))&&(!function(t){for(var e=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],r=0;r<64;r++){var n=o((e[r]*t+50)/100);n=Math.min(Math.max(n,1),255),s[A[r]]=n}for(var i=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],a=0;a<64;a++){var l=o((i[a]*t+50)/100);l=Math.min(Math.max(l,1),255),c[A[a]]=l}for(var f=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],d=0,p=0;p<8;p++)for(var g=0;g<8;g++)u[d]=1/(s[A[d]]*f[p]*f[g]*8),h[d]=1/(c[A[d]]*f[p]*f[g]*8),d++}(t<50?Math.floor(5e3/t):Math.floor(200-2*t)),a=t)}this.encode=function(t,a){a&&B(a),g=[],m=0,v=7,M(65496),M(65504),M(16),E(74),E(70),E(73),E(70),E(0),E(1),E(1),E(0),M(1),M(1),E(0),E(0),function(){M(65499),M(132),E(0);for(var t=0;t<64;t++)E(s[t]);E(1);for(var e=0;e<64;e++)E(c[e])}(),d=t.width,p=t.height,M(65472),M(17),E(8),M(p),M(d),E(3),E(1),E(17),E(0),E(2),E(17),E(1),E(3),E(17),E(1),function(){M(65476),M(418),E(0);for(var t=0;t<16;t++)E(L[t+1]);for(var e=0;e<=11;e++)E(S[e]);E(16);for(var r=0;r<16;r++)E(_[r+1]);for(var n=0;n<=161;n++)E(P[n]);E(1);for(var i=0;i<16;i++)E(k[i+1]);for(var a=0;a<=11;a++)E(F[a]);E(17);for(var o=0;o<16;o++)E(I[o+1]);for(var s=0;s<=161;s++)E(C[s])}(),M(65498),M(12),E(3),E(1),E(0),E(2),E(17),E(3),E(17),E(0),E(63),E(0);var o=0,l=0,f=0;m=0,v=7,this.encode.displayName="_encode_";for(var d,p,x,A,j,D,R,T,z,U,H,W=t.data,V=t.width,G=t.height,Y=4*V,J=0;J<G;){for(x=0;x<Y;){for(R=Y*J+x,z=-1,U=0,H=0;H<64;H++)T=R+(U=H>>3)*Y+(z=4*(7&H)),J+U>=G&&(T-=Y*(J+1+U-G)),x+z>=Y&&(T-=x+z-Y+4),A=W[T++],j=W[T++],D=W[T++],b[H]=(N[A]+N[j+256|0]+N[D+512|0]>>16)-128,y[H]=(N[A+768|0]+N[j+1024|0]+N[D+1280|0]>>16)-128,w[H]=(N[A+1280|0]+N[j+1536|0]+N[D+1792|0]>>16)-128;o=q(b,u,o,e,n),l=q(y,h,l,r,i),f=q(w,h,f,r,i),x+=32}J+=8}if(v>=0){var X=[];X[1]=v+1,X[0]=(1<<v+1)-1,O(X)}return M(65497),new Uint8Array(g)},t=t||50,function(){for(var t=String.fromCharCode,e=0;e<256;e++)x[e]=t(e)}(),e=j(L,S),r=j(k,F),n=j(_,P),i=j(I,C),function(){for(var t=1,e=2,r=1;r<=15;r++){for(var n=t;n<e;n++)f[32767+n]=r,l[32767+n]=[],l[32767+n][1]=r,l[32767+n][0]=n;for(var i=-(e-1);i<=-t;i++)f[32767+i]=r,l[32767+i]=[],l[32767+i][1]=r,l[32767+i][0]=e-1+i;t<<=1,e<<=1}}(),function(){for(var t=0;t<256;t++)N[t]=19595*t,N[t+256|0]=38470*t,N[t+512|0]=7471*t+32768,N[t+768|0]=-11059*t,N[t+1024|0]=-21709*t,N[t+1280|0]=32768*t+8421375,N[t+1536|0]=-27439*t,N[t+1792|0]=-5329*t}(),B(t)}function tZ(t,e){if(this.pos=0,this.buffer=t,this.datav=new DataView(t.buffer),this.is_with_alpha=!!e,this.bottom_up=!0,this.flag=String.fromCharCode(this.buffer[0])+String.fromCharCode(this.buffer[1]),this.pos+=2,-1===["BM","BA","CI","CP","IC","PT"].indexOf(this.flag))throw Error("Invalid BMP File");this.parseHeader(),this.parseBGR()}function t$(t){function e(t){if(!t)throw Error("assert :P")}function r(t,e,r){for(var n=0;4>n;n++)if(t[e+n]!=r.charCodeAt(n))return!0;return!1}function n(t,e,r,n,i){for(var a=0;a<i;a++)t[e+a]=r[n+a]}function i(t,e,r,n){for(var i=0;i<n;i++)t[e+i]=r}function a(t){return new Int32Array(t)}function o(t,e){for(var r=[],n=0;n<t;n++)r.push(new e);return r}function s(t,e){var r=[];return function t(r,n,i){for(var a=i[n],o=0;o<a&&(r.push(i.length>n+1?[]:new e),!(i.length<n+1));o++)t(r[o],n+1,i)}(r,0,t),r}var c=function(){var t=this;function c(t,e){for(var r=1<<e-1>>>0;t&r;)r>>>=1;return r?(t&r-1)+r:t}function u(t,r,n,i,a){e(!(i%n));do t[r+(i-=n)]=a;while(0<i)}function h(t,r,n,i,o){if(e(2328>=o),512>=o)var s=a(512);else if(null==(s=a(o)))return 0;return function(t,r,n,i,o,s){var h,f,d=r,p=1<<n,g=a(16),m=a(16);for(e(0!=o),e(null!=i),e(null!=t),e(0<n),f=0;f<o;++f){if(15<i[f])return 0;++g[i[f]]}if(g[0]==o)return 0;for(m[1]=0,h=1;15>h;++h){if(g[h]>1<<h)return 0;m[h+1]=m[h]+g[h]}for(f=0;f<o;++f)h=i[f],0<i[f]&&(s[m[h]++]=f);if(1==m[15])return(i=new l).g=0,i.value=s[0],u(t,d,1,p,i),p;var v,b=-1,y=p-1,w=0,x=1,N=1,A=1<<n;for(f=0,h=1,o=2;h<=n;++h,o<<=1){if(x+=N<<=1,0>(N-=g[h]))return 0;for(;0<g[h];--g[h])(i=new l).g=h,i.value=s[f++],u(t,d+w,o,A,i),w=c(w,h)}for(h=n+1,o=2;15>=h;++h,o<<=1){if(x+=N<<=1,0>(N-=g[h]))return 0;for(;0<g[h];--g[h]){if(i=new l,(w&y)!=b){for(d+=A,v=1<<(b=h)-n;15>b&&!(0>=(v-=g[b]));)++b,v<<=1;p+=A=1<<(v=b-n),t[r+(b=w&y)].g=v+n,t[r+b].value=d-r-b}i.g=h-n,i.value=s[f++],u(t,d+(w>>n),o,A,i),w=c(w,h)}}return x!=2*m[15]-1?0:p}(t,r,n,i,o,s)}function l(){this.value=this.g=0}function f(){this.value=this.g=0}function d(){this.G=o(5,l),this.H=a(5),this.jc=this.Qb=this.qb=this.nd=0,this.pd=o(rm,f)}function p(t,r,n,i){e(null!=t),e(null!=r),e(0x80000000>i),t.Ca=254,t.I=0,t.b=-8,t.Ka=0,t.oa=r,t.pa=n,t.Jd=r,t.Yc=n+i,t.Zc=4<=i?n+i-4+1:n,_(t)}function g(t,e){for(var r=0;0<e--;)r|=k(t,128)<<e;return r}function m(t,e){var r=g(t,e);return P(t)?-r:r}function v(t,r,n,i){var a,o=0;for(e(null!=t),e(null!=r),e(0xfffffff8>i),t.Sb=i,t.Ra=0,t.u=0,t.h=0,4<i&&(i=4),a=0;a<i;++a)o+=r[n+a]<<8*a;t.Ra=o,t.bb=i,t.oa=r,t.pa=n}function b(t){for(;8<=t.u&&t.bb<t.Sb;)t.Ra>>>=8,t.Ra+=t.oa[t.pa+t.bb]<<ry-8>>>0,++t.bb,t.u-=8;A(t)&&(t.h=1,t.u=0)}function y(t,r){if(e(0<=r),!t.h&&r<=rb){var n=N(t)&rv[r];return t.u+=r,b(t),n}return t.h=1,t.u=0}function w(){this.b=this.Ca=this.I=0,this.oa=[],this.pa=0,this.Jd=[],this.Yc=0,this.Zc=[],this.Ka=0}function x(){this.Ra=0,this.oa=[],this.h=this.u=this.bb=this.Sb=this.pa=0}function N(t){return t.Ra>>>(t.u&ry-1)>>>0}function A(t){return e(t.bb<=t.Sb),t.h||t.bb==t.Sb&&t.u>ry}function L(t,e){t.u=e,t.h=A(t)}function S(t){t.u>=rw&&(e(t.u>=rw),b(t))}function _(t){e(null!=t&&null!=t.oa),t.pa<t.Zc?(t.I=(t.oa[t.pa++]|t.I<<8)>>>0,t.b+=8):(e(null!=t&&null!=t.oa),t.pa<t.Yc?(t.b+=8,t.I=t.oa[t.pa++]|t.I<<8):t.Ka?t.b=0:(t.I<<=8,t.b+=8,t.Ka=1))}function P(t){return g(t,1)}function k(t,e){var r=t.Ca;0>t.b&&_(t);var n=t.b,i=r*e>>>8,a=(t.I>>>n>i)+0;for(a?(r-=i,t.I-=i+1<<n>>>0):r=i+1,n=r,i=0;256<=n;)i+=8,n>>=8;return n=7^i+rx[n],t.b-=n,t.Ca=(r<<n)-1,a}function F(t,e,r){t[e+0]=r>>24&255,t[e+1]=r>>16&255,t[e+2]=r>>8&255,t[e+3]=(0|r)&255}function I(t,e){return 0|t[e+0]|t[e+1]<<8}function C(t,e){return I(t,e)|t[e+2]<<16}function j(t,e){return I(t,e)|I(t,e+2)<<16}function O(t,r){return e(null!=t),e(0<r),t.X=a(1<<r),null==t.X?0:(t.Mb=32-r,t.Xa=r,1)}function E(t,r){e(null!=t),e(null!=r),e(t.Xa==r.Xa),n(r.X,0,t.X,0,1<<r.Xa)}function M(){this.X=[],this.Xa=this.Mb=0}function q(t,r,n,i){e(null!=n),e(null!=i);var a=n[0],o=i[0];return 0==a&&(a=(t*o+r/2)/r),0==o&&(o=(r*a+t/2)/t),0>=a||0>=o?0:(n[0]=a,i[0]=o,1)}function B(t,e){return t+(1<<e)-1>>>e}function D(t,e){return((0xff00ff00&t)+(0xff00ff00&e)>>>0&0xff00ff00)+((0xff00ff&t)+(0xff00ff&e)>>>0&0xff00ff)>>>0}function R(e,r){t[r]=function(r,n,i,a,o,s,c){var u;for(u=0;u<o;++u){var h=t[e](s[c+u-1],i,a+u);s[c+u]=D(r[n+u],h)}}}function T(){this.ud=this.hd=this.jd=0}function z(t,e){return((0xfefefefe&(t^e))>>>1)+(t&e)>>>0}function U(t){return 0<=t&&256>t?t:0>t?0:255<t?255:void 0}function H(t,e){return U(t+(t-e+.5>>1))}function W(t,e,r){return Math.abs(e-r)-Math.abs(t-r)}function V(t,e,r,n,i,a,o){for(n=a[o-1],r=0;r<i;++r)a[o+r]=n=D(t[e+r],n)}function G(t,e,r,n,i){var a;for(a=0;a<r;++a){var o=t[e+a],s=o>>8&255,c=0xff00ff&(c=(c=0xff00ff&o)+((s<<16)+s));n[i+a]=(0xff00ff00&o)+c>>>0}}function Y(t,e){e.jd=(0|t)&255,e.hd=t>>8&255,e.ud=t>>16&255}function J(t,e,r,n,i,a){var o;for(o=0;o<n;++o){var s=e[r+o],c=s>>>8,u=s,h=255&(h=(h=s>>>16)+((t.jd<<24>>24)*(c<<24>>24)>>>5));u=255&(u=(u+=(t.hd<<24>>24)*(c<<24>>24)>>>5)+((t.ud<<24>>24)*(h<<24>>24)>>>5)),i[a+o]=(0xff00ff00&s)+(h<<16)+u}}function X(e,r,n,i,a){t[r]=function(t,e,r,n,o,s,c,u,h){for(n=c;n<u;++n)for(c=0;c<h;++c)o[s++]=a(r[i(t[e++])])},t[e]=function(e,r,o,s,c,u,h){var l=8>>e.b,f=e.Ea,d=e.K[0],p=e.w;if(8>l)for(e=(1<<e.b)-1,p=(1<<l)-1;r<o;++r){var g,m=0;for(g=0;g<f;++g)g&e||(m=i(s[c++])),u[h++]=a(d[m&p]),m>>=l}else t["VP8LMapColor"+n](s,c,d,p,u,h,r,o,f)}}function K(t,e,r,n,i){for(r=e+r;e<r;){var a=t[e++];n[i++]=a>>16&255,n[i++]=a>>8&255,n[i++]=(0|a)&255}}function Z(t,e,r,n,i){for(r=e+r;e<r;){var a=t[e++];n[i++]=a>>16&255,n[i++]=a>>8&255,n[i++]=(0|a)&255,n[i++]=a>>24&255}}function $(t,e,r,n,i){for(r=e+r;e<r;){var a=(o=t[e++])>>16&240|o>>12&15,o=(0|o)&240|o>>28&15;n[i++]=a,n[i++]=o}}function Q(t,e,r,n,i){for(r=e+r;e<r;){var a=(o=t[e++])>>16&248|o>>13&7,o=o>>5&224|o>>3&31;n[i++]=a,n[i++]=o}}function tt(t,e,r,n,i){for(r=e+r;e<r;){var a=t[e++];n[i++]=(0|a)&255,n[i++]=a>>8&255,n[i++]=a>>16&255}}function te(t,e,r,i,a,o){if(0==o)for(r=e+r;e<r;)F(i,((o=t[e++])[0]>>24|o[1]>>8&65280|o[2]<<8&0xff0000|o[3]<<24)>>>0),a+=32;else n(i,a,t,e,r)}function tr(e,r){t[r][0]=t[e+"0"],t[r][1]=t[e+"1"],t[r][2]=t[e+"2"],t[r][3]=t[e+"3"],t[r][4]=t[e+"4"],t[r][5]=t[e+"5"],t[r][6]=t[e+"6"],t[r][7]=t[e+"7"],t[r][8]=t[e+"8"],t[r][9]=t[e+"9"],t[r][10]=t[e+"10"],t[r][11]=t[e+"11"],t[r][12]=t[e+"12"],t[r][13]=t[e+"13"],t[r][14]=t[e+"0"],t[r][15]=t[e+"0"]}function tn(t){return t==nh||t==nl||t==nf||t==nd}function ti(){this.eb=[],this.size=this.A=this.fb=0}function ta(){this.y=[],this.f=[],this.ea=[],this.F=[],this.Tc=this.Ed=this.Cd=this.Fd=this.lb=this.Db=this.Ab=this.fa=this.J=this.W=this.N=this.O=0}function to(){this.Rd=this.height=this.width=this.S=0,this.f={},this.f.RGBA=new ti,this.f.kb=new ta,this.sd=null}function ts(){this.width=[0],this.height=[0],this.Pd=[0],this.Qd=[0],this.format=[0]}function tc(){this.Id=this.fd=this.Md=this.hb=this.ib=this.da=this.bd=this.cd=this.j=this.v=this.Da=this.Sd=this.ob=0}function tu(t){return alert("todo:WebPSamplerProcessPlane"),t.T}function th(t,e){var r=t.T,i=e.ba.f.RGBA,a=i.eb,o=i.fb+t.ka*i.A,s=nD[e.ba.S],c=t.y,u=t.O,h=t.f,l=t.N,f=t.ea,d=t.W,p=e.cc,g=e.dc,m=e.Mc,v=e.Nc,b=t.ka,y=t.ka+t.T,w=t.U,x=w+1>>1;for(0==b?s(c,u,null,null,h,l,f,d,h,l,f,d,a,o,null,null,w):(s(e.ec,e.fc,c,u,p,g,m,v,h,l,f,d,a,o-i.A,a,o,w),++r);b+2<y;b+=2)p=h,g=l,m=f,v=d,l+=t.Rc,d+=t.Rc,o+=2*i.A,s(c,(u+=2*t.fa)-t.fa,c,u,p,g,m,v,h,l,f,d,a,o-i.A,a,o,w);return u+=t.fa,t.j+y<t.o?(n(e.ec,e.fc,c,u,w),n(e.cc,e.dc,h,l,x),n(e.Mc,e.Nc,f,d,x),r--):1&y||s(c,u,null,null,h,l,f,d,h,l,f,d,a,o+i.A,null,null,w),r}function tl(t,r,n){var i=t.F,a=[t.J];if(null!=i){var o=t.U,s=r.ba.S,c=s==ns||s==nf;r=r.ba.f.RGBA;var u=[0],h=t.ka;u[0]=t.T,t.Kb&&(0==h?--u[0]:(--h,a[0]-=t.width),t.j+t.ka+t.T==t.o&&(u[0]=t.o-t.j-h));var l=r.eb;h=r.fb+h*r.A,t=r5(i,a[0],t.width,o,u,l,h+3*!c,r.A),e(n==u),t&&tn(s)&&r1(l,h,c,o,u,r.A)}return 0}function tf(t){var e=t.ma,r=e.ba.S,n=11>r,i=r==ni||r==no||r==ns||r==nc||12==r||tn(r);if(e.memory=null,e.Ib=null,e.Jb=null,e.Nd=null,!rd(e.Oa,t,i?11:12))return 0;if(i&&tn(r)&&e6(),t.da)alert("todo:use_scaling");else{if(n){if(e.Ib=tu,t.Kb){if(r=t.U+1>>1,e.memory=a(t.U+2*r),null==e.memory)return 0;e.ec=e.memory,e.fc=0,e.cc=e.ec,e.dc=e.fc+t.U,e.Mc=e.cc,e.Nc=e.dc+r,e.Ib=th,e6()}}else alert("todo:EmitYUV");i&&(e.Jb=tl,n&&e3())}if(n&&!n$){for(t=0;256>t;++t)nQ[t]=89858*(t-128)+nY>>nG,n5[t]=-22014*(t-128)+nY,n2[t]=-45773*(t-128),n1[t]=113618*(t-128)+nY>>nG;for(t=nJ;t<nX;++t)e=76283*(t-16)+nY>>nG,n0[t-nJ]=tV(e,255),n3[t-nJ]=tV(e+8>>4,15);n$=1}return 1}function td(t){var r=t.ma,n=t.U,i=t.T;return e(!(1&t.ka)),0>=n||0>=i?0:(n=r.Ib(t,r),null!=r.Jb&&r.Jb(t,r,n),r.Dc+=n,1)}function tp(t){t.ma.memory=null}function tg(t,e,r,n){return 47!=y(t,8)?0:(e[0]=y(t,14)+1,r[0]=y(t,14)+1,n[0]=y(t,1),0!=y(t,3)?0:!t.h)}function tm(t,e){if(4>t)return t+1;var r=t-2>>1;return(2+(1&t)<<r)+y(e,r)+1}function tv(t,e){var r;return 120<e?e-120:1<=(r=((r=ny[e-1])>>4)*t+(8-(15&r)))?r:1}function tb(t,e,r){var n=N(r),i=t[e+=255&n].g-8;return 0<i&&(L(r,r.u+8),n=N(r),e+=t[e].value,e+=n&(1<<i)-1),L(r,r.u+t[e].g),t[e].value}function ty(t,r,n){return n.g+=t.g,n.value+=t.value<<r>>>0,e(8>=n.g),t.g}function tw(t,r,n){var i=t.xc;return e((r=0==i?0:t.vc[t.md*(n>>i)+(r>>i)])<t.Wb),t.Ya[r]}function tx(t,r,i,a){var o=t.ab,s=t.c*r,c=t.C;r=c+r;var u=i,h=a;for(a=t.Ta,i=t.Ua;0<o--;){var l=t.gc[o],f=c,d=r,p=u,g=h,m=(h=a,u=i,l.Ea);switch(e(f<d),e(d<=l.nc),l.hc){case 2:rL(p,g,(d-f)*m,h,u);break;case 0:var v=f,b=d,y=h,w=u,x=(_=l).Ea;0==v&&(rN(p,g,null,null,1,y,w),V(p,g+1,0,0,x-1,y,w+1),g+=x,w+=x,++v);for(var N=1<<_.b,A=N-1,L=B(x,_.b),S=_.K,_=_.w+(v>>_.b)*L;v<b;){var P=S,k=_,F=1;for(rA(p,g,y,w-x,1,y,w);F<x;){var I=(F&~A)+N;I>x&&(I=x),(0,rF[P[k++]>>8&15])(p,g+ +F,y,w+F-x,I-F,y,w+F),F=I}g+=x,w+=x,++v&A||(_+=L)}d!=l.nc&&n(h,u-m,h,u+(d-f-1)*m,m);break;case 1:for(m=p,b=g,x=(p=l.Ea)-(w=p&~(y=(g=1<<l.b)-1)),v=B(p,l.b),N=l.K,l=l.w+(f>>l.b)*v;f<d;){for(A=N,L=l,S=new T,_=b+w,P=b+p;b<_;)Y(A[L++],S),rI(S,m,b,g,h,u),b+=g,u+=g;b<P&&(Y(A[L++],S),rI(S,m,b,x,h,u),b+=x,u+=x),++f&y||(l+=v)}break;case 3:if(p==h&&g==u&&0<l.b){for(b=h,p=m=u+(d-f)*m-(w=(d-f)*B(l.Ea,l.b)),g=h,y=u,v=[],w=(x=w)-1;0<=w;--w)v[w]=g[y+w];for(w=x-1;0<=w;--w)b[p+w]=v[w];rS(l,f,d,h,m,h,u)}else rS(l,f,d,p,g,h,u)}u=a,h=i}h!=i&&n(a,i,u,h,s)}function tN(t,r){var n=t.V,i=t.Ba+t.c*t.C,a=r-t.C;if(e(r<=t.l.o),e(16>=a),0<a){var o=t.l,s=t.Ta,c=t.Ua,u=o.width;if(tx(t,a,n,i),a=c=[c],e((n=t.C)<(i=r)),e(o.v<o.va),i>o.o&&(i=o.o),n<o.j){var h=o.j-n;n=o.j,a[0]+=h*u}if(n>=i?n=0:(a[0]+=4*o.v,o.ka=n-o.j,o.U=o.va-o.v,o.T=i-n,n=1),n){if(c=c[0],11>(n=t.ca).S){var l=n.f.RGBA,f=(i=n.S,a=o.U,o=o.T,h=l.eb,l.A),d=o;for(l=l.fb+t.Ma*l.A;0<d--;){var p=c,g=a,m=h,v=l;switch(i){case nn:rC(s,p,g,m,v);break;case ni:rj(s,p,g,m,v);break;case nh:rj(s,p,g,m,v),r1(m,v,0,g,1,0);break;case na:rM(s,p,g,m,v);break;case no:te(s,p,g,m,v,1);break;case nl:te(s,p,g,m,v,1),r1(m,v,0,g,1,0);break;case ns:te(s,p,g,m,v,0);break;case nf:te(s,p,g,m,v,0),r1(m,v,1,g,1,0);break;case nc:rO(s,p,g,m,v);break;case nd:rO(s,p,g,m,v),r2(m,v,g,1,0);break;case nu:rE(s,p,g,m,v);break;default:e(0)}c+=u,l+=f}t.Ma+=o}else alert("todo:EmitRescaledRowsYUVA");e(t.Ma<=n.height)}}t.C=r,e(t.C<=t.i)}function tA(t){var e;if(0<t.ua)return 0;for(e=0;e<t.Wb;++e){var r=t.Ya[e].G,n=t.Ya[e].H;if(0<r[1][n[1]+0].g||0<r[2][n[2]+0].g||0<r[3][n[3]+0].g)return 0}return 1}function tL(t,r,n,i,a,o){if(0!=t.Z){var s=t.qd,c=t.rd;for(e(null!=nB[t.Z]);r<n;++r)nB[t.Z](s,c,i,a,i,a,o),s=i,c=a,a+=o;t.qd=s,t.rd=c}}function tS(t,r){var n=t.l.ma,i=0==n.Z||1==n.Z?t.l.j:t.C;if(i=t.C<i?i:t.C,e(r<=t.l.o),r>i){var a=t.l.width,o=n.ca,s=n.tb+a*i,c=t.V,u=t.Ba+t.c*i,h=t.gc;e(1==t.ab),e(3==h[0].hc),rP(h[0],i,r,c,u,o,s),tL(n,i,r,o,s,a)}t.C=t.Ma=r}function t_(t,r,n,i,a,o,s){var c=t.$/i,u=t.$%i,h=t.m,l=t.s,f=n+t.$,d=f;a=n+i*a;var p=n+i*o,g=280+l.ua,m=t.Pb?c:0x1000000,v=0<l.ua?l.Wa:null,b=l.wc,y=f<p?tw(l,u,c):null;e(t.C<o),e(p<=a);var w=!1;t:for(;;){for(;w||f<p;){var x=0;if(c>=m){var _=f-n;e((m=t).Pb),m.wd=m.m,m.xd=_,0<m.s.ua&&E(m.s.Wa,m.s.vb),m=c+nx}if(u&b||(y=tw(l,u,c)),e(null!=y),y.Qb&&(r[f]=y.qb,w=!0),!w)if(S(h),y.jc){x=h,_=r;var P=f,k=y.pd[N(x)&rm-1];e(y.jc),256>k.g?(L(x,x.u+k.g),_[P]=k.value,x=0):(L(x,x.u+k.g-256),e(256<=k.value),x=k.value),0==x&&(w=!0)}else x=tb(y.G[0],y.H[0],h);if(h.h)break;if(w||256>x){if(!w)if(y.nd)r[f]=(y.qb|x<<8)>>>0;else{if(S(h),w=tb(y.G[1],y.H[1],h),S(h),_=tb(y.G[2],y.H[2],h),P=tb(y.G[3],y.H[3],h),h.h)break;r[f]=(P<<24|w<<16|x<<8|_)>>>0}if(w=!1,++f,++u>=i&&(u=0,++c,null!=s&&c<=o&&!(c%16)&&s(t,c),null!=v))for(;d<f;)x=r[d++],v.X[(0x1e35a7bd*x|0)>>>v.Mb]=x}else if(280>x){if(x=tm(x-256,h),_=tb(y.G[4],y.H[4],h),S(h),_=tv(i,_=tm(_,h)),h.h)break;if(f-n<_||a-f<x)break t;for(P=0;P<x;++P)r[f+P]=r[f+P-_];for(f+=x,u+=x;u>=i;)u-=i,++c,null!=s&&c<=o&&!(c%16)&&s(t,c);if(e(f<=a),u&b&&(y=tw(l,u,c)),null!=v)for(;d<f;)x=r[d++],v.X[(0x1e35a7bd*x|0)>>>v.Mb]=x}else{if(!(x<g))break t;for(w=x-280,e(null!=v);d<f;)x=r[d++],v.X[(0x1e35a7bd*x|0)>>>v.Mb]=x;x=f,e(!(w>>>(_=v).Xa)),r[x]=_.X[w],w=!0}w||e(h.h==A(h))}if(t.Pb&&h.h&&f<a)e(t.m.h),t.a=5,t.m=t.wd,t.$=t.xd,0<t.s.ua&&E(t.s.vb,t.s.Wa);else{if(h.h)break;null!=s&&s(t,c>o?o:c),t.a=0,t.$=f-n}return 1}return t.a=3,0}function tP(t){e(null!=t),t.vc=null,t.yc=null,t.Ya=null;var r=t.Wa;null!=r&&(r.X=null),t.vb=null,e(null!=t)}function tk(){var e=new eX;return null==e?null:(e.a=0,e.xb=nq,tr("Predictor","VP8LPredictors"),tr("Predictor","VP8LPredictors_C"),tr("PredictorAdd","VP8LPredictorsAdd"),tr("PredictorAdd","VP8LPredictorsAdd_C"),rL=G,rI=J,rC=K,rj=Z,rO=$,rE=Q,rM=tt,t.VP8LMapColor32b=r_,t.VP8LMapColor8b=rk,e)}function tF(t,r,n,s,c){for(var u=1,f=[t],p=[r],g=s.m,m=s.s,v=null,b=0;;){if(n)for(;u&&y(g,1);){var w=f,x=p,A=1,_=s.m,P=s.gc[s.ab],k=y(_,2);if(s.Oc&1<<k)u=0;else{switch(s.Oc|=1<<k,P.hc=k,P.Ea=w[0],P.nc=x[0],P.K=[null],++s.ab,e(4>=s.ab),k){case 0:case 1:P.b=y(_,3)+2,A=tF(B(P.Ea,P.b),B(P.nc,P.b),0,s,P.K),P.K=P.K[0];break;case 3:var F,I=y(_,8)+1,C=16<I?0:4<I?1:2<I?2:3;if(w[0]=B(P.Ea,C),P.b=C,F=A=tF(I,1,0,s,P.K)){var j,E=1<<(8>>P.b),M=a(E);if(null==M)F=0;else{var q=P.K[0],R=P.w;for(M[0]=P.K[0][0],j=1;j<+I;++j)M[j]=D(q[R+j],M[j-1]);for(;j<4*E;++j)M[j]=0;P.K[0]=null,P.K[0]=M,F=1}}A=F;break;case 2:break;default:e(0)}u=A}}if(f=f[0],p=p[0],u&&y(g,1)&&!(u=1<=(b=y(g,4))&&11>=b)){s.a=3;break}if(T=u)e:{var T,z,U,H,W=f,V=p,G=b,Y=s.m,J=s.s,X=[null],K=1,Z=0,$=nw[G];r:for(;;){if(n&&y(Y,1)){var Q=y(Y,3)+2,tt=B(W,Q),te=B(V,Q),tr=tt*te;if(!tF(tt,te,0,s,X))break;for(X=X[0],J.xc=Q,z=0;z<tr;++z){var tn=X[z]>>8&65535;X[z]=tn,tn>=K&&(K=tn+1)}}if(Y.h)break;for(U=0;5>U;++U){var ti=nm[U];!U&&0<G&&(ti+=1<<G),Z<ti&&(Z=ti)}var ta=o(K*$,l),to=K,ts=o(to,d);if(null==ts)var tc=null;else e(65536>=to),tc=ts;var tu=a(Z);if(null==tc||null==tu||null==ta){s.a=1;break}for(z=H=0;z<K;++z){var th,tl=tc[z],tf=tl.G,td=tl.H,tp=0,tg=1,tm=0;for(U=0;5>U;++U){ti=nm[U],tf[U]=ta,td[U]=H,!U&&0<G&&(ti+=1<<G);n:{var tv,tb=ti,tw=H,tx=0,tN=s.m,tA=y(tN,1);if(i(tu,0,0,tb),tA){var tL=y(tN,1)+1,tS=y(tN,1),tk=y(tN,0==tS?1:8);tu[tk]=1,2==tL&&(tu[tk=y(tN,8)]=1);var tI=1}else{var tC=a(19),tj=y(tN,4)+4;if(19<tj){s.a=3;var tO=0;break n}for(tv=0;tv<tj;++tv)tC[nb[tv]]=y(tN,3);var tE=void 0,tM=void 0,tq=0,tB=s.m,tD=8,tR=o(128,l);i:for(;h(tR,0,7,tC,19);){if(y(tB,1)){var tT=2+2*y(tB,3);if((tE=2+y(tB,tT))>tb)break}else tE=tb;for(tM=0;tM<tb&&tE--;){S(tB);var tz=tR[0+(127&N(tB))];L(tB,tB.u+tz.g);var tU=tz.value;if(16>tU)tu[tM++]=tU,0!=tU&&(tD=tU);else{var tH=16==tU,tW=tU-16,tV=ng[tW],tG=y(tB,np[tW])+tV;if(tM+tG>tb)break i;for(var tY=tH?tD:0;0<tG--;)tu[tM++]=tY}}tq=1;break}tq||(s.a=3),tI=tq}(tI=tI&&!tN.h)&&(tx=h(ta,tw,8,tu,tb)),tI&&0!=tx?tO=tx:(s.a=3,tO=0)}if(0==tO)break r;if(tg&&1==nv[U]&&(tg=0==ta[H].g),tp+=ta[H].g,H+=tO,3>=U){var tJ,tX=tu[0];for(tJ=1;tJ<ti;++tJ)tu[tJ]>tX&&(tX=tu[tJ]);tm+=tX}}if(tl.nd=tg,tl.Qb=0,tg&&(tl.qb=(tf[3][td[3]+0].value<<24|tf[1][td[1]+0].value<<16|tf[2][td[2]+0].value)>>>0,0==tp&&256>tf[0][td[0]+0].value&&(tl.Qb=1,tl.qb+=tf[0][td[0]+0].value<<8)),tl.jc=!tl.Qb&&6>tm,tl.jc)for(th=0;th<rm;++th){var tK=th,tZ=tl.pd[tK],t$=tl.G[0][tl.H[0]+tK];256<=t$.value?(tZ.g=t$.g+256,tZ.value=t$.value):(tZ.g=0,tZ.value=0,tK>>=ty(t$,8,tZ),tK>>=ty(tl.G[1][tl.H[1]+tK],16,tZ),tK>>=ty(tl.G[2][tl.H[2]+tK],0,tZ),ty(tl.G[3][tl.H[3]+tK],24,tZ))}}J.vc=X,J.Wb=K,J.Ya=tc,J.yc=ta,T=1;break e}T=0}if(!(u=T)){s.a=3;break}if(0<b){if(m.ua=1<<b,!O(m.Wa,b)){s.a=1,u=0;break}}else m.ua=0;var tQ=f,t1=p,t2=s.s,t5=t2.xc;if(s.c=tQ,s.i=t1,t2.md=B(tQ,t5),t2.wc=0==t5?-1:(1<<t5)-1,n){s.xb=nM;break}if(null==(v=a(f*p))){s.a=1,u=0;break}u=(u=t_(s,v,0,f,p,p,null))&&!g.h;break}return u?(null!=c?c[0]=v:(e(null==v),e(n)),s.$=0,n||tP(m)):tP(m),u}function tI(t,r){var n=t.c*t.i;return e(t.c<=r),t.V=a(n+r+16*r),null==t.V?(t.Ta=null,t.Ua=0,t.a=1,0):(t.Ta=t.V,t.Ua=t.Ba+n+r,1)}function tC(t,r){var n=t.C,i=r-n,a=t.V,o=t.Ba+t.c*n;for(e(r<=t.l.o);0<i;){var s=16<i?16:i,c=t.l.ma,u=t.l.width,h=u*s,l=c.ca,f=c.tb+u*n,d=t.Ta,p=t.Ua;tx(t,s,a,o),r0(d,p,l,f,h),tL(c,n,n+s,l,f,u),i-=s,a+=s*t.c,n+=s}e(n==r),t.C=t.Ma=r}function tj(){this.ub=this.yd=this.td=this.Rb=0}function tO(){this.Kd=this.Ld=this.Ud=this.Td=this.i=this.c=0}function tE(){this.Fb=this.Bb=this.Cb=0,this.Zb=a(4),this.Lb=a(4)}function tM(){var t;this.Yb=(function t(e,r,n){for(var i=n[r],a=0;a<i&&(e.push(n.length>r+1?[]:0),!(n.length<r+1));a++)t(e[a],r+1,n)}(t=[],0,[3,11]),t)}function tq(){this.jb=a(3),this.Wc=s([4,8],tM),this.Xc=s([4,17],tM)}function tB(){this.Pc=this.wb=this.Tb=this.zd=0,this.vd=new a(4),this.od=new a(4)}function tD(){this.ld=this.La=this.dd=this.tc=0}function tR(){this.Na=this.la=0}function tT(){this.Sc=[0,0],this.Eb=[0,0],this.Qc=[0,0],this.ia=this.lc=0}function tz(){this.ad=a(384),this.Za=0,this.Ob=a(16),this.$b=this.Ad=this.ia=this.Gc=this.Hc=this.Dd=0}function tU(){this.uc=this.M=this.Nb=0,this.wa=Array(new tD),this.Y=0,this.ya=Array(new tz),this.aa=0,this.l=new tG}function tH(){this.y=a(16),this.f=a(8),this.ea=a(8)}function tW(){this.cb=this.a=0,this.sc="",this.m=new w,this.Od=new tj,this.Kc=new tO,this.ed=new tB,this.Qa=new tE,this.Ic=this.$c=this.Aa=0,this.D=new tU,this.Xb=this.Va=this.Hb=this.zb=this.yb=this.Ub=this.za=0,this.Jc=o(8,w),this.ia=0,this.pb=o(4,tT),this.Pa=new tq,this.Bd=this.kc=0,this.Ac=[],this.Bc=0,this.zc=[0,0,0,0],this.Gd=Array(new tH),this.Hd=0,this.rb=Array(new tR),this.sb=0,this.wa=Array(new tD),this.Y=0,this.oc=[],this.pc=0,this.sa=[],this.ta=0,this.qa=[],this.ra=0,this.Ha=[],this.B=this.R=this.Ia=0,this.Ec=[],this.M=this.ja=this.Vb=this.Fc=0,this.ya=Array(new tz),this.L=this.aa=0,this.gd=s([4,2],tD),this.ga=null,this.Fa=[],this.Cc=this.qc=this.P=0,this.Gb=[],this.Uc=0,this.mb=[],this.nb=0,this.rc=[],this.Ga=this.Vc=0}function tV(t,e){return 0>t?0:t>e?e:t}function tG(){this.T=this.U=this.ka=this.height=this.width=0,this.y=[],this.f=[],this.ea=[],this.Rc=this.fa=this.W=this.N=this.O=0,this.ma="void",this.put="VP8IoPutHook",this.ac="VP8IoSetupHook",this.bc="VP8IoTeardownHook",this.ha=this.Kb=0,this.data=[],this.hb=this.ib=this.da=this.o=this.j=this.va=this.v=this.Da=this.ob=this.w=0,this.F=[],this.J=0}function tY(){var t=new tW;return null!=t&&(t.a=0,t.sc="OK",t.cb=0,t.Xb=0,nL||(nL=tZ)),t}function tJ(t,e,r){return 0==t.a&&(t.a=e,t.sc=r,t.cb=0),0}function tX(t,e,r){return 3<=r&&157==t[e+0]&&1==t[e+1]&&42==t[e+2]}function tK(t,r){if(null==t)return 0;if(t.a=0,t.sc="OK",null==r)return tJ(t,2,"null VP8Io passed to VP8GetHeaders()");var n=r.data,a=r.w,o=r.ha;if(4>o)return tJ(t,7,"Truncated header.");var s=n[a+0]|n[a+1]<<8|n[a+2]<<16,c=t.Od;if(c.Rb=!(1&s),c.td=s>>1&7,c.yd=s>>4&1,c.ub=s>>5,3<c.td)return tJ(t,3,"Incorrect keyframe parameters.");if(!c.yd)return tJ(t,4,"Frame not displayable.");a+=3,o-=3;var u=t.Kc;if(c.Rb){if(7>o)return tJ(t,7,"cannot parse picture header");if(!tX(n,a,o))return tJ(t,3,"Bad code word");u.c=16383&(n[a+4]<<8|n[a+3]),u.Td=n[a+4]>>6,u.i=16383&(n[a+6]<<8|n[a+5]),u.Ud=n[a+6]>>6,a+=7,o-=7,t.za=u.c+15>>4,t.Ub=u.i+15>>4,r.width=u.c,r.height=u.i,r.Da=0,r.j=0,r.v=0,r.va=r.width,r.o=r.height,r.da=0,r.ib=r.width,r.hb=r.height,r.U=r.width,r.T=r.height,i((s=t.Pa).jb,0,255,s.jb.length),e(null!=(s=t.Qa)),s.Cb=0,s.Bb=0,s.Fb=1,i(s.Zb,0,0,s.Zb.length),i(s.Lb,0,0,s.Lb)}if(c.ub>o)return tJ(t,7,"bad partition length");p(s=t.m,n,a,c.ub),a+=c.ub,o-=c.ub,c.Rb&&(u.Ld=P(s),u.Kd=P(s)),u=t.Qa;var h,l=t.Pa;if(e(null!=s),e(null!=u),u.Cb=P(s),u.Cb){if(u.Bb=P(s),P(s)){for(u.Fb=P(s),h=0;4>h;++h)u.Zb[h]=P(s)?m(s,7):0;for(h=0;4>h;++h)u.Lb[h]=P(s)?m(s,6):0}if(u.Bb)for(h=0;3>h;++h)l.jb[h]=P(s)?g(s,8):255}else u.Bb=0;if(s.Ka)return tJ(t,3,"cannot parse segment header");if((u=t.ed).zd=P(s),u.Tb=g(s,6),u.wb=g(s,3),u.Pc=P(s),u.Pc&&P(s)){for(l=0;4>l;++l)P(s)&&(u.vd[l]=m(s,6));for(l=0;4>l;++l)P(s)&&(u.od[l]=m(s,6))}if(t.L=0==u.Tb?0:u.zd?1:2,s.Ka)return tJ(t,3,"cannot parse filter header");var f=o;if(o=h=a,a=h+f,u=f,t.Xb=(1<<g(t.m,2))-1,f<3*(l=t.Xb))n=7;else{for(h+=3*l,u-=3*l,f=0;f<l;++f){var d=n[o+0]|n[o+1]<<8|n[o+2]<<16;d>u&&(d=u),p(t.Jc[+f],n,h,d),h+=d,u-=d,o+=3}p(t.Jc[+l],n,h,u),n=h<a?0:5}if(0!=n)return tJ(t,n,"cannot parse partitions");for(n=g(h=t.m,7),o=P(h)?m(h,4):0,a=P(h)?m(h,4):0,u=P(h)?m(h,4):0,l=P(h)?m(h,4):0,h=P(h)?m(h,4):0,f=t.Qa,d=0;4>d;++d){if(f.Cb){var v=f.Zb[d];f.Fb||(v+=n)}else{if(0<d){t.pb[d]=t.pb[0];continue}v=n}var b=t.pb[d];b.Sc[0]=nN[tV(v+o,127)],b.Sc[1]=nA[tV(v+0,127)],b.Eb[0]=2*nN[tV(v+a,127)],b.Eb[1]=101581*nA[tV(v+u,127)]>>16,8>b.Eb[1]&&(b.Eb[1]=8),b.Qc[0]=nN[tV(v+l,117)],b.Qc[1]=nA[tV(v+h,127)],b.lc=v+h}if(!c.Rb)return tJ(t,4,"Not a key frame.");for(P(s),c=t.Pa,n=0;4>n;++n){for(o=0;8>o;++o)for(a=0;3>a;++a)for(u=0;11>u;++u)l=k(s,nI[n][o][a][u])?g(s,8):nk[n][o][a][u],c.Wc[n][o].Yb[a][u]=l;for(o=0;17>o;++o)c.Xc[n][o]=c.Wc[n][nC[o]]}return t.kc=P(s),t.kc&&(t.Bd=g(s,8)),t.cb=1}function tZ(t,e,r,n,i,a,o){var s=e[i].Yb[r];for(r=0;16>i;++i){if(!k(t,s[r+0]))return i;for(;!k(t,s[r+1]);)if(s=e[++i].Yb[0],r=0,16==i)return 16;var c=e[i+1].Yb;if(k(t,s[r+2])){var u=t,h=0;if(k(u,(f=s)[(l=r)+3]))if(k(u,f[l+6])){for(s=0,l=2*(h=k(u,f[l+8]))+(f=k(u,f[l+9+h])),h=0,f=nS[l];f[s];++s)h+=h+k(u,f[s]);h+=3+(8<<l)}else h=k(u,f[l+7])?7+2*k(u,165)+k(u,145):5+k(u,159);else h=k(u,f[l+4])?3+k(u,f[l+5]):2;s=c[2]}else h=1,s=c[1];c=o+n_[i],0>(u=t).b&&_(u);var l,f=u.b,d=(l=u.Ca>>1)-(u.I>>f)>>31;--u.b,u.Ca+=d,u.Ca|=1,u.I-=(l+1&d)<<f,a[c]=((h^d)-d)*n[(0<i)+0]}return 16}function t$(t){var e=t.rb[t.sb-1];e.la=0,e.Na=0,i(t.zc,0,0,t.zc.length),t.ja=0}function tQ(t,e,r,n,i){i=t[e+r+32*n]+(i>>3),t[e+r+32*n]=-256&i?0>i?0:255:i}function t1(t,e,r,n,i,a){tQ(t,e,0,r,n+i),tQ(t,e,1,r,n+a),tQ(t,e,2,r,n-a),tQ(t,e,3,r,n-i)}function t2(t){return(20091*t>>16)+t}function t5(t,e,r,n){var i,o=0,s=a(16);for(i=0;4>i;++i){var c=t[e+0]+t[e+8],u=t[e+0]-t[e+8],h=(35468*t[e+4]>>16)-t2(t[e+12]),l=t2(t[e+4])+(35468*t[e+12]>>16);s[o+0]=c+l,s[o+1]=u+h,s[o+2]=u-h,s[o+3]=c-l,o+=4,e++}for(i=o=0;4>i;++i)c=(t=s[o+0]+4)+s[o+8],u=t-s[o+8],h=(35468*s[o+4]>>16)-t2(s[o+12]),tQ(r,n,0,0,c+(l=t2(s[o+4])+(35468*s[o+12]>>16))),tQ(r,n,1,0,u+h),tQ(r,n,2,0,u-h),tQ(r,n,3,0,c-l),o++,n+=32}function t0(t,e,r,n){var i=t[e+0]+4,a=35468*t[e+4]>>16,o=t2(t[e+4]),s=35468*t[e+1]>>16;t1(r,n,0,i+o,t=t2(t[e+1]),s),t1(r,n,1,i+a,t,s),t1(r,n,2,i-a,t,s),t1(r,n,3,i-o,t,s)}function t3(t,e,r,n,i){t5(t,e,r,n),i&&t5(t,e+16,r,n+4)}function t4(t,e,r,n){rB(t,e+0,r,n,1),rB(t,e+32,r,n+128,1)}function t6(t,e,r,n){var i;for(t=t[e+0]+4,i=0;4>i;++i)for(e=0;4>e;++e)tQ(r,n,e,i,t)}function t8(t,e,r,n){t[e+0]&&rT(t,e+0,r,n),t[e+16]&&rT(t,e+16,r,n+4),t[e+32]&&rT(t,e+32,r,n+128),t[e+48]&&rT(t,e+48,r,n+128+4)}function t7(t,e,r,n){var i,o=a(16);for(i=0;4>i;++i){var s=t[e+0+i]+t[e+12+i],c=t[e+4+i]+t[e+8+i],u=t[e+4+i]-t[e+8+i],h=t[e+0+i]-t[e+12+i];o[0+i]=s+c,o[8+i]=s-c,o[4+i]=h+u,o[12+i]=h-u}for(i=0;4>i;++i)s=(t=o[0+4*i]+3)+o[3+4*i],c=o[1+4*i]+o[2+4*i],u=o[1+4*i]-o[2+4*i],h=t-o[3+4*i],r[n+0]=s+c>>3,r[n+16]=h+u>>3,r[n+32]=s-c>>3,r[n+48]=h-u>>3,n+=64}function t9(t,e,r){var n,i=e-32,a=255-t[i-1];for(n=0;n<r;++n){var o,s=a+t[e-1];for(o=0;o<r;++o)t[e+o]=ne[s+t[i+o]];e+=32}}function et(t,e){t9(t,e,4)}function ee(t,e){t9(t,e,8)}function er(t,e){t9(t,e,16)}function en(t,e){var r;for(r=0;16>r;++r)n(t,e+32*r,t,e-32,16)}function ei(t,e){var r;for(r=16;0<r;--r)i(t,e,t[e-1],16),e+=32}function ea(t,e,r){var n;for(n=0;16>n;++n)i(e,r+32*n,t,16)}function eo(t,e){var r,n=16;for(r=0;16>r;++r)n+=t[e-1+32*r]+t[e+r-32];ea(n>>5,t,e)}function es(t,e){var r,n=8;for(r=0;16>r;++r)n+=t[e-1+32*r];ea(n>>4,t,e)}function ec(t,e){var r,n=8;for(r=0;16>r;++r)n+=t[e+r-32];ea(n>>4,t,e)}function eu(t,e){ea(128,t,e)}function eh(t,e,r){return t+2*e+r+2>>2}function el(t,e){var r,i=e-32;for(r=0,i=new Uint8Array([eh(t[i-1],t[i+0],t[i+1]),eh(t[i+0],t[i+1],t[i+2]),eh(t[i+1],t[i+2],t[i+3]),eh(t[i+2],t[i+3],t[i+4])]);4>r;++r)n(t,e+32*r,i,0,i.length)}function ef(t,e){var r=t[e-1],n=t[e-1+32],i=t[e-1+64],a=t[e-1+96];F(t,e+0,0x1010101*eh(t[e-1-32],r,n)),F(t,e+32,0x1010101*eh(r,n,i)),F(t,e+64,0x1010101*eh(n,i,a)),F(t,e+96,0x1010101*eh(i,a,a))}function ed(t,e){var r,n=4;for(r=0;4>r;++r)n+=t[e+r-32]+t[e-1+32*r];for(n>>=3,r=0;4>r;++r)i(t,e+32*r,n,4)}function ep(t,e){var r=t[e-1+0],n=t[e-1+32],i=t[e-1+64],a=t[e-1-32],o=t[e+0-32],s=t[e+1-32],c=t[e+2-32],u=t[e+3-32];t[e+0+96]=eh(n,i,t[e-1+96]),t[e+1+96]=t[e+0+64]=eh(r,n,i),t[e+2+96]=t[e+1+64]=t[e+0+32]=eh(a,r,n),t[e+3+96]=t[e+2+64]=t[e+1+32]=t[e+0+0]=eh(o,a,r),t[e+3+64]=t[e+2+32]=t[e+1+0]=eh(s,o,a),t[e+3+32]=t[e+2+0]=eh(c,s,o),t[e+3+0]=eh(u,c,s)}function eg(t,e){var r=t[e+1-32],n=t[e+2-32],i=t[e+3-32],a=t[e+4-32],o=t[e+5-32],s=t[e+6-32],c=t[e+7-32];t[e+0+0]=eh(t[e+0-32],r,n),t[e+1+0]=t[e+0+32]=eh(r,n,i),t[e+2+0]=t[e+1+32]=t[e+0+64]=eh(n,i,a),t[e+3+0]=t[e+2+32]=t[e+1+64]=t[e+0+96]=eh(i,a,o),t[e+3+32]=t[e+2+64]=t[e+1+96]=eh(a,o,s),t[e+3+64]=t[e+2+96]=eh(o,s,c),t[e+3+96]=eh(s,c,c)}function em(t,e){var r=t[e-1+0],n=t[e-1+32],i=t[e-1+64],a=t[e-1-32],o=t[e+0-32],s=t[e+1-32],c=t[e+2-32],u=t[e+3-32];t[e+0+0]=t[e+1+64]=a+o+1>>1,t[e+1+0]=t[e+2+64]=o+s+1>>1,t[e+2+0]=t[e+3+64]=s+c+1>>1,t[e+3+0]=c+u+1>>1,t[e+0+96]=eh(i,n,r),t[e+0+64]=eh(n,r,a),t[e+0+32]=t[e+1+96]=eh(r,a,o),t[e+1+32]=t[e+2+96]=eh(a,o,s),t[e+2+32]=t[e+3+96]=eh(o,s,c),t[e+3+32]=eh(s,c,u)}function ev(t,e){var r=t[e+0-32],n=t[e+1-32],i=t[e+2-32],a=t[e+3-32],o=t[e+4-32],s=t[e+5-32],c=t[e+6-32],u=t[e+7-32];t[e+0+0]=r+n+1>>1,t[e+1+0]=t[e+0+64]=n+i+1>>1,t[e+2+0]=t[e+1+64]=i+a+1>>1,t[e+3+0]=t[e+2+64]=a+o+1>>1,t[e+0+32]=eh(r,n,i),t[e+1+32]=t[e+0+96]=eh(n,i,a),t[e+2+32]=t[e+1+96]=eh(i,a,o),t[e+3+32]=t[e+2+96]=eh(a,o,s),t[e+3+64]=eh(o,s,c),t[e+3+96]=eh(s,c,u)}function eb(t,e){var r=t[e-1+0],n=t[e-1+32],i=t[e-1+64],a=t[e-1+96];t[e+0+0]=r+n+1>>1,t[e+2+0]=t[e+0+32]=n+i+1>>1,t[e+2+32]=t[e+0+64]=i+a+1>>1,t[e+1+0]=eh(r,n,i),t[e+3+0]=t[e+1+32]=eh(n,i,a),t[e+3+32]=t[e+1+64]=eh(i,a,a),t[e+3+64]=t[e+2+64]=t[e+0+96]=t[e+1+96]=t[e+2+96]=t[e+3+96]=a}function ey(t,e){var r=t[e-1+0],n=t[e-1+32],i=t[e-1+64],a=t[e-1+96],o=t[e-1-32],s=t[e+0-32],c=t[e+1-32],u=t[e+2-32];t[e+0+0]=t[e+2+32]=r+o+1>>1,t[e+0+32]=t[e+2+64]=n+r+1>>1,t[e+0+64]=t[e+2+96]=i+n+1>>1,t[e+0+96]=a+i+1>>1,t[e+3+0]=eh(s,c,u),t[e+2+0]=eh(o,s,c),t[e+1+0]=t[e+3+32]=eh(r,o,s),t[e+1+32]=t[e+3+64]=eh(n,r,o),t[e+1+64]=t[e+3+96]=eh(i,n,r),t[e+1+96]=eh(a,i,n)}function ew(t,e){var r;for(r=0;8>r;++r)n(t,e+32*r,t,e-32,8)}function ex(t,e){var r;for(r=0;8>r;++r)i(t,e,t[e-1],8),e+=32}function eN(t,e,r){var n;for(n=0;8>n;++n)i(e,r+32*n,t,8)}function eA(t,e){var r,n=8;for(r=0;8>r;++r)n+=t[e+r-32]+t[e-1+32*r];eN(n>>4,t,e)}function eL(t,e){var r,n=4;for(r=0;8>r;++r)n+=t[e+r-32];eN(n>>3,t,e)}function eS(t,e){var r,n=4;for(r=0;8>r;++r)n+=t[e-1+32*r];eN(n>>3,t,e)}function e_(t,e){eN(128,t,e)}function eP(t,e,r){var n=t[e-r],i=t[e+0],a=3*(i-n)+r9[1020+t[e-2*r]-t[e+r]],o=nt[112+(a+4>>3)];t[e-r]=ne[255+n+nt[112+(a+3>>3)]],t[e+0]=ne[255+i-o]}function ek(t,e,r,n){var i=t[e+0],a=t[e+r];return nr[255+t[e-2*r]-t[e-r]]>n||nr[255+a-i]>n}function eF(t,e,r,n){return 4*nr[255+t[e-r]-t[e+0]]+nr[255+t[e-2*r]-t[e+r]]<=n}function eI(t,e,r,n,i){var a=t[e-3*r],o=t[e-2*r],s=t[e-r],c=t[e+0],u=t[e+r],h=t[e+2*r],l=t[e+3*r];return 4*nr[255+s-c]+nr[255+o-u]>n?0:nr[255+t[e-4*r]-a]<=i&&nr[255+a-o]<=i&&nr[255+o-s]<=i&&nr[255+l-h]<=i&&nr[255+h-u]<=i&&nr[255+u-c]<=i}function eC(t,e,r,n){var i=2*n+1;for(n=0;16>n;++n)eF(t,e+n,r,i)&&eP(t,e+n,r)}function ej(t,e,r,n){var i=2*n+1;for(n=0;16>n;++n)eF(t,e+n*r,1,i)&&eP(t,e+n*r,1)}function eO(t,e,r,n){var i;for(i=3;0<i;--i)eC(t,e+=4*r,r,n)}function eE(t,e,r,n){var i;for(i=3;0<i;--i)ej(t,e+=4,r,n)}function eM(t,e,r,n,i,a,o,s){for(a=2*a+1;0<i--;){if(eI(t,e,r,a,o))if(ek(t,e,r,s))eP(t,e,r);else{var c=e,u=t[c-2*r],h=t[c-r],l=t[c+0],f=t[c+r],d=t[c+2*r],p=27*(m=r9[1020+3*(l-h)+r9[1020+u-f]])+63>>7,g=18*m+63>>7,m=9*m+63>>7;t[c-3*r]=ne[255+t[c-3*r]+m],t[c-2*r]=ne[255+u+g],t[c-r]=ne[255+h+p],t[c+0]=ne[255+l-p],t[c+r]=ne[255+f-g],t[c+2*r]=ne[255+d-m]}e+=n}}function eq(t,e,r,n,i,a,o,s){for(a=2*a+1;0<i--;){if(eI(t,e,r,a,o))if(ek(t,e,r,s))eP(t,e,r);else{var c=e,u=t[c-r],h=t[c+0],l=t[c+r],f=nt[112+((d=3*(h-u))+4>>3)],d=nt[112+(d+3>>3)],p=f+1>>1;t[c-2*r]=ne[255+t[c-2*r]+p],t[c-r]=ne[255+u+d],t[c+0]=ne[255+h-f],t[c+r]=ne[255+l-p]}e+=n}}function eB(t,e,r,n,i,a){eM(t,e,r,1,16,n,i,a)}function eD(t,e,r,n,i,a){eM(t,e,1,r,16,n,i,a)}function eR(t,e,r,n,i,a){var o;for(o=3;0<o;--o)eq(t,e+=4*r,r,1,16,n,i,a)}function eT(t,e,r,n,i,a){var o;for(o=3;0<o;--o)eq(t,e+=4,1,r,16,n,i,a)}function ez(t,e,r,n,i,a,o,s){eM(t,e,i,1,8,a,o,s),eM(r,n,i,1,8,a,o,s)}function eU(t,e,r,n,i,a,o,s){eM(t,e,1,i,8,a,o,s),eM(r,n,1,i,8,a,o,s)}function eH(t,e,r,n,i,a,o,s){eq(t,e+4*i,i,1,8,a,o,s),eq(r,n+4*i,i,1,8,a,o,s)}function eW(t,e,r,n,i,a,o,s){eq(t,e+4,1,i,8,a,o,s),eq(r,n+4,1,i,8,a,o,s)}function eV(){this.ba=new to,this.ec=[],this.cc=[],this.Mc=[],this.Dc=this.Nc=this.dc=this.fc=0,this.Oa=new tc,this.memory=0,this.Ib="OutputFunc",this.Jb="OutputAlphaFunc",this.Nd="OutputRowFunc"}function eG(){this.data=[],this.offset=this.kd=this.ha=this.w=0,this.na=[],this.xa=this.gb=this.Ja=this.Sa=this.P=0}function eY(){this.nc=this.Ea=this.b=this.hc=0,this.K=[],this.w=0}function eJ(){this.ua=0,this.Wa=new M,this.vb=new M,this.md=this.xc=this.wc=0,this.vc=[],this.Wb=0,this.Ya=new d,this.yc=new l}function eX(){this.xb=this.a=0,this.l=new tG,this.ca=new to,this.V=[],this.Ba=0,this.Ta=[],this.Ua=0,this.m=new x,this.Pb=0,this.wd=new x,this.Ma=this.$=this.C=this.i=this.c=this.xd=0,this.s=new eJ,this.ab=0,this.gc=o(4,eY),this.Oc=0}function eK(){this.Lc=this.Z=this.$a=this.i=this.c=0,this.l=new tG,this.ic=0,this.ca=[],this.tb=0,this.qd=null,this.rd=0}function eZ(t,e,r,n,i,a,o){for(t=null==t?0:t[e+0],e=0;e<o;++e)i[a+e]=t+r[n+e]&255,t=i[a+e]}function e$(t,e,r,n,i,a,o){var s;if(null==t)eZ(null,null,r,n,i,a,o);else for(s=0;s<o;++s)i[a+s]=t[e+s]+r[n+s]&255}function eQ(t,e,r,n,i,a,o){if(null==t)eZ(null,null,r,n,i,a,o);else{var s,c=t[e+0],u=c,h=c;for(s=0;s<o;++s)u=h+(c=t[e+s])-u,h=r[n+s]+(-256&u?0>u?0:255:u)&255,u=c,i[a+s]=h}}function e1(t,e,r,n,i,a){for(;0<i--;){var o,s=e+ +!!r,c=e+3*!r;for(o=0;o<n;++o){var u=t[c+4*o];255!=u&&(u*=32897,t[s+4*o+0]=t[s+4*o+0]*u>>23,t[s+4*o+1]=t[s+4*o+1]*u>>23,t[s+4*o+2]=t[s+4*o+2]*u>>23)}e+=a}}function e2(t,e,r,n,i){for(;0<n--;){var a;for(a=0;a<r;++a){var o=t[e+2*a+0],s=15&(u=t[e+2*a+1]),c=4369*s,u=(240&u|u>>4)*c>>16;t[e+2*a+0]=(240&o|o>>4)*c>>16&240|(15&o|o<<4)*c>>16>>4&15,t[e+2*a+1]=240&u|s}e+=i}}function e5(t,e,r,n,i,a,o,s){var c,u,h=255;for(u=0;u<i;++u){for(c=0;c<n;++c){var l=t[e+c];a[o+4*c]=l,h&=l}e+=r,o+=s}return 255!=h}function e0(t,e,r,n,i){var a;for(a=0;a<i;++a)r[n+a]=t[e+a]>>8}function e3(){r1=e1,r2=e2,r5=e5,r0=e0}function e4(r,n,i){t[r]=function(t,r,a,o,s,c,u,h,l,f,d,p,g,m,v,b,y){var w,x=y-1>>1,N=s[c+0]|u[h+0]<<16,A=l[f+0]|d[p+0]<<16;e(null!=t);var L=3*N+A+131074>>2;for(n(t[r+0],255&L,L>>16,g,m),null!=a&&(L=3*A+N+131074>>2,n(a[o+0],255&L,L>>16,v,b)),w=1;w<=x;++w){var S=s[c+w]|u[h+w]<<16,_=l[f+w]|d[p+w]<<16,P=N+S+A+_+524296,k=P+2*(S+A)>>3;L=k+N>>1,N=(P=P+2*(N+_)>>3)+S>>1,n(t[r+2*w-1],255&L,L>>16,g,m+(2*w-1)*i),n(t[r+2*w-0],255&N,N>>16,g,m+(2*w-0)*i),null!=a&&(L=P+A>>1,N=k+_>>1,n(a[o+2*w-1],255&L,L>>16,v,b+(2*w-1)*i),n(a[o+2*w+0],255&N,N>>16,v,b+(2*w+0)*i)),N=S,A=_}1&y||(L=3*N+A+131074>>2,n(t[r+y-1],255&L,L>>16,g,m+(y-1)*i),null!=a&&(L=3*A+N+131074>>2,n(a[o+y-1],255&L,L>>16,v,b+(y-1)*i)))}}function e6(){nD[nn]=nR,nD[ni]=nz,nD[na]=nT,nD[no]=nU,nD[ns]=nH,nD[nc]=nW,nD[nu]=nV,nD[nh]=nz,nD[nl]=nU,nD[nf]=nH,nD[nd]=nW}function e8(t){return t&~nZ?0>t?0:255:t>>nK}function e7(t,e){return e8((19077*t>>8)+(26149*e>>8)-14234)}function e9(t,e,r){return e8((19077*t>>8)-(6419*e>>8)-(13320*r>>8)+8708)}function rt(t,e){return e8((19077*t>>8)+(33050*e>>8)-17685)}function re(t,e,r,n,i){n[i+0]=e7(t,r),n[i+1]=e9(t,e,r),n[i+2]=rt(t,e)}function rr(t,e,r,n,i){n[i+0]=rt(t,e),n[i+1]=e9(t,e,r),n[i+2]=e7(t,r)}function rn(t,e,r,n,i){var a=e9(t,e,r);e=a<<3&224|rt(t,e)>>3,n[i+0]=248&e7(t,r)|a>>5,n[i+1]=e}function ri(t,e,r,n,i){var a=240&rt(t,e)|15;n[i+0]=240&e7(t,r)|e9(t,e,r)>>4,n[i+1]=a}function ra(t,e,r,n,i){n[i+0]=255,re(t,e,r,n,i+1)}function ro(t,e,r,n,i){rr(t,e,r,n,i),n[i+3]=255}function rs(t,e,r,n,i){re(t,e,r,n,i),n[i+3]=255}function tV(t,e){return 0>t?0:t>e?e:t}function rc(e,r,n){t[e]=function(t,e,i,a,o,s,c,u,h){for(var l=u+(-2&h)*n;u!=l;)r(t[e+0],i[a+0],o[s+0],c,u),r(t[e+1],i[a+0],o[s+0],c,u+n),e+=2,++a,++s,u+=2*n;1&h&&r(t[e+0],i[a+0],o[s+0],c,u)}}function ru(t,e,r){return 0==r?0==t?0==e?6:5:4*(0==e):r}function rh(t,e,r,n,i){switch(t>>>30){case 3:rB(e,r,n,i,0);break;case 2:rD(e,r,n,i);break;case 1:rT(e,r,n,i)}}function rl(t,e){var r,a,o=e.M,s=e.Nb,c=t.oc,u=t.pc+40,h=t.oc,l=t.pc+584,f=t.oc,d=t.pc+600;for(r=0;16>r;++r)c[u+32*r-1]=129;for(r=0;8>r;++r)h[l+32*r-1]=129,f[d+32*r-1]=129;for(0<o?c[u-1-32]=h[l-1-32]=f[d-1-32]=129:(i(c,u-32-1,127,21),i(h,l-32-1,127,9),i(f,d-32-1,127,9)),a=0;a<t.za;++a){var p=e.ya[e.aa+a];if(0<a){for(r=-1;16>r;++r)n(c,u+32*r-4,c,u+32*r+12,4);for(r=-1;8>r;++r)n(h,l+32*r-4,h,l+32*r+4,4),n(f,d+32*r-4,f,d+32*r+4,4)}var g=t.Gd,m=t.Hd+a,v=p.ad,b=p.Hc;if(0<o&&(n(c,u-32,g[m].y,0,16),n(h,l-32,g[m].f,0,8),n(f,d-32,g[m].ea,0,8)),p.Za){var y=c,w=u-32+16;for(0<o&&(a>=t.za-1?i(y,w,g[m].y[15],4):n(y,w,g[m+1].y,0,4)),r=0;4>r;r++)y[w+128+r]=y[w+256+r]=y[w+384+r]=y[w+0+r];for(r=0;16>r;++r,b<<=2)y=c,w=u+n4[r],nO[p.Ob[r]](y,w),rh(b,v,16*r,y,w)}else if(nj[y=ru(a,o,p.Ob[0])](c,u),0!=b)for(r=0;16>r;++r,b<<=2)rh(b,v,16*r,c,u+n4[r]);for(r=p.Gc,nE[y=ru(a,o,p.Dd)](h,l),nE[y](f,d),b=v,y=h,w=l,255&(p=0|r)&&(170&p?rR(b,256,y,w):rz(b,256,y,w)),p=f,b=d,255&(r>>=8)&&(170&r?rR(v,320,p,b):rz(v,320,p,b)),o<t.Ub-1&&(n(g[m].y,0,c,u+480,16),n(g[m].f,0,h,l+224,8),n(g[m].ea,0,f,d+224,8)),r=8*s*t.B,g=t.sa,m=t.ta+16*a+16*s*t.R,v=t.qa,p=t.ra+8*a+r,b=t.Ha,y=t.Ia+8*a+r,r=0;16>r;++r)n(g,m+r*t.R,c,u+32*r,16);for(r=0;8>r;++r)n(v,p+r*t.B,h,l+32*r,8),n(b,y+r*t.B,f,d+32*r,8)}}function rf(t,n,i,a,o,s,c,u,h){var l=[0],f=[0],d=0,p=null!=h?h.kd:0,g=null!=h?h:new eG;if(null==t||12>i)return 7;g.data=t,g.w=n,g.ha=i,n=[n],i=[i],g.gb=[g.gb];t:{var m=n,b=i,y=g.gb;if(e(null!=t),e(null!=b),e(null!=y),y[0]=0,12<=b[0]&&!r(t,m[0],"RIFF")){if(r(t,m[0]+8,"WEBP")){y=3;break t}var w=j(t,m[0]+4);if(12>w||0xfffffff6<w){y=3;break t}if(p&&w>b[0]-8){y=7;break t}y[0]=w,m[0]+=12,b[0]-=12}y=0}if(0!=y)return y;for(w=0<g.gb[0],i=i[0];;){t:{var N=t;b=n,y=i;var A=l,L=f,S=m=[0];if((k=d=[d])[0]=0,8>y[0])y=7;else{if(!r(N,b[0],"VP8X")){if(10!=j(N,b[0]+4)){y=3;break t}if(18>y[0]){y=7;break t}var _=j(N,b[0]+8),P=1+C(N,b[0]+12);if(0x80000000<=P*(N=1+C(N,b[0]+15))){y=3;break t}null!=S&&(S[0]=_),null!=A&&(A[0]=P),null!=L&&(L[0]=N),b[0]+=18,y[0]-=18,k[0]=1}y=0}}if(d=d[0],m=m[0],0!=y)return y;if(b=!!(2&m),!w&&d)return 3;if(null!=s&&(s[0]=!!(16&m)),null!=c&&(c[0]=b),null!=u&&(u[0]=0),c=l[0],m=f[0],d&&b&&null==h){y=0;break}if(4>i){y=7;break}if(w&&d||!w&&!d&&!r(t,n[0],"ALPH")){i=[i],g.na=[g.na],g.P=[g.P],g.Sa=[g.Sa];t:{_=t,y=n,w=i;var k=g.gb;A=g.na,L=g.P,S=g.Sa,P=22,e(null!=_),e(null!=w),N=y[0];var F=w[0];for(e(null!=A),e(null!=S),A[0]=null,L[0]=null,S[0]=0;;){if(y[0]=N,w[0]=F,8>F){y=7;break t}var I=j(_,N+4);if(0xfffffff6<I){y=3;break t}var O=8+I+1&-2;if(P+=O,0<k&&P>k){y=3;break t}if(!r(_,N,"VP8 ")||!r(_,N,"VP8L")){y=0;break t}if(F[0]<O){y=7;break t}r(_,N,"ALPH")||(A[0]=_,L[0]=N+8,S[0]=I),N+=O,F-=O}}if(i=i[0],g.na=g.na[0],g.P=g.P[0],g.Sa=g.Sa[0],0!=y)break}i=[i],g.Ja=[g.Ja],g.xa=[g.xa];t:if(k=t,y=n,w=i,A=g.gb[0],L=g.Ja,S=g.xa,N=!r(k,_=y[0],"VP8 "),P=!r(k,_,"VP8L"),e(null!=k),e(null!=w),e(null!=L),e(null!=S),8>w[0])y=7;else{if(N||P){if(k=j(k,_+4),12<=A&&k>A-12){y=3;break t}if(p&&k>w[0]-8){y=7;break t}L[0]=k,y[0]+=8,w[0]-=8,S[0]=P}else S[0]=5<=w[0]&&47==k[_+0]&&!(k[_+4]>>5),L[0]=w[0];y=0}if(i=i[0],g.Ja=g.Ja[0],g.xa=g.xa[0],n=n[0],0!=y)break;if(0xfffffff6<g.Ja)return 3;if(null==u||b||(u[0]=g.xa?2:1),c=[c],m=[m],g.xa){if(5>i){y=7;break}u=c,p=m,b=s,null==t||5>i?t=0:5<=i&&47==t[n+0]&&!(t[n+4]>>5)?(w=[0],k=[0],A=[0],v(L=new x,t,n,i),tg(L,w,k,A)?(null!=u&&(u[0]=w[0]),null!=p&&(p[0]=k[0]),null!=b&&(b[0]=A[0]),t=1):t=0):t=0}else{if(10>i){y=7;break}u=m,null==t||10>i||!tX(t,n+3,i-3)?t=0:(p=t[n+0]|t[n+1]<<8|t[n+2]<<16,b=16383&(t[n+7]<<8|t[n+6]),t=16383&(t[n+9]<<8|t[n+8]),1&p||3<(p>>1&7)||!(p>>4&1)||p>>5>=g.Ja||!b||!t?t=0:(c&&(c[0]=b),u&&(u[0]=t),t=1))}if(!t||(c=c[0],m=m[0],d&&(l[0]!=c||f[0]!=m)))return 3;null!=h&&(h[0]=g,h.offset=n-h.w,e(0xfffffff6>n-h.w),e(h.offset==h.ha-i));break}return 0==y||7==y&&d&&null==h?(null!=s&&(s[0]|=null!=g.na&&0<g.na.length),null!=a&&(a[0]=c),null!=o&&(o[0]=m),0):y}function rd(t,e,r){var n=e.width,i=e.height,a=0,o=0,s=n,c=i;if(e.Da=null!=t&&0<t.Da,e.Da&&(s=t.cd,c=t.bd,a=t.v,o=t.j,11>r||(a&=-2,o&=-2),0>a||0>o||0>=s||0>=c||a+s>n||o+c>i))return 0;if(e.v=a,e.j=o,e.va=a+s,e.o=o+c,e.U=s,e.T=c,e.da=null!=t&&0<t.da,e.da){if(!q(s,c,r=[t.ib],a=[t.hb]))return 0;e.ib=r[0],e.hb=a[0]}return e.ob=null!=t&&t.ob,e.Kb=null==t||!t.Sd,e.da&&(e.ob=e.ib<3*n/4&&e.hb<3*i/4,e.Kb=0),1}function rp(t){if(null==t)return 2;if(11>t.S){var e=t.f.RGBA;e.fb+=(t.height-1)*e.A,e.A=-e.A}else e=t.f.kb,t=t.height,e.O+=(t-1)*e.fa,e.fa=-e.fa,e.N+=(t-1>>1)*e.Ab,e.Ab=-e.Ab,e.W+=(t-1>>1)*e.Db,e.Db=-e.Db,null!=e.F&&(e.J+=(t-1)*e.lb,e.lb=-e.lb);return 0}function rg(t,e,r,n){if(null==n||0>=t||0>=e)return 2;if(null!=r){if(r.Da){var i=r.cd,o=r.bd,s=-2&r.v,c=-2&r.j;if(0>s||0>c||0>=i||0>=o||s+i>t||c+o>e)return 2;t=i,e=o}if(r.da){if(!q(t,e,i=[r.ib],o=[r.hb]))return 2;t=i[0],e=o[0]}}n.width=t,n.height=e;t:{var u=n.width,h=n.height;if(t=n.S,0>=u||0>=h||!(t>=nn&&13>t))t=2;else{if(0>=n.Rd&&null==n.sd){s=o=i=e=0;var l=(c=u*n7[t])*h;if(11>t||(o=(h+1)/2*(e=(u+1)/2),12==t&&(s=(i=u)*h)),null==(h=a(l+2*o+s))){t=1;break t}n.sd=h,11>t?((u=n.f.RGBA).eb=h,u.fb=0,u.A=c,u.size=l):((u=n.f.kb).y=h,u.O=0,u.fa=c,u.Fd=l,u.f=h,u.N=0+l,u.Ab=e,u.Cd=o,u.ea=h,u.W=0+l+o,u.Db=e,u.Ed=o,12==t&&(u.F=h,u.J=0+l+2*o),u.Tc=s,u.lb=i)}if(e=1,i=n.S,o=n.width,s=n.height,i>=nn&&13>i)if(11>i)e&=(c=Math.abs((t=n.f.RGBA).A))*(s-1)+o<=t.size,e&=c>=o*n7[i],e&=null!=t.eb;else{t=n.f.kb,c=(o+1)/2,l=(s+1)/2,u=Math.abs(t.fa),h=Math.abs(t.Ab);var f=Math.abs(t.Db),d=Math.abs(t.lb),p=d*(s-1)+o;e&=u*(s-1)+o<=t.Fd,e&=h*(l-1)+c<=t.Cd,e=(e&=f*(l-1)+c<=t.Ed)&u>=o&h>=c&f>=c&null!=t.y&null!=t.f&null!=t.ea,12==i&&(e&=d>=o,e&=p<=t.Tc,e&=null!=t.F)}else e=0;t=2*!e}}return 0!=t||null!=r&&r.fd&&(t=rp(n)),t}var rm=64,rv=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535,131071,262143,524287,1048575,2097151,4194303,8388607,0xffffff],rb=24,ry=32,rw=8,rx=[0,0,1,1,2,2,2,2,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7];R("Predictor0","PredictorAdd0"),t.Predictor0=function(){return 0xff000000},t.Predictor1=function(t){return t},t.Predictor2=function(t,e,r){return e[r+0]},t.Predictor3=function(t,e,r){return e[r+1]},t.Predictor4=function(t,e,r){return e[r-1]},t.Predictor5=function(t,e,r){return z(z(t,e[r+1]),e[r+0])},t.Predictor6=function(t,e,r){return z(t,e[r-1])},t.Predictor7=function(t,e,r){return z(t,e[r+0])},t.Predictor8=function(t,e,r){return z(e[r-1],e[r+0])},t.Predictor9=function(t,e,r){return z(e[r+0],e[r+1])},t.Predictor10=function(t,e,r){return z(z(t,e[r-1]),z(e[r+0],e[r+1]))},t.Predictor11=function(t,e,r){var n=e[r+0];return 0>=W(n>>24&255,t>>24&255,(e=e[r-1])>>24&255)+W(n>>16&255,t>>16&255,e>>16&255)+W(n>>8&255,t>>8&255,e>>8&255)+W(255&n,255&t,255&e)?n:t},t.Predictor12=function(t,e,r){var n=e[r+0];return(U((t>>24&255)+(n>>24&255)-((e=e[r-1])>>24&255))<<24|U((t>>16&255)+(n>>16&255)-(e>>16&255))<<16|U((t>>8&255)+(n>>8&255)-(e>>8&255))<<8|U((255&t)+(255&n)-(255&e)))>>>0},t.Predictor13=function(t,e,r){var n=e[r-1];return(H((t=z(t,e[r+0]))>>24&255,n>>24&255)<<24|H(t>>16&255,n>>16&255)<<16|H(t>>8&255,n>>8&255)<<8|H((0|t)&255,(0|n)&255))>>>0};var rN=t.PredictorAdd0;t.PredictorAdd1=V,R("Predictor2","PredictorAdd2"),R("Predictor3","PredictorAdd3"),R("Predictor4","PredictorAdd4"),R("Predictor5","PredictorAdd5"),R("Predictor6","PredictorAdd6"),R("Predictor7","PredictorAdd7"),R("Predictor8","PredictorAdd8"),R("Predictor9","PredictorAdd9"),R("Predictor10","PredictorAdd10"),R("Predictor11","PredictorAdd11"),R("Predictor12","PredictorAdd12"),R("Predictor13","PredictorAdd13");var rA=t.PredictorAdd2;X("ColorIndexInverseTransform","MapARGB","32b",function(t){return t>>8&255},function(t){return t}),X("VP8LColorIndexInverseTransformAlpha","MapAlpha","8b",function(t){return t},function(t){return t>>8&255});var rL,rS=t.ColorIndexInverseTransform,r_=t.MapARGB,rP=t.VP8LColorIndexInverseTransformAlpha,rk=t.MapAlpha,rF=t.VP8LPredictorsAdd=[];rF.length=16,(t.VP8LPredictors=[]).length=16,(t.VP8LPredictorsAdd_C=[]).length=16,(t.VP8LPredictors_C=[]).length=16;var rI,rC,rj,rO,rE,rM,rq,rB,rD,rR,rT,rz,rU,rH,rW,rV,rG,rY,rJ,rX,rK,rZ,r$,rQ,r1,r2,r5,r0,r3=a(511),r4=a(2041),r6=a(225),r8=a(767),r7=0,r9=r4,nt=r6,ne=r8,nr=r3,nn=0,ni=1,na=2,no=3,ns=4,nc=5,nu=6,nh=7,nl=8,nf=9,nd=10,np=[2,3,7],ng=[3,3,11],nm=[280,256,256,256,40],nv=[0,1,1,1,0],nb=[17,18,0,1,2,3,4,5,16,6,7,8,9,10,11,12,13,14,15],ny=[24,7,23,25,40,6,39,41,22,26,38,42,56,5,55,57,21,27,54,58,37,43,72,4,71,73,20,28,53,59,70,74,36,44,88,69,75,52,60,3,87,89,19,29,86,90,35,45,68,76,85,91,51,61,104,2,103,105,18,30,102,106,34,46,84,92,67,77,101,107,50,62,120,1,119,121,83,93,17,31,100,108,66,78,118,122,33,47,117,123,49,63,99,109,82,94,0,116,124,65,79,16,32,98,110,48,115,125,81,95,64,114,126,97,111,80,113,127,96,112],nw=[2954,2956,2958,2962,2970,2986,3018,3082,3212,3468,3980,5004],nx=8,nN=[4,5,6,7,8,9,10,10,11,12,13,14,15,16,17,17,18,19,20,20,21,21,22,22,23,23,24,25,25,26,27,28,29,30,31,32,33,34,35,36,37,37,38,39,40,41,42,43,44,45,46,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,76,77,78,79,80,81,82,83,84,85,86,87,88,89,91,93,95,96,98,100,101,102,104,106,108,110,112,114,116,118,122,124,126,128,130,132,134,136,138,140,143,145,148,151,154,157],nA=[4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,60,62,64,66,68,70,72,74,76,78,80,82,84,86,88,90,92,94,96,98,100,102,104,106,108,110,112,114,116,119,122,125,128,131,134,137,140,143,146,149,152,155,158,161,164,167,170,173,177,181,185,189,193,197,201,205,209,213,217,221,225,229,234,239,245,249,254,259,264,269,274,279,284],nL=null,nS=[[173,148,140,0],[176,155,140,135,0],[180,157,141,134,130,0],[254,254,243,230,196,177,153,140,133,130,129,0]],n_=[0,1,4,8,5,2,3,6,9,12,13,10,7,11,14,15],nP=[-0,1,-1,2,-2,3,4,6,-3,5,-4,-5,-6,7,-7,8,-8,-9],nk=[[[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]],[[253,136,254,255,228,219,128,128,128,128,128],[189,129,242,255,227,213,255,219,128,128,128],[106,126,227,252,214,209,255,255,128,128,128]],[[1,98,248,255,236,226,255,255,128,128,128],[181,133,238,254,221,234,255,154,128,128,128],[78,134,202,247,198,180,255,219,128,128,128]],[[1,185,249,255,243,255,128,128,128,128,128],[184,150,247,255,236,224,128,128,128,128,128],[77,110,216,255,236,230,128,128,128,128,128]],[[1,101,251,255,241,255,128,128,128,128,128],[170,139,241,252,236,209,255,255,128,128,128],[37,116,196,243,228,255,255,255,128,128,128]],[[1,204,254,255,245,255,128,128,128,128,128],[207,160,250,255,238,128,128,128,128,128,128],[102,103,231,255,211,171,128,128,128,128,128]],[[1,152,252,255,240,255,128,128,128,128,128],[177,135,243,255,234,225,128,128,128,128,128],[80,129,211,255,194,224,128,128,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[246,1,255,128,128,128,128,128,128,128,128],[255,128,128,128,128,128,128,128,128,128,128]]],[[[198,35,237,223,193,187,162,160,145,155,62],[131,45,198,221,172,176,220,157,252,221,1],[68,47,146,208,149,167,221,162,255,223,128]],[[1,149,241,255,221,224,255,255,128,128,128],[184,141,234,253,222,220,255,199,128,128,128],[81,99,181,242,176,190,249,202,255,255,128]],[[1,129,232,253,214,197,242,196,255,255,128],[99,121,210,250,201,198,255,202,128,128,128],[23,91,163,242,170,187,247,210,255,255,128]],[[1,200,246,255,234,255,128,128,128,128,128],[109,178,241,255,231,245,255,255,128,128,128],[44,130,201,253,205,192,255,255,128,128,128]],[[1,132,239,251,219,209,255,165,128,128,128],[94,136,225,251,218,190,255,255,128,128,128],[22,100,174,245,186,161,255,199,128,128,128]],[[1,182,249,255,232,235,128,128,128,128,128],[124,143,241,255,227,234,128,128,128,128,128],[35,77,181,251,193,211,255,205,128,128,128]],[[1,157,247,255,236,231,255,255,128,128,128],[121,141,235,255,225,227,255,255,128,128,128],[45,99,188,251,195,217,255,224,128,128,128]],[[1,1,251,255,213,255,128,128,128,128,128],[203,1,248,255,255,128,128,128,128,128,128],[137,1,177,255,224,255,128,128,128,128,128]]],[[[253,9,248,251,207,208,255,192,128,128,128],[175,13,224,243,193,185,249,198,255,255,128],[73,17,171,221,161,179,236,167,255,234,128]],[[1,95,247,253,212,183,255,255,128,128,128],[239,90,244,250,211,209,255,255,128,128,128],[155,77,195,248,188,195,255,255,128,128,128]],[[1,24,239,251,218,219,255,205,128,128,128],[201,51,219,255,196,186,128,128,128,128,128],[69,46,190,239,201,218,255,228,128,128,128]],[[1,191,251,255,255,128,128,128,128,128,128],[223,165,249,255,213,255,128,128,128,128,128],[141,124,248,255,255,128,128,128,128,128,128]],[[1,16,248,255,255,128,128,128,128,128,128],[190,36,230,255,236,255,128,128,128,128,128],[149,1,255,128,128,128,128,128,128,128,128]],[[1,226,255,128,128,128,128,128,128,128,128],[247,192,255,128,128,128,128,128,128,128,128],[240,128,255,128,128,128,128,128,128,128,128]],[[1,134,252,255,255,128,128,128,128,128,128],[213,62,250,255,255,128,128,128,128,128,128],[55,93,255,128,128,128,128,128,128,128,128]],[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]]],[[[202,24,213,235,186,191,220,160,240,175,255],[126,38,182,232,169,184,228,174,255,187,128],[61,46,138,219,151,178,240,170,255,216,128]],[[1,112,230,250,199,191,247,159,255,255,128],[166,109,228,252,211,215,255,174,128,128,128],[39,77,162,232,172,180,245,178,255,255,128]],[[1,52,220,246,198,199,249,220,255,255,128],[124,74,191,243,183,193,250,221,255,255,128],[24,71,130,219,154,170,243,182,255,255,128]],[[1,182,225,249,219,240,255,224,128,128,128],[149,150,226,252,216,205,255,171,128,128,128],[28,108,170,242,183,194,254,223,255,255,128]],[[1,81,230,252,204,203,255,192,128,128,128],[123,102,209,247,188,196,255,233,128,128,128],[20,95,153,243,164,173,255,203,128,128,128]],[[1,222,248,255,216,213,128,128,128,128,128],[168,175,246,252,235,205,255,255,128,128,128],[47,116,215,255,211,212,255,255,128,128,128]],[[1,121,236,253,212,214,255,255,128,128,128],[141,84,213,252,201,202,255,219,128,128,128],[42,80,160,240,162,185,255,205,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[244,1,255,128,128,128,128,128,128,128,128],[238,1,255,128,128,128,128,128,128,128,128]]]],nF=[[[231,120,48,89,115,113,120,152,112],[152,179,64,126,170,118,46,70,95],[175,69,143,80,85,82,72,155,103],[56,58,10,171,218,189,17,13,152],[114,26,17,163,44,195,21,10,173],[121,24,80,195,26,62,44,64,85],[144,71,10,38,171,213,144,34,26],[170,46,55,19,136,160,33,206,71],[63,20,8,114,114,208,12,9,226],[81,40,11,96,182,84,29,16,36]],[[134,183,89,137,98,101,106,165,148],[72,187,100,130,157,111,32,75,80],[66,102,167,99,74,62,40,234,128],[41,53,9,178,241,141,26,8,107],[74,43,26,146,73,166,49,23,157],[65,38,105,160,51,52,31,115,128],[104,79,12,27,217,255,87,17,7],[87,68,71,44,114,51,15,186,23],[47,41,14,110,182,183,21,17,194],[66,45,25,102,197,189,23,18,22]],[[88,88,147,150,42,46,45,196,205],[43,97,183,117,85,38,35,179,61],[39,53,200,87,26,21,43,232,171],[56,34,51,104,114,102,29,93,77],[39,28,85,171,58,165,90,98,64],[34,22,116,206,23,34,43,166,73],[107,54,32,26,51,1,81,43,31],[68,25,106,22,64,171,36,225,114],[34,19,21,102,132,188,16,76,124],[62,18,78,95,85,57,50,48,51]],[[193,101,35,159,215,111,89,46,111],[60,148,31,172,219,228,21,18,111],[112,113,77,85,179,255,38,120,114],[40,42,1,196,245,209,10,25,109],[88,43,29,140,166,213,37,43,154],[61,63,30,155,67,45,68,1,209],[100,80,8,43,154,1,51,26,71],[142,78,78,16,255,128,34,197,171],[41,40,5,102,211,183,4,1,221],[51,50,17,168,209,192,23,25,82]],[[138,31,36,171,27,166,38,44,229],[67,87,58,169,82,115,26,59,179],[63,59,90,180,59,166,93,73,154],[40,40,21,116,143,209,34,39,175],[47,15,16,183,34,223,49,45,183],[46,17,33,183,6,98,15,32,183],[57,46,22,24,128,1,54,17,37],[65,32,73,115,28,128,23,128,205],[40,3,9,115,51,192,18,6,223],[87,37,9,115,59,77,64,21,47]],[[104,55,44,218,9,54,53,130,226],[64,90,70,205,40,41,23,26,57],[54,57,112,184,5,41,38,166,213],[30,34,26,133,152,116,10,32,134],[39,19,53,221,26,114,32,73,255],[31,9,65,234,2,15,1,118,73],[75,32,12,51,192,255,160,43,51],[88,31,35,67,102,85,55,186,85],[56,21,23,111,59,205,45,37,192],[55,38,70,124,73,102,1,34,98]],[[125,98,42,88,104,85,117,175,82],[95,84,53,89,128,100,113,101,45],[75,79,123,47,51,128,81,171,1],[57,17,5,71,102,57,53,41,49],[38,33,13,121,57,73,26,1,85],[41,10,67,138,77,110,90,47,114],[115,21,2,10,102,255,166,23,6],[101,29,16,10,85,128,101,196,26],[57,18,10,102,102,213,34,20,43],[117,20,15,36,163,128,68,1,26]],[[102,61,71,37,34,53,31,243,192],[69,60,71,38,73,119,28,222,37],[68,45,128,34,1,47,11,245,171],[62,17,19,70,146,85,55,62,70],[37,43,37,154,100,163,85,160,1],[63,9,92,136,28,64,32,201,85],[75,15,9,9,64,255,184,119,16],[86,6,28,5,64,255,25,248,1],[56,8,17,132,137,255,55,116,128],[58,15,20,82,135,57,26,121,40]],[[164,50,31,137,154,133,25,35,218],[51,103,44,131,131,123,31,6,158],[86,40,64,135,148,224,45,183,128],[22,26,17,131,240,154,14,1,209],[45,16,21,91,64,222,7,1,197],[56,21,39,155,60,138,23,102,213],[83,12,13,54,192,255,68,47,28],[85,26,85,85,128,128,32,146,171],[18,11,7,63,144,171,4,4,246],[35,27,10,146,174,171,12,26,128]],[[190,80,35,99,180,80,126,54,45],[85,126,47,87,176,51,41,20,32],[101,75,128,139,118,146,116,128,85],[56,41,15,176,236,85,37,9,62],[71,30,17,119,118,255,17,18,138],[101,38,60,138,55,70,43,26,142],[146,36,19,30,171,255,97,27,20],[138,45,61,62,219,1,81,188,64],[32,41,20,117,151,142,20,21,163],[112,19,12,61,195,128,48,4,24]]],nI=[[[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[176,246,255,255,255,255,255,255,255,255,255],[223,241,252,255,255,255,255,255,255,255,255],[249,253,253,255,255,255,255,255,255,255,255]],[[255,244,252,255,255,255,255,255,255,255,255],[234,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255]],[[255,246,254,255,255,255,255,255,255,255,255],[239,253,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[251,255,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[251,254,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,254,253,255,254,255,255,255,255,255,255],[250,255,254,255,254,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[217,255,255,255,255,255,255,255,255,255,255],[225,252,241,253,255,255,254,255,255,255,255],[234,250,241,250,253,255,253,254,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[223,254,254,255,255,255,255,255,255,255,255],[238,253,254,254,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[249,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,255,255,255,255,255,255,255,255,255],[247,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[252,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[186,251,250,255,255,255,255,255,255,255,255],[234,251,244,254,255,255,255,255,255,255,255],[251,251,243,253,254,255,254,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[236,253,254,255,255,255,255,255,255,255,255],[251,253,253,254,254,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[254,254,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[254,254,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[248,255,255,255,255,255,255,255,255,255,255],[250,254,252,254,255,255,255,255,255,255,255],[248,254,249,253,255,255,255,255,255,255,255]],[[255,253,253,255,255,255,255,255,255,255,255],[246,253,253,255,255,255,255,255,255,255,255],[252,254,251,254,254,255,255,255,255,255,255]],[[255,254,252,255,255,255,255,255,255,255,255],[248,254,253,255,255,255,255,255,255,255,255],[253,255,254,254,255,255,255,255,255,255,255]],[[255,251,254,255,255,255,255,255,255,255,255],[245,251,254,255,255,255,255,255,255,255,255],[253,253,254,255,255,255,255,255,255,255,255]],[[255,251,253,255,255,255,255,255,255,255,255],[252,253,254,255,255,255,255,255,255,255,255],[255,254,255,255,255,255,255,255,255,255,255]],[[255,252,255,255,255,255,255,255,255,255,255],[249,255,254,255,255,255,255,255,255,255,255],[255,255,254,255,255,255,255,255,255,255,255]],[[255,255,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]]],nC=[0,1,2,3,6,4,5,6,6,6,6,6,6,6,6,7,0],nj=[],nO=[],nE=[],nM=1,nq=2,nB=[],nD=[];e4("UpsampleRgbLinePair",re,3),e4("UpsampleBgrLinePair",rr,3),e4("UpsampleRgbaLinePair",rs,4),e4("UpsampleBgraLinePair",ro,4),e4("UpsampleArgbLinePair",ra,4),e4("UpsampleRgba4444LinePair",ri,2),e4("UpsampleRgb565LinePair",rn,2);var nR=t.UpsampleRgbLinePair,nT=t.UpsampleBgrLinePair,nz=t.UpsampleRgbaLinePair,nU=t.UpsampleBgraLinePair,nH=t.UpsampleArgbLinePair,nW=t.UpsampleRgba4444LinePair,nV=t.UpsampleRgb565LinePair,nG=16,nY=32768,nJ=-227,nX=482,nK=6,nZ=16383,n$=0,nQ=a(256),n1=a(256),n2=a(256),n5=a(256),n0=a(nX-nJ),n3=a(nX-nJ);rc("YuvToRgbRow",re,3),rc("YuvToBgrRow",rr,3),rc("YuvToRgbaRow",rs,4),rc("YuvToBgraRow",ro,4),rc("YuvToArgbRow",ra,4),rc("YuvToRgba4444Row",ri,2),rc("YuvToRgb565Row",rn,2);var n4=[0,4,8,12,128,132,136,140,256,260,264,268,384,388,392,396],n6=[0,2,8],n8=[8,7,6,4,4,2,2,2,1,1,1,1];this.WebPDecodeRGBA=function(t,r,s,c,u){var h=ni,l=new eV,f=new to;l.ba=f,f.S=h,f.width=[f.width],f.height=[f.height];var d=f.width,p=f.height,g=new ts;if(null==g||null==t)var m=2;else e(null!=g),m=rf(t,r,s,g.width,g.height,g.Pd,g.Qd,g.format,null);if(0!=m?d=0:(null!=d&&(d[0]=g.width[0]),null!=p&&(p[0]=g.height[0]),d=1),d){f.width=f.width[0],f.height=f.height[0],null!=c&&(c[0]=f.width),null!=u&&(u[0]=f.height);t:{if(c=new tG,(u=new eG).data=t,u.w=r,u.ha=s,u.kd=1,r=[0],e(null!=u),(0==(t=rf(u.data,u.w,u.ha,null,null,null,r,null,u))||7==t)&&r[0]&&(t=4),0==(r=t)){if(e(null!=l),c.data=u.data,c.w=u.w+u.offset,c.ha=u.ha-u.offset,c.put=td,c.ac=tf,c.bc=tp,c.ma=l,u.xa){if(null==(t=tk())){l=1;break t}if(function(t,r){for(var n=[0],i=[0],a=[0];;){if(null==t)return 0;if(null==r)return t.a=2,0;if(t.l=r,t.a=0,v(t.m,r.data,r.w,r.ha),!tg(t.m,n,i,a)){t.a=3;break}if(t.xb=nq,r.width=n[0],r.height=i[0],!tF(n[0],i[0],1,t,null))break;return 1}return e(0!=t.a),0}(t,c)){if(c=0==(r=rg(c.width,c.height,l.Oa,l.ba))){e:{for(c=t;;){if(null==c){c=0;break e}if(e(null!=c.s.yc),e(null!=c.s.Ya),e(0<c.s.Wb),e(null!=(s=c.l)),e(null!=(u=s.ma)),0!=c.xb){if(c.ca=u.ba,c.tb=u.tb,e(null!=c.ca),!rd(u.Oa,s,no)){c.a=2;break}if(!tI(c,s.width)||s.da)break;if((s.da||tn(c.ca.S))&&e3(),11>c.ca.S||(alert("todo:WebPInitConvertARGBToYUV"),null!=c.ca.f.kb.F&&e3()),c.Pb&&0<c.s.ua&&null==c.s.vb.X&&!O(c.s.vb,c.s.Wa.Xa)){c.a=1;break}c.xb=0}if(!t_(c,c.V,c.Ba,c.c,c.i,s.o,tN))break;u.Dc=c.Ma,c=1;break e}e(0!=c.a),c=0}c=!c}c&&(r=t.a)}else r=t.a}else if((t=new tY).Fa=u.na,t.P=u.P,t.qc=u.Sa,tK(t,c)){if(0==(r=rg(c.width,c.height,l.Oa,l.ba))){if(t.Aa=0,s=l.Oa,e(null!=(u=t)),null!=s){if(0<(d=0>(d=s.Md)?0:100<d?255:255*d/100)){for(p=g=0;4>p;++p)12>(m=u.pb[p]).lc&&(m.ia=d*n8[0>m.lc?0:m.lc]>>3),g|=m.ia;g&&(alert("todo:VP8InitRandom"),u.ia=1)}u.Ga=s.Id,100<u.Ga?u.Ga=100:0>u.Ga&&(u.Ga=0)}(function(t,r){if(null==t)return 0;if(null==r)return tJ(t,2,"NULL VP8Io parameter in VP8Decode().");if(!t.cb&&!tK(t,r))return 0;if(e(t.cb),null==r.ac||r.ac(r)){r.ob&&(t.L=0);var s=n6[t.L];if(2==t.L?(t.yb=0,t.zb=0):(t.yb=r.v-s>>4,t.zb=r.j-s>>4,0>t.yb&&(t.yb=0),0>t.zb&&(t.zb=0)),t.Va=r.o+15+s>>4,t.Hb=r.va+15+s>>4,t.Hb>t.za&&(t.Hb=t.za),t.Va>t.Ub&&(t.Va=t.Ub),0<t.L){var c,u=t.ed;for(s=0;4>s;++s){if(t.Qa.Cb){var h=t.Qa.Lb[s];t.Qa.Fb||(h+=u.Tb)}else h=u.Tb;for(c=0;1>=c;++c){var l=t.gd[s][c],f=h;if(u.Pc&&(f+=u.vd[0],c&&(f+=u.od[0])),0<(f=0>f?0:63<f?63:f)){var d=f;0<u.wb&&(d=4<u.wb?d>>2:d>>1)>9-u.wb&&(d=9-u.wb),1>d&&(d=1),l.dd=d,l.tc=2*f+d,l.ld=40<=f?2:+(15<=f)}else l.tc=0;l.La=c}}}s=0}else tJ(t,6,"Frame setup failed"),s=t.a;if(s=0==s){if(s){t.$c=0,0<t.Aa||(t.Ic=1);t:{s=t.Ic,u=4*(d=t.za);var p=32*d,g=d+1,m=0<t.L?d*(0<t.Aa?2:1):0,b=(2==t.Aa?2:1)*d;if((l=u+832+(c=3*(16*s+n6[t.L])/2*p)+(h=null!=t.Fa&&0<t.Fa.length?t.Kc.c*t.Kc.i:0))!=l)s=0;else{if(l>t.Vb){if(t.Vb=0,t.Ec=a(l),t.Fc=0,null==t.Ec){s=tJ(t,1,"no memory during frame initialization.");break t}t.Vb=l}l=t.Ec,f=t.Fc,t.Ac=l,t.Bc=f,f+=u,t.Gd=o(p,tH),t.Hd=0,t.rb=o(g+1,tR),t.sb=1,t.wa=m?o(m,tD):null,t.Y=0,t.D.Nb=0,t.D.wa=t.wa,t.D.Y=t.Y,0<t.Aa&&(t.D.Y+=d),e(!0),t.oc=l,t.pc=f,f+=832,t.ya=o(b,tz),t.aa=0,t.D.ya=t.ya,t.D.aa=t.aa,2==t.Aa&&(t.D.aa+=d),t.R=16*d,t.B=8*d,d=(p=n6[t.L])*t.R,p=p/2*t.B,t.sa=l,t.ta=f+d,t.qa=t.sa,t.ra=t.ta+16*s*t.R+p,t.Ha=t.qa,t.Ia=t.ra+8*s*t.B+p,t.$c=0,f+=c,t.mb=h?l:null,t.nb=h?f:null,e(f+h<=t.Fc+t.Vb),t$(t),i(t.Ac,t.Bc,0,u),s=1}}if(s){if(r.ka=0,r.y=t.sa,r.O=t.ta,r.f=t.qa,r.N=t.ra,r.ea=t.Ha,r.Vd=t.Ia,r.fa=t.R,r.Rc=t.B,r.F=null,r.J=0,!r7){for(s=-255;255>=s;++s)r3[255+s]=0>s?-s:s;for(s=-1020;1020>=s;++s)r4[1020+s]=-128>s?-128:127<s?127:s;for(s=-112;112>=s;++s)r6[112+s]=-16>s?-16:15<s?15:s;for(s=-255;510>=s;++s)r8[255+s]=0>s?0:255<s?255:s;r7=1}rq=t7,rB=t3,rR=t4,rT=t6,rz=t8,rD=t0,rU=eB,rH=eD,rW=ez,rV=eU,rG=eR,rY=eT,rJ=eH,rX=eW,rK=eC,rZ=ej,r$=eO,rQ=eE,nO[0]=ed,nO[1]=et,nO[2]=el,nO[3]=ef,nO[4]=ep,nO[5]=em,nO[6]=eg,nO[7]=ev,nO[8]=ey,nO[9]=eb,nj[0]=eo,nj[1]=er,nj[2]=en,nj[3]=ei,nj[4]=es,nj[5]=ec,nj[6]=eu,nE[0]=eA,nE[1]=ee,nE[2]=ew,nE[3]=ex,nE[4]=eS,nE[5]=eL,nE[6]=e_,s=1}else s=0}s&&(s=function(t,r){for(t.M=0;t.M<t.Va;++t.M){var o,s=t.Jc[t.M&t.Xb],c=t.m,u=t;for(o=0;o<u.za;++o){var h=c,l=u,f=l.Ac,d=l.Bc+4*o,p=l.zc,g=l.ya[l.aa+o];if(l.Qa.Bb?g.$b=k(h,l.Pa.jb[0])?2+k(h,l.Pa.jb[2]):k(h,l.Pa.jb[1]):g.$b=0,l.kc&&(g.Ad=k(h,l.Bd)),g.Za=!k(h,145)+0,g.Za){var m=g.Ob,b=0;for(l=0;4>l;++l){var y,w=p[0+l];for(y=0;4>y;++y){w=nF[f[d+y]][w];for(var x=nP[k(h,w[0])];0<x;)x=nP[2*x+k(h,w[x])];w=-x,f[d+y]=w}n(m,b,f,d,4),b+=4,p[0+l]=w}}else w=k(h,156)?k(h,128)?1:3:2*!!k(h,163),g.Ob[0]=w,i(f,d,w,4),i(p,0,w,4);g.Dd=k(h,142)?k(h,114)?k(h,183)?1:3:2:0}if(u.m.Ka)return tJ(t,7,"Premature end-of-partition0 encountered.");for(;t.ja<t.za;++t.ja){if(u=s,h=(c=t).rb[c.sb-1],f=c.rb[c.sb+c.ja],o=c.ya[c.aa+c.ja],d=c.kc?o.Ad:0)h.la=f.la=0,o.Za||(h.Na=f.Na=0),o.Hc=0,o.Gc=0,o.ia=0;else{if(h=f,f=u,d=c.Pa.Xc,p=c.ya[c.aa+c.ja],g=c.pb[p.$b],l=p.ad,m=0,b=c.rb[c.sb-1],w=y=0,i(l,m,0,384),p.Za)var N,L,_=0,P=d[3];else{x=a(16);var F=h.Na+b.Na;if(F=nL(f,d[1],F,g.Eb,0,x,0),h.Na=b.Na=(0<F)+0,1<F)rq(x,0,l,m);else{var I=x[0]+3>>3;for(x=0;256>x;x+=16)l[m+x]=I}_=1,P=d[0]}var C=15&h.la,j=15&b.la;for(x=0;4>x;++x){var O=1&j;for(I=L=0;4>I;++I)C=C>>1|(O=(F=nL(f,P,F=O+(1&C),g.Sc,_,l,m))>_)<<7,L=L<<2|(3<F?3:1<F?2:0!=l[m+0]),m+=16;C>>=4,j=j>>1|O<<7,y=(y<<8|L)>>>0}for(P=C,_=j>>4,N=0;4>N;N+=2){for(L=0,C=h.la>>4+N,j=b.la>>4+N,x=0;2>x;++x){for(O=1&j,I=0;2>I;++I)F=O+(1&C),C=C>>1|(O=0<(F=nL(f,d[2],F,g.Qc,0,l,m)))<<3,L=L<<2|(3<F?3:1<F?2:0!=l[m+0]),m+=16;C>>=2,j=j>>1|O<<5}w|=L<<4*N,P|=C<<4<<N,_|=(240&j)<<N}h.la=P,b.la=_,p.Hc=y,p.Gc=w,p.ia=43690&w?0:g.ia,d=!(y|w)}if(0<c.L&&(c.wa[c.Y+c.ja]=c.gd[o.$b][o.Za],c.wa[c.Y+c.ja].La|=!d),u.Ka)return tJ(t,7,"Premature end-of-file encountered.")}if(t$(t),c=r,u=1,o=(s=t).D,h=0<s.L&&s.M>=s.zb&&s.M<=s.Va,0==s.Aa)t:{if(o.M=s.M,o.uc=h,rl(s,o),u=1,o=(L=s.D).Nb,h=(w=n6[s.L])*s.R,f=w/2*s.B,x=16*o*s.R,I=8*o*s.B,d=s.sa,p=s.ta-h+x,g=s.qa,l=s.ra-f+I,m=s.Ha,b=s.Ia-f+I,j=0==(C=L.M),y=C>=s.Va-1,2==s.Aa&&rl(s,L),L.uc)for(O=(F=s).D.M,e(F.D.uc),L=F.yb;L<F.Hb;++L){_=L,P=O;var E=(M=(W=F).D).Nb;N=W.R;var M=M.wa[M.Y+_],q=W.sa,B=W.ta+16*E*N+16*_,D=M.dd,R=M.tc;if(0!=R)if(e(3<=R),1==W.L)0<_&&rZ(q,B,N,R+4),M.La&&rQ(q,B,N,R),0<P&&rK(q,B,N,R+4),M.La&&r$(q,B,N,R);else{var T=W.B,z=W.qa,U=W.ra+8*E*T+8*_,H=W.Ha,W=W.Ia+8*E*T+8*_;E=M.ld,0<_&&(rH(q,B,N,R+4,D,E),rV(z,U,H,W,T,R+4,D,E)),M.La&&(rY(q,B,N,R,D,E),rX(z,U,H,W,T,R,D,E)),0<P&&(rU(q,B,N,R+4,D,E),rW(z,U,H,W,T,R+4,D,E)),M.La&&(rG(q,B,N,R,D,E),rJ(z,U,H,W,T,R,D,E))}}if(s.ia&&alert("todo:DitherRow"),null!=c.put){if(L=16*C,C=16*(C+1),j?(c.y=s.sa,c.O=s.ta+x,c.f=s.qa,c.N=s.ra+I,c.ea=s.Ha,c.W=s.Ia+I):(L-=w,c.y=d,c.O=p,c.f=g,c.N=l,c.ea=m,c.W=b),y||(C-=w),C>c.o&&(C=c.o),c.F=null,c.J=null,null!=s.Fa&&0<s.Fa.length&&L<C&&(c.J=function(t,r,i,o){var s=r.width,c=r.o;if(e(null!=t&&null!=r),0>i||0>=o||i+o>c)return null;if(!t.Cc){if(null==t.ga){if(t.ga=new eK,(_=null==t.ga)||(_=r.width*r.o,e(0==t.Gb.length),t.Gb=a(_),t.Uc=0,null==t.Gb?_=0:(t.mb=t.Gb,t.nb=t.Uc,t.rc=null,_=1),_=!_),!_){_=t.ga;var u=t.Fa,h=t.P,l=t.qc,f=t.mb,d=t.nb,p=h+1,g=l-1,m=_.l;if(e(null!=u&&null!=f&&null!=r),nB[0]=null,nB[1]=eZ,nB[2]=e$,nB[3]=eQ,_.ca=f,_.tb=d,_.c=r.width,_.i=r.height,e(0<_.c&&0<_.i),1>=l)r=0;else if(_.$a=(0|u[h+0])&3,_.Z=u[h+0]>>2&3,_.Lc=u[h+0]>>4&3,h=u[h+0]>>6&3,0>_.$a||1<_.$a||4<=_.Z||1<_.Lc||h)r=0;else if(m.put=td,m.ac=tf,m.bc=tp,m.ma=_,m.width=r.width,m.height=r.height,m.Da=r.Da,m.v=r.v,m.va=r.va,m.j=r.j,m.o=r.o,_.$a)t:{for(e(1==_.$a),r=tk();;){if(null==r){r=0;break t}if(e(null!=_),_.mc=r,r.c=_.c,r.i=_.i,r.l=_.l,r.l.ma=_,r.l.width=_.c,r.l.height=_.i,r.a=0,v(r.m,u,p,g),!tF(_.c,_.i,1,r,null)||(1==r.ab&&3==r.gc[0].hc&&tA(r.s)?(_.ic=1,u=r.c*r.i,r.Ta=null,r.Ua=0,r.V=a(u),r.Ba=0,null==r.V?(r.a=1,r=0):r=1):(_.ic=0,r=tI(r,_.c)),!r))break;r=1;break t}_.mc=null,r=0}else r=g>=_.c*_.i;_=!r}if(_)return null;1!=t.ga.Lc?t.Ga=0:o=c-i}e(null!=t.ga),e(i+o<=c);t:{if(r=(u=t.ga).c,c=u.l.o,0==u.$a){if(p=t.rc,g=t.Vc,m=t.Fa,h=t.P+1+i*r,l=t.mb,f=t.nb+i*r,e(h<=t.P+t.qc),0!=u.Z)for(e(null!=nB[u.Z]),_=0;_<o;++_)nB[u.Z](p,g,m,h,l,f,r),p=l,g=f,f+=r,h+=r;else for(_=0;_<o;++_)n(l,f,m,h,r),p=l,g=f,f+=r,h+=r;t.rc=p,t.Vc=g}else{if(e(null!=u.mc),r=i+o,e(null!=(_=u.mc)),e(r<=_.i),_.C>=r)r=1;else if(u.ic||e3(),u.ic){u=_.V,p=_.Ba,g=_.c;var b=_.i,y=(m=1,h=_.$/g,l=_.$%g,f=_.m,d=_.s,_.$),w=g*b,x=g*r,N=d.wc,L=y<x?tw(d,l,h):null;e(y<=w),e(r<=b),e(tA(d));e:for(;;){for(;!f.h&&y<x;){if(l&N||(L=tw(d,l,h)),e(null!=L),S(f),256>(b=tb(L.G[0],L.H[0],f)))u[p+y]=b,++y,++l>=g&&(l=0,++h<=r&&!(h%16)&&tS(_,h));else{if(!(280>b)){m=0;break e}b=tm(b-256,f);var _,P,k=tb(L.G[4],L.H[4],f);if(S(f),!(y>=(k=tv(g,k=tm(k,f)))&&w-y>=b)){m=0;break e}for(P=0;P<b;++P)u[p+y+P]=u[p+y+P-k];for(y+=b,l+=b;l>=g;)l-=g,++h<=r&&!(h%16)&&tS(_,h);y<x&&l&N&&(L=tw(d,l,h))}e(f.h==A(f))}tS(_,h>r?r:h);break}!m||f.h&&y<w?(m=0,_.a=f.h?5:3):_.$=y,r=m}else r=t_(_,_.V,_.Ba,_.c,_.i,r,tC);if(!r){o=0;break t}}i+o>=c&&(t.Cc=1),o=1}if(!o)return null;if(t.Cc&&(null!=(o=t.ga)&&(o.mc=null),t.ga=null,0<t.Ga))return alert("todo:WebPDequantizeLevels"),null}return t.nb+i*s}(s,c,L,C-L),c.F=s.mb,null==c.F&&0==c.F.length)){u=tJ(s,3,"Could not decode alpha data.");break t}L<c.j&&(w=c.j-L,L=c.j,e(!(1&w)),c.O+=s.R*w,c.N+=s.B*(w>>1),c.W+=s.B*(w>>1),null!=c.F&&(c.J+=c.width*w)),L<C&&(c.O+=c.v,c.N+=c.v>>1,c.W+=c.v>>1,null!=c.F&&(c.J+=c.v),c.ka=L-c.j,c.U=c.va-c.v,c.T=C-L,u=c.put(c))}o+1!=s.Ic||y||(n(s.sa,s.ta-h,d,p+16*s.R,h),n(s.qa,s.ra-f,g,l+8*s.B,f),n(s.Ha,s.Ia-f,m,b+8*s.B,f))}if(!u)return tJ(t,6,"Output aborted.")}return 1}(t,r)),null!=r.bc&&r.bc(r),s&=1}return s?(t.cb=0,s):0})(t,c)||(r=t.a)}}else r=t.a;0==r&&null!=l.Oa&&l.Oa.fd&&(r=rp(l.ba))}l=r}h=0!=l?null:11>h?f.f.RGBA.eb:f.f.kb.y}else h=null;return h};var n7=[3,4,3,4,4,2,2,4,4,4,2,1,1]};function u(t,e){return(0|t[e+0]|t[e+1]<<8|t[e+2]<<16)>>>0}function h(t,e){return(0|t[e+0]|t[e+1]<<8|t[e+2]<<16|t[e+3]<<24)>>>0}new c;var l=[0],f=[0],d=[],p=new c,g=function(t,e){var r={},n=0,i=!1,a=0,o=0;if(r.frames=[],!function(t,e,r,n){for(var i=0;i<4;i++)if(t[e+i]!=r.charCodeAt(i))return!0;return!1}(t,e,"RIFF",0)){for(h(t,e+=4),e+=8;e<t.length;){var s,c,l,f=function(t,e){for(var r="",n=0;n<4;n++)r+=String.fromCharCode(t[e++]);return r}(t,e),d=h(t,e+=4);e+=4;var p=d+(1&d);switch(f){case"VP8 ":case"VP8L":void 0===r.frames[n]&&(r.frames[n]={}),(l=r.frames[n]).src_off=i?o:e-8,l.src_size=a+d+8,n++,i&&(i=!1,a=0,o=0);break;case"VP8X":(l=r.header={}).feature_flags=t[e];var g=e+4;l.canvas_width=1+u(t,g),g+=3,l.canvas_height=1+u(t,g),g+=3;break;case"ALPH":i=!0,a=p+8,o=e-8;break;case"ANIM":(l=r.header).bgcolor=h(t,e),g=e+4,l.loop_count=0|t[(s=g)+0]|t[s+1]<<8,g+=2;break;case"ANMF":(l=r.frames[n]={}).offset_x=2*u(t,e),e+=3,l.offset_y=2*u(t,e),e+=3,l.width=1+u(t,e),e+=3,l.height=1+u(t,e),e+=3,l.duration=u(t,e),e+=3,c=t[e++],l.dispose=1&c,l.blend=c>>1&1}"ANMF"!=f&&(e+=p)}return r}}(t,0);g.response=t,g.rgbaoutput=!0,g.dataurl=!1;var m=g.header?g.header:null,v=g.frames?g.frames:null;if(m){m.loop_counter=m.loop_count,l=[m.canvas_height],f=[m.canvas_width];for(var b=0;b<v.length&&0!=v[b].blend;b++);}var y=v[0],w=p.WebPDecodeRGBA(t,y.src_off,y.src_size,f,l);y.rgba=w,y.imgwidth=f[0],y.imgheight=l[0];for(var x=0;x<f[0]*l[0]*4;x++)d[x]=w[x];return this.width=f,this.height=l,this.data=d,this}!function(t){var e=function(e,n,u,h){var l=4,f=o;switch(h){case t.image_compression.FAST:l=1,f=a;break;case t.image_compression.MEDIUM:l=6,f=s;break;case t.image_compression.SLOW:l=9,f=c}e=r(e,n,u,f);var d=(0,i.$)(e,{level:l});return t.__addimage__.arrayBufferToBinaryString(d)},r=function(t,e,r,n){for(var i,a,o,s=t.length/e,c=new Uint8Array(t.length+s),u=h(),f=0;f<s;f+=1){if(o=f*e,i=t.subarray(o,o+e),n)c.set(n(i,r,a),o+f);else{for(var d,p=u.length,g=[];d<p;d+=1)g[d]=u[d](i,r,a);var m=l(g.concat());c.set(g[m],o+f)}a=i}return c},n=function(t){var e=Array.apply([],t);return e.unshift(0),e},a=function(t,e){var r,n=[],i=t.length;n[0]=1;for(var a=0;a<i;a+=1)r=t[a-e]||0,n[a+1]=t[a]-r+256&255;return n},o=function(t,e,r){var n,i=[],a=t.length;i[0]=2;for(var o=0;o<a;o+=1)n=r&&r[o]||0,i[o+1]=t[o]-n+256&255;return i},s=function(t,e,r){var n,i,a=[],o=t.length;a[0]=3;for(var s=0;s<o;s+=1)n=t[s-e]||0,i=r&&r[s]||0,a[s+1]=t[s]+256-(n+i>>>1)&255;return a},c=function(t,e,r){var n,i,a=[],o=t.length;a[0]=4;for(var s=0;s<o;s+=1)n=t[s-e]||0,i=u(n,r&&r[s]||0,r&&r[s-e]||0),a[s+1]=t[s]-i+256&255;return a},u=function(t,e,r){if(t===e&&e===r)return t;var n=Math.abs(e-r),i=Math.abs(t-r),a=Math.abs(t+e-r-r);return n<=i&&n<=a?t:i<=a?e:r},h=function(){return[n,a,o,s,c]},l=function(t){var e=t.map(function(t){return t.reduce(function(t,e){return t+Math.abs(e)},0)});return e.indexOf(Math.min.apply(null,e))};t.processPNG=function(r,n,a,o){var s,c,u,h,l,f,d,p,g,m,v,b,y,w,x,N=this.decode.FLATE_DECODE,A="";if(this.__addimage__.isArrayBuffer(r)&&(r=new Uint8Array(r)),this.__addimage__.isArrayBufferView(r)){if(r=(u=new tY(r)).imgData,c=u.bits,s=u.colorSpace,l=u.colors,-1!==[4,6].indexOf(u.colorType)){if(8===u.bits){g=(p=32==u.pixelBitlength?new Uint32Array(u.decodePixels().buffer):16==u.pixelBitlength?new Uint16Array(u.decodePixels().buffer):new Uint8Array(u.decodePixels().buffer)).length,v=new Uint8Array(g*u.colors),m=new Uint8Array(g);var L,S=u.pixelBitlength-u.bits;for(w=0,x=0;w<g;w++){for(y=p[w],L=0;L<S;)v[x++]=y>>>L&255,L+=u.bits;m[w]=y>>>L&255}}if(16===u.bits){v=new Uint8Array((g=(p=new Uint32Array(u.decodePixels().buffer)).length)*(32/u.pixelBitlength)*u.colors),m=new Uint8Array(g*(32/u.pixelBitlength)),b=u.colors>1,w=0,x=0;for(var _=0;w<g;)y=p[w++],v[x++]=y>>>0&255,b&&(v[x++]=y>>>16&255,y=p[w++],v[x++]=y>>>0&255),m[_++]=y>>>16&255;c=8}o!==t.image_compression.NONE&&"function"==typeof i.$?(r=e(v,u.width*u.colors,u.colors,o),d=e(m,u.width,1,o)):(r=v,d=m,N=void 0)}if(3===u.colorType&&(s=this.color_spaces.INDEXED,f=u.palette,u.transparency.indexed)){var P=u.transparency.indexed,k=0;for(w=0,g=P.length;w<g;++w)k+=P[w];if((k/=255)==g-1&&-1!==P.indexOf(0))h=[P.indexOf(0)];else if(k!==g){for(m=new Uint8Array((p=u.decodePixels()).length),w=0,g=p.length;w<g;w++)m[w]=P[p[w]];d=e(m,u.width,1)}}var F=function(e){var r;switch(e){case t.image_compression.FAST:r=11;break;case t.image_compression.MEDIUM:r=13;break;case t.image_compression.SLOW:r=14;break;default:r=12}return r}(o);return N===this.decode.FLATE_DECODE&&(A="/Predictor "+F+" "),A+="/Colors "+l+" /BitsPerComponent "+c+" /Columns "+u.width,(this.__addimage__.isArrayBuffer(r)||this.__addimage__.isArrayBufferView(r))&&(r=this.__addimage__.arrayBufferToBinaryString(r)),(d&&this.__addimage__.isArrayBuffer(d)||this.__addimage__.isArrayBufferView(d))&&(d=this.__addimage__.arrayBufferToBinaryString(d)),{alias:a,data:r,index:n,filter:N,decodeParameters:A,transparency:h,palette:f,sMask:d,predictor:F,width:u.width,height:u.height,bitsPerComponent:c,colorSpace:s}}}}(q.API),function(t){t.processGIF89A=function(e,r,n,i){var a=new tJ(e),o=a.width,s=a.height,c=[];a.decodeAndBlitFrameRGBA(0,c);var u=new tK(100).encode({data:c,width:o,height:s},100);return t.processJPEG.call(this,u,r,n,i)},t.processGIF87A=t.processGIF89A}(q.API),tZ.prototype.parseHeader=function(){if(this.fileSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.reserved=this.datav.getUint32(this.pos,!0),this.pos+=4,this.offset=this.datav.getUint32(this.pos,!0),this.pos+=4,this.headerSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.width=this.datav.getUint32(this.pos,!0),this.pos+=4,this.height=this.datav.getInt32(this.pos,!0),this.pos+=4,this.planes=this.datav.getUint16(this.pos,!0),this.pos+=2,this.bitPP=this.datav.getUint16(this.pos,!0),this.pos+=2,this.compress=this.datav.getUint32(this.pos,!0),this.pos+=4,this.rawSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.hr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.vr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.colors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.importantColors=this.datav.getUint32(this.pos,!0),this.pos+=4,16===this.bitPP&&this.is_with_alpha&&(this.bitPP=15),this.bitPP<15){var t=0===this.colors?1<<this.bitPP:this.colors;this.palette=Array(t);for(var e=0;e<t;e++){var r=this.datav.getUint8(this.pos++,!0),n=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0),a=this.datav.getUint8(this.pos++,!0);this.palette[e]={red:i,green:n,blue:r,quad:a}}}this.height<0&&(this.height*=-1,this.bottom_up=!1)},tZ.prototype.parseBGR=function(){this.pos=this.offset;try{var t="bit"+this.bitPP,e=this.width*this.height*4;this.data=new Uint8Array(e),this[t]()}catch(t){s.log("bit decode error:"+t)}},tZ.prototype.bit1=function(){var t,e=Math.ceil(this.width/8),r=e%4;for(t=this.height-1;t>=0;t--){for(var n=this.bottom_up?t:this.height-1-t,i=0;i<e;i++)for(var a=this.datav.getUint8(this.pos++,!0),o=n*this.width*4+8*i*4,s=0;s<8&&8*i+s<this.width;s++){var c=this.palette[a>>7-s&1];this.data[o+4*s]=c.blue,this.data[o+4*s+1]=c.green,this.data[o+4*s+2]=c.red,this.data[o+4*s+3]=255}0!==r&&(this.pos+=4-r)}},tZ.prototype.bit4=function(){for(var t=Math.ceil(this.width/2),e=t%4,r=this.height-1;r>=0;r--){for(var n=this.bottom_up?r:this.height-1-r,i=0;i<t;i++){var a=this.datav.getUint8(this.pos++,!0),o=n*this.width*4+2*i*4,s=a>>4,c=15&a,u=this.palette[s];if(this.data[o]=u.blue,this.data[o+1]=u.green,this.data[o+2]=u.red,this.data[o+3]=255,2*i+1>=this.width)break;u=this.palette[c],this.data[o+4]=u.blue,this.data[o+4+1]=u.green,this.data[o+4+2]=u.red,this.data[o+4+3]=255}0!==e&&(this.pos+=4-e)}},tZ.prototype.bit8=function(){for(var t=this.width%4,e=this.height-1;e>=0;e--){for(var r=this.bottom_up?e:this.height-1-e,n=0;n<this.width;n++){var i=this.datav.getUint8(this.pos++,!0),a=r*this.width*4+4*n;if(i<this.palette.length){var o=this.palette[i];this.data[a]=o.red,this.data[a+1]=o.green,this.data[a+2]=o.blue,this.data[a+3]=255}else this.data[a]=255,this.data[a+1]=255,this.data[a+2]=255,this.data[a+3]=255}0!==t&&(this.pos+=4-t)}},tZ.prototype.bit15=function(){for(var t=this.width%3,e=parseInt("11111",2),r=this.height-1;r>=0;r--){for(var n=this.bottom_up?r:this.height-1-r,i=0;i<this.width;i++){var a=this.datav.getUint16(this.pos,!0);this.pos+=2;var o=(a&e)/e*255|0,s=(a>>5&e)/e*255|0,c=(a>>10&e)/e*255|0,u=a>>15?255:0,h=n*this.width*4+4*i;this.data[h]=c,this.data[h+1]=s,this.data[h+2]=o,this.data[h+3]=u}this.pos+=t}},tZ.prototype.bit16=function(){for(var t=this.width%3,e=parseInt("11111",2),r=parseInt("111111",2),n=this.height-1;n>=0;n--){for(var i=this.bottom_up?n:this.height-1-n,a=0;a<this.width;a++){var o=this.datav.getUint16(this.pos,!0);this.pos+=2;var s=(o&e)/e*255|0,c=(o>>5&r)/r*255|0,u=(o>>11)/e*255|0,h=i*this.width*4+4*a;this.data[h]=u,this.data[h+1]=c,this.data[h+2]=s,this.data[h+3]=255}this.pos+=t}},tZ.prototype.bit24=function(){for(var t=this.height-1;t>=0;t--){for(var e=this.bottom_up?t:this.height-1-t,r=0;r<this.width;r++){var n=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0),a=this.datav.getUint8(this.pos++,!0),o=e*this.width*4+4*r;this.data[o]=a,this.data[o+1]=i,this.data[o+2]=n,this.data[o+3]=255}this.pos+=this.width%4}},tZ.prototype.bit32=function(){for(var t=this.height-1;t>=0;t--)for(var e=this.bottom_up?t:this.height-1-t,r=0;r<this.width;r++){var n=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0),a=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0),s=e*this.width*4+4*r;this.data[s]=a,this.data[s+1]=i,this.data[s+2]=n,this.data[s+3]=o}},tZ.prototype.getData=function(){return this.data},function(t){t.processBMP=function(e,r,n,i){var a=new tZ(e,!1),o=a.width,s=a.height,c={data:a.getData(),width:o,height:s},u=new tK(100).encode(c,100);return t.processJPEG.call(this,u,r,n,i)}}(q.API),t$.prototype.getData=function(){return this.data},function(t){t.processWEBP=function(e,r,n,i){var a=new t$(e),o=a.width,s=a.height,c={data:a.getData(),width:o,height:s},u=new tK(100).encode(c,100);return t.processJPEG.call(this,u,r,n,i)}}(q.API),q.API.processRGBA=function(t,e,r){for(var n=t.data,i=n.length,a=new Uint8Array(i/4*3),o=new Uint8Array(i/4),s=0,c=0,u=0;u<i;u+=4){var h=n[u],l=n[u+1],f=n[u+2],d=n[u+3];a[s++]=h,a[s++]=l,a[s++]=f,o[c++]=d}var p=this.__addimage__.arrayBufferToBinaryString(a);return{alpha:this.__addimage__.arrayBufferToBinaryString(o),data:p,index:e,alias:r,colorSpace:"DeviceRGB",bitsPerComponent:8,width:t.width,height:t.height}},q.API.setLanguage=function(t){return void 0===this.internal.languageSettings&&(this.internal.languageSettings={},this.internal.languageSettings.isSubscribed=!1),void 0!==({af:"Afrikaans",sq:"Albanian",ar:"Arabic (Standard)","ar-DZ":"Arabic (Algeria)","ar-BH":"Arabic (Bahrain)","ar-EG":"Arabic (Egypt)","ar-IQ":"Arabic (Iraq)","ar-JO":"Arabic (Jordan)","ar-KW":"Arabic (Kuwait)","ar-LB":"Arabic (Lebanon)","ar-LY":"Arabic (Libya)","ar-MA":"Arabic (Morocco)","ar-OM":"Arabic (Oman)","ar-QA":"Arabic (Qatar)","ar-SA":"Arabic (Saudi Arabia)","ar-SY":"Arabic (Syria)","ar-TN":"Arabic (Tunisia)","ar-AE":"Arabic (U.A.E.)","ar-YE":"Arabic (Yemen)",an:"Aragonese",hy:"Armenian",as:"Assamese",ast:"Asturian",az:"Azerbaijani",eu:"Basque",be:"Belarusian",bn:"Bengali",bs:"Bosnian",br:"Breton",bg:"Bulgarian",my:"Burmese",ca:"Catalan",ch:"Chamorro",ce:"Chechen",zh:"Chinese","zh-HK":"Chinese (Hong Kong)","zh-CN":"Chinese (PRC)","zh-SG":"Chinese (Singapore)","zh-TW":"Chinese (Taiwan)",cv:"Chuvash",co:"Corsican",cr:"Cree",hr:"Croatian",cs:"Czech",da:"Danish",nl:"Dutch (Standard)","nl-BE":"Dutch (Belgian)",en:"English","en-AU":"English (Australia)","en-BZ":"English (Belize)","en-CA":"English (Canada)","en-IE":"English (Ireland)","en-JM":"English (Jamaica)","en-NZ":"English (New Zealand)","en-PH":"English (Philippines)","en-ZA":"English (South Africa)","en-TT":"English (Trinidad & Tobago)","en-GB":"English (United Kingdom)","en-US":"English (United States)","en-ZW":"English (Zimbabwe)",eo:"Esperanto",et:"Estonian",fo:"Faeroese",fj:"Fijian",fi:"Finnish",fr:"French (Standard)","fr-BE":"French (Belgium)","fr-CA":"French (Canada)","fr-FR":"French (France)","fr-LU":"French (Luxembourg)","fr-MC":"French (Monaco)","fr-CH":"French (Switzerland)",fy:"Frisian",fur:"Friulian",gd:"Gaelic (Scots)","gd-IE":"Gaelic (Irish)",gl:"Galacian",ka:"Georgian",de:"German (Standard)","de-AT":"German (Austria)","de-DE":"German (Germany)","de-LI":"German (Liechtenstein)","de-LU":"German (Luxembourg)","de-CH":"German (Switzerland)",el:"Greek",gu:"Gujurati",ht:"Haitian",he:"Hebrew",hi:"Hindi",hu:"Hungarian",is:"Icelandic",id:"Indonesian",iu:"Inuktitut",ga:"Irish",it:"Italian (Standard)","it-CH":"Italian (Switzerland)",ja:"Japanese",kn:"Kannada",ks:"Kashmiri",kk:"Kazakh",km:"Khmer",ky:"Kirghiz",tlh:"Klingon",ko:"Korean","ko-KP":"Korean (North Korea)","ko-KR":"Korean (South Korea)",la:"Latin",lv:"Latvian",lt:"Lithuanian",lb:"Luxembourgish",mk:"North Macedonia",ms:"Malay",ml:"Malayalam",mt:"Maltese",mi:"Maori",mr:"Marathi",mo:"Moldavian",nv:"Navajo",ng:"Ndonga",ne:"Nepali",no:"Norwegian",nb:"Norwegian (Bokmal)",nn:"Norwegian (Nynorsk)",oc:"Occitan",or:"Oriya",om:"Oromo",fa:"Persian","fa-IR":"Persian/Iran",pl:"Polish",pt:"Portuguese","pt-BR":"Portuguese (Brazil)",pa:"Punjabi","pa-IN":"Punjabi (India)","pa-PK":"Punjabi (Pakistan)",qu:"Quechua",rm:"Rhaeto-Romanic",ro:"Romanian","ro-MO":"Romanian (Moldavia)",ru:"Russian","ru-MO":"Russian (Moldavia)",sz:"Sami (Lappish)",sg:"Sango",sa:"Sanskrit",sc:"Sardinian",sd:"Sindhi",si:"Singhalese",sr:"Serbian",sk:"Slovak",sl:"Slovenian",so:"Somani",sb:"Sorbian",es:"Spanish","es-AR":"Spanish (Argentina)","es-BO":"Spanish (Bolivia)","es-CL":"Spanish (Chile)","es-CO":"Spanish (Colombia)","es-CR":"Spanish (Costa Rica)","es-DO":"Spanish (Dominican Republic)","es-EC":"Spanish (Ecuador)","es-SV":"Spanish (El Salvador)","es-GT":"Spanish (Guatemala)","es-HN":"Spanish (Honduras)","es-MX":"Spanish (Mexico)","es-NI":"Spanish (Nicaragua)","es-PA":"Spanish (Panama)","es-PY":"Spanish (Paraguay)","es-PE":"Spanish (Peru)","es-PR":"Spanish (Puerto Rico)","es-ES":"Spanish (Spain)","es-UY":"Spanish (Uruguay)","es-VE":"Spanish (Venezuela)",sx:"Sutu",sw:"Swahili",sv:"Swedish","sv-FI":"Swedish (Finland)","sv-SV":"Swedish (Sweden)",ta:"Tamil",tt:"Tatar",te:"Teluga",th:"Thai",tig:"Tigre",ts:"Tsonga",tn:"Tswana",tr:"Turkish",tk:"Turkmen",uk:"Ukrainian",hsb:"Upper Sorbian",ur:"Urdu",ve:"Venda",vi:"Vietnamese",vo:"Volapuk",wa:"Walloon",cy:"Welsh",xh:"Xhosa",ji:"Yiddish",zu:"Zulu"})[t]&&(this.internal.languageSettings.languageCode=t,!1===this.internal.languageSettings.isSubscribed&&(this.internal.events.subscribe("putCatalog",function(){this.internal.write("/Lang ("+this.internal.languageSettings.languageCode+")")}),this.internal.languageSettings.isSubscribed=!0)),this},tH=(tU=q.API).getCharWidthsArray=function(t,e){var r,i,a=(e=e||{}).font||this.internal.getFont(),o=e.fontSize||this.internal.getFontSize(),s=e.charSpace||this.internal.getCharSpace(),c=e.widths?e.widths:a.metadata.Unicode.widths,u=c.fof?c.fof:1,h=e.kerning?e.kerning:a.metadata.Unicode.kerning,l=h.fof?h.fof:1,f=!1!==e.doKerning,d=0,p=t.length,g=0,m=c[0]||u,v=[];for(r=0;r<p;r++)i=t.charCodeAt(r),"function"==typeof a.metadata.widthOfString?v.push((a.metadata.widthOfGlyph(a.metadata.characterToGlyph(i))+1e3/o*s||0)/1e3):(d=f&&"object"===(0,n.A)(h[i])&&!isNaN(parseInt(h[i][g],10))?h[i][g]/l:0,v.push((c[i]||m)/u+d)),g=i;return v},tW=tU.getStringUnitWidth=function(t,e){var r=(e=e||{}).fontSize||this.internal.getFontSize(),n=e.font||this.internal.getFont(),i=e.charSpace||this.internal.getCharSpace();return tU.processArabic&&(t=tU.processArabic(t)),"function"==typeof n.metadata.widthOfString?n.metadata.widthOfString(t,r,i)/r:tH.apply(this,arguments).reduce(function(t,e){return t+e},0)},tV=function(t,e,r,n){for(var i=[],a=0,o=t.length,s=0;a!==o&&s+e[a]<r;)s+=e[a],a++;i.push(t.slice(0,a));var c=a;for(s=0;a!==o;)s+e[a]>n&&(i.push(t.slice(c,a)),s=0,c=a),s+=e[a],a++;return c!==a&&i.push(t.slice(c,a)),i},tG=function(t,e,r){r||(r={});var n,i,a,o,s,c,u,h=[],l=[h],f=r.textIndent||0,d=0,p=0,g=t.split(" "),m=tH.apply(this,[" ",r])[0];if(c=-1===r.lineIndent?g[0].length+2:r.lineIndent||0){var v=Array(c).join(" "),b=[];g.map(function(t){(t=t.split(/\s*\n/)).length>1?b=b.concat(t.map(function(t,e){return(e&&t.length?"\n":"")+t})):b.push(t[0])}),g=b,c=tW.apply(this,[v,r])}for(a=0,o=g.length;a<o;a++){var y=0;if(n=g[a],c&&"\n"==n[0]&&(n=n.substr(1),y=1),f+d+(p=(i=tH.apply(this,[n,r])).reduce(function(t,e){return t+e},0))>e||y){if(p>e){for(s=tV.apply(this,[n,i,e-(f+d),e]),h.push(s.shift()),h=[s.pop()];s.length;)l.push([s.shift()]);p=i.slice(n.length-(h[0]?h[0].length:0)).reduce(function(t,e){return t+e},0)}else h=[n];l.push(h),f=p+c,d=m}else h.push(n),f+=d+p,d=m}return u=c?function(t,e){return(e?v:"")+t.join(" ")}:function(t){return t.join(" ")},l.map(u)},tU.splitTextToSize=function(t,e,r){var n,i=(r=r||{}).fontSize||this.internal.getFontSize(),a=(function(t){if(t.widths&&t.kerning)return{widths:t.widths,kerning:t.kerning};var e=this.internal.getFont(t.fontName,t.fontStyle);return e.metadata.Unicode?{widths:e.metadata.Unicode.widths||{0:1},kerning:e.metadata.Unicode.kerning||{}}:{font:e.metadata,fontSize:this.internal.getFontSize(),charSpace:this.internal.getCharSpace()}}).call(this,r);n=Array.isArray(t)?t:String(t).split(/\r?\n/);var o=this.internal.scaleFactor*e/i;a.textIndent=r.textIndent?r.textIndent*this.internal.scaleFactor/i:0,a.lineIndent=r.lineIndent;var s,c,u=[];for(s=0,c=n.length;s<c;s++)u=u.concat(tG.apply(this,[n[s],o,a]));return u},function(t){t.__fontmetrics__=t.__fontmetrics__||{};for(var e="klmnopqrstuvwxyz",r={},i={},a=0;a<e.length;a++)r[e[a]]="0123456789abcdef"[a],i["0123456789abcdef"[a]]=e[a];var o=function(t){return"0x"+parseInt(t,10).toString(16)},s=t.__fontmetrics__.compress=function(t){var e,r,a,c,u=["{"];for(var h in t){if(e=t[h],r=isNaN(parseInt(h,10))?"'"+h+"'":(r=o(h=parseInt(h,10)).slice(2)).slice(0,-1)+i[r.slice(-1)],"number"==typeof e)e<0?(a=o(e).slice(3),c="-"):(a=o(e).slice(2),c=""),a=c+a.slice(0,-1)+i[a.slice(-1)];else{if("object"!==(0,n.A)(e))throw Error("Don't know what to do with value type "+(0,n.A)(e)+".");a=s(e)}u.push(r+a)}return u.push("}"),u.join("")},c=t.__fontmetrics__.uncompress=function(t){if("string"!=typeof t)throw Error("Invalid argument passed to uncompress.");for(var e,n,i,a,o={},s=1,c=o,u=[],h="",l="",f=t.length-1,d=1;d<f;d+=1)"'"==(a=t[d])?e?(i=e.join(""),e=void 0):e=[]:e?e.push(a):"{"==a?(u.push([c,i]),c={},i=void 0):"}"==a?((n=u.pop())[0][n[1]]=c,i=void 0,c=n[0]):"-"==a?s=-1:void 0===i?r.hasOwnProperty(a)?(h+=r[a],i=parseInt(h,16)*s,s=1,h=""):h+=a:r.hasOwnProperty(a)?(l+=r[a],c[i]=parseInt(l,16)*s,s=1,i=void 0,l=""):l+=a;return o},u={codePages:["WinAnsiEncoding"],WinAnsiEncoding:c("{19m8n201n9q201o9r201s9l201t9m201u8m201w9n201x9o201y8o202k8q202l8r202m9p202q8p20aw8k203k8t203t8v203u9v2cq8s212m9t15m8w15n9w2dw9s16k8u16l9u17s9z17x8y17y9y}")},h={Courier:u,"Courier-Bold":u,"Courier-BoldOblique":u,"Courier-Oblique":u,Helvetica:u,"Helvetica-Bold":u,"Helvetica-BoldOblique":u,"Helvetica-Oblique":u,"Times-Roman":u,"Times-Bold":u,"Times-BoldItalic":u,"Times-Italic":u},l={Unicode:{"Courier-Oblique":c("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-BoldItalic":c("{'widths'{k3o2q4ycx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2r202m2n2n3m2o3m2p5n202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5n4l4m4m4m4n4m4o4s4p4m4q4m4r4s4s4y4t2r4u3m4v4m4w3x4x5t4y4s4z4s5k3x5l4s5m4m5n3r5o3x5p4s5q4m5r5t5s4m5t3x5u3x5v2l5w1w5x2l5y3t5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q2l6r3m6s3r6t1w6u1w6v3m6w1w6x4y6y3r6z3m7k3m7l3m7m2r7n2r7o1w7p3r7q2w7r4m7s3m7t2w7u2r7v2n7w1q7x2n7y3t202l3mcl4mal2ram3man3mao3map3mar3mas2lat4uau1uav3maw3way4uaz2lbk2sbl3t'fof'6obo2lbp3tbq3mbr1tbs2lbu1ybv3mbz3mck4m202k3mcm4mcn4mco4mcp4mcq5ycr4mcs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz2w203k6o212m6o2dw2l2cq2l3t3m3u2l17s3x19m3m}'kerning'{cl{4qu5kt5qt5rs17ss5ts}201s{201ss}201t{cks4lscmscnscoscpscls2wu2yu201ts}201x{2wu2yu}2k{201ts}2w{4qx5kx5ou5qx5rs17su5tu}2x{17su5tu5ou}2y{4qx5kx5ou5qx5rs17ss5ts}'fof'-6ofn{17sw5tw5ou5qw5rs}7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qs}3v{17su5tu5os5qs}7p{17su5tu}ck{4qu5kt5qt5rs17ss5ts}4l{4qu5kt5qt5rs17ss5ts}cm{4qu5kt5qt5rs17ss5ts}cn{4qu5kt5qt5rs17ss5ts}co{4qu5kt5qt5rs17ss5ts}cp{4qu5kt5qt5rs17ss5ts}6l{4qu5ou5qw5rt17su5tu}5q{ckuclucmucnucoucpu4lu}5r{ckuclucmucnucoucpu4lu}7q{cksclscmscnscoscps4ls}6p{4qu5ou5qw5rt17sw5tw}ek{4qu5ou5qw5rt17su5tu}el{4qu5ou5qw5rt17su5tu}em{4qu5ou5qw5rt17su5tu}en{4qu5ou5qw5rt17su5tu}eo{4qu5ou5qw5rt17su5tu}ep{4qu5ou5qw5rt17su5tu}es{17ss5ts5qs4qu}et{4qu5ou5qw5rt17sw5tw}eu{4qu5ou5qw5rt17ss5ts}ev{17ss5ts5qs4qu}6z{17sw5tw5ou5qw5rs}fm{17sw5tw5ou5qw5rs}7n{201ts}fo{17sw5tw5ou5qw5rs}fp{17sw5tw5ou5qw5rs}fq{17sw5tw5ou5qw5rs}7r{cksclscmscnscoscps4ls}fs{17sw5tw5ou5qw5rs}ft{17su5tu}fu{17su5tu}fv{17su5tu}fw{17su5tu}fz{cksclscmscnscoscps4ls}}}"),"Helvetica-Bold":c("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),Courier:c("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-BoldOblique":c("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Bold":c("{'widths'{k3q2q5ncx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2l202m2n2n3m2o3m2p6o202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5x4l4s4m4m4n4s4o4s4p4m4q3x4r4y4s4y4t2r4u3m4v4y4w4m4x5y4y4s4z4y5k3x5l4y5m4s5n3r5o4m5p4s5q4s5r6o5s4s5t4s5u4m5v2l5w1w5x2l5y3u5z3m6k2l6l3m6m3r6n2w6o3r6p2w6q2l6r3m6s3r6t1w6u2l6v3r6w1w6x5n6y3r6z3m7k3r7l3r7m2w7n2r7o2l7p3r7q3m7r4s7s3m7t3m7u2w7v2r7w1q7x2r7y3o202l3mcl4sal2lam3man3mao3map3mar3mas2lat4uau1yav3maw3tay4uaz2lbk2sbl3t'fof'6obo2lbp3rbr1tbs2lbu2lbv3mbz3mck4s202k3mcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3rek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3m3u2l17s4s19m3m}'kerning'{cl{4qt5ks5ot5qy5rw17sv5tv}201t{cks4lscmscnscoscpscls4wv}2k{201ts}2w{4qu5ku7mu5os5qx5ru17su5tu}2x{17su5tu5ou5qs}2y{4qv5kv7mu5ot5qz5ru17su5tu}'fof'-6o7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qu}3v{17su5tu5os5qu}fu{17su5tu5ou5qu}7p{17su5tu5ou5qu}ck{4qt5ks5ot5qy5rw17sv5tv}4l{4qt5ks5ot5qy5rw17sv5tv}cm{4qt5ks5ot5qy5rw17sv5tv}cn{4qt5ks5ot5qy5rw17sv5tv}co{4qt5ks5ot5qy5rw17sv5tv}cp{4qt5ks5ot5qy5rw17sv5tv}6l{17st5tt5ou5qu}17s{ckuclucmucnucoucpu4lu4wu}5o{ckuclucmucnucoucpu4lu4wu}5q{ckzclzcmzcnzcozcpz4lz4wu}5r{ckxclxcmxcnxcoxcpx4lx4wu}5t{ckuclucmucnucoucpu4lu4wu}7q{ckuclucmucnucoucpu4lu}6p{17sw5tw5ou5qu}ek{17st5tt5qu}el{17st5tt5ou5qu}em{17st5tt5qu}en{17st5tt5qu}eo{17st5tt5qu}ep{17st5tt5ou5qu}es{17ss5ts5qu}et{17sw5tw5ou5qu}eu{17sw5tw5ou5qu}ev{17ss5ts5qu}6z{17sw5tw5ou5qu5rs}fm{17sw5tw5ou5qu5rs}fn{17sw5tw5ou5qu5rs}fo{17sw5tw5ou5qu5rs}fp{17sw5tw5ou5qu5rs}fq{17sw5tw5ou5qu5rs}7r{cktcltcmtcntcotcpt4lt5os}fs{17sw5tw5ou5qu5rs}ft{17su5tu5ou5qu}7m{5os}fv{17su5tu5ou5qu}fw{17su5tu5ou5qu}fz{cksclscmscnscoscps4ls}}}"),Symbol:c("{'widths'{k3uaw4r19m3m2k1t2l2l202m2y2n3m2p5n202q6o3k3m2s2l2t2l2v3r2w1t3m3m2y1t2z1wbk2sbl3r'fof'6o3n3m3o3m3p3m3q3m3r3m3s3m3t3m3u1w3v1w3w3r3x3r3y3r3z2wbp3t3l3m5v2l5x2l5z3m2q4yfr3r7v3k7w1o7x3k}'kerning'{'fof'-6o}}"),Helvetica:c("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}"),"Helvetica-BoldOblique":c("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),ZapfDingbats:c("{'widths'{k4u2k1w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-Bold":c("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Italic":c("{'widths'{k3n2q4ycx2l201n3m201o5t201s2l201t2l201u2l201w3r201x3r201y3r2k1t2l2l202m2n2n3m2o3m2p5n202q5t2r1p2s2l2t2l2u3m2v4n2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w4n3x4n3y4n3z3m4k5w4l3x4m3x4n4m4o4s4p3x4q3x4r4s4s4s4t2l4u2w4v4m4w3r4x5n4y4m4z4s5k3x5l4s5m3x5n3m5o3r5p4s5q3x5r5n5s3x5t3r5u3r5v2r5w1w5x2r5y2u5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q1w6r3m6s3m6t1w6u1w6v2w6w1w6x4s6y3m6z3m7k3m7l3m7m2r7n2r7o1w7p3m7q2w7r4m7s2w7t2w7u2r7v2s7w1v7x2s7y3q202l3mcl3xal2ram3man3mao3map3mar3mas2lat4wau1vav3maw4nay4waz2lbk2sbl4n'fof'6obo2lbp3mbq3obr1tbs2lbu1zbv3mbz3mck3x202k3mcm3xcn3xco3xcp3xcq5tcr4mcs3xct3xcu3xcv3xcw2l2m2ucy2lcz2ldl4mdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr4nfs3mft3mfu3mfv3mfw3mfz2w203k6o212m6m2dw2l2cq2l3t3m3u2l17s3r19m3m}'kerning'{cl{5kt4qw}201s{201sw}201t{201tw2wy2yy6q-t}201x{2wy2yy}2k{201tw}2w{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}2x{17ss5ts5os}2y{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}'fof'-6o6t{17ss5ts5qs}7t{5os}3v{5qs}7p{17su5tu5qs}ck{5kt4qw}4l{5kt4qw}cm{5kt4qw}cn{5kt4qw}co{5kt4qw}cp{5kt4qw}6l{4qs5ks5ou5qw5ru17su5tu}17s{2ks}5q{ckvclvcmvcnvcovcpv4lv}5r{ckuclucmucnucoucpu4lu}5t{2ks}6p{4qs5ks5ou5qw5ru17su5tu}ek{4qs5ks5ou5qw5ru17su5tu}el{4qs5ks5ou5qw5ru17su5tu}em{4qs5ks5ou5qw5ru17su5tu}en{4qs5ks5ou5qw5ru17su5tu}eo{4qs5ks5ou5qw5ru17su5tu}ep{4qs5ks5ou5qw5ru17su5tu}es{5ks5qs4qs}et{4qs5ks5ou5qw5ru17su5tu}eu{4qs5ks5qw5ru17su5tu}ev{5ks5qs4qs}ex{17ss5ts5qs}6z{4qv5ks5ou5qw5ru17su5tu}fm{4qv5ks5ou5qw5ru17su5tu}fn{4qv5ks5ou5qw5ru17su5tu}fo{4qv5ks5ou5qw5ru17su5tu}fp{4qv5ks5ou5qw5ru17su5tu}fq{4qv5ks5ou5qw5ru17su5tu}7r{5os}fs{4qv5ks5ou5qw5ru17su5tu}ft{17su5tu5qs}fu{17su5tu5qs}fv{17su5tu5qs}fw{17su5tu5qs}}}"),"Times-Roman":c("{'widths'{k3n2q4ycx2l201n3m201o6o201s2l201t2l201u2l201w2w201x2w201y2w2k1t2l2l202m2n2n3m2o3m2p5n202q6o2r1m2s2l2t2l2u3m2v3s2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v1w3w3s3x3s3y3s3z2w4k5w4l4s4m4m4n4m4o4s4p3x4q3r4r4s4s4s4t2l4u2r4v4s4w3x4x5t4y4s4z4s5k3r5l4s5m4m5n3r5o3x5p4s5q4s5r5y5s4s5t4s5u3x5v2l5w1w5x2l5y2z5z3m6k2l6l2w6m3m6n2w6o3m6p2w6q2l6r3m6s3m6t1w6u1w6v3m6w1w6x4y6y3m6z3m7k3m7l3m7m2l7n2r7o1w7p3m7q3m7r4s7s3m7t3m7u2w7v3k7w1o7x3k7y3q202l3mcl4sal2lam3man3mao3map3mar3mas2lat4wau1vav3maw3say4waz2lbk2sbl3s'fof'6obo2lbp3mbq2xbr1tbs2lbu1zbv3mbz2wck4s202k3mcm4scn4sco4scp4scq5tcr4mcs3xct3xcu3xcv3xcw2l2m2tcy2lcz2ldl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek2wel2wem2wen2weo2wep2weq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr3sfs3mft3mfu3mfv3mfw3mfz3m203k6o212m6m2dw2l2cq2l3t3m3u1w17s4s19m3m}'kerning'{cl{4qs5ku17sw5ou5qy5rw201ss5tw201ws}201s{201ss}201t{ckw4lwcmwcnwcowcpwclw4wu201ts}2k{201ts}2w{4qs5kw5os5qx5ru17sx5tx}2x{17sw5tw5ou5qu}2y{4qs5kw5os5qx5ru17sx5tx}'fof'-6o7t{ckuclucmucnucoucpu4lu5os5rs}3u{17su5tu5qs}3v{17su5tu5qs}7p{17sw5tw5qs}ck{4qs5ku17sw5ou5qy5rw201ss5tw201ws}4l{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cm{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cn{4qs5ku17sw5ou5qy5rw201ss5tw201ws}co{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cp{4qs5ku17sw5ou5qy5rw201ss5tw201ws}6l{17su5tu5os5qw5rs}17s{2ktclvcmvcnvcovcpv4lv4wuckv}5o{ckwclwcmwcnwcowcpw4lw4wu}5q{ckyclycmycnycoycpy4ly4wu5ms}5r{cktcltcmtcntcotcpt4lt4ws}5t{2ktclvcmvcnvcovcpv4lv4wuckv}7q{cksclscmscnscoscps4ls}6p{17su5tu5qw5rs}ek{5qs5rs}el{17su5tu5os5qw5rs}em{17su5tu5os5qs5rs}en{17su5qs5rs}eo{5qs5rs}ep{17su5tu5os5qw5rs}es{5qs}et{17su5tu5qw5rs}eu{17su5tu5qs5rs}ev{5qs}6z{17sv5tv5os5qx5rs}fm{5os5qt5rs}fn{17sv5tv5os5qx5rs}fo{17sv5tv5os5qx5rs}fp{5os5qt5rs}fq{5os5qt5rs}7r{ckuclucmucnucoucpu4lu5os}fs{17sv5tv5os5qx5rs}ft{17ss5ts5qs}fu{17sw5tw5qs}fv{17sw5tw5qs}fw{17ss5ts5qs}fz{ckuclucmucnucoucpu4lu5os5rs}}}"),"Helvetica-Oblique":c("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}")}};t.events.push(["addFont",function(t){var e=t.font,r=l.Unicode[e.postScriptName];r&&(e.metadata.Unicode={},e.metadata.Unicode.widths=r.widths,e.metadata.Unicode.kerning=r.kerning);var n=h[e.postScriptName];n&&(e.metadata.Unicode.encoding=n,e.encoding=n.codePages[0])}])}(q.API),function(t){var e=function(t){for(var e=t.length,r=new Uint8Array(e),n=0;n<e;n++)r[n]=t.charCodeAt(n);return r};t.API.events.push(["addFont",function(r){var n,i=void 0,a=r.font,o=r.instance;if(!a.isStandardFont){if(void 0===o)throw Error("Font does not exist in vFS, import fonts or remove declaration doc.addFont('"+a.postScriptName+"').");if("string"!=typeof(i=!1===o.existsFileInVFS(a.postScriptName)?o.loadFile(a.postScriptName):o.getFileFromVFS(a.postScriptName)))throw Error("Font is not stored as string-data in vFS, import fonts or remove declaration doc.addFont('"+a.postScriptName+"').");n=i,n=/^\x00\x01\x00\x00/.test(n)?e(n):e(l(n)),a.metadata=t.API.TTFFont.open(n),a.metadata.Unicode=a.metadata.Unicode||{encoding:{},kerning:{},widths:[]},a.metadata.glyIdsUsed=[0]}}])}(q),q.API.addSvgAsImage=function(t,e,n,i,o,c,u,h){if(isNaN(e)||isNaN(n))throw s.error("jsPDF.addSvgAsImage: Invalid coordinates",arguments),Error("Invalid coordinates passed to jsPDF.addSvgAsImage");if(isNaN(i)||isNaN(o))throw s.error("jsPDF.addSvgAsImage: Invalid measurements",arguments),Error("Invalid measurements (width and/or height) passed to jsPDF.addSvgAsImage");var l=document.createElement("canvas");l.width=i,l.height=o;var f=l.getContext("2d");f.fillStyle="#fff",f.fillRect(0,0,l.width,l.height);var d={ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0},p=this;return(a.canvg?Promise.resolve(a.canvg):Promise.all([r.e(501),r.e(2121)]).then(r.bind(r,34137))).catch(function(t){return Promise.reject(Error("Could not load canvg: "+t))}).then(function(t){return t.default?t.default:t}).then(function(e){return e.fromString(f,t,d)},function(){return Promise.reject(Error("Could not load canvg."))}).then(function(t){return t.render(d)}).then(function(){p.addImage(l.toDataURL("image/jpeg",1),e,n,i,o,u,h)})},q.API.putTotalPages=function(t){var e,r=0;15>parseInt(this.internal.getFont().id.substr(1),10)?(e=RegExp(t,"g"),r=this.internal.getNumberOfPages()):(e=RegExp(this.pdfEscape16(t,this.internal.getFont()),"g"),r=this.pdfEscape16(this.internal.getNumberOfPages()+"",this.internal.getFont()));for(var n=1;n<=this.internal.getNumberOfPages();n++)for(var i=0;i<this.internal.pages[n].length;i++)this.internal.pages[n][i]=this.internal.pages[n][i].replace(e,r);return this},q.API.viewerPreferences=function(t,e){t=t||{},e=e||!1;var r,i,a,o,s={HideToolbar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideMenubar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideWindowUI:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},FitWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},CenterWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},DisplayDocTitle:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.4},NonFullScreenPageMode:{defaultValue:"UseNone",value:"UseNone",type:"name",explicitSet:!1,valueSet:["UseNone","UseOutlines","UseThumbs","UseOC"],pdfVersion:1.3},Direction:{defaultValue:"L2R",value:"L2R",type:"name",explicitSet:!1,valueSet:["L2R","R2L"],pdfVersion:1.3},ViewArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},ViewClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintScaling:{defaultValue:"AppDefault",value:"AppDefault",type:"name",explicitSet:!1,valueSet:["AppDefault","None"],pdfVersion:1.6},Duplex:{defaultValue:"",value:"none",type:"name",explicitSet:!1,valueSet:["Simplex","DuplexFlipShortEdge","DuplexFlipLongEdge","none"],pdfVersion:1.7},PickTrayByPDFSize:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.7},PrintPageRange:{defaultValue:"",value:"",type:"array",explicitSet:!1,valueSet:null,pdfVersion:1.7},NumCopies:{defaultValue:1,value:1,type:"integer",explicitSet:!1,valueSet:null,pdfVersion:1.7}},c=Object.keys(s),u=[],h=0,l=0,f=0;function d(t,e){var r,n=!1;for(r=0;r<t.length;r+=1)t[r]===e&&(n=!0);return n}if(void 0===this.internal.viewerpreferences&&(this.internal.viewerpreferences={},this.internal.viewerpreferences.configuration=JSON.parse(JSON.stringify(s)),this.internal.viewerpreferences.isSubscribed=!1),r=this.internal.viewerpreferences.configuration,"reset"===t||!0===e){var p=c.length;for(f=0;f<p;f+=1)r[c[f]].value=r[c[f]].defaultValue,r[c[f]].explicitSet=!1}if("object"===(0,n.A)(t)){for(a in t)if(o=t[a],d(c,a)&&void 0!==o){if("boolean"===r[a].type&&"boolean"==typeof o)r[a].value=o;else if("name"===r[a].type&&d(r[a].valueSet,o))r[a].value=o;else if("integer"===r[a].type&&Number.isInteger(o))r[a].value=o;else if("array"===r[a].type){for(h=0;h<o.length;h+=1)if(i=!0,1===o[h].length&&"number"==typeof o[h][0])u.push(String(o[h]-1));else if(o[h].length>1){for(l=0;l<o[h].length;l+=1)"number"!=typeof o[h][l]&&(i=!1);!0===i&&u.push([o[h][0]-1,o[h][1]-1].join(" "))}r[a].value="["+u.join(" ")+"]"}else r[a].value=r[a].defaultValue;r[a].explicitSet=!0}}return!1===this.internal.viewerpreferences.isSubscribed&&(this.internal.events.subscribe("putCatalog",function(){var t,e=[];for(t in r)!0===r[t].explicitSet&&("name"===r[t].type?e.push("/"+t+" /"+r[t].value):e.push("/"+t+" "+r[t].value));0!==e.length&&this.internal.write("/ViewerPreferences\n<<\n"+e.join("\n")+"\n>>")}),this.internal.viewerpreferences.isSubscribed=!0),this.internal.viewerpreferences.configuration=r,this},function(t){var e=function(){var t='<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"><rdf:Description rdf:about="" xmlns:jspdf="'+this.internal.__metadata__.namespaceuri+'"><jspdf:metadata>',e=unescape(encodeURIComponent('<x:xmpmeta xmlns:x="adobe:ns:meta/">')),r=unescape(encodeURIComponent(t)),n=unescape(encodeURIComponent(this.internal.__metadata__.metadata)),i=unescape(encodeURIComponent("</jspdf:metadata></rdf:Description></rdf:RDF>")),a=unescape(encodeURIComponent("</x:xmpmeta>")),o=r.length+n.length+i.length+e.length+a.length;this.internal.__metadata__.metadata_object_number=this.internal.newObject(),this.internal.write("<< /Type /Metadata /Subtype /XML /Length "+o+" >>"),this.internal.write("stream"),this.internal.write(e+r+n+i+a),this.internal.write("endstream"),this.internal.write("endobj")},r=function(){this.internal.__metadata__.metadata_object_number&&this.internal.write("/Metadata "+this.internal.__metadata__.metadata_object_number+" 0 R")};t.addMetadata=function(t,n){return void 0===this.internal.__metadata__&&(this.internal.__metadata__={metadata:t,namespaceuri:n||"http://jspdf.default.namespaceuri/"},this.internal.events.subscribe("putCatalog",r),this.internal.events.subscribe("postPutResources",e)),this}}(q.API),function(t){var e=t.API,r=e.pdfEscape16=function(t,e){for(var r,n=e.metadata.Unicode.widths,i=["","0","00","000","0000"],a=[""],o=0,s=t.length;o<s&&(r=e.metadata.characterToGlyph(t.charCodeAt(o)),e.metadata.glyIdsUsed.push(r),e.metadata.toUnicode[r]=t.charCodeAt(o),-1==n.indexOf(r)&&(n.push(r),n.push([parseInt(e.metadata.widthOfGlyph(r),10)])),"0"!=r);++o)r=r.toString(16),a.push(i[4-r.length],r);return a.join("")},n=function(t){var e,r,n,i,a,o,s;for(a="/CIDInit /ProcSet findresource begin\n12 dict begin\nbegincmap\n/CIDSystemInfo <<\n  /Registry (Adobe)\n  /Ordering (UCS)\n  /Supplement 0\n>> def\n/CMapName /Adobe-Identity-UCS def\n/CMapType 2 def\n1 begincodespacerange\n<0000><ffff>\nendcodespacerange",n=[],o=0,s=(r=Object.keys(t).sort(function(t,e){return t-e})).length;o<s;o++)e=r[o],n.length>=100&&(a+="\n"+n.length+" beginbfchar\n"+n.join("\n")+"\nendbfchar",n=[]),void 0!==t[e]&&null!==t[e]&&"function"==typeof t[e].toString&&(i=("0000"+t[e].toString(16)).slice(-4),e=("0000"+(+e).toString(16)).slice(-4),n.push("<"+e+"><"+i+">"));return n.length&&(a+="\n"+n.length+" beginbfchar\n"+n.join("\n")+"\nendbfchar\n"),a+="endcmap\nCMapName currentdict /CMap defineresource pop\nend\nend"};e.events.push(["putFont",function(e){!function(e){var r=e.font,i=e.out,a=e.newObject,o=e.putStream;if(r.metadata instanceof t.API.TTFFont&&"Identity-H"===r.encoding){for(var s=r.metadata.Unicode.widths,c=r.metadata.subset.encode(r.metadata.glyIdsUsed,1),u="",h=0;h<c.length;h++)u+=String.fromCharCode(c[h]);var l=a();o({data:u,addLength1:!0,objectId:l}),i("endobj");var f=a();o({data:n(r.metadata.toUnicode),addLength1:!0,objectId:f}),i("endobj");var d=a();i("<<"),i("/Type /FontDescriptor"),i("/FontName /"+I(r.fontName)),i("/FontFile2 "+l+" 0 R"),i("/FontBBox "+t.API.PDFObject.convert(r.metadata.bbox)),i("/Flags "+r.metadata.flags),i("/StemV "+r.metadata.stemV),i("/ItalicAngle "+r.metadata.italicAngle),i("/Ascent "+r.metadata.ascender),i("/Descent "+r.metadata.decender),i("/CapHeight "+r.metadata.capHeight),i(">>"),i("endobj");var p=a();i("<<"),i("/Type /Font"),i("/BaseFont /"+I(r.fontName)),i("/FontDescriptor "+d+" 0 R"),i("/W "+t.API.PDFObject.convert(s)),i("/CIDToGIDMap /Identity"),i("/DW 1000"),i("/Subtype /CIDFontType2"),i("/CIDSystemInfo"),i("<<"),i("/Supplement 0"),i("/Registry (Adobe)"),i("/Ordering ("+r.encoding+")"),i(">>"),i(">>"),i("endobj"),r.objectNumber=a(),i("<<"),i("/Type /Font"),i("/Subtype /Type0"),i("/ToUnicode "+f+" 0 R"),i("/BaseFont /"+I(r.fontName)),i("/Encoding /"+r.encoding),i("/DescendantFonts ["+p+" 0 R]"),i(">>"),i("endobj"),r.isAlreadyPutted=!0}}(e)}]),e.events.push(["putFont",function(e){!function(e){var r=e.font,i=e.out,a=e.newObject,o=e.putStream;if(r.metadata instanceof t.API.TTFFont&&"WinAnsiEncoding"===r.encoding){for(var s=r.metadata.rawData,c="",u=0;u<s.length;u++)c+=String.fromCharCode(s[u]);var h=a();o({data:c,addLength1:!0,objectId:h}),i("endobj");var l=a();o({data:n(r.metadata.toUnicode),addLength1:!0,objectId:l}),i("endobj");var f=a();i("<<"),i("/Descent "+r.metadata.decender),i("/CapHeight "+r.metadata.capHeight),i("/StemV "+r.metadata.stemV),i("/Type /FontDescriptor"),i("/FontFile2 "+h+" 0 R"),i("/Flags 96"),i("/FontBBox "+t.API.PDFObject.convert(r.metadata.bbox)),i("/FontName /"+I(r.fontName)),i("/ItalicAngle "+r.metadata.italicAngle),i("/Ascent "+r.metadata.ascender),i(">>"),i("endobj"),r.objectNumber=a();for(var d=0;d<r.metadata.hmtx.widths.length;d++)r.metadata.hmtx.widths[d]=parseInt(r.metadata.hmtx.widths[d]*(1e3/r.metadata.head.unitsPerEm));i("<</Subtype/TrueType/Type/Font/ToUnicode "+l+" 0 R/BaseFont/"+I(r.fontName)+"/FontDescriptor "+f+" 0 R/Encoding/"+r.encoding+" /FirstChar 29 /LastChar 255 /Widths "+t.API.PDFObject.convert(r.metadata.hmtx.widths)+">>"),i("endobj"),r.isAlreadyPutted=!0}}(e)}]);var i=function(t){var e,n=t.text||"",i=t.x,a=t.y,o=t.options||{},s=t.mutex||{},c=s.pdfEscape,u=s.activeFontKey,h=s.fonts,l=u,f="",d=0,p="",g=h[l].encoding;if("Identity-H"!==h[l].encoding)return{text:n,x:i,y:a,options:o,mutex:s};for(p=n,l=u,Array.isArray(n)&&(p=n[0]),d=0;d<p.length;d+=1)h[l].metadata.hasOwnProperty("cmap")&&(e=h[l].metadata.cmap.unicode.codeMap[p[d].charCodeAt(0)]),e||256>p[d].charCodeAt(0)&&h[l].metadata.hasOwnProperty("Unicode")?f+=p[d]:f+="";var m="";return 14>parseInt(l.slice(1))||"WinAnsiEncoding"===g?m=c(f,l).split("").map(function(t){return t.charCodeAt(0).toString(16)}).join(""):"Identity-H"===g&&(m=r(f,h[l])),s.isHex=!0,{text:m,x:i,y:a,options:o,mutex:s}};e.events.push(["postProcessText",function(t){var e=t.text||"",r=[],n={text:e,x:t.x,y:t.y,options:t.options,mutex:t.mutex};if(Array.isArray(e)){var a=0;for(a=0;a<e.length;a+=1)Array.isArray(e[a])&&3===e[a].length?r.push([i(Object.assign({},n,{text:e[a][0]})).text,e[a][1],e[a][2]]):r.push(i(Object.assign({},n,{text:e[a]})).text);t.text=r}else t.text=i(Object.assign({},n,{text:e})).text}])}(q),function(t){var e=function(){return void 0===this.internal.vFS&&(this.internal.vFS={}),!0};t.existsFileInVFS=function(t){return e.call(this),void 0!==this.internal.vFS[t]},t.addFileToVFS=function(t,r){return e.call(this),this.internal.vFS[t]=r,this},t.getFileFromVFS=function(t){return e.call(this),void 0!==this.internal.vFS[t]?this.internal.vFS[t]:null}}(q.API),function(t){t.__bidiEngine__=t.prototype.__bidiEngine__=function(t){var r,n,i,a,o,s,c,u=[[0,3,0,1,0,0,0],[0,3,0,1,2,2,0],[0,3,0,17,2,0,1],[0,3,5,5,4,1,0],[0,3,21,21,4,0,1],[0,3,5,5,4,2,0]],h=[[2,0,1,1,0,1,0],[2,0,1,1,0,2,0],[2,0,2,1,3,2,0],[2,0,2,33,3,1,1]],l={L:0,R:1,EN:2,AN:3,N:4,B:5,S:6},f={0:0,5:1,6:2,7:3,32:4,251:5,254:6,255:7},d=["(",")","(","<",">","<","[","]","[","{","}","{","\xab","\xbb","\xab","‹","›","‹","⁅","⁆","⁅","⁽","⁾","⁽","₍","₎","₍","≤","≥","≤","〈","〉","〈","﹙","﹚","﹙","﹛","﹜","﹛","﹝","﹞","﹝","﹤","﹥","﹤"],p=new RegExp(/^([1-4|9]|1[0-9]|2[0-9]|3[0168]|4[04589]|5[012]|7[78]|159|16[0-9]|17[0-2]|21[569]|22[03489]|250)$/),g=!1,m=0;this.__bidiEngine__={};var v=function(t){var r=t.charCodeAt(),n=r>>8,i=f[n];return void 0!==i?e[256*i+(255&r)]:252===n||253===n?"AL":p.test(n)?"L":8===n?"R":"N"},b=function(t){for(var e,r=0;r<t.length&&"L"!==(e=v(t.charAt(r)));r++)if("R"===e)return!0;return!1},y=function(t,e,o,s){var c,u,h,l,f=e[s];switch(f){case"L":case"R":case"LRE":case"RLE":case"LRO":case"RLO":case"PDF":g=!1;break;case"N":case"AN":break;case"EN":g&&(f="AN");break;case"AL":g=!0,f="R";break;case"WS":case"BN":f="N";break;case"CS":s<1||s+1>=e.length||"EN"!==(c=o[s-1])&&"AN"!==c||"EN"!==(u=e[s+1])&&"AN"!==u?f="N":g&&(u="AN"),f=u===c?u:"N";break;case"ES":f="EN"===(c=s>0?o[s-1]:"B")&&s+1<e.length&&"EN"===e[s+1]?"EN":"N";break;case"ET":if(s>0&&"EN"===o[s-1]){f="EN";break}if(g){f="N";break}for(h=s+1,l=e.length;h<l&&"ET"===e[h];)h++;f=h<l&&"EN"===e[h]?"EN":"N";break;case"NSM":if(i&&!a){for(l=e.length,h=s+1;h<l&&"NSM"===e[h];)h++;if(h<l){var d=t[s];if(c=e[h],(d>=1425&&d<=2303||64286===d)&&("R"===c||"AL"===c)){f="R";break}}}f=s<1||"B"===(c=e[s-1])?"N":o[s-1];break;case"B":g=!1,r=!0,f=m;break;case"S":n=!0,f="N"}return f},w=function(t,e,r){var n=t.split("");return r&&x(n,r,{hiLevel:m}),n.reverse(),e&&e.reverse(),n.join("")},x=function(t,e,i){var a,o,s,c,f,d=-1,p=t.length,b=0,w=[],x=m?h:u,N=[];for(g=!1,r=!1,n=!1,o=0;o<p;o++)N[o]=v(t[o]);for(s=0;s<p;s++){if(f=b,w[s]=y(t,N,w,s),a=240&(b=x[f][l[w[s]]]),b&=15,e[s]=c=x[b][5],a>0)if(16===a){for(o=d;o<s;o++)e[o]=1;d=-1}else d=-1;if(x[b][6])-1===d&&(d=s);else if(d>-1){for(o=d;o<s;o++)e[o]=c;d=-1}"B"===N[s]&&(e[s]=0),i.hiLevel|=c}n&&function(t,e,r){for(var n=0;n<r;n++)if("S"===t[n]){e[n]=m;for(var i=n-1;i>=0&&"WS"===t[i];i--)e[i]=m}}(N,e,p)},N=function(t,e,n,i,a){if(!(a.hiLevel<t)){if(1===t&&1===m&&!r)return e.reverse(),void(n&&n.reverse());for(var o,s,c,u,h=e.length,l=0;l<h;){if(i[l]>=t){for(c=l+1;c<h&&i[c]>=t;)c++;for(u=l,s=c-1;u<s;u++,s--)o=e[u],e[u]=e[s],e[s]=o,n&&(o=n[u],n[u]=n[s],n[s]=o);l=c}l++}}},A=function(t,e,r){var n=t.split(""),i={hiLevel:m};return r||(r=[]),x(n,r,i),function(t,e,r){if(0!==r.hiLevel&&c)for(var n,i=0;i<t.length;i++)1===e[i]&&(n=d.indexOf(t[i]))>=0&&(t[i]=d[n+1])}(n,r,i),N(2,n,e,r,i),N(1,n,e,r,i),n.join("")};return this.__bidiEngine__.doBidiReorder=function(t,e,r){if(function(t,e){if(e)for(var r=0;r<t.length;r++)e[r]=r;void 0===a&&(a=b(t)),void 0===s&&(s=b(t))}(t,e),i||!o||s)if(i&&o&&a^s)m=+!!a,t=w(t,e,r);else if(!i&&o&&s)m=+!!a,t=w(t=A(t,e,r),e);else if(!i||a||o||s){if(i&&!o&&a^s)t=w(t,e),a?(m=0,t=A(t,e,r)):(m=1,t=w(t=A(t,e,r),e));else if(i&&a&&!o&&s)m=1,t=w(t=A(t,e,r),e);else if(!i&&!o&&a^s){var n=c;a?(m=1,t=A(t,e,r),m=0,c=!1,t=A(t,e,r),c=n):(m=0,t=w(t=A(t,e,r),e),m=1,c=!1,t=A(t,e,r),c=n,t=w(t,e))}}else m=0,t=A(t,e,r);else m=+!!a,t=A(t,e,r);return t},this.__bidiEngine__.setOptions=function(t){t&&(i=t.isInputVisual,o=t.isOutputVisual,a=t.isInputRtl,s=t.isOutputRtl,c=t.isSymmetricSwapping)},this.__bidiEngine__.setOptions(t),this.__bidiEngine__};var e=["BN","BN","BN","BN","BN","BN","BN","BN","BN","S","B","S","WS","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","B","B","B","S","WS","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","BN","BN","BN","BN","BN","BN","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","CS","N","ET","ET","ET","ET","N","N","N","N","L","N","N","BN","N","N","ET","ET","EN","EN","N","L","N","N","N","EN","L","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","N","N","N","N","N","ET","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","NSM","R","NSM","NSM","R","NSM","NSM","R","NSM","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","N","N","N","N","N","R","R","R","R","R","N","N","N","N","N","N","N","N","N","N","N","AN","AN","AN","AN","AN","AN","N","N","AL","ET","ET","AL","CS","AL","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","AN","AN","AN","AN","AN","AN","AN","AN","AN","ET","AN","AN","AL","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","N","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","NSM","NSM","N","NSM","NSM","NSM","NSM","AL","AL","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","R","N","N","N","N","R","N","N","N","N","N","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","BN","BN","BN","L","R","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","B","LRE","RLE","PDF","LRO","RLO","CS","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","BN","BN","BN","BN","BN","N","LRI","RLI","FSI","PDI","BN","BN","BN","BN","BN","BN","EN","L","N","N","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","L","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","N","N","N","N","N","R","NSM","R","R","R","R","R","R","R","R","R","R","ES","R","R","R","R","R","R","R","R","R","R","R","R","R","N","R","R","R","R","R","N","R","N","R","R","N","R","R","N","R","R","R","R","R","R","R","R","R","R","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","CS","N","N","CS","N","N","N","N","N","N","N","N","N","ET","N","N","ES","ES","N","N","N","N","N","ET","ET","N","N","N","N","N","AL","AL","AL","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","BN","N","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","N","N","N","ET","ET","N","N","N","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N"],r=new t.__bidiEngine__({isInputVisual:!0});t.API.events.push(["postProcessText",function(t){var e=t.text;t.x,t.y;var n=t.options||{};t.mutex,n.lang;var i=[];if(n.isInputVisual="boolean"!=typeof n.isInputVisual||n.isInputVisual,r.setOptions(n),"[object Array]"===Object.prototype.toString.call(e)){var a=0;for(i=[],a=0;a<e.length;a+=1)"[object Array]"===Object.prototype.toString.call(e[a])?i.push([r.doBidiReorder(e[a][0]),e[a][1],e[a][2]]):i.push([r.doBidiReorder(e[a])]);t.text=i}else t.text=r.doBidiReorder(e);r.setOptions({isInputVisual:!0})}])}(q),q.API.TTFFont=function(){function t(t){var e;if(this.rawData=t,e=this.contents=new t1(t),this.contents.pos=4,"ttcf"===e.readString(4))throw Error("TTCF not supported.");e.pos=0,this.parse(),this.subset=new eu(this),this.registerTTF()}return t.open=function(e){return new t(e)},t.prototype.parse=function(){return this.directory=new t2(this.contents),this.head=new t3(this),this.name=new ee(this),this.cmap=new t6(this),this.toUnicode={},this.hhea=new t8(this),this.maxp=new er(this),this.hmtx=new en(this),this.post=new t9(this),this.os2=new t7(this),this.loca=new ec(this),this.glyf=new ea(this),this.ascender=this.os2.exists&&this.os2.ascender||this.hhea.ascender,this.decender=this.os2.exists&&this.os2.decender||this.hhea.decender,this.lineGap=this.os2.exists&&this.os2.lineGap||this.hhea.lineGap,this.bbox=[this.head.xMin,this.head.yMin,this.head.xMax,this.head.yMax]},t.prototype.registerTTF=function(){var t,e,r,n,i;if(this.scaleFactor=1e3/this.head.unitsPerEm,this.bbox=(function(){var e,r,n,i;for(i=[],e=0,r=(n=this.bbox).length;e<r;e++)t=n[e],i.push(Math.round(t*this.scaleFactor));return i}).call(this),this.stemV=0,this.post.exists?(r=255&(n=this.post.italic_angle),0!=(32768&(e=n>>16))&&(e=-(1+(65535^e))),this.italicAngle=+(e+"."+r)):this.italicAngle=0,this.ascender=Math.round(this.ascender*this.scaleFactor),this.decender=Math.round(this.decender*this.scaleFactor),this.lineGap=Math.round(this.lineGap*this.scaleFactor),this.capHeight=this.os2.exists&&this.os2.capHeight||this.ascender,this.xHeight=this.os2.exists&&this.os2.xHeight||0,this.familyClass=(this.os2.exists&&this.os2.familyClass||0)>>8,this.isSerif=1===(i=this.familyClass)||2===i||3===i||4===i||5===i||7===i,this.isScript=10===this.familyClass,this.flags=0,this.post.isFixedPitch&&(this.flags|=1),this.isSerif&&(this.flags|=2),this.isScript&&(this.flags|=8),0!==this.italicAngle&&(this.flags|=64),this.flags|=32,!this.cmap.unicode)throw Error("No unicode cmap for font")},t.prototype.characterToGlyph=function(t){var e;return(null!=(e=this.cmap.unicode)?e.codeMap[t]:void 0)||0},t.prototype.widthOfGlyph=function(t){var e;return e=1e3/this.head.unitsPerEm,this.hmtx.forGlyph(t).advance*e},t.prototype.widthOfString=function(t,e,r){var n,i,a,o;for(a=0,i=0,o=(t=""+t).length;0<=o?i<o:i>o;i=0<=o?++i:--i)n=t.charCodeAt(i),a+=this.widthOfGlyph(this.characterToGlyph(n))+1e3/e*r||0;return e/1e3*a},t.prototype.lineHeight=function(t,e){var r;return null==e&&(e=!1),r=e?this.lineGap:0,(this.ascender+r-this.decender)/1e3*t},t}();var tQ,t1=function(){function t(t){this.data=null!=t?t:[],this.pos=0,this.length=this.data.length}return t.prototype.readByte=function(){return this.data[this.pos++]},t.prototype.writeByte=function(t){return this.data[this.pos++]=t},t.prototype.readUInt32=function(){return 0x1000000*this.readByte()+(this.readByte()<<16)+(this.readByte()<<8)+this.readByte()},t.prototype.writeUInt32=function(t){return this.writeByte(t>>>24&255),this.writeByte(t>>16&255),this.writeByte(t>>8&255),this.writeByte(255&t)},t.prototype.readInt32=function(){var t;return(t=this.readUInt32())>=0x80000000?t-0x100000000:t},t.prototype.writeInt32=function(t){return t<0&&(t+=0x100000000),this.writeUInt32(t)},t.prototype.readUInt16=function(){return this.readByte()<<8|this.readByte()},t.prototype.writeUInt16=function(t){return this.writeByte(t>>8&255),this.writeByte(255&t)},t.prototype.readInt16=function(){var t;return(t=this.readUInt16())>=32768?t-65536:t},t.prototype.writeInt16=function(t){return t<0&&(t+=65536),this.writeUInt16(t)},t.prototype.readString=function(t){var e,r;for(r=[],e=0;0<=t?e<t:e>t;e=0<=t?++e:--e)r[e]=String.fromCharCode(this.readByte());return r.join("")},t.prototype.writeString=function(t){var e,r,n;for(n=[],e=0,r=t.length;0<=r?e<r:e>r;e=0<=r?++e:--e)n.push(this.writeByte(t.charCodeAt(e)));return n},t.prototype.readShort=function(){return this.readInt16()},t.prototype.writeShort=function(t){return this.writeInt16(t)},t.prototype.readLongLong=function(){var t,e,r,n,i,a,o,s;return t=this.readByte(),e=this.readByte(),r=this.readByte(),n=this.readByte(),i=this.readByte(),a=this.readByte(),o=this.readByte(),s=this.readByte(),128&t?-1*(0x100000000000000*(255^t)+0x1000000000000*(255^e)+0x10000000000*(255^r)+0x100000000*(255^n)+0x1000000*(255^i)+65536*(255^a)+256*(255^o)+(255^s)+1):0x100000000000000*t+0x1000000000000*e+0x10000000000*r+0x100000000*n+0x1000000*i+65536*a+256*o+s},t.prototype.writeLongLong=function(t){var e,r;return e=Math.floor(t/0x100000000),r=0|t,this.writeByte(e>>24&255),this.writeByte(e>>16&255),this.writeByte(e>>8&255),this.writeByte(255&e),this.writeByte(r>>24&255),this.writeByte(r>>16&255),this.writeByte(r>>8&255),this.writeByte(255&r)},t.prototype.readInt=function(){return this.readInt32()},t.prototype.writeInt=function(t){return this.writeInt32(t)},t.prototype.read=function(t){var e,r;for(e=[],r=0;0<=t?r<t:r>t;r=0<=t?++r:--r)e.push(this.readByte());return e},t.prototype.write=function(t){var e,r,n,i;for(i=[],r=0,n=t.length;r<n;r++)e=t[r],i.push(this.writeByte(e));return i},t}(),t2=function(){var t;function e(t){var e,r,n;for(this.scalarType=t.readInt(),this.tableCount=t.readShort(),this.searchRange=t.readShort(),this.entrySelector=t.readShort(),this.rangeShift=t.readShort(),this.tables={},r=0,n=this.tableCount;0<=n?r<n:r>n;r=0<=n?++r:--r)e={tag:t.readString(4),checksum:t.readInt(),offset:t.readInt(),length:t.readInt()},this.tables[e.tag]=e}return e.prototype.encode=function(e){var r,n,i,a,o,s,c,u,h,l,f,d,p;for(p in a=Math.floor((h=16*Math.floor(Math.log(f=Object.keys(e).length)/(s=Math.log(2))))/s),u=16*f-h,(n=new t1).writeInt(this.scalarType),n.writeShort(f),n.writeShort(h),n.writeShort(a),n.writeShort(u),i=16*f,c=n.pos+i,o=null,d=[],e)for(l=e[p],n.writeString(p),n.writeInt(t(l)),n.writeInt(c),n.writeInt(l.length),d=d.concat(l),"head"===p&&(o=c),c+=l.length;c%4;)d.push(0),c++;return n.write(d),r=0xb1b0afba-t(n.data),n.pos=o+8,n.writeUInt32(r),n.data},t=function(t){var e,r,n,i;for(t=ei.call(t);t.length%4;)t.push(0);for(n=new t1(t),r=0,e=0,i=t.length;e<i;e=e+=4)r+=n.readUInt32();return 0|r},e}(),t5={}.hasOwnProperty,t0=function(t,e){for(var r in e)t5.call(e,r)&&(t[r]=e[r]);function n(){this.constructor=t}return n.prototype=e.prototype,t.prototype=new n,t.__super__=e.prototype,t};tQ=function(){function t(t){var e;this.file=t,e=this.file.directory.tables[this.tag],this.exists=!!e,e&&(this.offset=e.offset,this.length=e.length,this.parse(this.file.contents))}return t.prototype.parse=function(){},t.prototype.encode=function(){},t.prototype.raw=function(){return this.exists?(this.file.contents.pos=this.offset,this.file.contents.read(this.length)):null},t}();var t3=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return t0(e,tQ),e.prototype.tag="head",e.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.revision=t.readInt(),this.checkSumAdjustment=t.readInt(),this.magicNumber=t.readInt(),this.flags=t.readShort(),this.unitsPerEm=t.readShort(),this.created=t.readLongLong(),this.modified=t.readLongLong(),this.xMin=t.readShort(),this.yMin=t.readShort(),this.xMax=t.readShort(),this.yMax=t.readShort(),this.macStyle=t.readShort(),this.lowestRecPPEM=t.readShort(),this.fontDirectionHint=t.readShort(),this.indexToLocFormat=t.readShort(),this.glyphDataFormat=t.readShort()},e.prototype.encode=function(t){var e;return(e=new t1).writeInt(this.version),e.writeInt(this.revision),e.writeInt(this.checkSumAdjustment),e.writeInt(this.magicNumber),e.writeShort(this.flags),e.writeShort(this.unitsPerEm),e.writeLongLong(this.created),e.writeLongLong(this.modified),e.writeShort(this.xMin),e.writeShort(this.yMin),e.writeShort(this.xMax),e.writeShort(this.yMax),e.writeShort(this.macStyle),e.writeShort(this.lowestRecPPEM),e.writeShort(this.fontDirectionHint),e.writeShort(t),e.writeShort(this.glyphDataFormat),e.data},e}(),t4=function(){function t(t,e){var r,n,i,a,o,s,c,u,h,l,f,d,p,g,m,v;switch(this.platformID=t.readUInt16(),this.encodingID=t.readShort(),this.offset=e+t.readInt(),h=t.pos,t.pos=this.offset,this.format=t.readUInt16(),this.length=t.readUInt16(),this.language=t.readUInt16(),this.isUnicode=3===this.platformID&&1===this.encodingID&&4===this.format||0===this.platformID&&4===this.format,this.codeMap={},this.format){case 0:for(s=0;s<256;++s)this.codeMap[s]=t.readByte();break;case 4:for(l=t.readUInt16()/2,t.pos+=6,i=function(){var e,r;for(r=[],s=e=0;0<=l?e<l:e>l;s=0<=l?++e:--e)r.push(t.readUInt16());return r}(),t.pos+=2,d=function(){var e,r;for(r=[],s=e=0;0<=l?e<l:e>l;s=0<=l?++e:--e)r.push(t.readUInt16());return r}(),c=function(){var e,r;for(r=[],s=e=0;0<=l?e<l:e>l;s=0<=l?++e:--e)r.push(t.readUInt16());return r}(),u=function(){var e,r;for(r=[],s=e=0;0<=l?e<l:e>l;s=0<=l?++e:--e)r.push(t.readUInt16());return r}(),n=(this.length-t.pos+this.offset)/2,o=function(){var e,r;for(r=[],s=e=0;0<=n?e<n:e>n;s=0<=n?++e:--e)r.push(t.readUInt16());return r}(),s=g=0,v=i.length;g<v;s=++g)for(p=i[s],r=m=f=d[s];f<=p?m<=p:m>=p;r=f<=p?++m:--m)0===u[s]?a=r+c[s]:0!==(a=o[u[s]/2+(r-f)-(l-s)]||0)&&(a+=c[s]),this.codeMap[r]=65535&a}t.pos=h}return t.encode=function(t,e){var r,n,i,a,o,s,c,u,h,l,f,d,p,g,m,v,b,y,w,x,N,A,L,S,_,P,k,F,I,C,j,O,E,M,q,B,D,R,T,z,U,H,W,V,G,Y;switch(F=new t1,a=Object.keys(t).sort(function(t,e){return t-e}),e){case"macroman":for(p=0,g=function(){var t=[];for(d=0;d<256;++d)t.push(0);return t}(),v={0:0},i={},I=0,E=a.length;I<E;I++)null==v[W=t[n=a[I]]]&&(v[W]=++p),i[n]={old:t[n],new:v[t[n]]},g[n]=v[t[n]];return F.writeUInt16(1),F.writeUInt16(0),F.writeUInt32(12),F.writeUInt16(0),F.writeUInt16(262),F.writeUInt16(0),F.write(g),{charMap:i,subtable:F.data,maxGlyphID:p+1};case"unicode":for(P=[],h=[],b=0,v={},r={},m=c=null,C=0,M=a.length;C<M;C++)null==v[w=t[n=a[C]]]&&(v[w]=++b),r[n]={old:w,new:v[w]},o=v[w]-n,null!=m&&o===c||(m&&h.push(m),P.push(n),c=o),m=n;for(m&&h.push(m),h.push(65535),P.push(65535),S=2*(L=P.length),l=Math.log((A=2*Math.pow(Math.log(L)/Math.LN2,2))/2)/Math.LN2,N=2*L-A,s=[],x=[],f=[],d=j=0,q=P.length;j<q;d=++j){if(_=P[d],u=h[d],65535===_){s.push(0),x.push(0);break}if(_-(k=r[_].new)>=32768)for(s.push(0),x.push(2*(f.length+L-d)),n=O=_;_<=u?O<=u:O>=u;n=_<=u?++O:--O)f.push(r[n].new);else s.push(k-_),x.push(0)}for(F.writeUInt16(3),F.writeUInt16(1),F.writeUInt32(12),F.writeUInt16(4),F.writeUInt16(16+8*L+2*f.length),F.writeUInt16(0),F.writeUInt16(S),F.writeUInt16(A),F.writeUInt16(l),F.writeUInt16(N),U=0,B=h.length;U<B;U++)n=h[U],F.writeUInt16(n);for(F.writeUInt16(0),H=0,D=P.length;H<D;H++)n=P[H],F.writeUInt16(n);for(V=0,R=s.length;V<R;V++)o=s[V],F.writeUInt16(o);for(G=0,T=x.length;G<T;G++)y=x[G],F.writeUInt16(y);for(Y=0,z=f.length;Y<z;Y++)p=f[Y],F.writeUInt16(p);return{charMap:r,subtable:F.data,maxGlyphID:b+1}}},t}(),t6=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return t0(e,tQ),e.prototype.tag="cmap",e.prototype.parse=function(t){var e,r,n;for(t.pos=this.offset,this.version=t.readUInt16(),n=t.readUInt16(),this.tables=[],this.unicode=null,r=0;0<=n?r<n:r>n;r=0<=n?++r:--r)e=new t4(t,this.offset),this.tables.push(e),e.isUnicode&&null==this.unicode&&(this.unicode=e);return!0},e.encode=function(t,e){var r,n;return null==e&&(e="macroman"),r=t4.encode(t,e),(n=new t1).writeUInt16(0),n.writeUInt16(1),r.table=n.data.concat(r.subtable),r},e}(),t8=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return t0(e,tQ),e.prototype.tag="hhea",e.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.ascender=t.readShort(),this.decender=t.readShort(),this.lineGap=t.readShort(),this.advanceWidthMax=t.readShort(),this.minLeftSideBearing=t.readShort(),this.minRightSideBearing=t.readShort(),this.xMaxExtent=t.readShort(),this.caretSlopeRise=t.readShort(),this.caretSlopeRun=t.readShort(),this.caretOffset=t.readShort(),t.pos+=8,this.metricDataFormat=t.readShort(),this.numberOfMetrics=t.readUInt16()},e}(),t7=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return t0(e,tQ),e.prototype.tag="OS/2",e.prototype.parse=function(t){if(t.pos=this.offset,this.version=t.readUInt16(),this.averageCharWidth=t.readShort(),this.weightClass=t.readUInt16(),this.widthClass=t.readUInt16(),this.type=t.readShort(),this.ySubscriptXSize=t.readShort(),this.ySubscriptYSize=t.readShort(),this.ySubscriptXOffset=t.readShort(),this.ySubscriptYOffset=t.readShort(),this.ySuperscriptXSize=t.readShort(),this.ySuperscriptYSize=t.readShort(),this.ySuperscriptXOffset=t.readShort(),this.ySuperscriptYOffset=t.readShort(),this.yStrikeoutSize=t.readShort(),this.yStrikeoutPosition=t.readShort(),this.familyClass=t.readShort(),this.panose=function(){var e,r;for(r=[],e=0;e<10;++e)r.push(t.readByte());return r}(),this.charRange=function(){var e,r;for(r=[],e=0;e<4;++e)r.push(t.readInt());return r}(),this.vendorID=t.readString(4),this.selection=t.readShort(),this.firstCharIndex=t.readShort(),this.lastCharIndex=t.readShort(),this.version>0&&(this.ascent=t.readShort(),this.descent=t.readShort(),this.lineGap=t.readShort(),this.winAscent=t.readShort(),this.winDescent=t.readShort(),this.codePageRange=function(){var e,r;for(r=[],e=0;e<2;e=++e)r.push(t.readInt());return r}(),this.version>1))return this.xHeight=t.readShort(),this.capHeight=t.readShort(),this.defaultChar=t.readShort(),this.breakChar=t.readShort(),this.maxContext=t.readShort()},e}(),t9=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return t0(e,tQ),e.prototype.tag="post",e.prototype.parse=function(t){var e,r,n,i;switch(t.pos=this.offset,this.format=t.readInt(),this.italicAngle=t.readInt(),this.underlinePosition=t.readShort(),this.underlineThickness=t.readShort(),this.isFixedPitch=t.readInt(),this.minMemType42=t.readInt(),this.maxMemType42=t.readInt(),this.minMemType1=t.readInt(),this.maxMemType1=t.readInt(),this.format){case 65536:case 196608:break;case 131072:for(r=t.readUInt16(),this.glyphNameIndex=[],i=0;0<=r?i<r:i>r;i=0<=r?++i:--i)this.glyphNameIndex.push(t.readUInt16());for(this.names=[],n=[];t.pos<this.offset+this.length;)e=t.readByte(),n.push(this.names.push(t.readString(e)));return n;case 151552:return r=t.readUInt16(),this.offsets=t.read(r);case 262144:return this.map=(function(){var e,r,n;for(n=[],i=e=0,r=this.file.maxp.numGlyphs;0<=r?e<r:e>r;i=0<=r?++e:--e)n.push(t.readUInt32());return n}).call(this)}},e}(),et=function(t,e){this.raw=t,this.length=t.length,this.platformID=e.platformID,this.encodingID=e.encodingID,this.languageID=e.languageID},ee=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return t0(e,tQ),e.prototype.tag="name",e.prototype.parse=function(t){var e,r,n,i,a,o,s,c,u,h;for(t.pos=this.offset,t.readShort(),e=t.readShort(),o=t.readShort(),r=[],i=0;0<=e?i<e:i>e;i=0<=e?++i:--i)r.push({platformID:t.readShort(),encodingID:t.readShort(),languageID:t.readShort(),nameID:t.readShort(),length:t.readShort(),offset:this.offset+o+t.readShort()});for(s={},i=c=0,u=r.length;c<u;i=++c)t.pos=(n=r[i]).offset,a=new et(t.readString(n.length),n),null==s[h=n.nameID]&&(s[h]=[]),s[n.nameID].push(a);this.strings=s,this.copyright=s[0],this.fontFamily=s[1],this.fontSubfamily=s[2],this.uniqueSubfamily=s[3],this.fontName=s[4],this.version=s[5];try{this.postscriptName=s[6][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}catch(t){this.postscriptName=s[4][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}return this.trademark=s[7],this.manufacturer=s[8],this.designer=s[9],this.description=s[10],this.vendorUrl=s[11],this.designerUrl=s[12],this.license=s[13],this.licenseUrl=s[14],this.preferredFamily=s[15],this.preferredSubfamily=s[17],this.compatibleFull=s[18],this.sampleText=s[19]},e}(),er=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return t0(e,tQ),e.prototype.tag="maxp",e.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.numGlyphs=t.readUInt16(),this.maxPoints=t.readUInt16(),this.maxContours=t.readUInt16(),this.maxCompositePoints=t.readUInt16(),this.maxComponentContours=t.readUInt16(),this.maxZones=t.readUInt16(),this.maxTwilightPoints=t.readUInt16(),this.maxStorage=t.readUInt16(),this.maxFunctionDefs=t.readUInt16(),this.maxInstructionDefs=t.readUInt16(),this.maxStackElements=t.readUInt16(),this.maxSizeOfInstructions=t.readUInt16(),this.maxComponentElements=t.readUInt16(),this.maxComponentDepth=t.readUInt16()},e}(),en=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return t0(e,tQ),e.prototype.tag="hmtx",e.prototype.parse=function(t){var e,r,n,i,a,o,s;for(t.pos=this.offset,this.metrics=[],e=0,o=this.file.hhea.numberOfMetrics;0<=o?e<o:e>o;e=0<=o?++e:--e)this.metrics.push({advance:t.readUInt16(),lsb:t.readInt16()});for(n=this.file.maxp.numGlyphs-this.file.hhea.numberOfMetrics,this.leftSideBearings=function(){var r,i;for(i=[],e=r=0;0<=n?r<n:r>n;e=0<=n?++r:--r)i.push(t.readInt16());return i}(),this.widths=(function(){var t,e,r,n;for(n=[],t=0,e=(r=this.metrics).length;t<e;t++)i=r[t],n.push(i.advance);return n}).call(this),r=this.widths[this.widths.length-1],s=[],e=a=0;0<=n?a<n:a>n;e=0<=n?++a:--a)s.push(this.widths.push(r));return s},e.prototype.forGlyph=function(t){return t in this.metrics?this.metrics[t]:{advance:this.metrics[this.metrics.length-1].advance,lsb:this.leftSideBearings[t-this.metrics.length]}},e}(),ei=[].slice,ea=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return t0(e,tQ),e.prototype.tag="glyf",e.prototype.parse=function(){return this.cache={}},e.prototype.glyphFor=function(t){var e,r,n,i,a,o,s,c,u,h;return t in this.cache?this.cache[t]:(i=this.file.loca,e=this.file.contents,r=i.indexOf(t),0===(n=i.lengthOf(t))?this.cache[t]=null:(e.pos=this.offset+r,a=(o=new t1(e.read(n))).readShort(),c=o.readShort(),h=o.readShort(),s=o.readShort(),u=o.readShort(),this.cache[t]=-1===a?new es(o,c,h,s,u):new eo(o,a,c,h,s,u),this.cache[t]))},e.prototype.encode=function(t,e,r){var n,i,a,o,s;for(a=[],i=[],o=0,s=e.length;o<s;o++)n=t[e[o]],i.push(a.length),n&&(a=a.concat(n.encode(r)));return i.push(a.length),{table:a,offsets:i}},e}(),eo=function(){function t(t,e,r,n,i,a){this.raw=t,this.numberOfContours=e,this.xMin=r,this.yMin=n,this.xMax=i,this.yMax=a,this.compound=!1}return t.prototype.encode=function(){return this.raw.data},t}(),es=function(){function t(t,e,r,n,i){var a,o;for(this.raw=t,this.xMin=e,this.yMin=r,this.xMax=n,this.yMax=i,this.compound=!0,this.glyphIDs=[],this.glyphOffsets=[],a=this.raw;o=a.readShort(),this.glyphOffsets.push(a.pos),this.glyphIDs.push(a.readUInt16()),32&o;)a.pos+=1&o?4:2,128&o?a.pos+=8:64&o?a.pos+=4:8&o&&(a.pos+=2)}return t.prototype.encode=function(){var t,e,r;for(e=new t1(ei.call(this.raw.data)),t=0,r=this.glyphIDs.length;t<r;++t)e.pos=this.glyphOffsets[t];return e.data},t}(),ec=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return t0(e,tQ),e.prototype.tag="loca",e.prototype.parse=function(t){var e,r;return t.pos=this.offset,e=this.file.head.indexToLocFormat,this.offsets=0===e?(function(){var e,n;for(n=[],r=0,e=this.length;r<e;r+=2)n.push(2*t.readUInt16());return n}).call(this):(function(){var e,n;for(n=[],r=0,e=this.length;r<e;r+=4)n.push(t.readUInt32());return n}).call(this)},e.prototype.indexOf=function(t){return this.offsets[t]},e.prototype.lengthOf=function(t){return this.offsets[t+1]-this.offsets[t]},e.prototype.encode=function(t,e){for(var r=new Uint32Array(this.offsets.length),n=0,i=0,a=0;a<r.length;++a)if(r[a]=n,i<e.length&&e[i]==a){++i,r[a]=n;var o=this.offsets[a],s=this.offsets[a+1]-o;s>0&&(n+=s)}for(var c=Array(4*r.length),u=0;u<r.length;++u)c[4*u+3]=255&r[u],c[4*u+2]=(65280&r[u])>>8,c[4*u+1]=(0xff0000&r[u])>>16,c[4*u]=(0xff000000&r[u])>>24;return c},e}(),eu=function(){function t(t){this.font=t,this.subset={},this.unicodes={},this.next=33}return t.prototype.generateCmap=function(){var t,e,r,n,i;for(e in n=this.font.cmap.tables[0].codeMap,t={},i=this.subset)r=i[e],t[e]=n[r];return t},t.prototype.glyphsFor=function(t){var e,r,n,i,a,o,s;for(n={},a=0,o=t.length;a<o;a++)n[i=t[a]]=this.font.glyf.glyphFor(i);for(i in e=[],n)(null!=(r=n[i])?r.compound:void 0)&&e.push.apply(e,r.glyphIDs);if(e.length>0)for(i in s=this.glyphsFor(e))r=s[i],n[i]=r;return n},t.prototype.encode=function(t,e){var r,n,i,a,o,s,c,u,h,l,f,d,p,g,m;for(n in r=t6.encode(this.generateCmap(),"unicode"),a=this.glyphsFor(t),f={0:0},m=r.charMap)f[(s=m[n]).old]=s.new;for(d in l=r.maxGlyphID,a)d in f||(f[d]=l++);return h=Object.keys(u=function(t){var e,r;for(e in r={},t)r[t[e]]=e;return r}(f)).sort(function(t,e){return t-e}),p=function(){var t,e,r;for(r=[],t=0,e=h.length;t<e;t++)o=h[t],r.push(u[o]);return r}(),i=this.font.glyf.encode(a,p,f),c=this.font.loca.encode(i.offsets,p),g={cmap:this.font.cmap.raw(),glyf:i.table,loca:c,hmtx:this.font.hmtx.raw(),hhea:this.font.hhea.raw(),maxp:this.font.maxp.raw(),post:this.font.post.raw(),name:this.font.name.raw(),head:this.font.head.encode(e)},this.font.os2.exists&&(g["OS/2"]=this.font.os2.raw()),this.font.directory.encode(g)},t}();q.API.PDFObject=function(){var t;function e(){}return t=function(t,e){return(Array(e+1).join("0")+t).slice(-e)},e.convert=function(r){var n,i,a,o;if(Array.isArray(r))return"["+(function(){var t,i,a;for(a=[],t=0,i=r.length;t<i;t++)n=r[t],a.push(e.convert(n));return a})().join(" ")+"]";if("string"==typeof r)return"/"+r;if(null!=r?r.isString:void 0)return"("+r+")";if(r instanceof Date)return"(D:"+t(r.getUTCFullYear(),4)+t(r.getUTCMonth(),2)+t(r.getUTCDate(),2)+t(r.getUTCHours(),2)+t(r.getUTCMinutes(),2)+t(r.getUTCSeconds(),2)+"Z)";if("[object Object]"===({}).toString.call(r)){for(i in a=["<<"],r)o=r[i],a.push("/"+i+" "+e.convert(o));return a.push(">>"),a.join("\n")}return""+r},e}()}}]);