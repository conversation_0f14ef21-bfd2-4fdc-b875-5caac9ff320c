import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Schema للتحقق من صحة البيانات
const updateActivitySchema = z.object({
  title: z.string().min(1, 'عنوان النشاط مطلوب').optional(),
  description: z.string().optional(),
  location: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
})

// GET - جلب نشاط محدد
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    const { id } = await params
    const activity = await prisma.activity.findUnique({
      where: { id },
      include: {
        createdBy: {
          select: {
            name: true,
          },
        },
        _count: {
          select: {
            participants: true,
            photos: true,
          },
        },
      },
    })

    if (!activity) {
      return NextResponse.json({ error: 'النشاط غير موجود' }, { status: 404 })
    }

    return NextResponse.json(activity)
  } catch (error) {
    console.error('خطأ في جلب النشاط:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في جلب النشاط' },
      { status: 500 }
    )
  }
}

// PUT - تحديث نشاط
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    const { id } = await params
    const body = await request.json()

    // التحقق من وجود النشاط
    const existingActivity = await prisma.activity.findUnique({
      where: { id },
    })

    if (!existingActivity) {
      return NextResponse.json({ error: 'النشاط غير موجود' }, { status: 404 })
    }

    // التحقق من الصلاحيات (المالك أو الأدمن)
    if (existingActivity.createdById !== session.user.id && session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'غير مصرح لك بتعديل هذا النشاط' }, { status: 403 })
    }

    // التحقق من صحة البيانات
    const validatedData = updateActivitySchema.parse(body)

    // تحضير البيانات للتحديث
    const updateData: Record<string, unknown> = {}
    
    if (validatedData.title) updateData.title = validatedData.title
    if (validatedData.description !== undefined) updateData.description = validatedData.description
    if (validatedData.location !== undefined) updateData.location = validatedData.location
    
    if (validatedData.startDate) {
      updateData.startDate = new Date(validatedData.startDate)
    }
    
    if (validatedData.endDate) {
      updateData.endDate = new Date(validatedData.endDate)
    } else if (validatedData.startDate) {
      updateData.endDate = new Date(validatedData.startDate)
    }

    // التحقق من صحة التواريخ
    if (updateData.startDate && updateData.endDate && updateData.endDate < updateData.startDate) {
      return NextResponse.json(
        { error: 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية' },
        { status: 400 }
      )
    }

    // تحديث النشاط
    const updatedActivity = await prisma.activity.update({
      where: { id },
      data: updateData,
      include: {
        createdBy: {
          select: {
            name: true,
          },
        },
        _count: {
          select: {
            participants: true,
            photos: true,
          },
        },
      },
    })

    return NextResponse.json(updatedActivity)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'بيانات غير صحيحة', details: error.errors },
        { status: 400 }
      )
    }

    console.error('خطأ في تحديث النشاط:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في تحديث النشاط' },
      { status: 500 }
    )
  }
}

// DELETE - حذف نشاط
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    const { id } = await params

    // التحقق من وجود النشاط
    const existingActivity = await prisma.activity.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            photos: true,
            participants: true,
          },
        },
      },
    })

    if (!existingActivity) {
      return NextResponse.json({ error: 'النشاط غير موجود' }, { status: 404 })
    }

    // التحقق من الصلاحيات (المالك أو الأدمن)
    if (existingActivity.createdById !== session.user.id && session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'غير مصرح لك بحذف هذا النشاط' }, { status: 403 })
    }

    // التحقق من وجود صور مرتبطة
    if (existingActivity._count.photos > 0) {
      return NextResponse.json(
        { 
          error: `لا يمكن حذف النشاط لأنه يحتوي على ${existingActivity._count.photos} صورة. يرجى حذف الصور أولاً أو نقلها إلى نشاط آخر.` 
        },
        { status: 400 }
      )
    }

    // حذف النشاط
    await prisma.activity.delete({
      where: { id },
    })

    return NextResponse.json({ success: true, message: 'تم حذف النشاط بنجاح' })
  } catch (error) {
    console.error('خطأ في حذف النشاط:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في حذف النشاط' },
      { status: 500 }
    )
  }
}
