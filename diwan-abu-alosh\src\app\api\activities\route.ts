import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Schema للتحقق من صحة البيانات
const activitySchema = z.object({
  title: z.string().min(1, 'عنوان النشاط مطلوب'),
  description: z.string().optional(),
  location: z.string().optional(),
  startDate: z.string().min(1, 'تاريخ البداية مطلوب'),
  endDate: z.string().optional(),
})

// GET - جلب جميع الأنشطة
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search') || ''
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const skip = (page - 1) * limit

    // بناء شروط البحث
    const where: Record<string, unknown> = {}
    
    if (search) {
      where.OR = [
        { title: { contains: search } },
        { description: { contains: search } },
        { location: { contains: search } },
      ]
    }

    // جلب الأنشطة مع العلاقات
    const activities = await prisma.activity.findMany({
      where,
      include: {
        createdBy: {
          select: {
            name: true,
          },
        },
        _count: {
          select: {
            participants: true,
            photos: true,
          },
        },
      },
      orderBy: { startDate: 'desc' },
      skip,
      take: limit,
    })

    // جلب العدد الإجمالي للصفحات
    const total = await prisma.activity.count({ where })

    return NextResponse.json({
      activities,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('خطأ في جلب الأنشطة:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في جلب الأنشطة' },
      { status: 500 }
    )
  }
}

// POST - إضافة نشاط جديد
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // التحقق من الصلاحيات
    if (session.user.role === 'VIEWER') {
      return NextResponse.json({ error: 'غير مصرح لك بإنشاء الأنشطة' }, { status: 403 })
    }

    const body = await request.json()
    
    // التحقق من صحة البيانات
    const validatedData = activitySchema.parse(body)

    // تحويل التواريخ
    const startDate = new Date(validatedData.startDate)
    const endDate = validatedData.endDate ? new Date(validatedData.endDate) : startDate

    // التحقق من صحة التواريخ
    if (endDate < startDate) {
      return NextResponse.json(
        { error: 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية' },
        { status: 400 }
      )
    }

    // إنشاء النشاط في قاعدة البيانات
    const activity = await prisma.activity.create({
      data: {
        title: validatedData.title,
        description: validatedData.description,
        location: validatedData.location,
        startDate,
        endDate,
        createdById: session.user.id,
      },
      include: {
        createdBy: {
          select: {
            name: true,
          },
        },
        _count: {
          select: {
            participants: true,
            photos: true,
          },
        },
      },
    })

    return NextResponse.json(activity, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'بيانات غير صحيحة', details: error.errors },
        { status: 400 }
      )
    }

    console.error('خطأ في إضافة النشاط:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في إضافة النشاط' },
      { status: 500 }
    )
  }
}
