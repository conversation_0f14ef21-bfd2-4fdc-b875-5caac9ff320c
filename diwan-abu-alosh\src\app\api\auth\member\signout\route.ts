import { NextResponse } from 'next/server'

export async function POST() {
  try {
    // إنشاء response
    const response = NextResponse.json({
      success: true,
      message: 'تم تسجيل الخروج بنجاح'
    })

    // حذف cookie
    response.cookies.set('member-token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 0 // حذف فوري
    })

    return response
  } catch (error) {
    console.error('خطأ في تسجيل خروج العضو:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في تسجيل الخروج' },
      { status: 500 }
    )
  }
}
