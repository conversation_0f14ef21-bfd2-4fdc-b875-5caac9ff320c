import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import jwt from 'jsonwebtoken'

// API لجلب كشف حساب العضو - النظام الجديد المحدث

export async function GET(request: NextRequest) {
  try {
    // التحقق من المصادقة
    const token = request.cookies.get('member-token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'غير مصرح - يجب تسجيل الدخول' },
        { status: 401 }
      )
    }

    let decoded: { userId: string }
    try {
      decoded = jwt.verify(token, process.env.NEXTAUTH_SECRET || 'fallback-secret')
    } catch {
      return NextResponse.json(
        { error: 'رمز المصادقة غير صالح' },
        { status: 401 }
      )
    }

    // التحقق من وجود userId في التوكن
    if (!decoded.userId) {
      return NextResponse.json(
        { error: 'جلسة غير صالحة - معرف المستخدم مفقود' },
        { status: 401 }
      )
    }

    // جلب بيانات المستخدم والعضو
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      include: {
        memberUser: {
          include: {
            member: true
          }
        }
      }
    })

    if (!user || !user.memberUser) {
      return NextResponse.json(
        { error: 'المستخدم غير موجود' },
        { status: 404 }
      )
    }

    // التحقق من أن الحساب مفعل
    if (!user.memberUser.isActive) {
      return NextResponse.json(
        { error: 'حسابك غير مفعل' },
        { status: 403 }
      )
    }

    // التحقق من صلاحية عرض كشف الحساب
    if (!user.memberUser.canViewAccountStatement) {
      return NextResponse.json(
        { error: 'ليس لديك صلاحية لعرض كشف الحساب' },
        { status: 403 }
      )
    }

    const member = user.memberUser.member

    // جلب بيانات العضو مع الإيرادات
    const memberData = await prisma.member.findUnique({
      where: { id: member.id },
      include: {
        incomes: {
          orderBy: { date: 'desc' },
          include: {
            createdBy: {
              select: { name: true }
            }
          }
        }
      }
    })

    if (!memberData) {
      return NextResponse.json(
        { error: 'العضو غير موجود' },
        { status: 404 }
      )
    }

    // حساب الإحصائيات
    const totalAmount = memberData.incomes.reduce((sum, income) => sum + income.amount, 0)
    const transactionCount = memberData.incomes.length

    // تجميع البيانات حسب النوع
    const byType: Record<string, { count: number; amount: number }> = {}
    memberData.incomes.forEach(income => {
      if (!byType[income.type]) {
        byType[income.type] = { count: 0, amount: 0 }
      }
      byType[income.type].count++
      byType[income.type].amount += income.amount
    })

    // تجميع البيانات الشهرية للسنة الحالية
    const currentYear = new Date().getFullYear()
    const monthlyData = Array.from({ length: 12 }, (_, index) => {
      const month = index + 1
      const monthIncomes = memberData.incomes.filter(income => {
        const incomeDate = new Date(income.date)
        return incomeDate.getFullYear() === currentYear && incomeDate.getMonth() === index
      })

      return {
        month,
        monthName: `شهر${month}`,
        count: monthIncomes.length,
        amount: monthIncomes.reduce((sum, income) => sum + income.amount, 0)
      }
    })

    const accountStatement = {
      member: {
        id: memberData.id,
        name: memberData.name,
        phone: memberData.phone,
        email: memberData.email,
        address: memberData.address,
        photo: memberData.photo,
        status: memberData.status,
        createdAt: memberData.createdAt
      },
      summary: {
        totalAmount,
        transactionCount,
        averageMonthlyContribution: transactionCount > 0 ? totalAmount / Math.max(1, monthlyData.filter(m => m.count > 0).length) : 0,
        firstTransactionDate: memberData.incomes.length > 0 ? memberData.incomes[memberData.incomes.length - 1].date : null,
        lastTransactionDate: memberData.incomes.length > 0 ? memberData.incomes[0].date : null
      },
      byType,
      monthlyData,
      recentTransactions: memberData.incomes.slice(0, 10),
      allTransactions: memberData.incomes,
      period: {
        startDate: `${currentYear}-01-01`,
        endDate: `${currentYear}-12-31`,
        year: currentYear
      }
    }

    return NextResponse.json(accountStatement)
  } catch (error) {
    console.error('خطأ في جلب كشف حساب العضو:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في جلب كشف الحساب' },
      { status: 500 }
    )
  }
}
