'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import {
  Download,
  FileText,
  TrendingUp,
  TrendingDown,
  Users,
  DollarSign,
  Activity,
  Calendar,
  BarChart3,
  PieChart,
  Target,
  Award,
  RefreshCw
} from 'lucide-react'
import { formatCurrency, formatDate } from '@/lib/utils'

interface ReportsData {
  summary: {
    totalMembers: number
    activeMembers: number
    totalIncomes: number
    totalExpenses: number
    balance: number
    totalActivities: number
  }
  topContributors: Array<{
    id: string
    name: string
    totalContributions: number
    incomes: Array<{ amount: number }>
  }>
  recentTransactions: Array<{
    type: string
    id: string
    amount: number
    date: string
    description: string
    memberName?: string
  }>
  monthlyData: Array<{
    month: string
    incomes: number
    expenses: number
    balance: number
  }>
}

export default function ReportsPage() {
  const { data: session } = useSession()
  const [data, setData] = useState<ReportsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedPeriod, setSelectedPeriod] = useState('current-year')
  const [refreshing, setRefreshing] = useState(false)

  // جلب بيانات التقارير
  const fetchReportsData = async (showRefreshing = false) => {
    try {
      if (showRefreshing) {
        setRefreshing(true)
      } else {
        setLoading(true)
      }

      const response = await fetch(`/api/reports?period=${selectedPeriod}`)
      if (!response.ok) throw new Error('فشل في جلب بيانات التقارير')

      const reportsData = await response.json()
      setData(reportsData)
    } catch (error) {
      console.error('خطأ في جلب بيانات التقارير:', error)
      alert('حدث خطأ في جلب بيانات التقارير')
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  // تحديث البيانات
  const handleRefresh = () => {
    fetchReportsData(true)
  }

  useEffect(() => {
    if (session) {
      fetchReportsData()
    }
  }, [selectedPeriod, session])

  // تصدير التقرير الشامل بصيغة PDF
  const handleExportPDF = async () => {
    if (!data) return

    try {
      // إنشاء محتوى HTML للطباعة
      const periodNames: { [key: string]: string } = {
        'current-month': 'الشهر الحالي',
        'last-month': 'الشهر الماضي',
        'current-year': 'السنة الحالية',
        'last-year': 'السنة الماضية',
        'all-time': 'جميع الفترات'
      }

      const currentDate = new Date().toLocaleDateString('ar-JO', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })

      const htmlContent = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>تقرير شامل - ديوان أبو علوش</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: white;
            padding: 20px;
            direction: rtl;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 20px;
        }

        .header h1 {
            color: #2563eb;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header .subtitle {
            color: #666;
            font-size: 16px;
            margin-bottom: 5px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }

        .summary-card h3 {
            color: #475569;
            font-size: 14px;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .summary-card .value {
            font-size: 20px;
            font-weight: 700;
            color: #1e293b;
        }

        .section {
            margin-bottom: 30px;
            page-break-inside: avoid;
        }

        .section h2 {
            color: #2563eb;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 5px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .table th,
        .table td {
            padding: 8px 12px;
            text-align: right;
            border-bottom: 1px solid #e2e8f0;
            font-size: 12px;
        }

        .table th {
            background: #f1f5f9;
            font-weight: 600;
            color: #475569;
        }

        .income {
            color: #059669;
            font-weight: 600;
        }

        .expense {
            color: #dc2626;
            font-weight: 600;
        }

        @media print {
            body { padding: 0; }
            .section { page-break-inside: avoid; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>تقرير شامل - ديوان أبو علوش</h1>
        <div class="subtitle">فترة التقرير: ${periodNames[selectedPeriod] || selectedPeriod}</div>
        <div class="subtitle">تاريخ الإنشاء: ${currentDate}</div>
    </div>

    <div class="summary-grid">
        <div class="summary-card">
            <h3>إجمالي الأعضاء</h3>
            <div class="value">${data.summary.totalMembers}</div>
        </div>
        <div class="summary-card">
            <h3>الأعضاء النشطون</h3>
            <div class="value">${data.summary.activeMembers}</div>
        </div>
        <div class="summary-card">
            <h3>إجمالي الإيرادات</h3>
            <div class="value income">${formatCurrency(data.summary.totalIncomes)}</div>
        </div>
        <div class="summary-card">
            <h3>إجمالي المصروفات</h3>
            <div class="value expense">${formatCurrency(data.summary.totalExpenses)}</div>
        </div>
        <div class="summary-card">
            <h3>الرصيد الحالي</h3>
            <div class="value ${data.summary.balance >= 0 ? 'income' : 'expense'}">${formatCurrency(data.summary.balance)}</div>
        </div>
        <div class="summary-card">
            <h3>إجمالي الأنشطة</h3>
            <div class="value">${data.summary.totalActivities}</div>
        </div>
    </div>

    <div class="section">
        <h2>أكثر الأعضاء مساهمة</h2>
        <table class="table">
            <thead>
                <tr>
                    <th>الترتيب</th>
                    <th>اسم العضو</th>
                    <th>عدد المساهمات</th>
                    <th>إجمالي المساهمات</th>
                </tr>
            </thead>
            <tbody>
                ${data.topContributors.slice(0, 10).map((member, index) => `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${member.name}</td>
                        <td>${member.incomes.length}</td>
                        <td class="income">${formatCurrency(member.totalContributions)}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    </div>

    <div class="section">
        <h2>آخر المعاملات</h2>
        <table class="table">
            <thead>
                <tr>
                    <th>النوع</th>
                    <th>الوصف</th>
                    <th>المبلغ</th>
                    <th>التاريخ</th>
                    <th>العضو/المستفيد</th>
                </tr>
            </thead>
            <tbody>
                ${data.recentTransactions.slice(0, 15).map((transaction) => `
                    <tr>
                        <td>${transaction.type === 'income' ? 'إيراد' : 'مصروف'}</td>
                        <td>${transaction.description}</td>
                        <td class="${transaction.type === 'income' ? 'income' : 'expense'}">
                            ${formatCurrency(transaction.amount)}
                        </td>
                        <td>${formatDate(transaction.date)}</td>
                        <td>${transaction.memberName || 'غير محدد'}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    </div>
</body>
</html>`

      // إنشاء نافذة جديدة للطباعة
      const printWindow = window.open('', '_blank')
      if (printWindow) {
        printWindow.document.write(htmlContent)
        printWindow.document.close()

        // انتظار تحميل الخطوط ثم الطباعة
        setTimeout(() => {
          printWindow.print()
        }, 1000)
      } else {
        alert('يرجى السماح بفتح النوافذ المنبثقة لتصدير التقرير')
      }

    } catch (error) {
      console.error('خطأ في تصدير التقرير:', error)
      alert('حدث خطأ في تصدير التقرير')
    }
  }

  // تصدير PDF للتحميل المباشر
  const handleExportPDFDownload = async () => {
    if (!data) return

    try {
      // استيراد مكتبة html2pdf
      const html2pdf = (await import('html2pdf.js')).default as { set: (opt: unknown) => { from: (element: HTMLElement) => { save: () => Promise<void> } } }

      const periodNames: { [key: string]: string } = {
        'current-month': 'الشهر الحالي',
        'last-month': 'الشهر الماضي',
        'current-year': 'السنة الحالية',
        'last-year': 'السنة الماضية',
        'all-time': 'جميع الفترات'
      }

      const currentDate = new Date().toLocaleDateString('ar-JO', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })

      // إنشاء عنصر HTML مؤقت
      const element = document.createElement('div')
      element.innerHTML = `
        <div style="font-family: 'Cairo', Arial, sans-serif; direction: rtl; padding: 20px; color: #333;">
          <div style="text-align: center; margin-bottom: 30px; border-bottom: 3px solid #2563eb; padding-bottom: 20px;">
            <h1 style="color: #2563eb; font-size: 24px; font-weight: 700; margin-bottom: 10px;">
              تقرير شامل - ديوان أبو علوش
            </h1>
            <div style="color: #666; font-size: 14px; margin-bottom: 5px;">
              فترة التقرير: ${periodNames[selectedPeriod] || selectedPeriod}
            </div>
            <div style="color: #666; font-size: 14px;">
              تاريخ الإنشاء: ${currentDate}
            </div>
          </div>

          <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin-bottom: 30px;">
            <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; text-align: center;">
              <h3 style="color: #475569; font-size: 12px; margin-bottom: 8px; font-weight: 600;">إجمالي الأعضاء</h3>
              <div style="font-size: 18px; font-weight: 700; color: #1e293b;">${data.summary.totalMembers}</div>
            </div>
            <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; text-align: center;">
              <h3 style="color: #475569; font-size: 12px; margin-bottom: 8px; font-weight: 600;">الأعضاء النشطون</h3>
              <div style="font-size: 18px; font-weight: 700; color: #1e293b;">${data.summary.activeMembers}</div>
            </div>
            <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; text-align: center;">
              <h3 style="color: #475569; font-size: 12px; margin-bottom: 8px; font-weight: 600;">إجمالي الإيرادات</h3>
              <div style="font-size: 18px; font-weight: 700; color: #059669;">${formatCurrency(data.summary.totalIncomes)}</div>
            </div>
            <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; text-align: center;">
              <h3 style="color: #475569; font-size: 12px; margin-bottom: 8px; font-weight: 600;">إجمالي المصروفات</h3>
              <div style="font-size: 18px; font-weight: 700; color: #dc2626;">${formatCurrency(data.summary.totalExpenses)}</div>
            </div>
            <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; text-align: center;">
              <h3 style="color: #475569; font-size: 12px; margin-bottom: 8px; font-weight: 600;">الرصيد الحالي</h3>
              <div style="font-size: 18px; font-weight: 700; color: ${data.summary.balance >= 0 ? '#059669' : '#dc2626'};">${formatCurrency(data.summary.balance)}</div>
            </div>
            <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; text-align: center;">
              <h3 style="color: #475569; font-size: 12px; margin-bottom: 8px; font-weight: 600;">إجمالي الأنشطة</h3>
              <div style="font-size: 18px; font-weight: 700; color: #1e293b;">${data.summary.totalActivities}</div>
            </div>
          </div>

          <div style="margin-bottom: 30px;">
            <h2 style="color: #2563eb; font-size: 16px; font-weight: 600; margin-bottom: 15px; border-bottom: 2px solid #e2e8f0; padding-bottom: 5px;">
              أكثر الأعضاء مساهمة
            </h2>
            <table style="width: 100%; border-collapse: collapse;">
              <thead>
                <tr style="background: #f1f5f9;">
                  <th style="padding: 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 11px; font-weight: 600; color: #475569;">الترتيب</th>
                  <th style="padding: 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 11px; font-weight: 600; color: #475569;">اسم العضو</th>
                  <th style="padding: 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 11px; font-weight: 600; color: #475569;">عدد المساهمات</th>
                  <th style="padding: 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 11px; font-weight: 600; color: #475569;">إجمالي المساهمات</th>
                </tr>
              </thead>
              <tbody>
                ${data.topContributors.slice(0, 10).map((member, index) => `
                  <tr>
                    <td style="padding: 6px 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 10px;">${index + 1}</td>
                    <td style="padding: 6px 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 10px;">${member.name}</td>
                    <td style="padding: 6px 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 10px;">${member.incomes.length}</td>
                    <td style="padding: 6px 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 10px; color: #059669; font-weight: 600;">${formatCurrency(member.totalContributions)}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>

          <div style="margin-bottom: 30px;">
            <h2 style="color: #2563eb; font-size: 16px; font-weight: 600; margin-bottom: 15px; border-bottom: 2px solid #e2e8f0; padding-bottom: 5px;">
              آخر المعاملات
            </h2>
            <table style="width: 100%; border-collapse: collapse;">
              <thead>
                <tr style="background: #f1f5f9;">
                  <th style="padding: 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 11px; font-weight: 600; color: #475569;">النوع</th>
                  <th style="padding: 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 11px; font-weight: 600; color: #475569;">الوصف</th>
                  <th style="padding: 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 11px; font-weight: 600; color: #475569;">المبلغ</th>
                  <th style="padding: 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 11px; font-weight: 600; color: #475569;">التاريخ</th>
                  <th style="padding: 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 11px; font-weight: 600; color: #475569;">العضو/المستفيد</th>
                </tr>
              </thead>
              <tbody>
                ${data.recentTransactions.slice(0, 15).map((transaction) => `
                  <tr>
                    <td style="padding: 6px 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 10px;">${transaction.type === 'income' ? 'إيراد' : 'مصروف'}</td>
                    <td style="padding: 6px 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 10px;">${transaction.description}</td>
                    <td style="padding: 6px 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 10px; color: ${transaction.type === 'income' ? '#059669' : '#dc2626'}; font-weight: 600;">${formatCurrency(transaction.amount)}</td>
                    <td style="padding: 6px 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 10px;">${formatDate(transaction.date)}</td>
                    <td style="padding: 6px 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 10px;">${transaction.memberName || 'غير محدد'}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        </div>
      `

      // إعدادات PDF
      const opt = {
        margin: 1,
        filename: `تقرير_شامل_${selectedPeriod}_${new Date().toISOString().split('T')[0]}.pdf`,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { scale: 2, useCORS: true },
        jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }
      }

      // تحويل إلى PDF وتحميل
      await html2pdf().set(opt).from(element).save()

    } catch (error) {
      console.error('خطأ في تصدير التقرير:', error)
      alert('حدث خطأ في تصدير التقرير')
    }
  }

  // تصدير التقرير الشامل بصيغة CSV
  const handleExportCSV = () => {
    if (!data) return

    const reportContent = [
      ['نوع البيان', 'القيمة'],
      ['إجمالي الأعضاء', data.summary.totalMembers.toString()],
      ['الأعضاء النشطون', data.summary.activeMembers.toString()],
      ['إجمالي الإيرادات', formatCurrency(data.summary.totalIncomes)],
      ['إجمالي المصروفات', formatCurrency(data.summary.totalExpenses)],
      ['الرصيد الحالي', formatCurrency(data.summary.balance)],
      ['إجمالي الأنشطة', data.summary.totalActivities.toString()],
      [],
      ['أكثر الأعضاء مساهمة'],
      ['الترتيب', 'الاسم', 'إجمالي المساهمات'],
      ...data.topContributors.slice(0, 10).map((member, index) => [
        (index + 1).toString(),
        member.name,
        formatCurrency(member.totalContributions)
      ]),
      [],
      ['آخر المعاملات'],
      ['النوع', 'الوصف', 'المبلغ', 'التاريخ', 'العضو/المستفيد'],
      ...data.recentTransactions.slice(0, 20).map(transaction => [
        transaction.type === 'income' ? 'إيراد' : 'مصروف',
        transaction.description,
        formatCurrency(transaction.amount),
        formatDate(transaction.date),
        transaction.memberName || 'غير محدد'
      ])
    ].map(row => Array.isArray(row) ? row.join(',') : row).join('\n')

    const blob = new Blob(['\ufeff' + reportContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `تقرير_شامل_${selectedPeriod}_${new Date().toISOString().split('T')[0]}.csv`
    link.click()
  }

  // تصدير البيانات الشهرية
  const handleExportMonthlyData = () => {
    if (!data || !data.monthlyData) return

    const monthlyContent = [
      ['الشهر', 'الإيرادات', 'المصروفات', 'الرصيد'],
      ...data.monthlyData.map(month => [
        new Date(month.month + '-01').toLocaleDateString('ar-JO', {
          year: 'numeric',
          month: 'long'
        }),
        formatCurrency(month.incomes),
        formatCurrency(month.expenses),
        formatCurrency(month.balance)
      ])
    ].map(row => row.join(',')).join('\n')

    const blob = new Blob(['\ufeff' + monthlyContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `البيانات_الشهرية_${selectedPeriod}_${new Date().toISOString().split('T')[0]}.csv`
    link.click()
  }

  if (loading) {
    return (
      <div className="space-y-6 arabic-text">
        <div>
          <h1 className="text-2xl font-bold text-gray-900" style={{ fontFamily: 'Cairo, Almarai, sans-serif' }}>
            التقارير والإحصائيات
          </h1>
          <p className="text-gray-600" style={{ fontFamily: 'Cairo, Almarai, sans-serif' }}>
            تقارير شاملة عن أنشطة الديوان المالية والإدارية
          </p>
        </div>
        <div className="flex justify-center items-center h-64">
          <div className="text-gray-500" style={{ fontFamily: 'Cairo, Almarai, sans-serif' }}>
            جاري تحميل التقارير...
          </div>
        </div>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="space-y-6 arabic-text">
        <div>
          <h1 className="text-2xl font-bold text-gray-900" style={{ fontFamily: 'Cairo, Almarai, sans-serif' }}>
            التقارير والإحصائيات
          </h1>
          <p className="text-gray-600" style={{ fontFamily: 'Cairo, Almarai, sans-serif' }}>
            تقارير شاملة عن أنشطة الديوان المالية والإدارية
          </p>
        </div>
        <div className="flex justify-center items-center h-64">
          <div className="text-gray-500" style={{ fontFamily: 'Cairo, Almarai, sans-serif' }}>
            لا توجد بيانات متاحة
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 arabic-text">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900" style={{ fontFamily: 'Cairo, Almarai, sans-serif' }}>
            التقارير والإحصائيات
          </h1>
          <p className="text-gray-600" style={{ fontFamily: 'Cairo, Almarai, sans-serif' }}>
            تقارير شاملة عن أنشطة الديوان المالية والإدارية
          </p>
        </div>
        <div className="flex space-x-2 space-x-reverse">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-diwan-500"
          >
            <option value="current-month">الشهر الحالي</option>
            <option value="last-month">الشهر الماضي</option>
            <option value="current-year">السنة الحالية</option>
            <option value="last-year">السنة الماضية</option>
            <option value="all-time">جميع الفترات</option>
          </select>
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={refreshing}
            className="text-gray-600 border-gray-300 hover:bg-gray-50"
          >
            <RefreshCw className={`w-4 h-4 ml-2 ${refreshing ? 'animate-spin' : ''}`} />
            {refreshing ? 'جاري التحديث...' : 'تحديث'}
          </Button>
          <Button
            variant="outline"
            onClick={handleExportMonthlyData}
            className="text-blue-600 border-blue-600 hover:bg-blue-50"
          >
            <BarChart3 className="w-4 h-4 ml-2" />
            البيانات الشهرية
          </Button>
          <Button
            onClick={handleExportPDF}
            className="bg-red-600 hover:bg-red-700 text-white"
          >
            <FileText className="w-4 h-4 ml-2" />
            طباعة PDF
          </Button>
          <Button
            onClick={handleExportPDFDownload}
            className="bg-purple-600 hover:bg-purple-700 text-white"
          >
            <Download className="w-4 h-4 ml-2" />
            تحميل PDF
          </Button>
          <Button
            onClick={handleExportCSV}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            <Download className="w-4 h-4 ml-2" />
            تصدير CSV
          </Button>
        </div>
      </div>

      {/* الملخص المالي */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الأعضاء</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {data.summary.totalMembers}
            </div>
            <p className="text-xs text-muted-foreground">
              {data.summary.activeMembers} عضو نشط
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الإيرادات</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(data.summary.totalIncomes)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي المصروفات</CardTitle>
            <TrendingDown className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {formatCurrency(data.summary.totalExpenses)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الرصيد الحالي</CardTitle>
            <DollarSign className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${
              data.summary.balance >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {formatCurrency(data.summary.balance)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الأنشطة</CardTitle>
            <Activity className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {data.summary.totalActivities}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* التقارير التفصيلية */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
          <TabsTrigger value="contributors">أكثر المساهمين</TabsTrigger>
          <TabsTrigger value="transactions">المعاملات</TabsTrigger>
          <TabsTrigger value="analytics">التحليلات</TabsTrigger>
          <TabsTrigger value="insights">رؤى ذكية</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            {/* أكثر الأعضاء مساهمة */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Award className="w-5 h-5 ml-2" />
                  أكثر الأعضاء مساهمة
                </CardTitle>
              </CardHeader>
              <CardContent>
                {data.topContributors.length === 0 ? (
                  <p className="text-gray-500 text-center py-4">لا توجد مساهمات</p>
                ) : (
                  <div className="space-y-3">
                    {data.topContributors.slice(0, 10).map((member, index) => (
                      <div key={member.id} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white ${
                            index === 0 ? 'bg-yellow-500' :
                            index === 1 ? 'bg-gray-400' :
                            index === 2 ? 'bg-orange-600' :
                            'bg-gray-300'
                          }`}>
                            {index + 1}
                          </div>
                          <div className="mr-3">
                            <div className="text-sm font-medium text-gray-900">
                              {member.name}
                            </div>
                            <div className="text-xs text-gray-500">
                              {member.incomes.length} إيراد
                            </div>
                          </div>
                        </div>
                        <div className="text-sm font-medium text-green-600">
                          {formatCurrency(member.totalContributions)}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* آخر المعاملات */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="w-5 h-5 ml-2" />
                  آخر المعاملات
                </CardTitle>
              </CardHeader>
              <CardContent>
                {data.recentTransactions.length === 0 ? (
                  <p className="text-gray-500 text-center py-4">لا توجد معاملات</p>
                ) : (
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {data.recentTransactions.slice(0, 15).map((transaction, index) => (
                      <div key={`${transaction.type}-${transaction.id}-${index}`} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className={`w-3 h-3 rounded-full ${
                            transaction.type === 'income' ? 'bg-green-500' : 'bg-red-500'
                          }`}></div>
                          <div className="mr-3">
                            <div className="text-sm font-medium text-gray-900">
                              {transaction.description}
                            </div>
                            <div className="text-xs text-gray-500">
                              {transaction.memberName || 'غير محدد'} • {formatDate(transaction.date)}
                            </div>
                          </div>
                        </div>
                        <div className={`text-sm font-medium ${
                          transaction.type === 'income' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {transaction.type === 'income' ? '+' : '-'}{formatCurrency(transaction.amount)}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="contributors" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>تفاصيل المساهمين</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.topContributors.map((member, index) => (
                  <div key={member.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center">
                      <Badge variant={index < 3 ? 'success' : 'secondary'} className="ml-3">
                        #{index + 1}
                      </Badge>
                      <div>
                        <div className="font-medium">{member.name}</div>
                        <div className="text-sm text-gray-500">
                          {member.incomes.length} مساهمة
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium text-green-600">
                        {formatCurrency(member.totalContributions)}
                      </div>
                      <div className="text-sm text-gray-500">
                        متوسط: {formatCurrency(member.totalContributions / member.incomes.length)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="transactions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>جميع المعاملات الحديثة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {data.recentTransactions.map((transaction, index) => (
                  <div key={`${transaction.type}-${transaction.id}-${index}`}
                       className={`p-4 rounded-lg border-l-4 ${
                         transaction.type === 'income'
                           ? 'border-green-500 bg-green-50'
                           : 'border-red-500 bg-red-50'
                       }`}>
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="font-medium">{transaction.description}</div>
                        <div className="text-sm text-gray-600 mt-1">
                          <Calendar className="w-3 h-3 inline ml-1" />
                          {formatDate(transaction.date)}
                        </div>
                        {transaction.memberName && (
                          <div className="text-sm text-gray-600">
                            <Users className="w-3 h-3 inline ml-1" />
                            {transaction.memberName}
                          </div>
                        )}
                      </div>
                      <div className={`text-lg font-bold ${
                        transaction.type === 'income' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {transaction.type === 'income' ? '+' : '-'}{formatCurrency(transaction.amount)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            {/* إحصائيات إضافية */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Target className="w-5 h-5 ml-2" />
                  مؤشرات الأداء الرئيسية
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div className="text-center p-4 border border-gray-200 rounded-lg">
                    <div className="text-lg font-semibold text-gray-900">
                      {data.summary.totalIncomes > 0
                        ? formatCurrency(data.summary.totalIncomes / data.summary.totalMembers)
                        : formatCurrency(0)
                      }
                    </div>
                    <div className="text-sm text-gray-500">متوسط المساهمة لكل عضو</div>
                  </div>

                  <div className="text-center p-4 border border-gray-200 rounded-lg">
                    <div className="text-lg font-semibold text-gray-900">
                      {data.summary.totalExpenses > 0 && data.summary.totalActivities > 0
                        ? formatCurrency(data.summary.totalExpenses / data.summary.totalActivities)
                        : formatCurrency(0)
                      }
                    </div>
                    <div className="text-sm text-gray-500">متوسط تكلفة النشاط</div>
                  </div>

                  <div className="text-center p-4 border border-gray-200 rounded-lg">
                    <div className="text-lg font-semibold text-gray-900">
                      {data.summary.totalMembers > 0
                        ? Math.round((data.summary.activeMembers / data.summary.totalMembers) * 100)
                        : 0
                      }%
                    </div>
                    <div className="text-sm text-gray-500">نسبة الأعضاء النشطين</div>
                  </div>

                  <div className="text-center p-4 border border-gray-200 rounded-lg">
                    <div className="text-lg font-semibold text-gray-900">
                      {data.summary.totalIncomes > 0
                        ? Math.round((data.summary.totalExpenses / data.summary.totalIncomes) * 100)
                        : 0
                      }%
                    </div>
                    <div className="text-sm text-gray-500">نسبة المصروفات للإيرادات</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* البيانات الشهرية */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <PieChart className="w-5 h-5 ml-2" />
                  الاتجاهات الشهرية
                </CardTitle>
              </CardHeader>
              <CardContent>
                {data.monthlyData.length > 0 ? (
                  <div className="space-y-3">
                    {data.monthlyData.slice(-6).map((month) => (
                      <div key={month.month} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <div className="font-medium">
                            {new Date(month.month + '-01').toLocaleDateString('ar-JO', {
                              year: 'numeric',
                              month: 'long'
                            })}
                          </div>
                          <div className="text-sm text-gray-500">
                            إيرادات: {formatCurrency(month.incomes)} |
                            مصروفات: {formatCurrency(month.expenses)}
                          </div>
                        </div>
                        <div className={`font-medium ${
                          month.balance >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {formatCurrency(month.balance)}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-4">لا توجد بيانات شهرية</p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* تحليل الأداء */}
          <Card>
            <CardHeader>
              <CardTitle>تحليل الأداء المالي</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                <div className="text-center">
                  <div className={`text-3xl font-bold ${
                    data.summary.balance >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {data.summary.balance >= 0 ? '✓' : '⚠'}
                  </div>
                  <div className="text-sm text-gray-600 mt-2">
                    {data.summary.balance >= 0 ? 'الوضع المالي مستقر' : 'يحتاج مراجعة مالية'}
                  </div>
                </div>

                <div className="text-center">
                  <div className={`text-3xl font-bold ${
                    (data.summary.activeMembers / data.summary.totalMembers) >= 0.7 ? 'text-green-600' : 'text-yellow-600'
                  }`}>
                    {(data.summary.activeMembers / data.summary.totalMembers) >= 0.7 ? '📈' : '📊'}
                  </div>
                  <div className="text-sm text-gray-600 mt-2">
                    {(data.summary.activeMembers / data.summary.totalMembers) >= 0.7
                      ? 'مشاركة عالية للأعضاء'
                      : 'يمكن تحسين مشاركة الأعضاء'
                    }
                  </div>
                </div>

                <div className="text-center">
                  <div className={`text-3xl font-bold ${
                    data.summary.totalActivities >= 5 ? 'text-green-600' : 'text-blue-600'
                  }`}>
                    {data.summary.totalActivities >= 5 ? '🎯' : '📅'}
                  </div>
                  <div className="text-sm text-gray-600 mt-2">
                    {data.summary.totalActivities >= 5
                      ? 'نشاط جيد للديوان'
                      : 'يمكن زيادة الأنشطة'
                    }
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            {/* توصيات ذكية */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Target className="w-5 h-5 ml-2" />
                  توصيات ذكية
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* توصية الوضع المالي */}
                  <div className={`p-4 rounded-lg border-l-4 ${
                    data.summary.balance >= 0 ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'
                  }`}>
                    <div className="font-medium mb-2">
                      {data.summary.balance >= 0 ? '✅ الوضع المالي مستقر' : '⚠️ تحذير مالي'}
                    </div>
                    <div className="text-sm text-gray-600">
                      {data.summary.balance >= 0
                        ? 'الديوان يحقق فائضاً مالياً جيداً. يُنصح بالاستثمار في أنشطة إضافية.'
                        : 'الديوان يواجه عجزاً مالياً. يُنصح بمراجعة المصروفات وزيادة الإيرادات.'
                      }
                    </div>
                  </div>

                  {/* توصية مشاركة الأعضاء */}
                  <div className={`p-4 rounded-lg border-l-4 ${
                    (data.summary.activeMembers / data.summary.totalMembers) >= 0.7
                      ? 'border-green-500 bg-green-50'
                      : 'border-yellow-500 bg-yellow-50'
                  }`}>
                    <div className="font-medium mb-2">
                      {(data.summary.activeMembers / data.summary.totalMembers) >= 0.7
                        ? '👥 مشاركة ممتازة للأعضاء'
                        : '📢 تحسين مشاركة الأعضاء'
                      }
                    </div>
                    <div className="text-sm text-gray-600">
                      {(data.summary.activeMembers / data.summary.totalMembers) >= 0.7
                        ? 'نسبة عالية من الأعضاء النشطين. استمر في تقديم الأنشطة المفيدة.'
                        : 'يمكن تحسين مشاركة الأعضاء من خلال المزيد من الأنشطة التفاعلية.'
                      }
                    </div>
                  </div>

                  {/* توصية الأنشطة */}
                  <div className={`p-4 rounded-lg border-l-4 ${
                    data.summary.totalActivities >= 5 ? 'border-green-500 bg-green-50' : 'border-blue-500 bg-blue-50'
                  }`}>
                    <div className="font-medium mb-2">
                      {data.summary.totalActivities >= 5 ? '🎯 نشاط ممتاز' : '📅 زيادة الأنشطة'}
                    </div>
                    <div className="text-sm text-gray-600">
                      {data.summary.totalActivities >= 5
                        ? 'مستوى جيد من الأنشطة. حافظ على هذا المعدل.'
                        : 'يُنصح بزيادة عدد الأنشطة لتعزيز التفاعل بين الأعضاء.'
                      }
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* إحصائيات متقدمة */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="w-5 h-5 ml-2" />
                  إحصائيات متقدمة
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <span className="text-sm font-medium">معدل المساهمة الشهرية</span>
                    <span className="font-bold text-blue-600">
                      {data.summary.totalIncomes > 0 && data.monthlyData.length > 0
                        ? formatCurrency(data.summary.totalIncomes / Math.max(data.monthlyData.length, 1))
                        : formatCurrency(0)
                      }
                    </span>
                  </div>

                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <span className="text-sm font-medium">معدل الإنفاق الشهري</span>
                    <span className="font-bold text-red-600">
                      {data.summary.totalExpenses > 0 && data.monthlyData.length > 0
                        ? formatCurrency(data.summary.totalExpenses / Math.max(data.monthlyData.length, 1))
                        : formatCurrency(0)
                      }
                    </span>
                  </div>

                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <span className="text-sm font-medium">أعلى مساهمة فردية</span>
                    <span className="font-bold text-green-600">
                      {data.topContributors.length > 0
                        ? formatCurrency(data.topContributors[0].totalContributions)
                        : formatCurrency(0)
                      }
                    </span>
                  </div>

                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <span className="text-sm font-medium">نسبة الادخار</span>
                    <span className={`font-bold ${
                      data.summary.totalIncomes > 0
                        ? (data.summary.balance / data.summary.totalIncomes) >= 0.2
                          ? 'text-green-600'
                          : 'text-yellow-600'
                        : 'text-gray-600'
                    }`}>
                      {data.summary.totalIncomes > 0
                        ? `${Math.round((data.summary.balance / data.summary.totalIncomes) * 100)}%`
                        : '0%'
                      }
                    </span>
                  </div>

                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <span className="text-sm font-medium">متوسط المعاملات اليومية</span>
                    <span className="font-bold text-purple-600">
                      {data.recentTransactions.length > 0
                        ? Math.round(data.recentTransactions.length / 30)
                        : 0
                      } معاملة/يوم
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* مقارنة الأداء */}
          <Card>
            <CardHeader>
              <CardTitle>مقارنة الأداء</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                <div className="text-center p-6 border border-gray-200 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600 mb-2">
                    {Math.round((data.summary.activeMembers / data.summary.totalMembers) * 100)}%
                  </div>
                  <div className="text-sm text-gray-600 mb-2">معدل المشاركة</div>
                  <div className={`text-xs px-2 py-1 rounded-full ${
                    (data.summary.activeMembers / data.summary.totalMembers) >= 0.8
                      ? 'bg-green-100 text-green-800'
                      : (data.summary.activeMembers / data.summary.totalMembers) >= 0.6
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                  }`}>
                    {(data.summary.activeMembers / data.summary.totalMembers) >= 0.8
                      ? 'ممتاز'
                      : (data.summary.activeMembers / data.summary.totalMembers) >= 0.6
                        ? 'جيد'
                        : 'يحتاج تحسين'
                    }
                  </div>
                </div>

                <div className="text-center p-6 border border-gray-200 rounded-lg">
                  <div className="text-2xl font-bold text-green-600 mb-2">
                    {data.summary.totalIncomes > 0
                      ? Math.round((data.summary.balance / data.summary.totalIncomes) * 100)
                      : 0
                    }%
                  </div>
                  <div className="text-sm text-gray-600 mb-2">معدل الادخار</div>
                  <div className={`text-xs px-2 py-1 rounded-full ${
                    data.summary.totalIncomes > 0 && (data.summary.balance / data.summary.totalIncomes) >= 0.3
                      ? 'bg-green-100 text-green-800'
                      : data.summary.totalIncomes > 0 && (data.summary.balance / data.summary.totalIncomes) >= 0.1
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                  }`}>
                    {data.summary.totalIncomes > 0 && (data.summary.balance / data.summary.totalIncomes) >= 0.3
                      ? 'ممتاز'
                      : data.summary.totalIncomes > 0 && (data.summary.balance / data.summary.totalIncomes) >= 0.1
                        ? 'جيد'
                        : 'يحتاج تحسين'
                    }
                  </div>
                </div>

                <div className="text-center p-6 border border-gray-200 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600 mb-2">
                    {data.summary.totalActivities}
                  </div>
                  <div className="text-sm text-gray-600 mb-2">عدد الأنشطة</div>
                  <div className={`text-xs px-2 py-1 rounded-full ${
                    data.summary.totalActivities >= 10
                      ? 'bg-green-100 text-green-800'
                      : data.summary.totalActivities >= 5
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                  }`}>
                    {data.summary.totalActivities >= 10
                      ? 'نشط جداً'
                      : data.summary.totalActivities >= 5
                        ? 'نشط'
                        : 'يحتاج تفعيل'
                    }
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
