'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  Users,
  UserCheck,
  UserX,
  Phone,
  Filter,
  DollarSign,
  FileText,
  FileDown,
  Key,
  Download,
  MapPin
} from 'lucide-react'
import MemberDialog from '@/components/members/member-dialog'
import MemberDetailsDialog from '@/components/members/member-details-dialog'
import AccountStatementDialog from '@/components/members/account-statement-dialog'
import MemberIncomeDialog from '@/components/members/member-income-dialog'
import MemberPasswordDialog from '@/components/members/member-password-dialog'
import AdvancedSearch from '@/components/members/advanced-search'
import MemberSearchDialog from '@/components/members/member-search-dialog'
import { formatDate, formatCurrency, getMemberStatusText, getMemberStatusColor } from '@/lib/utils'
import { StatusSelect } from '@/components/ui/native-select'
import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'

interface Member {
  id: string
  name: string
  phone?: string
  address?: string
  notes?: string
  status: string
  createdAt: string
  _count: {
    incomes: number
  }
  incomes?: {
    amount: number
  }[]
  totalContributions?: number
}

interface MembersResponse {
  members: Member[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

export default function MembersPage() {
  const { data: session } = useSession()
  const [members, setMembers] = useState<Member[]>([])
  const [loading, setLoading] = useState(true)
  const [search, setSearch] = useState('')
  const [status, setStatus] = useState('all')
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0,
  })
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [selectedMember, setSelectedMember] = useState<Member | null>(null)
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false)
  const [selectedMemberForDetails, setSelectedMemberForDetails] = useState<Member | null>(null)
  const [isAdvancedSearchOpen, setIsAdvancedSearchOpen] = useState(false)
  const [isAccountStatementOpen, setIsAccountStatementOpen] = useState(false)
  const [selectedMemberForStatement, setSelectedMemberForStatement] = useState<string | null>(null)
  const [isMemberSearchOpen, setIsMemberSearchOpen] = useState(false)
  const [isMemberIncomeOpen, setIsMemberIncomeOpen] = useState(false)
  const [selectedMemberForPassword, setSelectedMemberForPassword] = useState<Member | null>(null)
  const [isPasswordDialogOpen, setIsPasswordDialogOpen] = useState(false)
  const [selectedMemberForIncome, setSelectedMemberForIncome] = useState<Member | null>(null)
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    late: 0,
    inactive: 0,
    suspended: 0,
    archived: 0,
  })

  // جلب الأعضاء
  const fetchMembers = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        search,
        status,
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
      })

      const response = await fetch(`/api/members?${params}`)
      if (!response.ok) throw new Error('فشل في جلب الأعضاء')

      const data: MembersResponse = await response.json()
      setMembers(data.members)
      setPagination(data.pagination)
    } catch (error) {
      console.error('خطأ في جلب الأعضاء:', error)
    } finally {
      setLoading(false)
    }
  }

  // جلب الإحصائيات
  const fetchStats = async () => {
    try {
      const response = await fetch('/api/members?limit=1000')
      if (!response.ok) return

      const data: MembersResponse = await response.json()
      const total = data.members.length
      const active = data.members.filter(m => m.status === 'ACTIVE').length
      const late = data.members.filter(m => m.status === 'LATE').length
      const inactive = data.members.filter(m => m.status === 'INACTIVE').length
      const suspended = data.members.filter(m => m.status === 'SUSPENDED').length
      const archived = data.members.filter(m => m.status === 'ARCHIVED').length

      setStats({ total, active, late, inactive, suspended, archived })
    } catch (error) {
      console.error('خطأ في جلب الإحصائيات:', error)
    }
  }

  useEffect(() => {
    fetchMembers()
  }, [search, status, pagination.page])

  useEffect(() => {
    fetchStats()
  }, [])

  // فتح نافذة إضافة عضو جديد
  const handleAddMember = () => {
    setSelectedMember(null)
    setIsDialogOpen(true)
  }

  // فتح نافذة تعديل عضو
  const handleEditMember = (member: Member) => {
    setSelectedMember(member)
    setIsDialogOpen(true)
  }

  // فتح نافذة تفاصيل العضو
  const handleViewMember = (member: Member) => {
    setSelectedMemberForDetails(member)
    setIsDetailsDialogOpen(true)
  }

  // فتح كشف حساب العضو
  const handleViewAccountStatement = (memberId: string) => {
    setSelectedMemberForStatement(memberId)
    setIsAccountStatementOpen(true)
  }

  // فتح نافذة البحث عن عضو لكشف الحساب
  const handleOpenMemberSearch = () => {
    setIsMemberSearchOpen(true)
  }

  // فتح نافذة إدارة كلمة مرور العضو
  const handleManagePassword = (member: Member) => {
    setSelectedMemberForPassword(member)
    setIsPasswordDialogOpen(true)
  }

  // اختيار عضو من نافذة البحث لكشف الحساب
  const handleSelectMemberForStatement = (memberId: string) => {
    setSelectedMemberForStatement(memberId)
    setIsMemberSearchOpen(false)
    setIsAccountStatementOpen(true)
  }

  // فتح نافذة إضافة إيراد للعضو
  const handleAddMemberIncome = (member: Member) => {
    setSelectedMemberForIncome(member)
    setIsMemberIncomeOpen(true)
  }

  // حذف عضو
  const handleDeleteMember = async (id: string) => {
    if (!confirm('هل أنت متأكد من حذف هذا العضو؟')) return

    try {
      const response = await fetch(`/api/members/${id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        alert(error.error || 'فشل في حذف العضو')
        return
      }

      fetchMembers()
      fetchStats()
    } catch (error) {
      console.error('خطأ في حذف العضو:', error)
      alert('حدث خطأ في حذف العضو')
    }
  }

  // عند نجاح العملية
  const handleSuccess = () => {
    setIsDialogOpen(false)
    fetchMembers()
    fetchStats()
  }

  // تصدير البيانات CSV
  const handleExportMembers = () => {
    const csvContent = [
      ['الاسم', 'الهاتف', 'العنوان', 'الحالة', 'إجمالي الإيرادات', 'تاريخ الإضافة'],
      ...members.map(member => [
        member.name,
        member.phone || '',
        member.address || '',
        getMemberStatusText(member.status),
        (member.incomes?.reduce((total, income) => total + income.amount, 0) || 0).toString(),
        formatDate(member.createdAt)
      ])
    ].map(row => row.join(',')).join('\n')

    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `اعضاء_الديوان_${new Date().toISOString().split('T')[0]}.csv`
    link.click()
  }

  // تصدير البيانات PDF (غير مستخدم حالياً)
  // const handleExportMembersPDF = () => {
    const doc = new jsPDF('p', 'mm', 'a4')

    // إعداد الخط
    doc.setFont('helvetica')
    doc.setFontSize(18)

    // العنوان باللغة الإنجليزية (لضمان الوضوح)
    const title = 'Diwan Abu Alosh - Members Report'
    const pageWidth = doc.internal.pageSize.getWidth()
    const titleWidth = doc.getTextWidth(title)
    doc.text(title, (pageWidth - titleWidth) / 2, 25)

    // العنوان الفرعي
    doc.setFontSize(14)
    const subtitle = 'Members List Report'
    const subtitleWidth = doc.getTextWidth(subtitle)
    doc.text(subtitle, (pageWidth - subtitleWidth) / 2, 35)

    // خط تحت العنوان
    doc.setDrawColor(0, 0, 0)
    doc.line(30, 40, pageWidth - 30, 40)

    // معلومات التقرير
    doc.setFontSize(12)
    const currentDate = new Date().toLocaleDateString('en-GB')
    doc.text(`Report Date: ${currentDate}`, 20, 55)

    // الإحصائيات في صندوق
    doc.setFillColor(245, 245, 245)
    doc.rect(20, 65, pageWidth - 40, 40, 'F')
    doc.setDrawColor(200, 200, 200)
    doc.rect(20, 65, pageWidth - 40, 40)

    doc.setFontSize(11)
    doc.setFont('helvetica', 'bold')
    doc.text('Members Statistics:', 25, 75)

    doc.setFont('helvetica', 'normal')
    doc.setFontSize(10)
    doc.text(`Total Members: ${stats.total}`, 25, 82)
    doc.text(`Active Members: ${stats.active}`, 25, 88)
    doc.text(`Late Members: ${stats.late}`, 100, 82)
    doc.text(`Inactive Members: ${stats.inactive}`, 100, 88)
    doc.text(`Suspended Members: ${stats.suspended}`, 25, 94)
    doc.text(`Archived Members: ${stats.archived}`, 100, 94)

    // رأس الجدول
    let yPosition = 120
    doc.setFontSize(10)
    doc.setFont('helvetica', 'bold')

    // رسم مستطيل للرأس
    doc.setFillColor(70, 130, 180)
    doc.rect(15, yPosition - 6, 180, 10, 'F')
    doc.setTextColor(255, 255, 255)

    const headers = ['Member Name', 'Phone Number', 'Status', 'Total Income (JOD)', 'Join Date']
    const columnPositions = [20, 65, 100, 130, 165]

    headers.forEach((header, index) => {
      doc.text(header, columnPositions[index], yPosition)
    })

    // خط تحت الرأس
    doc.setTextColor(0, 0, 0)
    doc.setDrawColor(0, 0, 0)
    doc.line(15, yPosition + 4, 195, yPosition + 4)

    // بيانات الأعضاء
    yPosition += 15
    doc.setFont('helvetica', 'normal')
    doc.setFontSize(9)

    members.forEach((member, index) => {
      if (yPosition > 270) { // صفحة جديدة
        doc.addPage()
        yPosition = 30

        // إعادة رسم رأس الجدول في الصفحة الجديدة
        doc.setFont('helvetica', 'bold')
        doc.setFontSize(10)
        doc.setFillColor(70, 130, 180)
        doc.rect(15, yPosition - 6, 180, 10, 'F')
        doc.setTextColor(255, 255, 255)

        headers.forEach((header, index) => {
          doc.text(header, columnPositions[index], yPosition)
        })

        doc.setTextColor(0, 0, 0)
        doc.line(15, yPosition + 4, 195, yPosition + 4)
        yPosition += 15
        doc.setFont('helvetica', 'normal')
        doc.setFontSize(9)
      }

      // تلوين الصفوف بالتناوب
      if (index % 2 === 0) {
        doc.setFillColor(248, 249, 250)
        doc.rect(15, yPosition - 5, 180, 9, 'F')
      }

      // إعداد البيانات - استخدام النصوص الإنجليزية لضمان الوضوح
      const memberName = member.name.length > 20 ? member.name.substring(0, 20) + '...' : member.name
      const memberPhone = member.phone || 'Not Specified'

      // تحويل حالة العضو للإنجليزية
      const getStatusInEnglish = (status: string) => {
        switch (status) {
          case 'ACTIVE': return 'Active'
          case 'LATE': return 'Late'
          case 'INACTIVE': return 'Inactive'
          case 'SUSPENDED': return 'Suspended'
          case 'ARCHIVED': return 'Archived'
          default: return status
        }
      }

      const memberStatus = getStatusInEnglish(member.status)
      const memberIncome = formatCurrency(member.incomes?.reduce((total, income) => total + income.amount, 0) || 0)
      const memberJoinDate = formatDate(member.createdAt)

      const rowData = [memberName, memberPhone, memberStatus, memberIncome, memberJoinDate]

      rowData.forEach((data, colIndex) => {
        doc.text(data.toString(), columnPositions[colIndex], yPosition)
      })

      // خط فاصل خفيف
      doc.setDrawColor(230, 230, 230)
      doc.line(15, yPosition + 3, 195, yPosition + 3)

      yPosition += 9
    })

    // إضافة تذييل
    const pageCount = (doc.internal as { getNumberOfPages: () => number }).getNumberOfPages()
    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i)
      doc.setFontSize(8)
      doc.setTextColor(100, 100, 100)
      doc.text(`Page ${i} of ${pageCount}`, pageWidth - 30, 285)
      doc.text('Generated by Diwan Abu Alosh System', 20, 285)

      // خط في أسفل الصفحة
      doc.setDrawColor(200, 200, 200)
      doc.line(20, 280, pageWidth - 20, 280)
    }

    // حفظ الملف
    const fileName = `Members_Report_${new Date().toISOString().split('T')[0]}.pdf`
    doc.save(fileName)
  // }

  // تصدير PDF باستخدام HTML (يدعم العربية بشكل أفضل)
  const handleExportMembersPDFArabic = async () => {
    // إنشاء عنصر HTML مؤقت
    const printElement = document.createElement('div')
    printElement.style.position = 'absolute'
    printElement.style.left = '-9999px'
    printElement.style.top = '0'
    printElement.style.width = '210mm'
    printElement.style.padding = '20mm'
    printElement.style.fontFamily = 'Arial, sans-serif'
    printElement.style.fontSize = '12px'
    printElement.style.lineHeight = '1.4'
    printElement.style.color = '#000'
    printElement.style.backgroundColor = '#fff'
    printElement.style.direction = 'rtl'

    const currentDate = new Date().toLocaleDateString('en-GB', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })

    printElement.innerHTML = `
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="font-size: 24px; margin: 0; color: #2c3e50;">ديوان أبو علوش</h1>
        <h2 style="font-size: 18px; margin: 10px 0; color: #34495e;">قائمة الأعضاء</h2>
        <hr style="border: 1px solid #bdc3c7; margin: 20px 0;">
      </div>

      <div style="margin-bottom: 25px;">
        <p style="font-size: 14px; margin: 5px 0;"><strong>تاريخ التقرير:</strong> ${currentDate}</p>
      </div>

      <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 25px; border: 1px solid #dee2e6;">
        <h3 style="margin: 0 0 10px 0; color: #495057;">إحصائيات الأعضاء:</h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
          <div>• إجمالي الأعضاء: <strong>${stats.total}</strong> عضو</div>
          <div>• الأعضاء النشطون: <strong>${stats.active}</strong> عضو</div>
          <div>• الأعضاء المتأخرون: <strong>${stats.late}</strong> عضو</div>
          <div>• الأعضاء غير الملتزمون: <strong>${stats.inactive}</strong> عضو</div>
          <div>• الأعضاء الموقوفون: <strong>${stats.suspended}</strong> عضو</div>
          <div>• الأعضاء المؤرشفون: <strong>${stats.archived}</strong> عضو</div>
        </div>
      </div>

      <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
        <thead>
          <tr style="background: #4682b4; color: white;">
            <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">اسم العضو</th>
            <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">رقم الهاتف</th>
            <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">حالة العضو</th>
            <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">إجمالي الإيرادات</th>
            <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">تاريخ الانضمام</th>
          </tr>
        </thead>
        <tbody>
          ${members.map((member, index) => `
            <tr style="background: ${index % 2 === 0 ? '#f8f9fa' : '#ffffff'};">
              <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${member.name}</td>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${member.phone || 'غير محدد'}</td>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${getMemberStatusText(member.status)}</td>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${formatCurrency(member.incomes?.reduce((total, income) => total + income.amount, 0) || 0)}</td>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${formatDate(member.createdAt)}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>

      <div style="margin-top: 30px; text-align: center; font-size: 10px; color: #6c757d;">
        <hr style="border: 1px solid #dee2e6; margin: 20px 0;">
        <p>تم إنشاؤه بواسطة نظام ديوان أبو علوش - ${currentDate}</p>
      </div>
    `

    document.body.appendChild(printElement)

    try {
      const canvas = await html2canvas(printElement, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
      })

      const imgData = canvas.toDataURL('image/png')
      const pdf = new jsPDF('p', 'mm', 'a4')

      const imgWidth = 210
      const pageHeight = 295
      const imgHeight = (canvas.height * imgWidth) / canvas.width
      let heightLeft = imgHeight

      let position = 0

      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
      heightLeft -= pageHeight

      while (heightLeft >= 0) {
        position = heightLeft - imgHeight
        pdf.addPage()
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
        heightLeft -= pageHeight
      }

      const fileName = `قائمة_اعضاء_الديوان_${new Date().toISOString().split('T')[0]}.pdf`
      pdf.save(fileName)

    } catch (error) {
      console.error('خطأ في تصدير PDF:', error)
      alert('حدث خطأ في تصدير PDF')
    } finally {
      document.body.removeChild(printElement)
    }
  }

  // تغيير حالة العضو
  const handleChangeStatus = async (member: Member, newStatus: string) => {
    try {
      const response = await fetch(`/api/members/${member.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...member,
          status: newStatus,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        alert(error.error || 'فشل في تحديث حالة العضو')
        return
      }

      fetchMembers()
      fetchStats()
    } catch (error) {
      console.error('خطأ في تحديث حالة العضو:', error)
      alert('حدث خطأ في تحديث حالة العضو')
    }
  }

  // البحث المتقدم
  const handleAdvancedSearch = (filters: { name?: string; phone?: string; address?: string; status?: string }) => {
    // تطبيق الفلاتر المتقدمة
    let searchTerm = ''
    if (filters.name) searchTerm += filters.name + ' '
    if (filters.phone) searchTerm += filters.phone + ' '
    if (filters.address) searchTerm += filters.address + ' '

    setSearch(searchTerm.trim())
    setStatus(filters.status)
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  // إعادة تعيين البحث
  const handleResetSearch = () => {
    setSearch('')
    setStatus('all')
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const canEdit = session?.user.role !== 'VIEWER'
  const canDelete = session?.user.role === 'ADMIN'

  return (
    <div className="space-y-8">
      {/* رأس الصفحة المحسن */}
      <div className="text-center mb-8">
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl shadow-2xl p-10 text-white relative overflow-hidden">
          {/* خلفية متحركة */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-30 animate-pulse"></div>

          {/* المحتوى */}
          <div className="relative z-10">
            <div className="inline-flex items-center justify-center w-20 h-20 rounded-full mb-6 bg-white bg-opacity-20 backdrop-blur-sm">
              <Users className="w-10 h-10 text-white" />
            </div>

            <h1 className="text-5xl font-black mb-4 text-white">
              إدارة الأعضاء
            </h1>

            <p className="text-xl font-semibold mb-6 text-blue-100">
              إدارة شاملة لأعضاء ديوان أبو علوش وبياناتهم
            </p>

            <div className="flex items-center justify-center space-x-2 space-x-reverse">
              <div className="h-1 w-16 rounded-full bg-white bg-opacity-60"></div>
              <div className="h-1 w-8 rounded-full bg-white bg-opacity-40"></div>
              <div className="h-1 w-16 rounded-full bg-white bg-opacity-60"></div>
            </div>
          </div>
        </div>
      </div>

      {/* أزرار الإجراءات */}
      <div className="flex justify-center mb-8">
        <div className="bg-white rounded-2xl shadow-xl p-6 border border-gray-100">
          <div className="flex flex-wrap gap-4 justify-center">
            <Button
              onClick={handleExportMembers}
              className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold"
            >
              <Download className="w-5 h-5 ml-2" />
              تصدير CSV
            </Button>
            <Button
              onClick={handleExportMembersPDFArabic}
              className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold"
            >
              <FileDown className="w-5 h-5 ml-2" />
              تصدير PDF
            </Button>
            {canEdit && (
              <Button
                onClick={handleAddMember}
                className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold"
              >
                <Plus className="w-5 h-5 ml-2" />
                إضافة عضو جديد
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* إحصائيات سريعة محسنة */}
      <div className="grid grid-cols-2 gap-6 sm:grid-cols-3 lg:grid-cols-6">
        <Card className="group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500 to-blue-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>

          <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-1 rounded-t-xl"></div>

          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative z-10">
            <CardTitle className="text-sm font-bold" style={{ color: '#333333' }}>إجمالي الأعضاء</CardTitle>
            <div className="p-3 rounded-2xl shadow-lg" style={{ backgroundColor: '#007bff' }}>
              <Users className="h-5 w-5 text-white" />
            </div>
          </CardHeader>

          <CardContent className="relative z-10">
            <div className="text-3xl font-black mb-2" style={{ color: '#191970' }}>
              {stats.total}
            </div>
          </CardContent>
        </Card>

        <Card className="group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative">
          <div className="absolute inset-0 bg-gradient-to-br from-green-500 to-green-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>

          <div className="bg-gradient-to-r from-green-500 to-green-600 p-1 rounded-t-xl"></div>

          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative z-10">
            <CardTitle className="text-sm font-bold" style={{ color: '#333333' }}>نشط</CardTitle>
            <div className="p-3 rounded-2xl shadow-lg" style={{ backgroundColor: '#28a745' }}>
              <UserCheck className="h-5 w-5 text-white" />
            </div>
          </CardHeader>

          <CardContent className="relative z-10">
            <div className="text-3xl font-black mb-2" style={{ color: '#191970' }}>
              {stats.active}
            </div>
          </CardContent>
        </Card>

        <Card className="group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative">
          <div className="absolute inset-0 bg-gradient-to-br from-yellow-500 to-yellow-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>

          <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 p-1 rounded-t-xl"></div>

          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative z-10">
            <CardTitle className="text-sm font-bold" style={{ color: '#333333' }}>متأخر</CardTitle>
            <div className="p-3 rounded-2xl shadow-lg" style={{ backgroundColor: '#ffc107' }}>
              <span className="text-white text-sm font-bold">⏰</span>
            </div>
          </CardHeader>

          <CardContent className="relative z-10">
            <div className="text-3xl font-black mb-2" style={{ color: '#191970' }}>
              {stats.late}
            </div>
          </CardContent>
        </Card>

        <Card className="group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative">
          <div className="absolute inset-0 bg-gradient-to-br from-red-500 to-red-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>

          <div className="bg-gradient-to-r from-red-500 to-red-600 p-1 rounded-t-xl"></div>

          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative z-10">
            <CardTitle className="text-sm font-bold" style={{ color: '#333333' }}>غير ملتزم</CardTitle>
            <div className="p-3 rounded-2xl shadow-lg" style={{ backgroundColor: '#dc3545' }}>
              <UserX className="h-5 w-5 text-white" />
            </div>
          </CardHeader>

          <CardContent className="relative z-10">
            <div className="text-3xl font-black mb-2" style={{ color: '#191970' }}>
              {stats.inactive}
            </div>
          </CardContent>
        </Card>

        <Card className="group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500 to-purple-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>

          <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-1 rounded-t-xl"></div>

          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative z-10">
            <CardTitle className="text-sm font-bold" style={{ color: '#333333' }}>موقوف</CardTitle>
            <div className="p-3 rounded-2xl shadow-lg" style={{ backgroundColor: '#800020' }}>
              <span className="text-white text-sm font-bold">⏸️</span>
            </div>
          </CardHeader>

          <CardContent className="relative z-10">
            <div className="text-3xl font-black mb-2" style={{ color: '#191970' }}>
              {stats.suspended}
            </div>
          </CardContent>
        </Card>

        <Card className="group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative">
          <div className="absolute inset-0 bg-gradient-to-br from-gray-500 to-gray-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>

          <div className="bg-gradient-to-r from-gray-500 to-gray-600 p-1 rounded-t-xl"></div>

          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative z-10">
            <CardTitle className="text-sm font-bold" style={{ color: '#333333' }}>مؤرشف</CardTitle>
            <div className="p-3 rounded-2xl shadow-lg" style={{ backgroundColor: '#6c757d' }}>
              <span className="text-white text-sm font-bold">📁</span>
            </div>
          </CardHeader>

          <CardContent className="relative z-10">
            <div className="text-3xl font-black mb-2" style={{ color: '#191970' }}>
              {stats.archived}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* أدوات البحث والتصفية */}
      <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-sm">
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
              <Input
                placeholder="البحث في الأعضاء بالاسم أو الهاتف..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pr-12 h-12 text-base shadow-md"
              />
            </div>
            <StatusSelect
              value={status}
              onChange={(e) => setStatus(e.target.value)}
              allLabel="جميع الأعضاء"
              className="shadow-md h-12"
            />
            <Button
              variant="info"
              onClick={() => setIsAdvancedSearchOpen(true)}
              className="shadow-lg h-12"
            >
              <Filter className="w-4 h-4 ml-2" />
              بحث متقدم
            </Button>
            <Button
              variant="accent"
              onClick={handleOpenMemberSearch}
              className="shadow-lg h-12"
            >
              <FileText className="w-4 h-4 ml-2" />
              كشف حساب
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* جدول الأعضاء */}
      <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm overflow-hidden">
        <CardContent className="p-0">
          {loading ? (
            <div className="flex flex-col justify-center items-center h-64 bg-gradient-to-br from-slate-50 to-slate-100">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-slate-500 mb-4"></div>
              <div className="text-slate-600 font-medium">جاري تحميل بيانات الأعضاء...</div>
            </div>
          ) : members.length === 0 ? (
            <div className="flex flex-col justify-center items-center h-64 bg-gradient-to-br from-slate-50 to-slate-100">
              <Users className="h-16 w-16 text-slate-300 mb-4" />
              <div className="text-slate-600 font-medium">لا توجد أعضاء مطابقة للبحث</div>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow style={{ backgroundColor: '#191970' }}>
                  <TableHead style={{ backgroundColor: '#191970', color: 'white', fontWeight: '600' }}>الاسم</TableHead>
                  <TableHead style={{ backgroundColor: '#191970', color: 'white', fontWeight: '600' }}>الهاتف</TableHead>
                  <TableHead style={{ backgroundColor: '#191970', color: 'white', fontWeight: '600' }}>الحالة</TableHead>
                  <TableHead style={{ backgroundColor: '#191970', color: 'white', fontWeight: '600' }}>إجمالي الإيرادات</TableHead>
                  <TableHead style={{ backgroundColor: '#191970', color: 'white', fontWeight: '600' }}>تاريخ الإضافة</TableHead>
                  <TableHead style={{ backgroundColor: '#191970', color: 'white', fontWeight: '600' }}>الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {members.map((member) => (
                  <TableRow key={member.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{member.name}</div>
                        {member.address && (
                          <div className="text-sm text-gray-500 flex items-center mt-1">
                            <MapPin className="w-3 h-3 ml-1" />
                            {member.address}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {member.phone && (
                        <div className="flex items-center">
                          <Phone className="w-3 h-3 ml-1" />
                          {member.phone}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      {canEdit ? (
                        <select
                          value={member.status}
                          onChange={(e) => handleChangeStatus(member, e.target.value)}
                          className={`px-2 py-1 text-xs font-semibold rounded-full border-0 cursor-pointer ${getMemberStatusColor(member.status)}`}
                        >
                          <option value="ACTIVE">✅ نشط</option>
                          <option value="LATE">⏰ متأخر</option>
                          <option value="INACTIVE">❌ غير ملتزم</option>
                          <option value="SUSPENDED">⏸️ موقوف مؤقتاً</option>
                          <option value="ARCHIVED">📁 مؤرشف</option>
                        </select>
                      ) : (
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getMemberStatusColor(member.status)}`}>
                          {getMemberStatusText(member.status)}
                        </span>
                      )}
                    </TableCell>
                    <TableCell>
                      {formatCurrency(
                        member.incomes?.reduce((total, income) => total + income.amount, 0) || 0
                      )}
                    </TableCell>
                    <TableCell>{formatDate(member.createdAt)}</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewMember(member)}
                          className="hover:bg-blue-50 border"
                          style={{
                            color: '#0056cc',
                            backgroundColor: 'rgba(0, 86, 204, 0.1)',
                            borderColor: 'rgba(0, 86, 204, 0.2)',
                            fontWeight: '600'
                          }}
                          title="عرض التفاصيل"
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewAccountStatement(member.id)}
                          className="hover:bg-purple-50 border"
                          style={{
                            color: '#800020',
                            backgroundColor: 'rgba(128, 0, 32, 0.1)',
                            borderColor: 'rgba(128, 0, 32, 0.2)',
                            fontWeight: '600'
                          }}
                          title="كشف الحساب"
                        >
                          <FileText className="w-4 h-4" />
                        </Button>
                        {canEdit && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleAddMemberIncome(member)}
                            className="hover:bg-yellow-50 border"
                            style={{
                              color: '#b8860b',
                              backgroundColor: 'rgba(184, 134, 11, 0.1)',
                              borderColor: 'rgba(184, 134, 11, 0.2)',
                              fontWeight: '600'
                            }}
                            title="إضافة إيراد"
                          >
                            <DollarSign className="w-4 h-4" />
                          </Button>
                        )}
                        {canEdit && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditMember(member)}
                            className="hover:bg-green-50 border"
                            style={{
                              color: '#1e7e34',
                              backgroundColor: 'rgba(30, 126, 52, 0.1)',
                              borderColor: 'rgba(30, 126, 52, 0.2)',
                              fontWeight: '600'
                            }}
                            title="تعديل"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                        )}
                        {canEdit && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleManagePassword(member)}
                            className="hover:bg-purple-50 border"
                            style={{
                              color: '#6f42c1',
                              backgroundColor: 'rgba(111, 66, 193, 0.1)',
                              borderColor: 'rgba(111, 66, 193, 0.2)',
                              fontWeight: '600'
                            }}
                            title="إدارة كلمة المرور"
                          >
                            <Key className="w-4 h-4" />
                          </Button>
                        )}
                        {canDelete && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteMember(member.id)}
                            className="hover:bg-red-50 border"
                            style={{
                              color: '#c82333',
                              backgroundColor: 'rgba(200, 35, 51, 0.1)',
                              borderColor: 'rgba(200, 35, 51, 0.2)',
                              fontWeight: '600'
                            }}
                            title="حذف"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* التصفح */}
      {pagination.pages > 1 && (
        <div className="flex justify-center space-x-2 space-x-reverse">
          <Button
            variant="outline"
            disabled={pagination.page === 1}
            onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
            className="border-2"
            style={{
              borderColor: '#007bff',
              color: pagination.page === 1 ? '#6c757d' : '#007bff',
              backgroundColor: 'white'
            }}
          >
            السابق
          </Button>
          <span className="flex items-center px-4" style={{ color: '#333333' }}>
            صفحة {pagination.page} من {pagination.pages}
          </span>
          <Button
            variant="outline"
            disabled={pagination.page === pagination.pages}
            onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
            className="border-2"
            style={{
              borderColor: '#007bff',
              color: pagination.page === pagination.pages ? '#6c757d' : '#007bff',
              backgroundColor: 'white'
            }}
          >
            التالي
          </Button>
        </div>
      )}

      {/* نافذة إضافة/تعديل العضو */}
      <MemberDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        member={selectedMember}
        onSuccess={handleSuccess}
      />

      {/* نافذة تفاصيل العضو */}
      <MemberDetailsDialog
        open={isDetailsDialogOpen}
        onOpenChange={setIsDetailsDialogOpen}
        member={selectedMemberForDetails}
      />

      {/* نافذة البحث المتقدم */}
      <AdvancedSearch
        open={isAdvancedSearchOpen}
        onOpenChange={setIsAdvancedSearchOpen}
        onSearch={handleAdvancedSearch}
        onReset={handleResetSearch}
      />

      {/* نافذة كشف حساب العضو */}
      <AccountStatementDialog
        open={isAccountStatementOpen}
        onOpenChange={setIsAccountStatementOpen}
        memberId={selectedMemberForStatement}
      />

      {/* نافذة إضافة إيراد للعضو */}
      <MemberIncomeDialog
        open={isMemberIncomeOpen}
        onOpenChange={setIsMemberIncomeOpen}
        member={selectedMemberForIncome}
        onSuccess={handleSuccess}
      />

      {/* نافذة البحث عن عضو لكشف الحساب */}
      <MemberSearchDialog
        open={isMemberSearchOpen}
        onOpenChange={setIsMemberSearchOpen}
        onSelectMember={handleSelectMemberForStatement}
        title="اختيار عضو لكشف الحساب"
        description="ابحث عن العضو المطلوب لعرض كشف حسابه"
      />

      {/* نافذة إدارة كلمة مرور العضو */}
      <MemberPasswordDialog
        open={isPasswordDialogOpen}
        onOpenChange={setIsPasswordDialogOpen}
        member={selectedMemberForPassword}
        onSuccess={handleSuccess}
      />
    </div>
  )
}
