import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'

// تحديث كلمة مرور العضو
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ message: 'غير مصرح' }, { status: 401 })
    }

    // التحقق من الصلاحيات - فقط المدير ومدخل البيانات
    if (!['ADMIN', 'DATA_ENTRY'].includes(session.user.role)) {
      return NextResponse.json(
        { message: 'ليس لديك صلاحية لتعديل كلمات مرور الأعضاء' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { password } = body
    const { id } = await params

    // التحقق من صحة البيانات
    if (!password) {
      return NextResponse.json(
        { message: 'كلمة المرور مطلوبة' },
        { status: 400 }
      )
    }

    if (password.length < 6) {
      return NextResponse.json(
        { message: 'كلمة المرور يجب أن تكون على الأقل 6 أحرف' },
        { status: 400 }
      )
    }

    // التحقق من وجود العضو
    const existingMember = await prisma.member.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        email: true
      }
    })

    if (!existingMember) {
      return NextResponse.json(
        { message: 'العضو غير موجود' },
        { status: 404 }
      )
    }

    // التحقق من وجود بريد إلكتروني للعضو
    if (!existingMember.email) {
      return NextResponse.json(
        { message: 'لا يمكن تعيين كلمة مرور للعضو بدون بريد إلكتروني' },
        { status: 400 }
      )
    }

    // تشفير كلمة المرور
    const hashedPassword = await bcrypt.hash(password, 12)

    // البحث عن المستخدم المرتبط بالعضو
    const memberUser = await prisma.memberUser.findFirst({
      where: { memberId: id },
      include: { user: true }
    })

    // تحديث كلمة مرور العضو
    const updatedMember = await prisma.member.update({
      where: { id },
      data: {
        password: hashedPassword,
        updatedAt: new Date()
      },
      select: {
        id: true,
        name: true,
        email: true,
        updatedAt: true
      }
    })

    // إذا كان هناك مستخدم مرتبط، قم بتحديث كلمة مروره أيضاً
    if (memberUser && memberUser.user) {
      await prisma.user.update({
        where: { id: memberUser.user.id },
        data: {
          password: hashedPassword,
          updatedAt: new Date()
        }
      })
    } else {
      // إذا لم يكن هناك مستخدم مرتبط، قم بإنشاء واحد
      const newUser = await prisma.user.create({
        data: {
          name: existingMember.name,
          email: existingMember.email!,
          password: hashedPassword,
          role: 'VIEWER',
          phone: null
        }
      })

      // ربط المستخدم الجديد بالعضو
      await prisma.memberUser.create({
        data: {
          memberId: id,
          userId: newUser.id,
          isActive: true,
          canViewAccountStatement: true,
          canViewGallery: true
        }
      })
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث كلمة مرور العضو بنجاح',
      member: updatedMember
    })
  } catch (error: unknown) {
    console.error('خطأ في تحديث كلمة مرور العضو:', error)
    
    return NextResponse.json(
      { message: 'حدث خطأ في تحديث كلمة مرور العضو' },
      { status: 500 }
    )
  }
}

// حذف كلمة مرور العضو (إلغاء تفعيل تسجيل الدخول)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ message: 'غير مصرح' }, { status: 401 })
    }

    // التحقق من الصلاحيات - فقط المدير
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'ليس لديك صلاحية لحذف كلمات مرور الأعضاء' },
        { status: 403 }
      )
    }

    const { id } = await params

    // التحقق من وجود العضو
    const existingMember = await prisma.member.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        email: true
      }
    })

    if (!existingMember) {
      return NextResponse.json(
        { message: 'العضو غير موجود' },
        { status: 404 }
      )
    }

    // البحث عن المستخدم المرتبط بالعضو
    const memberUser = await prisma.memberUser.findFirst({
      where: { memberId: id },
      include: { user: true }
    })

    // حذف كلمة المرور (تعيينها إلى null)
    const updatedMember = await prisma.member.update({
      where: { id },
      data: {
        password: null,
        lastLogin: null,
        updatedAt: new Date()
      },
      select: {
        id: true,
        name: true,
        email: true,
        updatedAt: true
      }
    })

    // إذا كان هناك مستخدم مرتبط، قم بحذف كلمة مروره أيضاً أو إلغاء تفعيل الحساب
    if (memberUser && memberUser.user) {
      await prisma.memberUser.update({
        where: { id: memberUser.id },
        data: {
          isActive: false,
          updatedAt: new Date()
        }
      })
    }

    return NextResponse.json({
      success: true,
      message: 'تم إلغاء تفعيل تسجيل دخول العضو بنجاح',
      member: updatedMember
    })
  } catch (error: unknown) {
    console.error('خطأ في حذف كلمة مرور العضو:', error)
    
    return NextResponse.json(
      { message: 'حدث خطأ في حذف كلمة مرور العضو' },
      { status: 500 }
    )
  }
}
