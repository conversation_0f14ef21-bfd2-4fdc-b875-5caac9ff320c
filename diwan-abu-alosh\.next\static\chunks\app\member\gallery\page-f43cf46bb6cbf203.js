(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3120],{24082:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var s=r(95155),a=r(12115),l=r(35695),i=r(30285),n=r(66695),o=r(62523),d=r(35169),c=r(84355),m=r(34835),x=r(47924),h=r(21380),g=r(69074),u=r(92657),f=r(27213),p=r(66766);function b(){let[e,t]=(0,a.useState)(null),[r,b]=(0,a.useState)([]),[y,v]=(0,a.useState)([]),[N,w]=(0,a.useState)(null),[j,A]=(0,a.useState)(""),[C,E]=(0,a.useState)(!0),[k,T]=(0,a.useState)(null),S=(0,l.useRouter)();(0,a.useEffect)(()=>{I()},[]);let I=async()=>{try{let e=await fetch("/api/auth/member/session"),r=await e.json();if(!e.ok)return void S.push("/member/signin");t(r.user.member),await R()}catch(e){console.error("خطأ في جلب البيانات:",e),S.push("/member/signin")}finally{E(!1)}},R=async()=>{try{let e=await fetch("/api/member/gallery");if(e.ok){let t=await e.json();b(t.folders),v(t.unfolderPhotos)}else console.error("خطأ في جلب المعرض:",e.status)}catch(e){console.error("خطأ في جلب المعرض:",e)}},D=async e=>{try{let t=await fetch("/api/member/gallery/".concat(e));if(t.ok){let e=await t.json();v(e.photos)}else console.error("خطأ في جلب صور المجلد:",t.status)}catch(e){console.error("خطأ في جلب صور المجلد:",e)}},O=async()=>{document.cookie="member-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;",window.location.href="/member/signin",fetch("/api/auth/member/signout",{method:"POST"}).catch(()=>{})},V=e=>{w(e),D(e)},L=y.filter(e=>{var t;return(e.title||"").toLowerCase().includes(j.toLowerCase())||(null==(t=e.description)?void 0:t.toLowerCase().includes(j.toLowerCase()))}),P=e=>new Date(e).toLocaleDateString("ar-SA");return C?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"جاري تحميل المعرض..."})]})}):e?(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50",children:[(0,s.jsx)("header",{className:"bg-white/80 backdrop-blur-sm border-b border-gray-200 shadow-sm",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)(i.$,{onClick:()=>S.push("/member/dashboard"),variant:"outline",size:"sm",className:"border-gray-300",children:[(0,s.jsx)(d.A,{className:"w-4 h-4 ml-2"}),"العودة"]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(c.A,{className:"w-6 h-6 text-indigo-600"}),(0,s.jsx)("h1",{className:"text-lg font-bold text-gray-900",children:"معرض الصور"})]})]}),(0,s.jsx)("div",{className:"flex items-center gap-3",children:(0,s.jsxs)(i.$,{onClick:O,variant:"outline",size:"sm",className:"text-red-600 border-red-200 hover:bg-red-50",children:[(0,s.jsx)(m.A,{className:"w-4 h-4 ml-2"}),"خروج"]})})]})})}),(0,s.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsxs)("div",{className:"relative max-w-md",children:[(0,s.jsx)(x.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,s.jsx)(o.p,{type:"text",placeholder:"البحث في الصور...",value:j,onChange:e=>A(e.target.value),className:"pr-10"})]})}),N?(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)(i.$,{onClick:()=>{w(null),R()},variant:"outline",size:"sm",children:[(0,s.jsx)(d.A,{className:"w-4 h-4 ml-2"}),"العودة للمجلدات"]}),(0,s.jsxs)("h2",{className:"text-2xl font-bold text-gray-900 flex items-center gap-2",children:[(0,s.jsx)(f.A,{className:"w-6 h-6 text-indigo-600"}),"الصور"]})]}),(0,s.jsxs)("p",{className:"text-gray-600",children:[L.length," صورة"]})]}),L.length>0?(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:L.map(e=>(0,s.jsxs)(n.Zp,{className:"cursor-pointer hover:shadow-lg transition-all duration-300 overflow-hidden",onClick:()=>T(e),children:[(0,s.jsx)("div",{className:"aspect-square relative",children:(0,s.jsx)(p.default,{src:e.imagePath,alt:e.title||"صورة",fill:!0,className:"object-cover"})}),(0,s.jsxs)(n.Wu,{className:"p-4",children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-900 truncate",children:e.title||"صورة"}),e.description&&(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-1 line-clamp-2",children:e.description}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:P(e.createdAt)})]})]},e.id))}):(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)(f.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"لا توجد صور"}),(0,s.jsx)("p",{className:"text-gray-600",children:"لا توجد صور في هذا المجلد"})]})]}):(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsxs)("h2",{className:"text-2xl font-bold text-gray-900 flex items-center gap-2",children:[(0,s.jsx)(h.A,{className:"w-6 h-6 text-indigo-600"}),"مجلدات الصور"]}),(0,s.jsxs)("p",{className:"text-gray-600",children:[r.length," مجلد"]})]}),r.length>0?(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:r.map(e=>(0,s.jsxs)(n.Zp,{className:"cursor-pointer hover:shadow-lg transition-all duration-300 border-2 hover:border-indigo-200",onClick:()=>V(e.id),children:[(0,s.jsx)(n.aR,{className:"pb-4",children:(0,s.jsxs)(n.ZB,{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,s.jsx)(h.A,{className:"w-6 h-6 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.title}),(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:[e._count.photos," صورة"]})]})]})}),(0,s.jsxs)(n.Wu,{children:[e.description&&(0,s.jsx)("p",{className:"text-gray-600 mb-3",children:e.description}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500",children:[(0,s.jsxs)("span",{className:"flex items-center gap-1",children:[(0,s.jsx)(g.A,{className:"w-4 h-4"}),P(e.createdAt)]}),(0,s.jsxs)("span",{className:"flex items-center gap-1",children:[(0,s.jsx)(u.A,{className:"w-4 h-4"}),"عرض الصور"]})]})]})]},e.id))}):(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)(h.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"لا توجد مجلدات"}),(0,s.jsx)("p",{className:"text-gray-600",children:"لم يتم إنشاء أي مجلدات في المعرض بعد"})]})]}),k&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4",onClick:()=>T(null),children:(0,s.jsxs)("div",{className:"max-w-4xl max-h-full bg-white rounded-lg overflow-hidden",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(p.default,{src:k.imagePath,alt:k.title||"صورة",width:800,height:600,className:"w-full h-auto"}),(0,s.jsx)(i.$,{onClick:()=>T(null),className:"absolute top-4 left-4 bg-black bg-opacity-50 text-white hover:bg-opacity-75",size:"sm",children:"إغلاق"})]}),(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-900",children:k.title||"صورة"}),k.description&&(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:k.description}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:P(k.createdAt)})]})]})})]})]}):null}},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>o});var s=r(95155),a=r(12115),l=r(74466),i=r(59434);let n=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-semibold transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-95",{variants:{variant:{default:"bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-primary-600 hover:to-primary-700",destructive:"bg-gradient-to-r from-danger-500 to-danger-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-danger-600 hover:to-danger-700",outline:"border-2 border-primary-300 bg-transparent text-primary-700 hover:bg-primary-50 hover:border-primary-400 hover:shadow-md",secondary:"bg-gradient-to-r from-secondary-100 to-secondary-200 text-secondary-800 shadow-md hover:shadow-lg hover:-translate-y-0.5 hover:from-secondary-200 hover:to-secondary-300",ghost:"text-primary-600 hover:bg-primary-100 hover:text-primary-800",link:"text-primary-600 underline-offset-4 hover:underline hover:text-primary-800",success:"bg-gradient-to-r from-success-500 to-success-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-success-600 hover:to-success-700",warning:"bg-gradient-to-r from-warning-500 to-warning-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-warning-600 hover:to-warning-700",info:"bg-gradient-to-r from-info-500 to-info-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-info-600 hover:to-info-700",accent:"bg-gradient-to-r from-gold-500 to-gold-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-gold-600 hover:to-gold-700"},size:{default:"h-11 px-6 py-2.5",sm:"h-9 rounded-lg px-4 text-xs",lg:"h-13 rounded-xl px-8 text-base",icon:"h-11 w-11",xs:"h-8 rounded-md px-3 text-xs"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef((e,t)=>{let{className:r,variant:a,size:l,...o}=e;return(0,s.jsx)("button",{className:(0,i.cn)(n({variant:a,size:l,className:r})),ref:t,...o})});o.displayName="Button"},56969:(e,t,r)=>{Promise.resolve().then(r.bind(r,24082))},59434:(e,t,r)=>{"use strict";r.d(t,{OR:()=>x,Tk:()=>d,WK:()=>m,Yq:()=>n,cn:()=>l,gr:()=>o,uF:()=>c,vv:()=>i});var s=r(52596),a=r(39688);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}function i(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"JOD",minimumFractionDigits:2}).format(e).replace("JOD","د.أ")}function n(e){let t="string"==typeof e?new Date(e):e;return isNaN(t.getTime())?"تاريخ غير صحيح":new Intl.DateTimeFormat("en-GB",{year:"numeric",month:"2-digit",day:"2-digit"}).format(t)}function o(e){return({ADMIN:"مدير",DATA_ENTRY:"مدخل بيانات",VIEWER:"مطلع"})[e]||e}function d(e){return({SUBSCRIPTION:"اشتراكات",DONATION:"تبرعات",EVENT:"فعاليات",OTHER:"أخرى"})[e]||e}function c(e){return({MEETINGS:"اجتماعات",EVENTS:"مناسبات",MAINTENANCE:"إصلاحات",SOCIAL:"اجتماعية",GENERAL:"عامة"})[e]||e}function m(e){return({ACTIVE:"نشط",LATE:"متأخر",INACTIVE:"غير ملتزم",SUSPENDED:"موقوف مؤقتاً",ARCHIVED:"مؤرشف"})[e]||e}function x(e){return({ACTIVE:"bg-green-100 text-green-800",LATE:"bg-yellow-100 text-yellow-800",INACTIVE:"bg-red-100 text-red-800",SUSPENDED:"bg-orange-100 text-orange-800",ARCHIVED:"bg-gray-100 text-gray-800"})[e]||"bg-gray-100 text-gray-800"}},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var s=r(95155),a=r(12115),l=r(59434);let i=a.forwardRef((e,t)=>{let{className:r,type:a,...i}=e;return(0,s.jsx)("input",{type:a,className:(0,l.cn)("flex h-11 w-full rounded-xl border-2 border-secondary-200 bg-white px-4 py-3 text-sm font-medium text-secondary-700 transition-all duration-200 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-secondary-400 focus-visible:outline-none focus-visible:border-primary-500 focus-visible:ring-4 focus-visible:ring-primary-500/20 disabled:cursor-not-allowed disabled:opacity-50 hover:border-secondary-300",r),ref:t,...i})});i.displayName="Input"},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>i,aR:()=>n});var s=r(95155),a=r(12115),l=r(59434);let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("rounded-xl border border-secondary-200 bg-white text-secondary-700 shadow-lg hover:shadow-xl transition-all duration-200 hover:-translate-y-1 backdrop-blur-sm",r),...a})});i.displayName="Card";let n=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6 bg-gradient-to-r from-secondary-50 to-secondary-100 rounded-t-xl border-b border-secondary-200",r),...a})});n.displayName="CardHeader";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,l.cn)("text-xl font-bold leading-none tracking-tight text-secondary-800",r),...a})});o.displayName="CardTitle";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-secondary-600 font-medium",r),...a})});d.displayName="CardDescription";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-4",r),...a})});c.displayName="CardContent",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0 bg-gradient-to-r from-secondary-50 to-secondary-100 rounded-b-xl border-t border-secondary-200",r),...a})}).displayName="CardFooter"}},e=>{var t=t=>e(e.s=t);e.O(0,[1778,7879,8441,1684,7358],()=>t(56969)),_N_E=e.O()}]);