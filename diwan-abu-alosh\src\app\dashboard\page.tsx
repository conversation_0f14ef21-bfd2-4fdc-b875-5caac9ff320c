import { prisma } from '@/lib/prisma'
import { formatCurrency } from '@/lib/utils'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Users, TrendingUp, TrendingDown, DollarSign, Image, Bell, Home } from 'lucide-react'

async function getDashboardStats() {
  const [
    totalMembers,
    activeMembers,
    totalIncomes,
    totalExpenses,
    recentIncomes,
    recentExpenses,
    totalPhotos,
    unreadNotifications,
  ] = await Promise.all([
    prisma.member.count(),
    prisma.member.count({ where: { status: 'ACTIVE' } }),
    prisma.income.aggregate({ _sum: { amount: true } }),
    prisma.expense.aggregate({ _sum: { amount: true } }),
    prisma.income.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      include: { member: true },
    }),
    prisma.expense.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
    }),
    prisma.galleryPhoto.count().catch(() => 0), // في حالة عدم وجود جدول الصور بعد
    prisma.notification.count({ where: { isRead: false } }).catch(() => 0), // في حالة عدم وجود جدول الإشعارات بعد
  ])

  const totalIncomesAmount = totalIncomes._sum.amount || 0
  const totalExpensesAmount = totalExpenses._sum.amount || 0
  const balance = totalIncomesAmount - totalExpensesAmount

  return {
    totalMembers,
    activeMembers,
    totalIncomesAmount,
    totalExpensesAmount,
    balance,
    recentIncomes,
    recentExpenses,
    totalPhotos,
    unreadNotifications,
  }
}

export default async function DashboardPage() {
  const stats = await getDashboardStats()

  return (
    <div className="space-y-8">
      {/* رأس الصفحة المحسن */}
      <div className="text-center mb-8">
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl shadow-2xl p-10 text-white relative overflow-hidden">
          {/* خلفية متحركة */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-30 animate-pulse"></div>

          {/* المحتوى */}
          <div className="relative z-10">
            <div className="inline-flex items-center justify-center w-20 h-20 rounded-full mb-6 bg-white bg-opacity-20 backdrop-blur-sm">
              <Home className="w-10 h-10 text-white" />
            </div>

            <h1 className="text-5xl font-black mb-4 text-white">
              لوحة التحكم
            </h1>

            <p className="text-xl font-semibold mb-6 text-blue-100">
              نظرة عامة شاملة على إدارة ديوان أبو علوش
            </p>

            <div className="flex items-center justify-center space-x-2 space-x-reverse">
              <div className="h-1 w-16 rounded-full bg-white bg-opacity-60"></div>
              <div className="h-1 w-8 rounded-full bg-white bg-opacity-40"></div>
              <div className="h-1 w-16 rounded-full bg-white bg-opacity-60"></div>
            </div>
          </div>
        </div>
      </div>

      {/* إحصائيات سريعة محسنة */}
      <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
        {/* بطاقة الأعضاء */}
        <Card className="group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500 to-blue-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>

          <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-1 rounded-t-xl"></div>

          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative z-10">
            <CardTitle className="text-sm font-bold" style={{ color: '#333333' }}>إجمالي الأعضاء</CardTitle>
            <div className="p-4 rounded-2xl shadow-lg" style={{ backgroundColor: '#007bff' }}>
              <Users className="h-7 w-7 text-white" />
            </div>
          </CardHeader>

          <CardContent className="relative z-10">
            <div className="text-4xl font-black mb-2" style={{ color: '#191970' }}>
              {stats.totalMembers}
            </div>
            <p className="text-sm font-semibold" style={{ color: '#6c757d' }}>
              {stats.activeMembers} عضو نشط
            </p>
          </CardContent>
        </Card>

        {/* بطاقة الإيرادات */}
        <Card className="group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative">
          <div className="absolute inset-0 bg-gradient-to-br from-green-500 to-green-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>

          <div className="bg-gradient-to-r from-green-500 to-green-600 p-1 rounded-t-xl"></div>

          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative z-10">
            <CardTitle className="text-sm font-bold" style={{ color: '#333333' }}>إجمالي الإيرادات</CardTitle>
            <div className="p-4 rounded-2xl shadow-lg" style={{ backgroundColor: '#28a745' }}>
              <TrendingUp className="h-7 w-7 text-white" />
            </div>
          </CardHeader>

          <CardContent className="relative z-10">
            <div className="text-4xl font-black mb-2" style={{ color: '#191970' }}>
              {formatCurrency(stats.totalIncomesAmount)}
            </div>
            <p className="text-sm font-semibold" style={{ color: '#6c757d' }}>
              إجمالي المبالغ المحصلة
            </p>
          </CardContent>
        </Card>

        {/* بطاقة المصروفات */}
        <Card className="group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative">
          <div className="absolute inset-0 bg-gradient-to-br from-red-500 to-red-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>

          <div className="bg-gradient-to-r from-red-500 to-red-600 p-1 rounded-t-xl"></div>

          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative z-10">
            <CardTitle className="text-sm font-bold" style={{ color: '#333333' }}>إجمالي المصروفات</CardTitle>
            <div className="p-4 rounded-2xl shadow-lg" style={{ backgroundColor: '#dc3545' }}>
              <TrendingDown className="h-7 w-7 text-white" />
            </div>
          </CardHeader>

          <CardContent className="relative z-10">
            <div className="text-4xl font-black mb-2" style={{ color: '#191970' }}>
              {formatCurrency(stats.totalExpensesAmount)}
            </div>
            <p className="text-sm font-semibold" style={{ color: '#6c757d' }}>
              إجمالي المبالغ المصروفة
            </p>
          </CardContent>
        </Card>

        {/* بطاقة الرصيد */}
        <Card className="group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative">
          <div className={`absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-10 transition-opacity duration-500 ${stats.balance >= 0 ? 'from-green-500 to-green-600' : 'from-slate-700 to-slate-800'}`}></div>

          <div className={`bg-gradient-to-r p-1 rounded-t-xl ${stats.balance >= 0 ? 'from-green-500 to-green-600' : 'from-slate-700 to-slate-800'}`}></div>

          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative z-10">
            <CardTitle className="text-sm font-bold" style={{ color: '#333333' }}>الرصيد الحالي</CardTitle>
            <div className="p-4 rounded-2xl shadow-lg" style={{ backgroundColor: stats.balance >= 0 ? '#28a745' : '#191970' }}>
              <DollarSign className="h-7 w-7 text-white" />
            </div>
          </CardHeader>

          <CardContent className="relative z-10">
            <div className="text-4xl font-black mb-2" style={{ color: '#191970' }}>
              {formatCurrency(stats.balance)}
            </div>
            <p className="text-sm font-semibold" style={{ color: '#6c757d' }}>
              {stats.balance >= 0 ? 'رصيد إيجابي' : 'عجز في الرصيد'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* إحصائيات إضافية محسنة */}
      <div className="grid grid-cols-1 gap-8 sm:grid-cols-2">
        {/* بطاقة معرض الصور */}
        <Card className="group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500 to-purple-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>

          <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-1 rounded-t-xl"></div>

          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative z-10">
            <CardTitle className="text-sm font-bold" style={{ color: '#333333' }}>معرض الصور</CardTitle>
            <div className="p-4 rounded-2xl shadow-lg" style={{ backgroundColor: '#800020' }}>
              <Image className="h-7 w-7 text-white" alt="Gallery icon" />
            </div>
          </CardHeader>

          <CardContent className="relative z-10">
            <div className="text-4xl font-black mb-2" style={{ color: '#191970' }}>
              {stats.totalPhotos || 0}
            </div>
            <p className="text-sm font-semibold" style={{ color: '#6c757d' }}>
              صورة في المعرض
            </p>
          </CardContent>
        </Card>

        {/* بطاقة الإشعارات */}
        <Card className="group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative">
          <div className="absolute inset-0 bg-gradient-to-br from-indigo-500 to-indigo-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>

          <div className="bg-gradient-to-r from-indigo-500 to-indigo-600 p-1 rounded-t-xl"></div>

          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative z-10">
            <CardTitle className="text-sm font-bold" style={{ color: '#333333' }}>الإشعارات</CardTitle>
            <div className="p-4 rounded-2xl shadow-lg" style={{ backgroundColor: '#007bff' }}>
              <Bell className="h-7 w-7 text-white" />
            </div>
          </CardHeader>

          <CardContent className="relative z-10">
            <div className="text-4xl font-black mb-2" style={{ color: '#191970' }}>
              {stats.unreadNotifications || 0}
            </div>
            <p className="text-sm font-semibold" style={{ color: '#6c757d' }}>
              إشعار غير مقروء
            </p>
          </CardContent>
        </Card>
      </div>

      {/* قسم النشاطات الحديثة المحسن */}
      <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
        {/* آخر الإيرادات */}
        <Card className="border-0 shadow-2xl bg-white overflow-hidden">
          <CardHeader className="border-b-0 bg-gradient-to-r from-green-500 to-green-600 text-white p-6">
            <CardTitle className="text-xl font-bold flex items-center gap-3">
              <div className="p-3 rounded-xl bg-white bg-opacity-20 backdrop-blur-sm">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              آخر الإيرادات
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-4">
              {stats.recentIncomes.length > 0 ? (
                stats.recentIncomes.map((income) => (
                  <div key={income.id} className="group flex items-center justify-between p-5 rounded-2xl border-2 border-gray-100 hover:border-green-200 hover:shadow-lg transition-all duration-300 bg-gradient-to-r from-white to-green-50">
                    <div className="flex-1">
                      <p className="text-base font-bold mb-1" style={{ color: '#333333' }}>{income.source}</p>
                      <p className="text-sm font-medium" style={{ color: '#6c757d' }}>
                        {income.member?.name || 'غير محدد'}
                      </p>
                    </div>
                    <div className="text-base font-black text-white px-4 py-2 rounded-xl shadow-lg" style={{ backgroundColor: '#28a745' }}>
                      {formatCurrency(income.amount)}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-12">
                  <div className="inline-flex items-center justify-center w-16 h-16 rounded-full mb-4" style={{ backgroundColor: '#f1f5f9' }}>
                    <TrendingUp className="h-8 w-8" style={{ color: '#cbd5e1' }} />
                  </div>
                  <p className="text-base font-semibold" style={{ color: '#6c757d' }}>لا توجد إيرادات حديثة</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* آخر المصروفات */}
        <Card className="border-0 shadow-2xl bg-white overflow-hidden">
          <CardHeader className="border-b-0 bg-gradient-to-r from-red-500 to-red-600 text-white p-6">
            <CardTitle className="text-xl font-bold flex items-center gap-3">
              <div className="p-3 rounded-xl bg-white bg-opacity-20 backdrop-blur-sm">
                <TrendingDown className="h-6 w-6 text-white" />
              </div>
              آخر المصروفات
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-4">
              {stats.recentExpenses.length > 0 ? (
                stats.recentExpenses.map((expense) => (
                  <div key={expense.id} className="group flex items-center justify-between p-5 rounded-2xl border-2 border-gray-100 hover:border-red-200 hover:shadow-lg transition-all duration-300 bg-gradient-to-r from-white to-red-50">
                    <div className="flex-1">
                      <p className="text-base font-bold mb-1" style={{ color: '#333333' }}>{expense.description}</p>
                      <p className="text-sm font-medium" style={{ color: '#6c757d' }}>
                        {expense.recipient || 'غير محدد'}
                      </p>
                    </div>
                    <div className="text-base font-black text-white px-4 py-2 rounded-xl shadow-lg" style={{ backgroundColor: '#dc3545' }}>
                      {formatCurrency(expense.amount)}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-12">
                  <div className="inline-flex items-center justify-center w-16 h-16 rounded-full mb-4" style={{ backgroundColor: '#f1f5f9' }}>
                    <TrendingDown className="h-8 w-8" style={{ color: '#cbd5e1' }} />
                  </div>
                  <p className="text-base font-semibold" style={{ color: '#6c757d' }}>لا توجد مصروفات حديثة</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>


    </div>
  )
}
