'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
// import { Textarea } from '@/components/ui/textarea'
import DetailedPermissions from './detailed-permissions'
import {
  Shield,
  Lock,
  Clock,
  Users,
  CheckCircle,
  History,
  UserCheck,
  Settings,
  UserPlus,
  Edit,
  Trash2,
  Mail,
  Phone
} from 'lucide-react'

interface SecuritySettingsProps {
  settings: any
  onChange: (settings: any) => void
  canEdit: boolean
}

interface SecuritySettingsData {
  // سياسة كلمات المرور
  passwordPolicy: {
    minLength: number
    requireUppercase: boolean
    requireLowercase: boolean
    requireNumbers: boolean
    requireSpecialChars: boolean
    preventReuse: number
    expirationDays: number
  }

  // إعدادات الجلسة
  sessionSettings: {
    timeout: number // بالدقائق
    maxConcurrentSessions: number
    requireReauth: boolean
    rememberMe: boolean
    rememberMeDuration: number // بالأيام
  }

  // إعدادات تسجيل الدخول
  loginSettings: {
    maxFailedAttempts: number
    lockoutDuration: number // بالدقائق
    enableCaptcha: boolean
    enableTwoFactor: boolean
    allowedIPs: string[]
    blockedIPs: string[]
  }

  // إعدادات التدقيق والسجلات
  auditSettings: {
    enableAuditLog: boolean
    logLoginAttempts: boolean
    logDataChanges: boolean
    logSystemEvents: boolean
    retentionDays: number
    enableRealTimeAlerts: boolean
  }

  // إعدادات الصلاحيات
  permissionSettings: {
    defaultRole: string
    allowSelfRegistration: boolean
    requireAdminApproval: boolean
    enableRoleHierarchy: boolean
    maxUsersPerRole: {
      ADMIN: number
      DATA_ENTRY: number
      VIEWER: number
    }
  }

  // إعدادات الأمان المتقدمة
  advancedSecurity: {
    enableEncryption: boolean
    enableSSL: boolean
    enableCSRF: boolean
    enableXSS: boolean
    enableSQLInjection: boolean
    enableRateLimit: boolean
    rateLimitRequests: number
    rateLimitWindow: number // بالدقائق
  }
}

const defaultSettings: SecuritySettingsData = {
  passwordPolicy: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: false,
    preventReuse: 5,
    expirationDays: 90
  },

  sessionSettings: {
    timeout: 30,
    maxConcurrentSessions: 3,
    requireReauth: false,
    rememberMe: true,
    rememberMeDuration: 30
  },

  loginSettings: {
    maxFailedAttempts: 5,
    lockoutDuration: 15,
    enableCaptcha: false,
    enableTwoFactor: false,
    allowedIPs: [],
    blockedIPs: []
  },

  auditSettings: {
    enableAuditLog: true,
    logLoginAttempts: true,
    logDataChanges: true,
    logSystemEvents: true,
    retentionDays: 365,
    enableRealTimeAlerts: true
  },

  permissionSettings: {
    defaultRole: 'VIEWER',
    allowSelfRegistration: false,
    requireAdminApproval: true,
    enableRoleHierarchy: true,
    maxUsersPerRole: {
      ADMIN: 3,
      DATA_ENTRY: 10,
      VIEWER: 100
    }
  },

  advancedSecurity: {
    enableEncryption: true,
    enableSSL: true,
    enableCSRF: true,
    enableXSS: true,
    enableSQLInjection: true,
    enableRateLimit: true,
    rateLimitRequests: 100,
    rateLimitWindow: 15
  }
}

export default function SecuritySettings({ settings, onChange, canEdit }: SecuritySettingsProps) {
  const [localSettings, setLocalSettings] = useState<SecuritySettingsData>(defaultSettings)
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [showUserManagement, setShowUserManagement] = useState(false)
  const [users, setUsers] = useState<any[]>([])
  const [showAddUser, setShowAddUser] = useState(false)
  const [newUser, setNewUser] = useState({
    name: '',
    email: '',
    phone: '',
    role: 'VIEWER',
    password: '',
    confirmPassword: ''
  })
  const [editingUser, setEditingUser] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [showDetailedPermissions, setShowDetailedPermissions] = useState(false)

  useEffect(() => {
    if (settings) {
      setLocalSettings({ ...defaultSettings, ...settings })
    }
  }, [settings])

  // تحميل المستخدمين
  useEffect(() => {
    if (showUserManagement) {
      loadUsers()
    }
  }, [showUserManagement])

  const loadUsers = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/users')
      if (response.ok) {
        const data = await response.json()
        setUsers(data)
      }
    } catch (error) {
      console.error('خطأ في تحميل المستخدمين:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (key: string, value: any) => {
    const keys = key.split('.')
    let newSettings = { ...localSettings }

    if (keys.length === 1) {
      newSettings = { ...newSettings, [keys[0]]: value }
    } else if (keys.length === 2) {
      newSettings = {
        ...newSettings,
        [keys[0]]: {
          ...newSettings[keys[0] as keyof SecuritySettingsData],
          [keys[1]]: value
        }
      }
    } else if (keys.length === 3) {
      newSettings = {
        ...newSettings,
        [keys[0]]: {
          ...newSettings[keys[0] as keyof SecuritySettingsData],
          [keys[1]]: {
            ...(newSettings[keys[0] as keyof SecuritySettingsData] as any)[keys[1]],
            [keys[2]]: value
          }
        }
      }
    }

    setLocalSettings(newSettings)
    onChange(newSettings)
  }

  // إضافة مستخدم جديد
  const handleAddUser = async () => {
    if (!newUser.name || !newUser.email || !newUser.password) {
      alert('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    if (newUser.password !== newUser.confirmPassword) {
      alert('كلمات المرور غير متطابقة')
      return
    }

    try {
      setLoading(true)
      const response = await fetch('/api/admin/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newUser.name,
          email: newUser.email,
          phone: newUser.phone,
          role: newUser.role,
          password: newUser.password
        }),
      })

      if (response.ok) {
        alert('تم إضافة المستخدم بنجاح')
        setNewUser({
          name: '',
          email: '',
          phone: '',
          role: 'VIEWER',
          password: '',
          confirmPassword: ''
        })
        setShowAddUser(false)
        loadUsers()
      } else {
        const error = await response.json()
        alert(error.message || 'فشل في إضافة المستخدم')
      }
    } catch (error) {
      console.error('خطأ في إضافة المستخدم:', error)
      alert('حدث خطأ أثناء إضافة المستخدم')
    } finally {
      setLoading(false)
    }
  }

  // تحديث مستخدم
  const handleUpdateUser = async (userId: string, updates: any) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      })

      if (response.ok) {
        alert('تم تحديث المستخدم بنجاح')
        setEditingUser(null)
        loadUsers()
      } else {
        const error = await response.json()
        alert(error.message || 'فشل في تحديث المستخدم')
      }
    } catch (error) {
      console.error('خطأ في تحديث المستخدم:', error)
      alert('حدث خطأ أثناء تحديث المستخدم')
    } finally {
      setLoading(false)
    }
  }

  // حذف مستخدم
  const handleDeleteUser = async (userId: string, userName: string) => {
    if (!confirm(`هل أنت متأكد من حذف المستخدم "${userName}"؟\n\nتحذير: سيتم حذف جميع البيانات المرتبطة بهذا المستخدم نهائياً ولا يمكن التراجع عن هذا الإجراء.`)) {
      return
    }

    try {
      setLoading(true)
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        alert('تم حذف المستخدم بنجاح')
        loadUsers()
      } else {
        const error = await response.json()
        alert(error.message || 'فشل في حذف المستخدم')
      }
    } catch (error) {
      console.error('خطأ في حذف المستخدم:', error)
      alert('حدث خطأ أثناء حذف المستخدم')
    } finally {
      setLoading(false)
    }
  }

  const getRoleText = (role: string) => {
    switch (role) {
      case 'ADMIN': return 'مدير'
      case 'DATA_ENTRY': return 'مدخل بيانات'
      case 'VIEWER': return 'مطلع'
      case 'MEMBER_VIEWER': return 'مطلع على عضو'
      case 'GALLERY_VIEWER': return 'مطلع على المعرض'
      case 'MEMBER': return 'عضو'
      default: return role
    }
  }

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'ADMIN': return 'bg-red-100 text-red-800'
      case 'DATA_ENTRY': return 'bg-blue-100 text-blue-800'
      case 'VIEWER': return 'bg-green-100 text-green-800'
      case 'MEMBER_VIEWER': return 'bg-purple-100 text-purple-800'
      case 'GALLERY_VIEWER': return 'bg-orange-100 text-orange-800'
      case 'MEMBER': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-6">
      {/* Password policy */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lock className="w-5 h-5 text-blue-600" />
            سياسة كلمات المرور
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="minLength">الحد الأدنى لطول كلمة المرور</Label>
              <Select
                value={localSettings.passwordPolicy.minLength.toString()}
                onValueChange={(value) => handleChange('passwordPolicy.minLength', parseInt(value))}
                disabled={!canEdit}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="6">6 أحرف</SelectItem>
                  <SelectItem value="8">8 أحرف</SelectItem>
                  <SelectItem value="10">10 أحرف</SelectItem>
                  <SelectItem value="12">12 حرف</SelectItem>
                  <SelectItem value="16">16 حرف</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="expirationDays">انتهاء صلاحية كلمة المرور (يوم)</Label>
              <Select
                value={localSettings.passwordPolicy.expirationDays.toString()}
                onValueChange={(value) => handleChange('passwordPolicy.expirationDays', parseInt(value))}
                disabled={!canEdit}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="30">30 يوم</SelectItem>
                  <SelectItem value="60">60 يوم</SelectItem>
                  <SelectItem value="90">90 يوم</SelectItem>
                  <SelectItem value="180">180 يوم</SelectItem>
                  <SelectItem value="365">سنة واحدة</SelectItem>
                  <SelectItem value="0">بدون انتهاء</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>يجب أن تحتوي على أحرف كبيرة</Label>
                <p className="text-sm text-gray-600">A-Z</p>
              </div>
              <Switch
                checked={localSettings.passwordPolicy.requireUppercase}
                onCheckedChange={(checked) => handleChange('passwordPolicy.requireUppercase', checked)}
                disabled={!canEdit}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>يجب أن تحتوي على أحرف صغيرة</Label>
                <p className="text-sm text-gray-600">a-z</p>
              </div>
              <Switch
                checked={localSettings.passwordPolicy.requireLowercase}
                onCheckedChange={(checked) => handleChange('passwordPolicy.requireLowercase', checked)}
                disabled={!canEdit}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>يجب أن تحتوي على أرقام</Label>
                <p className="text-sm text-gray-600">0-9</p>
              </div>
              <Switch
                checked={localSettings.passwordPolicy.requireNumbers}
                onCheckedChange={(checked) => handleChange('passwordPolicy.requireNumbers', checked)}
                disabled={!canEdit}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>يجب أن تحتوي على رموز خاصة</Label>
                <p className="text-sm text-gray-600">!@#$%^&*</p>
              </div>
              <Switch
                checked={localSettings.passwordPolicy.requireSpecialChars}
                onCheckedChange={(checked) => handleChange('passwordPolicy.requireSpecialChars', checked)}
                disabled={!canEdit}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="preventReuse">منع إعادة استخدام كلمات المرور السابقة</Label>
            <Select
              value={localSettings.passwordPolicy.preventReuse.toString()}
              onValueChange={(value) => handleChange('passwordPolicy.preventReuse', parseInt(value))}
              disabled={!canEdit}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0">السماح بإعادة الاستخدام</SelectItem>
                <SelectItem value="3">آخر 3 كلمات مرور</SelectItem>
                <SelectItem value="5">آخر 5 كلمات مرور</SelectItem>
                <SelectItem value="10">آخر 10 كلمات مرور</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Session settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5 text-green-600" />
            إعدادات الجلسة
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>مدة انتهاء الجلسة (دقيقة)</Label>
              <Select
                value={localSettings.sessionSettings.timeout.toString()}
                onValueChange={(value) => handleChange('sessionSettings.timeout', parseInt(value))}
                disabled={!canEdit}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="15">15 دقيقة</SelectItem>
                  <SelectItem value="30">30 دقيقة</SelectItem>
                  <SelectItem value="60">ساعة واحدة</SelectItem>
                  <SelectItem value="120">ساعتان</SelectItem>
                  <SelectItem value="480">8 ساعات</SelectItem>
                  <SelectItem value="1440">24 ساعة</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>الحد الأقصى للجلسات المتزامنة</Label>
              <Select
                value={localSettings.sessionSettings.maxConcurrentSessions.toString()}
                onValueChange={(value) => handleChange('sessionSettings.maxConcurrentSessions', parseInt(value))}
                disabled={!canEdit}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">جلسة واحدة فقط</SelectItem>
                  <SelectItem value="2">جلستان</SelectItem>
                  <SelectItem value="3">3 جلسات</SelectItem>
                  <SelectItem value="5">5 جلسات</SelectItem>
                  <SelectItem value="10">10 جلسات</SelectItem>
                  <SelectItem value="0">بدون حد</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>تذكرني</Label>
                <p className="text-sm text-gray-600">السماح بحفظ بيانات تسجيل الدخول</p>
              </div>
              <Switch
                checked={localSettings.sessionSettings.rememberMe}
                onCheckedChange={(checked) => handleChange('sessionSettings.rememberMe', checked)}
                disabled={!canEdit}
              />
            </div>

            {localSettings.sessionSettings.rememberMe && (
              <div className="space-y-2">
                <Label>مدة "تذكرني" (يوم)</Label>
                <Select
                  value={localSettings.sessionSettings.rememberMeDuration.toString()}
                  onValueChange={(value) => handleChange('sessionSettings.rememberMeDuration', parseInt(value))}
                  disabled={!canEdit}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7">أسبوع واحد</SelectItem>
                    <SelectItem value="30">شهر واحد</SelectItem>
                    <SelectItem value="90">3 أشهر</SelectItem>
                    <SelectItem value="365">سنة واحدة</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>إعادة المصادقة للعمليات الحساسة</Label>
                <p className="text-sm text-gray-600">طلب كلمة المرور مرة أخرى</p>
              </div>
              <Switch
                checked={localSettings.sessionSettings.requireReauth}
                onCheckedChange={(checked) => handleChange('sessionSettings.requireReauth', checked)}
                disabled={!canEdit}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Login settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserCheck className="w-5 h-5 text-purple-600" />
            إعدادات تسجيل الدخول
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>الحد الأقصى لمحاولات الدخول الفاشلة</Label>
              <Select
                value={localSettings.loginSettings.maxFailedAttempts.toString()}
                onValueChange={(value) => handleChange('loginSettings.maxFailedAttempts', parseInt(value))}
                disabled={!canEdit}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="3">3 محاولات</SelectItem>
                  <SelectItem value="5">5 محاولات</SelectItem>
                  <SelectItem value="10">10 محاولات</SelectItem>
                  <SelectItem value="0">بدون حد</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>مدة الحظر (دقيقة)</Label>
              <Select
                value={localSettings.loginSettings.lockoutDuration.toString()}
                onValueChange={(value) => handleChange('loginSettings.lockoutDuration', parseInt(value))}
                disabled={!canEdit}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 دقائق</SelectItem>
                  <SelectItem value="15">15 دقيقة</SelectItem>
                  <SelectItem value="30">30 دقيقة</SelectItem>
                  <SelectItem value="60">ساعة واحدة</SelectItem>
                  <SelectItem value="1440">24 ساعة</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>تفعيل CAPTCHA</Label>
                <p className="text-sm text-gray-600">التحقق من أنك لست روبوت</p>
              </div>
              <Switch
                checked={localSettings.loginSettings.enableCaptcha}
                onCheckedChange={(checked) => handleChange('loginSettings.enableCaptcha', checked)}
                disabled={!canEdit}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>المصادقة الثنائية</Label>
                <p className="text-sm text-gray-600">طبقة أمان إضافية</p>
              </div>
              <Switch
                checked={localSettings.loginSettings.enableTwoFactor}
                onCheckedChange={(checked) => handleChange('loginSettings.enableTwoFactor', checked)}
                disabled={!canEdit}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* إعدادات التدقيق */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="w-5 h-5 text-orange-600" />
            إعدادات التدقيق والسجلات
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>تفعيل سجل التدقيق</Label>
                <p className="text-sm text-gray-600">تسجيل جميع العمليات</p>
              </div>
              <Switch
                checked={localSettings.auditSettings.enableAuditLog}
                onCheckedChange={(checked) => handleChange('auditSettings.enableAuditLog', checked)}
                disabled={!canEdit}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>تسجيل محاولات الدخول</Label>
                <p className="text-sm text-gray-600">الناجحة والفاشلة</p>
              </div>
              <Switch
                checked={localSettings.auditSettings.logLoginAttempts}
                onCheckedChange={(checked) => handleChange('auditSettings.logLoginAttempts', checked)}
                disabled={!canEdit || !localSettings.auditSettings.enableAuditLog}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>تسجيل تغييرات البيانات</Label>
                <p className="text-sm text-gray-600">إضافة، تعديل، حذف</p>
              </div>
              <Switch
                checked={localSettings.auditSettings.logDataChanges}
                onCheckedChange={(checked) => handleChange('auditSettings.logDataChanges', checked)}
                disabled={!canEdit || !localSettings.auditSettings.enableAuditLog}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>تسجيل أحداث النظام</Label>
                <p className="text-sm text-gray-600">بدء التشغيل، الأخطاء، التحديثات</p>
              </div>
              <Switch
                checked={localSettings.auditSettings.logSystemEvents}
                onCheckedChange={(checked) => handleChange('auditSettings.logSystemEvents', checked)}
                disabled={!canEdit || !localSettings.auditSettings.enableAuditLog}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>التنبيهات الفورية</Label>
                <p className="text-sm text-gray-600">إشعار فوري للأحداث المهمة</p>
              </div>
              <Switch
                checked={localSettings.auditSettings.enableRealTimeAlerts}
                onCheckedChange={(checked) => handleChange('auditSettings.enableRealTimeAlerts', checked)}
                disabled={!canEdit || !localSettings.auditSettings.enableAuditLog}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label>مدة الاحتفاظ بالسجلات (يوم)</Label>
            <Select
              value={localSettings.auditSettings.retentionDays.toString()}
              onValueChange={(value) => handleChange('auditSettings.retentionDays', parseInt(value))}
              disabled={!canEdit || !localSettings.auditSettings.enableAuditLog}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="30">30 يوم</SelectItem>
                <SelectItem value="90">90 يوم</SelectItem>
                <SelectItem value="180">180 يوم</SelectItem>
                <SelectItem value="365">سنة واحدة</SelectItem>
                <SelectItem value="1095">3 سنوات</SelectItem>
                <SelectItem value="0">دائماً</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* إدارة المستخدمين */}
      <Card className="border-2 border-indigo-100 shadow-lg">
        <CardHeader className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-t-lg">
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-white bg-opacity-20 rounded-lg">
                <UserPlus className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold">إدارة المستخدمين</h3>
                <p className="text-indigo-100 text-sm mt-1">إضافة وتعديل وحذف حسابات المستخدمين</p>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowUserManagement(!showUserManagement)}
              disabled={!canEdit}
              className="bg-white bg-opacity-20 border-white border-opacity-30 text-white hover:bg-white hover:bg-opacity-30"
            >
              {showUserManagement ? 'إخفاء' : 'عرض'} المستخدمين
            </Button>
          </CardTitle>
        </CardHeader>
        {showUserManagement && (
          <CardContent className="space-y-6 bg-gradient-to-br from-gray-50 to-indigo-50">
            {/* زر إضافة مستخدم جديد */}
            <div className="flex justify-between items-center p-6 bg-white rounded-xl shadow-sm border border-gray-100">
              <div>
                <h3 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                  <Users className="w-5 h-5 text-indigo-600" />
                  قائمة المستخدمين
                </h3>
                <p className="text-sm text-gray-600 mt-1">إدارة حسابات المستخدمين وصلاحياتهم في النظام</p>
                <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                  <span>إجمالي المستخدمين: {users.length}</span>
                  <span>•</span>
                  <span>المديرون: {users.filter(u => u.role === 'ADMIN').length}</span>
                  <span>•</span>
                  <span>مدخلو البيانات: {users.filter(u => u.role === 'DATA_ENTRY').length}</span>
                  <span>•</span>
                  <span>المطلعون: {users.filter(u => u.role === 'VIEWER').length}</span>
                </div>
              </div>
              <div className="flex gap-3">
                <Button
                  onClick={() => setShowDetailedPermissions(true)}
                  disabled={!canEdit || loading}
                  variant="outline"
                  className="border-indigo-300 text-indigo-600 hover:bg-indigo-50"
                >
                  <Settings className="w-4 h-4 ml-2" />
                  الصلاحيات المفصلة
                </Button>
                <Button
                  onClick={() => setShowAddUser(true)}
                  disabled={!canEdit || loading}
                  className="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <UserPlus className="w-4 h-4 ml-2" />
                  إضافة مستخدم
                </Button>
              </div>
            </div>

            {/* قائمة المستخدمين */}
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
              </div>
            ) : (
              <div className="space-y-4">
                {users.length === 0 ? (
                  <div className="text-center py-12 bg-white rounded-xl border-2 border-dashed border-gray-200">
                    <div className="w-20 h-20 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Users className="w-10 h-10 text-indigo-400" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">لا توجد مستخدمين مسجلين</h3>
                    <p className="text-gray-600 mb-4">ابدأ بإضافة أول مستخدم في النظام</p>
                    <Button
                      onClick={() => setShowAddUser(true)}
                      disabled={!canEdit || loading}
                      className="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white"
                    >
                      <UserPlus className="w-4 h-4 ml-2" />
                      إضافة أول مستخدم
                    </Button>
                  </div>
                ) : (
                  users.map((user) => (
                    <div key={user.id} className="group flex items-center justify-between p-6 border border-gray-200 rounded-xl bg-white hover:bg-gradient-to-r hover:from-indigo-50 hover:to-blue-50 hover:border-indigo-200 transition-all duration-300 shadow-sm hover:shadow-md">
                      <div className="flex-1">
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                            <span className="text-white font-bold text-lg">
                              {user.name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <div>
                            <h4 className="font-bold text-gray-900 text-lg group-hover:text-indigo-700 transition-colors duration-300">{user.name}</h4>
                            <div className="flex items-center gap-6 text-sm text-gray-600 mt-1">
                              <span className="flex items-center gap-2">
                                <Mail className="w-4 h-4 text-indigo-500" />
                                {user.email}
                              </span>
                              {user.phone && (
                                <span className="flex items-center gap-2">
                                  <Phone className="w-4 h-4 text-green-500" />
                                  {user.phone}
                                </span>
                              )}
                              <span className="text-xs text-gray-500">
                                انضم في {new Date(user.createdAt).toLocaleDateString('ar-SA')}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <Badge className={`${getRoleBadgeColor(user.role)} px-3 py-1 text-sm font-semibold`}>
                          {getRoleText(user.role)}
                        </Badge>
                        {canEdit && (
                          <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setEditingUser(user)}
                              disabled={loading}
                              className="bg-blue-50 border-blue-200 text-blue-600 hover:bg-blue-100 hover:border-blue-300"
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteUser(user.id, user.name)}
                              disabled={loading}
                              className="bg-red-50 border-red-200 text-red-600 hover:bg-red-100 hover:border-red-300"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            )}
          </CardContent>
        )}
      </Card>

      {/* إعدادات الصلاحيات */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5 text-red-600" />
            إعدادات الصلاحيات
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>الدور الافتراضي للمستخدمين الجدد</Label>
            <Select
              value={localSettings.permissionSettings.defaultRole}
              onValueChange={(value) => handleChange('permissionSettings.defaultRole', value)}
              disabled={!canEdit}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="VIEWER">مطلع (عرض فقط)</SelectItem>
                <SelectItem value="DATA_ENTRY">مدخل بيانات</SelectItem>
                <SelectItem value="ADMIN">مدير (غير مستحسن)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>السماح بالتسجيل الذاتي</Label>
                <p className="text-sm text-gray-600">المستخدمون يمكنهم إنشاء حسابات</p>
              </div>
              <Switch
                checked={localSettings.permissionSettings.allowSelfRegistration}
                onCheckedChange={(checked) => handleChange('permissionSettings.allowSelfRegistration', checked)}
                disabled={!canEdit}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>يتطلب موافقة المدير</Label>
                <p className="text-sm text-gray-600">الحسابات الجديدة تحتاج موافقة</p>
              </div>
              <Switch
                checked={localSettings.permissionSettings.requireAdminApproval}
                onCheckedChange={(checked) => handleChange('permissionSettings.requireAdminApproval', checked)}
                disabled={!canEdit || !localSettings.permissionSettings.allowSelfRegistration}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>تفعيل التسلسل الهرمي للأدوار</Label>
                <p className="text-sm text-gray-600">المدير يمكنه إدارة جميع الأدوار</p>
              </div>
              <Switch
                checked={localSettings.permissionSettings.enableRoleHierarchy}
                onCheckedChange={(checked) => handleChange('permissionSettings.enableRoleHierarchy', checked)}
                disabled={!canEdit}
              />
            </div>
          </div>

          <div className="space-y-3">
            <Label>الحد الأقصى للمستخدمين لكل دور</Label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="maxAdmins">المديرون</Label>
                <Input
                  id="maxAdmins"
                  type="number"
                  min="1"
                  max="10"
                  value={localSettings.permissionSettings.maxUsersPerRole.ADMIN}
                  onChange={(e) => handleChange('permissionSettings.maxUsersPerRole.ADMIN', parseInt(e.target.value))}
                  disabled={!canEdit}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="maxDataEntry">مدخلو البيانات</Label>
                <Input
                  id="maxDataEntry"
                  type="number"
                  min="1"
                  max="50"
                  value={localSettings.permissionSettings.maxUsersPerRole.DATA_ENTRY}
                  onChange={(e) => handleChange('permissionSettings.maxUsersPerRole.DATA_ENTRY', parseInt(e.target.value))}
                  disabled={!canEdit}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="maxViewers">المطلعون</Label>
                <Input
                  id="maxViewers"
                  type="number"
                  min="1"
                  max="1000"
                  value={localSettings.permissionSettings.maxUsersPerRole.VIEWER}
                  onChange={(e) => handleChange('permissionSettings.maxUsersPerRole.VIEWER', parseInt(e.target.value))}
                  disabled={!canEdit}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* الإعدادات المتقدمة */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Shield className="w-5 h-5 text-red-600" />
              إعدادات الأمان المتقدمة
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAdvanced(!showAdvanced)}
            >
              {showAdvanced ? 'إخفاء' : 'عرض'} المتقدم
            </Button>
          </CardTitle>
        </CardHeader>
        {showAdvanced && (
          <CardContent className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>تشفير البيانات</Label>
                  <p className="text-sm text-gray-600">تشفير البيانات الحساسة</p>
                </div>
                <Switch
                  checked={localSettings.advancedSecurity.enableEncryption}
                  onCheckedChange={(checked) => handleChange('advancedSecurity.enableEncryption', checked)}
                  disabled={!canEdit}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>إجبار SSL/HTTPS</Label>
                  <p className="text-sm text-gray-600">اتصال آمن فقط</p>
                </div>
                <Switch
                  checked={localSettings.advancedSecurity.enableSSL}
                  onCheckedChange={(checked) => handleChange('advancedSecurity.enableSSL', checked)}
                  disabled={!canEdit}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>حماية CSRF</Label>
                  <p className="text-sm text-gray-600">منع هجمات التزوير</p>
                </div>
                <Switch
                  checked={localSettings.advancedSecurity.enableCSRF}
                  onCheckedChange={(checked) => handleChange('advancedSecurity.enableCSRF', checked)}
                  disabled={!canEdit}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>حماية XSS</Label>
                  <p className="text-sm text-gray-600">منع هجمات البرمجة النصية</p>
                </div>
                <Switch
                  checked={localSettings.advancedSecurity.enableXSS}
                  onCheckedChange={(checked) => handleChange('advancedSecurity.enableXSS', checked)}
                  disabled={!canEdit}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>حماية SQL Injection</Label>
                  <p className="text-sm text-gray-600">منع هجمات قواعد البيانات</p>
                </div>
                <Switch
                  checked={localSettings.advancedSecurity.enableSQLInjection}
                  onCheckedChange={(checked) => handleChange('advancedSecurity.enableSQLInjection', checked)}
                  disabled={!canEdit}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>تحديد معدل الطلبات</Label>
                  <p className="text-sm text-gray-600">منع الهجمات المكثفة</p>
                </div>
                <Switch
                  checked={localSettings.advancedSecurity.enableRateLimit}
                  onCheckedChange={(checked) => handleChange('advancedSecurity.enableRateLimit', checked)}
                  disabled={!canEdit}
                />
              </div>
            </div>

            {localSettings.advancedSecurity.enableRateLimit && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="rateLimitRequests">عدد الطلبات المسموحة</Label>
                  <Input
                    id="rateLimitRequests"
                    type="number"
                    min="10"
                    max="1000"
                    value={localSettings.advancedSecurity.rateLimitRequests}
                    onChange={(e) => handleChange('advancedSecurity.rateLimitRequests', parseInt(e.target.value))}
                    disabled={!canEdit}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="rateLimitWindow">النافزة الزمنية (دقيقة)</Label>
                  <Input
                    id="rateLimitWindow"
                    type="number"
                    min="1"
                    max="60"
                    value={localSettings.advancedSecurity.rateLimitWindow}
                    onChange={(e) => handleChange('advancedSecurity.rateLimitWindow', parseInt(e.target.value))}
                    disabled={!canEdit}
                  />
                </div>
              </div>
            )}
          </CardContent>
        )}
      </Card>

      {/* نموذج إضافة مستخدم جديد */}
      {showAddUser && (
        <Card className="border-2 border-indigo-200 bg-gradient-to-br from-indigo-50 to-blue-50 shadow-xl">
          <CardHeader className="bg-gradient-to-r from-indigo-500 to-indigo-600 text-white rounded-t-lg">
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-white bg-opacity-20 rounded-lg">
                  <UserPlus className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold">إضافة مستخدم جديد</h3>
                  <p className="text-indigo-100 text-sm mt-1">إنشاء حساب مستخدم جديد في النظام</p>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAddUser(false)}
                className="bg-white bg-opacity-20 border-white border-opacity-30 text-white hover:bg-white hover:bg-opacity-30"
              >
                إلغاء
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="newUserName">الاسم الكامل *</Label>
                <Input
                  id="newUserName"
                  value={newUser.name}
                  onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}
                  placeholder="أدخل الاسم الكامل"
                  disabled={loading}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="newUserEmail">البريد الإلكتروني *</Label>
                <Input
                  id="newUserEmail"
                  type="email"
                  value={newUser.email}
                  onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                  placeholder="أدخل البريد الإلكتروني"
                  disabled={loading}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="newUserPhone">رقم الهاتف</Label>
                <Input
                  id="newUserPhone"
                  value={newUser.phone}
                  onChange={(e) => setNewUser({ ...newUser, phone: e.target.value })}
                  placeholder="أدخل رقم الهاتف"
                  disabled={loading}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="newUserRole">الصلاحية *</Label>
                <Select
                  value={newUser.role}
                  onValueChange={(value) => setNewUser({ ...newUser, role: value })}
                  disabled={loading}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="VIEWER">مطلع (عرض فقط)</SelectItem>
                    <SelectItem value="DATA_ENTRY">مدخل بيانات</SelectItem>
                    <SelectItem value="MEMBER_VIEWER">مطلع على عضو معين</SelectItem>
                    <SelectItem value="GALLERY_VIEWER">مطلع على المعرض فقط</SelectItem>
                    <SelectItem value="ADMIN">مدير</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="newUserPassword">كلمة المرور *</Label>
                <Input
                  id="newUserPassword"
                  type="password"
                  value={newUser.password}
                  onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
                  placeholder="أدخل كلمة المرور"
                  disabled={loading}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="newUserConfirmPassword">تأكيد كلمة المرور *</Label>
                <Input
                  id="newUserConfirmPassword"
                  type="password"
                  value={newUser.confirmPassword}
                  onChange={(e) => setNewUser({ ...newUser, confirmPassword: e.target.value })}
                  placeholder="أعد إدخال كلمة المرور"
                  disabled={loading}
                />
              </div>
            </div>

            <div className="flex justify-end gap-3 pt-4">
              <Button
                variant="outline"
                onClick={() => setShowAddUser(false)}
                disabled={loading}
              >
                إلغاء
              </Button>
              <Button
                onClick={handleAddUser}
                disabled={loading || !newUser.name || !newUser.email || !newUser.password}
                className="bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                ) : (
                  <UserPlus className="w-4 h-4 ml-2" />
                )}
                إضافة المستخدم
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* نموذج تعديل مستخدم */}
      {editingUser && (
        <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50 shadow-xl">
          <CardHeader className="bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-t-lg">
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-white bg-opacity-20 rounded-lg">
                  <Edit className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold">تعديل المستخدم</h3>
                  <p className="text-blue-100 text-sm mt-1">{editingUser.name}</p>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setEditingUser(null)}
                className="bg-white bg-opacity-20 border-white border-opacity-30 text-white hover:bg-white hover:bg-opacity-30"
              >
                إلغاء
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="editUserName">الاسم الكامل</Label>
                <Input
                  id="editUserName"
                  value={editingUser.name}
                  onChange={(e) => setEditingUser({ ...editingUser, name: e.target.value })}
                  disabled={loading}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="editUserEmail">البريد الإلكتروني</Label>
                <Input
                  id="editUserEmail"
                  type="email"
                  value={editingUser.email}
                  onChange={(e) => setEditingUser({ ...editingUser, email: e.target.value })}
                  disabled={loading}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="editUserPhone">رقم الهاتف</Label>
                <Input
                  id="editUserPhone"
                  value={editingUser.phone || ''}
                  onChange={(e) => setEditingUser({ ...editingUser, phone: e.target.value })}
                  disabled={loading}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="editUserRole">الصلاحية</Label>
                <Select
                  value={editingUser.role}
                  onValueChange={(value) => setEditingUser({ ...editingUser, role: value })}
                  disabled={loading}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="VIEWER">مطلع (عرض فقط)</SelectItem>
                    <SelectItem value="DATA_ENTRY">مدخل بيانات</SelectItem>
                    <SelectItem value="MEMBER_VIEWER">مطلع على عضو معين</SelectItem>
                    <SelectItem value="GALLERY_VIEWER">مطلع على المعرض فقط</SelectItem>
                    <SelectItem value="ADMIN">مدير</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex justify-end gap-3 pt-4">
              <Button
                variant="outline"
                onClick={() => setEditingUser(null)}
                disabled={loading}
              >
                إلغاء
              </Button>
              <Button
                onClick={() => handleUpdateUser(editingUser.id, {
                  name: editingUser.name,
                  email: editingUser.email,
                  phone: editingUser.phone,
                  role: editingUser.role
                })}
                disabled={loading}
                className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                ) : (
                  <CheckCircle className="w-4 h-4 ml-2" />
                )}
                حفظ التغييرات
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* مكون الصلاحيات المفصلة */}
      {showDetailedPermissions && (
        <DetailedPermissions
          users={users}
          onClose={() => setShowDetailedPermissions(false)}
        />
      )}
    </div>
  )
}