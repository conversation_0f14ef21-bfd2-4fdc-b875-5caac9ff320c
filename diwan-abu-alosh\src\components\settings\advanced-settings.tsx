'use client'

import { useState } from 'react'
// import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
// import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Download,
  Upload,
  History,
  RotateCcw,
  FileText,
  AlertTriangle,
  CheckCircle,
  // Settings,
  Database,
  Clock
} from 'lucide-react'
import { toast } from 'sonner'

interface AdvancedSettingsProps {
  onExportSettings: () => void
  onImportSettings: (file: File) => void
  onResetSettings: () => void
  canEdit: boolean
}

export default function AdvancedSettings({ 
  onExportSettings, 
  onImportSettings, 
  onResetSettings, 
  canEdit 
}: AdvancedSettingsProps) {
  const [importFile, setImportFile] = useState<File | null>(null)
  const [showChangeLog, setShowChangeLog] = useState(false)
  const [changeLog] = useState([
    {
      id: 1,
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      user: 'أحمد محمد',
      action: 'تحديث إعدادات المظهر',
      details: 'تغيير اللون الأساسي إلى الأزرق',
      category: 'appearance'
    },
    {
      id: 2,
      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
      user: 'سارة أحمد',
      action: 'تحديث إعدادات الإشعارات',
      details: 'تفعيل إشعارات البريد الإلكتروني',
      category: 'notifications'
    },
    {
      id: 3,
      timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      user: 'محمد علي',
      action: 'تحديث إعدادات الأمان',
      details: 'تغيير سياسة كلمات المرور',
      category: 'security'
    }
  ])

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setImportFile(file)
    }
  }

  const handleImport = () => {
    if (importFile) {
      onImportSettings(importFile)
      setImportFile(null)
      toast.success('تم استيراد الإعدادات بنجاح')
    }
  }

  const handleExport = () => {
    onExportSettings()
    toast.success('تم تصدير الإعدادات بنجاح')
  }

  const handleReset = () => {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟ لا يمكن التراجع عن هذا الإجراء.')) {
      onResetSettings()
      toast.success('تم إعادة تعيين الإعدادات بنجاح')
    }
  }

  const getCategoryBadge = (category: string) => {
    const categories = {
      general: { label: 'عام', color: 'bg-blue-100 text-blue-800' },
      appearance: { label: 'مظهر', color: 'bg-purple-100 text-purple-800' },
      notifications: { label: 'إشعارات', color: 'bg-green-100 text-green-800' },
      security: { label: 'أمان', color: 'bg-red-100 text-red-800' },
      backup: { label: 'نسخ احتياطي', color: 'bg-orange-100 text-orange-800' }
    }
    
    const cat = categories[category as keyof typeof categories] || categories.general
    return <Badge className={cat.color}>{cat.label}</Badge>
  }

  return (
    <div className="space-y-6">
      {/* Import and export settings */}
      <div className="diwan-card">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
            <FileText className="w-5 h-5 text-white" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900">استيراد وتصدير الإعدادات</h3>
        </div>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Export settings */}
            <div className="space-y-3">
              <h4 className="font-medium">تصدير الإعدادات</h4>
              <p className="text-sm text-gray-600">
                احفظ نسخة من إعداداتك الحالية كملف JSON
              </p>
              <Button
                onClick={handleExport}
                disabled={!canEdit}
                className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg"
              >
                <Download className="w-4 h-4 ml-2" />
                تصدير الإعدادات
              </Button>
            </div>

            {/* Import settings */}
            <div className="space-y-3">
              <h4 className="font-medium">استيراد الإعدادات</h4>
              <p className="text-sm text-gray-600">
                استعد إعداداتك من ملف JSON محفوظ مسبقاً
              </p>
              <div className="space-y-2">
                <Input
                  type="file"
                  accept=".json"
                  onChange={handleFileUpload}
                  disabled={!canEdit}
                />
                <Button
                  onClick={handleImport}
                  disabled={!canEdit || !importFile}
                  className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-lg disabled:opacity-50"
                >
                  <Upload className="w-4 h-4 ml-2" />
                  استيراد الإعدادات
                </Button>
              </div>
            </div>
          </div>

          <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-amber-600 mt-0.5" />
              <div>
                <p className="text-amber-800 font-medium">تحذير</p>
                <p className="text-amber-700 text-sm mt-1">
                  استيراد الإعدادات سيستبدل جميع الإعدادات الحالية. 
                  تأكد من تصدير إعداداتك الحالية أولاً كنسخة احتياطية.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Reset settings */}
      <div className="diwan-card">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 bg-gradient-to-r from-red-500 to-red-600 rounded-lg">
            <RotateCcw className="w-5 h-5 text-white" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900">إعادة تعيين الإعدادات</h3>
        </div>
        <div className="space-y-4">
          <p className="text-gray-600">
            إعادة تعيين جميع الإعدادات إلى القيم الافتراضية. 
            هذا الإجراء لا يمكن التراجع عنه.
          </p>
          
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5" />
              <div>
                <p className="text-red-800 font-medium">تحذير شديد</p>
                <p className="text-red-700 text-sm mt-1">
                  سيتم حذف جميع الإعدادات المخصصة وإعادة تعيينها إلى القيم الافتراضية.
                  تأكد من تصدير إعداداتك أولاً إذا كنت تريد الاحتفاظ بها.
                </p>
              </div>
            </div>
          </div>

          <Button
            onClick={handleReset}
            disabled={!canEdit}
            variant="destructive"
            className="w-full md:w-auto"
          >
            <RotateCcw className="w-4 h-4 ml-2" />
            إعادة تعيين جميع الإعدادات
          </Button>
        </div>
      </div>

      {/* Change log */}
      <div className="diwan-card">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
              <History className="w-5 h-5 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">سجل التغييرات</h3>
          </div>
          <Dialog open={showChangeLog} onOpenChange={setShowChangeLog}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  عرض السجل الكامل
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-[50vw] max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>سجل التغييرات الكامل</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  {changeLog.map((entry) => (
                    <div key={entry.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {getCategoryBadge(entry.category)}
                          <span className="font-medium">{entry.action}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <Clock className="w-4 h-4" />
                          {entry.timestamp.toLocaleString('ar-SA')}
                        </div>
                      </div>
                      <p className="text-gray-600 mb-2">{entry.details}</p>
                      <p className="text-sm text-gray-500">بواسطة: {entry.user}</p>
                    </div>
                  ))}
                </div>
              </DialogContent>
          </Dialog>
        </div>
        <div>
          <div className="space-y-3">
            {changeLog.slice(0, 3).map((entry) => (
              <div key={entry.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  {getCategoryBadge(entry.category)}
                  <div>
                    <p className="font-medium text-sm">{entry.action}</p>
                    <p className="text-xs text-gray-600">{entry.details}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-xs text-gray-500">{entry.user}</p>
                  <p className="text-xs text-gray-400">
                    {entry.timestamp.toLocaleDateString('ar-SA')}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* System information */}
      <div className="diwan-card">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
            <Database className="w-5 h-5 text-white" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900">معلومات النظام</h3>
        </div>
        <div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>إصدار النظام</Label>
              <p className="text-sm text-gray-600">1.0.0</p>
            </div>
            <div className="space-y-2">
              <Label>آخر تحديث</Label>
              <p className="text-sm text-gray-600">2024-12-21</p>
            </div>
            <div className="space-y-2">
              <Label>قاعدة البيانات</Label>
              <p className="text-sm text-gray-600">SQLite</p>
            </div>
            <div className="space-y-2">
              <Label>حالة النظام</Label>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="text-sm text-green-600">يعمل بشكل طبيعي</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
