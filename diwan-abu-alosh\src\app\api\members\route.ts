import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { memberSchema } from '@/lib/validations'

// GET - جلب جميع الأعضاء مع البحث والتصفية
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || 'all'
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const includeIncomes = searchParams.get('includeIncomes') === 'true'
    const skip = (page - 1) * limit

    // بناء شروط البحث
    const where: Record<string, unknown> = {}
    
    if (search) {
      where.OR = [
        { name: { contains: search } },
        { phone: { contains: search } },
        { address: { contains: search } },
      ]
    }

    if (status !== 'all') {
      where.status = status
    }

    // تحديد ما يجب تضمينه
    const includeOptions: Record<string, unknown> = {
      _count: {
        select: {
          incomes: true,
        },
      },
    }

    if (includeIncomes) {
      includeOptions.incomes = {
        select: {
          id: true,
          amount: true,
          date: true,
          source: true,
          type: true,
          description: true,
        },
        orderBy: { date: 'desc' },
      }
    } else {
      includeOptions.incomes = {
        select: {
          amount: true,
        },
      }
    }

    // جلب الأعضاء مع العد الكلي
    const [members, total] = await Promise.all([
      prisma.member.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: includeOptions,
      }),
      prisma.member.count({ where }),
    ])

    return NextResponse.json({
      members,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('خطأ في جلب الأعضاء:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في جلب الأعضاء' },
      { status: 500 }
    )
  }
}

// POST - إضافة عضو جديد
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // التحقق من الصلاحيات
    if (session.user.role === 'VIEWER') {
      return NextResponse.json(
        { error: 'ليس لديك صلاحية لإضافة الأعضاء' },
        { status: 403 }
      )
    }

    const body = await request.json()
    
    // التحقق من صحة البيانات
    const validatedData = memberSchema.parse(body)

    // التحقق من عدم تكرار البريد الإلكتروني
    if (validatedData.email) {
      const existingMember = await prisma.member.findFirst({
        where: { email: validatedData.email },
      })
      if (existingMember) {
        return NextResponse.json(
          { error: 'البريد الإلكتروني مستخدم بالفعل' },
          { status: 400 }
        )
      }
    }

    // إنشاء العضو الجديد
    const member = await prisma.member.create({
      data: {
        ...validatedData,
        createdById: session.user.id,
      },
    })

    return NextResponse.json(member, { status: 201 })
  } catch (error: unknown) {
    console.error('خطأ في إضافة العضو:', error)
    
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'بيانات غير صحيحة', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'حدث خطأ في إضافة العضو' },
      { status: 500 }
    )
  }
}
