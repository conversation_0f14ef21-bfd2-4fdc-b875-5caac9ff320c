(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{3762:()=>{},7979:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var o=r(95155),n=r(12108);function a(e){let{children:t,session:r}=e;return(0,o.jsx)(n.Session<PERSON>rovider,{session:r,children:t})}},9927:()=>{},10326:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,30347,23)),Promise.resolve().then(r.bind(r,84573)),Promise.resolve().then(r.bind(r,87247)),Promise.resolve().then(r.bind(r,7979)),Promise.resolve().then(r.bind(r,83437)),Promise.resolve().then(r.bind(r,89074)),Promise.resolve().then(r.t.bind(r,63712,23)),Promise.resolve().then(r.t.bind(r,86627,23)),Promise.resolve().then(r.t.bind(r,29779,23)),Promise.resolve().then(r.t.bind(r,3762,23)),Promise.resolve().then(r.t.bind(r,79018,23)),Promise.resolve().then(r.t.bind(r,16945,23)),Promise.resolve().then(r.t.bind(r,49505,23)),Promise.resolve().then(r.t.bind(r,9927,23)),Promise.resolve().then(r.t.bind(r,66763,23)),Promise.resolve().then(r.t.bind(r,97055,23))},16945:()=>{},29350:(e,t,r)=>{"use strict";r.d(t,{r:()=>a});var o=r(12115);let n={theme:"light",primaryColor:"#3b82f6",secondaryColor:"#64748b",accentColor:"#f59e0b",backgroundColor:"#ffffff",textColor:"#1f2937",fontFamily:"Cairo",fontSize:"14px",fontWeight:"normal",lineHeight:"1.5",logo:"",favicon:"",brandName:"ديوان آل أبو علوش",brandColors:{primary:"#3b82f6",secondary:"#64748b"},sidebarStyle:"default",headerStyle:"default",cardStyle:"default",buttonStyle:"default",enableAnimations:!0,enableTransitions:!0,enableShadows:!0,enableGradients:!1,customCSS:"",enableCustomCSS:!1};function a(){let[e,t]=(0,o.useState)(n),[r,a]=(0,o.useState)(!0);(0,o.useEffect)(()=>{s()},[]);let s=async()=>{try{let e=await fetch("/api/settings");if(e.ok){let r=await e.json();r.appearance&&t({...n,...r.appearance})}}catch(e){console.error("خطأ في تحميل إعدادات المظهر:",e)}finally{a(!1)}};return{settings:e,loading:r,reload:s}}},29779:()=>{},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>i});var o=r(95155),n=r(12115),a=r(74466),s=r(59434);let l=(0,a.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-semibold transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-95",{variants:{variant:{default:"bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-primary-600 hover:to-primary-700",destructive:"bg-gradient-to-r from-danger-500 to-danger-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-danger-600 hover:to-danger-700",outline:"border-2 border-primary-300 bg-transparent text-primary-700 hover:bg-primary-50 hover:border-primary-400 hover:shadow-md",secondary:"bg-gradient-to-r from-secondary-100 to-secondary-200 text-secondary-800 shadow-md hover:shadow-lg hover:-translate-y-0.5 hover:from-secondary-200 hover:to-secondary-300",ghost:"text-primary-600 hover:bg-primary-100 hover:text-primary-800",link:"text-primary-600 underline-offset-4 hover:underline hover:text-primary-800",success:"bg-gradient-to-r from-success-500 to-success-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-success-600 hover:to-success-700",warning:"bg-gradient-to-r from-warning-500 to-warning-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-warning-600 hover:to-warning-700",info:"bg-gradient-to-r from-info-500 to-info-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-info-600 hover:to-info-700",accent:"bg-gradient-to-r from-gold-500 to-gold-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-gold-600 hover:to-gold-700"},size:{default:"h-11 px-6 py-2.5",sm:"h-9 rounded-lg px-4 text-xs",lg:"h-13 rounded-xl px-8 text-base",icon:"h-11 w-11",xs:"h-8 rounded-md px-3 text-xs"}},defaultVariants:{variant:"default",size:"default"}}),i=n.forwardRef((e,t)=>{let{className:r,variant:n,size:a,...i}=e;return(0,o.jsx)("button",{className:(0,s.cn)(l({variant:n,size:a,className:r})),ref:t,...i})});i.displayName="Button"},30347:()=>{},49505:()=>{},57340:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(19946).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},59434:(e,t,r)=>{"use strict";r.d(t,{OR:()=>u,Tk:()=>d,WK:()=>m,Yq:()=>l,cn:()=>a,gr:()=>i,uF:()=>c,vv:()=>s});var o=r(52596),n=r(39688);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,o.$)(t))}function s(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"JOD",minimumFractionDigits:2}).format(e).replace("JOD","د.أ")}function l(e){let t="string"==typeof e?new Date(e):e;return isNaN(t.getTime())?"تاريخ غير صحيح":new Intl.DateTimeFormat("en-GB",{year:"numeric",month:"2-digit",day:"2-digit"}).format(t)}function i(e){return({ADMIN:"مدير",DATA_ENTRY:"مدخل بيانات",VIEWER:"مطلع"})[e]||e}function d(e){return({SUBSCRIPTION:"اشتراكات",DONATION:"تبرعات",EVENT:"فعاليات",OTHER:"أخرى"})[e]||e}function c(e){return({MEETINGS:"اجتماعات",EVENTS:"مناسبات",MAINTENANCE:"إصلاحات",SOCIAL:"اجتماعية",GENERAL:"عامة"})[e]||e}function m(e){return({ACTIVE:"نشط",LATE:"متأخر",INACTIVE:"غير ملتزم",SUSPENDED:"موقوف مؤقتاً",ARCHIVED:"مؤرشف"})[e]||e}function u(e){return({ACTIVE:"bg-green-100 text-green-800",LATE:"bg-yellow-100 text-yellow-800",INACTIVE:"bg-red-100 text-red-800",SUSPENDED:"bg-orange-100 text-orange-800",ARCHIVED:"bg-gray-100 text-gray-800"})[e]||"bg-gray-100 text-gray-800"}},63712:()=>{},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>i,Zp:()=>s,aR:()=>l});var o=r(95155),n=r(12115),a=r(59434);let s=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,o.jsx)("div",{ref:t,className:(0,a.cn)("rounded-xl border border-secondary-200 bg-white text-secondary-700 shadow-lg hover:shadow-xl transition-all duration-200 hover:-translate-y-1 backdrop-blur-sm",r),...n})});s.displayName="Card";let l=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,o.jsx)("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6 bg-gradient-to-r from-secondary-50 to-secondary-100 rounded-t-xl border-b border-secondary-200",r),...n})});l.displayName="CardHeader";let i=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,o.jsx)("h3",{ref:t,className:(0,a.cn)("text-xl font-bold leading-none tracking-tight text-secondary-800",r),...n})});i.displayName="CardTitle";let d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,o.jsx)("p",{ref:t,className:(0,a.cn)("text-sm text-secondary-600 font-medium",r),...n})});d.displayName="CardDescription";let c=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,o.jsx)("div",{ref:t,className:(0,a.cn)("p-6 pt-4",r),...n})});c.displayName="CardContent",n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,o.jsx)("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0 bg-gradient-to-r from-secondary-50 to-secondary-100 rounded-b-xl border-t border-secondary-200",r),...n})}).displayName="CardFooter"},66763:()=>{},79018:()=>{},83437:(e,t,r)=>{"use strict";r.d(t,{SettingsProvider:()=>s});var o=r(95155),n=r(12115);let a=(0,n.createContext)(void 0);function s(e){let{children:t}=e,[r,s]=(0,n.useState)({}),[l,i]=(0,n.useState)(!0);(0,n.useEffect)(()=>{d()},[]);let d=async()=>{try{let e=await fetch("/api/settings");if(e.ok){let t=await e.json();s(t),c(t)}else console.error("فشل في تحميل الإعدادات:",e.status)}catch(e){console.error("خطأ في تحميل الإعدادات:",e)}finally{i(!1)}},c=e=>{if(e)try{if(e.appearance&&"object"==typeof e.appearance){let t=e.appearance;if(t.primaryColor&&document.documentElement.style.setProperty("--primary",t.primaryColor),t.secondaryColor&&document.documentElement.style.setProperty("--secondary",t.secondaryColor),t.accentColor&&document.documentElement.style.setProperty("--accent",t.accentColor),t.fontFamily&&(document.documentElement.style.setProperty("--font-family",t.fontFamily),document.body&&(document.body.style.fontFamily=t.fontFamily)),t.fontSize&&document.documentElement.style.setProperty("--font-size",t.fontSize),t.theme&&(document.documentElement.className="dark"===t.theme?"dark":""),t.enableCustomCSS&&t.customCSS){let e=document.getElementById("custom-css");e||((e=document.createElement("style")).id="custom-css",document.head.appendChild(e)),e.textContent=t.customCSS}}if(e.general&&"object"==typeof e.general){let t=e.general;if(t.diwanName&&(document.title=t.diwanName),t.favicon){let e=document.querySelector('link[rel="icon"]');e||((e=document.createElement("link")).rel="icon",document.head.appendChild(e)),e.href=t.favicon}}}catch(e){console.error("خطأ في تطبيق الإعدادات:",e)}};return(0,o.jsx)(a.Provider,{value:{settings:r,updateSettings:e=>{s(e),c(e)},loading:l},children:t})}},84573:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var o=r(95155),n=r(12115),a=r(1243),s=r(53904),l=r(57340),i=r(30285),d=r(66695);class c extends n.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("خطأ في التطبيق:",e,t),e.message.includes("Unexpected token")&&e.message.includes("<!DOCTYPE")&&console.error("\uD83D\uDEA8 الخادم أرجع HTML بدلاً من JSON - تحقق من حالة الخادم"),this.setState({error:e,errorInfo:t})}render(){if(this.state.hasError){if(this.props.fallback){let e=this.props.fallback;return(0,o.jsx)(e,{error:this.state.error,retry:this.retry})}return(0,o.jsx)(m,{error:this.state.error,retry:this.retry})}return this.props.children}constructor(e){super(e),this.retry=()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0})},this.state={hasError:!1}}}function m(e){let{error:t,retry:r}=e,n=t.message.includes("Unexpected token")&&t.message.includes("<!DOCTYPE");return(0,o.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-orange-100 p-4",children:(0,o.jsxs)(d.Zp,{className:"w-full max-w-md shadow-xl",children:[(0,o.jsxs)(d.aR,{className:"text-center space-y-4",children:[(0,o.jsx)("div",{className:"mx-auto w-20 h-20 bg-gradient-to-br from-red-600 to-orange-600 rounded-full flex items-center justify-center shadow-lg",children:(0,o.jsx)(a.A,{className:"w-10 h-10 text-white"})}),(0,o.jsx)("div",{children:(0,o.jsx)(d.ZB,{className:"text-2xl font-bold text-gray-900",children:"حدث خطأ غير متوقع"})})]}),(0,o.jsxs)(d.Wu,{className:"space-y-4",children:[n?(0,o.jsxs)("div",{className:"bg-orange-50 border border-orange-200 text-orange-700 px-4 py-3 rounded-lg text-sm",children:[(0,o.jsx)("p",{className:"font-semibold mb-2",children:"\uD83D\uDEA8 مشكلة في الاتصال بالخادم"}),(0,o.jsx)("p",{children:"الخادم أرجع صفحة HTML بدلاً من البيانات المطلوبة. قد يكون هناك:"}),(0,o.jsxs)("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[(0,o.jsx)("li",{children:"مشكلة في الخادم"}),(0,o.jsx)("li",{children:"إعادة توجيه غير متوقعة"}),(0,o.jsx)("li",{children:"انقطاع في الاتصال"})]})]}):(0,o.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm",children:[(0,o.jsx)("p",{className:"font-semibold mb-2",children:"تفاصيل الخطأ:"}),(0,o.jsx)("p",{className:"font-mono text-xs break-all",children:t.message})]}),(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsxs)(i.$,{onClick:r,className:"w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium py-2.5",children:[(0,o.jsx)(s.A,{className:"w-4 h-4 ml-2"}),"إعادة المحاولة"]}),(0,o.jsxs)(i.$,{onClick:()=>window.location.href="/",variant:"outline",className:"w-full border-gray-300 text-gray-600 hover:bg-gray-50 font-medium py-2.5",children:[(0,o.jsx)(l.A,{className:"w-4 h-4 ml-2"}),"العودة للصفحة الرئيسية"]})]}),!1]})]})})}let u=c},86627:()=>{},87247:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var o=r(12115),n=r(29350);function a(){let{settings:e}=(0,n.r)();return(0,o.useEffect)(()=>{if(e.favicon){let t=document.querySelector('link[rel="icon"]');if(t)t.href=e.favicon;else{let t=document.createElement("link");t.rel="icon",t.href=e.favicon,document.head.appendChild(t)}}if(e.brandName&&(document.title=e.brandName),e.enableCustomCSS&&e.customCSS){let t=document.getElementById("custom-css");t&&t.remove();let r=document.createElement("style");r.id="custom-css",r.textContent=e.customCSS,document.head.appendChild(r)}let t=document.documentElement;e.primaryColor&&t.style.setProperty("--primary-color",e.primaryColor),e.secondaryColor&&t.style.setProperty("--secondary-color",e.secondaryColor),e.accentColor&&t.style.setProperty("--accent-color",e.accentColor),e.backgroundColor&&t.style.setProperty("--background-color",e.backgroundColor),e.textColor&&t.style.setProperty("--text-color",e.textColor),e.fontFamily&&(t.style.setProperty("--font-family",e.fontFamily),document.body.style.fontFamily=e.fontFamily),e.fontSize&&t.style.setProperty("--font-size",e.fontSize),document.body.className="\n      ".concat(e.theme||"light","\n      ").concat(e.sidebarStyle?"sidebar-".concat(e.sidebarStyle):"","\n      ").concat(e.headerStyle?"header-".concat(e.headerStyle):"","\n      ").concat(e.cardStyle?"cards-".concat(e.cardStyle):"","\n      ").concat(e.buttonStyle?"buttons-".concat(e.buttonStyle):"","\n      ").concat(e.enableAnimations?"animations-enabled":"animations-disabled","\n      ").concat(e.enableShadows?"shadows-enabled":"shadows-disabled","\n      ").concat(e.enableGradients?"gradients-enabled":"gradients-disabled","\n    ").trim().replace(/\s+/g," ")},[e]),null}},89074:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>a});var o=r(95155),n=r(56671);let a=e=>{let{...t}=e;return(0,o.jsx)(n.l$,{theme:"light",className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...t})}},97055:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[7690,5218,1778,2108,2302,8441,1684,7358],()=>t(10326)),_N_E=e.O()}]);