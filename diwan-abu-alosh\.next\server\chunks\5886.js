"use strict";exports.id=5886,exports.ids=[5886],exports.modules={12941:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(62688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},14952:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},26312:(e,r,n)=>{n.d(r,{H_:()=>e4,UC:()=>e5,YJ:()=>e9,q7:()=>e3,VF:()=>re,JU:()=>e2,ZL:()=>e6,z6:()=>e8,hN:()=>e7,bL:()=>e0,wv:()=>rr,Pb:()=>rn,G5:()=>ro,ZP:()=>rt,l9:()=>e1});var t=n(43210),o=n(70569),a=n(98599),u=n(11273),i=n(65551),l=n(14163),s=n(9510),d=n(43),c=n(31355),p=n(1359),f=n(32547),m=n(96963),v=n(38674),h=n(25028),g=n(46059),w=n(72942),x=n(8730),y=n(13495),M=n(63376),b=n(42247),C=n(60687),R=["Enter"," "],j=["ArrowUp","PageDown","End"],D=["ArrowDown","PageUp","Home",...j],N={ltr:[...R,"ArrowRight"],rtl:[...R,"ArrowLeft"]},I={ltr:["ArrowLeft"],rtl:["ArrowRight"]},_="Menu",[k,T,E]=(0,s.N)(_),[P,A]=(0,u.A)(_,[E,v.Bk,w.RG]),O=(0,v.Bk)(),S=(0,w.RG)(),[L,F]=P(_),[G,K]=P(_),U=e=>{let{__scopeMenu:r,open:n=!1,children:o,dir:a,onOpenChange:u,modal:i=!0}=e,l=O(r),[s,c]=t.useState(null),p=t.useRef(!1),f=(0,y.c)(u),m=(0,d.jH)(a);return t.useEffect(()=>{let e=()=>{p.current=!0,document.addEventListener("pointerdown",r,{capture:!0,once:!0}),document.addEventListener("pointermove",r,{capture:!0,once:!0})},r=()=>p.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",r,{capture:!0}),document.removeEventListener("pointermove",r,{capture:!0})}},[]),(0,C.jsx)(v.bL,{...l,children:(0,C.jsx)(L,{scope:r,open:n,onOpenChange:f,content:s,onContentChange:c,children:(0,C.jsx)(G,{scope:r,onClose:t.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:p,dir:m,modal:i,children:o})})})};U.displayName=_;var B=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=O(n);return(0,C.jsx)(v.Mz,{...o,...t,ref:r})});B.displayName="MenuAnchor";var V="MenuPortal",[q,W]=P(V,{forceMount:void 0}),H=e=>{let{__scopeMenu:r,forceMount:n,children:t,container:o}=e,a=F(V,r);return(0,C.jsx)(q,{scope:r,forceMount:n,children:(0,C.jsx)(g.C,{present:n||a.open,children:(0,C.jsx)(h.Z,{asChild:!0,container:o,children:t})})})};H.displayName=V;var X="MenuContent",[z,Z]=P(X),Y=t.forwardRef((e,r)=>{let n=W(X,e.__scopeMenu),{forceMount:t=n.forceMount,...o}=e,a=F(X,e.__scopeMenu),u=K(X,e.__scopeMenu);return(0,C.jsx)(k.Provider,{scope:e.__scopeMenu,children:(0,C.jsx)(g.C,{present:t||a.open,children:(0,C.jsx)(k.Slot,{scope:e.__scopeMenu,children:u.modal?(0,C.jsx)(J,{...o,ref:r}):(0,C.jsx)(Q,{...o,ref:r})})})})}),J=t.forwardRef((e,r)=>{let n=F(X,e.__scopeMenu),u=t.useRef(null),i=(0,a.s)(r,u);return t.useEffect(()=>{let e=u.current;if(e)return(0,M.Eq)(e)},[]),(0,C.jsx)(ee,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),Q=t.forwardRef((e,r)=>{let n=F(X,e.__scopeMenu);return(0,C.jsx)(ee,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),$=(0,x.TL)("MenuContent.ScrollLock"),ee=t.forwardRef((e,r)=>{let{__scopeMenu:n,loop:u=!1,trapFocus:i,onOpenAutoFocus:l,onCloseAutoFocus:s,disableOutsidePointerEvents:d,onEntryFocus:m,onEscapeKeyDown:h,onPointerDownOutside:g,onFocusOutside:x,onInteractOutside:y,onDismiss:M,disableOutsideScroll:R,...N}=e,I=F(X,n),_=K(X,n),k=O(n),E=S(n),P=T(n),[A,L]=t.useState(null),G=t.useRef(null),U=(0,a.s)(r,G,I.onContentChange),B=t.useRef(0),V=t.useRef(""),q=t.useRef(0),W=t.useRef(null),H=t.useRef("right"),Z=t.useRef(0),Y=R?b.A:t.Fragment,J=e=>{let r=V.current+e,n=P().filter(e=>!e.disabled),t=document.activeElement,o=n.find(e=>e.ref.current===t)?.textValue,a=function(e,r,n){var t;let o=r.length>1&&Array.from(r).every(e=>e===r[0])?r[0]:r,a=n?e.indexOf(n):-1,u=(t=Math.max(a,0),e.map((r,n)=>e[(t+n)%e.length]));1===o.length&&(u=u.filter(e=>e!==n));let i=u.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return i!==n?i:void 0}(n.map(e=>e.textValue),r,o),u=n.find(e=>e.textValue===a)?.ref.current;!function e(r){V.current=r,window.clearTimeout(B.current),""!==r&&(B.current=window.setTimeout(()=>e(""),1e3))}(r),u&&setTimeout(()=>u.focus())};t.useEffect(()=>()=>window.clearTimeout(B.current),[]),(0,p.Oh)();let Q=t.useCallback(e=>H.current===W.current?.side&&function(e,r){return!!r&&function(e,r){let{x:n,y:t}=e,o=!1;for(let e=0,a=r.length-1;e<r.length;a=e++){let u=r[e],i=r[a],l=u.x,s=u.y,d=i.x,c=i.y;s>t!=c>t&&n<(d-l)*(t-s)/(c-s)+l&&(o=!o)}return o}({x:e.clientX,y:e.clientY},r)}(e,W.current?.area),[]);return(0,C.jsx)(z,{scope:n,searchRef:V,onItemEnter:t.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:t.useCallback(e=>{Q(e)||(G.current?.focus(),L(null))},[Q]),onTriggerLeave:t.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:q,onPointerGraceIntentChange:t.useCallback(e=>{W.current=e},[]),children:(0,C.jsx)(Y,{...R?{as:$,allowPinchZoom:!0}:void 0,children:(0,C.jsx)(f.n,{asChild:!0,trapped:i,onMountAutoFocus:(0,o.m)(l,e=>{e.preventDefault(),G.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,C.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:h,onPointerDownOutside:g,onFocusOutside:x,onInteractOutside:y,onDismiss:M,children:(0,C.jsx)(w.bL,{asChild:!0,...E,dir:_.dir,orientation:"vertical",loop:u,currentTabStopId:A,onCurrentTabStopIdChange:L,onEntryFocus:(0,o.m)(m,e=>{_.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,C.jsx)(v.UC,{role:"menu","aria-orientation":"vertical","data-state":eI(I.open),"data-radix-menu-content":"",dir:_.dir,...k,...N,ref:U,style:{outline:"none",...N.style},onKeyDown:(0,o.m)(N.onKeyDown,e=>{let r=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,t=1===e.key.length;r&&("Tab"===e.key&&e.preventDefault(),!n&&t&&J(e.key));let o=G.current;if(e.target!==o||!D.includes(e.key))return;e.preventDefault();let a=P().filter(e=>!e.disabled).map(e=>e.ref.current);j.includes(e.key)&&a.reverse(),function(e){let r=document.activeElement;for(let n of e)if(n===r||(n.focus(),document.activeElement!==r))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(B.current),V.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eT(e=>{let r=e.target,n=Z.current!==e.clientX;e.currentTarget.contains(r)&&n&&(H.current=e.clientX>Z.current?"right":"left",Z.current=e.clientX)}))})})})})})})});Y.displayName=X;var er=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,C.jsx)(l.sG.div,{role:"group",...t,ref:r})});er.displayName="MenuGroup";var en=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,C.jsx)(l.sG.div,{...t,ref:r})});en.displayName="MenuLabel";var et="MenuItem",eo="menu.itemSelect",ea=t.forwardRef((e,r)=>{let{disabled:n=!1,onSelect:u,...i}=e,s=t.useRef(null),d=K(et,e.__scopeMenu),c=Z(et,e.__scopeMenu),p=(0,a.s)(r,s),f=t.useRef(!1);return(0,C.jsx)(eu,{...i,ref:p,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=s.current;if(!n&&e){let r=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>u?.(e),{once:!0}),(0,l.hO)(e,r),r.defaultPrevented?f.current=!1:d.onClose()}}),onPointerDown:r=>{e.onPointerDown?.(r),f.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{f.current||e.currentTarget?.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=""!==c.searchRef.current;n||r&&" "===e.key||R.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=et;var eu=t.forwardRef((e,r)=>{let{__scopeMenu:n,disabled:u=!1,textValue:i,...s}=e,d=Z(et,n),c=S(n),p=t.useRef(null),f=(0,a.s)(r,p),[m,v]=t.useState(!1),[h,g]=t.useState("");return t.useEffect(()=>{let e=p.current;e&&g((e.textContent??"").trim())},[s.children]),(0,C.jsx)(k.ItemSlot,{scope:n,disabled:u,textValue:i??h,children:(0,C.jsx)(w.q7,{asChild:!0,...c,focusable:!u,children:(0,C.jsx)(l.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":u||void 0,"data-disabled":u?"":void 0,...s,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,eT(e=>{u?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eT(e=>d.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>v(!0)),onBlur:(0,o.m)(e.onBlur,()=>v(!1))})})})}),ei=t.forwardRef((e,r)=>{let{checked:n=!1,onCheckedChange:t,...a}=e;return(0,C.jsx)(ev,{scope:e.__scopeMenu,checked:n,children:(0,C.jsx)(ea,{role:"menuitemcheckbox","aria-checked":e_(n)?"mixed":n,...a,ref:r,"data-state":ek(n),onSelect:(0,o.m)(a.onSelect,()=>t?.(!!e_(n)||!n),{checkForDefaultPrevented:!1})})})});ei.displayName="MenuCheckboxItem";var el="MenuRadioGroup",[es,ed]=P(el,{value:void 0,onValueChange:()=>{}}),ec=t.forwardRef((e,r)=>{let{value:n,onValueChange:t,...o}=e,a=(0,y.c)(t);return(0,C.jsx)(es,{scope:e.__scopeMenu,value:n,onValueChange:a,children:(0,C.jsx)(er,{...o,ref:r})})});ec.displayName=el;var ep="MenuRadioItem",ef=t.forwardRef((e,r)=>{let{value:n,...t}=e,a=ed(ep,e.__scopeMenu),u=n===a.value;return(0,C.jsx)(ev,{scope:e.__scopeMenu,checked:u,children:(0,C.jsx)(ea,{role:"menuitemradio","aria-checked":u,...t,ref:r,"data-state":ek(u),onSelect:(0,o.m)(t.onSelect,()=>a.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});ef.displayName=ep;var em="MenuItemIndicator",[ev,eh]=P(em,{checked:!1}),eg=t.forwardRef((e,r)=>{let{__scopeMenu:n,forceMount:t,...o}=e,a=eh(em,n);return(0,C.jsx)(g.C,{present:t||e_(a.checked)||!0===a.checked,children:(0,C.jsx)(l.sG.span,{...o,ref:r,"data-state":ek(a.checked)})})});eg.displayName=em;var ew=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,C.jsx)(l.sG.div,{role:"separator","aria-orientation":"horizontal",...t,ref:r})});ew.displayName="MenuSeparator";var ex=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=O(n);return(0,C.jsx)(v.i3,{...o,...t,ref:r})});ex.displayName="MenuArrow";var ey="MenuSub",[eM,eb]=P(ey),eC=e=>{let{__scopeMenu:r,children:n,open:o=!1,onOpenChange:a}=e,u=F(ey,r),i=O(r),[l,s]=t.useState(null),[d,c]=t.useState(null),p=(0,y.c)(a);return t.useEffect(()=>(!1===u.open&&p(!1),()=>p(!1)),[u.open,p]),(0,C.jsx)(v.bL,{...i,children:(0,C.jsx)(L,{scope:r,open:o,onOpenChange:p,content:d,onContentChange:c,children:(0,C.jsx)(eM,{scope:r,contentId:(0,m.B)(),triggerId:(0,m.B)(),trigger:l,onTriggerChange:s,children:n})})})};eC.displayName=ey;var eR="MenuSubTrigger",ej=t.forwardRef((e,r)=>{let n=F(eR,e.__scopeMenu),u=K(eR,e.__scopeMenu),i=eb(eR,e.__scopeMenu),l=Z(eR,e.__scopeMenu),s=t.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:c}=l,p={__scopeMenu:e.__scopeMenu},f=t.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return t.useEffect(()=>f,[f]),t.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),c(null)}},[d,c]),(0,C.jsx)(B,{asChild:!0,...p,children:(0,C.jsx)(eu,{id:i.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":i.contentId,"data-state":eI(n.open),...e,ref:(0,a.t)(r,i.onTriggerChange),onClick:r=>{e.onClick?.(r),e.disabled||r.defaultPrevented||(r.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eT(r=>{l.onItemEnter(r),!r.defaultPrevented&&(e.disabled||n.open||s.current||(l.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eT(e=>{f();let r=n.content?.getBoundingClientRect();if(r){let t=n.content?.dataset.side,o="right"===t,a=r[o?"left":"right"],u=r[o?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:a,y:r.top},{x:u,y:r.top},{x:u,y:r.bottom},{x:a,y:r.bottom}],side:t}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,r=>{let t=""!==l.searchRef.current;e.disabled||t&&" "===r.key||N[u.dir].includes(r.key)&&(n.onOpenChange(!0),n.content?.focus(),r.preventDefault())})})})});ej.displayName=eR;var eD="MenuSubContent",eN=t.forwardRef((e,r)=>{let n=W(X,e.__scopeMenu),{forceMount:u=n.forceMount,...i}=e,l=F(X,e.__scopeMenu),s=K(X,e.__scopeMenu),d=eb(eD,e.__scopeMenu),c=t.useRef(null),p=(0,a.s)(r,c);return(0,C.jsx)(k.Provider,{scope:e.__scopeMenu,children:(0,C.jsx)(g.C,{present:u||l.open,children:(0,C.jsx)(k.Slot,{scope:e.__scopeMenu,children:(0,C.jsx)(ee,{id:d.contentId,"aria-labelledby":d.triggerId,...i,ref:p,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{s.isUsingKeyboardRef.current&&c.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==d.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=e.currentTarget.contains(e.target),n=I[s.dir].includes(e.key);r&&n&&(l.onOpenChange(!1),d.trigger?.focus(),e.preventDefault())})})})})})});function eI(e){return e?"open":"closed"}function e_(e){return"indeterminate"===e}function ek(e){return e_(e)?"indeterminate":e?"checked":"unchecked"}function eT(e){return r=>"mouse"===r.pointerType?e(r):void 0}eN.displayName=eD;var eE="DropdownMenu",[eP,eA]=(0,u.A)(eE,[A]),eO=A(),[eS,eL]=eP(eE),eF=e=>{let{__scopeDropdownMenu:r,children:n,dir:o,open:a,defaultOpen:u,onOpenChange:l,modal:s=!0}=e,d=eO(r),c=t.useRef(null),[p,f]=(0,i.i)({prop:a,defaultProp:u??!1,onChange:l,caller:eE});return(0,C.jsx)(eS,{scope:r,triggerId:(0,m.B)(),triggerRef:c,contentId:(0,m.B)(),open:p,onOpenChange:f,onOpenToggle:t.useCallback(()=>f(e=>!e),[f]),modal:s,children:(0,C.jsx)(U,{...d,open:p,onOpenChange:f,dir:o,modal:s,children:n})})};eF.displayName=eE;var eG="DropdownMenuTrigger",eK=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,disabled:t=!1,...u}=e,i=eL(eG,n),s=eO(n);return(0,C.jsx)(B,{asChild:!0,...s,children:(0,C.jsx)(l.sG.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":t?"":void 0,disabled:t,...u,ref:(0,a.t)(r,i.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!t&&0===e.button&&!1===e.ctrlKey&&(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!t&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eK.displayName=eG;var eU=e=>{let{__scopeDropdownMenu:r,...n}=e,t=eO(r);return(0,C.jsx)(H,{...t,...n})};eU.displayName="DropdownMenuPortal";var eB="DropdownMenuContent",eV=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...a}=e,u=eL(eB,n),i=eO(n),l=t.useRef(!1);return(0,C.jsx)(Y,{id:u.contentId,"aria-labelledby":u.triggerId,...i,...a,ref:r,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{l.current||u.triggerRef.current?.focus(),l.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let r=e.detail.originalEvent,n=0===r.button&&!0===r.ctrlKey,t=2===r.button||n;(!u.modal||t)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eV.displayName=eB;var eq=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,C.jsx)(er,{...o,...t,ref:r})});eq.displayName="DropdownMenuGroup";var eW=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,C.jsx)(en,{...o,...t,ref:r})});eW.displayName="DropdownMenuLabel";var eH=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,C.jsx)(ea,{...o,...t,ref:r})});eH.displayName="DropdownMenuItem";var eX=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,C.jsx)(ei,{...o,...t,ref:r})});eX.displayName="DropdownMenuCheckboxItem";var ez=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,C.jsx)(ec,{...o,...t,ref:r})});ez.displayName="DropdownMenuRadioGroup";var eZ=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,C.jsx)(ef,{...o,...t,ref:r})});eZ.displayName="DropdownMenuRadioItem";var eY=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,C.jsx)(eg,{...o,...t,ref:r})});eY.displayName="DropdownMenuItemIndicator";var eJ=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,C.jsx)(ew,{...o,...t,ref:r})});eJ.displayName="DropdownMenuSeparator",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,C.jsx)(ex,{...o,...t,ref:r})}).displayName="DropdownMenuArrow";var eQ=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,C.jsx)(ej,{...o,...t,ref:r})});eQ.displayName="DropdownMenuSubTrigger";var e$=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,C.jsx)(eN,{...o,...t,ref:r,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e$.displayName="DropdownMenuSubContent";var e0=eF,e1=eK,e6=eU,e5=eV,e9=eq,e2=eW,e3=eH,e4=eX,e8=ez,e7=eZ,re=eY,rr=eJ,rn=e=>{let{__scopeDropdownMenu:r,children:n,open:t,onOpenChange:o,defaultOpen:a}=e,u=eO(r),[l,s]=(0,i.i)({prop:t,defaultProp:a??!1,onChange:o,caller:"DropdownMenuSub"});return(0,C.jsx)(eC,{...u,open:l,onOpenChange:s,children:n})},rt=eQ,ro=e$},46059:(e,r,n)=>{n.d(r,{C:()=>u});var t=n(43210),o=n(98599),a=n(66156),u=e=>{let{present:r,children:n}=e,u=function(e){var r,n;let[o,u]=t.useState(),l=t.useRef(null),s=t.useRef(e),d=t.useRef("none"),[c,p]=(r=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},t.useReducer((e,r)=>n[e][r]??e,r));return t.useEffect(()=>{let e=i(l.current);d.current="mounted"===c?e:"none"},[c]),(0,a.N)(()=>{let r=l.current,n=s.current;if(n!==e){let t=d.current,o=i(r);e?p("MOUNT"):"none"===o||r?.display==="none"?p("UNMOUNT"):n&&t!==o?p("ANIMATION_OUT"):p("UNMOUNT"),s.current=e}},[e,p]),(0,a.N)(()=>{if(o){let e,r=o.ownerDocument.defaultView??window,n=n=>{let t=i(l.current).includes(n.animationName);if(n.target===o&&t&&(p("ANIMATION_END"),!s.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},t=e=>{e.target===o&&(d.current=i(l.current))};return o.addEventListener("animationstart",t),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{r.clearTimeout(e),o.removeEventListener("animationstart",t),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}p("ANIMATION_END")},[o,p]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:t.useCallback(e=>{l.current=e?getComputedStyle(e):null,u(e)},[])}}(r),l="function"==typeof n?n({present:u.isPresent}):t.Children.only(n),s=(0,o.s)(u.ref,function(e){let r=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=r&&"isReactWarning"in r&&r.isReactWarning;return n?e.ref:(n=(r=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof n||u.isPresent?t.cloneElement(l,{ref:s}):null};function i(e){return e?.animationName||"none"}u.displayName="Presence"},65822:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(62688).A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},72942:(e,r,n)=>{n.d(r,{RG:()=>M,bL:()=>k,q7:()=>T});var t=n(43210),o=n(70569),a=n(9510),u=n(98599),i=n(11273),l=n(96963),s=n(14163),d=n(13495),c=n(65551),p=n(43),f=n(60687),m="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[g,w,x]=(0,a.N)(h),[y,M]=(0,i.A)(h,[x]),[b,C]=y(h),R=t.forwardRef((e,r)=>(0,f.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(j,{...e,ref:r})})}));R.displayName=h;var j=t.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:n,orientation:a,loop:i=!1,dir:l,currentTabStopId:g,defaultCurrentTabStopId:x,onCurrentTabStopIdChange:y,onEntryFocus:M,preventScrollOnEntryFocus:C=!1,...R}=e,j=t.useRef(null),D=(0,u.s)(r,j),N=(0,p.jH)(l),[I,k]=(0,c.i)({prop:g,defaultProp:x??null,onChange:y,caller:h}),[T,E]=t.useState(!1),P=(0,d.c)(M),A=w(n),O=t.useRef(!1),[S,L]=t.useState(0);return t.useEffect(()=>{let e=j.current;if(e)return e.addEventListener(m,P),()=>e.removeEventListener(m,P)},[P]),(0,f.jsx)(b,{scope:n,orientation:a,dir:N,loop:i,currentTabStopId:I,onItemFocus:t.useCallback(e=>k(e),[k]),onItemShiftTab:t.useCallback(()=>E(!0),[]),onFocusableItemAdd:t.useCallback(()=>L(e=>e+1),[]),onFocusableItemRemove:t.useCallback(()=>L(e=>e-1),[]),children:(0,f.jsx)(s.sG.div,{tabIndex:T||0===S?-1:0,"data-orientation":a,...R,ref:D,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{O.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let r=!O.current;if(e.target===e.currentTarget&&r&&!T){let r=new CustomEvent(m,v);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){let e=A().filter(e=>e.focusable);_([e.find(e=>e.active),e.find(e=>e.id===I),...e].filter(Boolean).map(e=>e.ref.current),C)}}O.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>E(!1))})})}),D="RovingFocusGroupItem",N=t.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:n,focusable:a=!0,active:u=!1,tabStopId:i,children:d,...c}=e,p=(0,l.B)(),m=i||p,v=C(D,n),h=v.currentTabStopId===m,x=w(n),{onFocusableItemAdd:y,onFocusableItemRemove:M,currentTabStopId:b}=v;return t.useEffect(()=>{if(a)return y(),()=>M()},[a,y,M]),(0,f.jsx)(g.ItemSlot,{scope:n,id:m,focusable:a,active:u,children:(0,f.jsx)(s.sG.span,{tabIndex:h?0:-1,"data-orientation":v.orientation,...c,ref:r,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?v.onItemFocus(m):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>v.onItemFocus(m)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void v.onItemShiftTab();if(e.target!==e.currentTarget)return;let r=function(e,r,n){var t;let o=(t=e.key,"rtl"!==n?t:"ArrowLeft"===t?"ArrowRight":"ArrowRight"===t?"ArrowLeft":t);if(!("vertical"===r&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===r&&["ArrowUp","ArrowDown"].includes(o)))return I[o]}(e,v.orientation,v.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=x().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)n.reverse();else if("prev"===r||"next"===r){"prev"===r&&n.reverse();let t=n.indexOf(e.currentTarget);n=v.loop?function(e,r){return e.map((n,t)=>e[(r+t)%e.length])}(n,t+1):n.slice(t+1)}setTimeout(()=>_(n))}}),children:"function"==typeof d?d({isCurrentTabStop:h,hasTabStop:null!=b}):d})})});N.displayName=D;var I={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function _(e,r=!1){let n=document.activeElement;for(let t of e)if(t===n||(t.focus({preventScroll:r}),document.activeElement!==n))return}var k=R,T=N}};