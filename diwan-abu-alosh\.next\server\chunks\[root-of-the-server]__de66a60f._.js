module.exports = {

"[project]/.next-internal/server/app/api/settings/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]();
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = prisma;
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authOptions": (()=>authOptions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/credentials.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
;
;
;
const authOptions = {
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            name: 'credentials',
            credentials: {
                email: {
                    label: 'البريد الإلكتروني',
                    type: 'email'
                },
                password: {
                    label: 'كلمة المرور',
                    type: 'password'
                }
            },
            async authorize (credentials) {
                if (!credentials?.email || !credentials?.password) {
                    return null;
                }
                const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
                    where: {
                        email: credentials.email
                    }
                });
                if (!user) {
                    return null;
                }
                const isPasswordValid = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(credentials.password, user.password);
                if (!isPasswordValid) {
                    return null;
                }
                return {
                    id: user.id,
                    email: user.email,
                    name: user.name,
                    role: user.role
                };
            }
        })
    ],
    session: {
        strategy: 'jwt'
    },
    callbacks: {
        async jwt ({ token, user }) {
            if (user) {
                token.role = user.role;
            }
            return token;
        },
        async session ({ session, token }) {
            if (token) {
                session.user.id = token.sub;
                session.user.role = token.role;
            }
            return session;
        }
    },
    pages: {
        signIn: '/auth/signin'
    },
    secret: process.env.NEXTAUTH_SECRET
};
}}),
"[project]/src/app/api/settings/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST),
    "PUT": (()=>PUT)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
;
;
;
;
// الإعدادات الافتراضية
const defaultSettings = {
    general: {
        diwanName: 'ديوان آل أبو علوش',
        diwanDescription: 'نظام إدارة ديوان آل أبو علوش',
        diwanAddress: '',
        diwanPhone: '',
        diwanEmail: '',
        diwanWebsite: '',
        defaultCurrency: 'JOD',
        currencySymbol: 'د.أ',
        timezone: 'Asia/Amman',
        dateFormat: 'DD/MM/YYYY',
        timeFormat: '24h',
        language: 'ar',
        itemsPerPage: 10,
        autoSave: true,
        enableAuditLog: true,
        sessionTimeout: 30,
        showWelcomeMessage: true,
        welcomeMessage: 'مرحباً بك في ديوان آل أبو علوش',
        enableDashboardStats: true,
        enableQuickActions: true
    },
    appearance: {
        theme: 'light',
        primaryColor: '#3b82f6',
        secondaryColor: '#64748b',
        accentColor: '#f59e0b',
        backgroundColor: '#ffffff',
        textColor: '#1f2937',
        fontFamily: 'Cairo',
        fontSize: '14px',
        fontWeight: 'normal',
        lineHeight: '1.5',
        logo: '',
        favicon: '',
        brandName: 'ديوان آل أبو علوش',
        brandColors: {
            primary: '#3b82f6',
            secondary: '#64748b'
        },
        sidebarStyle: 'default',
        headerStyle: 'default',
        cardStyle: 'default',
        buttonStyle: 'default',
        enableAnimations: true,
        enableTransitions: true,
        enableShadows: true,
        enableGradients: false,
        customCSS: '',
        enableCustomCSS: false
    },
    notifications: {
        enableNotifications: true,
        enableSounds: true,
        enableDesktopNotifications: true,
        enableEmailNotifications: false,
        enableSMSNotifications: false,
        memberNotifications: {
            newMember: true,
            memberUpdate: true,
            memberStatusChange: true,
            memberPayment: true
        },
        incomeNotifications: {
            newIncome: true,
            incomeUpdate: true,
            paymentReminder: true,
            paymentOverdue: true
        },
        expenseNotifications: {
            newExpense: true,
            expenseUpdate: true,
            budgetAlert: true,
            expenseApproval: true
        },
        systemNotifications: {
            systemUpdate: true,
            securityAlert: true,
            backupComplete: true,
            errorAlert: true
        },
        quietHours: {
            enabled: false,
            startTime: '22:00',
            endTime: '08:00'
        },
        emailSettings: {
            smtpServer: '',
            smtpPort: 587,
            smtpUsername: '',
            smtpPassword: '',
            fromEmail: '',
            fromName: 'ديوان آل أبو علوش',
            enableSSL: true
        },
        smsSettings: {
            provider: '',
            apiKey: '',
            senderName: 'ديوان آل أبو علوش'
        },
        templates: {
            welcomeMessage: 'مرحباً بك في ديوان آل أبو علوش',
            paymentReminder: 'تذكير: يرجى دفع الاشتراك الشهري',
            paymentConfirmation: 'تم استلام دفعتك بنجاح',
            systemAlert: 'تنبيه من النظام'
        }
    },
    security: {
        passwordPolicy: {
            minLength: 8,
            requireUppercase: true,
            requireLowercase: true,
            requireNumbers: true,
            requireSpecialChars: false,
            preventReuse: 5,
            expirationDays: 90
        },
        sessionSettings: {
            timeout: 30,
            maxConcurrentSessions: 3,
            requireReauth: false,
            rememberMe: true,
            rememberMeDuration: 30
        },
        loginSettings: {
            maxFailedAttempts: 5,
            lockoutDuration: 15,
            enableCaptcha: false,
            enableTwoFactor: false,
            allowedIPs: [],
            blockedIPs: []
        },
        auditSettings: {
            enableAuditLog: true,
            logLoginAttempts: true,
            logDataChanges: true,
            logSystemEvents: true,
            retentionDays: 365,
            enableRealTimeAlerts: true
        },
        permissionSettings: {
            defaultRole: 'VIEWER',
            allowSelfRegistration: false,
            requireAdminApproval: true,
            enableRoleHierarchy: true,
            maxUsersPerRole: {
                ADMIN: 3,
                DATA_ENTRY: 10,
                VIEWER: 100
            }
        },
        advancedSecurity: {
            enableEncryption: true,
            enableSSL: true,
            enableCSRF: true,
            enableXSS: true,
            enableSQLInjection: true,
            enableRateLimit: true,
            rateLimitRequests: 100,
            rateLimitWindow: 15
        }
    },
    backup: {
        autoBackup: {
            enabled: true,
            frequency: 'daily',
            time: '02:00',
            retentionDays: 30,
            includeFiles: true,
            includeDatabase: true,
            includeSettings: true
        },
        storage: {
            location: 'local',
            localPath: './backups',
            cloudProvider: '',
            cloudCredentials: {
                accessKey: '',
                secretKey: '',
                bucket: '',
                region: ''
            }
        },
        importExport: {
            allowDataExport: true,
            allowDataImport: true,
            exportFormats: [
                'json',
                'csv',
                'xlsx'
            ],
            maxFileSize: 100,
            requireConfirmation: true
        },
        database: {
            enableOptimization: true,
            autoVacuum: true,
            compressionLevel: 6,
            encryptBackups: true
        }
    }
};
async function GET() {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        if (!session) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'غير مصرح'
            }, {
                status: 401
            });
        }
        // جلب جميع الإعدادات من قاعدة البيانات
        const settings = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].settings.findMany();
        // تنظيم الإعدادات حسب الفئة
        const organizedSettings = {
            ...defaultSettings
        };
        settings.forEach((setting)=>{
            try {
                const value = JSON.parse(setting.value);
                const category = setting.category.toLowerCase();
                if (organizedSettings[category]) {
                    organizedSettings[category] = {
                        ...organizedSettings[category],
                        ...value
                    };
                }
            } catch (error) {
                console.error(`خطأ في تحليل إعداد ${setting.key}:`, error);
            }
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(organizedSettings);
    } catch (error) {
        console.error('خطأ في جلب الإعدادات:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'خطأ في الخادم'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        if (!session || session.user?.role !== 'ADMIN') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'غير مصرح'
            }, {
                status: 401
            });
        }
        const settingsData = await request.json();
        // حفظ كل فئة من الإعدادات
        for (const [category, settings] of Object.entries(settingsData)){
            const categoryKey = category.toUpperCase();
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].settings.upsert({
                where: {
                    key: category
                },
                update: {
                    value: JSON.stringify(settings),
                    category: categoryKey,
                    updatedAt: new Date()
                },
                create: {
                    key: category,
                    value: JSON.stringify(settings),
                    category: categoryKey
                }
            });
        }
        // تسجيل العملية في سجل التدقيق إذا كان مفعلاً
        if (settingsData.general?.enableAuditLog) {
        // يمكن إضافة سجل التدقيق هنا
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            message: 'تم حفظ الإعدادات بنجاح'
        });
    } catch (error) {
        console.error('خطأ في حفظ الإعدادات:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'خطأ في الخادم'
        }, {
            status: 500
        });
    }
}
async function PUT(request) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        if (!session || session.user?.role !== 'ADMIN') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'غير مصرح'
            }, {
                status: 401
            });
        }
        const { key, value, category } = await request.json();
        if (!key || !category) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'مفتاح الإعداد والفئة مطلوبان'
            }, {
                status: 400
            });
        }
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].settings.upsert({
            where: {
                key
            },
            update: {
                value: JSON.stringify(value),
                category: category.toUpperCase(),
                updatedAt: new Date()
            },
            create: {
                key,
                value: JSON.stringify(value),
                category: category.toUpperCase()
            }
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            message: 'تم تحديث الإعداد بنجاح'
        });
    } catch (error) {
        console.error('خطأ في تحديث الإعداد:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'خطأ في الخادم'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__de66a60f._.js.map