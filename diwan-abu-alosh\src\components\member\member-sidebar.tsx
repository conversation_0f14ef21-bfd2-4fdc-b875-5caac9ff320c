'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
// import { useMemberAuth } from '@/hooks/use-member-auth'
import { useMemberPermissions } from '@/hooks/use-member-permissions'
import {
  Building2,
  FileText,
  Image,
  LogOut,
  User,
  // CreditCard
} from 'lucide-react'
import { Button } from '@/components/ui/button'

const navigation = [
  { name: 'لوحة التحكم', href: '/member/dashboard', icon: User, permission: null },
  { name: 'كشف الحساب', href: '/member/account-statement', icon: FileText, permission: 'canViewAccountStatement' },
  { name: 'معرض الصور', href: '/member/gallery', icon: Image, permission: 'canViewGallery' },
]

interface MemberSidebarProps {
  user: any
  onSignOut: () => void
}

export default function MemberSidebar({ user, onSignOut }: MemberSidebarProps) {
  const pathname = usePathname()
  const { hasPermission } = useMemberPermissions()

  return (
    <div className="fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg lg:block hidden">
      {/* الرأس */}
      <div className="flex h-16 items-center justify-center border-b border-gray-200">
        <div className="flex items-center space-x-2 space-x-reverse">
          <Building2 className="h-8 w-8 text-blue-600" />
          <div className="text-right">
            <h1 className="text-lg font-bold text-gray-900">ديوان آل أبو علوش</h1>
            <p className="text-xs text-gray-500">بوابة الأعضاء</p>
          </div>
        </div>
      </div>

      {/* معلومات العضو */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3 space-x-reverse">
          {user?.member?.photo ? (
            <img
              src={user.member.photo}
              alt={user.member.name}
              className="h-10 w-10 rounded-full object-cover"
            />
          ) : (
            <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
              <User className="h-5 w-5 text-blue-600" />
            </div>
          )}
          <div className="text-right">
            <p className="text-sm font-medium text-gray-900">{user?.member?.name}</p>
            <p className="text-xs text-gray-500">عضو</p>
          </div>
        </div>
      </div>
      
      {/* القائمة */}
      <nav className="mt-6 px-3">
        <ul className="space-y-1">
          {navigation.map((item) => {
            const isActive = pathname === item.href

            // التحقق من الصلاحيات
            if (item.permission && !hasPermission(item.permission as any)) {
              return null
            }

            return (
              <li key={item.name}>
                <Link
                  href={item.href}
                  className={cn(
                    'group flex items-center rounded-md px-3 py-2 text-sm font-medium transition-colors',
                    isActive
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                  )}
                >
                  <item.icon
                    className={cn(
                      'ml-3 h-5 w-5 flex-shrink-0',
                      isActive ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
                    )}
                  />
                  {item.name}
                </Link>
              </li>
            )
          })}
        </ul>
      </nav>
      
      {/* تسجيل الخروج */}
      <div className="absolute bottom-0 w-full p-4 border-t border-gray-200">
        <Button
          onClick={onSignOut}
          variant="outline"
          className="w-full justify-start"
        >
          <LogOut className="h-4 w-4 mr-2" />
          تسجيل الخروج
        </Button>
        
        <div className="mt-4 text-center text-xs text-gray-500">
          <p>الإصدار 1.0.0</p>
          <p className="mt-1">© 2024 ديوان آل أبو علوش</p>
        </div>
      </div>
    </div>
  )
}
