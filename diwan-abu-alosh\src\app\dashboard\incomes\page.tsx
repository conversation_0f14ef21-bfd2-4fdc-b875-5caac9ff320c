'use client'

import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Filter,
  TrendingUp,
  DollarSign,
  Users,
  Calendar,
  FileText
} from 'lucide-react'
import { formatCurrency, formatDate, getIncomeTypeText } from '@/lib/utils'
import IncomeDialog from '@/components/incomes/income-dialog'
import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'

interface Income {
  id: string
  amount: number
  date: string
  source: string
  type: string
  description?: string
  member?: {
    id: string
    name: string
  }
  createdBy: {
    name: string
  }
}

interface IncomesResponse {
  incomes: Income[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

export default function IncomesPage() {
  const { data: session } = useSession()
  const [incomes, setIncomes] = useState<Income[]>([])
  const [loading, setLoading] = useState(true)
  const [search, setSearch] = useState('')
  const [type, setType] = useState('all')
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0,
  })
  const [stats, setStats] = useState({
    totalAmount: 0,
    totalCount: 0,
    byType: [] as Array<{ type: string; _sum: { amount: number }; _count: number }>
  })
  const [isIncomeDialogOpen, setIsIncomeDialogOpen] = useState(false)
  const [editingIncome, setEditingIncome] = useState<Income | null>(null)

  // جلب الإيرادات
  const fetchIncomes = useCallback(async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        search,
        type,
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
      })

      const response = await fetch(`/api/incomes?${params}`)
      if (!response.ok) throw new Error('فشل في جلب الإيرادات')

      const data: IncomesResponse = await response.json()
      setIncomes(data.incomes)
      setPagination(data.pagination)
    } catch (error) {
      console.error('خطأ في جلب الإيرادات:', error)
    } finally {
      setLoading(false)
    }
  }, [search, type, pagination.page, pagination.limit])

  // جلب الإحصائيات
  const fetchStats = async () => {
    try {
      const response = await fetch('/api/incomes?limit=1000')
      if (!response.ok) return

      const data: IncomesResponse = await response.json()
      const totalAmount = data.incomes.reduce((sum, income) => sum + income.amount, 0)
      const totalCount = data.incomes.length

      // حساب الإحصائيات حسب النوع
      const byType = data.incomes.reduce((acc: Array<{ type: string; _sum: { amount: number }; _count: number }>, income) => {
        const existing = acc.find((item) => item.type === income.type)
        if (existing) {
          existing._sum.amount += income.amount
          existing._count += 1
        } else {
          acc.push({
            type: income.type,
            _sum: { amount: income.amount },
            _count: 1
          })
        }
        return acc
      }, [])

      setStats({ totalAmount, totalCount, byType })
    } catch (error) {
      console.error('خطأ في جلب الإحصائيات:', error)
    }
  }

  useEffect(() => {
    fetchIncomes()
  }, [fetchIncomes])

  useEffect(() => {
    fetchStats()
  }, [])

  // تعديل إيراد
  const handleEditIncome = (income: Income) => {
    setEditingIncome(income)
    setIsIncomeDialogOpen(true)
  }

  // حذف إيراد
  const handleDeleteIncome = async (id: string) => {
    if (!confirm('هل أنت متأكد من حذف هذا الإيراد؟\n\nهذا الإجراء لا يمكن التراجع عنه.')) return

    try {
      const response = await fetch(`/api/incomes/${id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        alert(error.error || 'فشل في حذف الإيراد')
        return
      }

      alert('تم حذف الإيراد بنجاح')
      fetchIncomes()
      fetchStats()
    } catch (error) {
      console.error('خطأ في حذف الإيراد:', error)
      alert('حدث خطأ في حذف الإيراد')
    }
  }

  // إضافة إيراد جديد
  const handleAddNewIncome = () => {
    setEditingIncome(null)
    setIsIncomeDialogOpen(true)
  }

  // إغلاق نافذة التعديل
  const handleCloseDialog = () => {
    setIsIncomeDialogOpen(false)
    setEditingIncome(null)
  }

  // تصدير البيانات إلى PDF
  const handleExportIncomes = async () => {
    try {
      // جلب جميع الإيرادات للتصدير
      const response = await fetch('/api/incomes?limit=1000')
      if (!response.ok) throw new Error('فشل في جلب البيانات')

      const data: IncomesResponse = await response.json()
      const allIncomes = data.incomes

      // حساب الإحصائيات
      const totalAmount = allIncomes.reduce((sum, income) => sum + income.amount, 0)
      const totalCount = allIncomes.length
      const avgAmount = totalCount > 0 ? totalAmount / totalCount : 0

      // حساب الإحصائيات حسب النوع
      const byType = allIncomes.reduce((acc: Array<{ type: string; amount: number; count: number }>, income) => {
        const existing = acc.find((item) => item.type === income.type)
        if (existing) {
          existing.amount += income.amount
          existing.count += 1
        } else {
          acc.push({
            type: income.type,
            amount: income.amount,
            count: 1
          })
        }
        return acc
      }, [])

      // إنشاء HTML للتقرير
      const currentDate = new Date().toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })

      const htmlContent = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
          <meta charset="UTF-8">
          <style>
            @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;600;700&display=swap');

            * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
            }

            body {
              font-family: 'Noto Sans Arabic', Arial, sans-serif;
              direction: rtl;
              background: white;
              color: #333;
              line-height: 1.6;
              padding: 20px;
            }

            .header {
              text-align: center;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              padding: 30px;
              border-radius: 15px;
              margin-bottom: 30px;
              box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            }

            .header h1 {
              font-size: 32px;
              font-weight: 700;
              margin-bottom: 10px;
            }

            .header h2 {
              font-size: 24px;
              font-weight: 600;
              margin-bottom: 15px;
            }

            .header .date {
              font-size: 16px;
              opacity: 0.9;
            }

            .stats {
              display: grid;
              grid-template-columns: repeat(3, 1fr);
              gap: 20px;
              margin-bottom: 30px;
            }

            .stat-card {
              background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
              color: white;
              padding: 25px;
              border-radius: 15px;
              text-align: center;
              box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            }

            .stat-card.green {
              background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            }

            .stat-card.blue {
              background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            }

            .stat-card h3 {
              font-size: 16px;
              margin-bottom: 10px;
              font-weight: 600;
            }

            .stat-card .value {
              font-size: 24px;
              font-weight: 700;
            }

            .table-container {
              background: white;
              border-radius: 15px;
              overflow: hidden;
              box-shadow: 0 10px 30px rgba(0,0,0,0.1);
              margin-bottom: 30px;
            }

            .table-header {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              padding: 20px;
              text-align: center;
            }

            .table-header h3 {
              font-size: 20px;
              font-weight: 600;
            }

            table {
              width: 100%;
              border-collapse: collapse;
              font-size: 14px;
            }

            th {
              background: #f8f9fa;
              padding: 15px 10px;
              text-align: center;
              font-weight: 600;
              color: #495057;
              border-bottom: 2px solid #dee2e6;
            }

            td {
              padding: 12px 10px;
              text-align: center;
              border-bottom: 1px solid #dee2e6;
            }

            tr:nth-child(even) {
              background-color: #f8f9fa;
            }

            .amount {
              font-weight: 700;
              color: #28a745;
            }

            .type-badge {
              padding: 5px 12px;
              border-radius: 20px;
              font-size: 12px;
              font-weight: 600;
              color: white;
            }

            .type-subscription { background: #007bff; }
            .type-donation { background: #28a745; }
            .type-event { background: #6f42c1; }
            .type-other { background: #6c757d; }

            .summary {
              background: white;
              border-radius: 15px;
              padding: 25px;
              box-shadow: 0 10px 30px rgba(0,0,0,0.1);
              margin-bottom: 30px;
            }

            .summary h3 {
              color: #495057;
              margin-bottom: 20px;
              font-size: 18px;
              font-weight: 600;
              text-align: center;
            }

            .summary-grid {
              display: grid;
              grid-template-columns: repeat(2, 1fr);
              gap: 15px;
            }

            .summary-item {
              background: #f8f9fa;
              padding: 15px;
              border-radius: 10px;
              border-right: 4px solid #007bff;
            }

            .summary-item .label {
              font-weight: 600;
              color: #495057;
              margin-bottom: 5px;
            }

            .summary-item .value {
              font-size: 16px;
              font-weight: 700;
              color: #007bff;
            }

            .footer {
              text-align: center;
              margin-top: 40px;
              padding: 20px;
              background: #f8f9fa;
              border-radius: 10px;
              color: #6c757d;
            }

            @media print {
              body { padding: 0; }
              .header { margin-bottom: 20px; }
              .stats { margin-bottom: 20px; }
              .table-container { margin-bottom: 20px; }
              .summary { margin-bottom: 20px; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>تقرير الإيرادات</h1>
            <h2>ديوان أبو علوش</h2>
            <div class="date">تاريخ التقرير: ${currentDate}</div>
          </div>

          <div class="stats">
            <div class="stat-card">
              <h3>إجمالي الإيرادات</h3>
              <div class="value">${formatCurrency(totalAmount)}</div>
            </div>
            <div class="stat-card green">
              <h3>عدد الإيرادات</h3>
              <div class="value">${totalCount} إيراد</div>
            </div>
            <div class="stat-card blue">
              <h3>متوسط الإيراد</h3>
              <div class="value">${formatCurrency(avgAmount)}</div>
            </div>
          </div>

          <div class="table-container">
            <div class="table-header">
              <h3>تفاصيل الإيرادات</h3>
            </div>
            <table>
              <thead>
                <tr>
                  <th>المصدر</th>
                  <th>المبلغ (د.أ)</th>
                  <th>النوع</th>
                  <th>العضو</th>
                  <th>التاريخ</th>
                </tr>
              </thead>
              <tbody>
                ${allIncomes.map(income => `
                  <tr>
                    <td>${income.source}</td>
                    <td class="amount">${income.amount.toFixed(2)}</td>
                    <td>
                      <span class="type-badge type-${income.type.toLowerCase()}">
                        ${getIncomeTypeText(income.type)}
                      </span>
                    </td>
                    <td>${income.member?.name || 'غير محدد'}</td>
                    <td>${formatDate(income.date)}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>

          <div class="summary">
            <h3>ملخص الإيرادات حسب النوع</h3>
            <div class="summary-grid">
              ${byType.map((typeStats: { type: string; amount: number; count: number }) => `
                <div class="summary-item">
                  <div class="label">${getIncomeTypeText(typeStats.type)}</div>
                  <div class="value">${formatCurrency(typeStats.amount)} (${typeStats.count} إيراد)</div>
                </div>
              `).join('')}
            </div>
          </div>

          <div class="footer">
            <p>ديوان أبو علوش - نظام إدارة العائلة</p>
            <p>تم إنشاء هذا التقرير في ${currentDate}</p>
          </div>
        </body>
        </html>
      `

      // إنشاء عنصر مؤقت لعرض HTML
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = htmlContent
      tempDiv.style.position = 'absolute'
      tempDiv.style.left = '-9999px'
      tempDiv.style.top = '0'
      tempDiv.style.width = '210mm' // عرض A4
      document.body.appendChild(tempDiv)

      // انتظار تحميل الخطوط
      await new Promise(resolve => setTimeout(resolve, 1000))

      // تحويل HTML إلى canvas
      const canvas = await html2canvas(tempDiv, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: 794, // عرض A4 بالبكسل
        height: 1123, // ارتفاع A4 بالبكسل
      })

      // إزالة العنصر المؤقت
      document.body.removeChild(tempDiv)

      // إنشاء PDF
      const pdf = new jsPDF('p', 'mm', 'a4')
      const imgData = canvas.toDataURL('image/png')

      // حساب الأبعاد
      const imgWidth = 210 // عرض A4
      const pageHeight = 297 // ارتفاع A4
      const imgHeight = (canvas.height * imgWidth) / canvas.width
      let heightLeft = imgHeight

      let position = 0

      // إضافة الصفحة الأولى
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
      heightLeft -= pageHeight

      // إضافة صفحات إضافية إذا لزم الأمر
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight
        pdf.addPage()
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
        heightLeft -= pageHeight
      }

      // حفظ الملف
      const fileName = `تقرير_الإيرادات_${new Date().toISOString().split('T')[0]}.pdf`
      pdf.save(fileName)

      // رسالة نجاح
      setTimeout(() => {
        alert('تم إنشاء تقرير PDF بنجاح! ✅')
      }, 500)

    } catch (error) {
      console.error('خطأ في تصدير PDF:', error)
      alert('حدث خطأ في تصدير التقرير ❌')
    }
  }

  const canEdit = session?.user.role !== 'VIEWER'
  const canDelete = session?.user.role === 'ADMIN'

  return (
    <div className="space-y-8">
      {/* رأس الصفحة المحسن */}
      <div className="text-center mb-8">
        <div className="bg-gradient-to-r from-green-600 to-emerald-600 rounded-3xl shadow-2xl p-10 text-white relative overflow-hidden">
          {/* خلفية متحركة */}
          <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-500 opacity-30 animate-pulse"></div>

          {/* المحتوى */}
          <div className="relative z-10">
            <div className="inline-flex items-center justify-center w-20 h-20 rounded-full mb-6 bg-white bg-opacity-20 backdrop-blur-sm">
              <TrendingUp className="w-10 h-10 text-white" />
            </div>

            <h1 className="text-5xl font-black mb-4 text-white">
              إدارة الإيرادات
            </h1>

            <p className="text-xl font-semibold mb-6 text-green-100">
              عرض وإدارة إيرادات الديوان بكفاءة وسهولة
            </p>

            <div className="flex items-center justify-center space-x-2 space-x-reverse">
              <div className="h-1 w-16 rounded-full bg-white bg-opacity-60"></div>
              <div className="h-1 w-8 rounded-full bg-white bg-opacity-40"></div>
              <div className="h-1 w-16 rounded-full bg-white bg-opacity-60"></div>
            </div>
          </div>
        </div>
      </div>

      {/* أزرار الإجراءات */}
      <div className="flex justify-center mb-8">
        <div className="bg-white rounded-2xl shadow-xl p-6 border border-gray-100">
          <div className="flex flex-wrap gap-4 justify-center">
            <Button
              onClick={handleExportIncomes}
              className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold"
            >
              <FileText className="w-5 h-5 ml-2" />
              تصدير PDF
            </Button>
            {canEdit && (
              <Button
                onClick={handleAddNewIncome}
                className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold"
              >
                <Plus className="w-5 h-5 ml-2" />
                إضافة إيراد جديد
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* إحصائيات سريعة محسنة */}
      <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
        <Card className="group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative">
          <div className="absolute inset-0 bg-gradient-to-br from-green-500 to-green-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>

          <div className="bg-gradient-to-r from-green-500 to-green-600 p-1 rounded-t-xl"></div>

          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative z-10">
            <CardTitle className="text-sm font-bold" style={{ color: '#333333' }}>إجمالي الإيرادات</CardTitle>
            <div className="p-4 rounded-2xl shadow-lg" style={{ backgroundColor: '#28a745' }}>
              <DollarSign className="h-7 w-7 text-white" />
            </div>
          </CardHeader>

          <CardContent className="relative z-10">
            <div className="text-4xl font-black mb-2" style={{ color: '#191970' }}>
              {formatCurrency(stats.totalAmount)}
            </div>
            <p className="text-sm font-semibold" style={{ color: '#6c757d' }}>
              إجمالي المبالغ
            </p>
          </CardContent>
        </Card>

        <Card className="group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500 to-blue-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>

          <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-1 rounded-t-xl"></div>

          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative z-10">
            <CardTitle className="text-sm font-bold" style={{ color: '#333333' }}>عدد الإيرادات</CardTitle>
            <div className="p-4 rounded-2xl shadow-lg" style={{ backgroundColor: '#007bff' }}>
              <TrendingUp className="h-7 w-7 text-white" />
            </div>
          </CardHeader>

          <CardContent className="relative z-10">
            <div className="text-4xl font-black mb-2" style={{ color: '#191970' }}>
              {stats.totalCount}
            </div>
            <p className="text-sm font-semibold" style={{ color: '#6c757d' }}>
              إجمالي العمليات
            </p>
          </CardContent>
        </Card>

        <Card className="group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative sm:col-span-2 lg:col-span-1">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500 to-purple-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>

          <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-1 rounded-t-xl"></div>

          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative z-10">
            <CardTitle className="text-sm font-bold" style={{ color: '#333333' }}>متوسط الإيراد</CardTitle>
            <div className="p-4 rounded-2xl shadow-lg" style={{ backgroundColor: '#800020' }}>
              <Users className="h-7 w-7 text-white" />
            </div>
          </CardHeader>

          <CardContent className="relative z-10">
            <div className="text-4xl font-black mb-2" style={{ color: '#191970' }}>
              {formatCurrency(stats.totalCount > 0 ? stats.totalAmount / stats.totalCount : 0)}
            </div>
            <p className="text-sm font-semibold" style={{ color: '#6c757d' }}>
              متوسط القيمة
            </p>
          </CardContent>
        </Card>
      </div>

      {/* أدوات البحث والتصفية المحسنة */}
      <Card className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="bg-gradient-to-r from-gray-50 to-blue-50 p-6 border-b border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
            <div className="w-2 h-2 bg-diwan-600 rounded-full"></div>
            البحث والتصفية
          </h3>
          <p className="text-gray-600 text-sm">ابحث وصفي الإيرادات حسب المعايير المختلفة</p>
        </div>
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-6">
            <div className="relative flex-1">
              <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                <Search className="w-5 h-5" />
              </div>
              <Input
                placeholder="البحث في الإيرادات (المصدر، الوصف، العضو...)"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="h-12 pr-12 text-base border-gray-200 focus:border-diwan-500 focus:ring-diwan-100 transition-all duration-200 rounded-xl"
              />
            </div>
            <div className="relative">
              <select
                value={type}
                onChange={(e) => setType(e.target.value)}
                className="h-12 px-4 pr-10 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-diwan-500 focus:ring-4 focus:ring-diwan-100 transition-all duration-200 bg-white text-gray-700 font-medium min-w-[200px] appearance-none cursor-pointer"
              >
                <option value="all">🔍 جميع الأنواع</option>
                <option value="SUBSCRIPTION">💳 اشتراكات</option>
                <option value="DONATION">💝 تبرعات</option>
                <option value="EVENT">🎉 فعاليات</option>
                <option value="OTHER">📋 أخرى</option>
              </select>
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                <Filter className="w-4 h-4 text-gray-400" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* جدول الإيرادات المحسن */}
      <Card className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="bg-gradient-to-r from-gray-50 to-blue-50 p-6 border-b border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
            <div className="w-2 h-2 bg-green-600 rounded-full"></div>
            قائمة الإيرادات
          </h3>
          <p className="text-gray-600 text-sm">جميع إيرادات الديوان مع تفاصيلها الكاملة</p>
        </div>
        <CardContent className="p-0">
          {loading ? (
            <div className="flex flex-col justify-center items-center h-64 bg-gray-50">
              <div className="w-12 h-12 border-4 border-diwan-600 border-t-transparent rounded-full animate-spin mb-4"></div>
              <div className="text-gray-600 font-medium">جاري تحميل الإيرادات...</div>
              <div className="text-gray-400 text-sm mt-1">يرجى الانتظار</div>
            </div>
          ) : incomes.length === 0 ? (
            <div className="flex flex-col justify-center items-center h-64 bg-gray-50">
              <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mb-4">
                <TrendingUp className="w-8 h-8 text-gray-400" />
              </div>
              <div className="text-gray-600 font-medium mb-2">لا توجد إيرادات</div>
              <div className="text-gray-400 text-sm">ابدأ بإضافة إيراد جديد</div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50 hover:bg-gray-50">
                    <TableHead className="font-semibold text-gray-700 py-4">المصدر</TableHead>
                    <TableHead className="font-semibold text-gray-700 py-4">المبلغ</TableHead>
                    <TableHead className="font-semibold text-gray-700 py-4">النوع</TableHead>
                    <TableHead className="font-semibold text-gray-700 py-4">العضو</TableHead>
                    <TableHead className="font-semibold text-gray-700 py-4">التاريخ</TableHead>
                    <TableHead className="font-semibold text-gray-700 py-4 text-center">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {incomes.map((income) => (
                    <TableRow key={income.id} className="hover:bg-blue-50/50 transition-colors duration-200 border-b border-gray-100">
                      <TableCell className="py-4">
                        <div className="flex items-start gap-3">
                          <div className="w-10 h-10 bg-gradient-to-br from-diwan-100 to-blue-100 rounded-xl flex items-center justify-center flex-shrink-0">
                            <DollarSign className="w-5 h-5 text-diwan-600" />
                          </div>
                          <div>
                            <div className="font-semibold text-gray-900 mb-1">{income.source}</div>
                            {income.description && (
                              <div className="text-sm text-gray-500 leading-relaxed">{income.description}</div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                          <div className="font-bold text-lg text-green-600">
                            {formatCurrency(income.amount)}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <Badge
                          variant="secondary"
                          className={`px-3 py-1 rounded-full font-medium ${
                            income.type === 'SUBSCRIPTION' ? 'bg-blue-100 text-blue-700 border-blue-200' :
                            income.type === 'DONATION' ? 'bg-green-100 text-green-700 border-green-200' :
                            income.type === 'EVENT' ? 'bg-purple-100 text-purple-700 border-purple-200' :
                            'bg-gray-100 text-gray-700 border-gray-200'
                          }`}
                        >
                          {getIncomeTypeText(income.type)}
                        </Badge>
                      </TableCell>
                      <TableCell className="py-4">
                        <div className="flex items-center gap-2">
                          <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                            <Users className="w-4 h-4 text-gray-600" />
                          </div>
                          <span className="font-medium text-gray-700">
                            {income.member?.name || 'غير محدد'}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <div className="flex items-center gap-2 text-gray-600">
                          <Calendar className="w-4 h-4" />
                          <span className="font-medium">{formatDate(income.date)}</span>
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <div className="flex items-center justify-center gap-2">
                          {canEdit && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditIncome(income)}
                              className="h-9 w-9 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-all duration-200"
                              title="تعديل"
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                          )}
                          {canDelete && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteIncome(income.id)}
                              className="h-9 w-9 p-0 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-all duration-200"
                              title="حذف"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* التصفح المحسن */}
      {pagination.pages > 1 && (
        <Card className="bg-white rounded-2xl shadow-lg border border-gray-100">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
              <div className="flex items-center gap-2 text-gray-600">
                <span className="text-sm font-medium">
                  عرض {((pagination.page - 1) * pagination.limit) + 1} - {Math.min(pagination.page * pagination.limit, pagination.total)} من {pagination.total} إيراد
                </span>
              </div>
              <div className="flex items-center gap-3">
                <Button
                  variant="outline"
                  disabled={pagination.page === 1}
                  onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                  className="h-10 px-4 border-2 border-gray-200 hover:border-diwan-500 hover:text-diwan-600 transition-all duration-200 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="font-medium">السابق</span>
                </Button>
                <div className="flex items-center gap-2">
                  {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                    const pageNum = i + 1;
                    return (
                      <button
                        key={pageNum}
                        onClick={() => setPagination(prev => ({ ...prev, page: pageNum }))}
                        className={`w-10 h-10 rounded-lg font-semibold transition-all duration-200 ${
                          pagination.page === pageNum
                            ? 'bg-diwan-600 text-white shadow-lg'
                            : 'bg-gray-100 text-gray-600 hover:bg-diwan-100 hover:text-diwan-600'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                </div>
                <Button
                  variant="outline"
                  disabled={pagination.page === pagination.pages}
                  onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                  className="h-10 px-4 border-2 border-gray-200 hover:border-diwan-500 hover:text-diwan-600 transition-all duration-200 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="font-medium">التالي</span>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* إحصائيات حسب النوع المحسنة */}
      {stats.byType.length > 0 && (
        <Card className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="bg-gradient-to-r from-gray-50 to-purple-50 p-6 border-b border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
              <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
              الإيرادات حسب النوع
            </h3>
            <p className="text-gray-600 text-sm">توزيع الإيرادات على الأنواع المختلفة</p>
          </div>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
              {stats.byType.map((typeStats, index) => {
                const colors = [
                  { bg: 'from-blue-50 to-indigo-50', border: 'border-blue-200', text: 'text-blue-700', icon: 'bg-blue-100', dot: 'bg-blue-500' },
                  { bg: 'from-green-50 to-emerald-50', border: 'border-green-200', text: 'text-green-700', icon: 'bg-green-100', dot: 'bg-green-500' },
                  { bg: 'from-purple-50 to-pink-50', border: 'border-purple-200', text: 'text-purple-700', icon: 'bg-purple-100', dot: 'bg-purple-500' },
                  { bg: 'from-orange-50 to-red-50', border: 'border-orange-200', text: 'text-orange-700', icon: 'bg-orange-100', dot: 'bg-orange-500' }
                ];
                const color = colors[index % colors.length];

                return (
                  <div
                    key={typeStats.type}
                    className={`relative overflow-hidden bg-gradient-to-br ${color.bg} ${color.border} border-2 rounded-2xl p-6 hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02]`}
                  >
                    <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>
                    <div className="relative">
                      <div className="flex items-center justify-between mb-4">
                        <div className={`p-3 ${color.icon} rounded-xl shadow-sm`}>
                          {typeStats.type === 'SUBSCRIPTION' && <Users className="w-5 h-5 text-blue-600" />}
                          {typeStats.type === 'DONATION' && <DollarSign className="w-5 h-5 text-green-600" />}
                          {typeStats.type === 'EVENT' && <Calendar className="w-5 h-5 text-purple-600" />}
                          {typeStats.type === 'OTHER' && <TrendingUp className="w-5 h-5 text-orange-600" />}
                        </div>
                        <div className={`w-3 h-3 ${color.dot} rounded-full animate-pulse`}></div>
                      </div>
                      <div className="text-sm font-semibold text-gray-600 mb-2">
                        {getIncomeTypeText(typeStats.type)}
                      </div>
                      <div className={`text-2xl font-bold ${color.text} mb-2`}>
                        {formatCurrency(typeStats._sum.amount || 0)}
                      </div>
                      <div className="flex items-center gap-2">
                        <div className={`w-2 h-2 ${color.dot} rounded-full`}></div>
                        <span className="text-sm text-gray-500 font-medium">
                          {typeStats._count} إيراد
                        </span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* حوار إضافة/تعديل إيراد */}
      <IncomeDialog
        open={isIncomeDialogOpen}
        onOpenChange={handleCloseDialog}
        income={editingIncome}
        onSuccess={() => {
          fetchIncomes()
          fetchStats()
        }}
      />
    </div>
  )
}
