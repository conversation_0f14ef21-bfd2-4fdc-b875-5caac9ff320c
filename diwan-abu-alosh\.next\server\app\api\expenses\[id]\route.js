(()=>{var e={};e.id=8865,e.ids=[8865],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,s)=>{"use strict";s.d(r,{N:()=>a});var t=s(13581),n=s(85663),i=s(31183);let a={providers:[(0,t.A)({name:"credentials",credentials:{email:{label:"البريد الإلكتروني",type:"email"},password:{label:"كلمة المرور",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await i.z.user.findUnique({where:{email:e.email}});return r&&await n.Ay.compare(e.password,r.password)?{id:r.id,email:r.email,name:r.name,role:r.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,s)=>{"use strict";s.d(r,{z:()=>n});var t=s(96330);let n=globalThis.prisma??new t.PrismaClient},42473:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>h,routeModule:()=>N,serverHooks:()=>g,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>j});var t={};s.r(t),s.d(t,{DELETE:()=>m,GET:()=>x,PUT:()=>w});var n=s(96559),i=s(48088),a=s(37719),o=s(32190),u=s(19854),p=s(12909),l=s(31183),c=s(45697);let d=c.z.object({amount:c.z.number().positive("المبلغ يجب أن يكون أكبر من صفر"),date:c.z.string().transform(e=>new Date(e)),description:c.z.string().min(1,"الوصف مطلوب"),category:c.z.enum(["MEETINGS","EVENTS","MAINTENANCE","SOCIAL","GENERAL"]),recipient:c.z.string().optional().nullable(),notes:c.z.string().optional().nullable()});async function x(e,{params:r}){try{if(!await (0,u.getServerSession)(p.N))return o.NextResponse.json({error:"غير مصرح"},{status:401});let{id:e}=await r,s=await l.z.expense.findUnique({where:{id:e},include:{createdBy:{select:{name:!0}}}});if(!s)return o.NextResponse.json({error:"المصروف غير موجود"},{status:404});return o.NextResponse.json(s)}catch(e){return console.error("خطأ في جلب المصروف:",e),o.NextResponse.json({error:"حدث خطأ في جلب المصروف"},{status:500})}}async function w(e,{params:r}){try{let s=await (0,u.getServerSession)(p.N);if(!s)return o.NextResponse.json({error:"غير مصرح"},{status:401});if("VIEWER"===s.user.role)return o.NextResponse.json({error:"ليس لديك صلاحية لتعديل المصروفات"},{status:403});let t=await e.json(),{id:n}=await r,i=d.parse(t);if(!await l.z.expense.findUnique({where:{id:n}}))return o.NextResponse.json({error:"المصروف غير موجود"},{status:404});let a=await l.z.expense.update({where:{id:n},data:i,include:{createdBy:{select:{name:!0}}}});return o.NextResponse.json(a)}catch(e){if(console.error("خطأ في تحديث المصروف:",e),"ZodError"===e.name)return o.NextResponse.json({error:"بيانات غير صحيحة",details:e.errors},{status:400});return o.NextResponse.json({error:"حدث خطأ في تحديث المصروف"},{status:500})}}async function m(e,{params:r}){try{let e=await (0,u.getServerSession)(p.N);if(!e)return o.NextResponse.json({error:"غير مصرح"},{status:401});if("ADMIN"!==e.user.role)return o.NextResponse.json({error:"ليس لديك صلاحية لحذف المصروفات"},{status:403});let{id:s}=await r;if(!await l.z.expense.findUnique({where:{id:s}}))return o.NextResponse.json({error:"المصروف غير موجود"},{status:404});return await l.z.expense.delete({where:{id:s}}),o.NextResponse.json({message:"تم حذف المصروف بنجاح"})}catch(e){return console.error("خطأ في حذف المصروف:",e),o.NextResponse.json({error:"حدث خطأ في حذف المصروف"},{status:500})}}let N=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/expenses/[id]/route",pathname:"/api/expenses/[id]",filename:"route",bundlePath:"app/api/expenses/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\expenses\\[id]\\route.ts",nextConfigOutput:"standalone",userland:t}),{workAsyncStorage:f,workUnitAsyncStorage:j,serverHooks:g}=N;function h(){return(0,a.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:j})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4243,5663,4999,3412,580,5697],()=>s(42473));module.exports=t})();