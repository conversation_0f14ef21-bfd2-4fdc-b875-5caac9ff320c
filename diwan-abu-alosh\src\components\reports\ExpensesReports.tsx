'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { formatCurrency, formatDate } from '@/lib/utils'
import {
  TrendingDown,
  FileText,
  Download,
  Search,
  Filter,
  Calendar,
  DollarSign,
  Building,
  Receipt
} from 'lucide-react'

interface Expense {
  id: string
  amount: number
  date: string
  description: string
  category: 'MEETINGS' | 'GENERAL' | 'MAINTENANCE' | 'SOCIAL' | 'EVENTS' | 'OTHER'
  recipient?: string
  createdBy?: {
    name: string
  }
}

interface ExpensesReportsProps {
  onExportPDF: (data: any, type: string) => void
  onExportCSV: (data: any, type: string) => void
}

export default function ExpensesReports({ onExportPDF, onExportCSV }: ExpensesReportsProps) {
  const [expenses, setExpenses] = useState<Expense[]>([])
  const [filteredExpenses, setFilteredExpenses] = useState<Expense[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [dateFilter, setDateFilter] = useState('all')
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')

  // جلب بيانات المصروفات
  const fetchExpenses = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/expenses?limit=1000')
      if (!response.ok) throw new Error('فشل في جلب بيانات المصروفات')
      
      const data = await response.json()
      setExpenses(data.expenses || [])
      setFilteredExpenses(data.expenses || [])
    } catch (error) {
      console.error('خطأ في جلب بيانات المصروفات:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchExpenses()
  }, [])

  // تطبيق الفلاتر
  useEffect(() => {
    let filtered = expenses

    // فلتر البحث
    if (searchTerm) {
      filtered = filtered.filter(expense =>
        expense.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        expense.recipient?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // فلتر الفئة
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(expense => expense.category === categoryFilter)
    }

    // فلتر التاريخ
    if (dateFilter !== 'all') {
      const now = new Date()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      
      switch (dateFilter) {
        case 'today':
          filtered = filtered.filter(expense => {
            const expenseDate = new Date(expense.date)
            return expenseDate >= today
          })
          break
        case 'week':
          const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
          filtered = filtered.filter(expense => {
            const expenseDate = new Date(expense.date)
            return expenseDate >= weekAgo
          })
          break
        case 'month':
          const monthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate())
          filtered = filtered.filter(expense => {
            const expenseDate = new Date(expense.date)
            return expenseDate >= monthAgo
          })
          break
        case 'year':
          const yearAgo = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate())
          filtered = filtered.filter(expense => {
            const expenseDate = new Date(expense.date)
            return expenseDate >= yearAgo
          })
          break
        case 'custom':
          if (startDate && endDate) {
            const start = new Date(startDate)
            const end = new Date(endDate)
            filtered = filtered.filter(expense => {
              const expenseDate = new Date(expense.date)
              return expenseDate >= start && expenseDate <= end
            })
          }
          break
      }
    }

    setFilteredExpenses(filtered)
  }, [expenses, searchTerm, categoryFilter, dateFilter, startDate, endDate])

  // حساب الإحصائيات
  const getStatistics = () => {
    const total = filteredExpenses.reduce((sum, expense) => sum + expense.amount, 0)
    const count = filteredExpenses.length
    const average = count > 0 ? total / count : 0

    // إحصائيات حسب الفئة
    const byCategory = filteredExpenses.reduce((acc, expense) => {
      acc[expense.category] = (acc[expense.category] || 0) + expense.amount
      return acc
    }, {} as Record<string, number>)

    // إحصائيات حسب المستفيد
    const byRecipient = filteredExpenses.reduce((acc, expense) => {
      if (expense.recipient) {
        acc[expense.recipient] = (acc[expense.recipient] || 0) + expense.amount
      }
      return acc
    }, {} as Record<string, number>)

    return {
      total,
      count,
      average,
      byCategory,
      byRecipient
    }
  }

  // تصدير التقرير
  const handleExport = (format: 'pdf' | 'csv', reportType: 'detailed' | 'summary' | 'by-category' | 'by-recipient') => {
    const stats = getStatistics()
    
    const reportData: any = {
      title: '',
      date: new Date().toLocaleDateString('ar-JO'),
      period: getPeriodLabel(),
      statistics: stats
    }

    switch (reportType) {
      case 'detailed':
        reportData.title = 'تقرير المصروفات المفصل'
        reportData.expenses = filteredExpenses.map(expense => ({
          date: formatDate(expense.date),
          description: expense.description,
          category: getCategoryLabel(expense.category),
          amount: expense.amount,
          recipient: expense.recipient || 'غير محدد',
          createdBy: expense.createdBy?.name || 'غير محدد'
        }))
        break
      
      case 'summary':
        reportData.title = 'ملخص المصروفات'
        reportData.summary = {
          totalAmount: stats.total,
          totalCount: stats.count,
          averageAmount: stats.average
        }
        break
      
      case 'by-category':
        reportData.title = 'تقرير المصروفات حسب الفئة'
        reportData.byCategory = Object.entries(stats.byCategory).map(([category, amount]) => ({
          category: getCategoryLabel(category),
          amount,
          percentage: ((amount / stats.total) * 100).toFixed(1)
        }))
        break
      
      case 'by-recipient':
        reportData.title = 'تقرير المصروفات حسب المستفيد'
        reportData.byRecipient = Object.entries(stats.byRecipient)
          .sort(([,a], [,b]) => b - a)
          .map(([recipient, amount]) => ({
            recipient,
            amount,
            percentage: ((amount / stats.total) * 100).toFixed(1)
          }))
        break
    }

    if (format === 'pdf') {
      onExportPDF(reportData, `expenses-${reportType}`)
    } else {
      onExportCSV(reportData, `expenses-${reportType}`)
    }
  }

  // الحصول على تسمية الفئة
  const getCategoryLabel = (category: string) => {
    const categoryLabels = {
      'MEETINGS': 'اجتماعات',
      'GENERAL': 'عامة',
      'MAINTENANCE': 'صيانة',
      'SOCIAL': 'اجتماعية',
      'EVENTS': 'فعاليات',
      'OTHER': 'أخرى'
    }
    return categoryLabels[category as keyof typeof categoryLabels] || category
  }

  // الحصول على لون الفئة
  const getCategoryColor = (category: string) => {
    const categoryColors = {
      'MEETINGS': 'bg-blue-100 text-blue-800',
      'GENERAL': 'bg-gray-100 text-gray-800',
      'MAINTENANCE': 'bg-orange-100 text-orange-800',
      'SOCIAL': 'bg-green-100 text-green-800',
      'EVENTS': 'bg-purple-100 text-purple-800',
      'OTHER': 'bg-yellow-100 text-yellow-800'
    }
    return categoryColors[category as keyof typeof categoryColors] || 'bg-gray-100 text-gray-800'
  }

  // الحصول على تسمية الفترة
  const getPeriodLabel = () => {
    switch (dateFilter) {
      case 'today': return 'اليوم'
      case 'week': return 'آخر أسبوع'
      case 'month': return 'آخر شهر'
      case 'year': return 'آخر سنة'
      case 'custom': return `من ${startDate} إلى ${endDate}`
      default: return 'جميع الفترات'
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-gray-500">جاري تحميل بيانات المصروفات...</div>
      </div>
    )
  }

  const stats = getStatistics()

  return (
    <div className="space-y-6">
      {/* عنوان القسم */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-bold text-gray-900 flex items-center">
            <TrendingDown className="w-5 h-5 ml-2" />
            تقارير المصروفات
          </h2>
          <p className="text-gray-600">تقارير شاملة عن المصروفات والنفقات</p>
        </div>
        <div className="flex space-x-2 space-x-reverse">
          <Button
            onClick={() => handleExport('pdf', 'detailed')}
            className="bg-red-600 hover:bg-red-700 text-white"
          >
            <FileText className="w-4 h-4 ml-2" />
            تقرير مفصل PDF
          </Button>
          <Button
            onClick={() => handleExport('csv', 'detailed')}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            <Download className="w-4 h-4 ml-2" />
            تصدير CSV
          </Button>
        </div>
      </div>

      {/* أدوات البحث والفلترة */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute right-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="البحث في المصروفات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
            
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger>
                <SelectValue placeholder="فلترة حسب الفئة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الفئات</SelectItem>
                <SelectItem value="MEETINGS">اجتماعات</SelectItem>
                <SelectItem value="GENERAL">عامة</SelectItem>
                <SelectItem value="MAINTENANCE">صيانة</SelectItem>
                <SelectItem value="SOCIAL">اجتماعية</SelectItem>
                <SelectItem value="EVENTS">فعاليات</SelectItem>
                <SelectItem value="OTHER">أخرى</SelectItem>
              </SelectContent>
            </Select>

            <Select value={dateFilter} onValueChange={setDateFilter}>
              <SelectTrigger>
                <SelectValue placeholder="فلترة حسب التاريخ" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الفترات</SelectItem>
                <SelectItem value="today">اليوم</SelectItem>
                <SelectItem value="week">آخر أسبوع</SelectItem>
                <SelectItem value="month">آخر شهر</SelectItem>
                <SelectItem value="year">آخر سنة</SelectItem>
                <SelectItem value="custom">فترة مخصصة</SelectItem>
              </SelectContent>
            </Select>

            <div className="text-sm text-gray-600 flex items-center">
              <Filter className="w-4 h-4 ml-2" />
              عدد النتائج: {filteredExpenses.length} من {expenses.length}
            </div>
          </div>

          {/* فلتر التاريخ المخصص */}
          {dateFilter === 'custom' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">من تاريخ</label>
                <Input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">إلى تاريخ</label>
                <Input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* الإحصائيات السريعة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <DollarSign className="w-8 h-8 text-red-500" />
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي المصروفات</p>
                <p className="text-2xl font-bold text-red-600">{formatCurrency(stats.total)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <Receipt className="w-8 h-8 text-blue-500" />
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">عدد المعاملات</p>
                <p className="text-2xl font-bold text-blue-600">{stats.count}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <TrendingDown className="w-8 h-8 text-purple-500" />
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">متوسط المبلغ</p>
                <p className="text-2xl font-bold text-purple-600">{formatCurrency(stats.average)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <Calendar className="w-8 h-8 text-orange-500" />
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">الفترة</p>
                <p className="text-lg font-bold text-orange-600">{getPeriodLabel()}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* أزرار التقارير المختلفة */}
      <Card>
        <CardHeader>
          <CardTitle>تقارير متخصصة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium">تقرير حسب الفئة</h4>
              <div className="flex space-x-2 space-x-reverse">
                <Button
                  size="sm"
                  onClick={() => handleExport('pdf', 'by-category')}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  PDF
                </Button>
                <Button
                  size="sm"
                  onClick={() => handleExport('csv', 'by-category')}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  CSV
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">تقرير حسب المستفيد</h4>
              <div className="flex space-x-2 space-x-reverse">
                <Button
                  size="sm"
                  onClick={() => handleExport('pdf', 'by-recipient')}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  PDF
                </Button>
                <Button
                  size="sm"
                  onClick={() => handleExport('csv', 'by-recipient')}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  CSV
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">ملخص المصروفات</h4>
              <div className="flex space-x-2 space-x-reverse">
                <Button
                  size="sm"
                  onClick={() => handleExport('pdf', 'summary')}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  PDF
                </Button>
                <Button
                  size="sm"
                  onClick={() => handleExport('csv', 'summary')}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  CSV
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* جدول المصروفات */}
      <Card>
        <CardHeader>
          <CardTitle>تفاصيل المصروفات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="text-right p-3 font-medium text-gray-700">التاريخ</th>
                  <th className="text-right p-3 font-medium text-gray-700">الوصف</th>
                  <th className="text-right p-3 font-medium text-gray-700">الفئة</th>
                  <th className="text-right p-3 font-medium text-gray-700">المبلغ</th>
                  <th className="text-right p-3 font-medium text-gray-700">المستفيد</th>
                  <th className="text-right p-3 font-medium text-gray-700">المسؤول</th>
                </tr>
              </thead>
              <tbody>
                {filteredExpenses.map((expense) => (
                  <tr key={expense.id} className="border-b hover:bg-gray-50">
                    <td className="p-3">{formatDate(expense.date)}</td>
                    <td className="p-3 font-medium">{expense.description}</td>
                    <td className="p-3">
                      <Badge className={getCategoryColor(expense.category)}>
                        {getCategoryLabel(expense.category)}
                      </Badge>
                    </td>
                    <td className="p-3 font-bold text-red-600">
                      {formatCurrency(expense.amount)}
                    </td>
                    <td className="p-3">
                      <div className="flex items-center">
                        <Building className="w-4 h-4 ml-2 text-gray-400" />
                        {expense.recipient || 'غير محدد'}
                      </div>
                    </td>
                    <td className="p-3 text-sm text-gray-600">
                      {expense.createdBy?.name || 'غير محدد'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
