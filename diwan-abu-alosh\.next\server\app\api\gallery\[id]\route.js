(()=>{var e={};e.id=9078,e.ids=[9078],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{N:()=>o});var s=t(13581),i=t(85663),a=t(31183);let o={providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"البريد الإلكتروني",type:"email"},password:{label:"كلمة المرور",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await a.z.user.findUnique({where:{email:e.email}});return r&&await i.Ay.compare(e.password,r.password)?{id:r.id,email:r.email,name:r.name,role:r.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>i});var s=t(96330);let i=globalThis.prisma??new s.PrismaClient},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72998:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>m,serverHooks:()=>q,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>j});var s={};t.r(s),t.d(s,{DELETE:()=>h,GET:()=>y,PUT:()=>g});var i=t(96559),a=t(48088),o=t(37719),n=t(32190),u=t(19854),l=t(12909),p=t(31183),c=t(45697),d=t(79748),x=t(33873);let w=c.z.object({title:c.z.string().min(1,"العنوان مطلوب").optional(),description:c.z.string().optional(),activityId:c.z.string().optional()});async function y(e,{params:r}){try{if(!await (0,u.getServerSession)(l.N))return n.NextResponse.json({error:"غير مصرح"},{status:401});let{id:e}=await r,t=await p.z.galleryPhoto.findUnique({where:{id:e},include:{uploader:{select:{name:!0}},activity:{select:{id:!0,title:!0}}}});if(!t)return n.NextResponse.json({error:"الصورة غير موجودة"},{status:404});return n.NextResponse.json(t)}catch(e){return console.error("خطأ في جلب الصورة:",e),n.NextResponse.json({error:"حدث خطأ في جلب الصورة"},{status:500})}}async function g(e,{params:r}){try{let t=await (0,u.getServerSession)(l.N);if(!t)return n.NextResponse.json({error:"غير مصرح"},{status:401});let{id:s}=await r,i=await e.json(),a=await p.z.galleryPhoto.findUnique({where:{id:s}});if(!a)return n.NextResponse.json({error:"الصورة غير موجودة"},{status:404});if(a.uploadedBy!==t.user.id&&"ADMIN"!==t.user.role)return n.NextResponse.json({error:"غير مصرح لك بتعديل هذه الصورة"},{status:403});let o=w.parse(i),c=await p.z.galleryPhoto.update({where:{id:s},data:o,include:{uploader:{select:{name:!0}},activity:{select:{id:!0,title:!0}}}});return n.NextResponse.json(c)}catch(e){if(e instanceof c.z.ZodError)return n.NextResponse.json({error:"بيانات غير صحيحة",details:e.errors},{status:400});return console.error("خطأ في تحديث الصورة:",e),n.NextResponse.json({error:"حدث خطأ في تحديث الصورة"},{status:500})}}async function h(e,{params:r}){try{let e=await (0,u.getServerSession)(l.N);if(!e)return n.NextResponse.json({error:"غير مصرح"},{status:401});let{id:t}=await r,s=await p.z.galleryPhoto.findUnique({where:{id:t}});if(!s)return n.NextResponse.json({error:"الصورة غير موجودة"},{status:404});if(s.uploadedBy!==e.user.id&&"ADMIN"!==e.user.role)return n.NextResponse.json({error:"غير مصرح لك بحذف هذه الصورة"},{status:403});try{let e=(0,x.join)(process.cwd(),"public",s.imagePath);await (0,d.unlink)(e)}catch(e){console.warn("تعذر حذف الملف:",e)}return await p.z.galleryPhoto.delete({where:{id:t}}),n.NextResponse.json({success:!0,message:"تم حذف الصورة بنجاح"})}catch(e){return console.error("خطأ في حذف الصورة:",e),n.NextResponse.json({error:"حدث خطأ في حذف الصورة"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/gallery/[id]/route",pathname:"/api/gallery/[id]",filename:"route",bundlePath:"app/api/gallery/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery\\[id]\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:j,serverHooks:q}=m;function v(){return(0,o.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:j})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,5663,4999,3412,580,5697],()=>t(72998));module.exports=s})();