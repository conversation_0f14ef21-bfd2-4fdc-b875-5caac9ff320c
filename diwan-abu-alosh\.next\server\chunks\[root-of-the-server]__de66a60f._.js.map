{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Cred<PERSON><PERSON><PERSON><PERSON>ider from 'next-auth/providers/credentials'\nimport bcrypt from 'bcryptjs'\nimport { prisma } from './prisma'\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'البريد الإلكتروني', type: 'email' },\n        password: { label: 'كلمة المرور', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          role: user.role,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAqB,MAAM;gBAAQ;gBACnD,UAAU;oBAAE,OAAO;oBAAe,MAAM;gBAAW;YACrD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;YACxB;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;YAChC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/app/api/settings/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\n\n// الإعدادات الافتراضية\nconst defaultSettings = {\n  general: {\n    diwanName: 'ديوان آل أبو علوش',\n    diwanDescription: 'نظام إدارة ديوان آل أبو علوش',\n    diwanAddress: '',\n    diwanPhone: '',\n    diwanEmail: '',\n    diwanWebsite: '',\n    defaultCurrency: 'JOD',\n    currencySymbol: 'د.أ',\n    timezone: 'Asia/Amman',\n    dateFormat: 'DD/MM/YYYY',\n    timeFormat: '24h',\n    language: 'ar',\n    itemsPerPage: 10,\n    autoSave: true,\n    enableAuditLog: true,\n    sessionTimeout: 30,\n    showWelcomeMessage: true,\n    welcomeMessage: 'مرحباً بك في ديوان آل أبو علوش',\n    enableDashboardStats: true,\n    enableQuickActions: true\n  },\n  appearance: {\n    theme: 'light',\n    primaryColor: '#3b82f6',\n    secondaryColor: '#64748b',\n    accentColor: '#f59e0b',\n    backgroundColor: '#ffffff',\n    textColor: '#1f2937',\n    fontFamily: 'Cairo',\n    fontSize: '14px',\n    fontWeight: 'normal',\n    lineHeight: '1.5',\n    logo: '',\n    favicon: '',\n    brandName: 'ديوان آل أبو علوش',\n    brandColors: {\n      primary: '#3b82f6',\n      secondary: '#64748b'\n    },\n    sidebarStyle: 'default',\n    headerStyle: 'default',\n    cardStyle: 'default',\n    buttonStyle: 'default',\n    enableAnimations: true,\n    enableTransitions: true,\n    enableShadows: true,\n    enableGradients: false,\n    customCSS: '',\n    enableCustomCSS: false\n  },\n  notifications: {\n    enableNotifications: true,\n    enableSounds: true,\n    enableDesktopNotifications: true,\n    enableEmailNotifications: false,\n    enableSMSNotifications: false,\n    memberNotifications: {\n      newMember: true,\n      memberUpdate: true,\n      memberStatusChange: true,\n      memberPayment: true\n    },\n    incomeNotifications: {\n      newIncome: true,\n      incomeUpdate: true,\n      paymentReminder: true,\n      paymentOverdue: true\n    },\n    expenseNotifications: {\n      newExpense: true,\n      expenseUpdate: true,\n      budgetAlert: true,\n      expenseApproval: true\n    },\n    systemNotifications: {\n      systemUpdate: true,\n      securityAlert: true,\n      backupComplete: true,\n      errorAlert: true\n    },\n    quietHours: {\n      enabled: false,\n      startTime: '22:00',\n      endTime: '08:00'\n    },\n    emailSettings: {\n      smtpServer: '',\n      smtpPort: 587,\n      smtpUsername: '',\n      smtpPassword: '',\n      fromEmail: '',\n      fromName: 'ديوان آل أبو علوش',\n      enableSSL: true\n    },\n    smsSettings: {\n      provider: '',\n      apiKey: '',\n      senderName: 'ديوان آل أبو علوش'\n    },\n    templates: {\n      welcomeMessage: 'مرحباً بك في ديوان آل أبو علوش',\n      paymentReminder: 'تذكير: يرجى دفع الاشتراك الشهري',\n      paymentConfirmation: 'تم استلام دفعتك بنجاح',\n      systemAlert: 'تنبيه من النظام'\n    }\n  },\n  security: {\n    passwordPolicy: {\n      minLength: 8,\n      requireUppercase: true,\n      requireLowercase: true,\n      requireNumbers: true,\n      requireSpecialChars: false,\n      preventReuse: 5,\n      expirationDays: 90\n    },\n    sessionSettings: {\n      timeout: 30,\n      maxConcurrentSessions: 3,\n      requireReauth: false,\n      rememberMe: true,\n      rememberMeDuration: 30\n    },\n    loginSettings: {\n      maxFailedAttempts: 5,\n      lockoutDuration: 15,\n      enableCaptcha: false,\n      enableTwoFactor: false,\n      allowedIPs: [],\n      blockedIPs: []\n    },\n    auditSettings: {\n      enableAuditLog: true,\n      logLoginAttempts: true,\n      logDataChanges: true,\n      logSystemEvents: true,\n      retentionDays: 365,\n      enableRealTimeAlerts: true\n    },\n    permissionSettings: {\n      defaultRole: 'VIEWER',\n      allowSelfRegistration: false,\n      requireAdminApproval: true,\n      enableRoleHierarchy: true,\n      maxUsersPerRole: {\n        ADMIN: 3,\n        DATA_ENTRY: 10,\n        VIEWER: 100\n      }\n    },\n    advancedSecurity: {\n      enableEncryption: true,\n      enableSSL: true,\n      enableCSRF: true,\n      enableXSS: true,\n      enableSQLInjection: true,\n      enableRateLimit: true,\n      rateLimitRequests: 100,\n      rateLimitWindow: 15\n    }\n  },\n  backup: {\n    autoBackup: {\n      enabled: true,\n      frequency: 'daily',\n      time: '02:00',\n      retentionDays: 30,\n      includeFiles: true,\n      includeDatabase: true,\n      includeSettings: true\n    },\n    storage: {\n      location: 'local',\n      localPath: './backups',\n      cloudProvider: '',\n      cloudCredentials: {\n        accessKey: '',\n        secretKey: '',\n        bucket: '',\n        region: ''\n      }\n    },\n    importExport: {\n      allowDataExport: true,\n      allowDataImport: true,\n      exportFormats: ['json', 'csv', 'xlsx'],\n      maxFileSize: 100,\n      requireConfirmation: true\n    },\n    database: {\n      enableOptimization: true,\n      autoVacuum: true,\n      compressionLevel: 6,\n      encryptBackups: true\n    }\n  }\n}\n\n// GET - استرجاع الإعدادات\nexport async function GET() {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session) {\n      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })\n    }\n\n    // جلب جميع الإعدادات من قاعدة البيانات\n    const settings = await prisma.settings.findMany()\n    \n    // تنظيم الإعدادات حسب الفئة\n    const organizedSettings = { ...defaultSettings }\n    \n    settings.forEach(setting => {\n      try {\n        const value = JSON.parse(setting.value)\n        const category = setting.category.toLowerCase()\n        \n        if (organizedSettings[category as keyof typeof organizedSettings]) {\n          organizedSettings[category as keyof typeof organizedSettings] = {\n            ...organizedSettings[category as keyof typeof organizedSettings],\n            ...value\n          }\n        }\n      } catch (error) {\n        console.error(`خطأ في تحليل إعداد ${setting.key}:`, error)\n      }\n    })\n\n    return NextResponse.json(organizedSettings)\n  } catch (error) {\n    console.error('خطأ في جلب الإعدادات:', error)\n    return NextResponse.json({ error: 'خطأ في الخادم' }, { status: 500 })\n  }\n}\n\n// POST - حفظ الإعدادات\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session || session.user?.role !== 'ADMIN') {\n      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })\n    }\n\n    const settingsData = await request.json()\n\n    // حفظ كل فئة من الإعدادات\n    for (const [category, settings] of Object.entries(settingsData)) {\n      const categoryKey = category.toUpperCase() as 'GENERAL' | 'APPEARANCE' | 'NOTIFICATIONS' | 'SECURITY' | 'BACKUP'\n      \n      await prisma.settings.upsert({\n        where: { key: category },\n        update: {\n          value: JSON.stringify(settings),\n          category: categoryKey,\n          updatedAt: new Date()\n        },\n        create: {\n          key: category,\n          value: JSON.stringify(settings),\n          category: categoryKey\n        }\n      })\n    }\n\n    // تسجيل العملية في سجل التدقيق إذا كان مفعلاً\n    if (settingsData.general?.enableAuditLog) {\n      // يمكن إضافة سجل التدقيق هنا\n    }\n\n    return NextResponse.json({ success: true, message: 'تم حفظ الإعدادات بنجاح' })\n  } catch (error) {\n    console.error('خطأ في حفظ الإعدادات:', error)\n    return NextResponse.json({ error: 'خطأ في الخادم' }, { status: 500 })\n  }\n}\n\n// PUT - تحديث إعداد محدد\nexport async function PUT(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session || session.user?.role !== 'ADMIN') {\n      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })\n    }\n\n    const { key, value, category } = await request.json()\n\n    if (!key || !category) {\n      return NextResponse.json({ error: 'مفتاح الإعداد والفئة مطلوبان' }, { status: 400 })\n    }\n\n    await prisma.settings.upsert({\n      where: { key },\n      update: {\n        value: JSON.stringify(value),\n        category: category.toUpperCase(),\n        updatedAt: new Date()\n      },\n      create: {\n        key,\n        value: JSON.stringify(value),\n        category: category.toUpperCase()\n      }\n    })\n\n    return NextResponse.json({ success: true, message: 'تم تحديث الإعداد بنجاح' })\n  } catch (error) {\n    console.error('خطأ في تحديث الإعداد:', error)\n    return NextResponse.json({ error: 'خطأ في الخادم' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;AAEA,uBAAuB;AACvB,MAAM,kBAAkB;IACtB,SAAS;QACP,WAAW;QACX,kBAAkB;QAClB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,iBAAiB;QACjB,gBAAgB;QAChB,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,UAAU;QACV,cAAc;QACd,UAAU;QACV,gBAAgB;QAChB,gBAAgB;QAChB,oBAAoB;QACpB,gBAAgB;QAChB,sBAAsB;QACtB,oBAAoB;IACtB;IACA,YAAY;QACV,OAAO;QACP,cAAc;QACd,gBAAgB;QAChB,aAAa;QACb,iBAAiB;QACjB,WAAW;QACX,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,MAAM;QACN,SAAS;QACT,WAAW;QACX,aAAa;YACX,SAAS;YACT,WAAW;QACb;QACA,cAAc;QACd,aAAa;QACb,WAAW;QACX,aAAa;QACb,kBAAkB;QAClB,mBAAmB;QACnB,eAAe;QACf,iBAAiB;QACjB,WAAW;QACX,iBAAiB;IACnB;IACA,eAAe;QACb,qBAAqB;QACrB,cAAc;QACd,4BAA4B;QAC5B,0BAA0B;QAC1B,wBAAwB;QACxB,qBAAqB;YACnB,WAAW;YACX,cAAc;YACd,oBAAoB;YACpB,eAAe;QACjB;QACA,qBAAqB;YACnB,WAAW;YACX,cAAc;YACd,iBAAiB;YACjB,gBAAgB;QAClB;QACA,sBAAsB;YACpB,YAAY;YACZ,eAAe;YACf,aAAa;YACb,iBAAiB;QACnB;QACA,qBAAqB;YACnB,cAAc;YACd,eAAe;YACf,gBAAgB;YAChB,YAAY;QACd;QACA,YAAY;YACV,SAAS;YACT,WAAW;YACX,SAAS;QACX;QACA,eAAe;YACb,YAAY;YACZ,UAAU;YACV,cAAc;YACd,cAAc;YACd,WAAW;YACX,UAAU;YACV,WAAW;QACb;QACA,aAAa;YACX,UAAU;YACV,QAAQ;YACR,YAAY;QACd;QACA,WAAW;YACT,gBAAgB;YAChB,iBAAiB;YACjB,qBAAqB;YACrB,aAAa;QACf;IACF;IACA,UAAU;QACR,gBAAgB;YACd,WAAW;YACX,kBAAkB;YAClB,kBAAkB;YAClB,gBAAgB;YAChB,qBAAqB;YACrB,cAAc;YACd,gBAAgB;QAClB;QACA,iBAAiB;YACf,SAAS;YACT,uBAAuB;YACvB,eAAe;YACf,YAAY;YACZ,oBAAoB;QACtB;QACA,eAAe;YACb,mBAAmB;YACnB,iBAAiB;YACjB,eAAe;YACf,iBAAiB;YACjB,YAAY,EAAE;YACd,YAAY,EAAE;QAChB;QACA,eAAe;YACb,gBAAgB;YAChB,kBAAkB;YAClB,gBAAgB;YAChB,iBAAiB;YACjB,eAAe;YACf,sBAAsB;QACxB;QACA,oBAAoB;YAClB,aAAa;YACb,uBAAuB;YACvB,sBAAsB;YACtB,qBAAqB;YACrB,iBAAiB;gBACf,OAAO;gBACP,YAAY;gBACZ,QAAQ;YACV;QACF;QACA,kBAAkB;YAChB,kBAAkB;YAClB,WAAW;YACX,YAAY;YACZ,WAAW;YACX,oBAAoB;YACpB,iBAAiB;YACjB,mBAAmB;YACnB,iBAAiB;QACnB;IACF;IACA,QAAQ;QACN,YAAY;YACV,SAAS;YACT,WAAW;YACX,MAAM;YACN,eAAe;YACf,cAAc;YACd,iBAAiB;YACjB,iBAAiB;QACnB;QACA,SAAS;YACP,UAAU;YACV,WAAW;YACX,eAAe;YACf,kBAAkB;gBAChB,WAAW;gBACX,WAAW;gBACX,QAAQ;gBACR,QAAQ;YACV;QACF;QACA,cAAc;YACZ,iBAAiB;YACjB,iBAAiB;YACjB,eAAe;gBAAC;gBAAQ;gBAAO;aAAO;YACtC,aAAa;YACb,qBAAqB;QACvB;QACA,UAAU;YACR,oBAAoB;YACpB,YAAY;YACZ,kBAAkB;YAClB,gBAAgB;QAClB;IACF;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAW,GAAG;gBAAE,QAAQ;YAAI;QAChE;QAEA,uCAAuC;QACvC,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ;QAE/C,4BAA4B;QAC5B,MAAM,oBAAoB;YAAE,GAAG,eAAe;QAAC;QAE/C,SAAS,OAAO,CAAC,CAAA;YACf,IAAI;gBACF,MAAM,QAAQ,KAAK,KAAK,CAAC,QAAQ,KAAK;gBACtC,MAAM,WAAW,QAAQ,QAAQ,CAAC,WAAW;gBAE7C,IAAI,iBAAiB,CAAC,SAA2C,EAAE;oBACjE,iBAAiB,CAAC,SAA2C,GAAG;wBAC9D,GAAG,iBAAiB,CAAC,SAA2C;wBAChE,GAAG,KAAK;oBACV;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,mBAAmB,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE;YACtD;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAgB,GAAG;YAAE,QAAQ;QAAI;IACrE;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,WAAW,QAAQ,IAAI,EAAE,SAAS,SAAS;YAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAW,GAAG;gBAAE,QAAQ;YAAI;QAChE;QAEA,MAAM,eAAe,MAAM,QAAQ,IAAI;QAEvC,0BAA0B;QAC1B,KAAK,MAAM,CAAC,UAAU,SAAS,IAAI,OAAO,OAAO,CAAC,cAAe;YAC/D,MAAM,cAAc,SAAS,WAAW;YAExC,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAC3B,OAAO;oBAAE,KAAK;gBAAS;gBACvB,QAAQ;oBACN,OAAO,KAAK,SAAS,CAAC;oBACtB,UAAU;oBACV,WAAW,IAAI;gBACjB;gBACA,QAAQ;oBACN,KAAK;oBACL,OAAO,KAAK,SAAS,CAAC;oBACtB,UAAU;gBACZ;YACF;QACF;QAEA,8CAA8C;QAC9C,IAAI,aAAa,OAAO,EAAE,gBAAgB;QACxC,6BAA6B;QAC/B;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;YAAM,SAAS;QAAyB;IAC9E,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAgB,GAAG;YAAE,QAAQ;QAAI;IACrE;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,WAAW,QAAQ,IAAI,EAAE,SAAS,SAAS;YAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAW,GAAG;gBAAE,QAAQ;YAAI;QAChE;QAEA,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEnD,IAAI,CAAC,OAAO,CAAC,UAAU;YACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA+B,GAAG;gBAAE,QAAQ;YAAI;QACpF;QAEA,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC3B,OAAO;gBAAE;YAAI;YACb,QAAQ;gBACN,OAAO,KAAK,SAAS,CAAC;gBACtB,UAAU,SAAS,WAAW;gBAC9B,WAAW,IAAI;YACjB;YACA,QAAQ;gBACN;gBACA,OAAO,KAAK,SAAS,CAAC;gBACtB,UAAU,SAAS,WAAW;YAChC;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;YAAM,SAAS;QAAyB;IAC9E,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAgB,GAAG;YAAE,QAAQ;QAAI;IACrE;AACF", "debugId": null}}]}