exports.id=5977,exports.ids=[5977],exports.modules={4780:(e,t,r)=>{"use strict";r.d(t,{OR:()=>u,Tk:()=>d,WK:()=>m,Yq:()=>i,cn:()=>n,gr:()=>l,uF:()=>c,vv:()=>a});var o=r(49384),s=r(82348);function n(...e){return(0,s.QP)((0,o.$)(e))}function a(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"JOD",minimumFractionDigits:2}).format(e).replace("JOD","د.أ")}function i(e){let t="string"==typeof e?new Date(e):e;return isNaN(t.getTime())?"تاريخ غير صحيح":new Intl.DateTimeFormat("en-GB",{year:"numeric",month:"2-digit",day:"2-digit"}).format(t)}function l(e){return({ADMIN:"مدير",DATA_ENTRY:"مدخل بيانات",VIEWER:"مطلع"})[e]||e}function d(e){return({SUBSCRIPTION:"اشتراكات",DONATION:"تبرعات",EVENT:"فعاليات",OTHER:"أخرى"})[e]||e}function c(e){return({MEETINGS:"اجتماعات",EVENTS:"مناسبات",MAINTENANCE:"إصلاحات",SOCIAL:"اجتماعية",GENERAL:"عامة"})[e]||e}function m(e){return({ACTIVE:"نشط",LATE:"متأخر",INACTIVE:"غير ملتزم",SUSPENDED:"موقوف مؤقتاً",ARCHIVED:"مؤرشف"})[e]||e}function u(e){return({ACTIVE:"bg-green-100 text-green-800",LATE:"bg-yellow-100 text-yellow-800",INACTIVE:"bg-red-100 text-red-800",SUSPENDED:"bg-orange-100 text-orange-800",ARCHIVED:"bg-gray-100 text-gray-800"})[e]||"bg-gray-100 text-gray-800"}},5691:()=>{},7328:()=>{},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>a});var o=r(13581),s=r(85663),n=r(31183);let a={providers:[(0,o.A)({name:"credentials",credentials:{email:{label:"البريد الإلكتروني",type:"email"},password:{label:"كلمة المرور",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let t=await n.z.user.findUnique({where:{email:e.email}});return t&&await s.Ay.compare(e.password,t.password)?{id:t.id,email:t.email,name:t.name,role:t.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},18495:()=>{},25711:()=>{},28637:(e,t,r)=>{"use strict";r.d(t,{default:()=>h});var o=r(60687),s=r(43210),n=r.n(s),a=r(43649),i=r(78122),l=r(32192),d=r(29523),c=r(44493);class m extends n().Component{constructor(e){super(e),this.retry=()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0})},this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("خطأ في التطبيق:",e,t),e.message.includes("Unexpected token")&&e.message.includes("<!DOCTYPE")&&console.error("\uD83D\uDEA8 الخادم أرجع HTML بدلاً من JSON - تحقق من حالة الخادم"),this.setState({error:e,errorInfo:t})}render(){if(this.state.hasError){if(this.props.fallback){let e=this.props.fallback;return(0,o.jsx)(e,{error:this.state.error,retry:this.retry})}return(0,o.jsx)(u,{error:this.state.error,retry:this.retry})}return this.props.children}}function u({error:e,retry:t}){let r=e.message.includes("Unexpected token")&&e.message.includes("<!DOCTYPE");return(0,o.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-orange-100 p-4",children:(0,o.jsxs)(c.Zp,{className:"w-full max-w-md shadow-xl",children:[(0,o.jsxs)(c.aR,{className:"text-center space-y-4",children:[(0,o.jsx)("div",{className:"mx-auto w-20 h-20 bg-gradient-to-br from-red-600 to-orange-600 rounded-full flex items-center justify-center shadow-lg",children:(0,o.jsx)(a.A,{className:"w-10 h-10 text-white"})}),(0,o.jsx)("div",{children:(0,o.jsx)(c.ZB,{className:"text-2xl font-bold text-gray-900",children:"حدث خطأ غير متوقع"})})]}),(0,o.jsxs)(c.Wu,{className:"space-y-4",children:[r?(0,o.jsxs)("div",{className:"bg-orange-50 border border-orange-200 text-orange-700 px-4 py-3 rounded-lg text-sm",children:[(0,o.jsx)("p",{className:"font-semibold mb-2",children:"\uD83D\uDEA8 مشكلة في الاتصال بالخادم"}),(0,o.jsx)("p",{children:"الخادم أرجع صفحة HTML بدلاً من البيانات المطلوبة. قد يكون هناك:"}),(0,o.jsxs)("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[(0,o.jsx)("li",{children:"مشكلة في الخادم"}),(0,o.jsx)("li",{children:"إعادة توجيه غير متوقعة"}),(0,o.jsx)("li",{children:"انقطاع في الاتصال"})]})]}):(0,o.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm",children:[(0,o.jsx)("p",{className:"font-semibold mb-2",children:"تفاصيل الخطأ:"}),(0,o.jsx)("p",{className:"font-mono text-xs break-all",children:e.message})]}),(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsxs)(d.$,{onClick:t,className:"w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium py-2.5",children:[(0,o.jsx)(i.A,{className:"w-4 h-4 ml-2"}),"إعادة المحاولة"]}),(0,o.jsxs)(d.$,{onClick:()=>window.location.href="/",variant:"outline",className:"w-full border-gray-300 text-gray-600 hover:bg-gray-50 font-medium py-2.5",children:[(0,o.jsx)(l.A,{className:"w-4 h-4 ml-2"}),"العودة للصفحة الرئيسية"]})]}),!1]})]})})}let h=m},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var o=r(60687),s=r(43210),n=r(24224),a=r(4780);let i=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-semibold transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-95",{variants:{variant:{default:"bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-primary-600 hover:to-primary-700",destructive:"bg-gradient-to-r from-danger-500 to-danger-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-danger-600 hover:to-danger-700",outline:"border-2 border-primary-300 bg-transparent text-primary-700 hover:bg-primary-50 hover:border-primary-400 hover:shadow-md",secondary:"bg-gradient-to-r from-secondary-100 to-secondary-200 text-secondary-800 shadow-md hover:shadow-lg hover:-translate-y-0.5 hover:from-secondary-200 hover:to-secondary-300",ghost:"text-primary-600 hover:bg-primary-100 hover:text-primary-800",link:"text-primary-600 underline-offset-4 hover:underline hover:text-primary-800",success:"bg-gradient-to-r from-success-500 to-success-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-success-600 hover:to-success-700",warning:"bg-gradient-to-r from-warning-500 to-warning-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-warning-600 hover:to-warning-700",info:"bg-gradient-to-r from-info-500 to-info-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-info-600 hover:to-info-700",accent:"bg-gradient-to-r from-gold-500 to-gold-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-gold-600 hover:to-gold-700"},size:{default:"h-11 px-6 py-2.5",sm:"h-9 rounded-lg px-4 text-xs",lg:"h-13 rounded-xl px-8 text-base",icon:"h-11 w-11",xs:"h-8 rounded-md px-3 text-xs"}},defaultVariants:{variant:"default",size:"default"}}),l=s.forwardRef(({className:e,variant:t,size:r,...s},n)=>(0,o.jsx)("button",{className:(0,a.cn)(i({variant:t,size:r,className:e})),ref:n,...s}));l.displayName="Button"},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>s});var o=r(96330);let s=globalThis.prisma??new o.PrismaClient},31699:()=>{},32099:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\ابوعلوش\\\\diwan-abu-alosh\\\\src\\\\components\\\\layout\\\\dynamic-head.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\layout\\dynamic-head.tsx","default")},37708:()=>{},42423:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},44403:()=>{},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>a,aR:()=>i});var o=r(60687),s=r(43210),n=r(4780);let a=s.forwardRef(({className:e,...t},r)=>(0,o.jsx)("div",{ref:r,className:(0,n.cn)("rounded-xl border border-secondary-200 bg-white text-secondary-700 shadow-lg hover:shadow-xl transition-all duration-200 hover:-translate-y-1 backdrop-blur-sm",e),...t}));a.displayName="Card";let i=s.forwardRef(({className:e,...t},r)=>(0,o.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6 bg-gradient-to-r from-secondary-50 to-secondary-100 rounded-t-xl border-b border-secondary-200",e),...t}));i.displayName="CardHeader";let l=s.forwardRef(({className:e,...t},r)=>(0,o.jsx)("h3",{ref:r,className:(0,n.cn)("text-xl font-bold leading-none tracking-tight text-secondary-800",e),...t}));l.displayName="CardTitle";let d=s.forwardRef(({className:e,...t},r)=>(0,o.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-secondary-600 font-medium",e),...t}));d.displayName="CardDescription";let c=s.forwardRef(({className:e,...t},r)=>(0,o.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-4",e),...t}));c.displayName="CardContent",s.forwardRef(({className:e,...t},r)=>(0,o.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0 bg-gradient-to-r from-secondary-50 to-secondary-100 rounded-b-xl border-t border-secondary-200",e),...t})).displayName="CardFooter"},45217:(e,t,r)=>{"use strict";r.d(t,{default:()=>s}),r(43210);var o=r(77556);function s(){let{settings:e}=(0,o.r)();return null}},48482:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\sonner.tsx","Toaster")},50594:(e,t,r)=>{Promise.resolve().then(r.bind(r,52739)),Promise.resolve().then(r.bind(r,32099)),Promise.resolve().then(r.bind(r,83305)),Promise.resolve().then(r.bind(r,72931)),Promise.resolve().then(r.bind(r,48482))},52739:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\ابوعلوش\\\\diwan-abu-alosh\\\\src\\\\components\\\\error-boundary.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\error-boundary.tsx","default")},57492:()=>{},61135:()=>{},64616:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>n});var o=r(60687),s=r(52581);let n=({...e})=>(0,o.jsx)(s.l$,{theme:"light",className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})},67309:()=>{},68045:()=>{},68746:(e,t,r)=>{Promise.resolve().then(r.bind(r,28637)),Promise.resolve().then(r.bind(r,45217)),Promise.resolve().then(r.bind(r,80415)),Promise.resolve().then(r.bind(r,97177)),Promise.resolve().then(r.bind(r,64616))},72931:(e,t,r)=>{"use strict";r.d(t,{SettingsProvider:()=>s});var o=r(12907);let s=(0,o.registerClientReference)(function(){throw Error("Attempted to call SettingsProvider() from the server but SettingsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\settings-provider.tsx","SettingsProvider");(0,o.registerClientReference)(function(){throw Error("Attempted to call useSettings() from the server but useSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\settings-provider.tsx","useSettings")},77556:(e,t,r)=>{"use strict";r.d(t,{r:()=>n});var o=r(43210);let s={theme:"light",primaryColor:"#3b82f6",secondaryColor:"#64748b",accentColor:"#f59e0b",backgroundColor:"#ffffff",textColor:"#1f2937",fontFamily:"Cairo",fontSize:"14px",fontWeight:"normal",lineHeight:"1.5",logo:"",favicon:"",brandName:"ديوان آل أبو علوش",brandColors:{primary:"#3b82f6",secondary:"#64748b"},sidebarStyle:"default",headerStyle:"default",cardStyle:"default",buttonStyle:"default",enableAnimations:!0,enableTransitions:!0,enableShadows:!0,enableGradients:!1,customCSS:"",enableCustomCSS:!1};function n(){let[e,t]=(0,o.useState)(s),[r,n]=(0,o.useState)(!0);return{settings:e,loading:r,reload:async()=>{try{let e=await fetch("/api/settings");if(e.ok){let r=await e.json();r.appearance&&t({...s,...r.appearance})}}catch(e){console.error("خطأ في تحميل إعدادات المظهر:",e)}finally{n(!1)}}}}},80415:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var o=r(60687),s=r(82136);function n({children:e,session:t}){return(0,o.jsx)(s.SessionProvider,{session:t,children:e})}},83305:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\ابوعلوش\\\\diwan-abu-alosh\\\\src\\\\components\\\\providers\\\\session-provider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\providers\\session-provider.tsx","default")},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,metadata:()=>m});var o=r(37413),s=r(19854),n=r(12909),a=r(83305),i=r(72931),l=r(48482),d=r(32099),c=r(52739);r(61135),r(57492),r(25711),r(68045),r(37708),r(7328),r(67309),r(18495),r(5691),r(31699),r(44403);let m={title:"ديوان آل أبو علوش",description:"نظام إدارة ديوان آل أبو علوش - إدارة الأعضاء والإيرادات والمصروفات",keywords:["ديوان","إدارة","أعضاء","إيرادات","مصروفات"]};async function u({children:e}){let t=await (0,s.getServerSession)(n.N);return(0,o.jsx)("html",{lang:"ar",dir:"rtl",children:(0,o.jsx)("body",{className:"antialiased",children:(0,o.jsx)(a.default,{session:t,children:(0,o.jsx)(i.SettingsProvider,{children:(0,o.jsxs)(c.default,{children:[(0,o.jsx)(d.default,{}),e,(0,o.jsx)(l.Toaster,{})]})})})})})}},95567:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},97177:(e,t,r)=>{"use strict";r.d(t,{SettingsProvider:()=>a});var o=r(60687),s=r(43210);let n=(0,s.createContext)(void 0);function a({children:e}){let[t,r]=(0,s.useState)({}),[a,i]=(0,s.useState)(!0),l=e=>{if(e)try{if(e.appearance&&"object"==typeof e.appearance){let t=e.appearance;if(t.primaryColor&&document.documentElement.style.setProperty("--primary",t.primaryColor),t.secondaryColor&&document.documentElement.style.setProperty("--secondary",t.secondaryColor),t.accentColor&&document.documentElement.style.setProperty("--accent",t.accentColor),t.fontFamily&&(document.documentElement.style.setProperty("--font-family",t.fontFamily),document.body&&(document.body.style.fontFamily=t.fontFamily)),t.fontSize&&document.documentElement.style.setProperty("--font-size",t.fontSize),t.theme&&(document.documentElement.className="dark"===t.theme?"dark":""),t.enableCustomCSS&&t.customCSS){let e=document.getElementById("custom-css");e||((e=document.createElement("style")).id="custom-css",document.head.appendChild(e)),e.textContent=t.customCSS}}if(e.general&&"object"==typeof e.general){let t=e.general;if(t.diwanName&&(document.title=t.diwanName),t.favicon){let e=document.querySelector('link[rel="icon"]');e||((e=document.createElement("link")).rel="icon",document.head.appendChild(e)),e.href=t.favicon}}}catch(e){console.error("خطأ في تطبيق الإعدادات:",e)}};return(0,o.jsx)(n.Provider,{value:{settings:t,updateSettings:e=>{r(e),l(e)},loading:a},children:e})}}};