"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3942],{5845:(e,t,r)=>{r.d(t,{i:()=>i});var n,l=r(12115),o=r(52712),u=(n||(n=r.t(l,2)))[" useInsertionEffect ".trim().toString()]||o.N;function i({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[o,i,c]=function({defaultProp:e,onChange:t}){let[r,n]=l.useState(e),o=l.useRef(r),i=l.useRef(t);return u(()=>{i.current=t},[t]),l.useEffect(()=>{o.current!==r&&(i.current?.(r),o.current=r)},[r,o]),[r,n,i]}({defaultProp:t,onChange:r}),a=void 0!==e,f=a?e:o;{let t=l.useRef(void 0!==e);l.useEffect(()=>{let e=t.current;if(e!==a){let t=a?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=a},[a,n])}return[f,l.useCallback(t=>{if(a){let r="function"==typeof t?t(e):t;r!==e&&c.current?.(r)}else i(t)},[a,e,i,c])]}Symbol("RADIX:SYNC_STATE")},6101:(e,t,r)=>{r.d(t,{s:()=>u,t:()=>o});var n=r(12115);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let r=!1,n=e.map(e=>{let n=l(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():l(e[t],null)}}}}function u(...e){return n.useCallback(o(...e),e)}},37328:(e,t,r)=>{function n(e,t,r){if(!t.has(e))throw TypeError("attempted to "+r+" private field on non-instance");return t.get(e)}function l(e,t){var r=n(e,t,"get");return r.get?r.get.call(e):r.value}function o(e,t,r){var l=n(e,t,"set");if(l.set)l.set.call(e,r);else{if(!l.writable)throw TypeError("attempted to set read only private field");l.value=r}return r}r.d(t,{N:()=>d});var u,i=r(12115),c=r(46081),a=r(6101),f=r(99708),s=r(95155);function d(e){let t=e+"CollectionProvider",[r,n]=(0,c.A)(t),[l,o]=r(t,{collectionRef:{current:null},itemMap:new Map}),u=e=>{let{scope:t,children:r}=e,n=i.useRef(null),o=i.useRef(new Map).current;return(0,s.jsx)(l,{scope:t,itemMap:o,collectionRef:n,children:r})};u.displayName=t;let d=e+"CollectionSlot",p=(0,f.TL)(d),m=i.forwardRef((e,t)=>{let{scope:r,children:n}=e,l=o(d,r),u=(0,a.s)(t,l.collectionRef);return(0,s.jsx)(p,{ref:u,children:n})});m.displayName=d;let v=e+"CollectionItemSlot",y="data-radix-collection-item",h=(0,f.TL)(v),g=i.forwardRef((e,t)=>{let{scope:r,children:n,...l}=e,u=i.useRef(null),c=(0,a.s)(t,u),f=o(v,r);return i.useEffect(()=>(f.itemMap.set(u,{ref:u,...l}),()=>void f.itemMap.delete(u))),(0,s.jsx)(h,{...{[y]:""},ref:c,children:n})});return g.displayName=v,[{Provider:u,Slot:m,ItemSlot:g},function(t){let r=o(e+"CollectionConsumer",t);return i.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(y,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}var p=new WeakMap;function m(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=v(t),l=n>=0?n:r+n;return l<0||l>=r?-1:l}(e,t);return -1===r?void 0:e[r]}function v(e){return e!=e||0===e?0:Math.trunc(e)}u=new WeakMap},39033:(e,t,r)=>{r.d(t,{c:()=>l});var n=r(12115);function l(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},46081:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(12115),l=r(95155);function o(e,t=[]){let r=[],u=()=>{let t=r.map(e=>n.createContext(e));return function(r){let l=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:l}}),[r,l])}};return u.scopeName=e,[function(t,o){let u=n.createContext(o),i=r.length;r=[...r,o];let c=t=>{let{scope:r,children:o,...c}=t,a=r?.[e]?.[i]||u,f=n.useMemo(()=>c,Object.values(c));return(0,l.jsx)(a.Provider,{value:f,children:o})};return c.displayName=t+"Provider",[c,function(r,l){let c=l?.[e]?.[i]||u,a=n.useContext(c);if(a)return a;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let l=r.reduce((t,{useScope:r,scopeName:n})=>{let l=r(e)[`__scope${n}`];return{...t,...l}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return r.scopeName=t.scopeName,r}(u,...t)]}},52712:(e,t,r)=>{r.d(t,{N:()=>l});var n=r(12115),l=globalThis?.document?n.useLayoutEffect:()=>{}},61285:(e,t,r)=>{r.d(t,{B:()=>c});var n,l=r(12115),o=r(52712),u=(n||(n=r.t(l,2)))[" useId ".trim().toString()]||(()=>void 0),i=0;function c(e){let[t,r]=l.useState(u());return(0,o.N)(()=>{e||r(e=>e??String(i++))},[e]),e||(t?`radix-${t}`:"")}},63655:(e,t,r)=>{r.d(t,{hO:()=>c,sG:()=>i});var n=r(12115),l=r(47650),o=r(99708),u=r(95155),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,o.TL)(`Primitive.${t}`),l=n.forwardRef((e,n)=>{let{asChild:l,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(l?r:t,{...o,ref:n})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{});function c(e,t){e&&l.flushSync(()=>e.dispatchEvent(t))}},85185:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},94315:(e,t,r)=>{r.d(t,{jH:()=>o});var n=r(12115);r(95155);var l=n.createContext(void 0);function o(e){let t=n.useContext(l);return e||t||"ltr"}},99708:(e,t,r)=>{r.d(t,{TL:()=>u});var n=r(12115),l=r(6101),o=r(95155);function u(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){var u;let e,i,c=(u=r,(i=(e=Object.getOwnPropertyDescriptor(u.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?u.ref:(i=(e=Object.getOwnPropertyDescriptor(u,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?u.props.ref:u.props.ref||u.ref),a=function(e,t){let r={...t};for(let n in t){let l=e[n],o=t[n];/^on[A-Z]/.test(n)?l&&o?r[n]=(...e)=>{let t=o(...e);return l(...e),t}:l&&(r[n]=l):"style"===n?r[n]={...l,...o}:"className"===n&&(r[n]=[l,o].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==n.Fragment&&(a.ref=t?(0,l.t)(t,c):c),n.cloneElement(r,a)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:l,...u}=e,i=n.Children.toArray(l),a=i.find(c);if(a){let e=a.props.children,l=i.map(t=>t!==a?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...u,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,l):null})}return(0,o.jsx)(t,{...u,ref:r,children:l})});return r.displayName=`${e}.Slot`,r}var i=Symbol("radix.slottable");function c(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}}}]);