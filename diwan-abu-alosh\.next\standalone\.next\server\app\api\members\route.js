(()=>{var e={};e.id=8231,e.ids=[8231],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>a});var s=r(13581),i=r(85663),n=r(31183);let a={providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"البريد الإلكتروني",type:"email"},password:{label:"كلمة المرور",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let t=await n.z.user.findUnique({where:{email:e.email}});return t&&await i.Ay.compare(e.password,t.password)?{id:t.id,email:t.email,name:t.name,role:t.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>i});var s=r(96330);let i=globalThis.prisma??new s.PrismaClient},35186:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>b,routeModule:()=>z,serverHooks:()=>x,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>E});var s={};r.r(s),r.d(s,{GET:()=>d,POST:()=>m});var i=r(96559),n=r(48088),a=r(37719),o=r(32190),l=r(19854),u=r(12909),p=r(31183),c=r(85463);async function d(e){try{if(!await (0,l.getServerSession)(u.N))return o.NextResponse.json({error:"غير مصرح"},{status:401});let{searchParams:t}=new URL(e.url),r=t.get("search")||"",s=t.get("status")||"all",i=parseInt(t.get("page")||"1"),n=parseInt(t.get("limit")||"10"),a="true"===t.get("includeIncomes"),c=(i-1)*n,d={};r&&(d.OR=[{name:{contains:r}},{phone:{contains:r}},{address:{contains:r}}]),"all"!==s&&(d.status=s);let m={_count:{select:{incomes:!0}}};a?m.incomes={select:{id:!0,amount:!0,date:!0,source:!0,type:!0,description:!0},orderBy:{date:"desc"}}:m.incomes={select:{amount:!0}};let[z,g]=await Promise.all([p.z.member.findMany({where:d,skip:c,take:n,orderBy:{createdAt:"desc"},include:m}),p.z.member.count({where:d})]);return o.NextResponse.json({members:z,pagination:{page:i,limit:n,total:g,pages:Math.ceil(g/n)}})}catch(e){return console.error("خطأ في جلب الأعضاء:",e),o.NextResponse.json({error:"حدث خطأ في جلب الأعضاء"},{status:500})}}async function m(e){try{let t=await (0,l.getServerSession)(u.N);if(!t)return o.NextResponse.json({error:"غير مصرح"},{status:401});if("VIEWER"===t.user.role)return o.NextResponse.json({error:"ليس لديك صلاحية لإضافة الأعضاء"},{status:403});let r=await e.json(),s=c.QZ.parse(r);if(s.email&&await p.z.member.findFirst({where:{email:s.email}}))return o.NextResponse.json({error:"البريد الإلكتروني مستخدم بالفعل"},{status:400});let i=await p.z.member.create({data:{...s,createdById:t.user.id}});return o.NextResponse.json(i,{status:201})}catch(e){if(console.error("خطأ في إضافة العضو:",e),"ZodError"===e.name)return o.NextResponse.json({error:"بيانات غير صحيحة",details:e.errors},{status:400});return o.NextResponse.json({error:"حدث خطأ في إضافة العضو"},{status:500})}}let z=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/members/route",pathname:"/api/members",filename:"route",bundlePath:"app/api/members/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:E,serverHooks:x}=z;function b(){return(0,a.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:E})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85463:(e,t,r)=>{"use strict";r.d(t,{J8:()=>n,QZ:()=>i});var s=r(45697);s.z.object({name:s.z.string().min(2,"الاسم يجب أن يكون على الأقل حرفين"),email:s.z.string().email("البريد الإلكتروني غير صحيح"),password:s.z.string().min(6,"كلمة المرور يجب أن تكون على الأقل 6 أحرف"),role:s.z.enum(["ADMIN","DATA_ENTRY","VIEWER","MEMBER_VIEWER","GALLERY_VIEWER","MEMBER"]).default("VIEWER")});let i=s.z.object({name:s.z.string().min(2,"الاسم يجب أن يكون على الأقل حرفين"),phone:s.z.string().optional().or(s.z.literal("")).or(s.z.literal(null)),email:s.z.union([s.z.string().email("البريد الإلكتروني غير صحيح"),s.z.literal(""),s.z.literal(null),s.z.undefined()]).optional(),address:s.z.string().optional().or(s.z.literal("")).or(s.z.literal(null)),notes:s.z.string().optional().or(s.z.literal("")).or(s.z.literal(null)),photo:s.z.string().optional().or(s.z.literal("")).or(s.z.literal(null)),status:s.z.enum(["ACTIVE","LATE","INACTIVE","SUSPENDED","ARCHIVED"]).default("ACTIVE")}),n=s.z.object({amount:s.z.number().positive("المبلغ يجب أن يكون أكبر من صفر"),date:s.z.date(),source:s.z.string().min(1,"مصدر الإيراد مطلوب"),type:s.z.enum(["SUBSCRIPTION","DONATION","EVENT","OTHER"]).default("SUBSCRIPTION"),description:s.z.string().optional().nullable(),notes:s.z.string().optional().nullable(),memberId:s.z.string().optional().nullable()});s.z.object({amount:s.z.number().positive("المبلغ يجب أن يكون أكبر من صفر"),date:s.z.date(),description:s.z.string().min(1,"وصف المصروف مطلوب"),category:s.z.enum(["MEETINGS","EVENTS","MAINTENANCE","SOCIAL","GENERAL"]).default("GENERAL"),recipient:s.z.string().optional().nullable(),notes:s.z.string().optional().nullable()}),s.z.object({title:s.z.string().min(1,"عنوان النشاط مطلوب"),description:s.z.string().optional(),startDate:s.z.date(),endDate:s.z.date().optional(),location:s.z.string().optional(),organizers:s.z.string().optional(),participantIds:s.z.array(s.z.string()).optional()}),s.z.object({email:s.z.string().email("البريد الإلكتروني غير صحيح"),password:s.z.string().min(1,"كلمة المرور مطلوبة")})},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,5663,4999,3412,580,5697],()=>r(35186));module.exports=s})();