import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// POST - إعادة تعيين الإعدادات إلى القيم الافتراضية
export async function POST() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // حذف جميع الإعدادات الحالية
    await prisma.settings.deleteMany()

    // إنشاء الإعدادات الافتراضية
    const defaultSettings = [
      {
        key: 'general',
        value: JSON.stringify({
          diwanName: 'ديوان آل أبو علوش',
          diwanDescription: 'نظام إدارة ديوان آل أبو علوش',
          diwanAddress: '',
          diwanPhone: '',
          diwanEmail: '',
          diwanWebsite: '',
          defaultCurrency: 'JOD',
          currencySymbol: 'د.أ',
          timezone: 'Asia/Amman',
          dateFormat: 'DD/MM/YYYY',
          timeFormat: '24h',
          language: 'ar',
          itemsPerPage: 10,
          autoSave: true,
          enableAuditLog: true,
          sessionTimeout: 30,
          showWelcomeMessage: true,
          welcomeMessage: 'مرحباً بك في ديوان آل أبو علوش',
          enableDashboardStats: true,
          enableQuickActions: true
        }),
        category: 'GENERAL'
      },
      {
        key: 'appearance',
        value: JSON.stringify({
          theme: 'light',
          primaryColor: '#3b82f6',
          secondaryColor: '#64748b',
          accentColor: '#f59e0b',
          backgroundColor: '#ffffff',
          textColor: '#1f2937',
          fontFamily: 'Cairo',
          fontSize: '14px',
          fontWeight: 'normal',
          lineHeight: '1.5',
          logo: '',
          favicon: '',
          brandName: 'ديوان آل أبو علوش',
          brandColors: {
            primary: '#3b82f6',
            secondary: '#64748b'
          },
          sidebarStyle: 'default',
          headerStyle: 'default',
          cardStyle: 'default',
          buttonStyle: 'default',
          enableAnimations: true,
          enableTransitions: true,
          enableShadows: true,
          enableGradients: false,
          customCSS: '',
          enableCustomCSS: false
        }),
        category: 'APPEARANCE'
      },
      {
        key: 'notifications',
        value: JSON.stringify({
          enableNotifications: true,
          enableSounds: true,
          enableDesktopNotifications: true,
          enableEmailNotifications: false,
          enableSMSNotifications: false,
          memberNotifications: {
            newMember: true,
            memberUpdate: true,
            memberStatusChange: true,
            memberPayment: true
          },
          incomeNotifications: {
            newIncome: true,
            incomeUpdate: true,
            paymentReminder: true,
            paymentOverdue: true
          },
          expenseNotifications: {
            newExpense: true,
            expenseUpdate: true,
            budgetAlert: true,
            expenseApproval: true
          },
          systemNotifications: {
            systemUpdate: true,
            securityAlert: true,
            backupComplete: true,
            errorAlert: true
          },
          quietHours: {
            enabled: false,
            startTime: '22:00',
            endTime: '08:00'
          },
          emailSettings: {
            smtpServer: '',
            smtpPort: 587,
            smtpUsername: '',
            smtpPassword: '',
            fromEmail: '',
            fromName: 'ديوان آل أبو علوش',
            enableSSL: true
          },
          smsSettings: {
            provider: '',
            apiKey: '',
            senderName: 'ديوان آل أبو علوش'
          },
          templates: {
            welcomeMessage: 'مرحباً بك في ديوان آل أبو علوش',
            paymentReminder: 'تذكير: يرجى دفع الاشتراك الشهري',
            paymentConfirmation: 'تم استلام دفعتك بنجاح',
            systemAlert: 'تنبيه من النظام'
          }
        }),
        category: 'NOTIFICATIONS'
      },
      {
        key: 'security',
        value: JSON.stringify({
          passwordPolicy: {
            minLength: 8,
            requireUppercase: true,
            requireLowercase: true,
            requireNumbers: true,
            requireSpecialChars: false,
            preventReuse: 5,
            expirationDays: 90
          },
          sessionSettings: {
            timeout: 30,
            maxConcurrentSessions: 3,
            requireReauth: false,
            rememberMe: true,
            rememberMeDuration: 30
          },
          loginSettings: {
            maxFailedAttempts: 5,
            lockoutDuration: 15,
            enableCaptcha: false,
            enableTwoFactor: false,
            allowedIPs: [],
            blockedIPs: []
          },
          auditSettings: {
            enableAuditLog: true,
            logLoginAttempts: true,
            logDataChanges: true,
            logSystemEvents: true,
            retentionDays: 365,
            enableRealTimeAlerts: true
          },
          permissionSettings: {
            defaultRole: 'VIEWER',
            allowSelfRegistration: false,
            requireAdminApproval: true,
            enableRoleHierarchy: true,
            maxUsersPerRole: {
              ADMIN: 3,
              DATA_ENTRY: 10,
              VIEWER: 100
            }
          },
          advancedSecurity: {
            enableEncryption: true,
            enableSSL: true,
            enableCSRF: true,
            enableXSS: true,
            enableSQLInjection: true,
            enableRateLimit: true,
            rateLimitRequests: 100,
            rateLimitWindow: 15
          }
        }),
        category: 'SECURITY'
      },
      {
        key: 'backup',
        value: JSON.stringify({
          autoBackup: {
            enabled: true,
            frequency: 'daily',
            time: '02:00',
            retentionDays: 30,
            includeFiles: true,
            includeDatabase: true,
            includeSettings: true
          },
          storage: {
            location: 'local',
            localPath: './backups',
            cloudProvider: '',
            cloudCredentials: {
              accessKey: '',
              secretKey: '',
              bucket: '',
              region: ''
            }
          },
          importExport: {
            allowDataExport: true,
            allowDataImport: true,
            exportFormats: ['json', 'csv', 'xlsx'],
            maxFileSize: 100,
            requireConfirmation: true
          },
          database: {
            enableOptimization: true,
            autoVacuum: true,
            compressionLevel: 6,
            encryptBackups: true
          }
        }),
        category: 'BACKUP'
      }
    ]

    // إنشاء الإعدادات الافتراضية
    await prisma.settings.createMany({
      data: defaultSettings as Array<{ key: string; value: string; category: string }>
    })

    return NextResponse.json({ 
      success: true, 
      message: 'تم إعادة تعيين الإعدادات إلى القيم الافتراضية بنجاح' 
    })
  } catch (error) {
    console.error('خطأ في إعادة تعيين الإعدادات:', error)
    return NextResponse.json({ error: 'خطأ في الخادم' }, { status: 500 })
  }
}
