(()=>{var e={};e.id=5177,e.ids=[5177],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3695:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>S,routeModule:()=>y,serverHooks:()=>x,workAsyncStorage:()=>b,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{GET:()=>m,POST:()=>d,PUT:()=>g});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),l=r(19854),u=r(12909),c=r(31183);let p={general:{diwanName:"ديوان آل أبو علوش",diwanDescription:"نظام إدارة ديوان آل أبو علوش",diwanAddress:"",diwanPhone:"",diwanEmail:"",diwanWebsite:"",defaultCurrency:"JOD",currencySymbol:"د.أ",timezone:"Asia/Amman",dateFormat:"DD/MM/YYYY",timeFormat:"24h",language:"ar",itemsPerPage:10,autoSave:!0,enableAuditLog:!0,sessionTimeout:30,showWelcomeMessage:!0,welcomeMessage:"مرحباً بك في ديوان آل أبو علوش",enableDashboardStats:!0,enableQuickActions:!0},appearance:{theme:"light",primaryColor:"#3b82f6",secondaryColor:"#64748b",accentColor:"#f59e0b",backgroundColor:"#ffffff",textColor:"#1f2937",fontFamily:"Cairo",fontSize:"14px",fontWeight:"normal",lineHeight:"1.5",logo:"",favicon:"",brandName:"ديوان آل أبو علوش",brandColors:{primary:"#3b82f6",secondary:"#64748b"},sidebarStyle:"default",headerStyle:"default",cardStyle:"default",buttonStyle:"default",enableAnimations:!0,enableTransitions:!0,enableShadows:!0,enableGradients:!1,customCSS:"",enableCustomCSS:!1},notifications:{enableNotifications:!0,enableSounds:!0,enableDesktopNotifications:!0,enableEmailNotifications:!1,enableSMSNotifications:!1,memberNotifications:{newMember:!0,memberUpdate:!0,memberStatusChange:!0,memberPayment:!0},incomeNotifications:{newIncome:!0,incomeUpdate:!0,paymentReminder:!0,paymentOverdue:!0},expenseNotifications:{newExpense:!0,expenseUpdate:!0,budgetAlert:!0,expenseApproval:!0},systemNotifications:{systemUpdate:!0,securityAlert:!0,backupComplete:!0,errorAlert:!0},quietHours:{enabled:!1,startTime:"22:00",endTime:"08:00"},emailSettings:{smtpServer:"",smtpPort:587,smtpUsername:"",smtpPassword:"",fromEmail:"",fromName:"ديوان آل أبو علوش",enableSSL:!0},smsSettings:{provider:"",apiKey:"",senderName:"ديوان آل أبو علوش"},templates:{welcomeMessage:"مرحباً بك في ديوان آل أبو علوش",paymentReminder:"تذكير: يرجى دفع الاشتراك الشهري",paymentConfirmation:"تم استلام دفعتك بنجاح",systemAlert:"تنبيه من النظام"}},security:{passwordPolicy:{minLength:8,requireUppercase:!0,requireLowercase:!0,requireNumbers:!0,requireSpecialChars:!1,preventReuse:5,expirationDays:90},sessionSettings:{timeout:30,maxConcurrentSessions:3,requireReauth:!1,rememberMe:!0,rememberMeDuration:30},loginSettings:{maxFailedAttempts:5,lockoutDuration:15,enableCaptcha:!1,enableTwoFactor:!1,allowedIPs:[],blockedIPs:[]},auditSettings:{enableAuditLog:!0,logLoginAttempts:!0,logDataChanges:!0,logSystemEvents:!0,retentionDays:365,enableRealTimeAlerts:!0},permissionSettings:{defaultRole:"VIEWER",allowSelfRegistration:!1,requireAdminApproval:!0,enableRoleHierarchy:!0,maxUsersPerRole:{ADMIN:3,DATA_ENTRY:10,VIEWER:100}},advancedSecurity:{enableEncryption:!0,enableSSL:!0,enableCSRF:!0,enableXSS:!0,enableSQLInjection:!0,enableRateLimit:!0,rateLimitRequests:100,rateLimitWindow:15}},backup:{autoBackup:{enabled:!0,frequency:"daily",time:"02:00",retentionDays:30,includeFiles:!0,includeDatabase:!0,includeSettings:!0},storage:{location:"local",localPath:"./backups",cloudProvider:"",cloudCredentials:{accessKey:"",secretKey:"",bucket:"",region:""}},importExport:{allowDataExport:!0,allowDataImport:!0,exportFormats:["json","csv","xlsx"],maxFileSize:100,requireConfirmation:!0},database:{enableOptimization:!0,autoVacuum:!0,compressionLevel:6,encryptBackups:!0}}};async function m(){try{if(!await (0,l.getServerSession)(u.N))return o.NextResponse.json({error:"غير مصرح"},{status:401});let e=await c.z.settings.findMany(),t={...p};return e.forEach(e=>{try{let r=JSON.parse(e.value),s=e.category.toLowerCase();t[s]&&(t[s]={...t[s],...r})}catch(t){console.error(`خطأ في تحليل إعداد ${e.key}:`,t)}}),o.NextResponse.json(t)}catch(e){return console.error("خطأ في جلب الإعدادات:",e),o.NextResponse.json({error:"خطأ في الخادم"},{status:500})}}async function d(e){try{let t=await (0,l.getServerSession)(u.N);if(!t||t.user?.role!=="ADMIN")return o.NextResponse.json({error:"غير مصرح"},{status:401});let r=await e.json();for(let[e,t]of Object.entries(r)){let r=e.toUpperCase();await c.z.settings.upsert({where:{key:e},update:{value:JSON.stringify(t),category:r,updatedAt:new Date},create:{key:e,value:JSON.stringify(t),category:r}})}return r.general?.enableAuditLog,o.NextResponse.json({success:!0,message:"تم حفظ الإعدادات بنجاح"})}catch(e){return console.error("خطأ في حفظ الإعدادات:",e),o.NextResponse.json({error:"خطأ في الخادم"},{status:500})}}async function g(e){try{let t=await (0,l.getServerSession)(u.N);if(!t||t.user?.role!=="ADMIN")return o.NextResponse.json({error:"غير مصرح"},{status:401});let{key:r,value:s,category:a}=await e.json();if(!r||!a)return o.NextResponse.json({error:"مفتاح الإعداد والفئة مطلوبان"},{status:400});return await c.z.settings.upsert({where:{key:r},update:{value:JSON.stringify(s),category:a.toUpperCase(),updatedAt:new Date},create:{key:r,value:JSON.stringify(s),category:a.toUpperCase()}}),o.NextResponse.json({success:!0,message:"تم تحديث الإعداد بنجاح"})}catch(e){return console.error("خطأ في تحديث الإعداد:",e),o.NextResponse.json({error:"خطأ في الخادم"},{status:500})}}let y=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/settings/route",pathname:"/api/settings",filename:"route",bundlePath:"app/api/settings/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\settings\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:b,workUnitAsyncStorage:f,serverHooks:x}=y;function S(){return(0,n.patchFetch)({workAsyncStorage:b,workUnitAsyncStorage:f})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>n});var s=r(13581),a=r(85663),i=r(31183);let n={providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"البريد الإلكتروني",type:"email"},password:{label:"كلمة المرور",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let t=await i.z.user.findUnique({where:{email:e.email}});return t&&await a.Ay.compare(e.password,t.password)?{id:t.id,email:t.email,name:t.name,role:t.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});var s=r(96330);let a=globalThis.prisma??new s.PrismaClient},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,5663,4999,3412,580],()=>r(3695));module.exports=s})();