'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { memberSchema, type MemberInput } from '@/lib/validations'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  User,
  Phone,
  Mail,
  MapPin,
  FileText,
  UserCheck,
  Calendar,
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle,
  Archive,
  Save,
  X,
  Camera,
  Image as ImageIcon,
  Upload,
  Loader2
} from 'lucide-react'

interface Member {
  id: string
  name: string
  phone?: string
  email?: string
  address?: string
  photo?: string
  notes?: string
  status: string
}

interface MemberDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  member?: Member | null
  onSuccess: () => void
}

export default function MemberDialog({
  open,
  onOpenChange,
  member,
  onSuccess,
}: MemberDialogProps) {
  const [loading, setLoading] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [uploadError, setUploadError] = useState<string | null>(null)
  const isEditing = !!member

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm<MemberInput>({
    resolver: zodResolver(memberSchema),
    defaultValues: {
      name: '',
      phone: '',
      email: '',
      address: '',
      photo: '',
      notes: '',
      status: 'ACTIVE',
    },
  })

  const status = watch('status')

  // دالة للحصول على أيقونة الحالة
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'LATE':
        return <Clock className="w-4 h-4 text-yellow-500" />
      case 'INACTIVE':
        return <AlertCircle className="w-4 h-4 text-orange-500" />
      case 'SUSPENDED':
        return <XCircle className="w-4 h-4 text-red-500" />
      case 'ARCHIVED':
        return <Archive className="w-4 h-4 text-gray-500" />
      default:
        return <UserCheck className="w-4 h-4 text-blue-500" />
    }
  }

  // دالة للحصول على لون الحالة
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-50 text-green-700 border-green-200'
      case 'LATE':
        return 'bg-yellow-50 text-yellow-700 border-yellow-200'
      case 'INACTIVE':
        return 'bg-orange-50 text-orange-700 border-orange-200'
      case 'SUSPENDED':
        return 'bg-red-50 text-red-700 border-red-200'
      case 'ARCHIVED':
        return 'bg-gray-50 text-gray-700 border-gray-200'
      default:
        return 'bg-blue-50 text-blue-700 border-blue-200'
    }
  }

  // تحديث النموذج عند تغيير العضو
  useEffect(() => {
    if (member) {
      setValue('name', member.name)
      setValue('phone', member.phone || '')
      setValue('email', member.email || '')
      setValue('address', member.address || '')
      setValue('photo', member.photo || '')
      setValue('notes', member.notes || '')
      setValue('status', member.status as any)
    } else {
      reset({
        name: '',
        phone: '',
        email: '',
        address: '',
        photo: '',
        notes: '',
        status: 'ACTIVE',
      })
    }
  }, [member, setValue, reset])

  // دالة رفع الصورة
  const handleImageUpload = async (file: File) => {
    if (!file) return null

    // التحقق من نوع الملف
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      setUploadError('نوع الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG, WebP)')
      return null
    }

    // التحقق من حجم الملف (5MB)
    const maxSize = 5 * 1024 * 1024
    if (file.size > maxSize) {
      setUploadError('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت')
      return null
    }

    setUploadError(null)
    setUploading(true)

    try {
      const formData = new FormData()
      formData.append('file', file)

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'فشل في رفع الصورة')
      }

      const data = await response.json()
      setValue('photo', data.filePath)
      return data.filePath
    } catch (error: any) {
      console.error('خطأ في رفع الصورة:', error)
      setUploadError(error.message || 'حدث خطأ في رفع الصورة')
      return null
    } finally {
      setUploading(false)
    }
  }

  // دالة حذف الصورة
  const handleImageRemove = async () => {
    const currentPhoto = watch('photo')
    if (currentPhoto) {
      try {
        await fetch(`/api/upload?path=${encodeURIComponent(currentPhoto)}`, {
          method: 'DELETE',
        })
      } catch (error) {
        console.error('خطأ في حذف الصورة:', error)
      }
    }
    setValue('photo', '')
    setUploadError(null)
  }

  const onSubmit = async (data: MemberInput) => {
    try {
      setLoading(true)

      // تنظيف البيانات قبل الإرسال
      const cleanData = {
        ...data,
        phone: data.phone?.trim() || null,
        email: data.email?.trim() || null,
        address: data.address?.trim() || null,
        notes: data.notes?.trim() || null,
      }

      const url = isEditing ? `/api/members/${member.id}` : '/api/members'
      const method = isEditing ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(cleanData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'حدث خطأ')
      }

      // إظهار رسالة نجاح
      const successMessage = isEditing
        ? 'تم تحديث بيانات العضو بنجاح'
        : 'تم إضافة العضو الجديد بنجاح'

      // يمكن استبدال alert برسالة toast أكثر جمالاً
      alert(successMessage)

      onSuccess()
      onOpenChange(false)
      reset()
    } catch (error: any) {
      console.error('خطأ في حفظ العضو:', error)
      alert(error.message || 'حدث خطأ في حفظ العضو')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[50vw] h-[85vh] max-w-none max-h-none overflow-y-auto fixed top-[1vh] left-1/2 -translate-x-1/2 translate-y-0">

        <DialogHeader className="p-6 pb-4 border-b border-gray-100 bg-gradient-to-r from-diwan-50 to-blue-50">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-white rounded-xl shadow-sm border border-diwan-200">
              <User className="w-6 h-6 text-diwan-600" />
            </div>
            <div className="flex-1">
              <DialogTitle className="text-xl font-bold text-gray-900 mb-1">
                {isEditing ? 'تعديل بيانات العضو' : 'إضافة عضو جديد'}
              </DialogTitle>
              <DialogDescription className="text-gray-600">
                {isEditing
                  ? 'قم بتعديل بيانات العضو في النموذج أدناه'
                  : 'أدخل بيانات العضو الجديد في النموذج أدناه'}
              </DialogDescription>
              {!isEditing && (
                <div className="mt-2 text-xs text-diwan-600 bg-diwan-50 px-2 py-1 rounded-md inline-block">
                  💡 الحقول المطلوبة مميزة بعلامة *
                </div>
              )}
            </div>
          </div>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="p-8 pt-0 space-y-8">
          {/* صورة العضو والمعلومات الأساسية */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* صورة العضو */}
            <Card className="border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
              <CardContent className="p-5">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2 pb-2 border-b border-gray-100">
                  <div className="p-1 bg-indigo-100 rounded-lg">
                    <Camera className="w-4 h-4 text-indigo-600" />
                  </div>
                  صورة العضو
                  <span className="text-xs text-gray-500 font-normal mr-auto">اختياري</span>
                </h3>

                <div className="flex flex-col items-center">
                  {/* مكون رفع الصورة */}
                  <div className="space-y-3 w-full">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0]
                        if (file) {
                          handleImageUpload(file)
                        }
                      }}
                      className="hidden"
                      id="photo-upload"
                      disabled={loading || uploading}
                    />

                    {watch('photo') ? (
                      // عرض الصورة المرفوعة
                      <div className="relative group">
                        <div className="relative w-32 h-32 mx-auto rounded-xl overflow-hidden border-2 border-gray-200 shadow-lg">
                          <img
                            src={watch('photo') || undefined}
                            alt="صورة العضو"
                            className="w-full h-full object-cover"
                          />
                          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                            <Button
                              type="button"
                              variant="destructive"
                              size="sm"
                              onClick={handleImageRemove}
                              disabled={loading || uploading}
                              className="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                            >
                              <X className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                        <div className="text-center mt-2">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => document.getElementById('photo-upload')?.click()}
                            disabled={loading || uploading}
                            className="text-xs"
                          >
                            <Camera className="w-3 h-3 ml-1" />
                            تغيير الصورة
                          </Button>
                        </div>
                      </div>
                    ) : (
                      // منطقة رفع الصورة
                      <div
                        onClick={() => document.getElementById('photo-upload')?.click()}
                        className={`
                          relative w-full h-32 border-2 border-dashed rounded-xl cursor-pointer
                          transition-all duration-200 flex flex-col items-center justify-center
                          border-gray-300 hover:border-diwan-400 hover:bg-gray-50
                          ${(loading || uploading) ? 'opacity-50 cursor-not-allowed' : ''}
                        `}
                      >
                        {uploading ? (
                          <div className="flex flex-col items-center gap-2 text-diwan-600">
                            <Loader2 className="w-6 h-6 animate-spin" />
                            <span className="text-xs font-medium">جاري الرفع...</span>
                          </div>
                        ) : (
                          <div className="flex flex-col items-center gap-2 text-gray-500">
                            <div className="p-2 bg-gray-100 rounded-full">
                              <ImageIcon className="w-6 h-6" />
                            </div>
                            <div className="text-center">
                              <p className="text-xs font-medium text-gray-700">
                                اضغط لاختيار صورة
                              </p>
                              <p className="text-xs text-gray-500">
                                JPG, PNG, WebP
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {uploadError && (
                      <div className="bg-red-50 border border-red-200 rounded-lg p-2">
                        <div className="flex items-center gap-2 text-red-600">
                          <AlertCircle className="w-3 h-3 flex-shrink-0" />
                          <span className="text-xs">{uploadError}</span>
                        </div>
                      </div>
                    )}

                    {watch('photo') && !uploadError && (
                      <div className="bg-green-50 border border-green-200 rounded-lg p-2">
                        <div className="flex items-center gap-2 text-green-600">
                          <CheckCircle className="w-3 h-3 flex-shrink-0" />
                          <span className="text-xs">تم رفع الصورة بنجاح</span>
                        </div>
                      </div>
                    )}
                  </div>

                  <p className="text-xs text-gray-500 mt-3 text-center">
                    صورة شخصية للعضو
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* معلومات أساسية */}
            <Card className="lg:col-span-2 border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
              <CardContent className="p-5">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2 pb-2 border-b border-gray-100">
                  <div className="p-1 bg-diwan-100 rounded-lg">
                    <User className="w-4 h-4 text-diwan-600" />
                  </div>
                  المعلومات الأساسية
                  <span className="text-xs text-gray-500 font-normal mr-auto">مطلوب</span>
                </h3>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-sm font-medium text-gray-700 flex items-center gap-2">
                      <User className="w-4 h-4" />
                      الاسم الكامل *
                    </Label>
                    <Input
                      id="name"
                      {...register('name')}
                      placeholder="أدخل الاسم الكامل للعضو"
                      className={`h-12 text-base transition-all duration-200 ${
                        errors.name
                          ? 'border-red-300 focus:border-red-500 focus:ring-red-500 bg-red-50'
                          : 'border-gray-300 focus:border-diwan-500 focus:ring-diwan-500'
                      }`}
                    />
                    {errors.name && (
                      <div className="bg-red-50 border border-red-200 rounded-lg p-3 mt-1">
                        <p className="text-sm text-red-600 flex items-center gap-2">
                          <AlertCircle className="w-4 h-4 flex-shrink-0" />
                          {errors.name.message}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* معلومات الاتصال */}
          <Card className="border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
            <CardContent className="p-5">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2 pb-2 border-b border-gray-100">
                <div className="p-1 bg-blue-100 rounded-lg">
                  <Phone className="w-4 h-4 text-blue-600" />
                </div>
                معلومات الاتصال
                <span className="text-xs text-gray-500 font-normal mr-auto">اختياري</span>
              </h3>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="phone" className="text-sm font-medium text-gray-700 flex items-center gap-2">
                    <Phone className="w-4 h-4" />
                    رقم الهاتف
                  </Label>
                  <Input
                    id="phone"
                    {...register('phone')}
                    placeholder="07xxxxxxxx"
                    dir="ltr"
                    className="h-12 text-base border-gray-300 focus:border-diwan-500 focus:ring-diwan-500"
                  />
                  {errors.phone && (
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <AlertCircle className="w-4 h-4" />
                      {errors.phone.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email" className="text-sm font-medium text-gray-700 flex items-center gap-2">
                    <Mail className="w-4 h-4" />
                    البريد الإلكتروني
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    {...register('email')}
                    placeholder="<EMAIL>"
                    dir="ltr"
                    className="h-12 text-base border-gray-300 focus:border-diwan-500 focus:ring-diwan-500"
                  />
                  {errors.email && (
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <AlertCircle className="w-4 h-4" />
                      {errors.email.message}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* العنوان والملاحظات */}
          <Card className="border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
            <CardContent className="p-5">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2 pb-2 border-b border-gray-100">
                <div className="p-1 bg-green-100 rounded-lg">
                  <MapPin className="w-4 h-4 text-green-600" />
                </div>
                العنوان والملاحظات
                <span className="text-xs text-gray-500 font-normal mr-auto">اختياري</span>
              </h3>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="address" className="text-sm font-medium text-gray-700 flex items-center gap-2">
                    <MapPin className="w-4 h-4" />
                    العنوان
                  </Label>
                  <Input
                    id="address"
                    {...register('address')}
                    placeholder="أدخل عنوان السكن"
                    className="h-12 text-base border-gray-300 focus:border-diwan-500 focus:ring-diwan-500"
                  />
                  {errors.address && (
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <AlertCircle className="w-4 h-4" />
                      {errors.address.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notes" className="text-sm font-medium text-gray-700 flex items-center gap-2">
                    <FileText className="w-4 h-4" />
                    ملاحظات إضافية
                  </Label>
                  <Textarea
                    id="notes"
                    {...register('notes')}
                    placeholder="أدخل أي ملاحظات أو معلومات إضافية عن العضو"
                    rows={4}
                    className="text-base border-gray-300 focus:border-diwan-500 focus:ring-diwan-500 resize-none"
                  />
                  {errors.notes && (
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <AlertCircle className="w-4 h-4" />
                      {errors.notes.message}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* حالة العضو */}
          <Card className="border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
            <CardContent className="p-5">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2 pb-2 border-b border-gray-100">
                <div className="p-1 bg-purple-100 rounded-lg">
                  <UserCheck className="w-4 h-4 text-purple-600" />
                </div>
                حالة العضو
                <span className="text-xs text-gray-500 font-normal mr-auto">مطلوب</span>
              </h3>

              <div className="space-y-4">
                <div className="space-y-3">
                  <Label htmlFor="status" className="text-sm font-medium text-gray-700">
                    اختر حالة العضو
                  </Label>

                  {/* عرض الحالة الحالية */}
                  <div className="flex items-center gap-2 mb-3">
                    <span className="text-sm text-gray-600">الحالة الحالية:</span>
                    <Badge className={`${getStatusColor(status)} border`}>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(status)}
                        <span>
                          {status === 'ACTIVE' && 'نشط'}
                          {status === 'LATE' && 'متأخر'}
                          {status === 'INACTIVE' && 'غير ملتزم'}
                          {status === 'SUSPENDED' && 'موقوف مؤقتاً'}
                          {status === 'ARCHIVED' && 'مؤرشف'}
                        </span>
                      </div>
                    </Badge>
                  </div>

                  <select
                    id="status"
                    {...register('status')}
                    className="w-full h-12 px-4 py-3 text-base border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-diwan-500 focus:border-diwan-500 bg-white"
                  >
                    <option value="ACTIVE">✅ نشط - عضو فعال ومنتظم</option>
                    <option value="LATE">⏰ متأخر - متأخر في الدفع</option>
                    <option value="INACTIVE">⚠️ غير ملتزم - غير منتظم في الحضور</option>
                    <option value="SUSPENDED">❌ موقوف مؤقتاً - موقوف لفترة محددة</option>
                    <option value="ARCHIVED">📁 مؤرشف - عضو سابق</option>
                  </select>
                  {errors.status && (
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <AlertCircle className="w-4 h-4" />
                      {errors.status.message}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* أزرار الحفظ والإلغاء */}
          <div className="flex justify-between items-center gap-4 pt-8 border-t-2 border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 -mx-6 px-8 py-6 rounded-b-xl">
            <div className="text-sm text-gray-600 font-medium">
              {isEditing ? (
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  تعديل بيانات العضو
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  إضافة عضو جديد
                </div>
              )}
            </div>
            <div className="flex gap-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
                className="px-8 py-3 h-12 border-2 border-gray-300 text-gray-700 hover:bg-white hover:border-gray-400 hover:shadow-md transition-all duration-200 flex items-center gap-2 font-medium"
              >
                <X className="w-4 h-4" />
                إلغاء
              </Button>
              <Button
                type="submit"
                disabled={loading}
                className={`px-8 py-3 h-12 font-bold text-white shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2 ${
                  isEditing
                    ? 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 border-2 border-blue-500'
                    : 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 border-2 border-green-500'
                }`}
              >
                {loading ? (
                  <div className="flex items-center gap-2">
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span className="text-base">جاري الحفظ...</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    {isEditing ? (
                      <>
                        <CheckCircle className="w-5 h-5" />
                        <span className="text-base">تحديث البيانات</span>
                      </>
                    ) : (
                      <>
                        <Save className="w-5 h-5" />
                        <span className="text-base">حفظ العضو الجديد</span>
                      </>
                    )}
                  </div>
                )}
              </Button>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
