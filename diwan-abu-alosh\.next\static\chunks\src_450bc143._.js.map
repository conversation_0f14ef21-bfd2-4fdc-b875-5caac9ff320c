{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/providers/session-provider.tsx"], "sourcesContent": ["'use client'\n\nimport { SessionProvider as NextAuthSessionProvider } from 'next-auth/react'\nimport { Session } from 'next-auth'\n\ninterface SessionProviderProps {\n  children: React.ReactNode\n  session: Session | null\n}\n\nexport default function SessionProvider({ children, session }: SessionProviderProps) {\n  return (\n    <NextAuthSessionProvider session={session}>\n      {children}\n    </NextAuthSessionProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUe,SAAS,gBAAgB,EAAE,QAAQ,EAAE,OAAO,EAAwB;IACjF,qBACE,6LAAC,iJAAA,CAAA,kBAAuB;QAAC,SAAS;kBAC/B;;;;;;AAGP;KANwB", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/settings/settings-provider.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\n\ninterface SettingsContextType {\n  settings: any\n  updateSettings: (newSettings: any) => void\n  loading: boolean\n}\n\nconst SettingsContext = createContext<SettingsContextType | undefined>(undefined)\n\nexport function SettingsProvider({ children }: { children: React.ReactNode }) {\n  const [settings, setSettings] = useState<any>({})\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    loadSettings()\n  }, [])\n\n  const loadSettings = async () => {\n    try {\n      const response = await fetch('/api/settings')\n      if (response.ok) {\n        const data = await response.json()\n\n        setSettings(data)\n        applySettings(data)\n      } else {\n        console.error('فشل في تحميل الإعدادات:', response.status)\n      }\n    } catch (error) {\n      console.error('خطأ في تحميل الإعدادات:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const updateSettings = (newSettings: any) => {\n    setSettings(newSettings)\n    applySettings(newSettings)\n  }\n\n  const applySettings = (settingsData: any) => {\n    if (!settingsData) {\n      return\n    }\n\n    try {\n\n      // تطبيق إعدادات المظهر\n      if (settingsData.appearance && typeof settingsData.appearance === 'object') {\n        const appearance = settingsData.appearance\n\n        // تطبيق الألوان\n        if (appearance.primaryColor) {\n          document.documentElement.style.setProperty('--primary', appearance.primaryColor)\n        }\n        if (appearance.secondaryColor) {\n          document.documentElement.style.setProperty('--secondary', appearance.secondaryColor)\n        }\n        if (appearance.accentColor) {\n          document.documentElement.style.setProperty('--accent', appearance.accentColor)\n        }\n\n        // تطبيق الخطوط\n        if (appearance.fontFamily) {\n          document.documentElement.style.setProperty('--font-family', appearance.fontFamily)\n          if (document.body) {\n            document.body.style.fontFamily = appearance.fontFamily\n          }\n        }\n        if (appearance.fontSize) {\n          document.documentElement.style.setProperty('--font-size', appearance.fontSize)\n        }\n\n        // تطبيق الثيم\n        if (appearance.theme) {\n          document.documentElement.className = appearance.theme === 'dark' ? 'dark' : ''\n        }\n\n        // تطبيق CSS مخصص\n        if (appearance.enableCustomCSS && appearance.customCSS) {\n          let customStyleElement = document.getElementById('custom-css')\n          if (!customStyleElement) {\n            customStyleElement = document.createElement('style')\n            customStyleElement.id = 'custom-css'\n            document.head.appendChild(customStyleElement)\n          }\n          customStyleElement.textContent = appearance.customCSS\n        }\n      }\n\n      // تطبيق إعدادات عامة\n      if (settingsData.general && typeof settingsData.general === 'object') {\n        const general = settingsData.general\n\n        // تحديث عنوان الصفحة\n        if (general.diwanName) {\n          document.title = general.diwanName\n        }\n\n        // تحديث الفافيكون\n        if (general.favicon) {\n          let faviconElement = document.querySelector('link[rel=\"icon\"]') as HTMLLinkElement\n          if (!faviconElement) {\n            faviconElement = document.createElement('link')\n            faviconElement.rel = 'icon'\n            document.head.appendChild(faviconElement)\n          }\n          faviconElement.href = general.favicon\n        }\n      }\n    } catch (error) {\n      console.error('خطأ في تطبيق الإعدادات:', error)\n    }\n  }\n\n  return (\n    <SettingsContext.Provider value={{ settings, updateSettings, loading }}>\n      {children}\n    </SettingsContext.Provider>\n  )\n}\n\nexport function useSettings() {\n  const context = useContext(SettingsContext)\n  if (context === undefined) {\n    throw new Error('useSettings must be used within a SettingsProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAUA,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAmC;AAEhE,SAAS,iBAAiB,EAAE,QAAQ,EAAiC;;IAC1E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;QACF;qCAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,YAAY;gBACZ,cAAc;YAChB,OAAO;gBACL,QAAQ,KAAK,CAAC,2BAA2B,SAAS,MAAM;YAC1D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,YAAY;QACZ,cAAc;IAChB;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,cAAc;YACjB;QACF;QAEA,IAAI;YAEF,uBAAuB;YACvB,IAAI,aAAa,UAAU,IAAI,OAAO,aAAa,UAAU,KAAK,UAAU;gBAC1E,MAAM,aAAa,aAAa,UAAU;gBAE1C,gBAAgB;gBAChB,IAAI,WAAW,YAAY,EAAE;oBAC3B,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,aAAa,WAAW,YAAY;gBACjF;gBACA,IAAI,WAAW,cAAc,EAAE;oBAC7B,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,eAAe,WAAW,cAAc;gBACrF;gBACA,IAAI,WAAW,WAAW,EAAE;oBAC1B,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,YAAY,WAAW,WAAW;gBAC/E;gBAEA,eAAe;gBACf,IAAI,WAAW,UAAU,EAAE;oBACzB,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,iBAAiB,WAAW,UAAU;oBACjF,IAAI,SAAS,IAAI,EAAE;wBACjB,SAAS,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,WAAW,UAAU;oBACxD;gBACF;gBACA,IAAI,WAAW,QAAQ,EAAE;oBACvB,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,eAAe,WAAW,QAAQ;gBAC/E;gBAEA,cAAc;gBACd,IAAI,WAAW,KAAK,EAAE;oBACpB,SAAS,eAAe,CAAC,SAAS,GAAG,WAAW,KAAK,KAAK,SAAS,SAAS;gBAC9E;gBAEA,iBAAiB;gBACjB,IAAI,WAAW,eAAe,IAAI,WAAW,SAAS,EAAE;oBACtD,IAAI,qBAAqB,SAAS,cAAc,CAAC;oBACjD,IAAI,CAAC,oBAAoB;wBACvB,qBAAqB,SAAS,aAAa,CAAC;wBAC5C,mBAAmB,EAAE,GAAG;wBACxB,SAAS,IAAI,CAAC,WAAW,CAAC;oBAC5B;oBACA,mBAAmB,WAAW,GAAG,WAAW,SAAS;gBACvD;YACF;YAEA,qBAAqB;YACrB,IAAI,aAAa,OAAO,IAAI,OAAO,aAAa,OAAO,KAAK,UAAU;gBACpE,MAAM,UAAU,aAAa,OAAO;gBAEpC,qBAAqB;gBACrB,IAAI,QAAQ,SAAS,EAAE;oBACrB,SAAS,KAAK,GAAG,QAAQ,SAAS;gBACpC;gBAEA,kBAAkB;gBAClB,IAAI,QAAQ,OAAO,EAAE;oBACnB,IAAI,iBAAiB,SAAS,aAAa,CAAC;oBAC5C,IAAI,CAAC,gBAAgB;wBACnB,iBAAiB,SAAS,aAAa,CAAC;wBACxC,eAAe,GAAG,GAAG;wBACrB,SAAS,IAAI,CAAC,WAAW,CAAC;oBAC5B;oBACA,eAAe,IAAI,GAAG,QAAQ,OAAO;gBACvC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,qBACE,6LAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;YAAU;YAAgB;QAAQ;kBAClE;;;;;;AAGP;GA/GgB;KAAA;AAiHT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\"\n\nimport { Toaster as Sonner } from \"sonner\"\n\ntype ToasterProps = React.ComponentProps<typeof Sonner>\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  return (\n    <Sonner\n      theme=\"light\"\n      className=\"toaster group\"\n      toastOptions={{\n        classNames: {\n          toast:\n            \"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg\",\n          description: \"group-[.toast]:text-muted-foreground\",\n          actionButton:\n            \"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground\",\n          cancelButton:\n            \"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground\",\n        },\n      }}\n      {...props}\n    />\n  )\n}\n\nexport { Toaster }\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAMA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;IACzC,qBACE,6LAAC,2IAAA,CAAA,UAAM;QACL,OAAM;QACN,WAAU;QACV,cAAc;YACZ,YAAY;gBACV,OACE;gBACF,aAAa;gBACb,cACE;gBACF,cACE;YACJ;QACF;QACC,GAAG,KAAK;;;;;;AAGf;KAnBM", "debugId": null}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/hooks/use-appearance-settings.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\ninterface AppearanceSettings {\n  theme: 'light' | 'dark' | 'system'\n  primaryColor: string\n  secondaryColor: string\n  accentColor: string\n  backgroundColor: string\n  textColor: string\n  fontFamily: string\n  fontSize: string\n  fontWeight: string\n  lineHeight: string\n  logo: string\n  favicon: string\n  brandName: string\n  brandColors: {\n    primary: string\n    secondary: string\n  }\n  sidebarStyle: 'default' | 'compact' | 'minimal'\n  headerStyle: 'default' | 'compact' | 'transparent'\n  cardStyle: 'default' | 'bordered' | 'shadow' | 'flat'\n  buttonStyle: 'default' | 'rounded' | 'square'\n  enableAnimations: boolean\n  enableTransitions: boolean\n  enableShadows: boolean\n  enableGradients: boolean\n  customCSS: string\n  enableCustomCSS: boolean\n}\n\nconst defaultSettings: AppearanceSettings = {\n  theme: 'light',\n  primaryColor: '#3b82f6',\n  secondaryColor: '#64748b',\n  accentColor: '#f59e0b',\n  backgroundColor: '#ffffff',\n  textColor: '#1f2937',\n  fontFamily: 'Cairo',\n  fontSize: '14px',\n  fontWeight: 'normal',\n  lineHeight: '1.5',\n  logo: '',\n  favicon: '',\n  brandName: 'ديوان آل أبو علوش',\n  brandColors: {\n    primary: '#3b82f6',\n    secondary: '#64748b'\n  },\n  sidebarStyle: 'default',\n  headerStyle: 'default',\n  cardStyle: 'default',\n  buttonStyle: 'default',\n  enableAnimations: true,\n  enableTransitions: true,\n  enableShadows: true,\n  enableGradients: false,\n  customCSS: '',\n  enableCustomCSS: false\n}\n\nexport function useAppearanceSettings() {\n  const [settings, setSettings] = useState<AppearanceSettings>(defaultSettings)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    loadSettings()\n  }, [])\n\n  const loadSettings = async () => {\n    try {\n      const response = await fetch('/api/settings')\n      if (response.ok) {\n        const data = await response.json()\n        if (data.appearance) {\n          setSettings({ ...defaultSettings, ...data.appearance })\n        }\n      }\n    } catch (error) {\n      console.error('خطأ في تحميل إعدادات المظهر:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return {\n    settings,\n    loading,\n    reload: loadSettings\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAkCA,MAAM,kBAAsC;IAC1C,OAAO;IACP,cAAc;IACd,gBAAgB;IAChB,aAAa;IACb,iBAAiB;IACjB,WAAW;IACX,YAAY;IACZ,UAAU;IACV,YAAY;IACZ,YAAY;IACZ,MAAM;IACN,SAAS;IACT,WAAW;IACX,aAAa;QACX,SAAS;QACT,WAAW;IACb;IACA,cAAc;IACd,aAAa;IACb,WAAW;IACX,aAAa;IACb,kBAAkB;IAClB,mBAAmB;IACnB,eAAe;IACf,iBAAiB;IACjB,WAAW;IACX,iBAAiB;AACnB;AAEO,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR;QACF;0CAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,UAAU,EAAE;oBACnB,YAAY;wBAAE,GAAG,eAAe;wBAAE,GAAG,KAAK,UAAU;oBAAC;gBACvD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,OAAO;QACL;QACA;QACA,QAAQ;IACV;AACF;GA7BgB", "debugId": null}}, {"offset": {"line": 296, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/layout/dynamic-head.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useAppearanceSettings } from '@/hooks/use-appearance-settings'\n\nexport default function DynamicHead() {\n  const { settings } = useAppearanceSettings()\n\n  useEffect(() => {\n    // تحديث الـ favicon\n    if (settings.favicon) {\n      const favicon = document.querySelector('link[rel=\"icon\"]') as HTMLLinkElement\n      if (favicon) {\n        favicon.href = settings.favicon\n      } else {\n        const newFavicon = document.createElement('link')\n        newFavicon.rel = 'icon'\n        newFavicon.href = settings.favicon\n        document.head.appendChild(newFavicon)\n      }\n    }\n\n    // تحديث عنوان الصفحة\n    if (settings.brandName) {\n      document.title = settings.brandName\n    }\n\n    // تطبيق CSS مخصص\n    if (settings.enableCustomCSS && settings.customCSS) {\n      const existingStyle = document.getElementById('custom-css')\n      if (existingStyle) {\n        existingStyle.remove()\n      }\n\n      const style = document.createElement('style')\n      style.id = 'custom-css'\n      style.textContent = settings.customCSS\n      document.head.appendChild(style)\n    }\n\n    // تطبيق متغيرات CSS للألوان\n    const root = document.documentElement\n    if (settings.primaryColor) {\n      root.style.setProperty('--primary-color', settings.primaryColor)\n    }\n    if (settings.secondaryColor) {\n      root.style.setProperty('--secondary-color', settings.secondaryColor)\n    }\n    if (settings.accentColor) {\n      root.style.setProperty('--accent-color', settings.accentColor)\n    }\n    if (settings.backgroundColor) {\n      root.style.setProperty('--background-color', settings.backgroundColor)\n    }\n    if (settings.textColor) {\n      root.style.setProperty('--text-color', settings.textColor)\n    }\n\n    // تطبيق الخط\n    if (settings.fontFamily) {\n      root.style.setProperty('--font-family', settings.fontFamily)\n      document.body.style.fontFamily = settings.fontFamily\n    }\n    if (settings.fontSize) {\n      root.style.setProperty('--font-size', settings.fontSize)\n    }\n\n    // تطبيق أنماط الواجهة\n    document.body.className = `\n      ${settings.theme || 'light'}\n      ${settings.sidebarStyle ? `sidebar-${settings.sidebarStyle}` : ''}\n      ${settings.headerStyle ? `header-${settings.headerStyle}` : ''}\n      ${settings.cardStyle ? `cards-${settings.cardStyle}` : ''}\n      ${settings.buttonStyle ? `buttons-${settings.buttonStyle}` : ''}\n      ${settings.enableAnimations ? 'animations-enabled' : 'animations-disabled'}\n      ${settings.enableShadows ? 'shadows-enabled' : 'shadows-disabled'}\n      ${settings.enableGradients ? 'gradients-enabled' : 'gradients-disabled'}\n    `.trim().replace(/\\s+/g, ' ')\n\n  }, [settings])\n\n  return null\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,wBAAqB,AAAD;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,oBAAoB;YACpB,IAAI,SAAS,OAAO,EAAE;gBACpB,MAAM,UAAU,SAAS,aAAa,CAAC;gBACvC,IAAI,SAAS;oBACX,QAAQ,IAAI,GAAG,SAAS,OAAO;gBACjC,OAAO;oBACL,MAAM,aAAa,SAAS,aAAa,CAAC;oBAC1C,WAAW,GAAG,GAAG;oBACjB,WAAW,IAAI,GAAG,SAAS,OAAO;oBAClC,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC5B;YACF;YAEA,qBAAqB;YACrB,IAAI,SAAS,SAAS,EAAE;gBACtB,SAAS,KAAK,GAAG,SAAS,SAAS;YACrC;YAEA,iBAAiB;YACjB,IAAI,SAAS,eAAe,IAAI,SAAS,SAAS,EAAE;gBAClD,MAAM,gBAAgB,SAAS,cAAc,CAAC;gBAC9C,IAAI,eAAe;oBACjB,cAAc,MAAM;gBACtB;gBAEA,MAAM,QAAQ,SAAS,aAAa,CAAC;gBACrC,MAAM,EAAE,GAAG;gBACX,MAAM,WAAW,GAAG,SAAS,SAAS;gBACtC,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B;YAEA,4BAA4B;YAC5B,MAAM,OAAO,SAAS,eAAe;YACrC,IAAI,SAAS,YAAY,EAAE;gBACzB,KAAK,KAAK,CAAC,WAAW,CAAC,mBAAmB,SAAS,YAAY;YACjE;YACA,IAAI,SAAS,cAAc,EAAE;gBAC3B,KAAK,KAAK,CAAC,WAAW,CAAC,qBAAqB,SAAS,cAAc;YACrE;YACA,IAAI,SAAS,WAAW,EAAE;gBACxB,KAAK,KAAK,CAAC,WAAW,CAAC,kBAAkB,SAAS,WAAW;YAC/D;YACA,IAAI,SAAS,eAAe,EAAE;gBAC5B,KAAK,KAAK,CAAC,WAAW,CAAC,sBAAsB,SAAS,eAAe;YACvE;YACA,IAAI,SAAS,SAAS,EAAE;gBACtB,KAAK,KAAK,CAAC,WAAW,CAAC,gBAAgB,SAAS,SAAS;YAC3D;YAEA,aAAa;YACb,IAAI,SAAS,UAAU,EAAE;gBACvB,KAAK,KAAK,CAAC,WAAW,CAAC,iBAAiB,SAAS,UAAU;gBAC3D,SAAS,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,SAAS,UAAU;YACtD;YACA,IAAI,SAAS,QAAQ,EAAE;gBACrB,KAAK,KAAK,CAAC,WAAW,CAAC,eAAe,SAAS,QAAQ;YACzD;YAEA,sBAAsB;YACtB,SAAS,IAAI,CAAC,SAAS,GAAG,CAAC;MACzB,EAAE,SAAS,KAAK,IAAI,QAAQ;MAC5B,EAAE,SAAS,YAAY,GAAG,CAAC,QAAQ,EAAE,SAAS,YAAY,EAAE,GAAG,GAAG;MAClE,EAAE,SAAS,WAAW,GAAG,CAAC,OAAO,EAAE,SAAS,WAAW,EAAE,GAAG,GAAG;MAC/D,EAAE,SAAS,SAAS,GAAG,CAAC,MAAM,EAAE,SAAS,SAAS,EAAE,GAAG,GAAG;MAC1D,EAAE,SAAS,WAAW,GAAG,CAAC,QAAQ,EAAE,SAAS,WAAW,EAAE,GAAG,GAAG;MAChE,EAAE,SAAS,gBAAgB,GAAG,uBAAuB,sBAAsB;MAC3E,EAAE,SAAS,aAAa,GAAG,oBAAoB,mBAAmB;MAClE,EAAE,SAAS,eAAe,GAAG,sBAAsB,qBAAqB;IAC1E,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,QAAQ;QAE3B;gCAAG;QAAC;KAAS;IAEb,OAAO;AACT;GA7EwB;;QACD,gJAAA,CAAA,wBAAqB;;;KADpB", "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// تحويل الأرقام العربية إلى إنجليزية\nexport function convertArabicToEnglishNumbers(text: string): string {\n  const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩']\n  const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']\n\n  let result = text\n  for (let i = 0; i < arabicNumbers.length; i++) {\n    result = result.replace(new RegExp(arabicNumbers[i], 'g'), englishNumbers[i])\n  }\n  return result\n}\n\n// تنسيق الأرقام بالفواصل (بأرقام إنجليزية)\nexport function formatNumber(num: number): string {\n  return new Intl.NumberFormat('en-US').format(num)\n}\n\n// تنسيق العملة بالدينار الأردني (بأرقام إنجليزية)\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'JOD',\n    minimumFractionDigits: 2,\n  }).format(amount).replace('JOD', 'د.أ')\n}\n\n// تنسيق التاريخ بالعربية (بأرقام إنجليزية)\nexport function formatDate(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date\n\n  // التحقق من صحة التاريخ\n  if (isNaN(dateObj.getTime())) {\n    return 'تاريخ غير صحيح'\n  }\n\n  return new Intl.DateTimeFormat('en-GB', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n  }).format(dateObj)\n}\n\n// تنسيق التاريخ والوقت (بأرقام إنجليزية)\nexport function formatDateTime(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date\n\n  // التحقق من صحة التاريخ\n  if (isNaN(dateObj.getTime())) {\n    return 'تاريخ غير صحيح'\n  }\n\n  return new Intl.DateTimeFormat('en-GB', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: false,\n  }).format(dateObj)\n}\n\n// تحويل أدوار المستخدمين إلى نص عربي\nexport function getRoleText(role: string): string {\n  const roles: Record<string, string> = {\n    ADMIN: 'مدير',\n    DATA_ENTRY: 'مدخل بيانات',\n    VIEWER: 'مطلع',\n  }\n  return roles[role] || role\n}\n\n// تحويل أنواع الإيرادات إلى نص عربي\nexport function getIncomeTypeText(type: string): string {\n  const types: Record<string, string> = {\n    SUBSCRIPTION: 'اشتراكات',\n    DONATION: 'تبرعات',\n    EVENT: 'فعاليات',\n    OTHER: 'أخرى',\n  }\n  return types[type] || type\n}\n\n// تحويل فئات المصروفات إلى نص عربي\nexport function getExpenseCategoryText(category: string): string {\n  const categories: Record<string, string> = {\n    MEETINGS: 'اجتماعات',\n    EVENTS: 'مناسبات',\n    MAINTENANCE: 'إصلاحات',\n    SOCIAL: 'اجتماعية',\n    GENERAL: 'عامة',\n  }\n  return categories[category] || category\n}\n\n// تحويل حالات الأعضاء إلى نص عربي\nexport function getMemberStatusText(status: string): string {\n  const statuses: Record<string, string> = {\n    ACTIVE: 'نشط',\n    LATE: 'متأخر',\n    INACTIVE: 'غير ملتزم',\n    SUSPENDED: 'موقوف مؤقتاً',\n    ARCHIVED: 'مؤرشف',\n  }\n  return statuses[status] || status\n}\n\n// الحصول على لون حالة العضو\nexport function getMemberStatusColor(status: string): string {\n  const colors: Record<string, string> = {\n    ACTIVE: 'bg-green-100 text-green-800',\n    LATE: 'bg-yellow-100 text-yellow-800',\n    INACTIVE: 'bg-red-100 text-red-800',\n    SUSPENDED: 'bg-orange-100 text-orange-800',\n    ARCHIVED: 'bg-gray-100 text-gray-800',\n  }\n  return colors[status] || 'bg-gray-100 text-gray-800'\n}\n\n// الحصول على أيقونة حالة العضو\nexport function getMemberStatusIcon(status: string): string {\n  const icons: Record<string, string> = {\n    ACTIVE: '✅',\n    LATE: '⏰',\n    INACTIVE: '❌',\n    SUSPENDED: '⏸️',\n    ARCHIVED: '📁',\n  }\n  return icons[status] || '❓'\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,8BAA8B,IAAY;IACxD,MAAM,gBAAgB;QAAC;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;KAAI;IACxE,MAAM,iBAAiB;QAAC;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;KAAI;IAEzE,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAK;QAC7C,SAAS,OAAO,OAAO,CAAC,IAAI,OAAO,aAAa,CAAC,EAAE,EAAE,MAAM,cAAc,CAAC,EAAE;IAC9E;IACA,OAAO;AACT;AAGO,SAAS,aAAa,GAAW;IACtC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAGO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;IACzB,GAAG,MAAM,CAAC,QAAQ,OAAO,CAAC,OAAO;AACnC;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAE5D,wBAAwB;IACxB,IAAI,MAAM,QAAQ,OAAO,KAAK;QAC5B,OAAO;IACT;IAEA,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,eAAe,IAAmB;IAChD,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAE5D,wBAAwB;IACxB,IAAI,MAAM,QAAQ,OAAO,KAAK;QAC5B,OAAO;IACT;IAEA,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;QACR,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,YAAY,IAAY;IACtC,MAAM,QAAgC;QACpC,OAAO;QACP,YAAY;QACZ,QAAQ;IACV;IACA,OAAO,KAAK,CAAC,KAAK,IAAI;AACxB;AAGO,SAAS,kBAAkB,IAAY;IAC5C,MAAM,QAAgC;QACpC,cAAc;QACd,UAAU;QACV,OAAO;QACP,OAAO;IACT;IACA,OAAO,KAAK,CAAC,KAAK,IAAI;AACxB;AAGO,SAAS,uBAAuB,QAAgB;IACrD,MAAM,aAAqC;QACzC,UAAU;QACV,QAAQ;QACR,aAAa;QACb,QAAQ;QACR,SAAS;IACX;IACA,OAAO,UAAU,CAAC,SAAS,IAAI;AACjC;AAGO,SAAS,oBAAoB,MAAc;IAChD,MAAM,WAAmC;QACvC,QAAQ;QACR,MAAM;QACN,UAAU;QACV,WAAW;QACX,UAAU;IACZ;IACA,OAAO,QAAQ,CAAC,OAAO,IAAI;AAC7B;AAGO,SAAS,qBAAqB,MAAc;IACjD,MAAM,SAAiC;QACrC,QAAQ;QACR,MAAM;QACN,UAAU;QACV,WAAW;QACX,UAAU;IACZ;IACA,OAAO,MAAM,CAAC,OAAO,IAAI;AAC3B;AAGO,SAAS,oBAAoB,MAAc;IAChD,MAAM,QAAgC;QACpC,QAAQ;QACR,MAAM;QACN,UAAU;QACV,WAAW;QACX,UAAU;IACZ;IACA,OAAO,KAAK,CAAC,OAAO,IAAI;AAC1B", "debugId": null}}, {"offset": {"line": 551, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-semibold transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-95\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-primary-600 hover:to-primary-700\",\n        destructive:\n          \"bg-gradient-to-r from-danger-500 to-danger-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-danger-600 hover:to-danger-700\",\n        outline:\n          \"border-2 border-primary-300 bg-transparent text-primary-700 hover:bg-primary-50 hover:border-primary-400 hover:shadow-md\",\n        secondary:\n          \"bg-gradient-to-r from-secondary-100 to-secondary-200 text-secondary-800 shadow-md hover:shadow-lg hover:-translate-y-0.5 hover:from-secondary-200 hover:to-secondary-300\",\n        ghost: \"text-primary-600 hover:bg-primary-100 hover:text-primary-800\",\n        link: \"text-primary-600 underline-offset-4 hover:underline hover:text-primary-800\",\n        success: \"bg-gradient-to-r from-success-500 to-success-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-success-600 hover:to-success-700\",\n        warning: \"bg-gradient-to-r from-warning-500 to-warning-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-warning-600 hover:to-warning-700\",\n        info: \"bg-gradient-to-r from-info-500 to-info-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-info-600 hover:to-info-700\",\n        accent: \"bg-gradient-to-r from-gold-500 to-gold-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-gold-600 hover:to-gold-700\",\n      },\n      size: {\n        default: \"h-11 px-6 py-2.5\",\n        sm: \"h-9 rounded-lg px-4 text-xs\",\n        lg: \"h-13 rounded-xl px-8 text-base\",\n        icon: \"h-11 w-11\",\n        xs: \"h-8 rounded-md px-3 text-xs\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;YACT,MAAM;YACN,QAAQ;QACV;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;YACN,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IACvC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 620, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border border-secondary-200 bg-white text-secondary-700 shadow-lg hover:shadow-xl transition-all duration-200 hover:-translate-y-1 backdrop-blur-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6 bg-gradient-to-r from-secondary-50 to-secondary-100 rounded-t-xl border-b border-secondary-200\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-xl font-bold leading-none tracking-tight text-secondary-800\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-secondary-600 font-medium\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-4\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0 bg-gradient-to-r from-secondary-50 to-secondary-100 rounded-b-xl border-t border-secondary-200\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gIAAgI;QAC7I,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6HAA6H;QAC1I,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 723, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/error-boundary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Al<PERSON><PERSON>riangle, Refresh<PERSON>w, Home } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\n\ninterface ErrorBoundaryState {\n  hasError: boolean\n  error?: Error\n  errorInfo?: React.ErrorInfo\n}\n\ninterface ErrorBoundaryProps {\n  children: React.ReactNode\n  fallback?: React.ComponentType<{ error: Error; retry: () => void }>\n}\n\nclass ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props)\n    this.state = { hasError: false }\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    return {\n      hasError: true,\n      error\n    }\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('خطأ في التطبيق:', error, errorInfo)\n    \n    // تسجيل أخطاء JSON parsing بشكل خاص\n    if (error.message.includes('Unexpected token') && error.message.includes('<!DOCTYPE')) {\n      console.error('🚨 الخادم أرجع HTML بدلاً من JSON - تحقق من حالة الخادم')\n    }\n    \n    this.setState({\n      error,\n      errorInfo\n    })\n  }\n\n  retry = () => {\n    this.setState({ hasError: false, error: undefined, errorInfo: undefined })\n  }\n\n  render() {\n    if (this.state.hasError) {\n      if (this.props.fallback) {\n        const FallbackComponent = this.props.fallback\n        return <FallbackComponent error={this.state.error!} retry={this.retry} />\n      }\n\n      return <DefaultErrorFallback error={this.state.error!} retry={this.retry} />\n    }\n\n    return this.props.children\n  }\n}\n\nfunction DefaultErrorFallback({ error, retry }: { error: Error; retry: () => void }) {\n  const isJSONError = error.message.includes('Unexpected token') && error.message.includes('<!DOCTYPE')\n  \n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-orange-100 p-4\">\n      <Card className=\"w-full max-w-md shadow-xl\">\n        <CardHeader className=\"text-center space-y-4\">\n          <div className=\"mx-auto w-20 h-20 bg-gradient-to-br from-red-600 to-orange-600 rounded-full flex items-center justify-center shadow-lg\">\n            <AlertTriangle className=\"w-10 h-10 text-white\" />\n          </div>\n          <div>\n            <CardTitle className=\"text-2xl font-bold text-gray-900\">\n              حدث خطأ غير متوقع\n            </CardTitle>\n          </div>\n        </CardHeader>\n        \n        <CardContent className=\"space-y-4\">\n          {isJSONError ? (\n            <div className=\"bg-orange-50 border border-orange-200 text-orange-700 px-4 py-3 rounded-lg text-sm\">\n              <p className=\"font-semibold mb-2\">🚨 مشكلة في الاتصال بالخادم</p>\n              <p>الخادم أرجع صفحة HTML بدلاً من البيانات المطلوبة. قد يكون هناك:</p>\n              <ul className=\"list-disc list-inside mt-2 space-y-1\">\n                <li>مشكلة في الخادم</li>\n                <li>إعادة توجيه غير متوقعة</li>\n                <li>انقطاع في الاتصال</li>\n              </ul>\n            </div>\n          ) : (\n            <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm\">\n              <p className=\"font-semibold mb-2\">تفاصيل الخطأ:</p>\n              <p className=\"font-mono text-xs break-all\">{error.message}</p>\n            </div>\n          )}\n\n          <div className=\"space-y-3\">\n            <Button\n              onClick={retry}\n              className=\"w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium py-2.5\"\n            >\n              <RefreshCw className=\"w-4 h-4 ml-2\" />\n              إعادة المحاولة\n            </Button>\n\n            <Button\n              onClick={() => window.location.href = '/'}\n              variant=\"outline\"\n              className=\"w-full border-gray-300 text-gray-600 hover:bg-gray-50 font-medium py-2.5\"\n            >\n              <Home className=\"w-4 h-4 ml-2\" />\n              العودة للصفحة الرئيسية\n            </Button>\n          </div>\n\n          {process.env.NODE_ENV === 'development' && (\n            <details className=\"mt-4\">\n              <summary className=\"cursor-pointer text-sm text-gray-500 hover:text-gray-700\">\n                تفاصيل تقنية (للمطورين)\n              </summary>\n              <pre className=\"mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40\">\n                {error.stack}\n              </pre>\n            </details>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n\nexport default ErrorBoundary\n"], "names": [], "mappings": ";;;AAqHW;;AAnHX;AACA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AAkBA,MAAM,sBAAsB,6JAAA,CAAA,UAAK,CAAC,SAAS;IACzC,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAsB;QAChE,OAAO;YACL,UAAU;YACV;QACF;IACF;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,mBAAmB,OAAO;QAExC,oCAAoC;QACpC,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,uBAAuB,MAAM,OAAO,CAAC,QAAQ,CAAC,cAAc;YACrF,QAAQ,KAAK,CAAC;QAChB;QAEA,IAAI,CAAC,QAAQ,CAAC;YACZ;YACA;QACF;IACF;IAEA,QAAQ;QACN,IAAI,CAAC,QAAQ,CAAC;YAAE,UAAU;YAAO,OAAO;YAAW,WAAW;QAAU;IAC1E,EAAC;IAED,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,MAAM,oBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ;gBAC7C,qBAAO,6LAAC;oBAAkB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;oBAAG,OAAO,IAAI,CAAC,KAAK;;;;;;YACvE;YAEA,qBAAO,6LAAC;gBAAqB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;gBAAG,OAAO,IAAI,CAAC,KAAK;;;;;;QAC1E;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAEA,SAAS,qBAAqB,EAAE,KAAK,EAAE,KAAK,EAAuC;IACjF,MAAM,cAAc,MAAM,OAAO,CAAC,QAAQ,CAAC,uBAAuB,MAAM,OAAO,CAAC,QAAQ,CAAC;IAEzF,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;;;;;;sCAE3B,6LAAC;sCACC,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAmC;;;;;;;;;;;;;;;;;8BAM5D,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;wBACpB,4BACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,6LAAC;8CAAE;;;;;;8CACH,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;iDAIR,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,6LAAC;oCAAE,WAAU;8CAA+B,MAAM,OAAO;;;;;;;;;;;;sCAI7D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,WAAU;;sDAEV,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAIxC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;oCACtC,SAAQ;oCACR,WAAU;;sDAEV,6LAAC,sMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;wBAKpC,oDAAyB,+BACxB,6LAAC;4BAAQ,WAAU;;8CACjB,6LAAC;oCAAQ,WAAU;8CAA2D;;;;;;8CAG9E,6LAAC;oCAAI,WAAU;8CACZ,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5B;KApES;uCAsEM", "debugId": null}}]}