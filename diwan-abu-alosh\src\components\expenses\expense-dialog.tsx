'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { DollarSign, Receipt } from 'lucide-react'

// Schema محلي للنموذج (يختلف عن schema الخادم)
const expenseFormSchema = z.object({
  amount: z.number().positive('المبلغ يجب أن يكون أكبر من صفر'),
  date: z.string().min(1, 'التاريخ مطلوب'),
  description: z.string().min(1, 'الوصف مطلوب'),
  category: z.enum(['MEETINGS', 'EVENTS', 'MAINTENANCE', 'SOCIAL', 'GENERAL']).default('GENERAL'),
  recipient: z.string().optional(),
  notes: z.string().optional(),
})

type ExpenseFormInput = z.infer<typeof expenseFormSchema>

interface ExpenseDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
  expense?: {
    id: string
    amount: number
    date: string
    description: string
    category: string
    recipient?: string
    notes?: string
  } | null
}

export default function ExpenseDialog({
  open,
  onOpenChange,
  onSuccess,
  expense = null,
}: ExpenseDialogProps) {
  const [loading, setLoading] = useState(false)

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm<ExpenseFormInput>({
    resolver: zodResolver(expenseFormSchema),
    defaultValues: {
      amount: 0,
      date: new Date().toISOString().split('T')[0],
      description: '',
      category: 'GENERAL',
      recipient: '',
      notes: '',
    },
  })

  const selectedCategory = watch('category')

  useEffect(() => {
    if (open) {
      if (expense) {
        // تعديل مصروف موجود
        reset({
          amount: expense.amount,
          date: expense.date.split('T')[0],
          description: expense.description,
          category: expense.category as any,
          recipient: expense.recipient || '',
          notes: expense.notes || '',
        })
      } else {
        // إضافة مصروف جديد
        reset({
          amount: 0,
          date: new Date().toISOString().split('T')[0],
          description: '',
          category: 'GENERAL',
          recipient: '',
          notes: '',
        })
      }
    }
  }, [open, expense, reset])

  const onSubmit = async (data: ExpenseFormInput) => {
    try {
      setLoading(true)

      // تنظيف وتحويل البيانات
      const submitData = {
        amount: Number(data.amount),
        date: new Date(data.date),
        description: data.description.trim(),
        category: data.category,
        recipient: data.recipient?.trim() || null,
        notes: data.notes?.trim() || null,
      }

      const isEditing = !!expense
      const url = isEditing ? `/api/expenses/${expense.id}` : '/api/expenses'
      const method = isEditing ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'حدث خطأ')
      }

      alert(isEditing ? 'تم تحديث المصروف بنجاح!' : 'تم إضافة المصروف بنجاح!')
      onSuccess?.()
      onOpenChange(false)
    } catch (error: any) {
      console.error('خطأ في حفظ المصروف:', error)
      alert(error.message || 'حدث خطأ في حفظ المصروف')
    } finally {
      setLoading(false)
    }
  }

  // const getCategoryText = (category: string) => {
  //   switch (category) {
  //     case 'MEETINGS': return 'اجتماعات'
  //     case 'EVENTS': return 'مناسبات'
  //     case 'MAINTENANCE': return 'إصلاحات'
  //     case 'SOCIAL': return 'اجتماعية'
  //     case 'GENERAL': return 'عامة'
  //     default: return category
  //   }
  // }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[50vw] max-h-[90vh] overflow-y-auto">
        <DialogHeader className="pb-6 border-b border-gray-100">
          <DialogTitle className="text-2xl font-bold text-gray-900 flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-orange-500 rounded-xl flex items-center justify-center">
              <Receipt className="w-6 h-6 text-white" />
            </div>
            {expense ? 'تعديل المصروف' : 'إضافة مصروف جديد'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="p-8 space-y-8 bg-gray-50/30">
          {/* القسم الأول: المعلومات الأساسية */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <div className="w-2 h-2 bg-red-600 rounded-full"></div>
              المعلومات الأساسية
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <Label htmlFor="amount" className="text-sm font-medium text-gray-700 flex items-center gap-1">
                  المبلغ (دينار أردني)
                  <span className="text-red-500">*</span>
                </Label>
                <div className="relative">
                  <Input
                    id="amount"
                    type="number"
                    step="0.01"
                    min="0"
                    {...register('amount', { valueAsNumber: true })}
                    className={`h-12 pr-12 transition-all duration-200 ${
                      errors.amount
                        ? 'border-red-300 focus:border-red-500 focus:ring-red-200'
                        : 'border-gray-200 focus:border-red-500 focus:ring-red-100'
                    }`}
                    placeholder="0.00"
                  />
                  <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                    <DollarSign className="w-5 h-5" />
                  </div>
                </div>
                {errors.amount && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                    {errors.amount.message}
                  </p>
                )}
              </div>

              <div className="space-y-3">
                <Label htmlFor="date" className="text-sm font-medium text-gray-700 flex items-center gap-1">
                  التاريخ
                  <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="date"
                  type="date"
                  {...register('date')}
                  className={`h-12 transition-all duration-200 ${
                    errors.date
                      ? 'border-red-300 focus:border-red-500 focus:ring-red-200'
                      : 'border-gray-200 focus:border-red-500 focus:ring-red-100'
                  }`}
                />
                {errors.date && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                    {errors.date.message}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* القسم الثاني: تفاصيل المصروف */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <div className="w-2 h-2 bg-orange-600 rounded-full"></div>
              تفاصيل المصروف
            </h3>
            <div className="space-y-6">
              <div className="space-y-3">
                <Label htmlFor="description" className="text-sm font-medium text-gray-700 flex items-center gap-1">
                  الوصف
                  <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="description"
                  {...register('description')}
                  placeholder="مثال: شراء مستلزمات، دفع فواتير، تكاليف فعالية"
                  className={`h-12 transition-all duration-200 ${
                    errors.description
                      ? 'border-red-300 focus:border-red-500 focus:ring-red-200'
                      : 'border-gray-200 focus:border-red-500 focus:ring-red-100'
                  }`}
                />
                {errors.description && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                    {errors.description.message}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <Label className="text-sm font-medium text-gray-700">
                    الفئة
                  </Label>
                  <Select value={selectedCategory} onValueChange={(value) => setValue('category', value as any)}>
                    <SelectTrigger className="h-12 border-gray-200 focus:border-red-500 focus:ring-red-100">
                      <SelectValue placeholder="اختر الفئة" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="GENERAL">🏢 عامة</SelectItem>
                      <SelectItem value="MEETINGS">🤝 اجتماعات</SelectItem>
                      <SelectItem value="EVENTS">🎉 مناسبات</SelectItem>
                      <SelectItem value="MAINTENANCE">🔧 إصلاحات</SelectItem>
                      <SelectItem value="SOCIAL">👥 اجتماعية</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-3">
                  <Label htmlFor="recipient" className="text-sm font-medium text-gray-700">
                    الجهة المستفيدة
                  </Label>
                  <Input
                    id="recipient"
                    {...register('recipient')}
                    placeholder="مثال: شركة، مورد، مقاول"
                    className="h-12 border-gray-200 focus:border-red-500 focus:ring-red-100 transition-all duration-200"
                  />
                </div>
              </div>

              <div className="space-y-3">
                <Label htmlFor="notes" className="text-sm font-medium text-gray-700">
                  ملاحظات إضافية
                </Label>
                <Textarea
                  id="notes"
                  {...register('notes')}
                  placeholder="أي ملاحظات أو تفاصيل إضافية..."
                  className="min-h-[100px] border-gray-200 focus:border-red-500 focus:ring-red-100 transition-all duration-200 resize-none"
                />
              </div>
            </div>
          </div>

          {/* أزرار الحفظ والإلغاء */}
          <div className="flex justify-end gap-4 pt-6 border-t border-gray-100">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="h-12 px-8 border-2 border-gray-300 text-gray-700 hover:border-gray-400 hover:text-gray-800 transition-all duration-200 font-medium"
              disabled={loading}
            >
              إلغاء
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="h-12 px-8 bg-gradient-to-r from-red-600 via-red-700 to-orange-600 hover:from-red-700 hover:via-red-800 hover:to-orange-700 text-white font-bold transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-xl"
            >
              {loading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>جاري الحفظ...</span>
                </div>
              ) : (
                <span>{expense ? 'تحديث المصروف' : 'إضافة المصروف'}</span>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
