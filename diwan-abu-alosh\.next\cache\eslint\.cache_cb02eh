[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\activities\\route.ts": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\activities\\[id]\\route.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\admin\\user-permissions\\route.ts": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\admin\\users\\route.ts": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\admin\\users\\[id]\\route.ts": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\clear-session\\route.ts": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\member\\session\\route.ts": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\member\\signin\\route.ts": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\member\\signout\\route.ts": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\[...nextauth]\\route.ts": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\expenses\\route.ts": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\expenses\\[id]\\route.ts": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\export\\route.ts": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery\\route.ts": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery\\[id]\\route.ts": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery-folders\\route.ts": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery-folders\\[id]\\route.ts": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\incomes\\route.ts": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\incomes\\[id]\\route.ts": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\member\\account-statement\\route.ts": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\member\\auth\\route.ts": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\member\\gallery\\route.ts": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\member\\gallery\\[id]\\route.ts": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\route.ts": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\[id]\\account-statement\\route.ts": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\[id]\\password\\route.ts": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\[id]\\quick-stats\\route.ts": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\[id]\\route.ts": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\notifications\\route.ts": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\reports\\route.ts": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\settings\\reset\\route.ts": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\settings\\route.ts": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\upload\\route.ts": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\auth\\clear-session\\page.tsx": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\auth\\signin\\page.tsx": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\expenses\\page.tsx": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\gallery\\folder\\[id]\\page.tsx": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\gallery\\page.tsx": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\incomes\\page.tsx": "39", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\layout.tsx": "40", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\members\\page.tsx": "41", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\members-simple\\page.tsx": "42", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\notifications\\page.tsx": "43", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\page.tsx": "44", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\reports\\page.tsx": "45", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\reports-advanced\\page.tsx": "46", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\settings\\page.tsx": "47", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\layout.tsx": "48", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\account-statement\\page.tsx": "49", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\dashboard\\page.tsx": "50", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\gallery\\page.tsx": "51", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\layout.tsx": "52", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\login\\page.tsx": "53", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\signin\\page.tsx": "54", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\page.tsx": "55", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\user-type-selection.tsx": "56", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\auth\\user-type-selection.tsx": "57", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\debug\\session-monitor.tsx": "58", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\error-boundary.tsx": "59", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\expenses\\expense-dialog.tsx": "60", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\gallery\\create-activity-dialog.tsx": "61", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\gallery\\edit-activity-dialog.tsx": "62", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\gallery\\folder-view.tsx": "63", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\gallery\\upload-photo-dialog.tsx": "64", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\incomes\\income-dialog.tsx": "65", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\layout\\dynamic-head.tsx": "66", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\layout\\header.tsx": "67", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\layout\\sidebar.tsx": "68", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\member\\member-account-statement.tsx": "69", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\member\\member-header.tsx": "70", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\member\\member-layout.tsx": "71", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\member\\member-sidebar.tsx": "72", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\account-statement-dialog.tsx": "73", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\advanced-search.tsx": "74", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-details-dialog.tsx": "75", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-dialog.tsx": "76", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-income-dialog.tsx": "77", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-password-dialog.tsx": "78", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-quick-stats.tsx": "79", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-search-dialog.tsx": "80", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\providers\\member-auth-provider.tsx": "81", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\providers\\session-provider.tsx": "82", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\ComprehensiveReports.tsx": "83", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\ExpensesReports.tsx": "84", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\HelperReports.tsx": "85", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\IncomesReports.tsx": "86", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\MembersReports.tsx": "87", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\ReportExporter.tsx": "88", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\advanced-settings.tsx": "89", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\appearance-settings.tsx": "90", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\backup-settings.tsx": "91", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\detailed-permissions.tsx": "92", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\general-settings.tsx": "93", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\notification-settings.tsx": "94", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\security-settings.tsx": "95", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\settings-provider.tsx": "96", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\alert.tsx": "97", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\badge.tsx": "98", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\button.tsx": "99", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\card.tsx": "100", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\dialog.tsx": "101", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\dropdown-menu.tsx": "102", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\enhanced-select.tsx": "103", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\enhanced-table.tsx": "104", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\image-upload.tsx": "105", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\input.tsx": "106", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\label.tsx": "107", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\loading.tsx": "108", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\native-select.tsx": "109", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\page-header.tsx": "110", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\progress.tsx": "111", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\select.tsx": "112", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\sonner.tsx": "113", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\switch.tsx": "114", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\table.tsx": "115", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\tabs.tsx": "116", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\textarea.tsx": "117", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\toast.tsx": "118", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\hooks\\use-appearance-settings.ts": "119", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\hooks\\use-member-auth.ts": "120", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\hooks\\use-member-permissions.ts": "121", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\auth-middleware.ts": "122", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\auth.ts": "123", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\member-auth.ts": "124", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\permissions.ts": "125", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\prisma.ts": "126", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\utils.ts": "127", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\validations.ts": "128", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\middleware.ts": "129", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\types\\next-auth.d.ts": "130"}, {"size": 4334, "mtime": 1750563987665, "results": "131", "hashOfConfig": "132"}, {"size": 6245, "mtime": 1750564004020, "results": "133", "hashOfConfig": "132"}, {"size": 3872, "mtime": 1750563525595, "results": "134", "hashOfConfig": "132"}, {"size": 3820, "mtime": 1750514867537, "results": "135", "hashOfConfig": "132"}, {"size": 7577, "mtime": 1750564024945, "results": "136", "hashOfConfig": "132"}, {"size": 1576, "mtime": 1750564803239, "results": "137", "hashOfConfig": "132"}, {"size": 2587, "mtime": 1750564069542, "results": "138", "hashOfConfig": "132"}, {"size": 2609, "mtime": 1750525358269, "results": "139", "hashOfConfig": "132"}, {"size": 685, "mtime": 1750564816468, "results": "140", "hashOfConfig": "132"}, {"size": 157, "mtime": 1750364801281, "results": "141", "hashOfConfig": "132"}, {"size": 3860, "mtime": 1750564115048, "results": "142", "hashOfConfig": "132"}, {"size": 4514, "mtime": 1750564132785, "results": "143", "hashOfConfig": "132"}, {"size": 8392, "mtime": 1750564188777, "results": "144", "hashOfConfig": "132"}, {"size": 8404, "mtime": 1750564218120, "results": "145", "hashOfConfig": "132"}, {"size": 5066, "mtime": 1750454694226, "results": "146", "hashOfConfig": "132"}, {"size": 4504, "mtime": 1750564249572, "results": "147", "hashOfConfig": "132"}, {"size": 5788, "mtime": 1750564269167, "results": "148", "hashOfConfig": "132"}, {"size": 6556, "mtime": 1750564310657, "results": "149", "hashOfConfig": "132"}, {"size": 6751, "mtime": 1750564354109, "results": "150", "hashOfConfig": "132"}, {"size": 4983, "mtime": 1750564385844, "results": "151", "hashOfConfig": "132"}, {"size": 4942, "mtime": 1750564464120, "results": "152", "hashOfConfig": "132"}, {"size": 3219, "mtime": 1750564493457, "results": "153", "hashOfConfig": "132"}, {"size": 2931, "mtime": 1750564521366, "results": "154", "hashOfConfig": "132"}, {"size": 4198, "mtime": 1750564562824, "results": "155", "hashOfConfig": "132"}, {"size": 4784, "mtime": 1750564583315, "results": "156", "hashOfConfig": "132"}, {"size": 6404, "mtime": 1750564612850, "results": "157", "hashOfConfig": "132"}, {"size": 1695, "mtime": 1750367238374, "results": "158", "hashOfConfig": "132"}, {"size": 5670, "mtime": 1750564631728, "results": "159", "hashOfConfig": "132"}, {"size": 8050, "mtime": 1750461947709, "results": "160", "hashOfConfig": "132"}, {"size": 7559, "mtime": 1750463417752, "results": "161", "hashOfConfig": "132"}, {"size": 7741, "mtime": 1750564827281, "results": "162", "hashOfConfig": "132"}, {"size": 8942, "mtime": 1750498678247, "results": "163", "hashOfConfig": "132"}, {"size": 4637, "mtime": 1750564705418, "results": "164", "hashOfConfig": "132"}, {"size": 1927, "mtime": 1750528320562, "results": "165", "hashOfConfig": "132"}, {"size": 5200, "mtime": 1750564734440, "results": "166", "hashOfConfig": "132"}, {"size": 31201, "mtime": 1750564901443, "results": "167", "hashOfConfig": "132"}, {"size": 15872, "mtime": 1750564977971, "results": "168", "hashOfConfig": "132"}, {"size": 32403, "mtime": 1750565117046, "results": "169", "hashOfConfig": "132"}, {"size": 39385, "mtime": 1750565996169, "results": "170", "hashOfConfig": "132"}, {"size": 980, "mtime": 1750512522256, "results": "171", "hashOfConfig": "132"}, {"size": 43255, "mtime": 1750566534042, "results": "172", "hashOfConfig": "132"}, {"size": 2768, "mtime": 1750417848156, "results": "173", "hashOfConfig": "132"}, {"size": 32571, "mtime": 1750565469367, "results": "174", "hashOfConfig": "132"}, {"size": 15508, "mtime": 1750565493965, "results": "175", "hashOfConfig": "132"}, {"size": 55051, "mtime": 1750565515832, "results": "176", "hashOfConfig": "132"}, {"size": 12094, "mtime": 1750565553252, "results": "177", "hashOfConfig": "132"}, {"size": 11991, "mtime": 1750565865904, "results": "178", "hashOfConfig": "132"}, {"size": 1703, "mtime": 1750537782831, "results": "179", "hashOfConfig": "132"}, {"size": 12546, "mtime": 1750560430970, "results": "180", "hashOfConfig": "132"}, {"size": 9756, "mtime": 1750566052366, "results": "181", "hashOfConfig": "132"}, {"size": 13070, "mtime": 1750563720424, "results": "182", "hashOfConfig": "132"}, {"size": 756, "mtime": 1750535927466, "results": "183", "hashOfConfig": "132"}, {"size": 7829, "mtime": 1750529500439, "results": "184", "hashOfConfig": "132"}, {"size": 9524, "mtime": 1750566081994, "results": "185", "hashOfConfig": "132"}, {"size": 426, "mtime": 1750535176147, "results": "186", "hashOfConfig": "132"}, {"size": 6959, "mtime": 1750535212104, "results": "187", "hashOfConfig": "132"}, {"size": 6955, "mtime": 1750535019985, "results": "188", "hashOfConfig": "132"}, {"size": 3360, "mtime": 1750534552956, "results": "189", "hashOfConfig": "132"}, {"size": 4843, "mtime": 1750537753963, "results": "190", "hashOfConfig": "132"}, {"size": 13245, "mtime": 1750566812822, "results": "191", "hashOfConfig": "132"}, {"size": 14598, "mtime": 1750545633364, "results": "192", "hashOfConfig": "132"}, {"size": 7285, "mtime": 1750456802345, "results": "193", "hashOfConfig": "132"}, {"size": 11035, "mtime": 1750566565965, "results": "194", "hashOfConfig": "132"}, {"size": 21956, "mtime": 1750545615948, "results": "195", "hashOfConfig": "132"}, {"size": 26635, "mtime": 1750566832768, "results": "196", "hashOfConfig": "132"}, {"size": 2790, "mtime": 1750503410476, "results": "197", "hashOfConfig": "132"}, {"size": 3471, "mtime": 1750560409726, "results": "198", "hashOfConfig": "132"}, {"size": 4856, "mtime": 1750566576857, "results": "199", "hashOfConfig": "132"}, {"size": 13927, "mtime": 1750566609421, "results": "200", "hashOfConfig": "132"}, {"size": 3043, "mtime": 1750566640335, "results": "201", "hashOfConfig": "132"}, {"size": 2285, "mtime": 1750534574945, "results": "202", "hashOfConfig": "132"}, {"size": 4161, "mtime": 1750566670510, "results": "203", "hashOfConfig": "132"}, {"size": 30099, "mtime": 1750566704989, "results": "204", "hashOfConfig": "132"}, {"size": 6874, "mtime": 1750545784472, "results": "205", "hashOfConfig": "132"}, {"size": 9774, "mtime": 1750566736948, "results": "206", "hashOfConfig": "132"}, {"size": 28020, "mtime": 1750566791221, "results": "207", "hashOfConfig": "132"}, {"size": 8176, "mtime": 1750566857334, "results": "208", "hashOfConfig": "132"}, {"size": 8825, "mtime": 1750545725925, "results": "209", "hashOfConfig": "132"}, {"size": 2045, "mtime": 1750367044769, "results": "210", "hashOfConfig": "132"}, {"size": 7285, "mtime": 1750545652058, "results": "211", "hashOfConfig": "132"}, {"size": 452, "mtime": 1750566878814, "results": "212", "hashOfConfig": "132"}, {"size": 418, "mtime": 1750365176912, "results": "213", "hashOfConfig": "132"}, {"size": 20247, "mtime": 1750566909788, "results": "214", "hashOfConfig": "132"}, {"size": 19903, "mtime": 1750566929567, "results": "215", "hashOfConfig": "132"}, {"size": 25188, "mtime": 1750566980927, "results": "216", "hashOfConfig": "132"}, {"size": 19443, "mtime": 1750566999484, "results": "217", "hashOfConfig": "132"}, {"size": 19553, "mtime": 1750495698804, "results": "218", "hashOfConfig": "132"}, {"size": 23962, "mtime": 1750567018409, "results": "219", "hashOfConfig": "132"}, {"size": 12462, "mtime": 1750545804148, "results": "220", "hashOfConfig": "132"}, {"size": 34625, "mtime": 1750503640897, "results": "221", "hashOfConfig": "132"}, {"size": 27101, "mtime": 1750501162044, "results": "222", "hashOfConfig": "132"}, {"size": 16650, "mtime": 1750528626328, "results": "223", "hashOfConfig": "132"}, {"size": 17371, "mtime": 1750500017924, "results": "224", "hashOfConfig": "132"}, {"size": 22261, "mtime": 1750501420976, "results": "225", "hashOfConfig": "132"}, {"size": 57790, "mtime": 1750528807124, "results": "226", "hashOfConfig": "132"}, {"size": 4105, "mtime": 1750499697958, "results": "227", "hashOfConfig": "132"}, {"size": 1584, "mtime": 1750504904602, "results": "228", "hashOfConfig": "132"}, {"size": 1319, "mtime": 1750514106290, "results": "229", "hashOfConfig": "132"}, {"size": 2837, "mtime": 1750565680011, "results": "230", "hashOfConfig": "132"}, {"size": 2191, "mtime": 1750507570716, "results": "231", "hashOfConfig": "132"}, {"size": 4247, "mtime": 1750545333803, "results": "232", "hashOfConfig": "132"}, {"size": 7309, "mtime": 1750525874636, "results": "233", "hashOfConfig": "132"}, {"size": 8584, "mtime": 1750506901281, "results": "234", "hashOfConfig": "132"}, {"size": 6294, "mtime": 1750506495437, "results": "235", "hashOfConfig": "132"}, {"size": 7094, "mtime": 1750420043477, "results": "236", "hashOfConfig": "132"}, {"size": 953, "mtime": 1750565631424, "results": "237", "hashOfConfig": "132"}, {"size": 554, "mtime": 1750565653541, "results": "238", "hashOfConfig": "132"}, {"size": 3574, "mtime": 1750506436097, "results": "239", "hashOfConfig": "132"}, {"size": 6066, "mtime": 1750507694420, "results": "240", "hashOfConfig": "132"}, {"size": 7025, "mtime": 1750514092463, "results": "241", "hashOfConfig": "132"}, {"size": 791, "mtime": 1750498781188, "results": "242", "hashOfConfig": "132"}, {"size": 5988, "mtime": 1750507661593, "results": "243", "hashOfConfig": "132"}, {"size": 787, "mtime": 1750499406241, "results": "244", "hashOfConfig": "132"}, {"size": 1295, "mtime": 1750563835136, "results": "245", "hashOfConfig": "132"}, {"size": 2438, "mtime": 1750365583178, "results": "246", "hashOfConfig": "132"}, {"size": 1897, "mtime": 1750367770665, "results": "247", "hashOfConfig": "132"}, {"size": 824, "mtime": 1750565663708, "results": "248", "hashOfConfig": "132"}, {"size": 4088, "mtime": 1750506460673, "results": "249", "hashOfConfig": "132"}, {"size": 2216, "mtime": 1750502969456, "results": "250", "hashOfConfig": "132"}, {"size": 5917, "mtime": 1750560394465, "results": "251", "hashOfConfig": "132"}, {"size": 666, "mtime": 1750525448701, "results": "252", "hashOfConfig": "132"}, {"size": 8165, "mtime": 1750565785591, "results": "253", "hashOfConfig": "132"}, {"size": 1563, "mtime": 1750364783570, "results": "254", "hashOfConfig": "132"}, {"size": 3203, "mtime": 1750565756077, "results": "255", "hashOfConfig": "132"}, {"size": 8120, "mtime": 1750528852597, "results": "256", "hashOfConfig": "132"}, {"size": 279, "mtime": 1750364728882, "results": "257", "hashOfConfig": "132"}, {"size": 4208, "mtime": 1750495803718, "results": "258", "hashOfConfig": "132"}, {"size": 3218, "mtime": 1750528529700, "results": "259", "hashOfConfig": "132"}, {"size": 1217, "mtime": 1750565713689, "results": "260", "hashOfConfig": "132"}, {"size": 351, "mtime": 1750565693523, "results": "261", "hashOfConfig": "132"}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "kfs9f6", {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 43, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 15, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 24, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 15, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\activities\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\activities\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\admin\\user-permissions\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\admin\\users\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\admin\\users\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\clear-session\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\member\\session\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\member\\signin\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\member\\signout\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\expenses\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\expenses\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\export\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery-folders\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery-folders\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\incomes\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\incomes\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\member\\account-statement\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\member\\auth\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\member\\gallery\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\member\\gallery\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\[id]\\account-statement\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\[id]\\password\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\[id]\\quick-stats\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\notifications\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\reports\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\settings\\reset\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\settings\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\upload\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\auth\\clear-session\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\auth\\signin\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\expenses\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\gallery\\folder\\[id]\\page.tsx", ["652", "653", "654"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\gallery\\page.tsx", ["655", "656", "657"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\incomes\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\members\\page.tsx", ["658"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\members-simple\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\notifications\\page.tsx", ["659"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\reports\\page.tsx", ["660"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\reports-advanced\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\settings\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\account-statement\\page.tsx", ["661", "662", "663", "664"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\gallery\\page.tsx", ["665"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\signin\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\user-type-selection.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\auth\\user-type-selection.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\debug\\session-monitor.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\error-boundary.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\expenses\\expense-dialog.tsx", ["666", "667", "668"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\gallery\\create-activity-dialog.tsx", ["669"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\gallery\\edit-activity-dialog.tsx", ["670"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\gallery\\folder-view.tsx", ["671"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\gallery\\upload-photo-dialog.tsx", ["672", "673"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\incomes\\income-dialog.tsx", ["674", "675", "676"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\layout\\dynamic-head.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\layout\\header.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\layout\\sidebar.tsx", ["677"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\member\\member-account-statement.tsx", ["678"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\member\\member-header.tsx", ["679", "680"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\member\\member-layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\member\\member-sidebar.tsx", ["681", "682", "683"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\account-statement-dialog.tsx", ["684", "685"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\advanced-search.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-details-dialog.tsx", ["686", "687", "688"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-dialog.tsx", ["689", "690", "691", "692"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-income-dialog.tsx", ["693", "694"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-password-dialog.tsx", ["695"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-quick-stats.tsx", ["696"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-search-dialog.tsx", ["697"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\providers\\member-auth-provider.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\providers\\session-provider.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\ComprehensiveReports.tsx", ["698", "699", "700", "701"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\ExpensesReports.tsx", ["702", "703", "704"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\HelperReports.tsx", ["705"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\IncomesReports.tsx", ["706", "707", "708"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\MembersReports.tsx", ["709", "710"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\ReportExporter.tsx", ["711", "712", "713", "714", "715", "716", "717", "718", "719", "720", "721", "722", "723", "724", "725", "726", "727", "728", "729", "730", "731", "732", "733", "734", "735", "736", "737", "738", "739", "740", "741", "742", "743", "744", "745", "746", "747", "748", "749", "750", "751", "752", "753"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\advanced-settings.tsx", ["754", "755", "756", "757", "758", "759", "760"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\appearance-settings.tsx", ["761", "762", "763", "764", "765", "766", "767", "768", "769"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\backup-settings.tsx", ["770", "771", "772", "773", "774", "775", "776"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\detailed-permissions.tsx", ["777", "778", "779", "780"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\general-settings.tsx", ["781", "782", "783", "784", "785", "786", "787", "788", "789", "790", "791", "792", "793", "794", "795"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\notification-settings.tsx", ["796", "797", "798", "799", "800", "801", "802", "803", "804", "805", "806", "807", "808", "809", "810", "811", "812", "813", "814", "815", "816", "817", "818", "819"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\security-settings.tsx", ["820", "821", "822", "823", "824", "825", "826", "827", "828", "829", "830", "831", "832", "833", "834"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\settings-provider.tsx", ["835", "836", "837", "838", "839", "840"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\alert.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\enhanced-select.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\enhanced-table.tsx", ["841", "842"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\image-upload.tsx", ["843", "844"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\input.tsx", ["845"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\label.tsx", ["846"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\loading.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\native-select.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\page-header.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\sonner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\switch.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\table.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\textarea.tsx", ["847"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\toast.tsx", ["848"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\hooks\\use-appearance-settings.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\hooks\\use-member-auth.ts", ["849", "850", "851"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\hooks\\use-member-permissions.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\auth-middleware.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\member-auth.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\permissions.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\prisma.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\validations.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\middleware.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\types\\next-auth.d.ts", [], [], {"ruleId": "852", "severity": 1, "message": "853", "line": 181, "column": 6, "nodeType": "854", "endLine": 181, "endColumn": 45, "suggestions": "855"}, {"ruleId": "856", "severity": 1, "message": "857", "line": 349, "column": 21, "nodeType": "858", "endLine": 354, "endColumn": 23}, {"ruleId": "856", "severity": 1, "message": "857", "line": 422, "column": 17, "nodeType": "858", "endLine": 426, "endColumn": 19}, {"ruleId": "852", "severity": 1, "message": "859", "line": 141, "column": 6, "nodeType": "854", "endLine": 141, "endColumn": 51, "suggestions": "860"}, {"ruleId": "856", "severity": 1, "message": "857", "line": 539, "column": 25, "nodeType": "858", "endLine": 544, "endColumn": 27}, {"ruleId": "856", "severity": 1, "message": "857", "line": 695, "column": 17, "nodeType": "858", "endLine": 699, "endColumn": 19}, {"ruleId": "852", "severity": 1, "message": "861", "line": 153, "column": 6, "nodeType": "854", "endLine": 153, "endColumn": 39, "suggestions": "862"}, {"ruleId": "852", "severity": 1, "message": "863", "line": 78, "column": 6, "nodeType": "854", "endLine": 78, "endColumn": 49, "suggestions": "864"}, {"ruleId": "852", "severity": 1, "message": "865", "line": 96, "column": 6, "nodeType": "854", "endLine": 96, "endColumn": 31, "suggestions": "866"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 28, "column": 11, "nodeType": "869", "messageId": "870", "endLine": 28, "endColumn": 14, "suggestions": "871"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 36, "column": 20, "nodeType": "869", "messageId": "870", "endLine": 36, "endColumn": 23, "suggestions": "872"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 37, "column": 16, "nodeType": "869", "messageId": "870", "endLine": 37, "endColumn": 19, "suggestions": "873"}, {"ruleId": "852", "severity": 1, "message": "874", "line": 49, "column": 6, "nodeType": "854", "endLine": 49, "endColumn": 8, "suggestions": "875"}, {"ruleId": "852", "severity": 1, "message": "874", "line": 64, "column": 6, "nodeType": "854", "endLine": 64, "endColumn": 8, "suggestions": "876"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 90, "column": 41, "nodeType": "869", "messageId": "870", "endLine": 90, "endColumn": 44, "suggestions": "877"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 142, "column": 21, "nodeType": "869", "messageId": "870", "endLine": 142, "endColumn": 24, "suggestions": "878"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 272, "column": 108, "nodeType": "869", "messageId": "870", "endLine": 272, "endColumn": 111, "suggestions": "879"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 91, "column": 21, "nodeType": "869", "messageId": "870", "endLine": 91, "endColumn": 24, "suggestions": "880"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 99, "column": 21, "nodeType": "869", "messageId": "870", "endLine": 99, "endColumn": 24, "suggestions": "881"}, {"ruleId": "856", "severity": 1, "message": "857", "line": 129, "column": 17, "nodeType": "858", "endLine": 133, "endColumn": 19}, {"ruleId": "867", "severity": 2, "message": "868", "line": 227, "column": 21, "nodeType": "869", "messageId": "870", "endLine": 227, "endColumn": 24, "suggestions": "882"}, {"ruleId": "856", "severity": 1, "message": "857", "line": 297, "column": 21, "nodeType": "858", "endLine": 301, "endColumn": 23}, {"ruleId": "867", "severity": 2, "message": "868", "line": 121, "column": 32, "nodeType": "869", "messageId": "870", "endLine": 121, "endColumn": 35, "suggestions": "883"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 236, "column": 21, "nodeType": "869", "messageId": "870", "endLine": 236, "endColumn": 24, "suggestions": "884"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 378, "column": 73, "nodeType": "869", "messageId": "870", "endLine": 378, "endColumn": 76, "suggestions": "885"}, {"ruleId": "856", "severity": 1, "message": "857", "line": 43, "column": 15, "nodeType": "858", "endLine": 47, "endColumn": 17}, {"ruleId": "867", "severity": 2, "message": "868", "line": 102, "column": 21, "nodeType": "869", "messageId": "870", "endLine": 102, "endColumn": 24, "suggestions": "886"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 15, "column": 9, "nodeType": "869", "messageId": "870", "endLine": 15, "endColumn": 12, "suggestions": "887"}, {"ruleId": "856", "severity": 1, "message": "857", "line": 57, "column": 19, "nodeType": "858", "endLine": 61, "endColumn": 21}, {"ruleId": "867", "severity": 2, "message": "868", "line": 25, "column": 9, "nodeType": "869", "messageId": "870", "endLine": 25, "endColumn": 12, "suggestions": "888"}, {"ruleId": "856", "severity": 1, "message": "857", "line": 50, "column": 13, "nodeType": "858", "endLine": 54, "endColumn": 15}, {"ruleId": "867", "severity": 2, "message": "868", "line": 74, "column": 70, "nodeType": "869", "messageId": "870", "endLine": 74, "endColumn": 73, "suggestions": "889"}, {"ruleId": "852", "severity": 1, "message": "890", "line": 118, "column": 6, "nodeType": "854", "endLine": 118, "endColumn": 36, "suggestions": "891"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 141, "column": 55, "nodeType": "869", "messageId": "870", "endLine": 141, "endColumn": 58, "suggestions": "892"}, {"ruleId": "852", "severity": 1, "message": "893", "line": 74, "column": 6, "nodeType": "854", "endLine": 74, "endColumn": 20, "suggestions": "894"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 87, "column": 77, "nodeType": "869", "messageId": "870", "endLine": 87, "endColumn": 80, "suggestions": "895"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 92, "column": 56, "nodeType": "869", "messageId": "870", "endLine": 92, "endColumn": 59, "suggestions": "896"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 140, "column": 43, "nodeType": "869", "messageId": "870", "endLine": 140, "endColumn": 46, "suggestions": "897"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 192, "column": 21, "nodeType": "869", "messageId": "870", "endLine": 192, "endColumn": 24, "suggestions": "898"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 257, "column": 21, "nodeType": "869", "messageId": "870", "endLine": 257, "endColumn": 24, "suggestions": "899"}, {"ruleId": "856", "severity": 1, "message": "857", "line": 327, "column": 27, "nodeType": "858", "endLine": 331, "endColumn": 29}, {"ruleId": "867", "severity": 2, "message": "868", "line": 123, "column": 21, "nodeType": "869", "messageId": "870", "endLine": 123, "endColumn": 24, "suggestions": "900"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 211, "column": 67, "nodeType": "869", "messageId": "870", "endLine": 211, "endColumn": 70, "suggestions": "901"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 94, "column": 21, "nodeType": "869", "messageId": "870", "endLine": 94, "endColumn": 24, "suggestions": "902"}, {"ruleId": "852", "severity": 1, "message": "903", "line": 23, "column": 6, "nodeType": "854", "endLine": 23, "endColumn": 16, "suggestions": "904"}, {"ruleId": "852", "severity": 1, "message": "861", "line": 64, "column": 6, "nodeType": "854", "endLine": 64, "endColumn": 24, "suggestions": "905"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 32, "column": 23, "nodeType": "869", "messageId": "870", "endLine": 32, "endColumn": 26, "suggestions": "906"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 33, "column": 23, "nodeType": "869", "messageId": "870", "endLine": 33, "endColumn": 26, "suggestions": "907"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 40, "column": 48, "nodeType": "869", "messageId": "870", "endLine": 40, "endColumn": 51, "suggestions": "908"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 177, "column": 74, "nodeType": "869", "messageId": "870", "endLine": 177, "endColumn": 77, "suggestions": "909"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 35, "column": 23, "nodeType": "869", "messageId": "870", "endLine": 35, "endColumn": 26, "suggestions": "910"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 36, "column": 23, "nodeType": "869", "messageId": "870", "endLine": 36, "endColumn": 26, "suggestions": "911"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 169, "column": 23, "nodeType": "869", "messageId": "870", "endLine": 169, "endColumn": 26, "suggestions": "912"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "913", "line": 141, "column": 5}, {"ruleId": "867", "severity": 2, "message": "868", "line": 39, "column": 23, "nodeType": "869", "messageId": "870", "endLine": 39, "endColumn": 26, "suggestions": "914"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 40, "column": 23, "nodeType": "869", "messageId": "870", "endLine": 40, "endColumn": 26, "suggestions": "915"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 175, "column": 23, "nodeType": "869", "messageId": "870", "endLine": 175, "endColumn": 26, "suggestions": "916"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 44, "column": 23, "nodeType": "869", "messageId": "870", "endLine": 44, "endColumn": 26, "suggestions": "917"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 45, "column": 23, "nodeType": "869", "messageId": "870", "endLine": 45, "endColumn": 26, "suggestions": "918"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 6, "column": 41, "nodeType": "869", "messageId": "870", "endLine": 6, "endColumn": 44, "suggestions": "919"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 8, "column": 63, "nodeType": "869", "messageId": "870", "endLine": 8, "endColumn": 66, "suggestions": "920"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 82, "column": 35, "nodeType": "869", "messageId": "870", "endLine": 82, "endColumn": 38, "suggestions": "921"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 284, "column": 40, "nodeType": "869", "messageId": "870", "endLine": 284, "endColumn": 43, "suggestions": "922"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 301, "column": 45, "nodeType": "869", "messageId": "870", "endLine": 301, "endColumn": 48, "suggestions": "923"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 319, "column": 41, "nodeType": "869", "messageId": "870", "endLine": 319, "endColumn": 44, "suggestions": "924"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 355, "column": 57, "nodeType": "869", "messageId": "870", "endLine": 355, "endColumn": 60, "suggestions": "925"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 371, "column": 39, "nodeType": "869", "messageId": "870", "endLine": 371, "endColumn": 42, "suggestions": "926"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 373, "column": 42, "nodeType": "869", "messageId": "870", "endLine": 373, "endColumn": 45, "suggestions": "927"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 386, "column": 40, "nodeType": "869", "messageId": "870", "endLine": 386, "endColumn": 43, "suggestions": "928"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 396, "column": 46, "nodeType": "869", "messageId": "870", "endLine": 396, "endColumn": 49, "suggestions": "929"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 408, "column": 44, "nodeType": "869", "messageId": "870", "endLine": 408, "endColumn": 47, "suggestions": "930"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 446, "column": 45, "nodeType": "869", "messageId": "870", "endLine": 446, "endColumn": 48, "suggestions": "931"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 463, "column": 45, "nodeType": "869", "messageId": "870", "endLine": 463, "endColumn": 48, "suggestions": "932"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 501, "column": 47, "nodeType": "869", "messageId": "870", "endLine": 501, "endColumn": 50, "suggestions": "933"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 518, "column": 40, "nodeType": "869", "messageId": "870", "endLine": 518, "endColumn": 43, "suggestions": "934"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 557, "column": 49, "nodeType": "869", "messageId": "870", "endLine": 557, "endColumn": 52, "suggestions": "935"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 576, "column": 43, "nodeType": "869", "messageId": "870", "endLine": 576, "endColumn": 46, "suggestions": "936"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 578, "column": 42, "nodeType": "869", "messageId": "870", "endLine": 578, "endColumn": 45, "suggestions": "937"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 598, "column": 44, "nodeType": "869", "messageId": "870", "endLine": 598, "endColumn": 47, "suggestions": "938"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 600, "column": 44, "nodeType": "869", "messageId": "870", "endLine": 600, "endColumn": 47, "suggestions": "939"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 620, "column": 39, "nodeType": "869", "messageId": "870", "endLine": 620, "endColumn": 42, "suggestions": "940"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 622, "column": 46, "nodeType": "869", "messageId": "870", "endLine": 622, "endColumn": 49, "suggestions": "941"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 644, "column": 44, "nodeType": "869", "messageId": "870", "endLine": 644, "endColumn": 47, "suggestions": "942"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 648, "column": 43, "nodeType": "869", "messageId": "870", "endLine": 648, "endColumn": 46, "suggestions": "943"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 652, "column": 43, "nodeType": "869", "messageId": "870", "endLine": 652, "endColumn": 46, "suggestions": "944"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 679, "column": 42, "nodeType": "869", "messageId": "870", "endLine": 679, "endColumn": 45, "suggestions": "945"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 691, "column": 42, "nodeType": "869", "messageId": "870", "endLine": 691, "endColumn": 45, "suggestions": "946"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 692, "column": 44, "nodeType": "869", "messageId": "870", "endLine": 692, "endColumn": 47, "suggestions": "947"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 693, "column": 44, "nodeType": "869", "messageId": "870", "endLine": 693, "endColumn": 47, "suggestions": "948"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 694, "column": 47, "nodeType": "869", "messageId": "870", "endLine": 694, "endColumn": 50, "suggestions": "949"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 695, "column": 48, "nodeType": "869", "messageId": "870", "endLine": 695, "endColumn": 51, "suggestions": "950"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 696, "column": 43, "nodeType": "869", "messageId": "870", "endLine": 696, "endColumn": 46, "suggestions": "951"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 697, "column": 45, "nodeType": "869", "messageId": "870", "endLine": 697, "endColumn": 48, "suggestions": "952"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 698, "column": 44, "nodeType": "869", "messageId": "870", "endLine": 698, "endColumn": 47, "suggestions": "953"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 700, "column": 41, "nodeType": "869", "messageId": "870", "endLine": 700, "endColumn": 44, "suggestions": "954"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 701, "column": 43, "nodeType": "869", "messageId": "870", "endLine": 701, "endColumn": 46, "suggestions": "955"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 702, "column": 43, "nodeType": "869", "messageId": "870", "endLine": 702, "endColumn": 46, "suggestions": "956"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 703, "column": 46, "nodeType": "869", "messageId": "870", "endLine": 703, "endColumn": 49, "suggestions": "957"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 704, "column": 47, "nodeType": "869", "messageId": "870", "endLine": 704, "endColumn": 50, "suggestions": "958"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 705, "column": 42, "nodeType": "869", "messageId": "870", "endLine": 705, "endColumn": 45, "suggestions": "959"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 706, "column": 44, "nodeType": "869", "messageId": "870", "endLine": 706, "endColumn": 47, "suggestions": "960"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 707, "column": 43, "nodeType": "869", "messageId": "870", "endLine": 707, "endColumn": 46, "suggestions": "961"}, {"ruleId": "962", "severity": 2, "message": "963", "line": 4, "column": 10, "nodeType": null, "messageId": "964", "endLine": 4, "endColumn": 14}, {"ruleId": "962", "severity": 2, "message": "965", "line": 4, "column": 16, "nodeType": null, "messageId": "964", "endLine": 4, "endColumn": 27}, {"ruleId": "962", "severity": 2, "message": "966", "line": 4, "column": 29, "nodeType": null, "messageId": "964", "endLine": 4, "endColumn": 39}, {"ruleId": "962", "severity": 2, "message": "967", "line": 4, "column": 41, "nodeType": null, "messageId": "964", "endLine": 4, "endColumn": 50}, {"ruleId": "962", "severity": 2, "message": "968", "line": 8, "column": 10, "nodeType": null, "messageId": "964", "endLine": 8, "endColumn": 18}, {"ruleId": "962", "severity": 2, "message": "969", "line": 25, "column": 3, "nodeType": null, "messageId": "964", "endLine": 25, "endColumn": 11}, {"ruleId": "962", "severity": 2, "message": "970", "line": 46, "column": 21, "nodeType": null, "messageId": "964", "endLine": 46, "endColumn": 33}, {"ruleId": "962", "severity": 2, "message": "971", "line": 10, "column": 10, "nodeType": null, "messageId": "964", "endLine": 10, "endColumn": 15}, {"ruleId": "867", "severity": 2, "message": "868", "line": 27, "column": 13, "nodeType": "869", "messageId": "870", "endLine": 27, "endColumn": 16, "suggestions": "972"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 28, "column": 24, "nodeType": "869", "messageId": "870", "endLine": 28, "endColumn": 27, "suggestions": "973"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 162, "column": 67, "nodeType": "869", "messageId": "870", "endLine": 162, "endColumn": 70, "suggestions": "974"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 175, "column": 51, "nodeType": "869", "messageId": "870", "endLine": 175, "endColumn": 54, "suggestions": "975"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 197, "column": 50, "nodeType": "869", "messageId": "870", "endLine": 197, "endColumn": 53, "suggestions": "976"}, {"ruleId": "977", "severity": 1, "message": "978", "line": 611, "column": 13, "nodeType": "858", "endLine": 611, "endColumn": 58}, {"ruleId": "856", "severity": 1, "message": "857", "line": 638, "column": 21, "nodeType": "858", "endLine": 642, "endColumn": 23}, {"ruleId": "856", "severity": 1, "message": "857", "line": 691, "column": 21, "nodeType": "858", "endLine": 695, "endColumn": 23}, {"ruleId": "867", "severity": 2, "message": "868", "line": 29, "column": 13, "nodeType": "869", "messageId": "870", "endLine": 29, "endColumn": 16, "suggestions": "979"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 30, "column": 24, "nodeType": "869", "messageId": "870", "endLine": 30, "endColumn": 27, "suggestions": "980"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 130, "column": 45, "nodeType": "869", "messageId": "870", "endLine": 130, "endColumn": 48, "suggestions": "981"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 150, "column": 69, "nodeType": "869", "messageId": "870", "endLine": 150, "endColumn": 72, "suggestions": "982"}, {"ruleId": "962", "severity": 2, "message": "983", "line": 187, "column": 14, "nodeType": null, "messageId": "964", "endLine": 187, "endColumn": 19}, {"ruleId": "962", "severity": 2, "message": "983", "line": 202, "column": 16, "nodeType": null, "messageId": "964", "endLine": 202, "endColumn": 21}, {"ruleId": "962", "severity": 2, "message": "983", "line": 217, "column": 14, "nodeType": null, "messageId": "964", "endLine": 217, "endColumn": 19}, {"ruleId": "962", "severity": 2, "message": "984", "line": 10, "column": 17, "nodeType": null, "messageId": "964", "endLine": 10, "endColumn": 20}, {"ruleId": "962", "severity": 2, "message": "985", "line": 10, "column": 22, "nodeType": null, "messageId": "964", "endLine": 10, "endColumn": 26}, {"ruleId": "962", "severity": 2, "message": "986", "line": 10, "column": 28, "nodeType": null, "messageId": "964", "endLine": 10, "endColumn": 34}, {"ruleId": "962", "severity": 2, "message": "987", "line": 10, "column": 44, "nodeType": null, "messageId": "964", "endLine": 10, "endColumn": 52}, {"ruleId": "962", "severity": 2, "message": "963", "line": 4, "column": 10, "nodeType": null, "messageId": "964", "endLine": 4, "endColumn": 14}, {"ruleId": "962", "severity": 2, "message": "965", "line": 4, "column": 16, "nodeType": null, "messageId": "964", "endLine": 4, "endColumn": 27}, {"ruleId": "962", "severity": 2, "message": "966", "line": 4, "column": 29, "nodeType": null, "messageId": "964", "endLine": 4, "endColumn": 39}, {"ruleId": "962", "severity": 2, "message": "967", "line": 4, "column": 41, "nodeType": null, "messageId": "964", "endLine": 4, "endColumn": 50}, {"ruleId": "962", "severity": 2, "message": "988", "line": 10, "column": 10, "nodeType": null, "messageId": "964", "endLine": 10, "endColumn": 16}, {"ruleId": "962", "severity": 2, "message": "971", "line": 11, "column": 10, "nodeType": null, "messageId": "964", "endLine": 11, "endColumn": 15}, {"ruleId": "962", "severity": 2, "message": "989", "line": 15, "column": 3, "nodeType": null, "messageId": "964", "endLine": 15, "endColumn": 13}, {"ruleId": "962", "severity": 2, "message": "990", "line": 16, "column": 3, "nodeType": null, "messageId": "964", "endLine": 16, "endColumn": 8}, {"ruleId": "962", "severity": 2, "message": "991", "line": 17, "column": 3, "nodeType": null, "messageId": "964", "endLine": 17, "endColumn": 12}, {"ruleId": "962", "severity": 2, "message": "992", "line": 18, "column": 3, "nodeType": null, "messageId": "964", "endLine": 18, "endColumn": 9}, {"ruleId": "962", "severity": 2, "message": "993", "line": 19, "column": 3, "nodeType": null, "messageId": "964", "endLine": 19, "endColumn": 8}, {"ruleId": "962", "severity": 2, "message": "994", "line": 20, "column": 3, "nodeType": null, "messageId": "964", "endLine": 20, "endColumn": 7}, {"ruleId": "867", "severity": 2, "message": "868", "line": 26, "column": 13, "nodeType": "869", "messageId": "870", "endLine": 26, "endColumn": 16, "suggestions": "995"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 27, "column": 24, "nodeType": "869", "messageId": "870", "endLine": 27, "endColumn": 27, "suggestions": "996"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 120, "column": 64, "nodeType": "869", "messageId": "870", "endLine": 120, "endColumn": 67, "suggestions": "997"}, {"ruleId": "962", "severity": 2, "message": "998", "line": 7, "column": 10, "nodeType": null, "messageId": "964", "endLine": 7, "endColumn": 16}, {"ruleId": "962", "severity": 2, "message": "999", "line": 7, "column": 18, "nodeType": null, "messageId": "964", "endLine": 7, "endColumn": 31}, {"ruleId": "962", "severity": 2, "message": "1000", "line": 7, "column": 33, "nodeType": null, "messageId": "964", "endLine": 7, "endColumn": 43}, {"ruleId": "962", "severity": 2, "message": "1001", "line": 7, "column": 45, "nodeType": null, "messageId": "964", "endLine": 7, "endColumn": 58}, {"ruleId": "962", "severity": 2, "message": "1002", "line": 7, "column": 60, "nodeType": null, "messageId": "964", "endLine": 7, "endColumn": 71}, {"ruleId": "962", "severity": 2, "message": "988", "line": 9, "column": 10, "nodeType": null, "messageId": "964", "endLine": 9, "endColumn": 16}, {"ruleId": "962", "severity": 2, "message": "971", "line": 10, "column": 10, "nodeType": null, "messageId": "964", "endLine": 10, "endColumn": 15}, {"ruleId": "962", "severity": 2, "message": "968", "line": 11, "column": 10, "nodeType": null, "messageId": "964", "endLine": 11, "endColumn": 18}, {"ruleId": "962", "severity": 2, "message": "994", "line": 14, "column": 3, "nodeType": null, "messageId": "964", "endLine": 14, "endColumn": 7}, {"ruleId": "962", "severity": 2, "message": "1003", "line": 15, "column": 3, "nodeType": null, "messageId": "964", "endLine": 15, "endColumn": 16}, {"ruleId": "962", "severity": 2, "message": "1004", "line": 16, "column": 3, "nodeType": null, "messageId": "964", "endLine": 16, "endColumn": 13}, {"ruleId": "962", "severity": 2, "message": "1005", "line": 17, "column": 3, "nodeType": null, "messageId": "964", "endLine": 17, "endColumn": 10}, {"ruleId": "962", "severity": 2, "message": "1006", "line": 18, "column": 3, "nodeType": null, "messageId": "964", "endLine": 18, "endColumn": 10}, {"ruleId": "962", "severity": 2, "message": "1007", "line": 23, "column": 3, "nodeType": null, "messageId": "964", "endLine": 23, "endColumn": 14}, {"ruleId": "962", "severity": 2, "message": "1008", "line": 24, "column": 3, "nodeType": null, "messageId": "964", "endLine": 24, "endColumn": 7}, {"ruleId": "867", "severity": 2, "message": "868", "line": 29, "column": 13, "nodeType": "869", "messageId": "870", "endLine": 29, "endColumn": 16, "suggestions": "1009"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 30, "column": 24, "nodeType": "869", "messageId": "870", "endLine": 30, "endColumn": 27, "suggestions": "1010"}, {"ruleId": "962", "severity": 2, "message": "1011", "line": 172, "column": 10, "nodeType": null, "messageId": "964", "endLine": 172, "endColumn": 22}, {"ruleId": "962", "severity": 2, "message": "1012", "line": 173, "column": 10, "nodeType": null, "messageId": "964", "endLine": 173, "endColumn": 20}, {"ruleId": "867", "severity": 2, "message": "868", "line": 181, "column": 45, "nodeType": "869", "messageId": "870", "endLine": 181, "endColumn": 48, "suggestions": "1013"}, {"ruleId": "962", "severity": 2, "message": "1014", "line": 201, "column": 9, "nodeType": null, "messageId": "964", "endLine": 201, "endColumn": 26}, {"ruleId": "962", "severity": 2, "message": "983", "line": 207, "column": 14, "nodeType": null, "messageId": "964", "endLine": 207, "endColumn": 19}, {"ruleId": "962", "severity": 2, "message": "1015", "line": 214, "column": 9, "nodeType": null, "messageId": "964", "endLine": 214, "endColumn": 24}, {"ruleId": "962", "severity": 2, "message": "983", "line": 220, "column": 14, "nodeType": null, "messageId": "964", "endLine": 220, "endColumn": 19}, {"ruleId": "962", "severity": 2, "message": "968", "line": 11, "column": 10, "nodeType": null, "messageId": "964", "endLine": 11, "endColumn": 18}, {"ruleId": "962", "severity": 2, "message": "1016", "line": 16, "column": 3, "nodeType": null, "messageId": "964", "endLine": 16, "endColumn": 6}, {"ruleId": "962", "severity": 2, "message": "984", "line": 17, "column": 3, "nodeType": null, "messageId": "964", "endLine": 17, "endColumn": 6}, {"ruleId": "962", "severity": 2, "message": "1017", "line": 18, "column": 3, "nodeType": null, "messageId": "964", "endLine": 18, "endColumn": 9}, {"ruleId": "962", "severity": 2, "message": "1018", "line": 21, "column": 3, "nodeType": null, "messageId": "964", "endLine": 21, "endColumn": 16}, {"ruleId": "962", "severity": 2, "message": "1008", "line": 23, "column": 3, "nodeType": null, "messageId": "964", "endLine": 23, "endColumn": 7}, {"ruleId": "867", "severity": 2, "message": "868", "line": 35, "column": 13, "nodeType": "869", "messageId": "870", "endLine": 35, "endColumn": 16, "suggestions": "1019"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 36, "column": 24, "nodeType": "869", "messageId": "870", "endLine": 36, "endColumn": 27, "suggestions": "1020"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 172, "column": 38, "nodeType": "869", "messageId": "870", "endLine": 172, "endColumn": 41, "suggestions": "1021"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 182, "column": 50, "nodeType": "869", "messageId": "870", "endLine": 182, "endColumn": 53, "suggestions": "1022"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 214, "column": 45, "nodeType": "869", "messageId": "870", "endLine": 214, "endColumn": 48, "suggestions": "1023"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 234, "column": 71, "nodeType": "869", "messageId": "870", "endLine": 234, "endColumn": 74, "suggestions": "1024"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 298, "column": 60, "nodeType": "869", "messageId": "870", "endLine": 298, "endColumn": 63, "suggestions": "1025"}, {"ruleId": "1026", "severity": 2, "message": "1027", "line": 569, "column": 28, "nodeType": "1028", "messageId": "1029", "suggestions": "1030"}, {"ruleId": "1026", "severity": 2, "message": "1027", "line": 569, "column": 35, "nodeType": "1028", "messageId": "1029", "suggestions": "1031"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 6, "column": 13, "nodeType": "869", "messageId": "870", "endLine": 6, "endColumn": 16, "suggestions": "1032"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 7, "column": 33, "nodeType": "869", "messageId": "870", "endLine": 7, "endColumn": 36, "suggestions": "1033"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 14, "column": 44, "nodeType": "869", "messageId": "870", "endLine": 14, "endColumn": 47, "suggestions": "1034"}, {"ruleId": "852", "severity": 1, "message": "1035", "line": 19, "column": 6, "nodeType": "854", "endLine": 19, "endColumn": 8, "suggestions": "1036"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 39, "column": 40, "nodeType": "869", "messageId": "870", "endLine": 39, "endColumn": 43, "suggestions": "1037"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 44, "column": 40, "nodeType": "869", "messageId": "870", "endLine": 44, "endColumn": 43, "suggestions": "1038"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 9, "column": 20, "nodeType": "869", "messageId": "870", "endLine": 9, "endColumn": 23, "suggestions": "1039"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 27, "column": 56, "nodeType": "869", "messageId": "870", "endLine": 27, "endColumn": 59, "suggestions": "1040"}, {"ruleId": "867", "severity": 2, "message": "868", "line": 69, "column": 21, "nodeType": "869", "messageId": "870", "endLine": 69, "endColumn": 24, "suggestions": "1041"}, {"ruleId": "856", "severity": 1, "message": "857", "line": 136, "column": 13, "nodeType": "858", "endLine": 140, "endColumn": 15}, {"ruleId": "1042", "severity": 2, "message": "1043", "line": 4, "column": 18, "nodeType": "1044", "messageId": "1045", "endLine": 4, "endColumn": 28, "suggestions": "1046"}, {"ruleId": "1042", "severity": 2, "message": "1043", "line": 4, "column": 18, "nodeType": "1044", "messageId": "1045", "endLine": 4, "endColumn": 28, "suggestions": "1047"}, {"ruleId": "1042", "severity": 2, "message": "1043", "line": 4, "column": 18, "nodeType": "1044", "messageId": "1045", "endLine": 4, "endColumn": 31, "suggestions": "1048"}, {"ruleId": "852", "severity": 1, "message": "1049", "line": 37, "column": 6, "nodeType": "854", "endLine": 37, "endColumn": 8, "suggestions": "1050"}, {"ruleId": "852", "severity": 1, "message": "1051", "line": 166, "column": 6, "nodeType": "854", "endLine": 166, "endColumn": 8, "suggestions": "1052"}, {"ruleId": "852", "severity": 1, "message": "1053", "line": 179, "column": 6, "nodeType": "854", "endLine": 179, "endColumn": 29, "suggestions": "1054"}, {"ruleId": "852", "severity": 1, "message": "1055", "line": 179, "column": 7, "nodeType": "1056", "endLine": 179, "endColumn": 28}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchFolderData'. Either include it or remove the dependency array.", "ArrayExpression", ["1057"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "React Hook useEffect has a missing dependency: 'fetchPhotos'. Either include it or remove the dependency array.", ["1058"], "React Hook useEffect has a missing dependency: 'fetchMembers'. Either include it or remove the dependency array.", ["1059"], "React Hook useEffect has a missing dependency: 'fetchNotifications'. Either include it or remove the dependency array.", ["1060"], "React Hook useEffect has a missing dependency: 'fetchReportsData'. Either include it or remove the dependency array.", ["1061"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["1062", "1063"], ["1064", "1065"], ["1066", "1067"], "React Hook useEffect has a missing dependency: 'checkAuthAndLoadData'. Either include it or remove the dependency array.", ["1068"], ["1069"], ["1070", "1071"], ["1072", "1073"], ["1074", "1075"], ["1076", "1077"], ["1078", "1079"], ["1080", "1081"], ["1082", "1083"], ["1084", "1085"], ["1086", "1087"], ["1088", "1089"], ["1090", "1091"], ["1092", "1093"], ["1094", "1095"], "React Hook useEffect has a missing dependency: 'fetchAccountStatement'. Either include it or remove the dependency array.", ["1096"], ["1097", "1098"], "React Hook useEffect has a missing dependency: 'fetchMemberDetails'. Either include it or remove the dependency array.", ["1099"], ["1100", "1101"], ["1102", "1103"], ["1104", "1105"], ["1106", "1107"], ["1108", "1109"], ["1110", "1111"], ["1112", "1113"], ["1114", "1115"], "React Hook useEffect has a missing dependency: 'fetchQuickStats'. Either include it or remove the dependency array.", ["1116"], ["1117"], ["1118", "1119"], ["1120", "1121"], ["1122", "1123"], ["1124", "1125"], ["1126", "1127"], ["1128", "1129"], ["1130", "1131"], "Parsing error: ',' expected.", ["1132", "1133"], ["1134", "1135"], ["1136", "1137"], ["1138", "1139"], ["1140", "1141"], ["1142", "1143"], ["1144", "1145"], ["1146", "1147"], ["1148", "1149"], ["1150", "1151"], ["1152", "1153"], ["1154", "1155"], ["1156", "1157"], ["1158", "1159"], ["1160", "1161"], ["1162", "1163"], ["1164", "1165"], ["1166", "1167"], ["1168", "1169"], ["1170", "1171"], ["1172", "1173"], ["1174", "1175"], ["1176", "1177"], ["1178", "1179"], ["1180", "1181"], ["1182", "1183"], ["1184", "1185"], ["1186", "1187"], ["1188", "1189"], ["1190", "1191"], ["1192", "1193"], ["1194", "1195"], ["1196", "1197"], ["1198", "1199"], ["1200", "1201"], ["1202", "1203"], ["1204", "1205"], ["1206", "1207"], ["1208", "1209"], ["1210", "1211"], ["1212", "1213"], ["1214", "1215"], ["1216", "1217"], ["1218", "1219"], ["1220", "1221"], ["1222", "1223"], ["1224", "1225"], ["1226", "1227"], "@typescript-eslint/no-unused-vars", "'Card' is defined but never used.", "unusedVar", "'CardContent' is defined but never used.", "'CardHeader' is defined but never used.", "'CardTitle' is defined but never used.", "'Textarea' is defined but never used.", "'Settings' is defined but never used.", "'setChangeLog' is assigned a value but never used.", "'Badge' is defined but never used.", ["1228", "1229"], ["1230", "1231"], ["1232", "1233"], ["1234", "1235"], ["1236", "1237"], "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", ["1238", "1239"], ["1240", "1241"], ["1242", "1243"], ["1244", "1245"], "'error' is defined but never used.", "'Eye' is defined but never used.", "'Edit' is defined but never used.", "'Trash2' is defined but never used.", "'FileText' is defined but never used.", "'Button' is defined but never used.", "'DollarSign' is defined but never used.", "'Clock' is defined but never used.", "'Languages' is defined but never used.", "'MapPin' is defined but never used.", "'Phone' is defined but never used.", "'Mail' is defined but never used.", ["1246", "1247"], ["1248", "1249"], ["1250", "1251"], "'Select' is defined but never used.", "'SelectContent' is defined but never used.", "'SelectItem' is defined but never used.", "'SelectTrigger' is defined but never used.", "'SelectValue' is defined but never used.", "'MessageSquare' is defined but never used.", "'Smartphone' is defined but never used.", "'Volume2' is defined but never used.", "'VolumeX' is defined but never used.", "'CheckCircle' is defined but never used.", "'Info' is defined but never used.", ["1252", "1253"], ["1254", "1255"], "'testingEmail' is assigned a value but never used.", "'testingSMS' is assigned a value but never used.", ["1256", "1257"], "'testEmailSettings' is assigned a value but never used.", "'testSMSSettings' is assigned a value but never used.", "'Key' is defined but never used.", "'EyeOff' is defined but never used.", "'AlertTriangle' is defined but never used.", ["1258", "1259"], ["1260", "1261"], ["1262", "1263"], ["1264", "1265"], ["1266", "1267"], ["1268", "1269"], ["1270", "1271"], "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["1272", "1273", "1274", "1275"], ["1276", "1277", "1278", "1279"], ["1280", "1281"], ["1282", "1283"], ["1284", "1285"], "React Hook useEffect has a missing dependency: 'loadSettings'. Either include it or remove the dependency array.", ["1286"], ["1287", "1288"], ["1289", "1290"], ["1291", "1292"], ["1293", "1294"], ["1295", "1296"], "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["1297"], ["1298"], ["1299"], "React Hook useCallback has a missing dependency: 'removeToast'. Either include it or remove the dependency array.", ["1300"], "React Hook useEffect has a missing dependency: 'checkSession'. Either include it or remove the dependency array.", ["1301"], "React Hook useEffect has missing dependencies: 'checkSession' and 'user'. Either include them or remove the dependency array.", ["1302"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "ConditionalExpression", {"desc": "1303", "fix": "1304"}, {"desc": "1305", "fix": "1306"}, {"desc": "1307", "fix": "1308"}, {"desc": "1309", "fix": "1310"}, {"desc": "1311", "fix": "1312"}, {"messageId": "1313", "fix": "1314", "desc": "1315"}, {"messageId": "1316", "fix": "1317", "desc": "1318"}, {"messageId": "1313", "fix": "1319", "desc": "1315"}, {"messageId": "1316", "fix": "1320", "desc": "1318"}, {"messageId": "1313", "fix": "1321", "desc": "1315"}, {"messageId": "1316", "fix": "1322", "desc": "1318"}, {"desc": "1323", "fix": "1324"}, {"desc": "1323", "fix": "1325"}, {"messageId": "1313", "fix": "1326", "desc": "1315"}, {"messageId": "1316", "fix": "1327", "desc": "1318"}, {"messageId": "1313", "fix": "1328", "desc": "1315"}, {"messageId": "1316", "fix": "1329", "desc": "1318"}, {"messageId": "1313", "fix": "1330", "desc": "1315"}, {"messageId": "1316", "fix": "1331", "desc": "1318"}, {"messageId": "1313", "fix": "1332", "desc": "1315"}, {"messageId": "1316", "fix": "1333", "desc": "1318"}, {"messageId": "1313", "fix": "1334", "desc": "1315"}, {"messageId": "1316", "fix": "1335", "desc": "1318"}, {"messageId": "1313", "fix": "1336", "desc": "1315"}, {"messageId": "1316", "fix": "1337", "desc": "1318"}, {"messageId": "1313", "fix": "1338", "desc": "1315"}, {"messageId": "1316", "fix": "1339", "desc": "1318"}, {"messageId": "1313", "fix": "1340", "desc": "1315"}, {"messageId": "1316", "fix": "1341", "desc": "1318"}, {"messageId": "1313", "fix": "1342", "desc": "1315"}, {"messageId": "1316", "fix": "1343", "desc": "1318"}, {"messageId": "1313", "fix": "1344", "desc": "1315"}, {"messageId": "1316", "fix": "1345", "desc": "1318"}, {"messageId": "1313", "fix": "1346", "desc": "1315"}, {"messageId": "1316", "fix": "1347", "desc": "1318"}, {"messageId": "1313", "fix": "1348", "desc": "1315"}, {"messageId": "1316", "fix": "1349", "desc": "1318"}, {"messageId": "1313", "fix": "1350", "desc": "1315"}, {"messageId": "1316", "fix": "1351", "desc": "1318"}, {"desc": "1352", "fix": "1353"}, {"messageId": "1313", "fix": "1354", "desc": "1315"}, {"messageId": "1316", "fix": "1355", "desc": "1318"}, {"desc": "1356", "fix": "1357"}, {"messageId": "1313", "fix": "1358", "desc": "1315"}, {"messageId": "1316", "fix": "1359", "desc": "1318"}, {"messageId": "1313", "fix": "1360", "desc": "1315"}, {"messageId": "1316", "fix": "1361", "desc": "1318"}, {"messageId": "1313", "fix": "1362", "desc": "1315"}, {"messageId": "1316", "fix": "1363", "desc": "1318"}, {"messageId": "1313", "fix": "1364", "desc": "1315"}, {"messageId": "1316", "fix": "1365", "desc": "1318"}, {"messageId": "1313", "fix": "1366", "desc": "1315"}, {"messageId": "1316", "fix": "1367", "desc": "1318"}, {"messageId": "1313", "fix": "1368", "desc": "1315"}, {"messageId": "1316", "fix": "1369", "desc": "1318"}, {"messageId": "1313", "fix": "1370", "desc": "1315"}, {"messageId": "1316", "fix": "1371", "desc": "1318"}, {"messageId": "1313", "fix": "1372", "desc": "1315"}, {"messageId": "1316", "fix": "1373", "desc": "1318"}, {"desc": "1374", "fix": "1375"}, {"desc": "1376", "fix": "1377"}, {"messageId": "1313", "fix": "1378", "desc": "1315"}, {"messageId": "1316", "fix": "1379", "desc": "1318"}, {"messageId": "1313", "fix": "1380", "desc": "1315"}, {"messageId": "1316", "fix": "1381", "desc": "1318"}, {"messageId": "1313", "fix": "1382", "desc": "1315"}, {"messageId": "1316", "fix": "1383", "desc": "1318"}, {"messageId": "1313", "fix": "1384", "desc": "1315"}, {"messageId": "1316", "fix": "1385", "desc": "1318"}, {"messageId": "1313", "fix": "1386", "desc": "1315"}, {"messageId": "1316", "fix": "1387", "desc": "1318"}, {"messageId": "1313", "fix": "1388", "desc": "1315"}, {"messageId": "1316", "fix": "1389", "desc": "1318"}, {"messageId": "1313", "fix": "1390", "desc": "1315"}, {"messageId": "1316", "fix": "1391", "desc": "1318"}, {"messageId": "1313", "fix": "1392", "desc": "1315"}, {"messageId": "1316", "fix": "1393", "desc": "1318"}, {"messageId": "1313", "fix": "1394", "desc": "1315"}, {"messageId": "1316", "fix": "1395", "desc": "1318"}, {"messageId": "1313", "fix": "1396", "desc": "1315"}, {"messageId": "1316", "fix": "1397", "desc": "1318"}, {"messageId": "1313", "fix": "1398", "desc": "1315"}, {"messageId": "1316", "fix": "1399", "desc": "1318"}, {"messageId": "1313", "fix": "1400", "desc": "1315"}, {"messageId": "1316", "fix": "1401", "desc": "1318"}, {"messageId": "1313", "fix": "1402", "desc": "1315"}, {"messageId": "1316", "fix": "1403", "desc": "1318"}, {"messageId": "1313", "fix": "1404", "desc": "1315"}, {"messageId": "1316", "fix": "1405", "desc": "1318"}, {"messageId": "1313", "fix": "1406", "desc": "1315"}, {"messageId": "1316", "fix": "1407", "desc": "1318"}, {"messageId": "1313", "fix": "1408", "desc": "1315"}, {"messageId": "1316", "fix": "1409", "desc": "1318"}, {"messageId": "1313", "fix": "1410", "desc": "1315"}, {"messageId": "1316", "fix": "1411", "desc": "1318"}, {"messageId": "1313", "fix": "1412", "desc": "1315"}, {"messageId": "1316", "fix": "1413", "desc": "1318"}, {"messageId": "1313", "fix": "1414", "desc": "1315"}, {"messageId": "1316", "fix": "1415", "desc": "1318"}, {"messageId": "1313", "fix": "1416", "desc": "1315"}, {"messageId": "1316", "fix": "1417", "desc": "1318"}, {"messageId": "1313", "fix": "1418", "desc": "1315"}, {"messageId": "1316", "fix": "1419", "desc": "1318"}, {"messageId": "1313", "fix": "1420", "desc": "1315"}, {"messageId": "1316", "fix": "1421", "desc": "1318"}, {"messageId": "1313", "fix": "1422", "desc": "1315"}, {"messageId": "1316", "fix": "1423", "desc": "1318"}, {"messageId": "1313", "fix": "1424", "desc": "1315"}, {"messageId": "1316", "fix": "1425", "desc": "1318"}, {"messageId": "1313", "fix": "1426", "desc": "1315"}, {"messageId": "1316", "fix": "1427", "desc": "1318"}, {"messageId": "1313", "fix": "1428", "desc": "1315"}, {"messageId": "1316", "fix": "1429", "desc": "1318"}, {"messageId": "1313", "fix": "1430", "desc": "1315"}, {"messageId": "1316", "fix": "1431", "desc": "1318"}, {"messageId": "1313", "fix": "1432", "desc": "1315"}, {"messageId": "1316", "fix": "1433", "desc": "1318"}, {"messageId": "1313", "fix": "1434", "desc": "1315"}, {"messageId": "1316", "fix": "1435", "desc": "1318"}, {"messageId": "1313", "fix": "1436", "desc": "1315"}, {"messageId": "1316", "fix": "1437", "desc": "1318"}, {"messageId": "1313", "fix": "1438", "desc": "1315"}, {"messageId": "1316", "fix": "1439", "desc": "1318"}, {"messageId": "1313", "fix": "1440", "desc": "1315"}, {"messageId": "1316", "fix": "1441", "desc": "1318"}, {"messageId": "1313", "fix": "1442", "desc": "1315"}, {"messageId": "1316", "fix": "1443", "desc": "1318"}, {"messageId": "1313", "fix": "1444", "desc": "1315"}, {"messageId": "1316", "fix": "1445", "desc": "1318"}, {"messageId": "1313", "fix": "1446", "desc": "1315"}, {"messageId": "1316", "fix": "1447", "desc": "1318"}, {"messageId": "1313", "fix": "1448", "desc": "1315"}, {"messageId": "1316", "fix": "1449", "desc": "1318"}, {"messageId": "1313", "fix": "1450", "desc": "1315"}, {"messageId": "1316", "fix": "1451", "desc": "1318"}, {"messageId": "1313", "fix": "1452", "desc": "1315"}, {"messageId": "1316", "fix": "1453", "desc": "1318"}, {"messageId": "1313", "fix": "1454", "desc": "1315"}, {"messageId": "1316", "fix": "1455", "desc": "1318"}, {"messageId": "1313", "fix": "1456", "desc": "1315"}, {"messageId": "1316", "fix": "1457", "desc": "1318"}, {"messageId": "1313", "fix": "1458", "desc": "1315"}, {"messageId": "1316", "fix": "1459", "desc": "1318"}, {"messageId": "1313", "fix": "1460", "desc": "1315"}, {"messageId": "1316", "fix": "1461", "desc": "1318"}, {"messageId": "1313", "fix": "1462", "desc": "1315"}, {"messageId": "1316", "fix": "1463", "desc": "1318"}, {"messageId": "1313", "fix": "1464", "desc": "1315"}, {"messageId": "1316", "fix": "1465", "desc": "1318"}, {"messageId": "1313", "fix": "1466", "desc": "1315"}, {"messageId": "1316", "fix": "1467", "desc": "1318"}, {"messageId": "1313", "fix": "1468", "desc": "1315"}, {"messageId": "1316", "fix": "1469", "desc": "1318"}, {"messageId": "1313", "fix": "1470", "desc": "1315"}, {"messageId": "1316", "fix": "1471", "desc": "1318"}, {"messageId": "1313", "fix": "1472", "desc": "1315"}, {"messageId": "1316", "fix": "1473", "desc": "1318"}, {"messageId": "1313", "fix": "1474", "desc": "1315"}, {"messageId": "1316", "fix": "1475", "desc": "1318"}, {"messageId": "1313", "fix": "1476", "desc": "1315"}, {"messageId": "1316", "fix": "1477", "desc": "1318"}, {"messageId": "1313", "fix": "1478", "desc": "1315"}, {"messageId": "1316", "fix": "1479", "desc": "1318"}, {"messageId": "1313", "fix": "1480", "desc": "1315"}, {"messageId": "1316", "fix": "1481", "desc": "1318"}, {"messageId": "1313", "fix": "1482", "desc": "1315"}, {"messageId": "1316", "fix": "1483", "desc": "1318"}, {"messageId": "1313", "fix": "1484", "desc": "1315"}, {"messageId": "1316", "fix": "1485", "desc": "1318"}, {"messageId": "1313", "fix": "1486", "desc": "1315"}, {"messageId": "1316", "fix": "1487", "desc": "1318"}, {"messageId": "1313", "fix": "1488", "desc": "1315"}, {"messageId": "1316", "fix": "1489", "desc": "1318"}, {"messageId": "1313", "fix": "1490", "desc": "1315"}, {"messageId": "1316", "fix": "1491", "desc": "1318"}, {"messageId": "1313", "fix": "1492", "desc": "1315"}, {"messageId": "1316", "fix": "1493", "desc": "1318"}, {"messageId": "1313", "fix": "1494", "desc": "1315"}, {"messageId": "1316", "fix": "1495", "desc": "1318"}, {"messageId": "1313", "fix": "1496", "desc": "1315"}, {"messageId": "1316", "fix": "1497", "desc": "1318"}, {"messageId": "1313", "fix": "1498", "desc": "1315"}, {"messageId": "1316", "fix": "1499", "desc": "1318"}, {"messageId": "1313", "fix": "1500", "desc": "1315"}, {"messageId": "1316", "fix": "1501", "desc": "1318"}, {"messageId": "1313", "fix": "1502", "desc": "1315"}, {"messageId": "1316", "fix": "1503", "desc": "1318"}, {"messageId": "1313", "fix": "1504", "desc": "1315"}, {"messageId": "1316", "fix": "1505", "desc": "1318"}, {"messageId": "1313", "fix": "1506", "desc": "1315"}, {"messageId": "1316", "fix": "1507", "desc": "1318"}, {"messageId": "1313", "fix": "1508", "desc": "1315"}, {"messageId": "1316", "fix": "1509", "desc": "1318"}, {"messageId": "1313", "fix": "1510", "desc": "1315"}, {"messageId": "1316", "fix": "1511", "desc": "1318"}, {"messageId": "1313", "fix": "1512", "desc": "1315"}, {"messageId": "1316", "fix": "1513", "desc": "1318"}, {"messageId": "1313", "fix": "1514", "desc": "1315"}, {"messageId": "1316", "fix": "1515", "desc": "1318"}, {"messageId": "1313", "fix": "1516", "desc": "1315"}, {"messageId": "1316", "fix": "1517", "desc": "1318"}, {"messageId": "1313", "fix": "1518", "desc": "1315"}, {"messageId": "1316", "fix": "1519", "desc": "1318"}, {"messageId": "1313", "fix": "1520", "desc": "1315"}, {"messageId": "1316", "fix": "1521", "desc": "1318"}, {"messageId": "1313", "fix": "1522", "desc": "1315"}, {"messageId": "1316", "fix": "1523", "desc": "1318"}, {"messageId": "1313", "fix": "1524", "desc": "1315"}, {"messageId": "1316", "fix": "1525", "desc": "1318"}, {"messageId": "1313", "fix": "1526", "desc": "1315"}, {"messageId": "1316", "fix": "1527", "desc": "1318"}, {"messageId": "1313", "fix": "1528", "desc": "1315"}, {"messageId": "1316", "fix": "1529", "desc": "1318"}, {"messageId": "1313", "fix": "1530", "desc": "1315"}, {"messageId": "1316", "fix": "1531", "desc": "1318"}, {"messageId": "1532", "data": "1533", "fix": "1534", "desc": "1535"}, {"messageId": "1532", "data": "1536", "fix": "1537", "desc": "1538"}, {"messageId": "1532", "data": "1539", "fix": "1540", "desc": "1541"}, {"messageId": "1532", "data": "1542", "fix": "1543", "desc": "1544"}, {"messageId": "1532", "data": "1545", "fix": "1546", "desc": "1535"}, {"messageId": "1532", "data": "1547", "fix": "1548", "desc": "1538"}, {"messageId": "1532", "data": "1549", "fix": "1550", "desc": "1541"}, {"messageId": "1532", "data": "1551", "fix": "1552", "desc": "1544"}, {"messageId": "1313", "fix": "1553", "desc": "1315"}, {"messageId": "1316", "fix": "1554", "desc": "1318"}, {"messageId": "1313", "fix": "1555", "desc": "1315"}, {"messageId": "1316", "fix": "1556", "desc": "1318"}, {"messageId": "1313", "fix": "1557", "desc": "1315"}, {"messageId": "1316", "fix": "1558", "desc": "1318"}, {"desc": "1559", "fix": "1560"}, {"messageId": "1313", "fix": "1561", "desc": "1315"}, {"messageId": "1316", "fix": "1562", "desc": "1318"}, {"messageId": "1313", "fix": "1563", "desc": "1315"}, {"messageId": "1316", "fix": "1564", "desc": "1318"}, {"messageId": "1313", "fix": "1565", "desc": "1315"}, {"messageId": "1316", "fix": "1566", "desc": "1318"}, {"messageId": "1313", "fix": "1567", "desc": "1315"}, {"messageId": "1316", "fix": "1568", "desc": "1318"}, {"messageId": "1313", "fix": "1569", "desc": "1315"}, {"messageId": "1316", "fix": "1570", "desc": "1318"}, {"messageId": "1571", "fix": "1572", "desc": "1573"}, {"messageId": "1571", "fix": "1574", "desc": "1573"}, {"messageId": "1571", "fix": "1575", "desc": "1573"}, {"desc": "1576", "fix": "1577"}, {"desc": "1578", "fix": "1579"}, {"desc": "1580", "fix": "1581"}, "Update the dependencies array to be: [search, session, folderId, folderType, fetchFolderData]", {"range": "1582", "text": "1583"}, "Update the dependencies array to be: [fetchPhotos, search, selectedCategory, session, viewMode]", {"range": "1584", "text": "1585"}, "Update the dependencies array to be: [search, status, pagination.page, fetchMembers]", {"range": "1586", "text": "1587"}, "Update the dependencies array to be: [search, filter, selectedCategory, session, fetchNotifications]", {"range": "1588", "text": "1589"}, "Update the dependencies array to be: [fetchReportsData, selectedPeriod, session]", {"range": "1590", "text": "1591"}, "suggestUnknown", {"range": "1592", "text": "1593"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1594", "text": "1595"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "1596", "text": "1593"}, {"range": "1597", "text": "1595"}, {"range": "1598", "text": "1593"}, {"range": "1599", "text": "1595"}, "Update the dependencies array to be: [checkAuthAndLoadData]", {"range": "1600", "text": "1601"}, {"range": "1602", "text": "1601"}, {"range": "1603", "text": "1593"}, {"range": "1604", "text": "1595"}, {"range": "1605", "text": "1593"}, {"range": "1606", "text": "1595"}, {"range": "1607", "text": "1593"}, {"range": "1608", "text": "1595"}, {"range": "1609", "text": "1593"}, {"range": "1610", "text": "1595"}, {"range": "1611", "text": "1593"}, {"range": "1612", "text": "1595"}, {"range": "1613", "text": "1593"}, {"range": "1614", "text": "1595"}, {"range": "1615", "text": "1593"}, {"range": "1616", "text": "1595"}, {"range": "1617", "text": "1593"}, {"range": "1618", "text": "1595"}, {"range": "1619", "text": "1593"}, {"range": "1620", "text": "1595"}, {"range": "1621", "text": "1593"}, {"range": "1622", "text": "1595"}, {"range": "1623", "text": "1593"}, {"range": "1624", "text": "1595"}, {"range": "1625", "text": "1593"}, {"range": "1626", "text": "1595"}, {"range": "1627", "text": "1593"}, {"range": "1628", "text": "1595"}, "Update the dependencies array to be: [open, memberId, selectedYear, fetchAccountStatement]", {"range": "1629", "text": "1630"}, {"range": "1631", "text": "1593"}, {"range": "1632", "text": "1595"}, "Update the dependencies array to be: [open, member, fetchMemberDetails]", {"range": "1633", "text": "1634"}, {"range": "1635", "text": "1593"}, {"range": "1636", "text": "1595"}, {"range": "1637", "text": "1593"}, {"range": "1638", "text": "1595"}, {"range": "1639", "text": "1593"}, {"range": "1640", "text": "1595"}, {"range": "1641", "text": "1593"}, {"range": "1642", "text": "1595"}, {"range": "1643", "text": "1593"}, {"range": "1644", "text": "1595"}, {"range": "1645", "text": "1593"}, {"range": "1646", "text": "1595"}, {"range": "1647", "text": "1593"}, {"range": "1648", "text": "1595"}, {"range": "1649", "text": "1593"}, {"range": "1650", "text": "1595"}, "Update the dependencies array to be: [fetchQuickStats, memberId]", {"range": "1651", "text": "1652"}, "Update the dependencies array to be: [fetchMembers, open, searchTerm]", {"range": "1653", "text": "1654"}, {"range": "1655", "text": "1593"}, {"range": "1656", "text": "1595"}, {"range": "1657", "text": "1593"}, {"range": "1658", "text": "1595"}, {"range": "1659", "text": "1593"}, {"range": "1660", "text": "1595"}, {"range": "1661", "text": "1593"}, {"range": "1662", "text": "1595"}, {"range": "1663", "text": "1593"}, {"range": "1664", "text": "1595"}, {"range": "1665", "text": "1593"}, {"range": "1666", "text": "1595"}, {"range": "1667", "text": "1593"}, {"range": "1668", "text": "1595"}, {"range": "1669", "text": "1593"}, {"range": "1670", "text": "1595"}, {"range": "1671", "text": "1593"}, {"range": "1672", "text": "1595"}, {"range": "1673", "text": "1593"}, {"range": "1674", "text": "1595"}, {"range": "1675", "text": "1593"}, {"range": "1676", "text": "1595"}, {"range": "1677", "text": "1593"}, {"range": "1678", "text": "1595"}, {"range": "1679", "text": "1593"}, {"range": "1680", "text": "1595"}, {"range": "1681", "text": "1593"}, {"range": "1682", "text": "1595"}, {"range": "1683", "text": "1593"}, {"range": "1684", "text": "1595"}, {"range": "1685", "text": "1593"}, {"range": "1686", "text": "1595"}, {"range": "1687", "text": "1593"}, {"range": "1688", "text": "1595"}, {"range": "1689", "text": "1593"}, {"range": "1690", "text": "1595"}, {"range": "1691", "text": "1593"}, {"range": "1692", "text": "1595"}, {"range": "1693", "text": "1593"}, {"range": "1694", "text": "1595"}, {"range": "1695", "text": "1593"}, {"range": "1696", "text": "1595"}, {"range": "1697", "text": "1593"}, {"range": "1698", "text": "1595"}, {"range": "1699", "text": "1593"}, {"range": "1700", "text": "1595"}, {"range": "1701", "text": "1593"}, {"range": "1702", "text": "1595"}, {"range": "1703", "text": "1593"}, {"range": "1704", "text": "1595"}, {"range": "1705", "text": "1593"}, {"range": "1706", "text": "1595"}, {"range": "1707", "text": "1593"}, {"range": "1708", "text": "1595"}, {"range": "1709", "text": "1593"}, {"range": "1710", "text": "1595"}, {"range": "1711", "text": "1593"}, {"range": "1712", "text": "1595"}, {"range": "1713", "text": "1593"}, {"range": "1714", "text": "1595"}, {"range": "1715", "text": "1593"}, {"range": "1716", "text": "1595"}, {"range": "1717", "text": "1593"}, {"range": "1718", "text": "1595"}, {"range": "1719", "text": "1593"}, {"range": "1720", "text": "1595"}, {"range": "1721", "text": "1593"}, {"range": "1722", "text": "1595"}, {"range": "1723", "text": "1593"}, {"range": "1724", "text": "1595"}, {"range": "1725", "text": "1593"}, {"range": "1726", "text": "1595"}, {"range": "1727", "text": "1593"}, {"range": "1728", "text": "1595"}, {"range": "1729", "text": "1593"}, {"range": "1730", "text": "1595"}, {"range": "1731", "text": "1593"}, {"range": "1732", "text": "1595"}, {"range": "1733", "text": "1593"}, {"range": "1734", "text": "1595"}, {"range": "1735", "text": "1593"}, {"range": "1736", "text": "1595"}, {"range": "1737", "text": "1593"}, {"range": "1738", "text": "1595"}, {"range": "1739", "text": "1593"}, {"range": "1740", "text": "1595"}, {"range": "1741", "text": "1593"}, {"range": "1742", "text": "1595"}, {"range": "1743", "text": "1593"}, {"range": "1744", "text": "1595"}, {"range": "1745", "text": "1593"}, {"range": "1746", "text": "1595"}, {"range": "1747", "text": "1593"}, {"range": "1748", "text": "1595"}, {"range": "1749", "text": "1593"}, {"range": "1750", "text": "1595"}, {"range": "1751", "text": "1593"}, {"range": "1752", "text": "1595"}, {"range": "1753", "text": "1593"}, {"range": "1754", "text": "1595"}, {"range": "1755", "text": "1593"}, {"range": "1756", "text": "1595"}, {"range": "1757", "text": "1593"}, {"range": "1758", "text": "1595"}, {"range": "1759", "text": "1593"}, {"range": "1760", "text": "1595"}, {"range": "1761", "text": "1593"}, {"range": "1762", "text": "1595"}, {"range": "1763", "text": "1593"}, {"range": "1764", "text": "1595"}, {"range": "1765", "text": "1593"}, {"range": "1766", "text": "1595"}, {"range": "1767", "text": "1593"}, {"range": "1768", "text": "1595"}, {"range": "1769", "text": "1593"}, {"range": "1770", "text": "1595"}, {"range": "1771", "text": "1593"}, {"range": "1772", "text": "1595"}, {"range": "1773", "text": "1593"}, {"range": "1774", "text": "1595"}, {"range": "1775", "text": "1593"}, {"range": "1776", "text": "1595"}, {"range": "1777", "text": "1593"}, {"range": "1778", "text": "1595"}, {"range": "1779", "text": "1593"}, {"range": "1780", "text": "1595"}, {"range": "1781", "text": "1593"}, {"range": "1782", "text": "1595"}, {"range": "1783", "text": "1593"}, {"range": "1784", "text": "1595"}, {"range": "1785", "text": "1593"}, {"range": "1786", "text": "1595"}, {"range": "1787", "text": "1593"}, {"range": "1788", "text": "1595"}, {"range": "1789", "text": "1593"}, {"range": "1790", "text": "1595"}, {"range": "1791", "text": "1593"}, {"range": "1792", "text": "1595"}, {"range": "1793", "text": "1593"}, {"range": "1794", "text": "1595"}, {"range": "1795", "text": "1593"}, {"range": "1796", "text": "1595"}, {"range": "1797", "text": "1593"}, {"range": "1798", "text": "1595"}, {"range": "1799", "text": "1593"}, {"range": "1800", "text": "1595"}, {"range": "1801", "text": "1593"}, {"range": "1802", "text": "1595"}, {"range": "1803", "text": "1593"}, {"range": "1804", "text": "1595"}, {"range": "1805", "text": "1593"}, {"range": "1806", "text": "1595"}, {"range": "1807", "text": "1593"}, {"range": "1808", "text": "1595"}, "replaceWithAlt", {"alt": "1809"}, {"range": "1810", "text": "1811"}, "Replace with `&quot;`.", {"alt": "1812"}, {"range": "1813", "text": "1814"}, "Replace with `&ldquo;`.", {"alt": "1815"}, {"range": "1816", "text": "1817"}, "Replace with `&#34;`.", {"alt": "1818"}, {"range": "1819", "text": "1820"}, "Replace with `&rdquo;`.", {"alt": "1809"}, {"range": "1821", "text": "1822"}, {"alt": "1812"}, {"range": "1823", "text": "1824"}, {"alt": "1815"}, {"range": "1825", "text": "1826"}, {"alt": "1818"}, {"range": "1827", "text": "1828"}, {"range": "1829", "text": "1593"}, {"range": "1830", "text": "1595"}, {"range": "1831", "text": "1593"}, {"range": "1832", "text": "1595"}, {"range": "1833", "text": "1593"}, {"range": "1834", "text": "1595"}, "Update the dependencies array to be: [loadSettings]", {"range": "1835", "text": "1836"}, {"range": "1837", "text": "1593"}, {"range": "1838", "text": "1595"}, {"range": "1839", "text": "1593"}, {"range": "1840", "text": "1595"}, {"range": "1841", "text": "1593"}, {"range": "1842", "text": "1595"}, {"range": "1843", "text": "1593"}, {"range": "1844", "text": "1595"}, {"range": "1845", "text": "1593"}, {"range": "1846", "text": "1595"}, "replaceEmptyInterfaceWithSuper", {"range": "1847", "text": "1848"}, "Replace empty interface with a type alias.", {"range": "1849", "text": "1850"}, {"range": "1851", "text": "1852"}, "Update the dependencies array to be: [removeToast]", {"range": "1853", "text": "1854"}, "Update the dependencies array to be: [checkSession]", {"range": "1855", "text": "1856"}, "Update the dependencies array to be: [checkSession, user]", {"range": "1857", "text": "1858"}, [4962, 5001], "[search, session, folderId, folderType, fetchFolderData]", [3824, 3869], "[fetchPhotos, search, selectedCategory, session, viewMode]", [4674, 4707], "[search, status, pagination.page, fetchMembers]", [2209, 2252], "[search, filter, selectedCategory, session, fetchNotifications]", [2292, 2317], "[fetchReport<PERSON><PERSON><PERSON>, selected<PERSON><PERSON><PERSON>, session]", [535, 538], "unknown", [535, 538], "never", [746, 749], [746, 749], [767, 770], [767, 770], [1170, 1172], "[checkAuthAndLoadData]", [1439, 1441], [2241, 2244], [2241, 2244], [3658, 3661], [3658, 3661], [9507, 9510], [9507, 9510], [2350, 2353], [2350, 2353], [2699, 2702], [2699, 2702], [6137, 6140], [6137, 6140], [3093, 3096], [3093, 3096], [6207, 6210], [6207, 6210], [12690, 12693], [12690, 12693], [2149, 2152], [2149, 2152], [340, 343], [340, 343], [756, 759], [756, 759], [2603, 2606], [2603, 2606], [2510, 2540], "[open, memberId, selected<PERSON>ear, fetchAccountStatement]", [3282, 3285], [3282, 3285], [1373, 1387], "[open, member, fetchMemberDetails]", [1778, 1781], [1778, 1781], [1955, 1958], [1955, 1958], [3521, 3524], [3521, 3524], [4810, 4813], [4810, 4813], [6493, 6496], [6493, 6496], [2969, 2972], [2969, 2972], [6168, 6171], [6168, 6171], [2235, 2238], [2235, 2238], [578, 588], "[fetchQuickStats, memberId]", [1416, 1434], "[fetchMembers, open, searchTerm]", [849, 852], [849, 852], [898, 901], [898, 901], [1248, 1251], [1248, 1251], [5592, 5595], [5592, 5595], [864, 867], [864, 867], [913, 916], [913, 916], [5263, 5266], [5263, 5266], [875, 878], [875, 878], [924, 927], [924, 927], [5261, 5264], [5261, 5264], [961, 964], [961, 964], [1010, 1013], [1010, 1013], [125, 128], [125, 128], [225, 228], [225, 228], [2467, 2470], [2467, 2470], [7664, 7667], [7664, 7667], [8219, 8222], [8219, 8222], [8862, 8865], [8862, 8865], [10077, 10080], [10077, 10080], [10602, 10605], [10602, 10605], [10780, 10783], [10780, 10783], [11080, 11083], [11080, 11083], [11458, 11461], [11458, 11461], [11769, 11772], [11769, 11772], [13049, 13052], [13049, 13052], [13622, 13625], [13622, 13625], [14909, 14912], [14909, 14912], [15490, 15493], [15490, 15493], [16863, 16866], [16863, 16866], [17538, 17541], [17538, 17541], [17666, 17669], [17666, 17669], [18161, 18164], [18161, 18164], [18295, 18298], [18295, 18298], [18797, 18800], [18797, 18800], [18967, 18970], [18967, 18970], [19604, 19607], [19604, 19607], [19727, 19730], [19727, 19730], [19849, 19852], [19849, 19852], [20776, 20779], [20776, 20779], [21214, 21217], [21214, 21217], [21298, 21301], [21298, 21301], [21382, 21385], [21382, 21385], [21469, 21472], [21469, 21472], [21557, 21560], [21557, 21560], [21640, 21643], [21640, 21643], [21725, 21728], [21725, 21728], [21809, 21812], [21809, 21812], [21891, 21894], [21891, 21894], [21973, 21976], [21973, 21976], [22055, 22058], [22055, 22058], [22140, 22143], [22140, 22143], [22226, 22229], [22226, 22229], [22307, 22310], [22307, 22310], [22390, 22393], [22390, 22393], [22472, 22475], [22472, 22475], [722, 725], [722, 725], [749, 752], [749, 752], [4752, 4755], [4752, 4755], [5135, 5138], [5135, 5138], [5860, 5863], [5860, 5863], [759, 762], [759, 762], [786, 789], [786, 789], [3068, 3071], [3068, 3071], [3712, 3715], [3712, 3715], [712, 715], [712, 715], [739, 742], [739, 742], [3464, 3467], [3464, 3467], [761, 764], [761, 764], [788, 791], [788, 791], [4033, 4036], [4033, 4036], [844, 847], [844, 847], [871, 874], [871, 874], [3985, 3988], [3985, 3988], [4255, 4258], [4255, 4258], [5036, 5039], [5036, 5039], [5682, 5685], [5682, 5685], [7198, 7201], [7198, 7201], "&quot;", [17713, 17731], "مدة &quot;تذكرني\" (يوم)", "&ldquo;", [17713, 17731], "مدة &ldquo;تذكرني\" (يوم)", "&#34;", [17713, 17731], "مدة &#34;تذكرني\" (يوم)", "&rdquo;", [17713, 17731], "مدة &rdquo;تذكرني\" (يوم)", [17713, 17731], "مدة \"تذكرني&quot; (يوم)", [17713, 17731], "مدة \"تذكرني&ldquo; (يوم)", [17713, 17731], "مدة \"تذكرني&#34; (يوم)", [17713, 17731], "مدة \"تذكرني&rdquo; (يوم)", [130, 133], [130, 133], [166, 169], [166, 169], [407, 410], [407, 410], [508, 510], "[loadSettings]", [990, 993], [990, 993], [1104, 1107], [1104, 1107], [232, 235], [232, 235], [664, 667], [664, 667], [1671, 1674], [1671, 1674], [72, 199], "type InputProps = React.InputHTMLAttributes<HTMLInputElement>", [72, 199], "type LabelProps = React.LabelHTMLAttributes<HTMLLabelElement>", [72, 211], "type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement>", [1058, 1060], "[removeToast]", [4802, 4804], "[checkSession]", [5111, 5134], "[checkSession, user]"]