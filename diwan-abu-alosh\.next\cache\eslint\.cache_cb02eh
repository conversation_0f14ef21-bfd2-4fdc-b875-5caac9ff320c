[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\activities\\route.ts": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\activities\\[id]\\route.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\admin\\user-permissions\\route.ts": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\admin\\users\\route.ts": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\admin\\users\\[id]\\route.ts": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\clear-session\\route.ts": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\member\\session\\route.ts": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\member\\signin\\route.ts": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\member\\signout\\route.ts": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\[...nextauth]\\route.ts": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\expenses\\route.ts": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\expenses\\[id]\\route.ts": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\export\\route.ts": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery\\route.ts": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery\\[id]\\route.ts": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery-folders\\route.ts": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery-folders\\[id]\\route.ts": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\incomes\\route.ts": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\incomes\\[id]\\route.ts": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\member\\account-statement\\route.ts": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\member\\auth\\route.ts": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\member\\gallery\\route.ts": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\member\\gallery\\[id]\\route.ts": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\route.ts": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\[id]\\account-statement\\route.ts": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\[id]\\password\\route.ts": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\[id]\\quick-stats\\route.ts": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\[id]\\route.ts": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\notifications\\route.ts": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\reports\\route.ts": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\settings\\reset\\route.ts": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\settings\\route.ts": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\upload\\route.ts": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\auth\\clear-session\\page.tsx": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\auth\\signin\\page.tsx": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\expenses\\page.tsx": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\gallery\\folder\\[id]\\page.tsx": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\gallery\\page.tsx": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\incomes\\page.tsx": "39", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\layout.tsx": "40", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\members\\page.tsx": "41", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\members-simple\\page.tsx": "42", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\notifications\\page.tsx": "43", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\page.tsx": "44", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\reports\\page.tsx": "45", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\reports-advanced\\page.tsx": "46", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\settings\\page.tsx": "47", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\layout.tsx": "48", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\account-statement\\page.tsx": "49", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\dashboard\\page.tsx": "50", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\gallery\\page.tsx": "51", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\layout.tsx": "52", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\login\\page.tsx": "53", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\signin\\page.tsx": "54", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\page.tsx": "55", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\user-type-selection.tsx": "56", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\auth\\user-type-selection.tsx": "57", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\debug\\session-monitor.tsx": "58", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\error-boundary.tsx": "59", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\expenses\\expense-dialog.tsx": "60", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\gallery\\create-activity-dialog.tsx": "61", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\gallery\\edit-activity-dialog.tsx": "62", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\gallery\\folder-view.tsx": "63", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\gallery\\upload-photo-dialog.tsx": "64", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\incomes\\income-dialog.tsx": "65", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\layout\\dynamic-head.tsx": "66", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\layout\\header.tsx": "67", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\layout\\sidebar.tsx": "68", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\member\\member-account-statement.tsx": "69", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\member\\member-header.tsx": "70", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\member\\member-layout.tsx": "71", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\member\\member-sidebar.tsx": "72", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\account-statement-dialog.tsx": "73", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\advanced-search.tsx": "74", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-details-dialog.tsx": "75", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-dialog.tsx": "76", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-income-dialog.tsx": "77", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-password-dialog.tsx": "78", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-quick-stats.tsx": "79", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-search-dialog.tsx": "80", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\providers\\member-auth-provider.tsx": "81", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\providers\\session-provider.tsx": "82", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\ComprehensiveReports.tsx": "83", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\ExpensesReports.tsx": "84", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\HelperReports.tsx": "85", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\IncomesReports.tsx": "86", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\MembersReports.tsx": "87", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\ReportExporter.tsx": "88", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\advanced-settings.tsx": "89", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\appearance-settings.tsx": "90", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\backup-settings.tsx": "91", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\detailed-permissions.tsx": "92", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\general-settings.tsx": "93", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\notification-settings.tsx": "94", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\security-settings.tsx": "95", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\settings-provider.tsx": "96", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\alert.tsx": "97", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\badge.tsx": "98", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\button.tsx": "99", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\card.tsx": "100", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\dialog.tsx": "101", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\dropdown-menu.tsx": "102", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\enhanced-select.tsx": "103", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\enhanced-table.tsx": "104", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\image-upload.tsx": "105", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\input.tsx": "106", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\label.tsx": "107", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\loading.tsx": "108", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\native-select.tsx": "109", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\page-header.tsx": "110", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\progress.tsx": "111", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\select.tsx": "112", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\sonner.tsx": "113", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\switch.tsx": "114", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\table.tsx": "115", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\tabs.tsx": "116", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\textarea.tsx": "117", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\toast.tsx": "118", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\hooks\\use-appearance-settings.ts": "119", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\hooks\\use-member-auth.ts": "120", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\hooks\\use-member-permissions.ts": "121", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\auth-middleware.ts": "122", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\auth.ts": "123", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\member-auth.ts": "124", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\permissions.ts": "125", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\prisma.ts": "126", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\utils.ts": "127", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\validations.ts": "128", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\middleware.ts": "129", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\types\\next-auth.d.ts": "130"}, {"size": 4334, "mtime": 1750563987665, "results": "131", "hashOfConfig": "132"}, {"size": 6245, "mtime": 1750564004020, "results": "133", "hashOfConfig": "132"}, {"size": 3872, "mtime": 1750563525595, "results": "134", "hashOfConfig": "132"}, {"size": 3820, "mtime": 1750514867537, "results": "135", "hashOfConfig": "132"}, {"size": 7577, "mtime": 1750564024945, "results": "136", "hashOfConfig": "132"}, {"size": 1576, "mtime": 1750564803239, "results": "137", "hashOfConfig": "132"}, {"size": 2587, "mtime": 1750564069542, "results": "138", "hashOfConfig": "132"}, {"size": 2609, "mtime": 1750525358269, "results": "139", "hashOfConfig": "132"}, {"size": 685, "mtime": 1750564816468, "results": "140", "hashOfConfig": "132"}, {"size": 157, "mtime": 1750364801281, "results": "141", "hashOfConfig": "132"}, {"size": 3860, "mtime": 1750564115048, "results": "142", "hashOfConfig": "132"}, {"size": 4514, "mtime": 1750564132785, "results": "143", "hashOfConfig": "132"}, {"size": 8392, "mtime": 1750564188777, "results": "144", "hashOfConfig": "132"}, {"size": 8404, "mtime": 1750564218120, "results": "145", "hashOfConfig": "132"}, {"size": 5066, "mtime": 1750454694226, "results": "146", "hashOfConfig": "132"}, {"size": 4504, "mtime": 1750564249572, "results": "147", "hashOfConfig": "132"}, {"size": 5788, "mtime": 1750564269167, "results": "148", "hashOfConfig": "132"}, {"size": 6556, "mtime": 1750564310657, "results": "149", "hashOfConfig": "132"}, {"size": 6751, "mtime": 1750564354109, "results": "150", "hashOfConfig": "132"}, {"size": 4983, "mtime": 1750564385844, "results": "151", "hashOfConfig": "132"}, {"size": 4942, "mtime": 1750564464120, "results": "152", "hashOfConfig": "132"}, {"size": 3219, "mtime": 1750564493457, "results": "153", "hashOfConfig": "132"}, {"size": 2931, "mtime": 1750564521366, "results": "154", "hashOfConfig": "132"}, {"size": 4198, "mtime": 1750564562824, "results": "155", "hashOfConfig": "132"}, {"size": 4784, "mtime": 1750564583315, "results": "156", "hashOfConfig": "132"}, {"size": 6404, "mtime": 1750564612850, "results": "157", "hashOfConfig": "132"}, {"size": 1695, "mtime": 1750367238374, "results": "158", "hashOfConfig": "132"}, {"size": 5670, "mtime": 1750564631728, "results": "159", "hashOfConfig": "132"}, {"size": 8050, "mtime": 1750461947709, "results": "160", "hashOfConfig": "132"}, {"size": 7559, "mtime": 1750463417752, "results": "161", "hashOfConfig": "132"}, {"size": 7741, "mtime": 1750564827281, "results": "162", "hashOfConfig": "132"}, {"size": 8942, "mtime": 1750498678247, "results": "163", "hashOfConfig": "132"}, {"size": 4637, "mtime": 1750564705418, "results": "164", "hashOfConfig": "132"}, {"size": 1927, "mtime": 1750528320562, "results": "165", "hashOfConfig": "132"}, {"size": 5200, "mtime": 1750564734440, "results": "166", "hashOfConfig": "132"}, {"size": 31201, "mtime": 1750564901443, "results": "167", "hashOfConfig": "132"}, {"size": 15872, "mtime": 1750564977971, "results": "168", "hashOfConfig": "132"}, {"size": 32403, "mtime": 1750565117046, "results": "169", "hashOfConfig": "132"}, {"size": 39385, "mtime": 1750565996169, "results": "170", "hashOfConfig": "132"}, {"size": 980, "mtime": 1750512522256, "results": "171", "hashOfConfig": "132"}, {"size": 43255, "mtime": 1750566534042, "results": "172", "hashOfConfig": "132"}, {"size": 2768, "mtime": 1750417848156, "results": "173", "hashOfConfig": "132"}, {"size": 32571, "mtime": 1750565469367, "results": "174", "hashOfConfig": "132"}, {"size": 15508, "mtime": 1750565493965, "results": "175", "hashOfConfig": "132"}, {"size": 55051, "mtime": 1750565515832, "results": "176", "hashOfConfig": "132"}, {"size": 12094, "mtime": 1750565553252, "results": "177", "hashOfConfig": "132"}, {"size": 11991, "mtime": 1750565865904, "results": "178", "hashOfConfig": "132"}, {"size": 1703, "mtime": 1750537782831, "results": "179", "hashOfConfig": "132"}, {"size": 12546, "mtime": 1750560430970, "results": "180", "hashOfConfig": "132"}, {"size": 9756, "mtime": 1750566052366, "results": "181", "hashOfConfig": "132"}, {"size": 13070, "mtime": 1750563720424, "results": "182", "hashOfConfig": "132"}, {"size": 756, "mtime": 1750535927466, "results": "183", "hashOfConfig": "132"}, {"size": 7829, "mtime": 1750529500439, "results": "184", "hashOfConfig": "132"}, {"size": 9524, "mtime": 1750566081994, "results": "185", "hashOfConfig": "132"}, {"size": 426, "mtime": 1750535176147, "results": "186", "hashOfConfig": "132"}, {"size": 6959, "mtime": 1750535212104, "results": "187", "hashOfConfig": "132"}, {"size": 6955, "mtime": 1750535019985, "results": "188", "hashOfConfig": "132"}, {"size": 3360, "mtime": 1750534552956, "results": "189", "hashOfConfig": "132"}, {"size": 4843, "mtime": 1750537753963, "results": "190", "hashOfConfig": "132"}, {"size": 13245, "mtime": 1750566812822, "results": "191", "hashOfConfig": "132"}, {"size": 14598, "mtime": 1750545633364, "results": "192", "hashOfConfig": "132"}, {"size": 7285, "mtime": 1750456802345, "results": "193", "hashOfConfig": "132"}, {"size": 11035, "mtime": 1750566565965, "results": "194", "hashOfConfig": "132"}, {"size": 21956, "mtime": 1750545615948, "results": "195", "hashOfConfig": "132"}, {"size": 26635, "mtime": 1750566832768, "results": "196", "hashOfConfig": "132"}, {"size": 2790, "mtime": 1750503410476, "results": "197", "hashOfConfig": "132"}, {"size": 3471, "mtime": 1750560409726, "results": "198", "hashOfConfig": "132"}, {"size": 4856, "mtime": 1750566576857, "results": "199", "hashOfConfig": "132"}, {"size": 13927, "mtime": 1750566609421, "results": "200", "hashOfConfig": "132"}, {"size": 3043, "mtime": 1750566640335, "results": "201", "hashOfConfig": "132"}, {"size": 2285, "mtime": 1750534574945, "results": "202", "hashOfConfig": "132"}, {"size": 4161, "mtime": 1750566670510, "results": "203", "hashOfConfig": "132"}, {"size": 30099, "mtime": 1750566704989, "results": "204", "hashOfConfig": "132"}, {"size": 6874, "mtime": 1750545784472, "results": "205", "hashOfConfig": "132"}, {"size": 9774, "mtime": 1750566736948, "results": "206", "hashOfConfig": "132"}, {"size": 28020, "mtime": 1750566791221, "results": "207", "hashOfConfig": "132"}, {"size": 8176, "mtime": 1750566857334, "results": "208", "hashOfConfig": "132"}, {"size": 8825, "mtime": 1750545725925, "results": "209", "hashOfConfig": "132"}, {"size": 2045, "mtime": 1750367044769, "results": "210", "hashOfConfig": "132"}, {"size": 7285, "mtime": 1750545652058, "results": "211", "hashOfConfig": "132"}, {"size": 452, "mtime": 1750566878814, "results": "212", "hashOfConfig": "132"}, {"size": 418, "mtime": 1750365176912, "results": "213", "hashOfConfig": "132"}, {"size": 20247, "mtime": 1750566909788, "results": "214", "hashOfConfig": "132"}, {"size": 19903, "mtime": 1750566929567, "results": "215", "hashOfConfig": "132"}, {"size": 25182, "mtime": 1750567128095, "results": "216", "hashOfConfig": "132"}, {"size": 19443, "mtime": 1750566999484, "results": "217", "hashOfConfig": "132"}, {"size": 19553, "mtime": 1750495698804, "results": "218", "hashOfConfig": "132"}, {"size": 23962, "mtime": 1750567018409, "results": "219", "hashOfConfig": "132"}, {"size": 12457, "mtime": 1750567205065, "results": "220", "hashOfConfig": "132"}, {"size": 34628, "mtime": 1750567215893, "results": "221", "hashOfConfig": "132"}, {"size": 27077, "mtime": 1750567248734, "results": "222", "hashOfConfig": "132"}, {"size": 16621, "mtime": 1750567266554, "results": "223", "hashOfConfig": "132"}, {"size": 17317, "mtime": 1750567308399, "results": "224", "hashOfConfig": "132"}, {"size": 22251, "mtime": 1750567395180, "results": "225", "hashOfConfig": "132"}, {"size": 57744, "mtime": 1750567433012, "results": "226", "hashOfConfig": "132"}, {"size": 4105, "mtime": 1750499697958, "results": "227", "hashOfConfig": "132"}, {"size": 1584, "mtime": 1750504904602, "results": "228", "hashOfConfig": "132"}, {"size": 1319, "mtime": 1750514106290, "results": "229", "hashOfConfig": "132"}, {"size": 2837, "mtime": 1750565680011, "results": "230", "hashOfConfig": "132"}, {"size": 2191, "mtime": 1750507570716, "results": "231", "hashOfConfig": "132"}, {"size": 4247, "mtime": 1750545333803, "results": "232", "hashOfConfig": "132"}, {"size": 7309, "mtime": 1750525874636, "results": "233", "hashOfConfig": "132"}, {"size": 8584, "mtime": 1750506901281, "results": "234", "hashOfConfig": "132"}, {"size": 6294, "mtime": 1750506495437, "results": "235", "hashOfConfig": "132"}, {"size": 7094, "mtime": 1750420043477, "results": "236", "hashOfConfig": "132"}, {"size": 953, "mtime": 1750565631424, "results": "237", "hashOfConfig": "132"}, {"size": 554, "mtime": 1750565653541, "results": "238", "hashOfConfig": "132"}, {"size": 3574, "mtime": 1750506436097, "results": "239", "hashOfConfig": "132"}, {"size": 6066, "mtime": 1750507694420, "results": "240", "hashOfConfig": "132"}, {"size": 7025, "mtime": 1750514092463, "results": "241", "hashOfConfig": "132"}, {"size": 791, "mtime": 1750498781188, "results": "242", "hashOfConfig": "132"}, {"size": 5988, "mtime": 1750507661593, "results": "243", "hashOfConfig": "132"}, {"size": 787, "mtime": 1750499406241, "results": "244", "hashOfConfig": "132"}, {"size": 1295, "mtime": 1750563835136, "results": "245", "hashOfConfig": "132"}, {"size": 2438, "mtime": 1750365583178, "results": "246", "hashOfConfig": "132"}, {"size": 1897, "mtime": 1750367770665, "results": "247", "hashOfConfig": "132"}, {"size": 824, "mtime": 1750565663708, "results": "248", "hashOfConfig": "132"}, {"size": 4088, "mtime": 1750506460673, "results": "249", "hashOfConfig": "132"}, {"size": 2216, "mtime": 1750502969456, "results": "250", "hashOfConfig": "132"}, {"size": 5917, "mtime": 1750560394465, "results": "251", "hashOfConfig": "132"}, {"size": 666, "mtime": 1750525448701, "results": "252", "hashOfConfig": "132"}, {"size": 8165, "mtime": 1750565785591, "results": "253", "hashOfConfig": "132"}, {"size": 1563, "mtime": 1750364783570, "results": "254", "hashOfConfig": "132"}, {"size": 3203, "mtime": 1750565756077, "results": "255", "hashOfConfig": "132"}, {"size": 8120, "mtime": 1750528852597, "results": "256", "hashOfConfig": "132"}, {"size": 279, "mtime": 1750364728882, "results": "257", "hashOfConfig": "132"}, {"size": 4208, "mtime": 1750495803718, "results": "258", "hashOfConfig": "132"}, {"size": 3218, "mtime": 1750528529700, "results": "259", "hashOfConfig": "132"}, {"size": 1217, "mtime": 1750565713689, "results": "260", "hashOfConfig": "132"}, {"size": 351, "mtime": 1750565693523, "results": "261", "hashOfConfig": "132"}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "kfs9f6", {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 43, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\activities\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\activities\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\admin\\user-permissions\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\admin\\users\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\admin\\users\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\clear-session\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\member\\session\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\member\\signin\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\member\\signout\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\expenses\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\expenses\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\export\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery-folders\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery-folders\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\incomes\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\incomes\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\member\\account-statement\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\member\\auth\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\member\\gallery\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\member\\gallery\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\[id]\\account-statement\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\[id]\\password\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\[id]\\quick-stats\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\notifications\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\reports\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\settings\\reset\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\settings\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\upload\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\auth\\clear-session\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\auth\\signin\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\expenses\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\gallery\\folder\\[id]\\page.tsx", ["652", "653", "654"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\gallery\\page.tsx", ["655", "656", "657"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\incomes\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\members\\page.tsx", ["658"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\members-simple\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\notifications\\page.tsx", ["659"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\reports\\page.tsx", ["660"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\reports-advanced\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\settings\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\account-statement\\page.tsx", ["661", "662", "663", "664"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\gallery\\page.tsx", ["665"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\signin\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\user-type-selection.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\auth\\user-type-selection.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\debug\\session-monitor.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\error-boundary.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\expenses\\expense-dialog.tsx", ["666", "667", "668"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\gallery\\create-activity-dialog.tsx", ["669"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\gallery\\edit-activity-dialog.tsx", ["670"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\gallery\\folder-view.tsx", ["671"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\gallery\\upload-photo-dialog.tsx", ["672", "673"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\incomes\\income-dialog.tsx", ["674", "675", "676"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\layout\\dynamic-head.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\layout\\header.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\layout\\sidebar.tsx", ["677"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\member\\member-account-statement.tsx", ["678"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\member\\member-header.tsx", ["679", "680"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\member\\member-layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\member\\member-sidebar.tsx", ["681", "682", "683"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\account-statement-dialog.tsx", ["684", "685"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\advanced-search.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-details-dialog.tsx", ["686", "687", "688"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-dialog.tsx", ["689", "690", "691", "692"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-income-dialog.tsx", ["693", "694"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-password-dialog.tsx", ["695"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-quick-stats.tsx", ["696"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-search-dialog.tsx", ["697"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\providers\\member-auth-provider.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\providers\\session-provider.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\ComprehensiveReports.tsx", ["698", "699", "700", "701"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\ExpensesReports.tsx", ["702", "703", "704"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\HelperReports.tsx", ["705", "706", "707", "708"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\IncomesReports.tsx", ["709", "710", "711"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\MembersReports.tsx", ["712", "713"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\ReportExporter.tsx", ["714", "715", "716", "717", "718", "719", "720", "721", "722", "723", "724", "725", "726", "727", "728", "729", "730", "731", "732", "733", "734", "735", "736", "737", "738", "739", "740", "741", "742", "743", "744", "745", "746", "747", "748", "749", "750", "751", "752", "753", "754", "755", "756"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\advanced-settings.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\appearance-settings.tsx", ["757", "758", "759", "760", "761", "762", "763", "764"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\backup-settings.tsx", ["765", "766", "767", "768"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\detailed-permissions.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\general-settings.tsx", ["769", "770", "771"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\notification-settings.tsx", ["772", "773", "774"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\security-settings.tsx", ["775", "776", "777", "778", "779", "780", "781", "782", "783"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\settings-provider.tsx", ["784", "785", "786", "787", "788", "789"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\alert.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\enhanced-select.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\enhanced-table.tsx", ["790", "791"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\image-upload.tsx", ["792", "793"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\input.tsx", ["794"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\label.tsx", ["795"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\loading.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\native-select.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\page-header.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\sonner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\switch.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\table.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\textarea.tsx", ["796"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\toast.tsx", ["797"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\hooks\\use-appearance-settings.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\hooks\\use-member-auth.ts", ["798", "799", "800"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\hooks\\use-member-permissions.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\auth-middleware.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\member-auth.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\permissions.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\prisma.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\validations.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\middleware.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\types\\next-auth.d.ts", [], [], {"ruleId": "801", "severity": 1, "message": "802", "line": 181, "column": 6, "nodeType": "803", "endLine": 181, "endColumn": 45, "suggestions": "804"}, {"ruleId": "805", "severity": 1, "message": "806", "line": 349, "column": 21, "nodeType": "807", "endLine": 354, "endColumn": 23}, {"ruleId": "805", "severity": 1, "message": "806", "line": 422, "column": 17, "nodeType": "807", "endLine": 426, "endColumn": 19}, {"ruleId": "801", "severity": 1, "message": "808", "line": 141, "column": 6, "nodeType": "803", "endLine": 141, "endColumn": 51, "suggestions": "809"}, {"ruleId": "805", "severity": 1, "message": "806", "line": 539, "column": 25, "nodeType": "807", "endLine": 544, "endColumn": 27}, {"ruleId": "805", "severity": 1, "message": "806", "line": 695, "column": 17, "nodeType": "807", "endLine": 699, "endColumn": 19}, {"ruleId": "801", "severity": 1, "message": "810", "line": 153, "column": 6, "nodeType": "803", "endLine": 153, "endColumn": 39, "suggestions": "811"}, {"ruleId": "801", "severity": 1, "message": "812", "line": 78, "column": 6, "nodeType": "803", "endLine": 78, "endColumn": 49, "suggestions": "813"}, {"ruleId": "801", "severity": 1, "message": "814", "line": 96, "column": 6, "nodeType": "803", "endLine": 96, "endColumn": 31, "suggestions": "815"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 28, "column": 11, "nodeType": "818", "messageId": "819", "endLine": 28, "endColumn": 14, "suggestions": "820"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 36, "column": 20, "nodeType": "818", "messageId": "819", "endLine": 36, "endColumn": 23, "suggestions": "821"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 37, "column": 16, "nodeType": "818", "messageId": "819", "endLine": 37, "endColumn": 19, "suggestions": "822"}, {"ruleId": "801", "severity": 1, "message": "823", "line": 49, "column": 6, "nodeType": "803", "endLine": 49, "endColumn": 8, "suggestions": "824"}, {"ruleId": "801", "severity": 1, "message": "823", "line": 64, "column": 6, "nodeType": "803", "endLine": 64, "endColumn": 8, "suggestions": "825"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 90, "column": 41, "nodeType": "818", "messageId": "819", "endLine": 90, "endColumn": 44, "suggestions": "826"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 142, "column": 21, "nodeType": "818", "messageId": "819", "endLine": 142, "endColumn": 24, "suggestions": "827"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 272, "column": 108, "nodeType": "818", "messageId": "819", "endLine": 272, "endColumn": 111, "suggestions": "828"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 91, "column": 21, "nodeType": "818", "messageId": "819", "endLine": 91, "endColumn": 24, "suggestions": "829"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 99, "column": 21, "nodeType": "818", "messageId": "819", "endLine": 99, "endColumn": 24, "suggestions": "830"}, {"ruleId": "805", "severity": 1, "message": "806", "line": 129, "column": 17, "nodeType": "807", "endLine": 133, "endColumn": 19}, {"ruleId": "816", "severity": 2, "message": "817", "line": 227, "column": 21, "nodeType": "818", "messageId": "819", "endLine": 227, "endColumn": 24, "suggestions": "831"}, {"ruleId": "805", "severity": 1, "message": "806", "line": 297, "column": 21, "nodeType": "807", "endLine": 301, "endColumn": 23}, {"ruleId": "816", "severity": 2, "message": "817", "line": 121, "column": 32, "nodeType": "818", "messageId": "819", "endLine": 121, "endColumn": 35, "suggestions": "832"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 236, "column": 21, "nodeType": "818", "messageId": "819", "endLine": 236, "endColumn": 24, "suggestions": "833"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 378, "column": 73, "nodeType": "818", "messageId": "819", "endLine": 378, "endColumn": 76, "suggestions": "834"}, {"ruleId": "805", "severity": 1, "message": "806", "line": 43, "column": 15, "nodeType": "807", "endLine": 47, "endColumn": 17}, {"ruleId": "816", "severity": 2, "message": "817", "line": 102, "column": 21, "nodeType": "818", "messageId": "819", "endLine": 102, "endColumn": 24, "suggestions": "835"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 15, "column": 9, "nodeType": "818", "messageId": "819", "endLine": 15, "endColumn": 12, "suggestions": "836"}, {"ruleId": "805", "severity": 1, "message": "806", "line": 57, "column": 19, "nodeType": "807", "endLine": 61, "endColumn": 21}, {"ruleId": "816", "severity": 2, "message": "817", "line": 25, "column": 9, "nodeType": "818", "messageId": "819", "endLine": 25, "endColumn": 12, "suggestions": "837"}, {"ruleId": "805", "severity": 1, "message": "806", "line": 50, "column": 13, "nodeType": "807", "endLine": 54, "endColumn": 15}, {"ruleId": "816", "severity": 2, "message": "817", "line": 74, "column": 70, "nodeType": "818", "messageId": "819", "endLine": 74, "endColumn": 73, "suggestions": "838"}, {"ruleId": "801", "severity": 1, "message": "839", "line": 118, "column": 6, "nodeType": "803", "endLine": 118, "endColumn": 36, "suggestions": "840"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 141, "column": 55, "nodeType": "818", "messageId": "819", "endLine": 141, "endColumn": 58, "suggestions": "841"}, {"ruleId": "801", "severity": 1, "message": "842", "line": 74, "column": 6, "nodeType": "803", "endLine": 74, "endColumn": 20, "suggestions": "843"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 87, "column": 77, "nodeType": "818", "messageId": "819", "endLine": 87, "endColumn": 80, "suggestions": "844"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 92, "column": 56, "nodeType": "818", "messageId": "819", "endLine": 92, "endColumn": 59, "suggestions": "845"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 140, "column": 43, "nodeType": "818", "messageId": "819", "endLine": 140, "endColumn": 46, "suggestions": "846"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 192, "column": 21, "nodeType": "818", "messageId": "819", "endLine": 192, "endColumn": 24, "suggestions": "847"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 257, "column": 21, "nodeType": "818", "messageId": "819", "endLine": 257, "endColumn": 24, "suggestions": "848"}, {"ruleId": "805", "severity": 1, "message": "806", "line": 327, "column": 27, "nodeType": "807", "endLine": 331, "endColumn": 29}, {"ruleId": "816", "severity": 2, "message": "817", "line": 123, "column": 21, "nodeType": "818", "messageId": "819", "endLine": 123, "endColumn": 24, "suggestions": "849"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 211, "column": 67, "nodeType": "818", "messageId": "819", "endLine": 211, "endColumn": 70, "suggestions": "850"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 94, "column": 21, "nodeType": "818", "messageId": "819", "endLine": 94, "endColumn": 24, "suggestions": "851"}, {"ruleId": "801", "severity": 1, "message": "852", "line": 23, "column": 6, "nodeType": "803", "endLine": 23, "endColumn": 16, "suggestions": "853"}, {"ruleId": "801", "severity": 1, "message": "810", "line": 64, "column": 6, "nodeType": "803", "endLine": 64, "endColumn": 24, "suggestions": "854"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 32, "column": 23, "nodeType": "818", "messageId": "819", "endLine": 32, "endColumn": 26, "suggestions": "855"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 33, "column": 23, "nodeType": "818", "messageId": "819", "endLine": 33, "endColumn": 26, "suggestions": "856"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 40, "column": 48, "nodeType": "818", "messageId": "819", "endLine": 40, "endColumn": 51, "suggestions": "857"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 177, "column": 74, "nodeType": "818", "messageId": "819", "endLine": 177, "endColumn": 77, "suggestions": "858"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 35, "column": 23, "nodeType": "818", "messageId": "819", "endLine": 35, "endColumn": 26, "suggestions": "859"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 36, "column": 23, "nodeType": "818", "messageId": "819", "endLine": 36, "endColumn": 26, "suggestions": "860"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 169, "column": 23, "nodeType": "818", "messageId": "819", "endLine": 169, "endColumn": 26, "suggestions": "861"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 49, "column": 23, "nodeType": "818", "messageId": "819", "endLine": 49, "endColumn": 26, "suggestions": "862"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 50, "column": 23, "nodeType": "818", "messageId": "819", "endLine": 50, "endColumn": 26, "suggestions": "863"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 83, "column": 76, "nodeType": "818", "messageId": "819", "endLine": 83, "endColumn": 79, "suggestions": "864"}, {"ruleId": "865", "severity": 2, "message": "866", "line": 116, "column": 11, "nodeType": null, "messageId": "867", "endLine": 116, "endColumn": 26}, {"ruleId": "816", "severity": 2, "message": "817", "line": 39, "column": 23, "nodeType": "818", "messageId": "819", "endLine": 39, "endColumn": 26, "suggestions": "868"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 40, "column": 23, "nodeType": "818", "messageId": "819", "endLine": 40, "endColumn": 26, "suggestions": "869"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 175, "column": 23, "nodeType": "818", "messageId": "819", "endLine": 175, "endColumn": 26, "suggestions": "870"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 44, "column": 23, "nodeType": "818", "messageId": "819", "endLine": 44, "endColumn": 26, "suggestions": "871"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 45, "column": 23, "nodeType": "818", "messageId": "819", "endLine": 45, "endColumn": 26, "suggestions": "872"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 6, "column": 41, "nodeType": "818", "messageId": "819", "endLine": 6, "endColumn": 44, "suggestions": "873"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 8, "column": 63, "nodeType": "818", "messageId": "819", "endLine": 8, "endColumn": 66, "suggestions": "874"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 82, "column": 35, "nodeType": "818", "messageId": "819", "endLine": 82, "endColumn": 38, "suggestions": "875"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 284, "column": 40, "nodeType": "818", "messageId": "819", "endLine": 284, "endColumn": 43, "suggestions": "876"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 301, "column": 45, "nodeType": "818", "messageId": "819", "endLine": 301, "endColumn": 48, "suggestions": "877"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 319, "column": 41, "nodeType": "818", "messageId": "819", "endLine": 319, "endColumn": 44, "suggestions": "878"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 355, "column": 57, "nodeType": "818", "messageId": "819", "endLine": 355, "endColumn": 60, "suggestions": "879"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 371, "column": 39, "nodeType": "818", "messageId": "819", "endLine": 371, "endColumn": 42, "suggestions": "880"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 373, "column": 42, "nodeType": "818", "messageId": "819", "endLine": 373, "endColumn": 45, "suggestions": "881"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 386, "column": 40, "nodeType": "818", "messageId": "819", "endLine": 386, "endColumn": 43, "suggestions": "882"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 396, "column": 46, "nodeType": "818", "messageId": "819", "endLine": 396, "endColumn": 49, "suggestions": "883"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 408, "column": 44, "nodeType": "818", "messageId": "819", "endLine": 408, "endColumn": 47, "suggestions": "884"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 446, "column": 45, "nodeType": "818", "messageId": "819", "endLine": 446, "endColumn": 48, "suggestions": "885"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 463, "column": 45, "nodeType": "818", "messageId": "819", "endLine": 463, "endColumn": 48, "suggestions": "886"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 501, "column": 47, "nodeType": "818", "messageId": "819", "endLine": 501, "endColumn": 50, "suggestions": "887"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 518, "column": 40, "nodeType": "818", "messageId": "819", "endLine": 518, "endColumn": 43, "suggestions": "888"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 557, "column": 49, "nodeType": "818", "messageId": "819", "endLine": 557, "endColumn": 52, "suggestions": "889"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 576, "column": 43, "nodeType": "818", "messageId": "819", "endLine": 576, "endColumn": 46, "suggestions": "890"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 578, "column": 42, "nodeType": "818", "messageId": "819", "endLine": 578, "endColumn": 45, "suggestions": "891"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 598, "column": 44, "nodeType": "818", "messageId": "819", "endLine": 598, "endColumn": 47, "suggestions": "892"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 600, "column": 44, "nodeType": "818", "messageId": "819", "endLine": 600, "endColumn": 47, "suggestions": "893"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 620, "column": 39, "nodeType": "818", "messageId": "819", "endLine": 620, "endColumn": 42, "suggestions": "894"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 622, "column": 46, "nodeType": "818", "messageId": "819", "endLine": 622, "endColumn": 49, "suggestions": "895"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 644, "column": 44, "nodeType": "818", "messageId": "819", "endLine": 644, "endColumn": 47, "suggestions": "896"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 648, "column": 43, "nodeType": "818", "messageId": "819", "endLine": 648, "endColumn": 46, "suggestions": "897"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 652, "column": 43, "nodeType": "818", "messageId": "819", "endLine": 652, "endColumn": 46, "suggestions": "898"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 679, "column": 42, "nodeType": "818", "messageId": "819", "endLine": 679, "endColumn": 45, "suggestions": "899"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 691, "column": 42, "nodeType": "818", "messageId": "819", "endLine": 691, "endColumn": 45, "suggestions": "900"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 692, "column": 44, "nodeType": "818", "messageId": "819", "endLine": 692, "endColumn": 47, "suggestions": "901"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 693, "column": 44, "nodeType": "818", "messageId": "819", "endLine": 693, "endColumn": 47, "suggestions": "902"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 694, "column": 47, "nodeType": "818", "messageId": "819", "endLine": 694, "endColumn": 50, "suggestions": "903"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 695, "column": 48, "nodeType": "818", "messageId": "819", "endLine": 695, "endColumn": 51, "suggestions": "904"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 696, "column": 43, "nodeType": "818", "messageId": "819", "endLine": 696, "endColumn": 46, "suggestions": "905"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 697, "column": 45, "nodeType": "818", "messageId": "819", "endLine": 697, "endColumn": 48, "suggestions": "906"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 698, "column": 44, "nodeType": "818", "messageId": "819", "endLine": 698, "endColumn": 47, "suggestions": "907"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 700, "column": 41, "nodeType": "818", "messageId": "819", "endLine": 700, "endColumn": 44, "suggestions": "908"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 701, "column": 43, "nodeType": "818", "messageId": "819", "endLine": 701, "endColumn": 46, "suggestions": "909"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 702, "column": 43, "nodeType": "818", "messageId": "819", "endLine": 702, "endColumn": 46, "suggestions": "910"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 703, "column": 46, "nodeType": "818", "messageId": "819", "endLine": 703, "endColumn": 49, "suggestions": "911"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 704, "column": 47, "nodeType": "818", "messageId": "819", "endLine": 704, "endColumn": 50, "suggestions": "912"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 705, "column": 42, "nodeType": "818", "messageId": "819", "endLine": 705, "endColumn": 45, "suggestions": "913"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 706, "column": 44, "nodeType": "818", "messageId": "819", "endLine": 706, "endColumn": 47, "suggestions": "914"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 707, "column": 43, "nodeType": "818", "messageId": "819", "endLine": 707, "endColumn": 46, "suggestions": "915"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 27, "column": 13, "nodeType": "818", "messageId": "819", "endLine": 27, "endColumn": 16, "suggestions": "916"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 28, "column": 24, "nodeType": "818", "messageId": "819", "endLine": 28, "endColumn": 27, "suggestions": "917"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 162, "column": 67, "nodeType": "818", "messageId": "819", "endLine": 162, "endColumn": 70, "suggestions": "918"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 175, "column": 51, "nodeType": "818", "messageId": "819", "endLine": 175, "endColumn": 54, "suggestions": "919"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 197, "column": 50, "nodeType": "818", "messageId": "819", "endLine": 197, "endColumn": 53, "suggestions": "920"}, {"ruleId": "921", "severity": 1, "message": "922", "line": 611, "column": 13, "nodeType": "807", "endLine": 611, "endColumn": 58}, {"ruleId": "805", "severity": 1, "message": "806", "line": 638, "column": 21, "nodeType": "807", "endLine": 642, "endColumn": 23}, {"ruleId": "805", "severity": 1, "message": "806", "line": 691, "column": 21, "nodeType": "807", "endLine": 695, "endColumn": 23}, {"ruleId": "816", "severity": 2, "message": "817", "line": 29, "column": 13, "nodeType": "818", "messageId": "819", "endLine": 29, "endColumn": 16, "suggestions": "923"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 30, "column": 24, "nodeType": "818", "messageId": "819", "endLine": 30, "endColumn": 27, "suggestions": "924"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 130, "column": 45, "nodeType": "818", "messageId": "819", "endLine": 130, "endColumn": 48, "suggestions": "925"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 150, "column": 69, "nodeType": "818", "messageId": "819", "endLine": 150, "endColumn": 72, "suggestions": "926"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 20, "column": 13, "nodeType": "818", "messageId": "819", "endLine": 20, "endColumn": 16, "suggestions": "927"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 21, "column": 24, "nodeType": "818", "messageId": "819", "endLine": 21, "endColumn": 27, "suggestions": "928"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 114, "column": 64, "nodeType": "818", "messageId": "819", "endLine": 114, "endColumn": 67, "suggestions": "929"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 22, "column": 13, "nodeType": "818", "messageId": "819", "endLine": 22, "endColumn": 16, "suggestions": "930"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 23, "column": 24, "nodeType": "818", "messageId": "819", "endLine": 23, "endColumn": 27, "suggestions": "931"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 174, "column": 45, "nodeType": "818", "messageId": "819", "endLine": 174, "endColumn": 48, "suggestions": "932"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 30, "column": 13, "nodeType": "818", "messageId": "819", "endLine": 30, "endColumn": 16, "suggestions": "933"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 31, "column": 24, "nodeType": "818", "messageId": "819", "endLine": 31, "endColumn": 27, "suggestions": "934"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 167, "column": 38, "nodeType": "818", "messageId": "819", "endLine": 167, "endColumn": 41, "suggestions": "935"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 177, "column": 50, "nodeType": "818", "messageId": "819", "endLine": 177, "endColumn": 53, "suggestions": "936"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 209, "column": 45, "nodeType": "818", "messageId": "819", "endLine": 209, "endColumn": 48, "suggestions": "937"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 229, "column": 71, "nodeType": "818", "messageId": "819", "endLine": 229, "endColumn": 74, "suggestions": "938"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 293, "column": 60, "nodeType": "818", "messageId": "819", "endLine": 293, "endColumn": 63, "suggestions": "939"}, {"ruleId": "940", "severity": 2, "message": "941", "line": 564, "column": 28, "nodeType": "942", "messageId": "943", "suggestions": "944"}, {"ruleId": "940", "severity": 2, "message": "941", "line": 564, "column": 35, "nodeType": "942", "messageId": "943", "suggestions": "945"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 6, "column": 13, "nodeType": "818", "messageId": "819", "endLine": 6, "endColumn": 16, "suggestions": "946"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 7, "column": 33, "nodeType": "818", "messageId": "819", "endLine": 7, "endColumn": 36, "suggestions": "947"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 14, "column": 44, "nodeType": "818", "messageId": "819", "endLine": 14, "endColumn": 47, "suggestions": "948"}, {"ruleId": "801", "severity": 1, "message": "949", "line": 19, "column": 6, "nodeType": "803", "endLine": 19, "endColumn": 8, "suggestions": "950"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 39, "column": 40, "nodeType": "818", "messageId": "819", "endLine": 39, "endColumn": 43, "suggestions": "951"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 44, "column": 40, "nodeType": "818", "messageId": "819", "endLine": 44, "endColumn": 43, "suggestions": "952"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 9, "column": 20, "nodeType": "818", "messageId": "819", "endLine": 9, "endColumn": 23, "suggestions": "953"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 27, "column": 56, "nodeType": "818", "messageId": "819", "endLine": 27, "endColumn": 59, "suggestions": "954"}, {"ruleId": "816", "severity": 2, "message": "817", "line": 69, "column": 21, "nodeType": "818", "messageId": "819", "endLine": 69, "endColumn": 24, "suggestions": "955"}, {"ruleId": "805", "severity": 1, "message": "806", "line": 136, "column": 13, "nodeType": "807", "endLine": 140, "endColumn": 15}, {"ruleId": "956", "severity": 2, "message": "957", "line": 4, "column": 18, "nodeType": "958", "messageId": "959", "endLine": 4, "endColumn": 28, "suggestions": "960"}, {"ruleId": "956", "severity": 2, "message": "957", "line": 4, "column": 18, "nodeType": "958", "messageId": "959", "endLine": 4, "endColumn": 28, "suggestions": "961"}, {"ruleId": "956", "severity": 2, "message": "957", "line": 4, "column": 18, "nodeType": "958", "messageId": "959", "endLine": 4, "endColumn": 31, "suggestions": "962"}, {"ruleId": "801", "severity": 1, "message": "963", "line": 37, "column": 6, "nodeType": "803", "endLine": 37, "endColumn": 8, "suggestions": "964"}, {"ruleId": "801", "severity": 1, "message": "965", "line": 166, "column": 6, "nodeType": "803", "endLine": 166, "endColumn": 8, "suggestions": "966"}, {"ruleId": "801", "severity": 1, "message": "967", "line": 179, "column": 6, "nodeType": "803", "endLine": 179, "endColumn": 29, "suggestions": "968"}, {"ruleId": "801", "severity": 1, "message": "969", "line": 179, "column": 7, "nodeType": "970", "endLine": 179, "endColumn": 28}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchFolderData'. Either include it or remove the dependency array.", "ArrayExpression", ["971"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "React Hook useEffect has a missing dependency: 'fetchPhotos'. Either include it or remove the dependency array.", ["972"], "React Hook useEffect has a missing dependency: 'fetchMembers'. Either include it or remove the dependency array.", ["973"], "React Hook useEffect has a missing dependency: 'fetchNotifications'. Either include it or remove the dependency array.", ["974"], "React Hook useEffect has a missing dependency: 'fetchReportsData'. Either include it or remove the dependency array.", ["975"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["976", "977"], ["978", "979"], ["980", "981"], "React Hook useEffect has a missing dependency: 'checkAuthAndLoadData'. Either include it or remove the dependency array.", ["982"], ["983"], ["984", "985"], ["986", "987"], ["988", "989"], ["990", "991"], ["992", "993"], ["994", "995"], ["996", "997"], ["998", "999"], ["1000", "1001"], ["1002", "1003"], ["1004", "1005"], ["1006", "1007"], ["1008", "1009"], "React Hook useEffect has a missing dependency: 'fetchAccountStatement'. Either include it or remove the dependency array.", ["1010"], ["1011", "1012"], "React Hook useEffect has a missing dependency: 'fetchMemberDetails'. Either include it or remove the dependency array.", ["1013"], ["1014", "1015"], ["1016", "1017"], ["1018", "1019"], ["1020", "1021"], ["1022", "1023"], ["1024", "1025"], ["1026", "1027"], ["1028", "1029"], "React Hook useEffect has a missing dependency: 'fetchQuickStats'. Either include it or remove the dependency array.", ["1030"], ["1031"], ["1032", "1033"], ["1034", "1035"], ["1036", "1037"], ["1038", "1039"], ["1040", "1041"], ["1042", "1043"], ["1044", "1045"], ["1046", "1047"], ["1048", "1049"], ["1050", "1051"], "@typescript-eslint/no-unused-vars", "'filteredMembers' is assigned a value but never used.", "unusedVar", ["1052", "1053"], ["1054", "1055"], ["1056", "1057"], ["1058", "1059"], ["1060", "1061"], ["1062", "1063"], ["1064", "1065"], ["1066", "1067"], ["1068", "1069"], ["1070", "1071"], ["1072", "1073"], ["1074", "1075"], ["1076", "1077"], ["1078", "1079"], ["1080", "1081"], ["1082", "1083"], ["1084", "1085"], ["1086", "1087"], ["1088", "1089"], ["1090", "1091"], ["1092", "1093"], ["1094", "1095"], ["1096", "1097"], ["1098", "1099"], ["1100", "1101"], ["1102", "1103"], ["1104", "1105"], ["1106", "1107"], ["1108", "1109"], ["1110", "1111"], ["1112", "1113"], ["1114", "1115"], ["1116", "1117"], ["1118", "1119"], ["1120", "1121"], ["1122", "1123"], ["1124", "1125"], ["1126", "1127"], ["1128", "1129"], ["1130", "1131"], ["1132", "1133"], ["1134", "1135"], ["1136", "1137"], ["1138", "1139"], ["1140", "1141"], ["1142", "1143"], ["1144", "1145"], ["1146", "1147"], ["1148", "1149"], ["1150", "1151"], ["1152", "1153"], ["1154", "1155"], ["1156", "1157"], "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", ["1158", "1159"], ["1160", "1161"], ["1162", "1163"], ["1164", "1165"], ["1166", "1167"], ["1168", "1169"], ["1170", "1171"], ["1172", "1173"], ["1174", "1175"], ["1176", "1177"], ["1178", "1179"], ["1180", "1181"], ["1182", "1183"], ["1184", "1185"], ["1186", "1187"], ["1188", "1189"], ["1190", "1191"], "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["1192", "1193", "1194", "1195"], ["1196", "1197", "1198", "1199"], ["1200", "1201"], ["1202", "1203"], ["1204", "1205"], "React Hook useEffect has a missing dependency: 'loadSettings'. Either include it or remove the dependency array.", ["1206"], ["1207", "1208"], ["1209", "1210"], ["1211", "1212"], ["1213", "1214"], ["1215", "1216"], "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["1217"], ["1218"], ["1219"], "React Hook useCallback has a missing dependency: 'removeToast'. Either include it or remove the dependency array.", ["1220"], "React Hook useEffect has a missing dependency: 'checkSession'. Either include it or remove the dependency array.", ["1221"], "React Hook useEffect has missing dependencies: 'checkSession' and 'user'. Either include them or remove the dependency array.", ["1222"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "ConditionalExpression", {"desc": "1223", "fix": "1224"}, {"desc": "1225", "fix": "1226"}, {"desc": "1227", "fix": "1228"}, {"desc": "1229", "fix": "1230"}, {"desc": "1231", "fix": "1232"}, {"messageId": "1233", "fix": "1234", "desc": "1235"}, {"messageId": "1236", "fix": "1237", "desc": "1238"}, {"messageId": "1233", "fix": "1239", "desc": "1235"}, {"messageId": "1236", "fix": "1240", "desc": "1238"}, {"messageId": "1233", "fix": "1241", "desc": "1235"}, {"messageId": "1236", "fix": "1242", "desc": "1238"}, {"desc": "1243", "fix": "1244"}, {"desc": "1243", "fix": "1245"}, {"messageId": "1233", "fix": "1246", "desc": "1235"}, {"messageId": "1236", "fix": "1247", "desc": "1238"}, {"messageId": "1233", "fix": "1248", "desc": "1235"}, {"messageId": "1236", "fix": "1249", "desc": "1238"}, {"messageId": "1233", "fix": "1250", "desc": "1235"}, {"messageId": "1236", "fix": "1251", "desc": "1238"}, {"messageId": "1233", "fix": "1252", "desc": "1235"}, {"messageId": "1236", "fix": "1253", "desc": "1238"}, {"messageId": "1233", "fix": "1254", "desc": "1235"}, {"messageId": "1236", "fix": "1255", "desc": "1238"}, {"messageId": "1233", "fix": "1256", "desc": "1235"}, {"messageId": "1236", "fix": "1257", "desc": "1238"}, {"messageId": "1233", "fix": "1258", "desc": "1235"}, {"messageId": "1236", "fix": "1259", "desc": "1238"}, {"messageId": "1233", "fix": "1260", "desc": "1235"}, {"messageId": "1236", "fix": "1261", "desc": "1238"}, {"messageId": "1233", "fix": "1262", "desc": "1235"}, {"messageId": "1236", "fix": "1263", "desc": "1238"}, {"messageId": "1233", "fix": "1264", "desc": "1235"}, {"messageId": "1236", "fix": "1265", "desc": "1238"}, {"messageId": "1233", "fix": "1266", "desc": "1235"}, {"messageId": "1236", "fix": "1267", "desc": "1238"}, {"messageId": "1233", "fix": "1268", "desc": "1235"}, {"messageId": "1236", "fix": "1269", "desc": "1238"}, {"messageId": "1233", "fix": "1270", "desc": "1235"}, {"messageId": "1236", "fix": "1271", "desc": "1238"}, {"desc": "1272", "fix": "1273"}, {"messageId": "1233", "fix": "1274", "desc": "1235"}, {"messageId": "1236", "fix": "1275", "desc": "1238"}, {"desc": "1276", "fix": "1277"}, {"messageId": "1233", "fix": "1278", "desc": "1235"}, {"messageId": "1236", "fix": "1279", "desc": "1238"}, {"messageId": "1233", "fix": "1280", "desc": "1235"}, {"messageId": "1236", "fix": "1281", "desc": "1238"}, {"messageId": "1233", "fix": "1282", "desc": "1235"}, {"messageId": "1236", "fix": "1283", "desc": "1238"}, {"messageId": "1233", "fix": "1284", "desc": "1235"}, {"messageId": "1236", "fix": "1285", "desc": "1238"}, {"messageId": "1233", "fix": "1286", "desc": "1235"}, {"messageId": "1236", "fix": "1287", "desc": "1238"}, {"messageId": "1233", "fix": "1288", "desc": "1235"}, {"messageId": "1236", "fix": "1289", "desc": "1238"}, {"messageId": "1233", "fix": "1290", "desc": "1235"}, {"messageId": "1236", "fix": "1291", "desc": "1238"}, {"messageId": "1233", "fix": "1292", "desc": "1235"}, {"messageId": "1236", "fix": "1293", "desc": "1238"}, {"desc": "1294", "fix": "1295"}, {"desc": "1296", "fix": "1297"}, {"messageId": "1233", "fix": "1298", "desc": "1235"}, {"messageId": "1236", "fix": "1299", "desc": "1238"}, {"messageId": "1233", "fix": "1300", "desc": "1235"}, {"messageId": "1236", "fix": "1301", "desc": "1238"}, {"messageId": "1233", "fix": "1302", "desc": "1235"}, {"messageId": "1236", "fix": "1303", "desc": "1238"}, {"messageId": "1233", "fix": "1304", "desc": "1235"}, {"messageId": "1236", "fix": "1305", "desc": "1238"}, {"messageId": "1233", "fix": "1306", "desc": "1235"}, {"messageId": "1236", "fix": "1307", "desc": "1238"}, {"messageId": "1233", "fix": "1308", "desc": "1235"}, {"messageId": "1236", "fix": "1309", "desc": "1238"}, {"messageId": "1233", "fix": "1310", "desc": "1235"}, {"messageId": "1236", "fix": "1311", "desc": "1238"}, {"messageId": "1233", "fix": "1312", "desc": "1235"}, {"messageId": "1236", "fix": "1313", "desc": "1238"}, {"messageId": "1233", "fix": "1314", "desc": "1235"}, {"messageId": "1236", "fix": "1315", "desc": "1238"}, {"messageId": "1233", "fix": "1316", "desc": "1235"}, {"messageId": "1236", "fix": "1317", "desc": "1238"}, {"messageId": "1233", "fix": "1318", "desc": "1235"}, {"messageId": "1236", "fix": "1319", "desc": "1238"}, {"messageId": "1233", "fix": "1320", "desc": "1235"}, {"messageId": "1236", "fix": "1321", "desc": "1238"}, {"messageId": "1233", "fix": "1322", "desc": "1235"}, {"messageId": "1236", "fix": "1323", "desc": "1238"}, {"messageId": "1233", "fix": "1324", "desc": "1235"}, {"messageId": "1236", "fix": "1325", "desc": "1238"}, {"messageId": "1233", "fix": "1326", "desc": "1235"}, {"messageId": "1236", "fix": "1327", "desc": "1238"}, {"messageId": "1233", "fix": "1328", "desc": "1235"}, {"messageId": "1236", "fix": "1329", "desc": "1238"}, {"messageId": "1233", "fix": "1330", "desc": "1235"}, {"messageId": "1236", "fix": "1331", "desc": "1238"}, {"messageId": "1233", "fix": "1332", "desc": "1235"}, {"messageId": "1236", "fix": "1333", "desc": "1238"}, {"messageId": "1233", "fix": "1334", "desc": "1235"}, {"messageId": "1236", "fix": "1335", "desc": "1238"}, {"messageId": "1233", "fix": "1336", "desc": "1235"}, {"messageId": "1236", "fix": "1337", "desc": "1238"}, {"messageId": "1233", "fix": "1338", "desc": "1235"}, {"messageId": "1236", "fix": "1339", "desc": "1238"}, {"messageId": "1233", "fix": "1340", "desc": "1235"}, {"messageId": "1236", "fix": "1341", "desc": "1238"}, {"messageId": "1233", "fix": "1342", "desc": "1235"}, {"messageId": "1236", "fix": "1343", "desc": "1238"}, {"messageId": "1233", "fix": "1344", "desc": "1235"}, {"messageId": "1236", "fix": "1345", "desc": "1238"}, {"messageId": "1233", "fix": "1346", "desc": "1235"}, {"messageId": "1236", "fix": "1347", "desc": "1238"}, {"messageId": "1233", "fix": "1348", "desc": "1235"}, {"messageId": "1236", "fix": "1349", "desc": "1238"}, {"messageId": "1233", "fix": "1350", "desc": "1235"}, {"messageId": "1236", "fix": "1351", "desc": "1238"}, {"messageId": "1233", "fix": "1352", "desc": "1235"}, {"messageId": "1236", "fix": "1353", "desc": "1238"}, {"messageId": "1233", "fix": "1354", "desc": "1235"}, {"messageId": "1236", "fix": "1355", "desc": "1238"}, {"messageId": "1233", "fix": "1356", "desc": "1235"}, {"messageId": "1236", "fix": "1357", "desc": "1238"}, {"messageId": "1233", "fix": "1358", "desc": "1235"}, {"messageId": "1236", "fix": "1359", "desc": "1238"}, {"messageId": "1233", "fix": "1360", "desc": "1235"}, {"messageId": "1236", "fix": "1361", "desc": "1238"}, {"messageId": "1233", "fix": "1362", "desc": "1235"}, {"messageId": "1236", "fix": "1363", "desc": "1238"}, {"messageId": "1233", "fix": "1364", "desc": "1235"}, {"messageId": "1236", "fix": "1365", "desc": "1238"}, {"messageId": "1233", "fix": "1366", "desc": "1235"}, {"messageId": "1236", "fix": "1367", "desc": "1238"}, {"messageId": "1233", "fix": "1368", "desc": "1235"}, {"messageId": "1236", "fix": "1369", "desc": "1238"}, {"messageId": "1233", "fix": "1370", "desc": "1235"}, {"messageId": "1236", "fix": "1371", "desc": "1238"}, {"messageId": "1233", "fix": "1372", "desc": "1235"}, {"messageId": "1236", "fix": "1373", "desc": "1238"}, {"messageId": "1233", "fix": "1374", "desc": "1235"}, {"messageId": "1236", "fix": "1375", "desc": "1238"}, {"messageId": "1233", "fix": "1376", "desc": "1235"}, {"messageId": "1236", "fix": "1377", "desc": "1238"}, {"messageId": "1233", "fix": "1378", "desc": "1235"}, {"messageId": "1236", "fix": "1379", "desc": "1238"}, {"messageId": "1233", "fix": "1380", "desc": "1235"}, {"messageId": "1236", "fix": "1381", "desc": "1238"}, {"messageId": "1233", "fix": "1382", "desc": "1235"}, {"messageId": "1236", "fix": "1383", "desc": "1238"}, {"messageId": "1233", "fix": "1384", "desc": "1235"}, {"messageId": "1236", "fix": "1385", "desc": "1238"}, {"messageId": "1233", "fix": "1386", "desc": "1235"}, {"messageId": "1236", "fix": "1387", "desc": "1238"}, {"messageId": "1233", "fix": "1388", "desc": "1235"}, {"messageId": "1236", "fix": "1389", "desc": "1238"}, {"messageId": "1233", "fix": "1390", "desc": "1235"}, {"messageId": "1236", "fix": "1391", "desc": "1238"}, {"messageId": "1233", "fix": "1392", "desc": "1235"}, {"messageId": "1236", "fix": "1393", "desc": "1238"}, {"messageId": "1233", "fix": "1394", "desc": "1235"}, {"messageId": "1236", "fix": "1395", "desc": "1238"}, {"messageId": "1233", "fix": "1396", "desc": "1235"}, {"messageId": "1236", "fix": "1397", "desc": "1238"}, {"messageId": "1233", "fix": "1398", "desc": "1235"}, {"messageId": "1236", "fix": "1399", "desc": "1238"}, {"messageId": "1233", "fix": "1400", "desc": "1235"}, {"messageId": "1236", "fix": "1401", "desc": "1238"}, {"messageId": "1233", "fix": "1402", "desc": "1235"}, {"messageId": "1236", "fix": "1403", "desc": "1238"}, {"messageId": "1233", "fix": "1404", "desc": "1235"}, {"messageId": "1236", "fix": "1405", "desc": "1238"}, {"messageId": "1233", "fix": "1406", "desc": "1235"}, {"messageId": "1236", "fix": "1407", "desc": "1238"}, {"messageId": "1233", "fix": "1408", "desc": "1235"}, {"messageId": "1236", "fix": "1409", "desc": "1238"}, {"messageId": "1233", "fix": "1410", "desc": "1235"}, {"messageId": "1236", "fix": "1411", "desc": "1238"}, {"messageId": "1233", "fix": "1412", "desc": "1235"}, {"messageId": "1236", "fix": "1413", "desc": "1238"}, {"messageId": "1233", "fix": "1414", "desc": "1235"}, {"messageId": "1236", "fix": "1415", "desc": "1238"}, {"messageId": "1233", "fix": "1416", "desc": "1235"}, {"messageId": "1236", "fix": "1417", "desc": "1238"}, {"messageId": "1233", "fix": "1418", "desc": "1235"}, {"messageId": "1236", "fix": "1419", "desc": "1238"}, {"messageId": "1233", "fix": "1420", "desc": "1235"}, {"messageId": "1236", "fix": "1421", "desc": "1238"}, {"messageId": "1233", "fix": "1422", "desc": "1235"}, {"messageId": "1236", "fix": "1423", "desc": "1238"}, {"messageId": "1233", "fix": "1424", "desc": "1235"}, {"messageId": "1236", "fix": "1425", "desc": "1238"}, {"messageId": "1233", "fix": "1426", "desc": "1235"}, {"messageId": "1236", "fix": "1427", "desc": "1238"}, {"messageId": "1233", "fix": "1428", "desc": "1235"}, {"messageId": "1236", "fix": "1429", "desc": "1238"}, {"messageId": "1233", "fix": "1430", "desc": "1235"}, {"messageId": "1236", "fix": "1431", "desc": "1238"}, {"messageId": "1233", "fix": "1432", "desc": "1235"}, {"messageId": "1236", "fix": "1433", "desc": "1238"}, {"messageId": "1233", "fix": "1434", "desc": "1235"}, {"messageId": "1236", "fix": "1435", "desc": "1238"}, {"messageId": "1233", "fix": "1436", "desc": "1235"}, {"messageId": "1236", "fix": "1437", "desc": "1238"}, {"messageId": "1233", "fix": "1438", "desc": "1235"}, {"messageId": "1236", "fix": "1439", "desc": "1238"}, {"messageId": "1233", "fix": "1440", "desc": "1235"}, {"messageId": "1236", "fix": "1441", "desc": "1238"}, {"messageId": "1233", "fix": "1442", "desc": "1235"}, {"messageId": "1236", "fix": "1443", "desc": "1238"}, {"messageId": "1233", "fix": "1444", "desc": "1235"}, {"messageId": "1236", "fix": "1445", "desc": "1238"}, {"messageId": "1233", "fix": "1446", "desc": "1235"}, {"messageId": "1236", "fix": "1447", "desc": "1238"}, {"messageId": "1233", "fix": "1448", "desc": "1235"}, {"messageId": "1236", "fix": "1449", "desc": "1238"}, {"messageId": "1233", "fix": "1450", "desc": "1235"}, {"messageId": "1236", "fix": "1451", "desc": "1238"}, {"messageId": "1233", "fix": "1452", "desc": "1235"}, {"messageId": "1236", "fix": "1453", "desc": "1238"}, {"messageId": "1233", "fix": "1454", "desc": "1235"}, {"messageId": "1236", "fix": "1455", "desc": "1238"}, {"messageId": "1233", "fix": "1456", "desc": "1235"}, {"messageId": "1236", "fix": "1457", "desc": "1238"}, {"messageId": "1458", "data": "1459", "fix": "1460", "desc": "1461"}, {"messageId": "1458", "data": "1462", "fix": "1463", "desc": "1464"}, {"messageId": "1458", "data": "1465", "fix": "1466", "desc": "1467"}, {"messageId": "1458", "data": "1468", "fix": "1469", "desc": "1470"}, {"messageId": "1458", "data": "1471", "fix": "1472", "desc": "1461"}, {"messageId": "1458", "data": "1473", "fix": "1474", "desc": "1464"}, {"messageId": "1458", "data": "1475", "fix": "1476", "desc": "1467"}, {"messageId": "1458", "data": "1477", "fix": "1478", "desc": "1470"}, {"messageId": "1233", "fix": "1479", "desc": "1235"}, {"messageId": "1236", "fix": "1480", "desc": "1238"}, {"messageId": "1233", "fix": "1481", "desc": "1235"}, {"messageId": "1236", "fix": "1482", "desc": "1238"}, {"messageId": "1233", "fix": "1483", "desc": "1235"}, {"messageId": "1236", "fix": "1484", "desc": "1238"}, {"desc": "1485", "fix": "1486"}, {"messageId": "1233", "fix": "1487", "desc": "1235"}, {"messageId": "1236", "fix": "1488", "desc": "1238"}, {"messageId": "1233", "fix": "1489", "desc": "1235"}, {"messageId": "1236", "fix": "1490", "desc": "1238"}, {"messageId": "1233", "fix": "1491", "desc": "1235"}, {"messageId": "1236", "fix": "1492", "desc": "1238"}, {"messageId": "1233", "fix": "1493", "desc": "1235"}, {"messageId": "1236", "fix": "1494", "desc": "1238"}, {"messageId": "1233", "fix": "1495", "desc": "1235"}, {"messageId": "1236", "fix": "1496", "desc": "1238"}, {"messageId": "1497", "fix": "1498", "desc": "1499"}, {"messageId": "1497", "fix": "1500", "desc": "1499"}, {"messageId": "1497", "fix": "1501", "desc": "1499"}, {"desc": "1502", "fix": "1503"}, {"desc": "1504", "fix": "1505"}, {"desc": "1506", "fix": "1507"}, "Update the dependencies array to be: [search, session, folderId, folderType, fetchFolderData]", {"range": "1508", "text": "1509"}, "Update the dependencies array to be: [fetchPhotos, search, selectedCategory, session, viewMode]", {"range": "1510", "text": "1511"}, "Update the dependencies array to be: [search, status, pagination.page, fetchMembers]", {"range": "1512", "text": "1513"}, "Update the dependencies array to be: [search, filter, selectedCategory, session, fetchNotifications]", {"range": "1514", "text": "1515"}, "Update the dependencies array to be: [fetchReportsData, selectedPeriod, session]", {"range": "1516", "text": "1517"}, "suggestUnknown", {"range": "1518", "text": "1519"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1520", "text": "1521"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "1522", "text": "1519"}, {"range": "1523", "text": "1521"}, {"range": "1524", "text": "1519"}, {"range": "1525", "text": "1521"}, "Update the dependencies array to be: [checkAuthAndLoadData]", {"range": "1526", "text": "1527"}, {"range": "1528", "text": "1527"}, {"range": "1529", "text": "1519"}, {"range": "1530", "text": "1521"}, {"range": "1531", "text": "1519"}, {"range": "1532", "text": "1521"}, {"range": "1533", "text": "1519"}, {"range": "1534", "text": "1521"}, {"range": "1535", "text": "1519"}, {"range": "1536", "text": "1521"}, {"range": "1537", "text": "1519"}, {"range": "1538", "text": "1521"}, {"range": "1539", "text": "1519"}, {"range": "1540", "text": "1521"}, {"range": "1541", "text": "1519"}, {"range": "1542", "text": "1521"}, {"range": "1543", "text": "1519"}, {"range": "1544", "text": "1521"}, {"range": "1545", "text": "1519"}, {"range": "1546", "text": "1521"}, {"range": "1547", "text": "1519"}, {"range": "1548", "text": "1521"}, {"range": "1549", "text": "1519"}, {"range": "1550", "text": "1521"}, {"range": "1551", "text": "1519"}, {"range": "1552", "text": "1521"}, {"range": "1553", "text": "1519"}, {"range": "1554", "text": "1521"}, "Update the dependencies array to be: [open, memberId, selectedYear, fetchAccountStatement]", {"range": "1555", "text": "1556"}, {"range": "1557", "text": "1519"}, {"range": "1558", "text": "1521"}, "Update the dependencies array to be: [open, member, fetchMemberDetails]", {"range": "1559", "text": "1560"}, {"range": "1561", "text": "1519"}, {"range": "1562", "text": "1521"}, {"range": "1563", "text": "1519"}, {"range": "1564", "text": "1521"}, {"range": "1565", "text": "1519"}, {"range": "1566", "text": "1521"}, {"range": "1567", "text": "1519"}, {"range": "1568", "text": "1521"}, {"range": "1569", "text": "1519"}, {"range": "1570", "text": "1521"}, {"range": "1571", "text": "1519"}, {"range": "1572", "text": "1521"}, {"range": "1573", "text": "1519"}, {"range": "1574", "text": "1521"}, {"range": "1575", "text": "1519"}, {"range": "1576", "text": "1521"}, "Update the dependencies array to be: [fetchQuickStats, memberId]", {"range": "1577", "text": "1578"}, "Update the dependencies array to be: [fetchMembers, open, searchTerm]", {"range": "1579", "text": "1580"}, {"range": "1581", "text": "1519"}, {"range": "1582", "text": "1521"}, {"range": "1583", "text": "1519"}, {"range": "1584", "text": "1521"}, {"range": "1585", "text": "1519"}, {"range": "1586", "text": "1521"}, {"range": "1587", "text": "1519"}, {"range": "1588", "text": "1521"}, {"range": "1589", "text": "1519"}, {"range": "1590", "text": "1521"}, {"range": "1591", "text": "1519"}, {"range": "1592", "text": "1521"}, {"range": "1593", "text": "1519"}, {"range": "1594", "text": "1521"}, {"range": "1595", "text": "1519"}, {"range": "1596", "text": "1521"}, {"range": "1597", "text": "1519"}, {"range": "1598", "text": "1521"}, {"range": "1599", "text": "1519"}, {"range": "1600", "text": "1521"}, {"range": "1601", "text": "1519"}, {"range": "1602", "text": "1521"}, {"range": "1603", "text": "1519"}, {"range": "1604", "text": "1521"}, {"range": "1605", "text": "1519"}, {"range": "1606", "text": "1521"}, {"range": "1607", "text": "1519"}, {"range": "1608", "text": "1521"}, {"range": "1609", "text": "1519"}, {"range": "1610", "text": "1521"}, {"range": "1611", "text": "1519"}, {"range": "1612", "text": "1521"}, {"range": "1613", "text": "1519"}, {"range": "1614", "text": "1521"}, {"range": "1615", "text": "1519"}, {"range": "1616", "text": "1521"}, {"range": "1617", "text": "1519"}, {"range": "1618", "text": "1521"}, {"range": "1619", "text": "1519"}, {"range": "1620", "text": "1521"}, {"range": "1621", "text": "1519"}, {"range": "1622", "text": "1521"}, {"range": "1623", "text": "1519"}, {"range": "1624", "text": "1521"}, {"range": "1625", "text": "1519"}, {"range": "1626", "text": "1521"}, {"range": "1627", "text": "1519"}, {"range": "1628", "text": "1521"}, {"range": "1629", "text": "1519"}, {"range": "1630", "text": "1521"}, {"range": "1631", "text": "1519"}, {"range": "1632", "text": "1521"}, {"range": "1633", "text": "1519"}, {"range": "1634", "text": "1521"}, {"range": "1635", "text": "1519"}, {"range": "1636", "text": "1521"}, {"range": "1637", "text": "1519"}, {"range": "1638", "text": "1521"}, {"range": "1639", "text": "1519"}, {"range": "1640", "text": "1521"}, {"range": "1641", "text": "1519"}, {"range": "1642", "text": "1521"}, {"range": "1643", "text": "1519"}, {"range": "1644", "text": "1521"}, {"range": "1645", "text": "1519"}, {"range": "1646", "text": "1521"}, {"range": "1647", "text": "1519"}, {"range": "1648", "text": "1521"}, {"range": "1649", "text": "1519"}, {"range": "1650", "text": "1521"}, {"range": "1651", "text": "1519"}, {"range": "1652", "text": "1521"}, {"range": "1653", "text": "1519"}, {"range": "1654", "text": "1521"}, {"range": "1655", "text": "1519"}, {"range": "1656", "text": "1521"}, {"range": "1657", "text": "1519"}, {"range": "1658", "text": "1521"}, {"range": "1659", "text": "1519"}, {"range": "1660", "text": "1521"}, {"range": "1661", "text": "1519"}, {"range": "1662", "text": "1521"}, {"range": "1663", "text": "1519"}, {"range": "1664", "text": "1521"}, {"range": "1665", "text": "1519"}, {"range": "1666", "text": "1521"}, {"range": "1667", "text": "1519"}, {"range": "1668", "text": "1521"}, {"range": "1669", "text": "1519"}, {"range": "1670", "text": "1521"}, {"range": "1671", "text": "1519"}, {"range": "1672", "text": "1521"}, {"range": "1673", "text": "1519"}, {"range": "1674", "text": "1521"}, {"range": "1675", "text": "1519"}, {"range": "1676", "text": "1521"}, {"range": "1677", "text": "1519"}, {"range": "1678", "text": "1521"}, {"range": "1679", "text": "1519"}, {"range": "1680", "text": "1521"}, {"range": "1681", "text": "1519"}, {"range": "1682", "text": "1521"}, {"range": "1683", "text": "1519"}, {"range": "1684", "text": "1521"}, {"range": "1685", "text": "1519"}, {"range": "1686", "text": "1521"}, {"range": "1687", "text": "1519"}, {"range": "1688", "text": "1521"}, {"range": "1689", "text": "1519"}, {"range": "1690", "text": "1521"}, {"range": "1691", "text": "1519"}, {"range": "1692", "text": "1521"}, {"range": "1693", "text": "1519"}, {"range": "1694", "text": "1521"}, {"range": "1695", "text": "1519"}, {"range": "1696", "text": "1521"}, {"range": "1697", "text": "1519"}, {"range": "1698", "text": "1521"}, {"range": "1699", "text": "1519"}, {"range": "1700", "text": "1521"}, {"range": "1701", "text": "1519"}, {"range": "1702", "text": "1521"}, {"range": "1703", "text": "1519"}, {"range": "1704", "text": "1521"}, {"range": "1705", "text": "1519"}, {"range": "1706", "text": "1521"}, {"range": "1707", "text": "1519"}, {"range": "1708", "text": "1521"}, {"range": "1709", "text": "1519"}, {"range": "1710", "text": "1521"}, {"range": "1711", "text": "1519"}, {"range": "1712", "text": "1521"}, {"range": "1713", "text": "1519"}, {"range": "1714", "text": "1521"}, {"range": "1715", "text": "1519"}, {"range": "1716", "text": "1521"}, {"range": "1717", "text": "1519"}, {"range": "1718", "text": "1521"}, {"range": "1719", "text": "1519"}, {"range": "1720", "text": "1521"}, {"range": "1721", "text": "1519"}, {"range": "1722", "text": "1521"}, {"range": "1723", "text": "1519"}, {"range": "1724", "text": "1521"}, {"range": "1725", "text": "1519"}, {"range": "1726", "text": "1521"}, {"range": "1727", "text": "1519"}, {"range": "1728", "text": "1521"}, {"range": "1729", "text": "1519"}, {"range": "1730", "text": "1521"}, {"range": "1731", "text": "1519"}, {"range": "1732", "text": "1521"}, {"range": "1733", "text": "1519"}, {"range": "1734", "text": "1521"}, {"range": "1735", "text": "1519"}, {"range": "1736", "text": "1521"}, {"range": "1737", "text": "1519"}, {"range": "1738", "text": "1521"}, {"range": "1739", "text": "1519"}, {"range": "1740", "text": "1521"}, "replaceWithAlt", {"alt": "1741"}, {"range": "1742", "text": "1743"}, "Replace with `&quot;`.", {"alt": "1744"}, {"range": "1745", "text": "1746"}, "Replace with `&ldquo;`.", {"alt": "1747"}, {"range": "1748", "text": "1749"}, "Replace with `&#34;`.", {"alt": "1750"}, {"range": "1751", "text": "1752"}, "Replace with `&rdquo;`.", {"alt": "1741"}, {"range": "1753", "text": "1754"}, {"alt": "1744"}, {"range": "1755", "text": "1756"}, {"alt": "1747"}, {"range": "1757", "text": "1758"}, {"alt": "1750"}, {"range": "1759", "text": "1760"}, {"range": "1761", "text": "1519"}, {"range": "1762", "text": "1521"}, {"range": "1763", "text": "1519"}, {"range": "1764", "text": "1521"}, {"range": "1765", "text": "1519"}, {"range": "1766", "text": "1521"}, "Update the dependencies array to be: [loadSettings]", {"range": "1767", "text": "1768"}, {"range": "1769", "text": "1519"}, {"range": "1770", "text": "1521"}, {"range": "1771", "text": "1519"}, {"range": "1772", "text": "1521"}, {"range": "1773", "text": "1519"}, {"range": "1774", "text": "1521"}, {"range": "1775", "text": "1519"}, {"range": "1776", "text": "1521"}, {"range": "1777", "text": "1519"}, {"range": "1778", "text": "1521"}, "replaceEmptyInterfaceWithSuper", {"range": "1779", "text": "1780"}, "Replace empty interface with a type alias.", {"range": "1781", "text": "1782"}, {"range": "1783", "text": "1784"}, "Update the dependencies array to be: [removeToast]", {"range": "1785", "text": "1786"}, "Update the dependencies array to be: [checkSession]", {"range": "1787", "text": "1788"}, "Update the dependencies array to be: [checkSession, user]", {"range": "1789", "text": "1790"}, [4962, 5001], "[search, session, folderId, folderType, fetchFolderData]", [3824, 3869], "[fetchPhotos, search, selectedCategory, session, viewMode]", [4674, 4707], "[search, status, pagination.page, fetchMembers]", [2209, 2252], "[search, filter, selectedCategory, session, fetchNotifications]", [2292, 2317], "[fetchReport<PERSON><PERSON><PERSON>, selected<PERSON><PERSON><PERSON>, session]", [535, 538], "unknown", [535, 538], "never", [746, 749], [746, 749], [767, 770], [767, 770], [1170, 1172], "[checkAuthAndLoadData]", [1439, 1441], [2241, 2244], [2241, 2244], [3658, 3661], [3658, 3661], [9507, 9510], [9507, 9510], [2350, 2353], [2350, 2353], [2699, 2702], [2699, 2702], [6137, 6140], [6137, 6140], [3093, 3096], [3093, 3096], [6207, 6210], [6207, 6210], [12690, 12693], [12690, 12693], [2149, 2152], [2149, 2152], [340, 343], [340, 343], [756, 759], [756, 759], [2603, 2606], [2603, 2606], [2510, 2540], "[open, memberId, selected<PERSON>ear, fetchAccountStatement]", [3282, 3285], [3282, 3285], [1373, 1387], "[open, member, fetchMemberDetails]", [1778, 1781], [1778, 1781], [1955, 1958], [1955, 1958], [3521, 3524], [3521, 3524], [4810, 4813], [4810, 4813], [6493, 6496], [6493, 6496], [2969, 2972], [2969, 2972], [6168, 6171], [6168, 6171], [2235, 2238], [2235, 2238], [578, 588], "[fetchQuickStats, memberId]", [1416, 1434], "[fetchMembers, open, searchTerm]", [849, 852], [849, 852], [898, 901], [898, 901], [1248, 1251], [1248, 1251], [5592, 5595], [5592, 5595], [864, 867], [864, 867], [913, 916], [913, 916], [5263, 5266], [5263, 5266], [943, 946], [943, 946], [992, 995], [992, 995], [2326, 2329], [2326, 2329], [875, 878], [875, 878], [924, 927], [924, 927], [5261, 5264], [5261, 5264], [961, 964], [961, 964], [1010, 1013], [1010, 1013], [125, 128], [125, 128], [225, 228], [225, 228], [2467, 2470], [2467, 2470], [7664, 7667], [7664, 7667], [8219, 8222], [8219, 8222], [8862, 8865], [8862, 8865], [10077, 10080], [10077, 10080], [10602, 10605], [10602, 10605], [10780, 10783], [10780, 10783], [11080, 11083], [11080, 11083], [11458, 11461], [11458, 11461], [11769, 11772], [11769, 11772], [13049, 13052], [13049, 13052], [13622, 13625], [13622, 13625], [14909, 14912], [14909, 14912], [15490, 15493], [15490, 15493], [16863, 16866], [16863, 16866], [17538, 17541], [17538, 17541], [17666, 17669], [17666, 17669], [18161, 18164], [18161, 18164], [18295, 18298], [18295, 18298], [18797, 18800], [18797, 18800], [18967, 18970], [18967, 18970], [19604, 19607], [19604, 19607], [19727, 19730], [19727, 19730], [19849, 19852], [19849, 19852], [20776, 20779], [20776, 20779], [21214, 21217], [21214, 21217], [21298, 21301], [21298, 21301], [21382, 21385], [21382, 21385], [21469, 21472], [21469, 21472], [21557, 21560], [21557, 21560], [21640, 21643], [21640, 21643], [21725, 21728], [21725, 21728], [21809, 21812], [21809, 21812], [21891, 21894], [21891, 21894], [21973, 21976], [21973, 21976], [22055, 22058], [22055, 22058], [22140, 22143], [22140, 22143], [22226, 22229], [22226, 22229], [22307, 22310], [22307, 22310], [22390, 22393], [22390, 22393], [22472, 22475], [22472, 22475], [725, 728], [725, 728], [752, 755], [752, 755], [4755, 4758], [4755, 4758], [5138, 5141], [5138, 5141], [5863, 5866], [5863, 5866], [759, 762], [759, 762], [786, 789], [786, 789], [3068, 3071], [3068, 3071], [3712, 3715], [3712, 3715], [658, 661], [658, 661], [685, 688], [685, 688], [3410, 3413], [3410, 3413], [689, 692], [689, 692], [716, 719], [716, 719], [3967, 3970], [3967, 3970], [798, 801], [798, 801], [825, 828], [825, 828], [3939, 3942], [3939, 3942], [4209, 4212], [4209, 4212], [4990, 4993], [4990, 4993], [5636, 5639], [5636, 5639], [7152, 7155], [7152, 7155], "&quot;", [17667, 17685], "مدة &quot;تذكرني\" (يوم)", "&ldquo;", [17667, 17685], "مدة &ldquo;تذكرني\" (يوم)", "&#34;", [17667, 17685], "مدة &#34;تذكرني\" (يوم)", "&rdquo;", [17667, 17685], "مدة &rdquo;تذكرني\" (يوم)", [17667, 17685], "مدة \"تذكرني&quot; (يوم)", [17667, 17685], "مدة \"تذكرني&ldquo; (يوم)", [17667, 17685], "مدة \"تذكرني&#34; (يوم)", [17667, 17685], "مدة \"تذكرني&rdquo; (يوم)", [130, 133], [130, 133], [166, 169], [166, 169], [407, 410], [407, 410], [508, 510], "[loadSettings]", [990, 993], [990, 993], [1104, 1107], [1104, 1107], [232, 235], [232, 235], [664, 667], [664, 667], [1671, 1674], [1671, 1674], [72, 199], "type InputProps = React.InputHTMLAttributes<HTMLInputElement>", [72, 199], "type LabelProps = React.LabelHTMLAttributes<HTMLLabelElement>", [72, 211], "type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement>", [1058, 1060], "[removeToast]", [4802, 4804], "[checkSession]", [5111, 5134], "[checkSession, user]"]