[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\activities\\route.ts": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\activities\\[id]\\route.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\admin\\user-permissions\\route.ts": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\admin\\users\\route.ts": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\admin\\users\\[id]\\route.ts": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\clear-session\\route.ts": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\member\\session\\route.ts": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\member\\signin\\route.ts": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\member\\signout\\route.ts": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\[...nextauth]\\route.ts": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\expenses\\route.ts": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\expenses\\[id]\\route.ts": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\export\\route.ts": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery\\route.ts": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery\\[id]\\route.ts": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery-folders\\route.ts": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery-folders\\[id]\\route.ts": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\incomes\\route.ts": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\incomes\\[id]\\route.ts": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\member\\account-statement\\route.ts": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\member\\auth\\route.ts": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\member\\gallery\\route.ts": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\member\\gallery\\[id]\\route.ts": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\route.ts": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\[id]\\account-statement\\route.ts": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\[id]\\password\\route.ts": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\[id]\\quick-stats\\route.ts": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\[id]\\route.ts": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\notifications\\route.ts": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\reports\\route.ts": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\settings\\reset\\route.ts": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\settings\\route.ts": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\upload\\route.ts": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\auth\\clear-session\\page.tsx": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\auth\\signin\\page.tsx": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\expenses\\page.tsx": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\gallery\\folder\\[id]\\page.tsx": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\gallery\\page.tsx": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\incomes\\page.tsx": "39", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\layout.tsx": "40", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\members\\page.tsx": "41", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\members-simple\\page.tsx": "42", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\notifications\\page.tsx": "43", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\page.tsx": "44", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\reports\\page.tsx": "45", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\reports-advanced\\page.tsx": "46", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\settings\\page.tsx": "47", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\layout.tsx": "48", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\account-statement\\page.tsx": "49", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\dashboard\\page.tsx": "50", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\gallery\\page.tsx": "51", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\layout.tsx": "52", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\login\\page.tsx": "53", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\signin\\page.tsx": "54", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\page.tsx": "55", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\user-type-selection.tsx": "56", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\auth\\user-type-selection.tsx": "57", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\debug\\session-monitor.tsx": "58", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\error-boundary.tsx": "59", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\expenses\\expense-dialog.tsx": "60", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\gallery\\create-activity-dialog.tsx": "61", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\gallery\\edit-activity-dialog.tsx": "62", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\gallery\\folder-view.tsx": "63", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\gallery\\upload-photo-dialog.tsx": "64", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\incomes\\income-dialog.tsx": "65", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\layout\\dynamic-head.tsx": "66", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\layout\\header.tsx": "67", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\layout\\sidebar.tsx": "68", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\member\\member-account-statement.tsx": "69", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\member\\member-header.tsx": "70", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\member\\member-layout.tsx": "71", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\member\\member-sidebar.tsx": "72", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\account-statement-dialog.tsx": "73", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\advanced-search.tsx": "74", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-details-dialog.tsx": "75", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-dialog.tsx": "76", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-income-dialog.tsx": "77", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-password-dialog.tsx": "78", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-quick-stats.tsx": "79", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-search-dialog.tsx": "80", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\providers\\member-auth-provider.tsx": "81", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\providers\\session-provider.tsx": "82", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\ComprehensiveReports.tsx": "83", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\ExpensesReports.tsx": "84", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\HelperReports.tsx": "85", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\IncomesReports.tsx": "86", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\MembersReports.tsx": "87", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\ReportExporter.tsx": "88", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\advanced-settings.tsx": "89", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\appearance-settings.tsx": "90", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\backup-settings.tsx": "91", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\detailed-permissions.tsx": "92", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\general-settings.tsx": "93", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\notification-settings.tsx": "94", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\security-settings.tsx": "95", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\settings-provider.tsx": "96", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\alert.tsx": "97", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\badge.tsx": "98", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\button.tsx": "99", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\card.tsx": "100", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\dialog.tsx": "101", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\dropdown-menu.tsx": "102", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\enhanced-select.tsx": "103", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\enhanced-table.tsx": "104", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\image-upload.tsx": "105", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\input.tsx": "106", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\label.tsx": "107", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\loading.tsx": "108", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\native-select.tsx": "109", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\page-header.tsx": "110", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\progress.tsx": "111", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\select.tsx": "112", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\sonner.tsx": "113", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\switch.tsx": "114", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\table.tsx": "115", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\tabs.tsx": "116", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\textarea.tsx": "117", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\toast.tsx": "118", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\hooks\\use-appearance-settings.ts": "119", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\hooks\\use-member-auth.ts": "120", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\hooks\\use-member-permissions.ts": "121", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\auth-middleware.ts": "122", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\auth.ts": "123", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\member-auth.ts": "124", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\permissions.ts": "125", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\prisma.ts": "126", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\utils.ts": "127", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\validations.ts": "128", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\middleware.ts": "129", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\types\\next-auth.d.ts": "130"}, {"size": 4334, "mtime": 1750563987665, "results": "131", "hashOfConfig": "132"}, {"size": 6245, "mtime": 1750564004020, "results": "133", "hashOfConfig": "132"}, {"size": 3872, "mtime": 1750563525595, "results": "134", "hashOfConfig": "132"}, {"size": 3820, "mtime": 1750514867537, "results": "135", "hashOfConfig": "132"}, {"size": 7577, "mtime": 1750564024945, "results": "136", "hashOfConfig": "132"}, {"size": 1576, "mtime": 1750564803239, "results": "137", "hashOfConfig": "132"}, {"size": 2587, "mtime": 1750564069542, "results": "138", "hashOfConfig": "132"}, {"size": 2609, "mtime": 1750525358269, "results": "139", "hashOfConfig": "132"}, {"size": 685, "mtime": 1750564816468, "results": "140", "hashOfConfig": "132"}, {"size": 157, "mtime": 1750364801281, "results": "141", "hashOfConfig": "132"}, {"size": 3860, "mtime": 1750564115048, "results": "142", "hashOfConfig": "132"}, {"size": 4514, "mtime": 1750564132785, "results": "143", "hashOfConfig": "132"}, {"size": 8392, "mtime": 1750564188777, "results": "144", "hashOfConfig": "132"}, {"size": 8404, "mtime": 1750564218120, "results": "145", "hashOfConfig": "132"}, {"size": 5066, "mtime": 1750454694226, "results": "146", "hashOfConfig": "132"}, {"size": 4504, "mtime": 1750564249572, "results": "147", "hashOfConfig": "132"}, {"size": 5788, "mtime": 1750564269167, "results": "148", "hashOfConfig": "132"}, {"size": 6556, "mtime": 1750564310657, "results": "149", "hashOfConfig": "132"}, {"size": 6751, "mtime": 1750564354109, "results": "150", "hashOfConfig": "132"}, {"size": 4983, "mtime": 1750564385844, "results": "151", "hashOfConfig": "132"}, {"size": 4942, "mtime": 1750564464120, "results": "152", "hashOfConfig": "132"}, {"size": 3219, "mtime": 1750564493457, "results": "153", "hashOfConfig": "132"}, {"size": 2931, "mtime": 1750564521366, "results": "154", "hashOfConfig": "132"}, {"size": 4198, "mtime": 1750564562824, "results": "155", "hashOfConfig": "132"}, {"size": 4784, "mtime": 1750564583315, "results": "156", "hashOfConfig": "132"}, {"size": 6404, "mtime": 1750564612850, "results": "157", "hashOfConfig": "132"}, {"size": 1695, "mtime": 1750367238374, "results": "158", "hashOfConfig": "132"}, {"size": 5670, "mtime": 1750564631728, "results": "159", "hashOfConfig": "132"}, {"size": 8050, "mtime": 1750461947709, "results": "160", "hashOfConfig": "132"}, {"size": 7559, "mtime": 1750463417752, "results": "161", "hashOfConfig": "132"}, {"size": 7741, "mtime": 1750564827281, "results": "162", "hashOfConfig": "132"}, {"size": 8942, "mtime": 1750498678247, "results": "163", "hashOfConfig": "132"}, {"size": 4637, "mtime": 1750564705418, "results": "164", "hashOfConfig": "132"}, {"size": 1927, "mtime": 1750528320562, "results": "165", "hashOfConfig": "132"}, {"size": 5200, "mtime": 1750564734440, "results": "166", "hashOfConfig": "132"}, {"size": 31201, "mtime": 1750564901443, "results": "167", "hashOfConfig": "132"}, {"size": 15872, "mtime": 1750564977971, "results": "168", "hashOfConfig": "132"}, {"size": 32403, "mtime": 1750565117046, "results": "169", "hashOfConfig": "132"}, {"size": 39386, "mtime": 1750565316675, "results": "170", "hashOfConfig": "132"}, {"size": 980, "mtime": 1750512522256, "results": "171", "hashOfConfig": "132"}, {"size": 43223, "mtime": 1750565433928, "results": "172", "hashOfConfig": "132"}, {"size": 2768, "mtime": 1750417848156, "results": "173", "hashOfConfig": "132"}, {"size": 32571, "mtime": 1750565469367, "results": "174", "hashOfConfig": "132"}, {"size": 15508, "mtime": 1750565493965, "results": "175", "hashOfConfig": "132"}, {"size": 55051, "mtime": 1750565515832, "results": "176", "hashOfConfig": "132"}, {"size": 12094, "mtime": 1750565553252, "results": "177", "hashOfConfig": "132"}, {"size": 12661, "mtime": 1750563697486, "results": "178", "hashOfConfig": "132"}, {"size": 1703, "mtime": 1750537782831, "results": "179", "hashOfConfig": "132"}, {"size": 12546, "mtime": 1750560430970, "results": "180", "hashOfConfig": "132"}, {"size": 9897, "mtime": 1750560268774, "results": "181", "hashOfConfig": "132"}, {"size": 13070, "mtime": 1750563720424, "results": "182", "hashOfConfig": "132"}, {"size": 756, "mtime": 1750535927466, "results": "183", "hashOfConfig": "132"}, {"size": 7829, "mtime": 1750529500439, "results": "184", "hashOfConfig": "132"}, {"size": 9540, "mtime": 1750537474120, "results": "185", "hashOfConfig": "132"}, {"size": 426, "mtime": 1750535176147, "results": "186", "hashOfConfig": "132"}, {"size": 6959, "mtime": 1750535212104, "results": "187", "hashOfConfig": "132"}, {"size": 6955, "mtime": 1750535019985, "results": "188", "hashOfConfig": "132"}, {"size": 3360, "mtime": 1750534552956, "results": "189", "hashOfConfig": "132"}, {"size": 4843, "mtime": 1750537753963, "results": "190", "hashOfConfig": "132"}, {"size": 13215, "mtime": 1750545596091, "results": "191", "hashOfConfig": "132"}, {"size": 14598, "mtime": 1750545633364, "results": "192", "hashOfConfig": "132"}, {"size": 7285, "mtime": 1750456802345, "results": "193", "hashOfConfig": "132"}, {"size": 11029, "mtime": 1750459852834, "results": "194", "hashOfConfig": "132"}, {"size": 21956, "mtime": 1750545615948, "results": "195", "hashOfConfig": "132"}, {"size": 26608, "mtime": 1750545578137, "results": "196", "hashOfConfig": "132"}, {"size": 2790, "mtime": 1750503410476, "results": "197", "hashOfConfig": "132"}, {"size": 3471, "mtime": 1750560409726, "results": "198", "hashOfConfig": "132"}, {"size": 4853, "mtime": 1750526526466, "results": "199", "hashOfConfig": "132"}, {"size": 13918, "mtime": 1750525527428, "results": "200", "hashOfConfig": "132"}, {"size": 3043, "mtime": 1750504145352, "results": "201", "hashOfConfig": "132"}, {"size": 2285, "mtime": 1750534574945, "results": "202", "hashOfConfig": "132"}, {"size": 4155, "mtime": 1750517232258, "results": "203", "hashOfConfig": "132"}, {"size": 30090, "mtime": 1750563773461, "results": "204", "hashOfConfig": "132"}, {"size": 6874, "mtime": 1750545784472, "results": "205", "hashOfConfig": "132"}, {"size": 9766, "mtime": 1750545672032, "results": "206", "hashOfConfig": "132"}, {"size": 28005, "mtime": 1750563798273, "results": "207", "hashOfConfig": "132"}, {"size": 8149, "mtime": 1750545704465, "results": "208", "hashOfConfig": "132"}, {"size": 8825, "mtime": 1750545725925, "results": "209", "hashOfConfig": "132"}, {"size": 2045, "mtime": 1750367044769, "results": "210", "hashOfConfig": "132"}, {"size": 7285, "mtime": 1750545652058, "results": "211", "hashOfConfig": "132"}, {"size": 449, "mtime": 1750525654535, "results": "212", "hashOfConfig": "132"}, {"size": 418, "mtime": 1750365176912, "results": "213", "hashOfConfig": "132"}, {"size": 20241, "mtime": 1750496391145, "results": "214", "hashOfConfig": "132"}, {"size": 19901, "mtime": 1750495022531, "results": "215", "hashOfConfig": "132"}, {"size": 25176, "mtime": 1750497581121, "results": "216", "hashOfConfig": "132"}, {"size": 19441, "mtime": 1750494551914, "results": "217", "hashOfConfig": "132"}, {"size": 19553, "mtime": 1750495698804, "results": "218", "hashOfConfig": "132"}, {"size": 23974, "mtime": 1750563811142, "results": "219", "hashOfConfig": "132"}, {"size": 12462, "mtime": 1750545804148, "results": "220", "hashOfConfig": "132"}, {"size": 34625, "mtime": 1750503640897, "results": "221", "hashOfConfig": "132"}, {"size": 27101, "mtime": 1750501162044, "results": "222", "hashOfConfig": "132"}, {"size": 16650, "mtime": 1750528626328, "results": "223", "hashOfConfig": "132"}, {"size": 17371, "mtime": 1750500017924, "results": "224", "hashOfConfig": "132"}, {"size": 22261, "mtime": 1750501420976, "results": "225", "hashOfConfig": "132"}, {"size": 57790, "mtime": 1750528807124, "results": "226", "hashOfConfig": "132"}, {"size": 4105, "mtime": 1750499697958, "results": "227", "hashOfConfig": "132"}, {"size": 1584, "mtime": 1750504904602, "results": "228", "hashOfConfig": "132"}, {"size": 1319, "mtime": 1750514106290, "results": "229", "hashOfConfig": "132"}, {"size": 2854, "mtime": 1750507525753, "results": "230", "hashOfConfig": "132"}, {"size": 2191, "mtime": 1750507570716, "results": "231", "hashOfConfig": "132"}, {"size": 4247, "mtime": 1750545333803, "results": "232", "hashOfConfig": "132"}, {"size": 7309, "mtime": 1750525874636, "results": "233", "hashOfConfig": "132"}, {"size": 8584, "mtime": 1750506901281, "results": "234", "hashOfConfig": "132"}, {"size": 6294, "mtime": 1750506495437, "results": "235", "hashOfConfig": "132"}, {"size": 7094, "mtime": 1750420043477, "results": "236", "hashOfConfig": "132"}, {"size": 903, "mtime": 1750507589343, "results": "237", "hashOfConfig": "132"}, {"size": 504, "mtime": 1750365758266, "results": "238", "hashOfConfig": "132"}, {"size": 3574, "mtime": 1750506436097, "results": "239", "hashOfConfig": "132"}, {"size": 6066, "mtime": 1750507694420, "results": "240", "hashOfConfig": "132"}, {"size": 7025, "mtime": 1750514092463, "results": "241", "hashOfConfig": "132"}, {"size": 791, "mtime": 1750498781188, "results": "242", "hashOfConfig": "132"}, {"size": 5988, "mtime": 1750507661593, "results": "243", "hashOfConfig": "132"}, {"size": 787, "mtime": 1750499406241, "results": "244", "hashOfConfig": "132"}, {"size": 1295, "mtime": 1750563835136, "results": "245", "hashOfConfig": "132"}, {"size": 2438, "mtime": 1750365583178, "results": "246", "hashOfConfig": "132"}, {"size": 1897, "mtime": 1750367770665, "results": "247", "hashOfConfig": "132"}, {"size": 771, "mtime": 1750365769466, "results": "248", "hashOfConfig": "132"}, {"size": 4088, "mtime": 1750506460673, "results": "249", "hashOfConfig": "132"}, {"size": 2216, "mtime": 1750502969456, "results": "250", "hashOfConfig": "132"}, {"size": 5917, "mtime": 1750560394465, "results": "251", "hashOfConfig": "132"}, {"size": 666, "mtime": 1750525448701, "results": "252", "hashOfConfig": "132"}, {"size": 7965, "mtime": 1750563761336, "results": "253", "hashOfConfig": "132"}, {"size": 1563, "mtime": 1750364783570, "results": "254", "hashOfConfig": "132"}, {"size": 3100, "mtime": 1750534501490, "results": "255", "hashOfConfig": "132"}, {"size": 8120, "mtime": 1750528852597, "results": "256", "hashOfConfig": "132"}, {"size": 279, "mtime": 1750364728882, "results": "257", "hashOfConfig": "132"}, {"size": 4208, "mtime": 1750495803718, "results": "258", "hashOfConfig": "132"}, {"size": 3218, "mtime": 1750528529700, "results": "259", "hashOfConfig": "132"}, {"size": 1188, "mtime": 1750533224946, "results": "260", "hashOfConfig": "132"}, {"size": 350, "mtime": 1750364792766, "results": "261", "hashOfConfig": "132"}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "kfs9f6", {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 19, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 10, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 44, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 15, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 24, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 15, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\activities\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\activities\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\admin\\user-permissions\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\admin\\users\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\admin\\users\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\clear-session\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\member\\session\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\member\\signin\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\member\\signout\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\expenses\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\expenses\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\export\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery-folders\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery-folders\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\incomes\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\incomes\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\member\\account-statement\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\member\\auth\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\member\\gallery\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\member\\gallery\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\[id]\\account-statement\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\[id]\\password\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\[id]\\quick-stats\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\notifications\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\reports\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\settings\\reset\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\settings\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\upload\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\auth\\clear-session\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\auth\\signin\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\expenses\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\gallery\\folder\\[id]\\page.tsx", ["652", "653", "654"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\gallery\\page.tsx", ["655", "656", "657"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\incomes\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\members\\page.tsx", ["658"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\members-simple\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\notifications\\page.tsx", ["659"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\reports\\page.tsx", ["660"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\reports-advanced\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\settings\\page.tsx", ["661", "662", "663", "664", "665", "666", "667", "668", "669", "670", "671", "672", "673", "674", "675", "676", "677", "678", "679"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\account-statement\\page.tsx", ["680", "681", "682", "683"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\dashboard\\page.tsx", ["684", "685", "686"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\gallery\\page.tsx", ["687"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\member\\signin\\page.tsx", ["688", "689"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\user-type-selection.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\auth\\user-type-selection.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\debug\\session-monitor.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\error-boundary.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\expenses\\expense-dialog.tsx", ["690", "691", "692", "693"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\gallery\\create-activity-dialog.tsx", ["694"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\gallery\\edit-activity-dialog.tsx", ["695"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\gallery\\folder-view.tsx", ["696", "697", "698"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\gallery\\upload-photo-dialog.tsx", ["699", "700"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\incomes\\income-dialog.tsx", ["701", "702", "703", "704"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\layout\\dynamic-head.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\layout\\header.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\layout\\sidebar.tsx", ["705", "706"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\member\\member-account-statement.tsx", ["707", "708", "709", "710"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\member\\member-header.tsx", ["711", "712", "713", "714"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\member\\member-layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\member\\member-sidebar.tsx", ["715", "716", "717", "718", "719"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\account-statement-dialog.tsx", ["720", "721", "722", "723", "724"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\advanced-search.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-details-dialog.tsx", ["725", "726", "727", "728", "729", "730"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-dialog.tsx", ["731", "732", "733", "734", "735", "736", "737", "738", "739"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-income-dialog.tsx", ["740", "741", "742"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-password-dialog.tsx", ["743"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-quick-stats.tsx", ["744"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\members\\member-search-dialog.tsx", ["745"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\providers\\member-auth-provider.tsx", ["746", "747"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\providers\\session-provider.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\ComprehensiveReports.tsx", ["748", "749", "750", "751", "752", "753", "754", "755", "756", "757"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\ExpensesReports.tsx", ["758", "759", "760", "761"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\HelperReports.tsx", ["762", "763", "764", "765", "766", "767"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\IncomesReports.tsx", ["768", "769", "770", "771"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\MembersReports.tsx", ["772", "773"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\reports\\ReportExporter.tsx", ["774", "775", "776", "777", "778", "779", "780", "781", "782", "783", "784", "785", "786", "787", "788", "789", "790", "791", "792", "793", "794", "795", "796", "797", "798", "799", "800", "801", "802", "803", "804", "805", "806", "807", "808", "809", "810", "811", "812", "813", "814", "815", "816", "817"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\advanced-settings.tsx", ["818", "819", "820", "821", "822", "823", "824"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\appearance-settings.tsx", ["825", "826", "827", "828", "829", "830", "831", "832", "833"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\backup-settings.tsx", ["834", "835", "836", "837", "838", "839", "840"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\detailed-permissions.tsx", ["841", "842", "843", "844"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\general-settings.tsx", ["845", "846", "847", "848", "849", "850", "851", "852", "853", "854", "855", "856", "857", "858", "859"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\notification-settings.tsx", ["860", "861", "862", "863", "864", "865", "866", "867", "868", "869", "870", "871", "872", "873", "874", "875", "876", "877", "878", "879", "880", "881", "882", "883"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\security-settings.tsx", ["884", "885", "886", "887", "888", "889", "890", "891", "892", "893", "894", "895", "896", "897", "898"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\settings\\settings-provider.tsx", ["899", "900", "901", "902", "903", "904"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\alert.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\button.tsx", ["905"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\enhanced-select.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\enhanced-table.tsx", ["906", "907"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\image-upload.tsx", ["908", "909"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\input.tsx", ["910"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\label.tsx", ["911"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\loading.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\native-select.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\page-header.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\sonner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\switch.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\table.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\textarea.tsx", ["912"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\components\\ui\\toast.tsx", ["913"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\hooks\\use-appearance-settings.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\hooks\\use-member-auth.ts", ["914", "915", "916"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\hooks\\use-member-permissions.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\auth-middleware.ts", ["917", "918"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\member-auth.ts", ["919", "920", "921"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\permissions.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\prisma.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\lib\\validations.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\middleware.ts", ["922", "923", "924"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\types\\next-auth.d.ts", ["925"], [], {"ruleId": "926", "severity": 1, "message": "927", "line": 181, "column": 6, "nodeType": "928", "endLine": 181, "endColumn": 45, "suggestions": "929"}, {"ruleId": "930", "severity": 1, "message": "931", "line": 349, "column": 21, "nodeType": "932", "endLine": 354, "endColumn": 23}, {"ruleId": "930", "severity": 1, "message": "931", "line": 422, "column": 17, "nodeType": "932", "endLine": 426, "endColumn": 19}, {"ruleId": "926", "severity": 1, "message": "933", "line": 141, "column": 6, "nodeType": "928", "endLine": 141, "endColumn": 51, "suggestions": "934"}, {"ruleId": "930", "severity": 1, "message": "931", "line": 539, "column": 25, "nodeType": "932", "endLine": 544, "endColumn": 27}, {"ruleId": "930", "severity": 1, "message": "931", "line": 695, "column": 17, "nodeType": "932", "endLine": 699, "endColumn": 19}, {"ruleId": "926", "severity": 1, "message": "935", "line": 156, "column": 6, "nodeType": "928", "endLine": 156, "endColumn": 39, "suggestions": "936"}, {"ruleId": "926", "severity": 1, "message": "937", "line": 78, "column": 6, "nodeType": "928", "endLine": 78, "endColumn": 49, "suggestions": "938"}, {"ruleId": "926", "severity": 1, "message": "939", "line": 96, "column": 6, "nodeType": "928", "endLine": 96, "endColumn": 31, "suggestions": "940"}, {"ruleId": "941", "severity": 2, "message": "942", "line": 5, "column": 10, "nodeType": null, "messageId": "943", "endLine": 5, "endColumn": 14}, {"ruleId": "941", "severity": 2, "message": "944", "line": 5, "column": 16, "nodeType": null, "messageId": "943", "endLine": 5, "endColumn": 27}, {"ruleId": "941", "severity": 2, "message": "945", "line": 5, "column": 29, "nodeType": null, "messageId": "943", "endLine": 5, "endColumn": 39}, {"ruleId": "941", "severity": 2, "message": "946", "line": 5, "column": 41, "nodeType": null, "messageId": "943", "endLine": 5, "endColumn": 50}, {"ruleId": "941", "severity": 2, "message": "947", "line": 15, "column": 3, "nodeType": null, "messageId": "943", "endLine": 15, "endColumn": 11}, {"ruleId": "941", "severity": 2, "message": "948", "line": 16, "column": 3, "nodeType": null, "messageId": "943", "endLine": 16, "endColumn": 9}, {"ruleId": "941", "severity": 2, "message": "949", "line": 20, "column": 3, "nodeType": null, "messageId": "943", "endLine": 20, "endColumn": 14}, {"ruleId": "950", "severity": 2, "message": "951", "line": 34, "column": 12, "nodeType": "952", "messageId": "953", "endLine": 34, "endColumn": 15, "suggestions": "954"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 35, "column": 15, "nodeType": "952", "messageId": "953", "endLine": 35, "endColumn": 18, "suggestions": "955"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 36, "column": 18, "nodeType": "952", "messageId": "953", "endLine": 36, "endColumn": 21, "suggestions": "956"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 37, "column": 13, "nodeType": "952", "messageId": "953", "endLine": 37, "endColumn": 16, "suggestions": "957"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 38, "column": 11, "nodeType": "952", "messageId": "953", "endLine": 38, "endColumn": 14, "suggestions": "958"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 39, "column": 13, "nodeType": "952", "messageId": "953", "endLine": 39, "endColumn": 16, "suggestions": "959"}, {"ruleId": "941", "severity": 2, "message": "960", "line": 59, "column": 9, "nodeType": null, "messageId": "943", "endLine": 59, "endColumn": 24}, {"ruleId": "941", "severity": 2, "message": "961", "line": 60, "column": 9, "nodeType": null, "messageId": "943", "endLine": 60, "endColumn": 22}, {"ruleId": "950", "severity": 2, "message": "951", "line": 141, "column": 76, "nodeType": "952", "messageId": "953", "endLine": 141, "endColumn": 79, "suggestions": "962"}, {"ruleId": "941", "severity": 2, "message": "963", "line": 168, "column": 16, "nodeType": null, "messageId": "943", "endLine": 168, "endColumn": 21}, {"ruleId": "950", "severity": 2, "message": "951", "line": 347, "column": 49, "nodeType": "952", "messageId": "953", "endLine": 347, "endColumn": 52, "suggestions": "964"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 356, "column": 49, "nodeType": "952", "messageId": "953", "endLine": 356, "endColumn": 52, "suggestions": "965"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 28, "column": 11, "nodeType": "952", "messageId": "953", "endLine": 28, "endColumn": 14, "suggestions": "966"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 36, "column": 20, "nodeType": "952", "messageId": "953", "endLine": 36, "endColumn": 23, "suggestions": "967"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 37, "column": 16, "nodeType": "952", "messageId": "953", "endLine": 37, "endColumn": 19, "suggestions": "968"}, {"ruleId": "926", "severity": 1, "message": "969", "line": 49, "column": 6, "nodeType": "928", "endLine": 49, "endColumn": 8, "suggestions": "970"}, {"ruleId": "941", "severity": 2, "message": "971", "line": 3, "column": 10, "nodeType": null, "messageId": "943", "endLine": 3, "endColumn": 18}, {"ruleId": "941", "severity": 2, "message": "972", "line": 7, "column": 10, "nodeType": null, "messageId": "943", "endLine": 7, "endColumn": 15}, {"ruleId": "941", "severity": 2, "message": "973", "line": 22, "column": 11, "nodeType": null, "messageId": "943", "endLine": 22, "endColumn": 17}, {"ruleId": "926", "severity": 1, "message": "969", "line": 64, "column": 6, "nodeType": "928", "endLine": 64, "endColumn": 8, "suggestions": "974"}, {"ruleId": "941", "severity": 2, "message": "963", "line": 38, "column": 14, "nodeType": null, "messageId": "943", "endLine": 38, "endColumn": 19}, {"ruleId": "941", "severity": 2, "message": "963", "line": 52, "column": 14, "nodeType": null, "messageId": "943", "endLine": 52, "endColumn": 19}, {"ruleId": "950", "severity": 2, "message": "951", "line": 90, "column": 41, "nodeType": "952", "messageId": "953", "endLine": 90, "endColumn": 44, "suggestions": "975"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 142, "column": 21, "nodeType": "952", "messageId": "953", "endLine": 142, "endColumn": 24, "suggestions": "976"}, {"ruleId": "941", "severity": 2, "message": "977", "line": 150, "column": 9, "nodeType": null, "messageId": "943", "endLine": 150, "endColumn": 24}, {"ruleId": "950", "severity": 2, "message": "951", "line": 272, "column": 108, "nodeType": "952", "messageId": "953", "endLine": 272, "endColumn": 111, "suggestions": "978"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 91, "column": 21, "nodeType": "952", "messageId": "953", "endLine": 91, "endColumn": 24, "suggestions": "979"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 99, "column": 21, "nodeType": "952", "messageId": "953", "endLine": 99, "endColumn": 24, "suggestions": "980"}, {"ruleId": "941", "severity": 2, "message": "971", "line": 3, "column": 10, "nodeType": null, "messageId": "943", "endLine": 3, "endColumn": 18}, {"ruleId": "941", "severity": 2, "message": "981", "line": 16, "column": 3, "nodeType": null, "messageId": "943", "endLine": 16, "endColumn": 15}, {"ruleId": "930", "severity": 1, "message": "931", "line": 129, "column": 17, "nodeType": "932", "endLine": 133, "endColumn": 19}, {"ruleId": "950", "severity": 2, "message": "951", "line": 227, "column": 21, "nodeType": "952", "messageId": "953", "endLine": 227, "endColumn": 24, "suggestions": "982"}, {"ruleId": "930", "severity": 1, "message": "931", "line": 297, "column": 21, "nodeType": "932", "endLine": 301, "endColumn": 23}, {"ruleId": "950", "severity": 2, "message": "951", "line": 121, "column": 32, "nodeType": "952", "messageId": "953", "endLine": 121, "endColumn": 35, "suggestions": "983"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 236, "column": 21, "nodeType": "952", "messageId": "953", "endLine": 236, "endColumn": 24, "suggestions": "984"}, {"ruleId": "941", "severity": 2, "message": "985", "line": 244, "column": 9, "nodeType": null, "messageId": "943", "endLine": 244, "endColumn": 26}, {"ruleId": "950", "severity": 2, "message": "951", "line": 378, "column": 73, "nodeType": "952", "messageId": "953", "endLine": 378, "endColumn": 76, "suggestions": "986"}, {"ruleId": "941", "severity": 2, "message": "987", "line": 17, "column": 3, "nodeType": null, "messageId": "943", "endLine": 17, "endColumn": 12}, {"ruleId": "930", "severity": 1, "message": "931", "line": 43, "column": 15, "nodeType": "932", "endLine": 47, "endColumn": 17}, {"ruleId": "941", "severity": 2, "message": "988", "line": 25, "column": 3, "nodeType": null, "messageId": "943", "endLine": 25, "endColumn": 13}, {"ruleId": "941", "severity": 2, "message": "989", "line": 27, "column": 3, "nodeType": null, "messageId": "943", "endLine": 27, "endColumn": 8}, {"ruleId": "941", "severity": 2, "message": "949", "line": 28, "column": 3, "nodeType": null, "messageId": "943", "endLine": 28, "endColumn": 14}, {"ruleId": "950", "severity": 2, "message": "951", "line": 102, "column": 21, "nodeType": "952", "messageId": "953", "endLine": 102, "endColumn": 24, "suggestions": "990"}, {"ruleId": "941", "severity": 2, "message": "971", "line": 3, "column": 10, "nodeType": null, "messageId": "943", "endLine": 3, "endColumn": 18}, {"ruleId": "941", "severity": 2, "message": "991", "line": 4, "column": 16, "nodeType": null, "messageId": "943", "endLine": 4, "endColumn": 17}, {"ruleId": "950", "severity": 2, "message": "951", "line": 15, "column": 9, "nodeType": "952", "messageId": "953", "endLine": 15, "endColumn": 12, "suggestions": "992"}, {"ruleId": "930", "severity": 1, "message": "931", "line": 57, "column": 19, "nodeType": "932", "endLine": 61, "endColumn": 21}, {"ruleId": "941", "severity": 2, "message": "993", "line": 6, "column": 10, "nodeType": null, "messageId": "943", "endLine": 6, "endColumn": 23}, {"ruleId": "941", "severity": 2, "message": "988", "line": 14, "column": 3, "nodeType": null, "messageId": "943", "endLine": 14, "endColumn": 13}, {"ruleId": "950", "severity": 2, "message": "951", "line": 25, "column": 9, "nodeType": "952", "messageId": "953", "endLine": 25, "endColumn": 12, "suggestions": "994"}, {"ruleId": "930", "severity": 1, "message": "931", "line": 50, "column": 13, "nodeType": "932", "endLine": 54, "endColumn": 15}, {"ruleId": "950", "severity": 2, "message": "951", "line": 74, "column": 70, "nodeType": "952", "messageId": "953", "endLine": 74, "endColumn": 73, "suggestions": "995"}, {"ruleId": "941", "severity": 2, "message": "996", "line": 37, "column": 3, "nodeType": null, "messageId": "943", "endLine": 37, "endColumn": 9}, {"ruleId": "941", "severity": 2, "message": "949", "line": 41, "column": 3, "nodeType": null, "messageId": "943", "endLine": 41, "endColumn": 14}, {"ruleId": "941", "severity": 2, "message": "997", "line": 45, "column": 8, "nodeType": null, "messageId": "943", "endLine": 45, "endColumn": 13}, {"ruleId": "926", "severity": 1, "message": "998", "line": 118, "column": 6, "nodeType": "928", "endLine": 118, "endColumn": 36, "suggestions": "999"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 141, "column": 55, "nodeType": "952", "messageId": "953", "endLine": 141, "endColumn": 58, "suggestions": "1000"}, {"ruleId": "941", "severity": 2, "message": "972", "line": 11, "column": 10, "nodeType": null, "messageId": "943", "endLine": 11, "endColumn": 15}, {"ruleId": "941", "severity": 2, "message": "1001", "line": 16, "column": 3, "nodeType": null, "messageId": "943", "endLine": 16, "endColumn": 7}, {"ruleId": "941", "severity": 2, "message": "1002", "line": 19, "column": 3, "nodeType": null, "messageId": "943", "endLine": 19, "endColumn": 13}, {"ruleId": "926", "severity": 1, "message": "1003", "line": 74, "column": 6, "nodeType": "928", "endLine": 74, "endColumn": 20, "suggestions": "1004"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 87, "column": 77, "nodeType": "952", "messageId": "953", "endLine": 87, "endColumn": 80, "suggestions": "1005"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 92, "column": 56, "nodeType": "952", "messageId": "953", "endLine": 92, "endColumn": 59, "suggestions": "1006"}, {"ruleId": "941", "severity": 2, "message": "1007", "line": 11, "column": 3, "nodeType": null, "messageId": "943", "endLine": 11, "endColumn": 15}, {"ruleId": "941", "severity": 2, "message": "1008", "line": 14, "column": 3, "nodeType": null, "messageId": "943", "endLine": 14, "endColumn": 14}, {"ruleId": "941", "severity": 2, "message": "1009", "line": 20, "column": 10, "nodeType": null, "messageId": "943", "endLine": 20, "endColumn": 16}, {"ruleId": "941", "severity": 2, "message": "1010", "line": 30, "column": 3, "nodeType": null, "messageId": "943", "endLine": 30, "endColumn": 11}, {"ruleId": "941", "severity": 2, "message": "948", "line": 40, "column": 3, "nodeType": null, "messageId": "943", "endLine": 40, "endColumn": 9}, {"ruleId": "950", "severity": 2, "message": "951", "line": 140, "column": 43, "nodeType": "952", "messageId": "953", "endLine": 140, "endColumn": 46, "suggestions": "1011"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 192, "column": 21, "nodeType": "952", "messageId": "953", "endLine": 192, "endColumn": 24, "suggestions": "1012"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 257, "column": 21, "nodeType": "952", "messageId": "953", "endLine": 257, "endColumn": 24, "suggestions": "1013"}, {"ruleId": "930", "severity": 1, "message": "931", "line": 327, "column": 27, "nodeType": "932", "endLine": 331, "endColumn": 29}, {"ruleId": "950", "severity": 2, "message": "951", "line": 123, "column": 21, "nodeType": "952", "messageId": "953", "endLine": 123, "endColumn": 24, "suggestions": "1014"}, {"ruleId": "941", "severity": 2, "message": "985", "line": 131, "column": 9, "nodeType": null, "messageId": "943", "endLine": 131, "endColumn": 26}, {"ruleId": "950", "severity": 2, "message": "951", "line": 211, "column": 67, "nodeType": "952", "messageId": "953", "endLine": 211, "endColumn": 70, "suggestions": "1015"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 94, "column": 21, "nodeType": "952", "messageId": "953", "endLine": 94, "endColumn": 24, "suggestions": "1016"}, {"ruleId": "926", "severity": 1, "message": "1017", "line": 23, "column": 6, "nodeType": "928", "endLine": 23, "endColumn": 16, "suggestions": "1018"}, {"ruleId": "926", "severity": 1, "message": "935", "line": 64, "column": 6, "nodeType": "928", "endLine": 64, "endColumn": 24, "suggestions": "1019"}, {"ruleId": "941", "severity": 2, "message": "1020", "line": 3, "column": 10, "nodeType": null, "messageId": "943", "endLine": 3, "endColumn": 23}, {"ruleId": "941", "severity": 2, "message": "1021", "line": 3, "column": 25, "nodeType": null, "messageId": "943", "endLine": 3, "endColumn": 35}, {"ruleId": "941", "severity": 2, "message": "1022", "line": 6, "column": 10, "nodeType": null, "messageId": "943", "endLine": 6, "endColumn": 15}, {"ruleId": "941", "severity": 2, "message": "1023", "line": 7, "column": 10, "nodeType": null, "messageId": "943", "endLine": 7, "endColumn": 16}, {"ruleId": "941", "severity": 2, "message": "1024", "line": 7, "column": 18, "nodeType": null, "messageId": "943", "endLine": 7, "endColumn": 31}, {"ruleId": "941", "severity": 2, "message": "1025", "line": 7, "column": 33, "nodeType": null, "messageId": "943", "endLine": 7, "endColumn": 43}, {"ruleId": "941", "severity": 2, "message": "1026", "line": 7, "column": 45, "nodeType": null, "messageId": "943", "endLine": 7, "endColumn": 58}, {"ruleId": "941", "severity": 2, "message": "1027", "line": 7, "column": 60, "nodeType": null, "messageId": "943", "endLine": 7, "endColumn": 71}, {"ruleId": "950", "severity": 2, "message": "951", "line": 32, "column": 23, "nodeType": "952", "messageId": "953", "endLine": 32, "endColumn": 26, "suggestions": "1028"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 33, "column": 23, "nodeType": "952", "messageId": "953", "endLine": 33, "endColumn": 26, "suggestions": "1029"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 40, "column": 48, "nodeType": "952", "messageId": "953", "endLine": 40, "endColumn": 51, "suggestions": "1030"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 177, "column": 74, "nodeType": "952", "messageId": "953", "endLine": 177, "endColumn": 77, "suggestions": "1031"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 35, "column": 23, "nodeType": "952", "messageId": "953", "endLine": 35, "endColumn": 26, "suggestions": "1032"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 36, "column": 23, "nodeType": "952", "messageId": "953", "endLine": 36, "endColumn": 26, "suggestions": "1033"}, {"ruleId": "1034", "severity": 2, "message": "1035", "line": 169, "column": 9, "nodeType": "1036", "messageId": "1037", "endLine": 169, "endColumn": 24, "fix": "1038"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 169, "column": 21, "nodeType": "952", "messageId": "953", "endLine": 169, "endColumn": 24, "suggestions": "1039"}, {"ruleId": "941", "severity": 2, "message": "1001", "line": 17, "column": 3, "nodeType": null, "messageId": "943", "endLine": 17, "endColumn": 7}, {"ruleId": "941", "severity": 2, "message": "1010", "line": 18, "column": 3, "nodeType": null, "messageId": "943", "endLine": 18, "endColumn": 11}, {"ruleId": "950", "severity": 2, "message": "951", "line": 49, "column": 23, "nodeType": "952", "messageId": "953", "endLine": 49, "endColumn": 26, "suggestions": "1040"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 50, "column": 23, "nodeType": "952", "messageId": "953", "endLine": 50, "endColumn": 26, "suggestions": "1041"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 83, "column": 76, "nodeType": "952", "messageId": "953", "endLine": 83, "endColumn": 79, "suggestions": "1042"}, {"ruleId": "941", "severity": 2, "message": "1043", "line": 116, "column": 11, "nodeType": null, "messageId": "943", "endLine": 116, "endColumn": 26}, {"ruleId": "950", "severity": 2, "message": "951", "line": 39, "column": 23, "nodeType": "952", "messageId": "953", "endLine": 39, "endColumn": 26, "suggestions": "1044"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 40, "column": 23, "nodeType": "952", "messageId": "953", "endLine": 40, "endColumn": 26, "suggestions": "1045"}, {"ruleId": "1034", "severity": 2, "message": "1035", "line": 175, "column": 9, "nodeType": "1036", "messageId": "1037", "endLine": 175, "endColumn": 24, "fix": "1046"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 175, "column": 21, "nodeType": "952", "messageId": "953", "endLine": 175, "endColumn": 24, "suggestions": "1047"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 44, "column": 23, "nodeType": "952", "messageId": "953", "endLine": 44, "endColumn": 26, "suggestions": "1048"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 45, "column": 23, "nodeType": "952", "messageId": "953", "endLine": 45, "endColumn": 26, "suggestions": "1049"}, {"ruleId": "941", "severity": 2, "message": "1050", "line": 3, "column": 26, "nodeType": null, "messageId": "943", "endLine": 3, "endColumn": 36}, {"ruleId": "950", "severity": 2, "message": "951", "line": 6, "column": 41, "nodeType": "952", "messageId": "953", "endLine": 6, "endColumn": 44, "suggestions": "1051"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 8, "column": 63, "nodeType": "952", "messageId": "953", "endLine": 8, "endColumn": 66, "suggestions": "1052"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 82, "column": 35, "nodeType": "952", "messageId": "953", "endLine": 82, "endColumn": 38, "suggestions": "1053"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 284, "column": 40, "nodeType": "952", "messageId": "953", "endLine": 284, "endColumn": 43, "suggestions": "1054"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 301, "column": 45, "nodeType": "952", "messageId": "953", "endLine": 301, "endColumn": 48, "suggestions": "1055"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 319, "column": 41, "nodeType": "952", "messageId": "953", "endLine": 319, "endColumn": 44, "suggestions": "1056"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 355, "column": 57, "nodeType": "952", "messageId": "953", "endLine": 355, "endColumn": 60, "suggestions": "1057"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 371, "column": 39, "nodeType": "952", "messageId": "953", "endLine": 371, "endColumn": 42, "suggestions": "1058"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 373, "column": 42, "nodeType": "952", "messageId": "953", "endLine": 373, "endColumn": 45, "suggestions": "1059"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 386, "column": 40, "nodeType": "952", "messageId": "953", "endLine": 386, "endColumn": 43, "suggestions": "1060"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 396, "column": 46, "nodeType": "952", "messageId": "953", "endLine": 396, "endColumn": 49, "suggestions": "1061"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 408, "column": 44, "nodeType": "952", "messageId": "953", "endLine": 408, "endColumn": 47, "suggestions": "1062"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 446, "column": 45, "nodeType": "952", "messageId": "953", "endLine": 446, "endColumn": 48, "suggestions": "1063"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 463, "column": 45, "nodeType": "952", "messageId": "953", "endLine": 463, "endColumn": 48, "suggestions": "1064"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 501, "column": 47, "nodeType": "952", "messageId": "953", "endLine": 501, "endColumn": 50, "suggestions": "1065"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 518, "column": 40, "nodeType": "952", "messageId": "953", "endLine": 518, "endColumn": 43, "suggestions": "1066"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 557, "column": 49, "nodeType": "952", "messageId": "953", "endLine": 557, "endColumn": 52, "suggestions": "1067"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 576, "column": 43, "nodeType": "952", "messageId": "953", "endLine": 576, "endColumn": 46, "suggestions": "1068"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 578, "column": 42, "nodeType": "952", "messageId": "953", "endLine": 578, "endColumn": 45, "suggestions": "1069"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 598, "column": 44, "nodeType": "952", "messageId": "953", "endLine": 598, "endColumn": 47, "suggestions": "1070"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 600, "column": 44, "nodeType": "952", "messageId": "953", "endLine": 600, "endColumn": 47, "suggestions": "1071"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 620, "column": 39, "nodeType": "952", "messageId": "953", "endLine": 620, "endColumn": 42, "suggestions": "1072"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 622, "column": 46, "nodeType": "952", "messageId": "953", "endLine": 622, "endColumn": 49, "suggestions": "1073"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 644, "column": 44, "nodeType": "952", "messageId": "953", "endLine": 644, "endColumn": 47, "suggestions": "1074"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 648, "column": 43, "nodeType": "952", "messageId": "953", "endLine": 648, "endColumn": 46, "suggestions": "1075"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 652, "column": 43, "nodeType": "952", "messageId": "953", "endLine": 652, "endColumn": 46, "suggestions": "1076"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 679, "column": 42, "nodeType": "952", "messageId": "953", "endLine": 679, "endColumn": 45, "suggestions": "1077"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 691, "column": 42, "nodeType": "952", "messageId": "953", "endLine": 691, "endColumn": 45, "suggestions": "1078"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 692, "column": 44, "nodeType": "952", "messageId": "953", "endLine": 692, "endColumn": 47, "suggestions": "1079"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 693, "column": 44, "nodeType": "952", "messageId": "953", "endLine": 693, "endColumn": 47, "suggestions": "1080"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 694, "column": 47, "nodeType": "952", "messageId": "953", "endLine": 694, "endColumn": 50, "suggestions": "1081"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 695, "column": 48, "nodeType": "952", "messageId": "953", "endLine": 695, "endColumn": 51, "suggestions": "1082"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 696, "column": 43, "nodeType": "952", "messageId": "953", "endLine": 696, "endColumn": 46, "suggestions": "1083"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 697, "column": 45, "nodeType": "952", "messageId": "953", "endLine": 697, "endColumn": 48, "suggestions": "1084"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 698, "column": 44, "nodeType": "952", "messageId": "953", "endLine": 698, "endColumn": 47, "suggestions": "1085"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 700, "column": 41, "nodeType": "952", "messageId": "953", "endLine": 700, "endColumn": 44, "suggestions": "1086"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 701, "column": 43, "nodeType": "952", "messageId": "953", "endLine": 701, "endColumn": 46, "suggestions": "1087"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 702, "column": 43, "nodeType": "952", "messageId": "953", "endLine": 702, "endColumn": 46, "suggestions": "1088"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 703, "column": 46, "nodeType": "952", "messageId": "953", "endLine": 703, "endColumn": 49, "suggestions": "1089"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 704, "column": 47, "nodeType": "952", "messageId": "953", "endLine": 704, "endColumn": 50, "suggestions": "1090"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 705, "column": 42, "nodeType": "952", "messageId": "953", "endLine": 705, "endColumn": 45, "suggestions": "1091"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 706, "column": 44, "nodeType": "952", "messageId": "953", "endLine": 706, "endColumn": 47, "suggestions": "1092"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 707, "column": 43, "nodeType": "952", "messageId": "953", "endLine": 707, "endColumn": 46, "suggestions": "1093"}, {"ruleId": "941", "severity": 2, "message": "942", "line": 4, "column": 10, "nodeType": null, "messageId": "943", "endLine": 4, "endColumn": 14}, {"ruleId": "941", "severity": 2, "message": "944", "line": 4, "column": 16, "nodeType": null, "messageId": "943", "endLine": 4, "endColumn": 27}, {"ruleId": "941", "severity": 2, "message": "945", "line": 4, "column": 29, "nodeType": null, "messageId": "943", "endLine": 4, "endColumn": 39}, {"ruleId": "941", "severity": 2, "message": "946", "line": 4, "column": 41, "nodeType": null, "messageId": "943", "endLine": 4, "endColumn": 50}, {"ruleId": "941", "severity": 2, "message": "1094", "line": 8, "column": 10, "nodeType": null, "messageId": "943", "endLine": 8, "endColumn": 18}, {"ruleId": "941", "severity": 2, "message": "1095", "line": 25, "column": 3, "nodeType": null, "messageId": "943", "endLine": 25, "endColumn": 11}, {"ruleId": "941", "severity": 2, "message": "1096", "line": 46, "column": 21, "nodeType": null, "messageId": "943", "endLine": 46, "endColumn": 33}, {"ruleId": "941", "severity": 2, "message": "972", "line": 10, "column": 10, "nodeType": null, "messageId": "943", "endLine": 10, "endColumn": 15}, {"ruleId": "950", "severity": 2, "message": "951", "line": 27, "column": 13, "nodeType": "952", "messageId": "953", "endLine": 27, "endColumn": 16, "suggestions": "1097"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 28, "column": 24, "nodeType": "952", "messageId": "953", "endLine": 28, "endColumn": 27, "suggestions": "1098"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 162, "column": 67, "nodeType": "952", "messageId": "953", "endLine": 162, "endColumn": 70, "suggestions": "1099"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 175, "column": 51, "nodeType": "952", "messageId": "953", "endLine": 175, "endColumn": 54, "suggestions": "1100"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 197, "column": 50, "nodeType": "952", "messageId": "953", "endLine": 197, "endColumn": 53, "suggestions": "1101"}, {"ruleId": "1102", "severity": 1, "message": "1103", "line": 611, "column": 13, "nodeType": "932", "endLine": 611, "endColumn": 58}, {"ruleId": "930", "severity": 1, "message": "931", "line": 638, "column": 21, "nodeType": "932", "endLine": 642, "endColumn": 23}, {"ruleId": "930", "severity": 1, "message": "931", "line": 691, "column": 21, "nodeType": "932", "endLine": 695, "endColumn": 23}, {"ruleId": "950", "severity": 2, "message": "951", "line": 29, "column": 13, "nodeType": "952", "messageId": "953", "endLine": 29, "endColumn": 16, "suggestions": "1104"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 30, "column": 24, "nodeType": "952", "messageId": "953", "endLine": 30, "endColumn": 27, "suggestions": "1105"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 130, "column": 45, "nodeType": "952", "messageId": "953", "endLine": 130, "endColumn": 48, "suggestions": "1106"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 150, "column": 69, "nodeType": "952", "messageId": "953", "endLine": 150, "endColumn": 72, "suggestions": "1107"}, {"ruleId": "941", "severity": 2, "message": "963", "line": 187, "column": 14, "nodeType": null, "messageId": "943", "endLine": 187, "endColumn": 19}, {"ruleId": "941", "severity": 2, "message": "963", "line": 202, "column": 16, "nodeType": null, "messageId": "943", "endLine": 202, "endColumn": 21}, {"ruleId": "941", "severity": 2, "message": "963", "line": 217, "column": 14, "nodeType": null, "messageId": "943", "endLine": 217, "endColumn": 19}, {"ruleId": "941", "severity": 2, "message": "1108", "line": 10, "column": 17, "nodeType": null, "messageId": "943", "endLine": 10, "endColumn": 20}, {"ruleId": "941", "severity": 2, "message": "1109", "line": 10, "column": 22, "nodeType": null, "messageId": "943", "endLine": 10, "endColumn": 26}, {"ruleId": "941", "severity": 2, "message": "1110", "line": 10, "column": 28, "nodeType": null, "messageId": "943", "endLine": 10, "endColumn": 34}, {"ruleId": "941", "severity": 2, "message": "1111", "line": 10, "column": 44, "nodeType": null, "messageId": "943", "endLine": 10, "endColumn": 52}, {"ruleId": "941", "severity": 2, "message": "942", "line": 4, "column": 10, "nodeType": null, "messageId": "943", "endLine": 4, "endColumn": 14}, {"ruleId": "941", "severity": 2, "message": "944", "line": 4, "column": 16, "nodeType": null, "messageId": "943", "endLine": 4, "endColumn": 27}, {"ruleId": "941", "severity": 2, "message": "945", "line": 4, "column": 29, "nodeType": null, "messageId": "943", "endLine": 4, "endColumn": 39}, {"ruleId": "941", "severity": 2, "message": "946", "line": 4, "column": 41, "nodeType": null, "messageId": "943", "endLine": 4, "endColumn": 50}, {"ruleId": "941", "severity": 2, "message": "1112", "line": 10, "column": 10, "nodeType": null, "messageId": "943", "endLine": 10, "endColumn": 16}, {"ruleId": "941", "severity": 2, "message": "972", "line": 11, "column": 10, "nodeType": null, "messageId": "943", "endLine": 11, "endColumn": 15}, {"ruleId": "941", "severity": 2, "message": "1002", "line": 15, "column": 3, "nodeType": null, "messageId": "943", "endLine": 15, "endColumn": 13}, {"ruleId": "941", "severity": 2, "message": "989", "line": 16, "column": 3, "nodeType": null, "messageId": "943", "endLine": 16, "endColumn": 8}, {"ruleId": "941", "severity": 2, "message": "1113", "line": 17, "column": 3, "nodeType": null, "messageId": "943", "endLine": 17, "endColumn": 12}, {"ruleId": "941", "severity": 2, "message": "996", "line": 18, "column": 3, "nodeType": null, "messageId": "943", "endLine": 18, "endColumn": 9}, {"ruleId": "941", "severity": 2, "message": "1114", "line": 19, "column": 3, "nodeType": null, "messageId": "943", "endLine": 19, "endColumn": 8}, {"ruleId": "941", "severity": 2, "message": "1001", "line": 20, "column": 3, "nodeType": null, "messageId": "943", "endLine": 20, "endColumn": 7}, {"ruleId": "950", "severity": 2, "message": "951", "line": 26, "column": 13, "nodeType": "952", "messageId": "953", "endLine": 26, "endColumn": 16, "suggestions": "1115"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 27, "column": 24, "nodeType": "952", "messageId": "953", "endLine": 27, "endColumn": 27, "suggestions": "1116"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 120, "column": 64, "nodeType": "952", "messageId": "953", "endLine": 120, "endColumn": 67, "suggestions": "1117"}, {"ruleId": "941", "severity": 2, "message": "1023", "line": 7, "column": 10, "nodeType": null, "messageId": "943", "endLine": 7, "endColumn": 16}, {"ruleId": "941", "severity": 2, "message": "1024", "line": 7, "column": 18, "nodeType": null, "messageId": "943", "endLine": 7, "endColumn": 31}, {"ruleId": "941", "severity": 2, "message": "1025", "line": 7, "column": 33, "nodeType": null, "messageId": "943", "endLine": 7, "endColumn": 43}, {"ruleId": "941", "severity": 2, "message": "1026", "line": 7, "column": 45, "nodeType": null, "messageId": "943", "endLine": 7, "endColumn": 58}, {"ruleId": "941", "severity": 2, "message": "1027", "line": 7, "column": 60, "nodeType": null, "messageId": "943", "endLine": 7, "endColumn": 71}, {"ruleId": "941", "severity": 2, "message": "1112", "line": 9, "column": 10, "nodeType": null, "messageId": "943", "endLine": 9, "endColumn": 16}, {"ruleId": "941", "severity": 2, "message": "972", "line": 10, "column": 10, "nodeType": null, "messageId": "943", "endLine": 10, "endColumn": 15}, {"ruleId": "941", "severity": 2, "message": "1094", "line": 11, "column": 10, "nodeType": null, "messageId": "943", "endLine": 11, "endColumn": 18}, {"ruleId": "941", "severity": 2, "message": "1001", "line": 14, "column": 3, "nodeType": null, "messageId": "943", "endLine": 14, "endColumn": 7}, {"ruleId": "941", "severity": 2, "message": "1118", "line": 15, "column": 3, "nodeType": null, "messageId": "943", "endLine": 15, "endColumn": 16}, {"ruleId": "941", "severity": 2, "message": "1119", "line": 16, "column": 3, "nodeType": null, "messageId": "943", "endLine": 16, "endColumn": 13}, {"ruleId": "941", "severity": 2, "message": "1120", "line": 17, "column": 3, "nodeType": null, "messageId": "943", "endLine": 17, "endColumn": 10}, {"ruleId": "941", "severity": 2, "message": "1121", "line": 18, "column": 3, "nodeType": null, "messageId": "943", "endLine": 18, "endColumn": 10}, {"ruleId": "941", "severity": 2, "message": "949", "line": 23, "column": 3, "nodeType": null, "messageId": "943", "endLine": 23, "endColumn": 14}, {"ruleId": "941", "severity": 2, "message": "1122", "line": 24, "column": 3, "nodeType": null, "messageId": "943", "endLine": 24, "endColumn": 7}, {"ruleId": "950", "severity": 2, "message": "951", "line": 29, "column": 13, "nodeType": "952", "messageId": "953", "endLine": 29, "endColumn": 16, "suggestions": "1123"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 30, "column": 24, "nodeType": "952", "messageId": "953", "endLine": 30, "endColumn": 27, "suggestions": "1124"}, {"ruleId": "941", "severity": 2, "message": "1125", "line": 172, "column": 10, "nodeType": null, "messageId": "943", "endLine": 172, "endColumn": 22}, {"ruleId": "941", "severity": 2, "message": "1126", "line": 173, "column": 10, "nodeType": null, "messageId": "943", "endLine": 173, "endColumn": 20}, {"ruleId": "950", "severity": 2, "message": "951", "line": 181, "column": 45, "nodeType": "952", "messageId": "953", "endLine": 181, "endColumn": 48, "suggestions": "1127"}, {"ruleId": "941", "severity": 2, "message": "1128", "line": 201, "column": 9, "nodeType": null, "messageId": "943", "endLine": 201, "endColumn": 26}, {"ruleId": "941", "severity": 2, "message": "963", "line": 207, "column": 14, "nodeType": null, "messageId": "943", "endLine": 207, "endColumn": 19}, {"ruleId": "941", "severity": 2, "message": "1129", "line": 214, "column": 9, "nodeType": null, "messageId": "943", "endLine": 214, "endColumn": 24}, {"ruleId": "941", "severity": 2, "message": "963", "line": 220, "column": 14, "nodeType": null, "messageId": "943", "endLine": 220, "endColumn": 19}, {"ruleId": "941", "severity": 2, "message": "1094", "line": 11, "column": 10, "nodeType": null, "messageId": "943", "endLine": 11, "endColumn": 18}, {"ruleId": "941", "severity": 2, "message": "1130", "line": 16, "column": 3, "nodeType": null, "messageId": "943", "endLine": 16, "endColumn": 6}, {"ruleId": "941", "severity": 2, "message": "1108", "line": 17, "column": 3, "nodeType": null, "messageId": "943", "endLine": 17, "endColumn": 6}, {"ruleId": "941", "severity": 2, "message": "1131", "line": 18, "column": 3, "nodeType": null, "messageId": "943", "endLine": 18, "endColumn": 9}, {"ruleId": "941", "severity": 2, "message": "1132", "line": 21, "column": 3, "nodeType": null, "messageId": "943", "endLine": 21, "endColumn": 16}, {"ruleId": "941", "severity": 2, "message": "1122", "line": 23, "column": 3, "nodeType": null, "messageId": "943", "endLine": 23, "endColumn": 7}, {"ruleId": "950", "severity": 2, "message": "951", "line": 35, "column": 13, "nodeType": "952", "messageId": "953", "endLine": 35, "endColumn": 16, "suggestions": "1133"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 36, "column": 24, "nodeType": "952", "messageId": "953", "endLine": 36, "endColumn": 27, "suggestions": "1134"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 172, "column": 38, "nodeType": "952", "messageId": "953", "endLine": 172, "endColumn": 41, "suggestions": "1135"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 182, "column": 50, "nodeType": "952", "messageId": "953", "endLine": 182, "endColumn": 53, "suggestions": "1136"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 214, "column": 45, "nodeType": "952", "messageId": "953", "endLine": 214, "endColumn": 48, "suggestions": "1137"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 234, "column": 71, "nodeType": "952", "messageId": "953", "endLine": 234, "endColumn": 74, "suggestions": "1138"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 298, "column": 60, "nodeType": "952", "messageId": "953", "endLine": 298, "endColumn": 63, "suggestions": "1139"}, {"ruleId": "1140", "severity": 2, "message": "1141", "line": 569, "column": 28, "nodeType": "1142", "messageId": "1143", "suggestions": "1144"}, {"ruleId": "1140", "severity": 2, "message": "1141", "line": 569, "column": 35, "nodeType": "1142", "messageId": "1143", "suggestions": "1145"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 6, "column": 13, "nodeType": "952", "messageId": "953", "endLine": 6, "endColumn": 16, "suggestions": "1146"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 7, "column": 33, "nodeType": "952", "messageId": "953", "endLine": 7, "endColumn": 36, "suggestions": "1147"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 14, "column": 44, "nodeType": "952", "messageId": "953", "endLine": 14, "endColumn": 47, "suggestions": "1148"}, {"ruleId": "926", "severity": 1, "message": "1149", "line": 19, "column": 6, "nodeType": "928", "endLine": 19, "endColumn": 8, "suggestions": "1150"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 39, "column": 40, "nodeType": "952", "messageId": "953", "endLine": 39, "endColumn": 43, "suggestions": "1151"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 44, "column": 40, "nodeType": "952", "messageId": "953", "endLine": 44, "endColumn": 43, "suggestions": "1152"}, {"ruleId": "941", "severity": 2, "message": "1153", "line": 46, "column": 32, "nodeType": null, "messageId": "943", "endLine": 46, "endColumn": 39}, {"ruleId": "950", "severity": 2, "message": "951", "line": 9, "column": 20, "nodeType": "952", "messageId": "953", "endLine": 9, "endColumn": 23, "suggestions": "1154"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 27, "column": 56, "nodeType": "952", "messageId": "953", "endLine": 27, "endColumn": 59, "suggestions": "1155"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 69, "column": 21, "nodeType": "952", "messageId": "953", "endLine": 69, "endColumn": 24, "suggestions": "1156"}, {"ruleId": "930", "severity": 1, "message": "931", "line": 136, "column": 13, "nodeType": "932", "endLine": 140, "endColumn": 15}, {"ruleId": "1157", "severity": 2, "message": "1158", "line": 4, "column": 18, "nodeType": "1036", "messageId": "1159", "endLine": 4, "endColumn": 28, "suggestions": "1160"}, {"ruleId": "1157", "severity": 2, "message": "1158", "line": 4, "column": 18, "nodeType": "1036", "messageId": "1159", "endLine": 4, "endColumn": 28, "suggestions": "1161"}, {"ruleId": "1157", "severity": 2, "message": "1158", "line": 4, "column": 18, "nodeType": "1036", "messageId": "1159", "endLine": 4, "endColumn": 31, "suggestions": "1162"}, {"ruleId": "926", "severity": 1, "message": "1163", "line": 37, "column": 6, "nodeType": "928", "endLine": 37, "endColumn": 8, "suggestions": "1164"}, {"ruleId": "926", "severity": 1, "message": "1165", "line": 166, "column": 6, "nodeType": "928", "endLine": 166, "endColumn": 8, "suggestions": "1166"}, {"ruleId": "926", "severity": 1, "message": "1167", "line": 179, "column": 6, "nodeType": "928", "endLine": 179, "endColumn": 29, "suggestions": "1168"}, {"ruleId": "926", "severity": 1, "message": "1169", "line": 179, "column": 7, "nodeType": "1170", "endLine": 179, "endColumn": 28}, {"ruleId": "950", "severity": 2, "message": "951", "line": 83, "column": 73, "nodeType": "952", "messageId": "953", "endLine": 83, "endColumn": 76, "suggestions": "1171"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 139, "column": 91, "nodeType": "952", "messageId": "953", "endLine": 139, "endColumn": 94, "suggestions": "1172"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 15, "column": 12, "nodeType": "952", "messageId": "953", "endLine": 15, "endColumn": 15, "suggestions": "1173"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 83, "column": 43, "nodeType": "952", "messageId": "953", "endLine": 83, "endColumn": 46, "suggestions": "1174"}, {"ruleId": "941", "severity": 2, "message": "963", "line": 106, "column": 12, "nodeType": null, "messageId": "943", "endLine": 106, "endColumn": 17}, {"ruleId": "941", "severity": 2, "message": "1175", "line": 16, "column": 27, "nodeType": null, "messageId": "943", "endLine": 16, "endColumn": 30}, {"ruleId": "950", "severity": 2, "message": "951", "line": 24, "column": 18, "nodeType": "952", "messageId": "953", "endLine": 24, "endColumn": 21, "suggestions": "1176"}, {"ruleId": "950", "severity": 2, "message": "951", "line": 24, "column": 29, "nodeType": "952", "messageId": "953", "endLine": 24, "endColumn": 32, "suggestions": "1177"}, {"ruleId": "941", "severity": 2, "message": "1178", "line": 1, "column": 8, "nodeType": null, "messageId": "943", "endLine": 1, "endColumn": 16}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchFolderData'. Either include it or remove the dependency array.", "ArrayExpression", ["1179"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "React Hook useEffect has a missing dependency: 'fetchPhotos'. Either include it or remove the dependency array.", ["1180"], "React Hook useEffect has a missing dependency: 'fetchMembers'. Either include it or remove the dependency array.", ["1181"], "React Hook useEffect has a missing dependency: 'fetchNotifications'. Either include it or remove the dependency array.", ["1182"], "React Hook useEffect has a missing dependency: 'fetchReportsData'. Either include it or remove the dependency array.", ["1183"], "@typescript-eslint/no-unused-vars", "'Card' is defined but never used.", "unusedVar", "'CardContent' is defined but never used.", "'CardHeader' is defined but never used.", "'CardTitle' is defined but never used.", "'Download' is defined but never used.", "'Upload' is defined but never used.", "'CheckCircle' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["1184", "1185"], ["1186", "1187"], ["1188", "1189"], ["1190", "1191"], ["1192", "1193"], ["1194", "1195"], "'canViewSecurity' is assigned a value but never used.", "'canViewBackup' is assigned a value but never used.", ["1196", "1197"], "'error' is defined but never used.", ["1198", "1199"], ["1200", "1201"], ["1202", "1203"], ["1204", "1205"], ["1206", "1207"], "React Hook useEffect has a missing dependency: 'checkAuthAndLoadData'. Either include it or remove the dependency array.", ["1208"], "'useState' is defined but never used.", "'Badge' is defined but never used.", "'Member' is defined but never used.", ["1209"], ["1210", "1211"], ["1212", "1213"], "'getCategoryText' is assigned a value but never used.", ["1214", "1215"], ["1216", "1217"], ["1218", "1219"], "'MoreVertical' is defined but never used.", ["1220", "1221"], ["1222", "1223"], ["1224", "1225"], "'getIncomeTypeText' is assigned a value but never used.", ["1226", "1227"], "'BarChart3' is defined but never used.", "'CreditCard' is defined but never used.", "'Clock' is defined but never used.", ["1228", "1229"], "'X' is defined but never used.", ["1230", "1231"], "'useMemberAuth' is defined but never used.", ["1232", "1233"], ["1234", "1235"], "'MapPin' is defined but never used.", "'jsPDF' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchAccountStatement'. Either include it or remove the dependency array.", ["1236"], ["1237", "1238"], "'Mail' is defined but never used.", "'DollarSign' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchMemberDetails'. Either include it or remove the dependency array.", ["1239"], ["1240", "1241"], ["1242", "1243"], "'DialogFooter' is defined but never used.", "'DialogClose' is defined but never used.", "'Switch' is defined but never used.", "'Calendar' is defined but never used.", ["1244", "1245"], ["1246", "1247"], ["1248", "1249"], ["1250", "1251"], ["1252", "1253"], ["1254", "1255"], "React Hook useEffect has a missing dependency: 'fetchQuickStats'. Either include it or remove the dependency array.", ["1256"], ["1257"], "'createContext' is defined but never used.", "'useContext' is defined but never used.", "'Input' is defined but never used.", "'Select' is defined but never used.", "'SelectContent' is defined but never used.", "'SelectItem' is defined but never used.", "'SelectTrigger' is defined but never used.", "'SelectValue' is defined but never used.", ["1258", "1259"], ["1260", "1261"], ["1262", "1263"], ["1264", "1265"], ["1266", "1267"], ["1268", "1269"], "prefer-const", "'reportData' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "1270", "text": "1271"}, ["1272", "1273"], ["1274", "1275"], ["1276", "1277"], ["1278", "1279"], "'filteredMembers' is assigned a value but never used.", ["1280", "1281"], ["1282", "1283"], {"range": "1284", "text": "1271"}, ["1285", "1286"], ["1287", "1288"], ["1289", "1290"], "'formatDate' is defined but never used.", ["1291", "1292"], ["1293", "1294"], ["1295", "1296"], ["1297", "1298"], ["1299", "1300"], ["1301", "1302"], ["1303", "1304"], ["1305", "1306"], ["1307", "1308"], ["1309", "1310"], ["1311", "1312"], ["1313", "1314"], ["1315", "1316"], ["1317", "1318"], ["1319", "1320"], ["1321", "1322"], ["1323", "1324"], ["1325", "1326"], ["1327", "1328"], ["1329", "1330"], ["1331", "1332"], ["1333", "1334"], ["1335", "1336"], ["1337", "1338"], ["1339", "1340"], ["1341", "1342"], ["1343", "1344"], ["1345", "1346"], ["1347", "1348"], ["1349", "1350"], ["1351", "1352"], ["1353", "1354"], ["1355", "1356"], ["1357", "1358"], ["1359", "1360"], ["1361", "1362"], ["1363", "1364"], ["1365", "1366"], ["1367", "1368"], ["1369", "1370"], ["1371", "1372"], ["1373", "1374"], ["1375", "1376"], "'Textarea' is defined but never used.", "'Settings' is defined but never used.", "'setChangeLog' is assigned a value but never used.", ["1377", "1378"], ["1379", "1380"], ["1381", "1382"], ["1383", "1384"], ["1385", "1386"], "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", ["1387", "1388"], ["1389", "1390"], ["1391", "1392"], ["1393", "1394"], "'Eye' is defined but never used.", "'Edit' is defined but never used.", "'Trash2' is defined but never used.", "'FileText' is defined but never used.", "'Button' is defined but never used.", "'Languages' is defined but never used.", "'Phone' is defined but never used.", ["1395", "1396"], ["1397", "1398"], ["1399", "1400"], "'MessageSquare' is defined but never used.", "'Smartphone' is defined but never used.", "'Volume2' is defined but never used.", "'VolumeX' is defined but never used.", "'Info' is defined but never used.", ["1401", "1402"], ["1403", "1404"], "'testingEmail' is assigned a value but never used.", "'testingSMS' is assigned a value but never used.", ["1405", "1406"], "'testEmailSettings' is assigned a value but never used.", "'testSMSSettings' is assigned a value but never used.", "'Key' is defined but never used.", "'EyeOff' is defined but never used.", "'AlertTriangle' is defined but never used.", ["1407", "1408"], ["1409", "1410"], ["1411", "1412"], ["1413", "1414"], ["1415", "1416"], ["1417", "1418"], ["1419", "1420"], "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["1421", "1422", "1423", "1424"], ["1425", "1426", "1427", "1428"], ["1429", "1430"], ["1431", "1432"], ["1433", "1434"], "React Hook useEffect has a missing dependency: 'loadSettings'. Either include it or remove the dependency array.", ["1435"], ["1436", "1437"], ["1438", "1439"], "'as<PERSON><PERSON>d' is assigned a value but never used.", ["1440", "1441"], ["1442", "1443"], ["1444", "1445"], "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "noEmptyInterfaceWithSuper", ["1446"], ["1447"], ["1448"], "React Hook useCallback has a missing dependency: 'removeToast'. Either include it or remove the dependency array.", ["1449"], "React Hook useEffect has a missing dependency: 'checkSession'. Either include it or remove the dependency array.", ["1450"], "React Hook useEffect has missing dependencies: 'checkSession' and 'user'. Either include them or remove the dependency array.", ["1451"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "ConditionalExpression", ["1452", "1453"], ["1454", "1455"], ["1456", "1457"], ["1458", "1459"], "'req' is defined but never used.", ["1460", "1461"], ["1462", "1463"], "'NextAuth' is defined but never used.", {"desc": "1464", "fix": "1465"}, {"desc": "1466", "fix": "1467"}, {"desc": "1468", "fix": "1469"}, {"desc": "1470", "fix": "1471"}, {"desc": "1472", "fix": "1473"}, {"messageId": "1474", "fix": "1475", "desc": "1476"}, {"messageId": "1477", "fix": "1478", "desc": "1479"}, {"messageId": "1474", "fix": "1480", "desc": "1476"}, {"messageId": "1477", "fix": "1481", "desc": "1479"}, {"messageId": "1474", "fix": "1482", "desc": "1476"}, {"messageId": "1477", "fix": "1483", "desc": "1479"}, {"messageId": "1474", "fix": "1484", "desc": "1476"}, {"messageId": "1477", "fix": "1485", "desc": "1479"}, {"messageId": "1474", "fix": "1486", "desc": "1476"}, {"messageId": "1477", "fix": "1487", "desc": "1479"}, {"messageId": "1474", "fix": "1488", "desc": "1476"}, {"messageId": "1477", "fix": "1489", "desc": "1479"}, {"messageId": "1474", "fix": "1490", "desc": "1476"}, {"messageId": "1477", "fix": "1491", "desc": "1479"}, {"messageId": "1474", "fix": "1492", "desc": "1476"}, {"messageId": "1477", "fix": "1493", "desc": "1479"}, {"messageId": "1474", "fix": "1494", "desc": "1476"}, {"messageId": "1477", "fix": "1495", "desc": "1479"}, {"messageId": "1474", "fix": "1496", "desc": "1476"}, {"messageId": "1477", "fix": "1497", "desc": "1479"}, {"messageId": "1474", "fix": "1498", "desc": "1476"}, {"messageId": "1477", "fix": "1499", "desc": "1479"}, {"messageId": "1474", "fix": "1500", "desc": "1476"}, {"messageId": "1477", "fix": "1501", "desc": "1479"}, {"desc": "1502", "fix": "1503"}, {"desc": "1502", "fix": "1504"}, {"messageId": "1474", "fix": "1505", "desc": "1476"}, {"messageId": "1477", "fix": "1506", "desc": "1479"}, {"messageId": "1474", "fix": "1507", "desc": "1476"}, {"messageId": "1477", "fix": "1508", "desc": "1479"}, {"messageId": "1474", "fix": "1509", "desc": "1476"}, {"messageId": "1477", "fix": "1510", "desc": "1479"}, {"messageId": "1474", "fix": "1511", "desc": "1476"}, {"messageId": "1477", "fix": "1512", "desc": "1479"}, {"messageId": "1474", "fix": "1513", "desc": "1476"}, {"messageId": "1477", "fix": "1514", "desc": "1479"}, {"messageId": "1474", "fix": "1515", "desc": "1476"}, {"messageId": "1477", "fix": "1516", "desc": "1479"}, {"messageId": "1474", "fix": "1517", "desc": "1476"}, {"messageId": "1477", "fix": "1518", "desc": "1479"}, {"messageId": "1474", "fix": "1519", "desc": "1476"}, {"messageId": "1477", "fix": "1520", "desc": "1479"}, {"messageId": "1474", "fix": "1521", "desc": "1476"}, {"messageId": "1477", "fix": "1522", "desc": "1479"}, {"messageId": "1474", "fix": "1523", "desc": "1476"}, {"messageId": "1477", "fix": "1524", "desc": "1479"}, {"messageId": "1474", "fix": "1525", "desc": "1476"}, {"messageId": "1477", "fix": "1526", "desc": "1479"}, {"messageId": "1474", "fix": "1527", "desc": "1476"}, {"messageId": "1477", "fix": "1528", "desc": "1479"}, {"messageId": "1474", "fix": "1529", "desc": "1476"}, {"messageId": "1477", "fix": "1530", "desc": "1479"}, {"desc": "1531", "fix": "1532"}, {"messageId": "1474", "fix": "1533", "desc": "1476"}, {"messageId": "1477", "fix": "1534", "desc": "1479"}, {"desc": "1535", "fix": "1536"}, {"messageId": "1474", "fix": "1537", "desc": "1476"}, {"messageId": "1477", "fix": "1538", "desc": "1479"}, {"messageId": "1474", "fix": "1539", "desc": "1476"}, {"messageId": "1477", "fix": "1540", "desc": "1479"}, {"messageId": "1474", "fix": "1541", "desc": "1476"}, {"messageId": "1477", "fix": "1542", "desc": "1479"}, {"messageId": "1474", "fix": "1543", "desc": "1476"}, {"messageId": "1477", "fix": "1544", "desc": "1479"}, {"messageId": "1474", "fix": "1545", "desc": "1476"}, {"messageId": "1477", "fix": "1546", "desc": "1479"}, {"messageId": "1474", "fix": "1547", "desc": "1476"}, {"messageId": "1477", "fix": "1548", "desc": "1479"}, {"messageId": "1474", "fix": "1549", "desc": "1476"}, {"messageId": "1477", "fix": "1550", "desc": "1479"}, {"messageId": "1474", "fix": "1551", "desc": "1476"}, {"messageId": "1477", "fix": "1552", "desc": "1479"}, {"desc": "1553", "fix": "1554"}, {"desc": "1555", "fix": "1556"}, {"messageId": "1474", "fix": "1557", "desc": "1476"}, {"messageId": "1477", "fix": "1558", "desc": "1479"}, {"messageId": "1474", "fix": "1559", "desc": "1476"}, {"messageId": "1477", "fix": "1560", "desc": "1479"}, {"messageId": "1474", "fix": "1561", "desc": "1476"}, {"messageId": "1477", "fix": "1562", "desc": "1479"}, {"messageId": "1474", "fix": "1563", "desc": "1476"}, {"messageId": "1477", "fix": "1564", "desc": "1479"}, {"messageId": "1474", "fix": "1565", "desc": "1476"}, {"messageId": "1477", "fix": "1566", "desc": "1479"}, {"messageId": "1474", "fix": "1567", "desc": "1476"}, {"messageId": "1477", "fix": "1568", "desc": "1479"}, [5245, 5399], "const reportData: any = {\n      title: '',\n      date: new Date().toLocaleDateString('ar-JO'),\n      period: getPeriodLabel(),\n      statistics: stats\n    }", {"messageId": "1474", "fix": "1569", "desc": "1476"}, {"messageId": "1477", "fix": "1570", "desc": "1479"}, {"messageId": "1474", "fix": "1571", "desc": "1476"}, {"messageId": "1477", "fix": "1572", "desc": "1479"}, {"messageId": "1474", "fix": "1573", "desc": "1476"}, {"messageId": "1477", "fix": "1574", "desc": "1479"}, {"messageId": "1474", "fix": "1575", "desc": "1476"}, {"messageId": "1477", "fix": "1576", "desc": "1479"}, {"messageId": "1474", "fix": "1577", "desc": "1476"}, {"messageId": "1477", "fix": "1578", "desc": "1479"}, {"messageId": "1474", "fix": "1579", "desc": "1476"}, {"messageId": "1477", "fix": "1580", "desc": "1479"}, [5243, 5397], {"messageId": "1474", "fix": "1581", "desc": "1476"}, {"messageId": "1477", "fix": "1582", "desc": "1479"}, {"messageId": "1474", "fix": "1583", "desc": "1476"}, {"messageId": "1477", "fix": "1584", "desc": "1479"}, {"messageId": "1474", "fix": "1585", "desc": "1476"}, {"messageId": "1477", "fix": "1586", "desc": "1479"}, {"messageId": "1474", "fix": "1587", "desc": "1476"}, {"messageId": "1477", "fix": "1588", "desc": "1479"}, {"messageId": "1474", "fix": "1589", "desc": "1476"}, {"messageId": "1477", "fix": "1590", "desc": "1479"}, {"messageId": "1474", "fix": "1591", "desc": "1476"}, {"messageId": "1477", "fix": "1592", "desc": "1479"}, {"messageId": "1474", "fix": "1593", "desc": "1476"}, {"messageId": "1477", "fix": "1594", "desc": "1479"}, {"messageId": "1474", "fix": "1595", "desc": "1476"}, {"messageId": "1477", "fix": "1596", "desc": "1479"}, {"messageId": "1474", "fix": "1597", "desc": "1476"}, {"messageId": "1477", "fix": "1598", "desc": "1479"}, {"messageId": "1474", "fix": "1599", "desc": "1476"}, {"messageId": "1477", "fix": "1600", "desc": "1479"}, {"messageId": "1474", "fix": "1601", "desc": "1476"}, {"messageId": "1477", "fix": "1602", "desc": "1479"}, {"messageId": "1474", "fix": "1603", "desc": "1476"}, {"messageId": "1477", "fix": "1604", "desc": "1479"}, {"messageId": "1474", "fix": "1605", "desc": "1476"}, {"messageId": "1477", "fix": "1606", "desc": "1479"}, {"messageId": "1474", "fix": "1607", "desc": "1476"}, {"messageId": "1477", "fix": "1608", "desc": "1479"}, {"messageId": "1474", "fix": "1609", "desc": "1476"}, {"messageId": "1477", "fix": "1610", "desc": "1479"}, {"messageId": "1474", "fix": "1611", "desc": "1476"}, {"messageId": "1477", "fix": "1612", "desc": "1479"}, {"messageId": "1474", "fix": "1613", "desc": "1476"}, {"messageId": "1477", "fix": "1614", "desc": "1479"}, {"messageId": "1474", "fix": "1615", "desc": "1476"}, {"messageId": "1477", "fix": "1616", "desc": "1479"}, {"messageId": "1474", "fix": "1617", "desc": "1476"}, {"messageId": "1477", "fix": "1618", "desc": "1479"}, {"messageId": "1474", "fix": "1619", "desc": "1476"}, {"messageId": "1477", "fix": "1620", "desc": "1479"}, {"messageId": "1474", "fix": "1621", "desc": "1476"}, {"messageId": "1477", "fix": "1622", "desc": "1479"}, {"messageId": "1474", "fix": "1623", "desc": "1476"}, {"messageId": "1477", "fix": "1624", "desc": "1479"}, {"messageId": "1474", "fix": "1625", "desc": "1476"}, {"messageId": "1477", "fix": "1626", "desc": "1479"}, {"messageId": "1474", "fix": "1627", "desc": "1476"}, {"messageId": "1477", "fix": "1628", "desc": "1479"}, {"messageId": "1474", "fix": "1629", "desc": "1476"}, {"messageId": "1477", "fix": "1630", "desc": "1479"}, {"messageId": "1474", "fix": "1631", "desc": "1476"}, {"messageId": "1477", "fix": "1632", "desc": "1479"}, {"messageId": "1474", "fix": "1633", "desc": "1476"}, {"messageId": "1477", "fix": "1634", "desc": "1479"}, {"messageId": "1474", "fix": "1635", "desc": "1476"}, {"messageId": "1477", "fix": "1636", "desc": "1479"}, {"messageId": "1474", "fix": "1637", "desc": "1476"}, {"messageId": "1477", "fix": "1638", "desc": "1479"}, {"messageId": "1474", "fix": "1639", "desc": "1476"}, {"messageId": "1477", "fix": "1640", "desc": "1479"}, {"messageId": "1474", "fix": "1641", "desc": "1476"}, {"messageId": "1477", "fix": "1642", "desc": "1479"}, {"messageId": "1474", "fix": "1643", "desc": "1476"}, {"messageId": "1477", "fix": "1644", "desc": "1479"}, {"messageId": "1474", "fix": "1645", "desc": "1476"}, {"messageId": "1477", "fix": "1646", "desc": "1479"}, {"messageId": "1474", "fix": "1647", "desc": "1476"}, {"messageId": "1477", "fix": "1648", "desc": "1479"}, {"messageId": "1474", "fix": "1649", "desc": "1476"}, {"messageId": "1477", "fix": "1650", "desc": "1479"}, {"messageId": "1474", "fix": "1651", "desc": "1476"}, {"messageId": "1477", "fix": "1652", "desc": "1479"}, {"messageId": "1474", "fix": "1653", "desc": "1476"}, {"messageId": "1477", "fix": "1654", "desc": "1479"}, {"messageId": "1474", "fix": "1655", "desc": "1476"}, {"messageId": "1477", "fix": "1656", "desc": "1479"}, {"messageId": "1474", "fix": "1657", "desc": "1476"}, {"messageId": "1477", "fix": "1658", "desc": "1479"}, {"messageId": "1474", "fix": "1659", "desc": "1476"}, {"messageId": "1477", "fix": "1660", "desc": "1479"}, {"messageId": "1474", "fix": "1661", "desc": "1476"}, {"messageId": "1477", "fix": "1662", "desc": "1479"}, {"messageId": "1474", "fix": "1663", "desc": "1476"}, {"messageId": "1477", "fix": "1664", "desc": "1479"}, {"messageId": "1474", "fix": "1665", "desc": "1476"}, {"messageId": "1477", "fix": "1666", "desc": "1479"}, {"messageId": "1474", "fix": "1667", "desc": "1476"}, {"messageId": "1477", "fix": "1668", "desc": "1479"}, {"messageId": "1474", "fix": "1669", "desc": "1476"}, {"messageId": "1477", "fix": "1670", "desc": "1479"}, {"messageId": "1474", "fix": "1671", "desc": "1476"}, {"messageId": "1477", "fix": "1672", "desc": "1479"}, {"messageId": "1474", "fix": "1673", "desc": "1476"}, {"messageId": "1477", "fix": "1674", "desc": "1479"}, {"messageId": "1474", "fix": "1675", "desc": "1476"}, {"messageId": "1477", "fix": "1676", "desc": "1479"}, {"messageId": "1474", "fix": "1677", "desc": "1476"}, {"messageId": "1477", "fix": "1678", "desc": "1479"}, {"messageId": "1474", "fix": "1679", "desc": "1476"}, {"messageId": "1477", "fix": "1680", "desc": "1479"}, {"messageId": "1474", "fix": "1681", "desc": "1476"}, {"messageId": "1477", "fix": "1682", "desc": "1479"}, {"messageId": "1474", "fix": "1683", "desc": "1476"}, {"messageId": "1477", "fix": "1684", "desc": "1479"}, {"messageId": "1474", "fix": "1685", "desc": "1476"}, {"messageId": "1477", "fix": "1686", "desc": "1479"}, {"messageId": "1474", "fix": "1687", "desc": "1476"}, {"messageId": "1477", "fix": "1688", "desc": "1479"}, {"messageId": "1474", "fix": "1689", "desc": "1476"}, {"messageId": "1477", "fix": "1690", "desc": "1479"}, {"messageId": "1474", "fix": "1691", "desc": "1476"}, {"messageId": "1477", "fix": "1692", "desc": "1479"}, {"messageId": "1474", "fix": "1693", "desc": "1476"}, {"messageId": "1477", "fix": "1694", "desc": "1479"}, {"messageId": "1474", "fix": "1695", "desc": "1476"}, {"messageId": "1477", "fix": "1696", "desc": "1479"}, {"messageId": "1474", "fix": "1697", "desc": "1476"}, {"messageId": "1477", "fix": "1698", "desc": "1479"}, {"messageId": "1474", "fix": "1699", "desc": "1476"}, {"messageId": "1477", "fix": "1700", "desc": "1479"}, {"messageId": "1474", "fix": "1701", "desc": "1476"}, {"messageId": "1477", "fix": "1702", "desc": "1479"}, {"messageId": "1474", "fix": "1703", "desc": "1476"}, {"messageId": "1477", "fix": "1704", "desc": "1479"}, {"messageId": "1474", "fix": "1705", "desc": "1476"}, {"messageId": "1477", "fix": "1706", "desc": "1479"}, {"messageId": "1474", "fix": "1707", "desc": "1476"}, {"messageId": "1477", "fix": "1708", "desc": "1479"}, {"messageId": "1474", "fix": "1709", "desc": "1476"}, {"messageId": "1477", "fix": "1710", "desc": "1479"}, {"messageId": "1474", "fix": "1711", "desc": "1476"}, {"messageId": "1477", "fix": "1712", "desc": "1479"}, {"messageId": "1474", "fix": "1713", "desc": "1476"}, {"messageId": "1477", "fix": "1714", "desc": "1479"}, {"messageId": "1474", "fix": "1715", "desc": "1476"}, {"messageId": "1477", "fix": "1716", "desc": "1479"}, {"messageId": "1717", "data": "1718", "fix": "1719", "desc": "1720"}, {"messageId": "1717", "data": "1721", "fix": "1722", "desc": "1723"}, {"messageId": "1717", "data": "1724", "fix": "1725", "desc": "1726"}, {"messageId": "1717", "data": "1727", "fix": "1728", "desc": "1729"}, {"messageId": "1717", "data": "1730", "fix": "1731", "desc": "1720"}, {"messageId": "1717", "data": "1732", "fix": "1733", "desc": "1723"}, {"messageId": "1717", "data": "1734", "fix": "1735", "desc": "1726"}, {"messageId": "1717", "data": "1736", "fix": "1737", "desc": "1729"}, {"messageId": "1474", "fix": "1738", "desc": "1476"}, {"messageId": "1477", "fix": "1739", "desc": "1479"}, {"messageId": "1474", "fix": "1740", "desc": "1476"}, {"messageId": "1477", "fix": "1741", "desc": "1479"}, {"messageId": "1474", "fix": "1742", "desc": "1476"}, {"messageId": "1477", "fix": "1743", "desc": "1479"}, {"desc": "1744", "fix": "1745"}, {"messageId": "1474", "fix": "1746", "desc": "1476"}, {"messageId": "1477", "fix": "1747", "desc": "1479"}, {"messageId": "1474", "fix": "1748", "desc": "1476"}, {"messageId": "1477", "fix": "1749", "desc": "1479"}, {"messageId": "1474", "fix": "1750", "desc": "1476"}, {"messageId": "1477", "fix": "1751", "desc": "1479"}, {"messageId": "1474", "fix": "1752", "desc": "1476"}, {"messageId": "1477", "fix": "1753", "desc": "1479"}, {"messageId": "1474", "fix": "1754", "desc": "1476"}, {"messageId": "1477", "fix": "1755", "desc": "1479"}, {"messageId": "1756", "fix": "1757", "desc": "1758"}, {"messageId": "1756", "fix": "1759", "desc": "1758"}, {"messageId": "1756", "fix": "1760", "desc": "1758"}, {"desc": "1761", "fix": "1762"}, {"desc": "1763", "fix": "1764"}, {"desc": "1765", "fix": "1766"}, {"messageId": "1474", "fix": "1767", "desc": "1476"}, {"messageId": "1477", "fix": "1768", "desc": "1479"}, {"messageId": "1474", "fix": "1769", "desc": "1476"}, {"messageId": "1477", "fix": "1770", "desc": "1479"}, {"messageId": "1474", "fix": "1771", "desc": "1476"}, {"messageId": "1477", "fix": "1772", "desc": "1479"}, {"messageId": "1474", "fix": "1773", "desc": "1476"}, {"messageId": "1477", "fix": "1774", "desc": "1479"}, {"messageId": "1474", "fix": "1775", "desc": "1476"}, {"messageId": "1477", "fix": "1776", "desc": "1479"}, {"messageId": "1474", "fix": "1777", "desc": "1476"}, {"messageId": "1477", "fix": "1778", "desc": "1479"}, "Update the dependencies array to be: [search, session, folderId, folderType, fetchFolderData]", {"range": "1779", "text": "1780"}, "Update the dependencies array to be: [fetchPhotos, search, selectedCategory, session, viewMode]", {"range": "1781", "text": "1782"}, "Update the dependencies array to be: [search, status, pagination.page, fetchMembers]", {"range": "1783", "text": "1784"}, "Update the dependencies array to be: [search, filter, selectedCategory, session, fetchNotifications]", {"range": "1785", "text": "1786"}, "Update the dependencies array to be: [fetchReportsData, selectedPeriod, session]", {"range": "1787", "text": "1788"}, "suggestUnknown", {"range": "1789", "text": "1790"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1791", "text": "1792"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "1793", "text": "1790"}, {"range": "1794", "text": "1792"}, {"range": "1795", "text": "1790"}, {"range": "1796", "text": "1792"}, {"range": "1797", "text": "1790"}, {"range": "1798", "text": "1792"}, {"range": "1799", "text": "1790"}, {"range": "1800", "text": "1792"}, {"range": "1801", "text": "1790"}, {"range": "1802", "text": "1792"}, {"range": "1803", "text": "1790"}, {"range": "1804", "text": "1792"}, {"range": "1805", "text": "1790"}, {"range": "1806", "text": "1792"}, {"range": "1807", "text": "1790"}, {"range": "1808", "text": "1792"}, {"range": "1809", "text": "1790"}, {"range": "1810", "text": "1792"}, {"range": "1811", "text": "1790"}, {"range": "1812", "text": "1792"}, {"range": "1813", "text": "1790"}, {"range": "1814", "text": "1792"}, "Update the dependencies array to be: [checkAuthAndLoadData]", {"range": "1815", "text": "1816"}, {"range": "1817", "text": "1816"}, {"range": "1818", "text": "1790"}, {"range": "1819", "text": "1792"}, {"range": "1820", "text": "1790"}, {"range": "1821", "text": "1792"}, {"range": "1822", "text": "1790"}, {"range": "1823", "text": "1792"}, {"range": "1824", "text": "1790"}, {"range": "1825", "text": "1792"}, {"range": "1826", "text": "1790"}, {"range": "1827", "text": "1792"}, {"range": "1828", "text": "1790"}, {"range": "1829", "text": "1792"}, {"range": "1830", "text": "1790"}, {"range": "1831", "text": "1792"}, {"range": "1832", "text": "1790"}, {"range": "1833", "text": "1792"}, {"range": "1834", "text": "1790"}, {"range": "1835", "text": "1792"}, {"range": "1836", "text": "1790"}, {"range": "1837", "text": "1792"}, {"range": "1838", "text": "1790"}, {"range": "1839", "text": "1792"}, {"range": "1840", "text": "1790"}, {"range": "1841", "text": "1792"}, {"range": "1842", "text": "1790"}, {"range": "1843", "text": "1792"}, "Update the dependencies array to be: [open, memberId, selectedYear, fetchAccountStatement]", {"range": "1844", "text": "1845"}, {"range": "1846", "text": "1790"}, {"range": "1847", "text": "1792"}, "Update the dependencies array to be: [open, member, fetchMemberDetails]", {"range": "1848", "text": "1849"}, {"range": "1850", "text": "1790"}, {"range": "1851", "text": "1792"}, {"range": "1852", "text": "1790"}, {"range": "1853", "text": "1792"}, {"range": "1854", "text": "1790"}, {"range": "1855", "text": "1792"}, {"range": "1856", "text": "1790"}, {"range": "1857", "text": "1792"}, {"range": "1858", "text": "1790"}, {"range": "1859", "text": "1792"}, {"range": "1860", "text": "1790"}, {"range": "1861", "text": "1792"}, {"range": "1862", "text": "1790"}, {"range": "1863", "text": "1792"}, {"range": "1864", "text": "1790"}, {"range": "1865", "text": "1792"}, "Update the dependencies array to be: [fetchQuickStats, memberId]", {"range": "1866", "text": "1867"}, "Update the dependencies array to be: [fetchMembers, open, searchTerm]", {"range": "1868", "text": "1869"}, {"range": "1870", "text": "1790"}, {"range": "1871", "text": "1792"}, {"range": "1872", "text": "1790"}, {"range": "1873", "text": "1792"}, {"range": "1874", "text": "1790"}, {"range": "1875", "text": "1792"}, {"range": "1876", "text": "1790"}, {"range": "1877", "text": "1792"}, {"range": "1878", "text": "1790"}, {"range": "1879", "text": "1792"}, {"range": "1880", "text": "1790"}, {"range": "1881", "text": "1792"}, {"range": "1882", "text": "1790"}, {"range": "1883", "text": "1792"}, {"range": "1884", "text": "1790"}, {"range": "1885", "text": "1792"}, {"range": "1886", "text": "1790"}, {"range": "1887", "text": "1792"}, {"range": "1888", "text": "1790"}, {"range": "1889", "text": "1792"}, {"range": "1890", "text": "1790"}, {"range": "1891", "text": "1792"}, {"range": "1892", "text": "1790"}, {"range": "1893", "text": "1792"}, {"range": "1894", "text": "1790"}, {"range": "1895", "text": "1792"}, {"range": "1896", "text": "1790"}, {"range": "1897", "text": "1792"}, {"range": "1898", "text": "1790"}, {"range": "1899", "text": "1792"}, {"range": "1900", "text": "1790"}, {"range": "1901", "text": "1792"}, {"range": "1902", "text": "1790"}, {"range": "1903", "text": "1792"}, {"range": "1904", "text": "1790"}, {"range": "1905", "text": "1792"}, {"range": "1906", "text": "1790"}, {"range": "1907", "text": "1792"}, {"range": "1908", "text": "1790"}, {"range": "1909", "text": "1792"}, {"range": "1910", "text": "1790"}, {"range": "1911", "text": "1792"}, {"range": "1912", "text": "1790"}, {"range": "1913", "text": "1792"}, {"range": "1914", "text": "1790"}, {"range": "1915", "text": "1792"}, {"range": "1916", "text": "1790"}, {"range": "1917", "text": "1792"}, {"range": "1918", "text": "1790"}, {"range": "1919", "text": "1792"}, {"range": "1920", "text": "1790"}, {"range": "1921", "text": "1792"}, {"range": "1922", "text": "1790"}, {"range": "1923", "text": "1792"}, {"range": "1924", "text": "1790"}, {"range": "1925", "text": "1792"}, {"range": "1926", "text": "1790"}, {"range": "1927", "text": "1792"}, {"range": "1928", "text": "1790"}, {"range": "1929", "text": "1792"}, {"range": "1930", "text": "1790"}, {"range": "1931", "text": "1792"}, {"range": "1932", "text": "1790"}, {"range": "1933", "text": "1792"}, {"range": "1934", "text": "1790"}, {"range": "1935", "text": "1792"}, {"range": "1936", "text": "1790"}, {"range": "1937", "text": "1792"}, {"range": "1938", "text": "1790"}, {"range": "1939", "text": "1792"}, {"range": "1940", "text": "1790"}, {"range": "1941", "text": "1792"}, {"range": "1942", "text": "1790"}, {"range": "1943", "text": "1792"}, {"range": "1944", "text": "1790"}, {"range": "1945", "text": "1792"}, {"range": "1946", "text": "1790"}, {"range": "1947", "text": "1792"}, {"range": "1948", "text": "1790"}, {"range": "1949", "text": "1792"}, {"range": "1950", "text": "1790"}, {"range": "1951", "text": "1792"}, {"range": "1952", "text": "1790"}, {"range": "1953", "text": "1792"}, {"range": "1954", "text": "1790"}, {"range": "1955", "text": "1792"}, {"range": "1956", "text": "1790"}, {"range": "1957", "text": "1792"}, {"range": "1958", "text": "1790"}, {"range": "1959", "text": "1792"}, {"range": "1960", "text": "1790"}, {"range": "1961", "text": "1792"}, {"range": "1962", "text": "1790"}, {"range": "1963", "text": "1792"}, {"range": "1964", "text": "1790"}, {"range": "1965", "text": "1792"}, {"range": "1966", "text": "1790"}, {"range": "1967", "text": "1792"}, {"range": "1968", "text": "1790"}, {"range": "1969", "text": "1792"}, {"range": "1970", "text": "1790"}, {"range": "1971", "text": "1792"}, {"range": "1972", "text": "1790"}, {"range": "1973", "text": "1792"}, {"range": "1974", "text": "1790"}, {"range": "1975", "text": "1792"}, {"range": "1976", "text": "1790"}, {"range": "1977", "text": "1792"}, {"range": "1978", "text": "1790"}, {"range": "1979", "text": "1792"}, {"range": "1980", "text": "1790"}, {"range": "1981", "text": "1792"}, {"range": "1982", "text": "1790"}, {"range": "1983", "text": "1792"}, {"range": "1984", "text": "1790"}, {"range": "1985", "text": "1792"}, {"range": "1986", "text": "1790"}, {"range": "1987", "text": "1792"}, {"range": "1988", "text": "1790"}, {"range": "1989", "text": "1792"}, {"range": "1990", "text": "1790"}, {"range": "1991", "text": "1792"}, {"range": "1992", "text": "1790"}, {"range": "1993", "text": "1792"}, {"range": "1994", "text": "1790"}, {"range": "1995", "text": "1792"}, {"range": "1996", "text": "1790"}, {"range": "1997", "text": "1792"}, {"range": "1998", "text": "1790"}, {"range": "1999", "text": "1792"}, {"range": "2000", "text": "1790"}, {"range": "2001", "text": "1792"}, {"range": "2002", "text": "1790"}, {"range": "2003", "text": "1792"}, {"range": "2004", "text": "1790"}, {"range": "2005", "text": "1792"}, {"range": "2006", "text": "1790"}, {"range": "2007", "text": "1792"}, {"range": "2008", "text": "1790"}, {"range": "2009", "text": "1792"}, {"range": "2010", "text": "1790"}, {"range": "2011", "text": "1792"}, {"range": "2012", "text": "1790"}, {"range": "2013", "text": "1792"}, {"range": "2014", "text": "1790"}, {"range": "2015", "text": "1792"}, {"range": "2016", "text": "1790"}, {"range": "2017", "text": "1792"}, {"range": "2018", "text": "1790"}, {"range": "2019", "text": "1792"}, {"range": "2020", "text": "1790"}, {"range": "2021", "text": "1792"}, {"range": "2022", "text": "1790"}, {"range": "2023", "text": "1792"}, {"range": "2024", "text": "1790"}, {"range": "2025", "text": "1792"}, {"range": "2026", "text": "1790"}, {"range": "2027", "text": "1792"}, {"range": "2028", "text": "1790"}, {"range": "2029", "text": "1792"}, "replaceWithAlt", {"alt": "2030"}, {"range": "2031", "text": "2032"}, "Replace with `&quot;`.", {"alt": "2033"}, {"range": "2034", "text": "2035"}, "Replace with `&ldquo;`.", {"alt": "2036"}, {"range": "2037", "text": "2038"}, "Replace with `&#34;`.", {"alt": "2039"}, {"range": "2040", "text": "2041"}, "Replace with `&rdquo;`.", {"alt": "2030"}, {"range": "2042", "text": "2043"}, {"alt": "2033"}, {"range": "2044", "text": "2045"}, {"alt": "2036"}, {"range": "2046", "text": "2047"}, {"alt": "2039"}, {"range": "2048", "text": "2049"}, {"range": "2050", "text": "1790"}, {"range": "2051", "text": "1792"}, {"range": "2052", "text": "1790"}, {"range": "2053", "text": "1792"}, {"range": "2054", "text": "1790"}, {"range": "2055", "text": "1792"}, "Update the dependencies array to be: [loadSettings]", {"range": "2056", "text": "2057"}, {"range": "2058", "text": "1790"}, {"range": "2059", "text": "1792"}, {"range": "2060", "text": "1790"}, {"range": "2061", "text": "1792"}, {"range": "2062", "text": "1790"}, {"range": "2063", "text": "1792"}, {"range": "2064", "text": "1790"}, {"range": "2065", "text": "1792"}, {"range": "2066", "text": "1790"}, {"range": "2067", "text": "1792"}, "replaceEmptyInterfaceWithSuper", {"range": "2068", "text": "2069"}, "Replace empty interface with a type alias.", {"range": "2070", "text": "2071"}, {"range": "2072", "text": "2073"}, "Update the dependencies array to be: [removeToast]", {"range": "2074", "text": "2075"}, "Update the dependencies array to be: [checkSession]", {"range": "2076", "text": "2077"}, "Update the dependencies array to be: [checkSession, user]", {"range": "2078", "text": "2079"}, {"range": "2080", "text": "1790"}, {"range": "2081", "text": "1792"}, {"range": "2082", "text": "1790"}, {"range": "2083", "text": "1792"}, {"range": "2084", "text": "1790"}, {"range": "2085", "text": "1792"}, {"range": "2086", "text": "1790"}, {"range": "2087", "text": "1792"}, {"range": "2088", "text": "1790"}, {"range": "2089", "text": "1792"}, {"range": "2090", "text": "1790"}, {"range": "2091", "text": "1792"}, [4962, 5001], "[search, session, folderId, folderType, fetchFolderData]", [3824, 3869], "[fetchPhotos, search, selectedCategory, session, viewMode]", [4677, 4710], "[search, status, pagination.page, fetchMembers]", [2209, 2252], "[search, filter, selectedCategory, session, fetchNotifications]", [2292, 2317], "[fetchReport<PERSON><PERSON><PERSON>, selected<PERSON><PERSON><PERSON>, session]", [1042, 1045], "unknown", [1042, 1045], "never", [1060, 1063], [1060, 1063], [1081, 1084], [1081, 1084], [1097, 1100], [1097, 1100], [1111, 1114], [1111, 1114], [1127, 1130], [1127, 1130], [3829, 3832], [3829, 3832], [10803, 10806], [10803, 10806], [11308, 11311], [11308, 11311], [535, 538], [535, 538], [746, 749], [746, 749], [767, 770], [767, 770], [1170, 1172], "[checkAuthAndLoadData]", [1439, 1441], [2241, 2244], [2241, 2244], [3658, 3661], [3658, 3661], [9477, 9480], [9477, 9480], [2350, 2353], [2350, 2353], [2699, 2702], [2699, 2702], [6137, 6140], [6137, 6140], [3093, 3096], [3093, 3096], [6207, 6210], [6207, 6210], [12663, 12666], [12663, 12666], [2140, 2143], [2140, 2143], [340, 343], [340, 343], [750, 753], [750, 753], [2597, 2600], [2597, 2600], [2501, 2531], "[open, memberId, selected<PERSON>ear, fetchAccountStatement]", [3273, 3276], [3273, 3276], [1365, 1379], "[open, member, fetchMemberDetails]", [1770, 1773], [1770, 1773], [1947, 1950], [1947, 1950], [3506, 3509], [3506, 3509], [4795, 4798], [4795, 4798], [6478, 6481], [6478, 6481], [2969, 2972], [2969, 2972], [6141, 6144], [6141, 6144], [2235, 2238], [2235, 2238], [578, 588], "[fetchQuickStats, memberId]", [1416, 1434], "[fetchMembers, open, searchTerm]", [843, 846], [843, 846], [892, 895], [892, 895], [1242, 1245], [1242, 1245], [5586, 5589], [5586, 5589], [864, 867], [864, 867], [913, 916], [913, 916], [5261, 5264], [5261, 5264], [937, 940], [937, 940], [986, 989], [986, 989], [2320, 2323], [2320, 2323], [875, 878], [875, 878], [924, 927], [924, 927], [5259, 5262], [5259, 5262], [961, 964], [961, 964], [1010, 1013], [1010, 1013], [137, 140], [137, 140], [237, 240], [237, 240], [2479, 2482], [2479, 2482], [7676, 7679], [7676, 7679], [8231, 8234], [8231, 8234], [8874, 8877], [8874, 8877], [10089, 10092], [10089, 10092], [10614, 10617], [10614, 10617], [10792, 10795], [10792, 10795], [11092, 11095], [11092, 11095], [11470, 11473], [11470, 11473], [11781, 11784], [11781, 11784], [13061, 13064], [13061, 13064], [13634, 13637], [13634, 13637], [14921, 14924], [14921, 14924], [15502, 15505], [15502, 15505], [16875, 16878], [16875, 16878], [17550, 17553], [17550, 17553], [17678, 17681], [17678, 17681], [18173, 18176], [18173, 18176], [18307, 18310], [18307, 18310], [18809, 18812], [18809, 18812], [18979, 18982], [18979, 18982], [19616, 19619], [19616, 19619], [19739, 19742], [19739, 19742], [19861, 19864], [19861, 19864], [20788, 20791], [20788, 20791], [21226, 21229], [21226, 21229], [21310, 21313], [21310, 21313], [21394, 21397], [21394, 21397], [21481, 21484], [21481, 21484], [21569, 21572], [21569, 21572], [21652, 21655], [21652, 21655], [21737, 21740], [21737, 21740], [21821, 21824], [21821, 21824], [21903, 21906], [21903, 21906], [21985, 21988], [21985, 21988], [22067, 22070], [22067, 22070], [22152, 22155], [22152, 22155], [22238, 22241], [22238, 22241], [22319, 22322], [22319, 22322], [22402, 22405], [22402, 22405], [22484, 22487], [22484, 22487], [722, 725], [722, 725], [749, 752], [749, 752], [4752, 4755], [4752, 4755], [5135, 5138], [5135, 5138], [5860, 5863], [5860, 5863], [759, 762], [759, 762], [786, 789], [786, 789], [3068, 3071], [3068, 3071], [3712, 3715], [3712, 3715], [712, 715], [712, 715], [739, 742], [739, 742], [3464, 3467], [3464, 3467], [761, 764], [761, 764], [788, 791], [788, 791], [4033, 4036], [4033, 4036], [844, 847], [844, 847], [871, 874], [871, 874], [3985, 3988], [3985, 3988], [4255, 4258], [4255, 4258], [5036, 5039], [5036, 5039], [5682, 5685], [5682, 5685], [7198, 7201], [7198, 7201], "&quot;", [17713, 17731], "مدة &quot;تذكرني\" (يوم)", "&ldquo;", [17713, 17731], "مدة &ldquo;تذكرني\" (يوم)", "&#34;", [17713, 17731], "مدة &#34;تذكرني\" (يوم)", "&rdquo;", [17713, 17731], "مدة &rdquo;تذكرني\" (يوم)", [17713, 17731], "مدة \"تذكرني&quot; (يوم)", [17713, 17731], "مدة \"تذكرني&ldquo; (يوم)", [17713, 17731], "مدة \"تذكرني&#34; (يوم)", [17713, 17731], "مدة \"تذكرني&rdquo; (يوم)", [130, 133], [130, 133], [166, 169], [166, 169], [407, 410], [407, 410], [508, 510], "[loadSettings]", [990, 993], [990, 993], [1104, 1107], [1104, 1107], [232, 235], [232, 235], [664, 667], [664, 667], [1671, 1674], [1671, 1674], [72, 149], "type InputProps = React.InputHTMLAttributes<HTMLInputElement>", [72, 149], "type LabelProps = React.LabelHTMLAttributes<HTMLLabelElement>", [72, 158], "type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement>", [1058, 1060], "[removeToast]", [4802, 4804], "[checkSession]", [5111, 5134], "[checkSession, user]", [2983, 2986], [2983, 2986], [4665, 4668], [4665, 4668], [380, 383], [380, 383], [2167, 2170], [2167, 2170], [708, 711], [708, 711], [719, 722], [719, 722]]