'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { formatCurrency, formatDate } from '@/lib/utils'
import {
  TrendingUp,
  FileText,
  Download,
  Search,
  Filter,
  Calendar,
  DollarSign,
  User,
  Receipt
} from 'lucide-react'

interface Income {
  id: string
  amount: number
  date: string
  source: string
  type: 'SUBSCRIPTION' | 'DONATION' | 'EVENT' | 'OTHER'
  description?: string
  member?: {
    id: string
    name: string
  }
  createdBy?: {
    name: string
  }
}

interface IncomesReportsProps {
  onExportPDF: (data: any, type: string) => void
  onExportCSV: (data: any, type: string) => void
}

export default function IncomesReports({ onExportPDF, onExportCSV }: IncomesReportsProps) {
  const [incomes, setIncomes] = useState<Income[]>([])
  const [filteredIncomes, setFilteredIncomes] = useState<Income[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [typeFilter, setTypeFilter] = useState('all')
  const [dateFilter, setDateFilter] = useState('all')
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')

  // جلب بيانات الإيرادات
  const fetchIncomes = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/incomes?limit=1000')
      if (!response.ok) throw new Error('فشل في جلب بيانات الإيرادات')

      const data = await response.json()
      setIncomes(data.incomes || [])
      setFilteredIncomes(data.incomes || [])
    } catch (error) {
      console.error('خطأ في جلب بيانات الإيرادات:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchIncomes()
  }, [])

  // تطبيق الفلاتر
  useEffect(() => {
    let filtered = incomes

    // فلتر البحث
    if (searchTerm) {
      filtered = filtered.filter(income =>
        income.source.toLowerCase().includes(searchTerm.toLowerCase()) ||
        income.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        income.member?.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // فلتر النوع
    if (typeFilter !== 'all') {
      filtered = filtered.filter(income => income.type === typeFilter)
    }

    // فلتر التاريخ
    if (dateFilter !== 'all') {
      const now = new Date()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      
      switch (dateFilter) {
        case 'today':
          filtered = filtered.filter(income => {
            const incomeDate = new Date(income.date)
            return incomeDate >= today
          })
          break
        case 'week':
          const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
          filtered = filtered.filter(income => {
            const incomeDate = new Date(income.date)
            return incomeDate >= weekAgo
          })
          break
        case 'month':
          const monthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate())
          filtered = filtered.filter(income => {
            const incomeDate = new Date(income.date)
            return incomeDate >= monthAgo
          })
          break
        case 'year':
          const yearAgo = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate())
          filtered = filtered.filter(income => {
            const incomeDate = new Date(income.date)
            return incomeDate >= yearAgo
          })
          break
        case 'custom':
          if (startDate && endDate) {
            const start = new Date(startDate)
            const end = new Date(endDate)
            filtered = filtered.filter(income => {
              const incomeDate = new Date(income.date)
              return incomeDate >= start && incomeDate <= end
            })
          }
          break
      }
    }

    setFilteredIncomes(filtered)
  }, [incomes, searchTerm, typeFilter, dateFilter, startDate, endDate])

  // حساب الإحصائيات
  const getStatistics = () => {
    const total = filteredIncomes.reduce((sum, income) => sum + income.amount, 0)
    const count = filteredIncomes.length
    const average = count > 0 ? total / count : 0

    // إحصائيات حسب النوع
    const byType = filteredIncomes.reduce((acc, income) => {
      acc[income.type] = (acc[income.type] || 0) + income.amount
      return acc
    }, {} as Record<string, number>)

    // إحصائيات حسب العضو
    const byMember = filteredIncomes.reduce((acc, income) => {
      if (income.member) {
        const memberName = income.member.name
        acc[memberName] = (acc[memberName] || 0) + income.amount
      }
      return acc
    }, {} as Record<string, number>)

    return {
      total,
      count,
      average,
      byType,
      byMember
    }
  }

  // تصدير التقرير
  const handleExport = (format: 'pdf' | 'csv', reportType: 'detailed' | 'summary' | 'by-type' | 'by-member') => {
    const stats = getStatistics()
    
    const reportData: any = {
      title: '',
      date: new Date().toLocaleDateString('ar-JO'),
      period: getPeriodLabel(),
      statistics: stats
    }

    switch (reportType) {
      case 'detailed':
        reportData.title = 'تقرير الإيرادات المفصل'
        reportData.incomes = filteredIncomes.map(income => ({
          date: formatDate(income.date),
          memberName: income.member?.name || 'غير محدد',
          type: getTypeLabel(income.type),
          source: income.source,
          amount: income.amount,
          description: income.description || 'غير محدد',
          createdBy: income.createdBy?.name || 'غير محدد'
        }))
        break
      
      case 'summary':
        reportData.title = 'ملخص الإيرادات'
        reportData.summary = {
          totalAmount: stats.total,
          totalCount: stats.count,
          averageAmount: stats.average
        }
        break
      
      case 'by-type':
        reportData.title = 'تقرير الإيرادات حسب النوع'
        reportData.byType = Object.entries(stats.byType).map(([type, amount]) => ({
          type: getTypeLabel(type),
          amount,
          percentage: ((amount / stats.total) * 100).toFixed(1)
        }))
        break
      
      case 'by-member':
        reportData.title = 'تقرير الإيرادات حسب العضو'
        reportData.byMember = Object.entries(stats.byMember)
          .sort(([,a], [,b]) => b - a)
          .map(([memberName, amount]) => ({
            memberName,
            amount,
            percentage: ((amount / stats.total) * 100).toFixed(1)
          }))
        break
    }

    if (format === 'pdf') {
      onExportPDF(reportData, `incomes-${reportType}`)
    } else {
      onExportCSV(reportData, `incomes-${reportType}`)
    }
  }

  // الحصول على تسمية النوع
  const getTypeLabel = (type: string) => {
    const typeLabels = {
      'SUBSCRIPTION': 'اشتراك',
      'DONATION': 'تبرع',
      'EVENT': 'فعالية',
      'OTHER': 'أخرى'
    }
    return typeLabels[type as keyof typeof typeLabels] || type
  }

  // الحصول على لون النوع
  const getTypeColor = (type: string) => {
    const typeColors = {
      'SUBSCRIPTION': 'bg-blue-100 text-blue-800',
      'DONATION': 'bg-green-100 text-green-800',
      'EVENT': 'bg-purple-100 text-purple-800',
      'OTHER': 'bg-gray-100 text-gray-800'
    }
    return typeColors[type as keyof typeof typeColors] || 'bg-gray-100 text-gray-800'
  }

  // الحصول على تسمية الفترة
  const getPeriodLabel = () => {
    switch (dateFilter) {
      case 'today': return 'اليوم'
      case 'week': return 'آخر أسبوع'
      case 'month': return 'آخر شهر'
      case 'year': return 'آخر سنة'
      case 'custom': return `من ${startDate} إلى ${endDate}`
      default: return 'جميع الفترات'
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-gray-500">جاري تحميل بيانات الإيرادات...</div>
      </div>
    )
  }

  const stats = getStatistics()

  return (
    <div className="space-y-6">
      {/* عنوان القسم */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-bold text-gray-900 flex items-center">
            <TrendingUp className="w-5 h-5 ml-2" />
            تقارير الإيرادات
          </h2>
          <p className="text-gray-600">تقارير شاملة عن الإيرادات والمساهمات</p>
        </div>
        <div className="flex space-x-2 space-x-reverse">
          <Button
            onClick={() => handleExport('pdf', 'detailed')}
            className="bg-red-600 hover:bg-red-700 text-white"
          >
            <FileText className="w-4 h-4 ml-2" />
            تقرير مفصل PDF
          </Button>
          <Button
            onClick={() => handleExport('csv', 'detailed')}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            <Download className="w-4 h-4 ml-2" />
            تصدير CSV
          </Button>
        </div>
      </div>

      {/* أدوات البحث والفلترة */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute right-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="البحث في الإيرادات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
            
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger>
                <SelectValue placeholder="فلترة حسب النوع" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الأنواع</SelectItem>
                <SelectItem value="SUBSCRIPTION">اشتراك</SelectItem>
                <SelectItem value="DONATION">تبرع</SelectItem>
                <SelectItem value="EVENT">فعالية</SelectItem>
                <SelectItem value="OTHER">أخرى</SelectItem>
              </SelectContent>
            </Select>

            <Select value={dateFilter} onValueChange={setDateFilter}>
              <SelectTrigger>
                <SelectValue placeholder="فلترة حسب التاريخ" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الفترات</SelectItem>
                <SelectItem value="today">اليوم</SelectItem>
                <SelectItem value="week">آخر أسبوع</SelectItem>
                <SelectItem value="month">آخر شهر</SelectItem>
                <SelectItem value="year">آخر سنة</SelectItem>
                <SelectItem value="custom">فترة مخصصة</SelectItem>
              </SelectContent>
            </Select>

            <div className="text-sm text-gray-600 flex items-center">
              <Filter className="w-4 h-4 ml-2" />
              عدد النتائج: {filteredIncomes.length} من {incomes.length}
            </div>
          </div>

          {/* فلتر التاريخ المخصص */}
          {dateFilter === 'custom' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">من تاريخ</label>
                <Input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">إلى تاريخ</label>
                <Input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* الإحصائيات السريعة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <DollarSign className="w-8 h-8 text-green-500" />
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي الإيرادات</p>
                <p className="text-2xl font-bold text-green-600">{formatCurrency(stats.total)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <Receipt className="w-8 h-8 text-blue-500" />
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">عدد المعاملات</p>
                <p className="text-2xl font-bold text-blue-600">{stats.count}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <TrendingUp className="w-8 h-8 text-purple-500" />
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">متوسط المبلغ</p>
                <p className="text-2xl font-bold text-purple-600">{formatCurrency(stats.average)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <Calendar className="w-8 h-8 text-orange-500" />
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">الفترة</p>
                <p className="text-lg font-bold text-orange-600">{getPeriodLabel()}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* أزرار التقارير المختلفة */}
      <Card>
        <CardHeader>
          <CardTitle>تقارير متخصصة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium">تقرير حسب النوع</h4>
              <div className="flex space-x-2 space-x-reverse">
                <Button
                  size="sm"
                  onClick={() => handleExport('pdf', 'by-type')}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  PDF
                </Button>
                <Button
                  size="sm"
                  onClick={() => handleExport('csv', 'by-type')}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  CSV
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">تقرير حسب العضو</h4>
              <div className="flex space-x-2 space-x-reverse">
                <Button
                  size="sm"
                  onClick={() => handleExport('pdf', 'by-member')}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  PDF
                </Button>
                <Button
                  size="sm"
                  onClick={() => handleExport('csv', 'by-member')}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  CSV
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">ملخص الإيرادات</h4>
              <div className="flex space-x-2 space-x-reverse">
                <Button
                  size="sm"
                  onClick={() => handleExport('pdf', 'summary')}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  PDF
                </Button>
                <Button
                  size="sm"
                  onClick={() => handleExport('csv', 'summary')}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  CSV
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* جدول الإيرادات */}
      <Card>
        <CardHeader>
          <CardTitle>تفاصيل الإيرادات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="text-right p-3 font-medium text-gray-700">التاريخ</th>
                  <th className="text-right p-3 font-medium text-gray-700">العضو</th>
                  <th className="text-right p-3 font-medium text-gray-700">النوع</th>
                  <th className="text-right p-3 font-medium text-gray-700">المصدر</th>
                  <th className="text-right p-3 font-medium text-gray-700">المبلغ</th>
                  <th className="text-right p-3 font-medium text-gray-700">الوصف</th>
                </tr>
              </thead>
              <tbody>
                {filteredIncomes.map((income) => (
                  <tr key={income.id} className="border-b hover:bg-gray-50">
                    <td className="p-3">{formatDate(income.date)}</td>
                    <td className="p-3">
                      <div className="flex items-center">
                        <User className="w-4 h-4 ml-2 text-gray-400" />
                        {income.member?.name || 'غير محدد'}
                      </div>
                    </td>
                    <td className="p-3">
                      <Badge className={getTypeColor(income.type)}>
                        {getTypeLabel(income.type)}
                      </Badge>
                    </td>
                    <td className="p-3">{income.source}</td>
                    <td className="p-3 font-bold text-green-600">
                      {formatCurrency(income.amount)}
                    </td>
                    <td className="p-3 text-sm text-gray-600">
                      {income.description || 'غير محدد'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
