(()=>{var e={};e.id=2169,e.ids=[2169],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{N:()=>o});var s=t(13581),i=t(85663),a=t(31183);let o={providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"البريد الإلكتروني",type:"email"},password:{label:"كلمة المرور",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await a.z.user.findUnique({where:{email:e.email}});return r&&await i.Ay.compare(e.password,r.password)?{id:r.id,email:r.email,name:r.name,role:r.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>i});var s=t(96330);let i=globalThis.prisma??new s.PrismaClient},40223:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>q,routeModule:()=>d,serverHooks:()=>w,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>c});var i=t(96559),a=t(48088),o=t(37719),n=t(32190),u=t(19854),p=t(12909),l=t(31183);async function c(e,{params:r}){try{if(!await (0,u.getServerSession)(p.N))return n.NextResponse.json({error:"غير مصرح"},{status:401});let{id:e}=await r;if(!await l.z.member.findUnique({where:{id:e}}))return n.NextResponse.json({error:"العضو غير موجود"},{status:404});let[t,s,i]=await Promise.all([l.z.income.aggregate({where:{memberId:e},_sum:{amount:!0}}),l.z.income.count({where:{memberId:e}}),l.z.income.findFirst({where:{memberId:e},orderBy:{date:"desc"},select:{date:!0}})]),a={totalContributions:t._sum.amount||0,contributionsCount:s,lastContributionDate:i?.date||null};return n.NextResponse.json(a)}catch(e){return console.error("خطأ في جلب الإحصائيات السريعة:",e),n.NextResponse.json({error:"حدث خطأ في جلب الإحصائيات"},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/members/[id]/quick-stats/route",pathname:"/api/members/[id]/quick-stats",filename:"route",bundlePath:"app/api/members/[id]/quick-stats/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\[id]\\quick-stats\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:x,serverHooks:w}=d;function q(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:x})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,5663,4999,3412,580],()=>t(40223));module.exports=s})();