(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8237],{40662:(e,s,a)=>{Promise.resolve().then(a.bind(a,46107))},46107:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>b});var l=a(95155),t=a(12115),i=a(12108),r=a(35695),c=a(30285),n=a(62523),d=a(66695),o=a(26126),m=a(54165),x=a(92138),h=a(21380),p=a(29869),u=a(69074),j=a(71007),v=a(27213),g=a(47924),N=a(92657),f=a(13717),y=a(62525),w=a(73054);function b(){let{data:e}=(0,i.useSession)(),s=(0,r.useParams)(),a=(0,r.useRouter)(),b=(0,r.useSearchParams)(),C=s.id,A=b.get("type")||"activity",[k,E]=(0,t.useState)([]),[S,I]=(0,t.useState)(null),[$,_]=(0,t.useState)(!0),[O,P]=(0,t.useState)(""),[D,F]=(0,t.useState)(null),[z,L]=(0,t.useState)(!1),[R,W]=(0,t.useState)(!1),q=(0,t.useCallback)(async()=>{try{let e;_(!0);let s="/api/gallery?";if("general"===C)s+="activityId=null&folderId=null",e={id:"general",title:"الصور العامة",description:"صور غير مرتبطة بأنشطة أو مجلدات محددة",type:"general",photosCount:0};else if("folder"===A){s+="folderId=".concat(C);try{let s=await fetch("/api/gallery-folders/".concat(C));if(s.ok){let a=await s.json();e={id:C,title:a.title,description:a.description,type:"folder",photosCount:0}}else throw Error("فشل في جلب معلومات المجلد")}catch(s){console.error("خطأ في جلب معلومات المجلد:",s),e={id:C,title:"مجلد غير معروف",description:"لا يمكن جلب معلومات المجلد",type:"folder",photosCount:0}}}else{s+="activityId=".concat(C);try{let s=await fetch("/api/activities/".concat(C));if(s.ok){let a=await s.json();e={id:C,title:a.title,description:a.description,type:"activity",photosCount:0}}else throw Error("فشل في جلب معلومات النشاط")}catch(s){console.error("خطأ في جلب معلومات النشاط:",s),e={id:C,title:"نشاط غير معروف",description:"لا يمكن جلب معلومات النشاط",type:"activity",photosCount:0}}}I(e),O&&(s+="&search=".concat(encodeURIComponent(O)));let a=await fetch(s);if(!a.ok)throw Error("فشل في جلب الصور");let l=await a.json();E(l.photos||[]),I(e=>{var s;return e?{...e,photosCount:(null==(s=l.photos)?void 0:s.length)||0}:null})}catch(e){console.error("خطأ في جلب بيانات المجلد:",e),E([])}finally{_(!1)}},[O,C,A]);(0,t.useEffect)(()=>{e&&C&&q()},[O,e,C,A]);let Z=e=>{F(e),L(!0)},G=async e=>{if(confirm("هل أنت متأكد من حذف هذه الصورة؟"))try{if(!(await fetch("/api/gallery/".concat(e),{method:"DELETE"})).ok)throw Error("فشل في حذف الصورة");q()}catch(e){console.error("خطأ في حذف الصورة:",e),alert("حدث خطأ في حذف الصورة")}},J=(null==e?void 0:e.user.role)!=="VIEWER",M=(null==e?void 0:e.user.role)==="ADMIN";return $?(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"animate-pulse",children:[(0,l.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3 mb-2"}),(0,l.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]}),(0,l.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:[...Array(8)].map((e,s)=>(0,l.jsxs)("div",{className:"animate-pulse",children:[(0,l.jsx)("div",{className:"aspect-square bg-gray-200 rounded-lg mb-3"}),(0,l.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),(0,l.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]},s))})]}):(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4 space-x-reverse",children:[(0,l.jsxs)(c.$,{variant:"outline",onClick:()=>a.push("/dashboard/gallery"),className:"flex items-center",children:[(0,l.jsx)(x.A,{className:"w-4 h-4 ml-2"}),"العودة للمعرض"]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,l.jsx)(h.A,{className:"w-6 h-6 text-diwan-600"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:(null==S?void 0:S.title)||"مجلد الصور"}),(null==S?void 0:S.description)&&(0,l.jsx)("p",{className:"text-gray-600",children:S.description})]})]})]}),(0,l.jsx)("div",{className:"flex space-x-2 space-x-reverse",children:J&&(0,l.jsxs)(c.$,{onClick:()=>W(!0),className:"bg-diwan-600 hover:bg-diwan-700",children:[(0,l.jsx)(p.A,{className:"w-4 h-4 ml-2"}),"إضافة صور"]})})]}),(0,l.jsx)(d.Zp,{children:(0,l.jsx)(d.Wu,{className:"p-4",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4 space-x-reverse",children:[(0,l.jsx)(o.E,{variant:"secondary",className:"".concat((null==S?void 0:S.type)==="activity"?"bg-green-100 text-green-700":(null==S?void 0:S.type)==="folder"?"bg-purple-100 text-purple-700":"bg-blue-100 text-blue-700"),children:(null==S?void 0:S.type)==="activity"?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(u.A,{className:"w-3 h-3 ml-1"}),"نشاط"]}):(null==S?void 0:S.type)==="folder"?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(h.A,{className:"w-3 h-3 ml-1"}),"مجلد"]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(j.A,{className:"w-3 h-3 ml-1"}),"عام"]})}),(0,l.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,l.jsx)(v.A,{className:"w-4 h-4 ml-1"}),k.length," صورة"]})]}),(0,l.jsxs)("div",{className:"relative w-64",children:[(0,l.jsx)(g.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,l.jsx)(n.p,{placeholder:"البحث في الصور...",value:O,onChange:e=>P(e.target.value),className:"pr-10"})]})]})})}),(0,l.jsx)(d.Zp,{children:(0,l.jsx)(d.Wu,{className:"p-6",children:0===k.length?(0,l.jsxs)("div",{className:"text-center py-12",children:[(0,l.jsx)(v.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,l.jsx)("p",{className:"text-gray-500",children:"لا توجد صور في هذا المجلد"}),J&&(0,l.jsxs)(c.$,{onClick:()=>W(!0),className:"mt-4",variant:"outline",children:[(0,l.jsx)(p.A,{className:"w-4 h-4 ml-2"}),"إضافة أول صورة"]})]}):(0,l.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:k.map(e=>(0,l.jsxs)("div",{className:"group relative",children:[(0,l.jsx)("div",{className:"aspect-square overflow-hidden rounded-lg bg-gray-100",children:(0,l.jsx)("img",{src:e.imagePath,alt:e.title,className:"w-full h-full object-cover group-hover:scale-105 transition-transform duration-200 cursor-pointer",onClick:()=>Z(e)})}),(0,l.jsxs)("div",{className:"mt-3",children:[(0,l.jsx)("h3",{className:"text-sm font-medium text-gray-900 truncate",children:e.title}),e.description&&(0,l.jsx)("p",{className:"text-xs text-gray-500 truncate mt-1",children:e.description}),(0,l.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,l.jsx)("div",{className:"text-xs text-gray-500",children:e.uploader.name}),(0,l.jsxs)("div",{className:"flex space-x-1 space-x-reverse",children:[(0,l.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>Z(e),className:"text-blue-600 hover:text-blue-700",title:"عرض",children:(0,l.jsx)(N.A,{className:"w-3 h-3"})}),J&&(0,l.jsx)(c.$,{variant:"ghost",size:"sm",className:"text-green-600 hover:text-green-700",title:"تعديل",children:(0,l.jsx)(f.A,{className:"w-3 h-3"})}),M&&(0,l.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>G(e.id),className:"text-red-600 hover:text-red-700",title:"حذف",children:(0,l.jsx)(y.A,{className:"w-3 h-3"})})]})]})]})]},e.id))})})}),(0,l.jsx)(m.lG,{open:z,onOpenChange:L,children:(0,l.jsxs)(m.Cf,{className:"max-w-4xl",children:[(0,l.jsx)(m.c7,{children:(0,l.jsx)(m.L3,{children:null==D?void 0:D.title})}),D&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("div",{className:"aspect-video overflow-hidden rounded-lg bg-gray-100",children:(0,l.jsx)("img",{src:D.imagePath,alt:D.title,className:"w-full h-full object-contain"})}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-medium mb-2",children:"تفاصيل الصورة"}),(0,l.jsxs)("div",{className:"space-y-1 text-gray-600",children:[(0,l.jsxs)("p",{children:[(0,l.jsx)("span",{className:"font-medium",children:"الوصف:"})," ",D.description||"لا يوجد"]}),(0,l.jsxs)("p",{children:[(0,l.jsx)("span",{className:"font-medium",children:"رفعت بواسطة:"})," ",D.uploader.name]}),(0,l.jsxs)("p",{children:[(0,l.jsx)("span",{className:"font-medium",children:"تاريخ الرفع:"})," ",new Date(D.uploadedAt).toLocaleDateString("ar-JO")]}),D.activity&&(0,l.jsxs)("p",{children:[(0,l.jsx)("span",{className:"font-medium",children:"النشاط:"})," ",D.activity.title]}),D.folder&&(0,l.jsxs)("p",{children:[(0,l.jsx)("span",{className:"font-medium",children:"المجلد:"})," ",D.folder.title]})]})]})})]})]})}),(0,l.jsx)(w.A,{open:R,onOpenChange:W,onSuccess:q,defaultFolderId:"folder"===A?C:void 0,defaultActivityId:"activity"===A?C:void 0})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[1778,2108,3942,5217,8130,3068,5110,8441,1684,7358],()=>s(40662)),_N_E=e.O()}]);