(()=>{var e={};e.id=5949,e.ids=[5949],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},49333:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{POST:()=>i});var a=t(96559),n=t(48088),o=t(37719),u=t(32190);async function i(){try{let e=u.NextResponse.json({success:!0,message:"تم تسجيل الخروج بنجاح"});return e.cookies.set("member-token","",{httpOnly:!0,secure:!0,sameSite:"lax",maxAge:0}),e}catch(e){return console.error("خطأ في تسجيل خروج العضو:",e),u.NextResponse.json({error:"حدث خطأ في تسجيل الخروج"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/member/signout/route",pathname:"/api/auth/member/signout",filename:"route",bundlePath:"app/api/auth/member/signout/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\member\\signout\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:m}=p;function x(){return(0,o.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580],()=>t(49333));module.exports=s})();