import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import jwt from 'jsonwebtoken'

// API لجلب المعرض للأعضاء
export async function GET(request: NextRequest) {
  try {
    // التحقق من المصادقة
    const token = request.cookies.get('member-token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'غير مصرح - يجب تسجيل الدخول' },
        { status: 401 }
      )
    }

    let decoded: { userId: string }
    try {
      decoded = jwt.verify(token, process.env.NEXTAUTH_SECRET || 'fallback-secret')
    } catch {
      return NextResponse.json(
        { error: 'رمز المصادقة غير صالح' },
        { status: 401 }
      )
    }

    // التحقق من وجود userId في التوكن
    if (!decoded.userId) {
      return NextResponse.json(
        { error: 'جلسة غير صالحة - معرف المستخدم مفقود' },
        { status: 401 }
      )
    }

    // جلب بيانات المستخدم والعضو
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      include: {
        memberUser: {
          include: {
            member: true
          }
        }
      }
    })

    if (!user || !user.memberUser) {
      return NextResponse.json(
        { error: 'المستخدم غير موجود' },
        { status: 404 }
      )
    }

    // التحقق من أن الحساب مفعل
    if (!user.memberUser.isActive) {
      return NextResponse.json(
        { error: 'حسابك غير مفعل' },
        { status: 403 }
      )
    }

    // التحقق من صلاحية عرض المعرض
    if (!user.memberUser.canViewGallery) {
      return NextResponse.json(
        { error: 'ليس لديك صلاحية لعرض المعرض' },
        { status: 403 }
      )
    }

    // جلب المجلدات مع الصور
    const folders = await prisma.galleryFolder.findMany({
      include: {
        creator: {
          select: {
            name: true,
          },
        },
        photos: {
          take: 1,
          orderBy: { createdAt: 'desc' },
        },
        _count: {
          select: {
            photos: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    })

    // جلب الصور غير المجمعة في مجلدات
    const unfolderPhotos = await prisma.galleryPhoto.findMany({
      where: {
        folderId: null
      },
      include: {
        uploader: {
          select: {
            name: true,
          },
        },
        activity: {
          select: {
            id: true,
            title: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    })

    return NextResponse.json({
      folders,
      unfolderPhotos,
      totalFolders: folders.length,
      totalPhotos: folders.reduce((sum, folder) => sum + folder._count.photos, 0) + unfolderPhotos.length
    })
  } catch (error) {
    console.error('خطأ في جلب المعرض للعضو:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في جلب المعرض' },
      { status: 500 }
    )
  }
}
