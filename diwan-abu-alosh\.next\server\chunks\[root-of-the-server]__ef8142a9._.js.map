{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Cred<PERSON><PERSON><PERSON><PERSON>ider from 'next-auth/providers/credentials'\nimport bcrypt from 'bcryptjs'\nimport { prisma } from './prisma'\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'البريد الإلكتروني', type: 'email' },\n        password: { label: 'كلمة المرور', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          role: user.role,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAqB,MAAM;gBAAQ;gBACnD,UAAU;oBAAE,OAAO;oBAAe,MAAM;gBAAW;YACrD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;YACxB;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;YAChC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/lib/validations.ts"], "sourcesContent": ["import { z } from 'zod'\n\n// تحقق من صحة بيانات المستخدم\nexport const userSchema = z.object({\n  name: z.string().min(2, 'الاسم يجب أن يكون على الأقل حرفين'),\n  email: z.string().email('البريد الإلكتروني غير صحيح'),\n  password: z.string().min(6, 'كلمة المرور يجب أن تكون على الأقل 6 أحرف'),\n  role: z.enum(['ADMIN', 'DATA_ENTRY', 'VIEWER', 'MEMBER_VIEWER', 'GALLERY_VIEWER', 'MEMBER']).default('VIEWER'),\n})\n\n// تحقق من صحة بيانات العضو\nexport const memberSchema = z.object({\n  name: z.string().min(2, 'الاسم يجب أن يكون على الأقل حرفين'),\n  phone: z.string().optional().or(z.literal('')).or(z.literal(null)),\n  email: z.union([\n    z.string().email('البريد الإلكتروني غير صحيح'),\n    z.literal(''),\n    z.literal(null),\n    z.undefined()\n  ]).optional(),\n  address: z.string().optional().or(z.literal('')).or(z.literal(null)),\n  notes: z.string().optional().or(z.literal('')).or(z.literal(null)),\n  photo: z.string().optional().or(z.literal('')).or(z.literal(null)),\n  status: z.enum(['ACTIVE', 'LATE', 'INACTIVE', 'SUSPENDED', 'ARCHIVED']).default('ACTIVE'),\n})\n\n// تحقق من صحة بيانات الإيراد\nexport const incomeSchema = z.object({\n  amount: z.number().positive('المبلغ يجب أن يكون أكبر من صفر'),\n  date: z.date(),\n  source: z.string().min(1, 'مصدر الإيراد مطلوب'),\n  type: z.enum(['SUBSCRIPTION', 'DONATION', 'EVENT', 'OTHER']).default('SUBSCRIPTION'),\n  description: z.string().optional().nullable(),\n  notes: z.string().optional().nullable(),\n  memberId: z.string().optional().nullable(),\n})\n\n// تحقق من صحة بيانات المصروف\nexport const expenseSchema = z.object({\n  amount: z.number().positive('المبلغ يجب أن يكون أكبر من صفر'),\n  date: z.date(),\n  description: z.string().min(1, 'وصف المصروف مطلوب'),\n  category: z.enum(['MEETINGS', 'EVENTS', 'MAINTENANCE', 'SOCIAL', 'GENERAL']).default('GENERAL'),\n  recipient: z.string().optional().nullable(),\n  notes: z.string().optional().nullable(),\n})\n\n// تحقق من صحة بيانات النشاط\nexport const activitySchema = z.object({\n  title: z.string().min(1, 'عنوان النشاط مطلوب'),\n  description: z.string().optional(),\n  startDate: z.date(),\n  endDate: z.date().optional(),\n  location: z.string().optional(),\n  organizers: z.string().optional(),\n  participantIds: z.array(z.string()).optional(),\n})\n\n// تحقق من صحة بيانات تسجيل الدخول\nexport const loginSchema = z.object({\n  email: z.string().email('البريد الإلكتروني غير صحيح'),\n  password: z.string().min(1, 'كلمة المرور مطلوبة'),\n})\n\n// أنواع TypeScript المستخرجة من المخططات\nexport type UserInput = z.infer<typeof userSchema>\nexport type MemberInput = z.infer<typeof memberSchema>\nexport type IncomeInput = z.infer<typeof incomeSchema>\nexport type ExpenseInput = z.infer<typeof expenseSchema>\nexport type ActivityInput = z.infer<typeof activitySchema>\nexport type LoginInput = z.infer<typeof loginSchema>\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;;AAGO,MAAM,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,MAAM,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAS;QAAc;QAAU;QAAiB;QAAkB;KAAS,EAAE,OAAO,CAAC;AACvG;AAGO,MAAM,eAAe,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,EAAE,CAAC,mLAAA,CAAA,IAAC,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,mLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAC5D,OAAO,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC;QACb,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;QACjB,mLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;QACV,mLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;QACV,mLAAA,CAAA,IAAC,CAAC,SAAS;KACZ,EAAE,QAAQ;IACX,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,EAAE,CAAC,mLAAA,CAAA,IAAC,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,mLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAC9D,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,EAAE,CAAC,mLAAA,CAAA,IAAC,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,mLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAC5D,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,EAAE,CAAC,mLAAA,CAAA,IAAC,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,mLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAC5D,QAAQ,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAU;QAAQ;QAAY;QAAa;KAAW,EAAE,OAAO,CAAC;AAClF;AAGO,MAAM,eAAe,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC5B,MAAM,mLAAA,CAAA,IAAC,CAAC,IAAI;IACZ,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,MAAM,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAgB;QAAY;QAAS;KAAQ,EAAE,OAAO,CAAC;IACrE,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC3C,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACrC,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;AAC1C;AAGO,MAAM,gBAAgB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC5B,MAAM,mLAAA,CAAA,IAAC,CAAC,IAAI;IACZ,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,UAAU,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAY;QAAU;QAAe;QAAU;KAAU,EAAE,OAAO,CAAC;IACrF,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACzC,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;AACvC;AAGO,MAAM,iBAAiB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,WAAW,mLAAA,CAAA,IAAC,CAAC,IAAI;IACjB,SAAS,mLAAA,CAAA,IAAC,CAAC,IAAI,GAAG,QAAQ;IAC1B,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,gBAAgB,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;AAC9C;AAGO,MAAM,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B", "debugId": null}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/app/api/members/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\nimport { memberSchema } from '@/lib/validations'\n\n// GET - جلب جميع الأعضاء مع البحث والتصفية\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    if (!session) {\n      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })\n    }\n\n    const { searchParams } = new URL(request.url)\n    const search = searchParams.get('search') || ''\n    const status = searchParams.get('status') || 'all'\n    const page = parseInt(searchParams.get('page') || '1')\n    const limit = parseInt(searchParams.get('limit') || '10')\n    const includeIncomes = searchParams.get('includeIncomes') === 'true'\n    const skip = (page - 1) * limit\n\n    // بناء شروط البحث\n    const where: Record<string, unknown> = {}\n    \n    if (search) {\n      where.OR = [\n        { name: { contains: search } },\n        { phone: { contains: search } },\n        { address: { contains: search } },\n      ]\n    }\n\n    if (status !== 'all') {\n      where.status = status\n    }\n\n    // تحديد ما يجب تضمينه\n    const includeOptions: Record<string, unknown> = {\n      _count: {\n        select: {\n          incomes: true,\n        },\n      },\n    }\n\n    if (includeIncomes) {\n      includeOptions.incomes = {\n        select: {\n          id: true,\n          amount: true,\n          date: true,\n          source: true,\n          type: true,\n          description: true,\n        },\n        orderBy: { date: 'desc' },\n      }\n    } else {\n      includeOptions.incomes = {\n        select: {\n          amount: true,\n        },\n      }\n    }\n\n    // جلب الأعضاء مع العد الكلي\n    const [members, total] = await Promise.all([\n      prisma.member.findMany({\n        where,\n        skip,\n        take: limit,\n        orderBy: { createdAt: 'desc' },\n        include: includeOptions,\n      }),\n      prisma.member.count({ where }),\n    ])\n\n    return NextResponse.json({\n      members,\n      pagination: {\n        page,\n        limit,\n        total,\n        pages: Math.ceil(total / limit),\n      },\n    })\n  } catch (error) {\n    console.error('خطأ في جلب الأعضاء:', error)\n    return NextResponse.json(\n      { error: 'حدث خطأ في جلب الأعضاء' },\n      { status: 500 }\n    )\n  }\n}\n\n// POST - إضافة عضو جديد\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    if (!session) {\n      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })\n    }\n\n    // التحقق من الصلاحيات\n    if (session.user.role === 'VIEWER') {\n      return NextResponse.json(\n        { error: 'ليس لديك صلاحية لإضافة الأعضاء' },\n        { status: 403 }\n      )\n    }\n\n    const body = await request.json()\n    \n    // التحقق من صحة البيانات\n    const validatedData = memberSchema.parse(body)\n\n    // التحقق من عدم تكرار البريد الإلكتروني\n    if (validatedData.email) {\n      const existingMember = await prisma.member.findFirst({\n        where: { email: validatedData.email },\n      })\n      if (existingMember) {\n        return NextResponse.json(\n          { error: 'البريد الإلكتروني مستخدم بالفعل' },\n          { status: 400 }\n        )\n      }\n    }\n\n    // إنشاء العضو الجديد\n    const member = await prisma.member.create({\n      data: {\n        ...validatedData,\n        createdById: session.user.id,\n      },\n    })\n\n    return NextResponse.json(member, { status: 201 })\n  } catch (error: unknown) {\n    console.error('خطأ في إضافة العضو:', error)\n    \n    if (error.name === 'ZodError') {\n      return NextResponse.json(\n        { error: 'بيانات غير صحيحة', details: error.errors },\n        { status: 400 }\n      )\n    }\n\n    return NextResponse.json(\n      { error: 'حدث خطأ في إضافة العضو' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAW,GAAG;gBAAE,QAAQ;YAAI;QAChE;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,iBAAiB,aAAa,GAAG,CAAC,sBAAsB;QAC9D,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,kBAAkB;QAClB,MAAM,QAAiC,CAAC;QAExC,IAAI,QAAQ;YACV,MAAM,EAAE,GAAG;gBACT;oBAAE,MAAM;wBAAE,UAAU;oBAAO;gBAAE;gBAC7B;oBAAE,OAAO;wBAAE,UAAU;oBAAO;gBAAE;gBAC9B;oBAAE,SAAS;wBAAE,UAAU;oBAAO;gBAAE;aACjC;QACH;QAEA,IAAI,WAAW,OAAO;YACpB,MAAM,MAAM,GAAG;QACjB;QAEA,sBAAsB;QACtB,MAAM,iBAA0C;YAC9C,QAAQ;gBACN,QAAQ;oBACN,SAAS;gBACX;YACF;QACF;QAEA,IAAI,gBAAgB;YAClB,eAAe,OAAO,GAAG;gBACvB,QAAQ;oBACN,IAAI;oBACJ,QAAQ;oBACR,MAAM;oBACN,QAAQ;oBACR,MAAM;oBACN,aAAa;gBACf;gBACA,SAAS;oBAAE,MAAM;gBAAO;YAC1B;QACF,OAAO;YACL,eAAe,OAAO,GAAG;gBACvB,QAAQ;oBACN,QAAQ;gBACV;YACF;QACF;QAEA,4BAA4B;QAC5B,MAAM,CAAC,SAAS,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;YACzC,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;gBACrB;gBACA;gBACA,MAAM;gBACN,SAAS;oBAAE,WAAW;gBAAO;gBAC7B,SAAS;YACX;YACA,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,KAAK,CAAC;gBAAE;YAAM;SAC7B;QAED,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA,YAAY;gBACV;gBACA;gBACA;gBACA,OAAO,KAAK,IAAI,CAAC,QAAQ;YAC3B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAW,GAAG;gBAAE,QAAQ;YAAI;QAChE;QAEA,sBAAsB;QACtB,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,UAAU;YAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiC,GAC1C;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,yBAAyB;QACzB,MAAM,gBAAgB,2HAAA,CAAA,eAAY,CAAC,KAAK,CAAC;QAEzC,wCAAwC;QACxC,IAAI,cAAc,KAAK,EAAE;YACvB,MAAM,iBAAiB,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,SAAS,CAAC;gBACnD,OAAO;oBAAE,OAAO,cAAc,KAAK;gBAAC;YACtC;YACA,IAAI,gBAAgB;gBAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAkC,GAC3C;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,qBAAqB;QACrB,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,MAAM,CAAC;YACxC,MAAM;gBACJ,GAAG,aAAa;gBAChB,aAAa,QAAQ,IAAI,CAAC,EAAE;YAC9B;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,QAAQ;YAAE,QAAQ;QAAI;IACjD,EAAE,OAAO,OAAgB;QACvB,QAAQ,KAAK,CAAC,uBAAuB;QAErC,IAAI,MAAM,IAAI,KAAK,YAAY;YAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAoB,SAAS,MAAM,MAAM;YAAC,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}