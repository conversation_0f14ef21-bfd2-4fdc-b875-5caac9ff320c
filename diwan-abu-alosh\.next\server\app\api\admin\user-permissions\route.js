(()=>{var e={};e.id=7536,e.ids=[7536],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,s,r)=>{"use strict";r.d(s,{N:()=>n});var t=r(13581),i=r(85663),a=r(31183);let n={providers:[(0,t.A)({name:"credentials",credentials:{email:{label:"البريد الإلكتروني",type:"email"},password:{label:"كلمة المرور",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let s=await a.z.user.findUnique({where:{email:e.email}});return s&&await i.Ay.compare(e.password,s.password)?{id:s.id,email:s.email,name:s.name,role:s.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:s})=>(s&&(e.role=s.role),e),session:async({session:e,token:s})=>(s&&(e.user.id=s.sub,e.user.role=s.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,s,r)=>{"use strict";r.d(s,{z:()=>i});var t=r(96330);let i=globalThis.prisma??new t.PrismaClient},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85836:(e,s,r)=>{"use strict";r.r(s),r.d(s,{patchFetch:()=>f,routeModule:()=>m,serverHooks:()=>g,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>w});var t={};r.r(t),r.d(t,{GET:()=>c,POST:()=>d});var i=r(96559),a=r(48088),n=r(37719),u=r(32190),o=r(19854),p=r(12909),l=r(31183);async function c(e){try{let s=await (0,o.getServerSession)(p.N);if(!s?.user||"ADMIN"!==s.user.role)return u.NextResponse.json({message:"غير مصرح لك بالوصول"},{status:403});let{searchParams:r}=new URL(e.url),t=r.get("userId");if(!t)return u.NextResponse.json({message:"معرف المستخدم مطلوب"},{status:400});let i=await l.z.userPermission.findFirst({where:{userId:t},include:{user:{select:{id:!0,name:!0,email:!0,role:!0}},specificMember:{select:{id:!0,name:!0}}}});return u.NextResponse.json(i)}catch(e){return console.error("خطأ في جلب صلاحيات المستخدم:",e),u.NextResponse.json({message:"حدث خطأ في الخادم"},{status:500})}}async function d(e){try{let s=await (0,o.getServerSession)(p.N);if(!s?.user||"ADMIN"!==s.user.role)return u.NextResponse.json({message:"غير مصرح لك بالوصول"},{status:403});let{userId:r,permissions:t}=await e.json();if(!r||!t)return u.NextResponse.json({message:"معرف المستخدم والصلاحيات مطلوبان"},{status:400});if(!await l.z.user.findUnique({where:{id:r}}))return u.NextResponse.json({message:"المستخدم غير موجود"},{status:404});let i=await l.z.userPermission.findFirst({where:{userId:r}}),a=i?await l.z.userPermission.update({where:{id:i.id},data:t,include:{user:{select:{id:!0,name:!0,email:!0,role:!0}},specificMember:{select:{id:!0,name:!0}}}}):await l.z.userPermission.create({data:{userId:r,...t},include:{user:{select:{id:!0,name:!0,email:!0,role:!0}},specificMember:{select:{id:!0,name:!0}}}});return u.NextResponse.json(a)}catch(e){return console.error("خطأ في تحديث صلاحيات المستخدم:",e),u.NextResponse.json({message:"حدث خطأ في الخادم"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/admin/user-permissions/route",pathname:"/api/admin/user-permissions",filename:"route",bundlePath:"app/api/admin/user-permissions/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\admin\\user-permissions\\route.ts",nextConfigOutput:"standalone",userland:t}),{workAsyncStorage:x,workUnitAsyncStorage:w,serverHooks:g}=m;function f(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:w})}},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4243,5663,4999,3412,580],()=>r(85836));module.exports=t})();