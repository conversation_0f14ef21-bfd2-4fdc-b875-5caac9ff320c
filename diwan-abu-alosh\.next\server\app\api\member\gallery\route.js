(()=>{var e={};e.id=2687,e.ids=[2687],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>o});var s=t(96330);let o=globalThis.prisma??new s.PrismaClient},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48629:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>d,serverHooks:()=>y,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>p});var o=t(96559),a=t(48088),n=t(37719),i=t(32190),u=t(31183),l=t(43205),c=t.n(l);async function p(e){try{let r,t=e.cookies.get("member-token")?.value;if(!t)return i.NextResponse.json({error:"غير مصرح - يجب تسجيل الدخول"},{status:401});try{r=c().verify(t,process.env.NEXTAUTH_SECRET||"fallback-secret")}catch{return i.NextResponse.json({error:"رمز المصادقة غير صالح"},{status:401})}if(!r.userId)return i.NextResponse.json({error:"جلسة غير صالحة - معرف المستخدم مفقود"},{status:401});let s=await u.z.user.findUnique({where:{id:r.userId},include:{memberUser:{include:{member:!0}}}});if(!s||!s.memberUser)return i.NextResponse.json({error:"المستخدم غير موجود"},{status:404});if(!s.memberUser.isActive)return i.NextResponse.json({error:"حسابك غير مفعل"},{status:403});if(!s.memberUser.canViewGallery)return i.NextResponse.json({error:"ليس لديك صلاحية لعرض المعرض"},{status:403});let o=await u.z.galleryFolder.findMany({include:{creator:{select:{name:!0}},photos:{take:1,orderBy:{createdAt:"desc"}},_count:{select:{photos:!0}}},orderBy:{createdAt:"desc"}}),a=await u.z.galleryPhoto.findMany({where:{folderId:null},include:{uploader:{select:{name:!0}},activity:{select:{id:!0,title:!0}}},orderBy:{createdAt:"desc"}});return i.NextResponse.json({folders:o,unfolderPhotos:a,totalFolders:o.length,totalPhotos:o.reduce((e,r)=>e+r._count.photos,0)+a.length})}catch(e){return console.error("خطأ في جلب المعرض للعضو:",e),i.NextResponse.json({error:"حدث خطأ في جلب المعرض"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/member/gallery/route",pathname:"/api/member/gallery",filename:"route",bundlePath:"app/api/member/gallery/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\member\\gallery\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:x,serverHooks:y}=d;function h(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:x})}},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,3205],()=>t(48629));module.exports=s})();