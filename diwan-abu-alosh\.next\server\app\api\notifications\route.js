(()=>{var e={};e.id=2170,e.ids=[2170],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>n});var s=r(13581),a=r(85663),i=r(31183);let n={providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"البريد الإلكتروني",type:"email"},password:{label:"كلمة المرور",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let t=await i.z.user.findUnique({where:{email:e.email}});return t&&await a.Ay.compare(e.password,t.password)?{id:t.id,email:t.email,name:t.name,role:t.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},13294:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>R,routeModule:()=>m,serverHooks:()=>y,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{DELETE:()=>g,GET:()=>d,POST:()=>x,PUT:()=>p});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),u=r(19854),c=r(12909);let l=[{id:"1",title:"عضو جديد انضم للديوان",message:"انضم محمد أحمد إلى ديوان آل أبو علوش",type:"success",category:"member",isRead:!1,createdAt:new Date(Date.now()-72e5).toISOString(),actionUrl:"/dashboard/members",relatedId:"member-1"},{id:"2",title:"إيراد جديد مسجل",message:"تم تسجيل إيراد بقيمة 500 دينار أردني من اشتراكات الأعضاء",type:"info",category:"financial",isRead:!1,createdAt:new Date(Date.now()-144e5).toISOString(),actionUrl:"/dashboard/incomes",relatedId:"income-1"},{id:"3",title:"مصروف جديد",message:"تم تسجيل مصروف بقيمة 200 دينار أردني لصيانة المقر",type:"warning",category:"financial",isRead:!0,createdAt:new Date(Date.now()-864e5).toISOString(),actionUrl:"/dashboard/expenses",relatedId:"expense-1"},{id:"4",title:"تحديث النظام",message:"تم تحديث نظام إدارة الديوان إلى الإصدار الجديد",type:"success",category:"system",isRead:!0,createdAt:new Date(Date.now()-1728e5).toISOString(),relatedId:"system-1"},{id:"5",title:"تذكير بالاجتماع",message:"اجتماع مجلس الإدارة غداً الساعة 7:00 مساءً",type:"info",category:"activity",isRead:!1,createdAt:new Date(Date.now()-216e5).toISOString(),actionUrl:"/dashboard/activities",relatedId:"activity-1"},{id:"6",title:"خطأ في النظام",message:"حدث خطأ في تحميل بعض البيانات، يرجى المحاولة مرة أخرى",type:"error",category:"system",isRead:!1,createdAt:new Date(Date.now()-18e5).toISOString(),relatedId:"error-1"}];async function d(e){try{if(!await (0,u.getServerSession)(c.N))return o.NextResponse.json({error:"غير مصرح"},{status:401});let{searchParams:t}=new URL(e.url),r=t.get("search")||"",s=t.get("filter")||"all",a=t.get("category")||"all",i=[...l];return r&&(i=i.filter(e=>e.title.includes(r)||e.message.includes(r))),"read"===s?i=i.filter(e=>e.isRead):"unread"===s&&(i=i.filter(e=>!e.isRead)),"all"!==a&&(i=i.filter(e=>e.category===a)),i.sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime()),o.NextResponse.json(i)}catch(e){return console.error("خطأ في جلب الإشعارات:",e),o.NextResponse.json({error:"حدث خطأ في جلب الإشعارات"},{status:500})}}async function p(e){try{if(!await (0,u.getServerSession)(c.N))return o.NextResponse.json({error:"غير مصرح"},{status:401});let{notificationId:t,isRead:r,markAllAsRead:s}=await e.json();if(s)return l.forEach(e=>{e.isRead=!0}),o.NextResponse.json({message:"تم تحديد جميع الإشعارات كمقروءة"});if(t){let e=l.find(e=>e.id===t);if(e)return e.isRead=r,o.NextResponse.json({message:"تم تحديث حالة الإشعار"});return o.NextResponse.json({error:"الإشعار غير موجود"},{status:404})}return o.NextResponse.json({error:"بيانات غير صحيحة"},{status:400})}catch(e){return console.error("خطأ في تحديث الإشعار:",e),o.NextResponse.json({error:"حدث خطأ في تحديث الإشعار"},{status:500})}}async function g(e){try{if(!await (0,u.getServerSession)(c.N))return o.NextResponse.json({error:"غير مصرح"},{status:401});let{searchParams:t}=new URL(e.url),r=t.get("id");if(!r)return o.NextResponse.json({error:"معرف الإشعار مطلوب"},{status:400});let s=l.findIndex(e=>e.id===r);if(-1!==s)return l.splice(s,1),o.NextResponse.json({message:"تم حذف الإشعار"});return o.NextResponse.json({error:"الإشعار غير موجود"},{status:404})}catch(e){return console.error("خطأ في حذف الإشعار:",e),o.NextResponse.json({error:"حدث خطأ في حذف الإشعار"},{status:500})}}async function x(e){try{let t=await (0,u.getServerSession)(c.N);if(!t)return o.NextResponse.json({error:"غير مصرح"},{status:401});if("VIEWER"===t.user.role)return o.NextResponse.json({error:"ليس لديك صلاحية لإضافة الإشعارات"},{status:403});let{title:r,message:s,type:a,category:i,actionUrl:n,relatedId:d}=await e.json();if(!r||!s||!a||!i)return o.NextResponse.json({error:"العنوان والرسالة والنوع والفئة مطلوبة"},{status:400});let p={id:Date.now().toString(),title:r,message:s,type:a,category:i,isRead:!1,createdAt:new Date().toISOString(),actionUrl:n,relatedId:d};return l.unshift(p),o.NextResponse.json(p,{status:201})}catch(e){return console.error("خطأ في إضافة الإشعار:",e),o.NextResponse.json({error:"حدث خطأ في إضافة الإشعار"},{status:500})}}let m=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/notifications/route",pathname:"/api/notifications",filename:"route",bundlePath:"app/api/notifications/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\notifications\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:f,serverHooks:y}=m;function R(){return(0,n.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:f})}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});var s=r(96330);let a=globalThis.prisma??new s.PrismaClient},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,5663,4999,3412,580],()=>r(13294));module.exports=s})();