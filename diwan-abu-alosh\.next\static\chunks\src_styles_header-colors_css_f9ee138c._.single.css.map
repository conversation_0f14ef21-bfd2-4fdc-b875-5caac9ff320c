{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/header-colors.css"], "sourcesContent": ["/* أنماط الترويسات بالألوان الجديدة */\n\n/* الترويسات الأساسية - كحلي غامق #191970 */\nh1, h2, h3, h4, h5, h6 {\n  color: #191970 !important;\n  font-weight: 600 !important;\n}\n\n/* ترويسات الصفحات */\n.page-title,\n.page-header h1,\n.page-header h2 {\n  color: #191970 !important;\n  font-weight: 700 !important;\n}\n\n/* ترويسات البطاقات */\n.card-header,\n.card-title,\n.diwan-card h1,\n.diwan-card h2,\n.diwan-card h3 {\n  color: #191970 !important;\n  font-weight: 600 !important;\n}\n\n/* ترويسات الجداول */\n.table th,\n.table thead th,\ntable th {\n  background-color: #191970 !important;\n  color: white !important;\n  font-weight: 600 !important;\n  border-color: #191970 !important;\n}\n\n/* ترويسات النوافذ المنبثقة */\n.modal-header,\n.modal-title,\n.dialog-header,\n.dialog-title {\n  background-color: #191970 !important;\n  color: white !important;\n  font-weight: 600 !important;\n}\n\n/* ترويسات الشريط العلوي */\n.header,\n.navbar,\n.top-header {\n  background-color: #191970 !important;\n  color: white !important;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;\n}\n\n.header h1,\n.header h2,\n.navbar h1,\n.navbar h2 {\n  color: white !important;\n}\n\n/* ترويسات بديلة - عنابي #800020 */\n.header-alt,\n.navbar-alt,\n.secondary-header {\n  background-color: #800020 !important;\n  color: white !important;\n}\n\n.header-alt h1,\n.header-alt h2,\n.navbar-alt h1,\n.navbar-alt h2,\n.secondary-header h1,\n.secondary-header h2 {\n  color: white !important;\n}\n\n/* ترويسات الأقسام */\n.section-header,\n.section-title {\n  color: #191970 !important;\n  font-weight: 700 !important;\n  border-bottom: 2px solid #191970 !important;\n  padding-bottom: 0.5rem !important;\n  margin-bottom: 1rem !important;\n}\n\n/* ترويسات فرعية */\n.sub-header,\n.sub-title {\n  color: #800020 !important;\n  font-weight: 600 !important;\n}\n\n/* ترويسات الإحصائيات */\n.stats-header,\n.stats-title {\n  color: #191970 !important;\n  font-weight: 700 !important;\n  text-align: center !important;\n}\n\n/* ترويسات النماذج */\n.form-header,\n.form-title {\n  color: #191970 !important;\n  font-weight: 600 !important;\n  margin-bottom: 1rem !important;\n}\n\n/* ترويسات التقارير */\n.report-header,\n.report-title {\n  background-color: #191970 !important;\n  color: white !important;\n  padding: 1rem !important;\n  text-align: center !important;\n  font-weight: 700 !important;\n  border-radius: 0.5rem 0.5rem 0 0 !important;\n}\n\n/* ترويسات الإعدادات */\n.settings-header,\n.settings-title {\n  color: #191970 !important;\n  font-weight: 600 !important;\n  border-bottom: 1px solid #e5e7eb !important;\n  padding-bottom: 0.5rem !important;\n}\n\n/* ترويسات المعرض */\n.gallery-header,\n.gallery-title {\n  color: #191970 !important;\n  font-weight: 700 !important;\n  text-align: center !important;\n  margin-bottom: 2rem !important;\n}\n\n/* ترويسات الإشعارات */\n.notification-header,\n.notification-title {\n  color: #800020 !important;\n  font-weight: 600 !important;\n}\n\n/* ترويسات الأعضاء */\n.member-header,\n.member-title {\n  color: #191970 !important;\n  font-weight: 600 !important;\n}\n\n/* ترويسات الإيرادات والمصروفات */\n.income-header,\n.expense-header {\n  color: #191970 !important;\n  font-weight: 600 !important;\n}\n\n/* ترويسات مخصصة للديوان */\n.diwan-header-primary {\n  background-color: #191970 !important;\n  color: white !important;\n  padding: 1rem !important;\n  border-radius: 0.5rem !important;\n  text-align: center !important;\n  font-weight: 700 !important;\n}\n\n.diwan-header-secondary {\n  background-color: #800020 !important;\n  color: white !important;\n  padding: 1rem !important;\n  border-radius: 0.5rem !important;\n  text-align: center !important;\n  font-weight: 700 !important;\n}\n\n.diwan-header-text {\n  color: #191970 !important;\n  font-weight: 600 !important;\n  margin-bottom: 1rem !important;\n}\n\n/* تدرجات للترويسات */\n.gradient-header {\n  background: linear-gradient(135deg, #191970 0%, #800020 100%) !important;\n  color: white !important;\n  padding: 1rem !important;\n  border-radius: 0.5rem !important;\n  text-align: center !important;\n  font-weight: 700 !important;\n}\n\n/* ترويسات مع ظلال */\n.shadow-header {\n  color: #191970 !important;\n  font-weight: 700 !important;\n  text-shadow: 2px 2px 4px rgba(25, 25, 112, 0.3) !important;\n}\n\n/* ترويسات للطباعة */\n@media print {\n  h1, h2, h3, h4, h5, h6 {\n    color: #000000 !important;\n  }\n  \n  .header,\n  .navbar,\n  .modal-header {\n    background-color: #ffffff !important;\n    color: #000000 !important;\n    border: 1px solid #000000 !important;\n  }\n}\n\n/* تحسينات للاستجابة */\n@media (max-width: 768px) {\n  h1 {\n    font-size: 1.5rem !important;\n  }\n  \n  h2 {\n    font-size: 1.25rem !important;\n  }\n  \n  h3 {\n    font-size: 1.125rem !important;\n  }\n  \n  .page-title {\n    font-size: 1.5rem !important;\n    text-align: center !important;\n  }\n  \n  .section-header {\n    font-size: 1.25rem !important;\n  }\n}\n\n/* تأثيرات خاصة */\n.animated-header {\n  color: #191970 !important;\n  font-weight: 700 !important;\n  transition: all 0.3s ease !important;\n}\n\n.animated-header:hover {\n  color: #800020 !important;\n  transform: translateY(-2px) !important;\n}\n\n/* ترويسات مع خطوط تحت */\n.underlined-header {\n  color: #191970 !important;\n  font-weight: 600 !important;\n  position: relative !important;\n}\n\n.underlined-header::after {\n  content: '' !important;\n  position: absolute !important;\n  bottom: -4px !important;\n  left: 0 !important;\n  width: 100% !important;\n  height: 2px !important;\n  background-color: #800020 !important;\n}\n\n/* ترويسات مع أيقونات */\n.icon-header {\n  color: #191970 !important;\n  font-weight: 600 !important;\n  display: flex !important;\n  align-items: center !important;\n  gap: 0.5rem !important;\n}\n\n.icon-header svg {\n  color: #800020 !important;\n}\n\n/* تطبيق الألوان على عناصر محددة */\n.text-primary {\n  color: #007bff !important;\n}\n\n.text-success {\n  color: #28a745 !important;\n}\n\n.text-header {\n  color: #191970 !important;\n}\n\n.text-header-alt {\n  color: #800020 !important;\n}\n\n/* خلفيات الترويسات */\n.bg-header {\n  background-color: #191970 !important;\n  color: white !important;\n}\n\n.bg-header-alt {\n  background-color: #800020 !important;\n  color: white !important;\n}\n"], "names": [], "mappings": "AAGA;;;;;AAMA;;;;;AAQA;;;;;AAUA;;;;;;;AAUA;;;;;;AAUA;;;;;;AAQA;;;;AAQA;;;;;AAOA;;;;AAUA;;;;;;;;AAUA;;;;;AAOA;;;;;;AAQA;;;;;;AAQA;;;;;;;;;AAWA;;;;;;;AASA;;;;;;;AASA;;;;;AAOA;;;;;AAcA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;AAOA;;;;;;;;;AAUA;;;;;;AAOA;EACE;;;;EAIA;;;;;;;AAUF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;AAMF;;;;;;AAMA;;;;;AAMA;;;;;;AAMA;;;;;;;;;;AAWA;;;;;;;;AAQA;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;;AAKA"}}]}