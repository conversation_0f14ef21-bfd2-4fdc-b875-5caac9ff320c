'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  Database,
  Download,
  Upload,
  Clock,
  HardDrive,
  Cloud,
  FileText,
  Archive,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Calendar,
  Settings
} from 'lucide-react'

interface BackupSettingsProps {
  settings: any
  onChange: (settings: any) => void
  canEdit: boolean
}

interface BackupSettingsData {
  // إعدادات النسخ الاحتياطي التلقائي
  autoBackup: {
    enabled: boolean
    frequency: 'daily' | 'weekly' | 'monthly'
    time: string
    retentionDays: number
    includeFiles: boolean
    includeDatabase: boolean
    includeSettings: boolean
  }
  
  // إعدادات التخزين
  storage: {
    location: 'local' | 'cloud' | 'both'
    localPath: string
    cloudProvider: string
    cloudCredentials: {
      accessKey: string
      secretKey: string
      bucket: string
      region: string
    }
  }
  
  // إعدادات الاستيراد والتصدير
  importExport: {
    allowDataExport: boolean
    allowDataImport: boolean
    exportFormats: string[]
    maxFileSize: number // MB
    requireConfirmation: boolean
  }
  
  // إعدادات قاعدة البيانات
  database: {
    enableOptimization: boolean
    autoVacuum: boolean
    compressionLevel: number
    encryptBackups: boolean
  }
}

const defaultSettings: BackupSettingsData = {
  autoBackup: {
    enabled: true,
    frequency: 'daily',
    time: '02:00',
    retentionDays: 30,
    includeFiles: true,
    includeDatabase: true,
    includeSettings: true
  },
  
  storage: {
    location: 'local',
    localPath: './backups',
    cloudProvider: '',
    cloudCredentials: {
      accessKey: '',
      secretKey: '',
      bucket: '',
      region: ''
    }
  },
  
  importExport: {
    allowDataExport: true,
    allowDataImport: true,
    exportFormats: ['json', 'csv', 'xlsx'],
    maxFileSize: 100,
    requireConfirmation: true
  },
  
  database: {
    enableOptimization: true,
    autoVacuum: true,
    compressionLevel: 6,
    encryptBackups: true
  }
}

export default function BackupSettings({ settings, onChange, canEdit }: BackupSettingsProps) {
  const [localSettings, setLocalSettings] = useState<BackupSettingsData>(defaultSettings)
  const [backupProgress, setBackupProgress] = useState(0)
  const [isBackingUp, setIsBackingUp] = useState(false)
  const [lastBackup, setLastBackup] = useState<Date | null>(null)
  const [backupSize, setBackupSize] = useState('0 MB')

  useEffect(() => {
    if (settings) {
      setLocalSettings({ ...defaultSettings, ...settings })
    }
    loadBackupInfo()
  }, [settings])

  const handleChange = (key: string, value: any) => {
    const keys = key.split('.')
    let newSettings = { ...localSettings }
    
    if (keys.length === 1) {
      newSettings = { ...newSettings, [keys[0]]: value }
    } else if (keys.length === 2) {
      newSettings = {
        ...newSettings,
        [keys[0]]: {
          ...newSettings[keys[0] as keyof BackupSettingsData],
          [keys[1]]: value
        }
      }
    } else if (keys.length === 3) {
      newSettings = {
        ...newSettings,
        [keys[0]]: {
          ...newSettings[keys[0] as keyof BackupSettingsData],
          [keys[1]]: {
            ...(newSettings[keys[0] as keyof BackupSettingsData] as any)[keys[1]],
            [keys[2]]: value
          }
        }
      }
    }
    
    setLocalSettings(newSettings)
    onChange(newSettings)
  }

  const loadBackupInfo = async () => {
    try {
      // هنا يمكن إضافة API call لجلب معلومات النسخ الاحتياطية
      // محاكاة البيانات
      setLastBackup(new Date(Date.now() - 24 * 60 * 60 * 1000)) // أمس
      setBackupSize('45.2 MB')
    } catch (error) {
      console.error('خطأ في تحميل معلومات النسخ الاحتياطية:', error)
    }
  }

  const createBackup = async () => {
    if (!canEdit) return
    
    setIsBackingUp(true)
    setBackupProgress(0)
    
    try {
      // محاكاة عملية النسخ الاحتياطي
      for (let i = 0; i <= 100; i += 10) {
        setBackupProgress(i)
        await new Promise(resolve => setTimeout(resolve, 200))
      }
      
      setLastBackup(new Date())
      alert('تم إنشاء النسخة الاحتياطية بنجاح!')
    } catch {
      alert('فشل في إنشاء النسخة الاحتياطية')
    } finally {
      setIsBackingUp(false)
      setBackupProgress(0)
    }
  }

  const restoreBackup = async () => {
    if (!canEdit) return
    
    if (confirm('هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {
      try {
        // هنا يمكن إضافة API call لاستعادة النسخة الاحتياطية
        alert('تم استعادة النسخة الاحتياطية بنجاح!')
      } catch {
        alert('فشل في استعادة النسخة الاحتياطية')
      }
    }
  }

  const exportData = async (format: string) => {
    if (!canEdit) return
    
    try {
      // هنا يمكن إضافة API call لتصدير البيانات
      const link = document.createElement('a')
      link.href = `/api/export?format=${format}`
      link.download = `diwan-data.${format}`
      link.click()
    } catch {
      alert('فشل في تصدير البيانات')
    }
  }

  return (
    <div className="space-y-6">
      {/* Backup information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="w-5 h-5 text-blue-600" />
            معلومات النسخ الاحتياطية
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <Calendar className="w-8 h-8 text-blue-600 mx-auto mb-2" />
              <p className="text-sm text-gray-600">آخر نسخة احتياطية</p>
              <p className="font-semibold">
                {lastBackup ? lastBackup.toLocaleDateString('ar-SA') : 'لا توجد'}
              </p>
            </div>

            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <HardDrive className="w-8 h-8 text-green-600 mx-auto mb-2" />
              <p className="text-sm text-gray-600">حجم النسخة الاحتياطية</p>
              <p className="font-semibold">{backupSize}</p>
            </div>

            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <CheckCircle className="w-8 h-8 text-green-600 mx-auto mb-2" />
              <p className="text-sm text-gray-600">الحالة</p>
              <Badge variant="outline" className="text-green-600 border-green-200">
                جاهز
              </Badge>
            </div>
          </div>

          {isBackingUp && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">جاري إنشاء النسخة الاحتياطية...</span>
                <span className="text-sm font-medium">{backupProgress}%</span>
              </div>
              <Progress value={backupProgress} className="w-full" />
            </div>
          )}

          <div className="flex gap-3">
            <Button
              onClick={createBackup}
              disabled={!canEdit || isBackingUp}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isBackingUp ? (
                <RefreshCw className="w-4 h-4 ml-2 animate-spin" />
              ) : (
                <Archive className="w-4 h-4 ml-2" />
              )}
              إنشاء نسخة احتياطية
            </Button>

            <Button
              variant="outline"
              onClick={restoreBackup}
              disabled={!canEdit || !lastBackup}
            >
              <Upload className="w-4 h-4 ml-2" />
              استعادة النسخة الاحتياطية
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* إعدادات النسخ الاحتياطي التلقائي */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5 text-green-600" />
            النسخ الاحتياطي التلقائي
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label>تفعيل النسخ الاحتياطي التلقائي</Label>
              <p className="text-sm text-gray-600">إنشاء نسخ احتياطية تلقائياً</p>
            </div>
            <Switch
              checked={localSettings.autoBackup.enabled}
              onCheckedChange={(checked) => handleChange('autoBackup.enabled', checked)}
              disabled={!canEdit}
            />
          </div>

          {localSettings.autoBackup.enabled && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>تكرار النسخ الاحتياطي</Label>
                  <Select
                    value={localSettings.autoBackup.frequency}
                    onValueChange={(value) => handleChange('autoBackup.frequency', value)}
                    disabled={!canEdit}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">يومياً</SelectItem>
                      <SelectItem value="weekly">أسبوعياً</SelectItem>
                      <SelectItem value="monthly">شهرياً</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="backupTime">وقت النسخ الاحتياطي</Label>
                  <Input
                    id="backupTime"
                    type="time"
                    value={localSettings.autoBackup.time}
                    onChange={(e) => handleChange('autoBackup.time', e.target.value)}
                    disabled={!canEdit}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>مدة الاحتفاظ بالنسخ الاحتياطية (يوم)</Label>
                <Select
                  value={localSettings.autoBackup.retentionDays.toString()}
                  onValueChange={(value) => handleChange('autoBackup.retentionDays', parseInt(value))}
                  disabled={!canEdit}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7">أسبوع واحد</SelectItem>
                    <SelectItem value="30">شهر واحد</SelectItem>
                    <SelectItem value="90">3 أشهر</SelectItem>
                    <SelectItem value="365">سنة واحدة</SelectItem>
                    <SelectItem value="0">دائماً</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-3">
                <Label>محتويات النسخة الاحتياطية</Label>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>قاعدة البيانات</Label>
                      <p className="text-sm text-gray-600">جميع البيانات والجداول</p>
                    </div>
                    <Switch
                      checked={localSettings.autoBackup.includeDatabase}
                      onCheckedChange={(checked) => handleChange('autoBackup.includeDatabase', checked)}
                      disabled={!canEdit}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>الملفات المرفوعة</Label>
                      <p className="text-sm text-gray-600">الصور والمستندات</p>
                    </div>
                    <Switch
                      checked={localSettings.autoBackup.includeFiles}
                      onCheckedChange={(checked) => handleChange('autoBackup.includeFiles', checked)}
                      disabled={!canEdit}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>إعدادات النظام</Label>
                      <p className="text-sm text-gray-600">التكوينات والإعدادات</p>
                    </div>
                    <Switch
                      checked={localSettings.autoBackup.includeSettings}
                      onCheckedChange={(checked) => handleChange('autoBackup.includeSettings', checked)}
                      disabled={!canEdit}
                    />
                  </div>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* إعدادات التخزين */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Cloud className="w-5 h-5 text-purple-600" />
            إعدادات التخزين
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>موقع التخزين</Label>
            <Select
              value={localSettings.storage.location}
              onValueChange={(value) => handleChange('storage.location', value)}
              disabled={!canEdit}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="local">محلي فقط</SelectItem>
                <SelectItem value="cloud">سحابي فقط</SelectItem>
                <SelectItem value="both">محلي وسحابي</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {(localSettings.storage.location === 'local' || localSettings.storage.location === 'both') && (
            <div className="space-y-2">
              <Label htmlFor="localPath">مسار التخزين المحلي</Label>
              <Input
                id="localPath"
                value={localSettings.storage.localPath}
                onChange={(e) => handleChange('storage.localPath', e.target.value)}
                disabled={!canEdit}
                placeholder="./backups"
              />
            </div>
          )}

          {(localSettings.storage.location === 'cloud' || localSettings.storage.location === 'both') && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>مزود الخدمة السحابية</Label>
                <Select
                  value={localSettings.storage.cloudProvider}
                  onValueChange={(value) => handleChange('storage.cloudProvider', value)}
                  disabled={!canEdit}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر مزود الخدمة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="aws">Amazon S3</SelectItem>
                    <SelectItem value="google">Google Cloud Storage</SelectItem>
                    <SelectItem value="azure">Microsoft Azure</SelectItem>
                    <SelectItem value="digitalocean">DigitalOcean Spaces</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {localSettings.storage.cloudProvider && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="accessKey">مفتاح الوصول</Label>
                    <Input
                      id="accessKey"
                      type="password"
                      value={localSettings.storage.cloudCredentials.accessKey}
                      onChange={(e) => handleChange('storage.cloudCredentials.accessKey', e.target.value)}
                      disabled={!canEdit}
                      placeholder="Access Key"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="secretKey">المفتاح السري</Label>
                    <Input
                      id="secretKey"
                      type="password"
                      value={localSettings.storage.cloudCredentials.secretKey}
                      onChange={(e) => handleChange('storage.cloudCredentials.secretKey', e.target.value)}
                      disabled={!canEdit}
                      placeholder="Secret Key"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="bucket">اسم الحاوية</Label>
                    <Input
                      id="bucket"
                      value={localSettings.storage.cloudCredentials.bucket}
                      onChange={(e) => handleChange('storage.cloudCredentials.bucket', e.target.value)}
                      disabled={!canEdit}
                      placeholder="bucket-name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="region">المنطقة</Label>
                    <Input
                      id="region"
                      value={localSettings.storage.cloudCredentials.region}
                      onChange={(e) => handleChange('storage.cloudCredentials.region', e.target.value)}
                      disabled={!canEdit}
                      placeholder="us-east-1"
                    />
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* إعدادات الاستيراد والتصدير */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5 text-orange-600" />
            الاستيراد والتصدير
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>السماح بتصدير البيانات</Label>
                <p className="text-sm text-gray-600">تمكين المستخدمين من تصدير البيانات</p>
              </div>
              <Switch
                checked={localSettings.importExport.allowDataExport}
                onCheckedChange={(checked) => handleChange('importExport.allowDataExport', checked)}
                disabled={!canEdit}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>السماح باستيراد البيانات</Label>
                <p className="text-sm text-gray-600">تمكين المستخدمين من استيراد البيانات</p>
              </div>
              <Switch
                checked={localSettings.importExport.allowDataImport}
                onCheckedChange={(checked) => handleChange('importExport.allowDataImport', checked)}
                disabled={!canEdit}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>يتطلب تأكيد</Label>
                <p className="text-sm text-gray-600">طلب تأكيد قبل الاستيراد/التصدير</p>
              </div>
              <Switch
                checked={localSettings.importExport.requireConfirmation}
                onCheckedChange={(checked) => handleChange('importExport.requireConfirmation', checked)}
                disabled={!canEdit}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="maxFileSize">الحد الأقصى لحجم الملف (MB)</Label>
            <Input
              id="maxFileSize"
              type="number"
              min="1"
              max="1000"
              value={localSettings.importExport.maxFileSize}
              onChange={(e) => handleChange('importExport.maxFileSize', parseInt(e.target.value))}
              disabled={!canEdit}
            />
          </div>

          <div className="space-y-3">
            <Label>تصدير البيانات</Label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              <Button
                variant="outline"
                onClick={() => exportData('json')}
                disabled={!canEdit || !localSettings.importExport.allowDataExport}
                className="flex items-center gap-2"
              >
                <Download className="w-4 h-4" />
                JSON
              </Button>
              <Button
                variant="outline"
                onClick={() => exportData('csv')}
                disabled={!canEdit || !localSettings.importExport.allowDataExport}
                className="flex items-center gap-2"
              >
                <Download className="w-4 h-4" />
                CSV
              </Button>
              <Button
                variant="outline"
                onClick={() => exportData('xlsx')}
                disabled={!canEdit || !localSettings.importExport.allowDataExport}
                className="flex items-center gap-2"
              >
                <Download className="w-4 h-4" />
                Excel
              </Button>
              <Button
                variant="outline"
                onClick={() => exportData('pdf')}
                disabled={!canEdit || !localSettings.importExport.allowDataExport}
                className="flex items-center gap-2"
              >
                <Download className="w-4 h-4" />
                PDF
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* إعدادات قاعدة البيانات */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5 text-red-600" />
            إعدادات قاعدة البيانات
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>تحسين قاعدة البيانات</Label>
                <p className="text-sm text-gray-600">تحسين الأداء تلقائياً</p>
              </div>
              <Switch
                checked={localSettings.database.enableOptimization}
                onCheckedChange={(checked) => handleChange('database.enableOptimization', checked)}
                disabled={!canEdit}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>التنظيف التلقائي</Label>
                <p className="text-sm text-gray-600">إزالة البيانات المحذوفة تلقائياً</p>
              </div>
              <Switch
                checked={localSettings.database.autoVacuum}
                onCheckedChange={(checked) => handleChange('database.autoVacuum', checked)}
                disabled={!canEdit}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>تشفير النسخ الاحتياطية</Label>
                <p className="text-sm text-gray-600">حماية النسخ الاحتياطية بالتشفير</p>
              </div>
              <Switch
                checked={localSettings.database.encryptBackups}
                onCheckedChange={(checked) => handleChange('database.encryptBackups', checked)}
                disabled={!canEdit}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label>مستوى الضغط</Label>
            <Select
              value={localSettings.database.compressionLevel.toString()}
              onValueChange={(value) => handleChange('database.compressionLevel', parseInt(value))}
              disabled={!canEdit}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">منخفض (سريع)</SelectItem>
                <SelectItem value="3">متوسط</SelectItem>
                <SelectItem value="6">عالي (افتراضي)</SelectItem>
                <SelectItem value="9">أقصى (بطيء)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-amber-600 mt-0.5" />
              <div>
                <p className="text-amber-800 font-medium">تحذير مهم</p>
                <p className="text-amber-700 text-sm mt-1">
                  تأكد من إنشاء نسخة احتياطية قبل تغيير إعدادات قاعدة البيانات.
                  بعض التغييرات قد تتطلب إعادة تشغيل النظام.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
