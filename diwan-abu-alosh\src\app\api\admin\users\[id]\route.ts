import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'

// تحديث مستخدم
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'غير مصرح لك بالوصول' },
        { status: 403 }
      )
    }

    const { id } = await params
    const body = await request.json()
    const { name, email, phone, role, password } = body

    // التحقق من وجود المستخدم
    const existingUser = await prisma.user.findUnique({
      where: { id }
    })

    if (!existingUser) {
      return NextResponse.json(
        { message: 'المستخدم غير موجود' },
        { status: 404 }
      )
    }

    // منع المستخدم من تعديل نفسه
    if (session.user.id === id) {
      return NextResponse.json(
        { message: 'لا يمكنك تعديل حسابك الخاص من هنا' },
        { status: 400 }
      )
    }

    // التحقق من البيانات
    if (!name || !email || !role) {
      return NextResponse.json(
        { message: 'الاسم والبريد الإلكتروني والدور مطلوبة' },
        { status: 400 }
      )
    }

    // التحقق من صحة البريد الإلكتروني
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { message: 'البريد الإلكتروني غير صحيح' },
        { status: 400 }
      )
    }

    // التحقق من أن البريد الإلكتروني غير مستخدم من قبل مستخدم آخر
    if (email !== existingUser.email) {
      const emailExists = await prisma.user.findUnique({
        where: { email }
      })

      if (emailExists) {
        return NextResponse.json(
          { message: 'البريد الإلكتروني مستخدم بالفعل' },
          { status: 400 }
        )
      }
    }

    // التحقق من صحة الدور
    const validRoles = ['ADMIN', 'DATA_ENTRY', 'VIEWER']
    if (!validRoles.includes(role)) {
      return NextResponse.json(
        { message: 'الدور المحدد غير صحيح' },
        { status: 400 }
      )
    }

    // إعداد البيانات للتحديث
    const updateData: Record<string, unknown> = {
      name,
      email,
      phone: phone || null,
      role,
    }

    // إضافة كلمة المرور الجديدة إذا تم توفيرها
    if (password && password.length > 0) {
      if (password.length < 6) {
        return NextResponse.json(
          { message: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' },
          { status: 400 }
        )
      }
      updateData.password = await bcrypt.hash(password, 12)
    }

    // تحديث المستخدم
    const updatedUser = await prisma.user.update({
      where: { id },
      data: updateData,
      select: {
        id: true,
        name: true,
        email: true,
        phone: true,
        role: true,
        updatedAt: true
      }
    })

    return NextResponse.json(updatedUser)
  } catch (error) {
    console.error('خطأ في تحديث المستخدم:', error)
    return NextResponse.json(
      { message: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// حذف مستخدم
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'غير مصرح لك بالوصول' },
        { status: 403 }
      )
    }

    const { id } = await params

    // التحقق من وجود المستخدم
    const existingUser = await prisma.user.findUnique({
      where: { id }
    })

    if (!existingUser) {
      return NextResponse.json(
        { message: 'المستخدم غير موجود' },
        { status: 404 }
      )
    }

    // منع المستخدم من حذف نفسه
    if (session.user.id === id) {
      return NextResponse.json(
        { message: 'لا يمكنك حذف حسابك الخاص' },
        { status: 400 }
      )
    }

    // حذف البيانات المرتبطة أولاً
    await prisma.$transaction(async (tx) => {
      // حذف الإشعارات المرتبطة
      await tx.notification.deleteMany({
        where: { userId: id }
      })

      // حذف MemberUser إذا كان موجوداً
      await tx.memberUser.deleteMany({
        where: { userId: id }
      })

      // حذف الأعضاء المرتبطة (إذا كانت موجودة)
      await tx.member.deleteMany({
        where: { createdById: id }
      })

      // حذف الإيرادات المرتبطة (إذا كانت موجودة)
      await tx.income.deleteMany({
        where: { createdById: id }
      })

      // حذف المصروفات المرتبطة (إذا كانت موجودة)
      await tx.expense.deleteMany({
        where: { createdById: id }
      })

      // حذف الأنشطة المرتبطة (إذا كانت موجودة)
      await tx.activity.deleteMany({
        where: { createdById: id }
      })

      // حذف مجلدات المعرض المرتبطة (إذا كانت موجودة)
      await tx.galleryFolder.deleteMany({
        where: { createdBy: id }
      })

      // حذف صور المعرض المرتبطة (إذا كانت موجودة)
      await tx.galleryPhoto.deleteMany({
        where: { uploadedBy: id }
      })

      // حذف المستخدم
      await tx.user.delete({
        where: { id }
      })
    })

    return NextResponse.json(
      { message: 'تم حذف المستخدم بنجاح' },
      { status: 200 }
    )
  } catch (error) {
    console.error('خطأ في حذف المستخدم:', error)
    return NextResponse.json(
      { message: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// جلب مستخدم واحد
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'غير مصرح لك بالوصول' },
        { status: 403 }
      )
    }

    const { id } = await params

    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        email: true,
        phone: true,
        role: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            createdMembers: true,
            createdIncomes: true,
            notifications: true
          }
        }
      }
    })

    if (!user) {
      return NextResponse.json(
        { message: 'المستخدم غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json(user)
  } catch (error) {
    console.error('خطأ في جلب المستخدم:', error)
    return NextResponse.json(
      { message: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}
