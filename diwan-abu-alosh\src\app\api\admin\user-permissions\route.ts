import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// جلب صلاحيات مستخدم معين
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'غير مصرح لك بالوصول' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json(
        { message: 'معرف المستخدم مطلوب' },
        { status: 400 }
      )
    }

    const userPermissions = await prisma.userPermission.findFirst({
      where: { userId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        },
        specificMember: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    return NextResponse.json(userPermissions)
  } catch (error) {
    console.error('خطأ في جلب صلاحيات المستخدم:', error)
    return NextResponse.json(
      { message: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// إنشاء أو تحديث صلاحيات مستخدم
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'غير مصرح لك بالوصول' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { userId, permissions } = body

    if (!userId || !permissions) {
      return NextResponse.json(
        { message: 'معرف المستخدم والصلاحيات مطلوبان' },
        { status: 400 }
      )
    }

    // التحقق من وجود المستخدم
    const user = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!user) {
      return NextResponse.json(
        { message: 'المستخدم غير موجود' },
        { status: 404 }
      )
    }

    // البحث عن الصلاحيات الموجودة
    const existingPermission = await prisma.userPermission.findFirst({
      where: { userId }
    })

    // إنشاء أو تحديث الصلاحيات
    const userPermissions = existingPermission
      ? await prisma.userPermission.update({
          where: { id: existingPermission.id },
          data: permissions,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true
              }
            },
            specificMember: {
              select: {
                id: true,
                name: true
              }
            }
          }
        })
      : await prisma.userPermission.create({
          data: {
            userId,
            ...permissions
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true
              }
            },
            specificMember: {
              select: {
                id: true,
                name: true
              }
            }
          }
        })

    return NextResponse.json(userPermissions)
  } catch (error) {
    console.error('خطأ في تحديث صلاحيات المستخدم:', error)
    return NextResponse.json(
      { message: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}
