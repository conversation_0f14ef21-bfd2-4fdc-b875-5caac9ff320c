import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Schema للتحقق من صحة البيانات
const galleryPhotoSchema = z.object({
  title: z.string().min(1, 'العنوان مطلوب'),
  description: z.string().optional(),
  imagePath: z.string().min(1, 'مسار الصورة مطلوب'),
  activityId: z.string().optional(),
  folderId: z.string().optional(),
})

// GET - جلب جميع الصور مع البحث والتصفية
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search') || ''
    const category = searchParams.get('category') || 'all'
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const groupBy = searchParams.get('groupBy') || 'none' // 'activity' أو 'none'
    const activityId = searchParams.get('activityId') // لجلب صور نشاط محدد
    const folderId = searchParams.get('folderId') // لجلب صور مجلد محدد
    const skip = (page - 1) * limit

    // إذا كان المطلوب جلب صور نشاط محدد أو مجلد محدد أو الصور العامة
    if (activityId || folderId) {
      let where: Record<string, unknown> = {}

      if (activityId) {
        where = activityId === 'null' ? { activityId: null, folderId: null } : { activityId }
      } else if (folderId) {
        where = folderId === 'null' ? { folderId: null, activityId: null } : { folderId }
      }

      if (search) {
        where.OR = [
          { title: { contains: search } },
          { description: { contains: search } },
        ]
      }

      const photos = await prisma.galleryPhoto.findMany({
        where,
        include: {
          uploader: {
            select: {
              name: true,
            },
          },
          activity: {
            select: {
              id: true,
              title: true,
            },
          },
          folder: {
            select: {
              id: true,
              title: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      })

      const total = await prisma.galleryPhoto.count({ where })

      return NextResponse.json({
        photos,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      })
    }

    // إذا كان المطلوب التجميع حسب الأنشطة والمجلدات
    if (groupBy === 'activity') {
      // جلب جميع المجلدات المخصصة (حتى الفارغة)
      const allFolders = await prisma.galleryFolder.findMany({
        include: {
          photos: {
            take: 1, // صورة واحدة كمعاينة
            orderBy: { createdAt: 'desc' },
          },
          _count: {
            select: {
              photos: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      })

      // جلب جميع الأنشطة (حتى التي لا تحتوي على صور)
      const allActivities = await prisma.activity.findMany({
        include: {
          photos: {
            take: 1, // صورة واحدة كمعاينة
            orderBy: { createdAt: 'desc' },
          },
          _count: {
            select: {
              photos: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      })

      // جلب الصور غير المرتبطة بأنشطة أو مجلدات (الصور العامة)
      const generalPhotosCount = await prisma.galleryPhoto.count({
        where: {
          AND: [
            { activityId: null },
            { folderId: null }
          ]
        },
      })

      const generalPhotos = generalPhotosCount > 0 ? await prisma.galleryPhoto.findMany({
        where: {
          AND: [
            { activityId: null },
            { folderId: null }
          ]
        },
        take: 1,
        orderBy: { createdAt: 'desc' },
      }) : []

      return NextResponse.json({
        folders: [
          // المجلدات المخصصة (جميع المجلدات حتى الفارغة)
          ...allFolders.map(folder => ({
            id: folder.id,
            title: folder.title,
            description: folder.description,
            photosCount: folder._count.photos,
            coverPhoto: folder.photos[0] || null,
            type: 'folder'
          })),
          // مجلدات الأنشطة (جميع الأنشطة حتى الفارغة)
          ...allActivities.map(activity => ({
            id: activity.id,
            title: activity.title,
            description: activity.description,
            photosCount: activity._count.photos,
            coverPhoto: activity.photos[0] || null,
            type: 'activity'
          })),
          // مجلد الصور العامة
          ...(generalPhotosCount > 0 ? [{
            id: 'general',
            title: 'الصور العامة',
            description: 'صور غير مرتبطة بأنشطة أو مجلدات محددة',
            photosCount: generalPhotosCount,
            coverPhoto: generalPhotos[0] || null,
            type: 'general'
          }] : []),
        ]
      })
    }

    // الطريقة العادية - جلب جميع الصور
    const where: Record<string, unknown> = {}

    if (search) {
      where.OR = [
        { title: { contains: search } },
        { description: { contains: search } },
      ]
    }

    // تصفية حسب النشاط إذا كان category ليس 'all'
    if (category !== 'all') {
      if (category === 'activities') {
        where.activityId = { not: null }
      } else if (category === 'members') {
        where.activityId = null
      }
    }

    // جلب الصور مع العلاقات
    const photos = await prisma.galleryPhoto.findMany({
      where,
      include: {
        uploader: {
          select: {
            name: true,
          },
        },
        activity: {
          select: {
            id: true,
            title: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit,
    })

    // جلب العدد الإجمالي للصفحات
    const total = await prisma.galleryPhoto.count({ where })

    return NextResponse.json({
      photos,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('خطأ في جلب الصور:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في جلب الصور' },
      { status: 500 }
    )
  }
}

// POST - إضافة صورة جديدة
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    const body = await request.json()
    
    // التحقق من صحة البيانات
    const validatedData = galleryPhotoSchema.parse(body)

    // إنشاء الصورة في قاعدة البيانات
    const photo = await prisma.galleryPhoto.create({
      data: {
        ...validatedData,
        uploadedBy: session.user.id,
      },
      include: {
        uploader: {
          select: {
            name: true,
          },
        },
        activity: {
          select: {
            id: true,
            title: true,
          },
        },
      },
    })

    return NextResponse.json(photo, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'بيانات غير صحيحة', details: error.errors },
        { status: 400 }
      )
    }

    console.error('خطأ في إضافة الصورة:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في إضافة الصورة' },
      { status: 500 }
    )
  }
}
