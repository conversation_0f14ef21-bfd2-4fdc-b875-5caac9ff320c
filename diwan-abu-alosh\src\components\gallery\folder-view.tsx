'use client'

// import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Folder,
  Image as ImageIcon,
  Calendar,
  Users,
  ArrowRight,
  Eye,
  Edit,
  Trash2,
  // MoreVertical,
  Plus
} from 'lucide-react'


interface GalleryFolder {
  id: string
  title: string
  description?: string
  photosCount: number
  coverPhoto?: {
    id: string
    imagePath: string
    title: string
  } | null
  type: 'activity' | 'general' | 'folder'
}

interface FolderViewProps {
  folders: GalleryFolder[]
  onFolderClick: (folder: GalleryFolder) => void
  onEditFolder?: (folder: GalleryFolder) => void
  onDeleteFolder?: (folder: GalleryFolder) => void
  canEdit?: boolean
  canDelete?: boolean
  loading?: boolean
}

export default function FolderView({
  folders,
  onFolderClick,
  onEditFolder,
  onDeleteFolder,
  canEdit = false,
  canDelete = false,
  loading
}: FolderViewProps) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {[...Array(8)].map((_, index) => (
          <Card key={index} className="animate-pulse">
            <CardContent className="p-0">
              <div className="aspect-video bg-gray-200 rounded-t-lg"></div>
              <div className="p-4 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (folders.length === 0) {
    return (
      <div className="text-center py-12">
        <Folder className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد مجلدات</h3>
        <p className="text-gray-500">لم يتم العثور على أي مجلدات</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      {folders.map((folder) => (
        <Card
          key={folder.id}
          className="group hover:shadow-lg transition-all duration-200 border-2 hover:border-diwan-300 relative"
        >
          <CardContent className="p-0">
            {/* أزرار التحكم */}
            {(canEdit || canDelete) && (folder.type === 'activity' || folder.type === 'folder') && (
              <div className="absolute top-2 left-2 z-10 flex space-x-1 space-x-reverse opacity-0 group-hover:opacity-100 transition-opacity">
                {canEdit && onEditFolder && (
                  <Button
                    variant="secondary"
                    size="sm"
                    className="h-8 w-8 p-0 bg-white/90 hover:bg-white"
                    onClick={(e) => {
                      e.stopPropagation()
                      onEditFolder(folder)
                    }}
                    title="تعديل المجلد"
                  >
                    <Edit className="h-3 w-3" />
                  </Button>
                )}
                {canDelete && onDeleteFolder && (
                  <Button
                    variant="secondary"
                    size="sm"
                    className="h-8 w-8 p-0 bg-white/90 hover:bg-white text-red-600 hover:text-red-700"
                    onClick={(e) => {
                      e.stopPropagation()
                      onDeleteFolder(folder)
                    }}
                    title="حذف المجلد"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                )}
              </div>
            )}

            {/* صورة الغلاف */}
            <div
              className="relative aspect-video overflow-hidden rounded-t-lg bg-gradient-to-br from-diwan-50 to-diwan-100 cursor-pointer"
              onClick={() => onFolderClick(folder)}
            >
              {folder.coverPhoto ? (
                <img
                  src={folder.coverPhoto.imagePath}
                  alt={folder.coverPhoto.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                />
              ) : (
                <div className="w-full h-full flex flex-col items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
                  <div className="bg-diwan-100 rounded-full p-4 mb-3">
                    <Folder className="w-8 h-8 text-diwan-600" />
                  </div>
                  <p className="text-sm font-medium text-gray-600 mb-1">مجلد فارغ</p>
                  <p className="text-xs text-gray-500 text-center px-2">
                    {folder.type === 'folder' ? 'مجلد جديد' : 'لا توجد صور'}
                  </p>
                  {folder.photosCount === 0 && (
                    <div className="mt-2 px-3 py-1 bg-yellow-100 text-yellow-700 rounded-full text-xs font-medium">
                      جديد
                    </div>
                  )}
                </div>
              )}
              
              {/* أيقونة النوع */}
              <div className="absolute top-2 right-2">
                <Badge
                  variant="secondary"
                  className={`text-xs ${
                    folder.type === 'activity'
                      ? 'bg-green-100 text-green-700'
                      : folder.type === 'folder'
                      ? 'bg-purple-100 text-purple-700'
                      : 'bg-blue-100 text-blue-700'
                  }`}
                >
                  {folder.type === 'activity' ? (
                    <>
                      <Calendar className="w-3 h-3 ml-1" />
                      نشاط
                    </>
                  ) : folder.type === 'folder' ? (
                    <>
                      <Folder className="w-3 h-3 ml-1" />
                      مجلد
                    </>
                  ) : (
                    <>
                      <Users className="w-3 h-3 ml-1" />
                      عام
                    </>
                  )}
                </Badge>
              </div>

              {/* عدد الصور */}
              <div className="absolute bottom-2 left-2">
                <Badge
                  className={`text-xs ${
                    folder.photosCount === 0
                      ? 'bg-orange-500/80 text-white'
                      : 'bg-black/70 text-white'
                  }`}
                >
                  <ImageIcon className="w-3 h-3 ml-1" />
                  {folder.photosCount === 0 ? 'فارغ' : `${folder.photosCount} صورة`}
                </Badge>
              </div>

              {/* زر العرض */}
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200 flex items-center justify-center">
                <Button
                  variant="secondary"
                  size="sm"
                  className="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                  onClick={() => onFolderClick(folder)}
                >
                  {folder.photosCount === 0 ? (
                    <>
                      <Plus className="w-4 h-4 ml-2" />
                      إضافة صور
                    </>
                  ) : (
                    <>
                      <Eye className="w-4 h-4 ml-2" />
                      عرض الصور
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* معلومات المجلد */}
            <div className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-medium text-gray-900 truncate group-hover:text-diwan-700 transition-colors">
                    {folder.title}
                  </h3>
                  {folder.description && (
                    <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                      {folder.description}
                    </p>
                  )}
                </div>
                <ArrowRight className="w-4 h-4 text-gray-400 group-hover:text-diwan-500 transition-colors flex-shrink-0 mr-2" />
              </div>

              {/* إحصائيات إضافية */}
              <div className="mt-3 flex items-center justify-between text-xs text-gray-500">
                <span>
                  {folder.photosCount === 1 
                    ? 'صورة واحدة' 
                    : folder.photosCount === 2 
                    ? 'صورتان' 
                    : `${folder.photosCount} صور`
                  }
                </span>
                <span className="flex items-center">
                  <Folder className="w-3 h-3 ml-1" />
                  مجلد
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

// مكون إضافي لعرض إحصائيات المجلدات
export function FoldersStats({ folders }: { folders: GalleryFolder[] }) {
  const totalPhotos = folders.reduce((sum, folder) => sum + folder.photosCount, 0)
  const activityFolders = folders.filter(f => f.type === 'activity').length
  const generalFolders = folders.filter(f => f.type === 'general').length

  return (
    <div className="grid grid-cols-1 sm:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardContent className="p-4 text-center">
          <Folder className="w-8 h-8 text-blue-600 mx-auto mb-2" />
          <div className="text-2xl font-bold text-blue-600">{folders.length}</div>
          <div className="text-sm text-gray-600">إجمالي المجلدات</div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-4 text-center">
          <ImageIcon className="w-8 h-8 text-green-600 mx-auto mb-2" />
          <div className="text-2xl font-bold text-green-600">{totalPhotos}</div>
          <div className="text-sm text-gray-600">إجمالي الصور</div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-4 text-center">
          <Calendar className="w-8 h-8 text-purple-600 mx-auto mb-2" />
          <div className="text-2xl font-bold text-purple-600">{activityFolders}</div>
          <div className="text-sm text-gray-600">مجلدات الأنشطة</div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-4 text-center">
          <Users className="w-8 h-8 text-orange-600 mx-auto mb-2" />
          <div className="text-2xl font-bold text-orange-600">{generalFolders}</div>
          <div className="text-sm text-gray-600">المجلدات العامة</div>
        </CardContent>
      </Card>
    </div>
  )
}
