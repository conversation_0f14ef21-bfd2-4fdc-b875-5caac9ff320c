"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5110],{13717:(e,a,s)=>{s.d(a,{A:()=>r});let r=(0,s(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},21380:(e,a,s)=>{s.d(a,{A:()=>r});let r=(0,s(19946).A)("folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]])},27213:(e,a,s)=>{s.d(a,{A:()=>r});let r=(0,s(19946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},29869:(e,a,s)=>{s.d(a,{A:()=>r});let r=(0,s(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},35695:(e,a,s)=>{var r=s(18999);s.o(r,"useParams")&&s.d(a,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(a,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(a,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(a,{useSearchParams:function(){return r.useSearchParams}})},47924:(e,a,s)=>{s.d(a,{A:()=>r});let r=(0,s(19946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},62525:(e,a,s)=>{s.d(a,{A:()=>r});let r=(0,s(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},71007:(e,a,s)=>{s.d(a,{A:()=>r});let r=(0,s(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},73054:(e,a,s)=>{s.d(a,{A:()=>p});var r=s(95155),l=s(12115),t=s(30285),d=s(62523),i=s(85057),n=s(88539),o=s(54165),c=s(59409),x=s(29869),m=s(27213),h=s(54416),u=s(21380),g=s(69074),f=s(26126);function p(e){let{open:a,onOpenChange:s,onSuccess:p,defaultFolderId:v,defaultActivityId:b}=e,[j,y]=(0,l.useState)(""),[N,w]=(0,l.useState)(""),[k,A]=(0,l.useState)("none"),[C,z]=(0,l.useState)("none"),[P,S]=(0,l.useState)(null),[M,R]=(0,l.useState)(null),[E,H]=(0,l.useState)(!1),[J,L]=(0,l.useState)(null),[V,D]=(0,l.useState)([]),[F,T]=(0,l.useState)([]),[q,B]=(0,l.useState)(!1),[G,O]=(0,l.useState)(!1),_=(0,l.useRef)(null);(0,l.useEffect)(()=>{a&&(v?(z(v),A("none")):b&&(A(b),z("none")))},[a,v,b]),(0,l.useEffect)(()=>{let e=async()=>{try{B(!0),O(!0);let e=await fetch("/api/activities");if(e.ok){let a=await e.json();D(a.activities||[])}let a=await fetch("/api/gallery-folders");if(a.ok){let e=await a.json();T(e.folders||[])}}catch(e){console.error("خطأ في جلب البيانات:",e)}finally{B(!1),O(!1)}};a&&e()},[a]);let $=e=>{if(!["image/jpeg","image/jpg","image/png","image/webp"].includes(e.type))return void L("نوع الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG, WebP)");if(e.size>5242880)return void L("حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت");S(e),L(null);let a=new FileReader;a.onload=e=>{var a;R(null==(a=e.target)?void 0:a.result)},a.readAsDataURL(e)},I=async e=>{if(e.preventDefault(),!P)return void L("يرجى اختيار صورة");if(!j.trim())return void L("يرجى إدخال عنوان للصورة");H(!0),L(null);try{let e=new FormData;e.append("file",P),e.append("type","gallery");let a=await fetch("/api/upload",{method:"POST",body:e});if(!a.ok){let e=await a.json();throw Error(e.error||"فشل في رفع الصورة")}let r=await a.json(),l=await fetch("/api/gallery",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:j.trim(),description:N.trim()||void 0,imagePath:r.filePath,activityId:k&&"none"!==k?k:void 0,folderId:C&&"none"!==C?C:void 0})});if(!l.ok){let e=await l.json();throw Error(e.error||"فشل في إضافة الصورة إلى المعرض")}y(""),w(""),A("none"),z("none"),S(null),R(null),p(),s(!1)}catch(e){console.error("خطأ في رفع الصورة:",e),L(e.message||"حدث خطأ في رفع الصورة")}finally{H(!1)}};return(0,r.jsx)(o.lG,{open:a,onOpenChange:s,children:(0,r.jsxs)(o.Cf,{className:"max-w-[50vw] max-h-[95vh] overflow-y-auto bg-gradient-to-br from-white to-gray-50",children:[(0,r.jsx)(o.c7,{className:"pb-6 border-b border-gray-100",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"bg-diwan-100 rounded-full p-3",children:(0,r.jsx)(x.A,{className:"w-6 h-6 text-diwan-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)(o.L3,{className:"text-2xl font-bold text-diwan-700",children:"رفع صورة جديدة"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"أضف صورة جديدة إلى معرض الصور مع إمكانية ربطها بمجلد أو نشاط"})]})]})}),(0,r.jsxs)("form",{onSubmit:I,className:"space-y-8 pt-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,r.jsx)(m.A,{className:"w-5 h-5 text-diwan-600"}),(0,r.jsx)(i.J,{className:"text-lg font-semibold text-gray-800",children:"اختيار الصورة *"})]}),P?(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"relative group",children:(0,r.jsxs)("div",{className:"relative w-full h-80 rounded-2xl overflow-hidden border-2 border-diwan-200 bg-gray-100 shadow-lg",children:[(0,r.jsx)("img",{src:M,alt:"معاينة الصورة",className:"w-full h-full object-cover"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-end justify-center pb-6",children:(0,r.jsxs)(t.$,{type:"button",variant:"destructive",size:"sm",onClick:()=>{S(null),R(null),_.current&&(_.current.value="")},className:"bg-red-600 hover:bg-red-700 text-white font-semibold px-6 py-3 h-12 shadow-xl hover:shadow-2xl transition-all duration-200 rounded-xl",disabled:E,children:[(0,r.jsx)(h.A,{className:"w-5 h-5 ml-2"}),"إزالة الصورة"]})})]})}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"bg-green-100 rounded-full p-2",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-green-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-semibold text-gray-900",children:P.name}),(0,r.jsxs)("p",{className:"text-xs text-gray-600",children:[(P.size/1048576).toFixed(2)," ميجابايت • تم اختيار الصورة بنجاح"]})]})]}),(0,r.jsx)(f.E,{className:"bg-green-100 text-green-800 border-green-200 font-medium",children:"جاهز للرفع"})]})})]}):(0,r.jsxs)("div",{className:"drop-zone border-2 border-dashed border-diwan-300 rounded-2xl p-12 text-center cursor-pointer hover:border-diwan-500 hover:bg-gradient-to-br hover:from-diwan-50 hover:to-blue-50 transition-all duration-300 bg-gradient-to-br from-gray-50 via-white to-gray-50 group",onDrop:e=>{e.preventDefault();let a=e.dataTransfer.files[0];a&&$(a)},onDragOver:e=>{e.preventDefault()},onClick:()=>{var e;null==(e=_.current)||e.click()},children:[(0,r.jsx)("div",{className:"bg-gradient-to-br from-diwan-100 to-diwan-200 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg",children:(0,r.jsx)(m.A,{className:"w-10 h-10 text-diwan-700"})}),(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3",children:"اختر صورة للرفع"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6 text-lg",children:"اسحب الصورة هنا أو انقر للاختيار من جهازك"}),(0,r.jsxs)("div",{className:"bg-gray-50 rounded-xl p-4 mb-4",children:[(0,r.jsx)("div",{className:"flex items-center justify-center gap-3 text-sm text-gray-600 mb-3",children:(0,r.jsx)("span",{className:"font-medium",children:"الأنواع المدعومة:"})}),(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsx)(f.E,{variant:"outline",className:"text-sm font-medium bg-green-50 text-green-700 border-green-200",children:"JPG"}),(0,r.jsx)(f.E,{variant:"outline",className:"text-sm font-medium bg-blue-50 text-blue-700 border-blue-200",children:"PNG"}),(0,r.jsx)(f.E,{variant:"outline",className:"text-sm font-medium bg-purple-50 text-purple-700 border-purple-200",children:"WebP"})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2 text-sm text-gray-500",children:[(0,r.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})}),(0,r.jsx)("span",{children:"حد أقصى: 5 ميجابايت"})]})]}),(0,r.jsx)("input",{ref:_,type:"file",accept:"image/*",onChange:e=>{var a;let s=null==(a=e.target.files)?void 0:a[0];s&&$(s)},className:"hidden",disabled:E})]}),(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-6",children:[(0,r.jsx)("svg",{className:"w-5 h-5 text-diwan-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z",clipRule:"evenodd"})}),(0,r.jsx)(i.J,{className:"text-lg font-semibold text-gray-800",children:"معلومات الصورة"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(i.J,{htmlFor:"title",className:"text-sm font-semibold text-gray-700 flex items-center gap-2",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-red-500 rounded-full"}),"العنوان"]}),(0,r.jsx)(d.p,{id:"title",value:j,onChange:e=>y(e.target.value),placeholder:"أدخل عنوان واضح ومميز للصورة",disabled:E,required:!0,className:"h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 text-base"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(i.J,{className:"text-sm font-semibold text-gray-700 flex items-center gap-2",children:[(0,r.jsx)(u.A,{className:"w-4 h-4 text-purple-600"}),"ربط بمجلد (اختياري)"]}),(0,r.jsxs)(c.l6,{value:C,onValueChange:e=>{z(e),"none"!==e&&A("none")},disabled:E||G,children:[(0,r.jsx)(c.bq,{className:"h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 text-base",children:(0,r.jsx)(c.yv,{placeholder:G?"جاري التحميل...":"اختر مجلد لتنظيم الصورة"})}),(0,r.jsxs)(c.gC,{children:[(0,r.jsx)(c.eb,{value:"none",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(m.A,{className:"w-4 h-4 ml-2 text-gray-400"}),"بدون ربط (صور عامة)"]})}),F.map(e=>(0,r.jsx)(c.eb,{value:e.id,children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(u.A,{className:"w-4 h-4 ml-2 text-purple-600"}),e.title]})},e.id))]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(i.J,{className:"text-sm font-semibold text-gray-700 flex items-center gap-2",children:[(0,r.jsx)(g.A,{className:"w-4 h-4 text-green-600"}),"ربط بنشاط (اختياري)"]}),(0,r.jsxs)(c.l6,{value:k,onValueChange:e=>{A(e),"none"!==e&&z("none")},disabled:E||q||"none"!==C,children:[(0,r.jsx)(c.bq,{className:"h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 text-base",children:(0,r.jsx)(c.yv,{placeholder:q?"جاري التحميل...":"none"!==C?"تم اختيار مجلد":"اختر نشاط مرتبط"})}),(0,r.jsxs)(c.gC,{children:[(0,r.jsx)(c.eb,{value:"none",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(m.A,{className:"w-4 h-4 ml-2 text-gray-400"}),"بدون ربط"]})}),V.map(e=>(0,r.jsx)(c.eb,{value:e.id,children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(g.A,{className:"w-4 h-4 ml-2 text-green-600"}),e.title]})},e.id))]})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(i.J,{htmlFor:"description",className:"text-sm font-semibold text-gray-700 flex items-center gap-2",children:[(0,r.jsx)("svg",{className:"w-4 h-4 text-blue-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z",clipRule:"evenodd"})}),"الوصف (اختياري)"]}),(0,r.jsx)(n.T,{id:"description",value:N,onChange:e=>w(e.target.value),placeholder:"أضف وصفاً تفصيلياً للصورة، المناسبة، أو الحدث المصور...",disabled:E,rows:4,className:"border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 resize-none text-base"})]})]})]}),J&&(0,r.jsx)("div",{className:"bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-xl p-5 shadow-sm",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0 bg-red-100 rounded-full p-2",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-red-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-semibold text-red-800 mb-1",children:"حدث خطأ"}),(0,r.jsx)("p",{className:"text-sm text-red-700",children:J})]})]})}),(0,r.jsxs)(o.Es,{className:"gap-4 pt-8 border-t border-gray-100",children:[(0,r.jsxs)(t.$,{type:"button",variant:"outline",onClick:()=>s(!1),disabled:E,className:"flex-1 h-14 border-2 border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 font-semibold text-base rounded-xl transition-all duration-200",children:[(0,r.jsx)(h.A,{className:"w-5 h-5 ml-2"}),"إلغاء"]}),(0,r.jsx)(t.$,{type:"submit",disabled:E||!P||!j.trim(),className:"flex-1 h-14 bg-gradient-to-r from-emerald-600 via-green-600 to-teal-600 hover:from-emerald-700 hover:via-green-700 hover:to-teal-700 text-white font-bold text-base shadow-xl hover:shadow-2xl transition-all duration-300 rounded-xl border-0 transform hover:scale-105 active:scale-95",children:E?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-6 h-6 ml-2 border-3 border-white border-t-transparent rounded-full animate-spin"}),(0,r.jsx)("span",{className:"font-bold",children:"جاري الرفع..."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"bg-white/20 rounded-full p-1 ml-2",children:(0,r.jsx)(x.A,{className:"w-5 h-5 text-white"})}),(0,r.jsx)("span",{className:"font-bold",children:"رفع الصورة"})]})})]})]})]})})}},85057:(e,a,s)=>{s.d(a,{J:()=>d});var r=s(95155),l=s(12115),t=s(59434);let d=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,r.jsx)("label",{ref:a,className:(0,t.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",s),...l})});d.displayName="Label"},88539:(e,a,s)=>{s.d(a,{T:()=>d});var r=s(95155),l=s(12115),t=s(59434);let d=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,r.jsx)("textarea",{className:(0,t.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:a,...l})});d.displayName="Textarea"},92138:(e,a,s)=>{s.d(a,{A:()=>r});let r=(0,s(19946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},92657:(e,a,s)=>{s.d(a,{A:()=>r});let r=(0,s(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);