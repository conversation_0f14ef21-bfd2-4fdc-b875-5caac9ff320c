'use client'

import { useState, useEffect } from 'react'
// import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
// import { Button } from '@/components/ui/button'
// import { Badge } from '@/components/ui/badge'
import {
  Building2,
  Globe,
  FileText,
  Calendar
} from 'lucide-react'

interface GeneralSettingsProps {
  settings: any
  onChange: (settings: any) => void
  canEdit: boolean
}

interface GeneralSettingsData {
  // معلومات الديوان الأساسية
  diwanName: string
  diwanDescription: string
  diwanAddress: string
  diwanPhone: string
  diwanEmail: string
  diwanWebsite: string
  
  // الإعدادات الإقليمية
  defaultCurrency: string
  currencySymbol: string
  timezone: string
  dateFormat: string
  timeFormat: string
  language: string
  
  // إعدادات النظام
  itemsPerPage: number
  autoSave: boolean
  enableAuditLog: boolean
  sessionTimeout: number
  
  // إعدادات العرض
  showWelcomeMessage: boolean
  welcomeMessage: string
  enableDashboardStats: boolean
  enableQuickActions: boolean
}

const defaultSettings: GeneralSettingsData = {
  diwanName: 'ديوان آل أبو علوش',
  diwanDescription: 'نظام إدارة ديوان آل أبو علوش',
  diwanAddress: '',
  diwanPhone: '',
  diwanEmail: '',
  diwanWebsite: '',
  defaultCurrency: 'JOD',
  currencySymbol: 'د.أ',
  timezone: 'Asia/Amman',
  dateFormat: 'DD/MM/YYYY',
  timeFormat: '24h',
  language: 'ar',
  itemsPerPage: 10,
  autoSave: true,
  enableAuditLog: true,
  sessionTimeout: 30,
  showWelcomeMessage: true,
  welcomeMessage: 'مرحباً بك في ديوان آل أبو علوش',
  enableDashboardStats: true,
  enableQuickActions: true
}

const currencies = [
  { value: 'JOD', label: 'دينار أردني (JOD)', symbol: 'د.أ' },
  { value: 'USD', label: 'دولار أمريكي (USD)', symbol: '$' },
  { value: 'EUR', label: 'يورو (EUR)', symbol: '€' },
  { value: 'SAR', label: 'ريال سعودي (SAR)', symbol: 'ر.س' },
  { value: 'AED', label: 'درهم إماراتي (AED)', symbol: 'د.إ' },
  { value: 'KWD', label: 'دينار كويتي (KWD)', symbol: 'د.ك' },
  { value: 'QAR', label: 'ريال قطري (QAR)', symbol: 'ر.ق' },
  { value: 'BHD', label: 'دينار بحريني (BHD)', symbol: 'د.ب' }
]

const timezones = [
  { value: 'Asia/Amman', label: 'عمان (UTC+3)' },
  { value: 'Asia/Riyadh', label: 'الرياض (UTC+3)' },
  { value: 'Asia/Dubai', label: 'دبي (UTC+4)' },
  { value: 'Asia/Kuwait', label: 'الكويت (UTC+3)' },
  { value: 'Asia/Qatar', label: 'الدوحة (UTC+3)' },
  { value: 'Asia/Bahrain', label: 'المنامة (UTC+3)' }
]

const dateFormats = [
  { value: 'DD/MM/YYYY', label: 'يوم/شهر/سنة (31/12/2024)' },
  { value: 'MM/DD/YYYY', label: 'شهر/يوم/سنة (12/31/2024)' },
  { value: 'YYYY-MM-DD', label: 'سنة-شهر-يوم (2024-12-31)' },
  { value: 'DD-MM-YYYY', label: 'يوم-شهر-سنة (31-12-2024)' }
]

export default function GeneralSettings({ settings, onChange, canEdit }: GeneralSettingsProps) {
  const [localSettings, setLocalSettings] = useState<GeneralSettingsData>(defaultSettings)

  useEffect(() => {
    if (settings) {
      setLocalSettings({ ...defaultSettings, ...settings })
    }
  }, [settings])

  const handleChange = (key: keyof GeneralSettingsData, value: any) => {
    const newSettings = { ...localSettings, [key]: value }
    setLocalSettings(newSettings)
    onChange(newSettings)
  }

  const handleCurrencyChange = (currencyCode: string) => {
    const currency = currencies.find(c => c.value === currencyCode)
    if (currency) {
      handleChange('defaultCurrency', currencyCode)
      handleChange('currencySymbol', currency.symbol)
    }
  }

  return (
    <div className="space-y-6">
      {/* معلومات الديوان الأساسية */}
      <div className="diwan-card">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
            <Building2 className="w-5 h-5 text-white" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900">معلومات الديوان الأساسية</h3>
        </div>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="diwanName">اسم الديوان</Label>
              <Input
                id="diwanName"
                value={localSettings.diwanName}
                onChange={(e) => handleChange('diwanName', e.target.value)}
                disabled={!canEdit}
                placeholder="اسم الديوان"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="diwanEmail">البريد الإلكتروني</Label>
              <Input
                id="diwanEmail"
                type="email"
                value={localSettings.diwanEmail}
                onChange={(e) => handleChange('diwanEmail', e.target.value)}
                disabled={!canEdit}
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="diwanDescription">وصف الديوان</Label>
            <Textarea
              id="diwanDescription"
              value={localSettings.diwanDescription}
              onChange={(e) => handleChange('diwanDescription', e.target.value)}
              disabled={!canEdit}
              placeholder="وصف مختصر عن الديوان"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="diwanPhone">رقم الهاتف</Label>
              <Input
                id="diwanPhone"
                value={localSettings.diwanPhone}
                onChange={(e) => handleChange('diwanPhone', e.target.value)}
                disabled={!canEdit}
                placeholder="+962 6 1234567"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="diwanWebsite">الموقع الإلكتروني</Label>
              <Input
                id="diwanWebsite"
                value={localSettings.diwanWebsite}
                onChange={(e) => handleChange('diwanWebsite', e.target.value)}
                disabled={!canEdit}
                placeholder="https://example.com"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="diwanAddress">العنوان</Label>
            <Textarea
              id="diwanAddress"
              value={localSettings.diwanAddress}
              onChange={(e) => handleChange('diwanAddress', e.target.value)}
              disabled={!canEdit}
              placeholder="العنوان الكامل للديوان"
              rows={2}
            />
          </div>
        </div>
      </div>

      {/* الإعدادات الإقليمية */}
      <div className="diwan-card">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
            <Globe className="w-5 h-5 text-white" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900">الإعدادات الإقليمية</h3>
        </div>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>العملة الافتراضية</Label>
              <Select
                value={localSettings.defaultCurrency}
                onValueChange={handleCurrencyChange}
                disabled={!canEdit}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر العملة" />
                </SelectTrigger>
                <SelectContent>
                  {currencies.map((currency) => (
                    <SelectItem key={currency.value} value={currency.value}>
                      <div className="flex items-center gap-2">
                        <span>{currency.symbol}</span>
                        <span>{currency.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>المنطقة الزمنية</Label>
              <Select
                value={localSettings.timezone}
                onValueChange={(value) => handleChange('timezone', value)}
                disabled={!canEdit}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر المنطقة الزمنية" />
                </SelectTrigger>
                <SelectContent>
                  {timezones.map((tz) => (
                    <SelectItem key={tz.value} value={tz.value}>
                      {tz.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>تنسيق التاريخ</Label>
              <Select
                value={localSettings.dateFormat}
                onValueChange={(value) => handleChange('dateFormat', value)}
                disabled={!canEdit}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر تنسيق التاريخ" />
                </SelectTrigger>
                <SelectContent>
                  {dateFormats.map((format) => (
                    <SelectItem key={format.value} value={format.value}>
                      {format.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>تنسيق الوقت</Label>
              <Select
                value={localSettings.timeFormat}
                onValueChange={(value) => handleChange('timeFormat', value)}
                disabled={!canEdit}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر تنسيق الوقت" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="12h">12 ساعة (1:30 PM)</SelectItem>
                  <SelectItem value="24h">24 ساعة (13:30)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>

      {/* إعدادات النظام */}
      <div className="diwan-card">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
            <FileText className="w-5 h-5 text-white" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900">إعدادات النظام</h3>
        </div>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="itemsPerPage">عدد العناصر في الصفحة</Label>
              <Select
                value={localSettings.itemsPerPage.toString()}
                onValueChange={(value) => handleChange('itemsPerPage', parseInt(value))}
                disabled={!canEdit}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 عناصر</SelectItem>
                  <SelectItem value="10">10 عناصر</SelectItem>
                  <SelectItem value="20">20 عنصر</SelectItem>
                  <SelectItem value="50">50 عنصر</SelectItem>
                  <SelectItem value="100">100 عنصر</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="sessionTimeout">مدة انتهاء الجلسة (دقيقة)</Label>
              <Select
                value={localSettings.sessionTimeout.toString()}
                onValueChange={(value) => handleChange('sessionTimeout', parseInt(value))}
                disabled={!canEdit}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="15">15 دقيقة</SelectItem>
                  <SelectItem value="30">30 دقيقة</SelectItem>
                  <SelectItem value="60">ساعة واحدة</SelectItem>
                  <SelectItem value="120">ساعتان</SelectItem>
                  <SelectItem value="480">8 ساعات</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>الحفظ التلقائي</Label>
                <p className="text-sm text-gray-600">حفظ التغييرات تلقائياً أثناء الكتابة</p>
              </div>
              <Switch
                checked={localSettings.autoSave}
                onCheckedChange={(checked) => handleChange('autoSave', checked)}
                disabled={!canEdit}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>سجل العمليات</Label>
                <p className="text-sm text-gray-600">تسجيل جميع العمليات والتغييرات</p>
              </div>
              <Switch
                checked={localSettings.enableAuditLog}
                onCheckedChange={(checked) => handleChange('enableAuditLog', checked)}
                disabled={!canEdit}
              />
            </div>
          </div>
        </div>
      </div>

      {/* إعدادات العرض */}
      <div className="diwan-card">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg">
            <Calendar className="w-5 h-5 text-white" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900">إعدادات العرض</h3>
        </div>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label>رسالة الترحيب</Label>
              <p className="text-sm text-gray-600">عرض رسالة ترحيب في لوحة التحكم</p>
            </div>
            <Switch
              checked={localSettings.showWelcomeMessage}
              onCheckedChange={(checked) => handleChange('showWelcomeMessage', checked)}
              disabled={!canEdit}
            />
          </div>

          {localSettings.showWelcomeMessage && (
            <div className="space-y-2">
              <Label htmlFor="welcomeMessage">نص رسالة الترحيب</Label>
              <Input
                id="welcomeMessage"
                value={localSettings.welcomeMessage}
                onChange={(e) => handleChange('welcomeMessage', e.target.value)}
                disabled={!canEdit}
                placeholder="مرحباً بك في ديوان آل أبو علوش"
              />
            </div>
          )}

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label>إحصائيات لوحة التحكم</Label>
              <p className="text-sm text-gray-600">عرض الإحصائيات في لوحة التحكم</p>
            </div>
            <Switch
              checked={localSettings.enableDashboardStats}
              onCheckedChange={(checked) => handleChange('enableDashboardStats', checked)}
              disabled={!canEdit}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label>الإجراءات السريعة</Label>
              <p className="text-sm text-gray-600">عرض أزرار الإجراءات السريعة</p>
            </div>
            <Switch
              checked={localSettings.enableQuickActions}
              onCheckedChange={(checked) => handleChange('enableQuickActions', checked)}
              disabled={!canEdit}
            />
          </div>
        </div>
      </div>
    </div>
  )
}
