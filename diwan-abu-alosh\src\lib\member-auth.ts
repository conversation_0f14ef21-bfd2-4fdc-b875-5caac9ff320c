import { NextRequest, NextResponse } from 'next/server'
import jwt from 'jsonwebtoken'
import { prisma } from '@/lib/prisma'

export interface MemberSession {
  memberId: string
  memberName: string
  memberEmail: string
  type: 'member'
}

// دالة للتحقق من مصادقة العضو
export async function verifyMemberAuth(request: NextRequest): Promise<{
  authenticated: boolean
  member?: {
    id: string
    name: string
    email: string
    phone?: string
  }
  error?: string
}> {
  try {
    const token = request.cookies.get('member-token')?.value

    if (!token) {
      return { authenticated: false, error: 'غير مسجل الدخول' }
    }

    // التحقق من صحة التوكن
    let decoded: MemberSession
    try {
      decoded = jwt.verify(token, process.env.NEXTAUTH_SECRET || 'fallback-secret') as MemberSession
    } catch (error) {
      // تجنب تسجيل أخطاء انتهاء الصلاحية العادية
      if (!(error instanceof jwt.TokenExpiredError)) {
        console.error('خطأ في التحقق من التوكن:', error)
      }
      return { authenticated: false, error: 'جلسة غير صالحة' }
    }

    if (decoded.type !== 'member') {
      return { authenticated: false, error: 'نوع المستخدم غير صحيح' }
    }

    // التحقق من وجود العضو في قاعدة البيانات
    const member = await prisma.member.findFirst({
      where: { 
        id: decoded.memberId,
        status: 'ACTIVE'
      }
    })

    if (!member) {
      return { authenticated: false, error: 'العضو غير موجود أو غير نشط' }
    }

    return {
      authenticated: true,
      member: {
        id: member.id,
        name: member.name,
        email: member.email,
        phone: member.phone
      }
    }
  } catch (error) {
    console.error('خطأ في التحقق من مصادقة العضو:', error)
    return { authenticated: false, error: 'خطأ في التحقق من الهوية' }
  }
}

// دالة للتحقق من مصادقة العضو في API routes
export async function requireMemberAuth(request: NextRequest) {
  const authResult = await verifyMemberAuth(request)
  
  if (!authResult.authenticated) {
    return NextResponse.json(
      { message: authResult.error || 'يجب تسجيل الدخول أولاً' },
      { status: 401 }
    )
  }

  return { member: authResult.member }
}

// دالة لإنشاء توكن العضو
export function createMemberToken(member: { id: string; name: string; email: string }): string {
  return jwt.sign(
    { 
      memberId: member.id,
      memberName: member.name,
      memberEmail: member.email,
      type: 'member'
    },
    process.env.NEXTAUTH_SECRET || 'fallback-secret',
    { expiresIn: '24h' }
  )
}

// دالة للحصول على معلومات العضو من التوكن
export function getMemberFromToken(token: string): MemberSession | null {
  try {
    const decoded = jwt.verify(token, process.env.NEXTAUTH_SECRET || 'fallback-secret') as MemberSession
    
    if (decoded.type !== 'member') {
      return null
    }

    return decoded
  } catch {
    return null
  }
}
