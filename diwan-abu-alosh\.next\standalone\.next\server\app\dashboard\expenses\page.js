(()=>{var e={};e.id=5343,e.ids=[5343],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,t,s)=>{"use strict";s.d(t,{A0:()=>d,BF:()=>i,Hj:()=>n,XI:()=>o,nA:()=>x,nd:()=>c});var r=s(60687),a=s(43210),l=s(4780);let o=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:s,className:(0,l.cn)("w-full caption-bottom text-sm",e),...t})}));o.displayName="Table";let d=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("thead",{ref:s,className:(0,l.cn)("[&_tr]:border-b",e),...t}));d.displayName="TableHeader";let i=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("tbody",{ref:s,className:(0,l.cn)("[&_tr:last-child]:border-0",e),...t}));i.displayName="TableBody",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("tfoot",{ref:s,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let n=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("tr",{ref:s,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));n.displayName="TableRow";let c=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("th",{ref:s,className:(0,l.cn)("h-12 px-4 text-right align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));c.displayName="TableHead";let x=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("td",{ref:s,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));x.displayName="TableCell"},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},14862:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>P});var r=s(60687),a=s(43210),l=s(82136),o=s(29523),d=s(89667),i=s(44493),n=s(96834),c=s(6211),x=s(12640),p=s(31158),m=s(96474),h=s(23928),u=s(23576),g=s(99270),b=s(80462),f=s(40228),y=s(63143),v=s(88233),j=s(15079),N=s(4780),w=s(27605),C=s(57335),k=s(9275),A=s(80013),D=s(34729),E=s(63503);let z=k.z.object({amount:k.z.number().positive("المبلغ يجب أن يكون أكبر من صفر"),date:k.z.string().min(1,"التاريخ مطلوب"),description:k.z.string().min(1,"الوصف مطلوب"),category:k.z.enum(["MEETINGS","EVENTS","MAINTENANCE","SOCIAL","GENERAL"]).default("GENERAL"),recipient:k.z.string().optional(),notes:k.z.string().optional()});function S({open:e,onOpenChange:t,onSuccess:s,expense:l=null}){let[i,n]=(0,a.useState)(!1),{register:c,handleSubmit:x,reset:p,setValue:m,watch:g,formState:{errors:b}}=(0,w.mN)({resolver:(0,C.u)(z),defaultValues:{amount:0,date:new Date().toISOString().split("T")[0],description:"",category:"GENERAL",recipient:"",notes:""}}),f=g("category"),y=async e=>{try{n(!0);let r={amount:Number(e.amount),date:new Date(e.date),description:e.description.trim(),category:e.category,recipient:e.recipient?.trim()||null,notes:e.notes?.trim()||null},a=!!l,o=a?`/api/expenses/${l.id}`:"/api/expenses",d=await fetch(o,{method:a?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!d.ok){let e=await d.json();throw Error(e.error||"حدث خطأ")}alert(a?"تم تحديث المصروف بنجاح!":"تم إضافة المصروف بنجاح!"),s?.(),t(!1)}catch(e){console.error("خطأ في حفظ المصروف:",e),alert(e.message||"حدث خطأ في حفظ المصروف")}finally{n(!1)}};return(0,r.jsx)(E.lG,{open:e,onOpenChange:t,children:(0,r.jsxs)(E.Cf,{className:"max-w-[50vw] max-h-[90vh] overflow-y-auto",children:[(0,r.jsx)(E.c7,{className:"pb-6 border-b border-gray-100",children:(0,r.jsxs)(E.L3,{className:"text-2xl font-bold text-gray-900 flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-red-500 to-orange-500 rounded-xl flex items-center justify-center",children:(0,r.jsx)(u.A,{className:"w-6 h-6 text-white"})}),l?"تعديل المصروف":"إضافة مصروف جديد"]})}),(0,r.jsxs)("form",{onSubmit:x(y),className:"p-8 space-y-8 bg-gray-50/30",children:[(0,r.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border border-gray-100",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-red-600 rounded-full"}),"المعلومات الأساسية"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(A.J,{htmlFor:"amount",className:"text-sm font-medium text-gray-700 flex items-center gap-1",children:["المبلغ (دينار أردني)",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(d.p,{id:"amount",type:"number",step:"0.01",min:"0",...c("amount",{valueAsNumber:!0}),className:`h-12 pr-12 transition-all duration-200 ${b.amount?"border-red-300 focus:border-red-500 focus:ring-red-200":"border-gray-200 focus:border-red-500 focus:ring-red-100"}`,placeholder:"0.00"}),(0,r.jsx)("div",{className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400",children:(0,r.jsx)(h.A,{className:"w-5 h-5"})})]}),b.amount&&(0,r.jsxs)("p",{className:"text-red-500 text-sm flex items-center gap-1",children:[(0,r.jsx)("span",{className:"w-1 h-1 bg-red-500 rounded-full"}),b.amount.message]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(A.J,{htmlFor:"date",className:"text-sm font-medium text-gray-700 flex items-center gap-1",children:["التاريخ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)(d.p,{id:"date",type:"date",...c("date"),className:`h-12 transition-all duration-200 ${b.date?"border-red-300 focus:border-red-500 focus:ring-red-200":"border-gray-200 focus:border-red-500 focus:ring-red-100"}`}),b.date&&(0,r.jsxs)("p",{className:"text-red-500 text-sm flex items-center gap-1",children:[(0,r.jsx)("span",{className:"w-1 h-1 bg-red-500 rounded-full"}),b.date.message]})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border border-gray-100",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-orange-600 rounded-full"}),"تفاصيل المصروف"]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(A.J,{htmlFor:"description",className:"text-sm font-medium text-gray-700 flex items-center gap-1",children:["الوصف",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)(d.p,{id:"description",...c("description"),placeholder:"مثال: شراء مستلزمات، دفع فواتير، تكاليف فعالية",className:`h-12 transition-all duration-200 ${b.description?"border-red-300 focus:border-red-500 focus:ring-red-200":"border-gray-200 focus:border-red-500 focus:ring-red-100"}`}),b.description&&(0,r.jsxs)("p",{className:"text-red-500 text-sm flex items-center gap-1",children:[(0,r.jsx)("span",{className:"w-1 h-1 bg-red-500 rounded-full"}),b.description.message]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(A.J,{className:"text-sm font-medium text-gray-700",children:"الفئة"}),(0,r.jsxs)(j.l6,{value:f,onValueChange:e=>m("category",e),children:[(0,r.jsx)(j.bq,{className:"h-12 border-gray-200 focus:border-red-500 focus:ring-red-100",children:(0,r.jsx)(j.yv,{placeholder:"اختر الفئة"})}),(0,r.jsxs)(j.gC,{children:[(0,r.jsx)(j.eb,{value:"GENERAL",children:"\uD83C\uDFE2 عامة"}),(0,r.jsx)(j.eb,{value:"MEETINGS",children:"\uD83E\uDD1D اجتماعات"}),(0,r.jsx)(j.eb,{value:"EVENTS",children:"\uD83C\uDF89 مناسبات"}),(0,r.jsx)(j.eb,{value:"MAINTENANCE",children:"\uD83D\uDD27 إصلاحات"}),(0,r.jsx)(j.eb,{value:"SOCIAL",children:"\uD83D\uDC65 اجتماعية"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(A.J,{htmlFor:"recipient",className:"text-sm font-medium text-gray-700",children:"الجهة المستفيدة"}),(0,r.jsx)(d.p,{id:"recipient",...c("recipient"),placeholder:"مثال: شركة، مورد، مقاول",className:"h-12 border-gray-200 focus:border-red-500 focus:ring-red-100 transition-all duration-200"})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(A.J,{htmlFor:"notes",className:"text-sm font-medium text-gray-700",children:"ملاحظات إضافية"}),(0,r.jsx)(D.T,{id:"notes",...c("notes"),placeholder:"أي ملاحظات أو تفاصيل إضافية...",className:"min-h-[100px] border-gray-200 focus:border-red-500 focus:ring-red-100 transition-all duration-200 resize-none"})]})]})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-4 pt-6 border-t border-gray-100",children:[(0,r.jsx)(o.$,{type:"button",variant:"outline",onClick:()=>t(!1),className:"h-12 px-8 border-2 border-gray-300 text-gray-700 hover:border-gray-400 hover:text-gray-800 transition-all duration-200 font-medium",disabled:i,children:"إلغاء"}),(0,r.jsx)(o.$,{type:"submit",disabled:i,className:"h-12 px-8 bg-gradient-to-r from-red-600 via-red-700 to-orange-600 hover:from-red-700 hover:via-red-800 hover:to-orange-700 text-white font-bold transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-xl",children:i?(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),(0,r.jsx)("span",{children:"جاري الحفظ..."})]}):(0,r.jsx)("span",{children:l?"تحديث المصروف":"إضافة المصروف"})})]})]})]})})}var T=s(4403),$=s(94424);function P(){let{data:e}=(0,l.useSession)(),[t,s]=(0,a.useState)([]),[w,C]=(0,a.useState)(!0),[k,A]=(0,a.useState)(""),[D,E]=(0,a.useState)("all"),[z,P]=(0,a.useState)({page:1,limit:10,total:0,pages:0}),[q,R]=(0,a.useState)({totalAmount:0,totalCount:0,byCategory:[]}),[_,I]=(0,a.useState)(!1),[F,G]=(0,a.useState)(null),M=(0,a.useCallback)(async()=>{try{C(!0);let e=new URLSearchParams({search:k,category:D,page:z.page.toString(),limit:z.limit.toString()}),t=await fetch(`/api/expenses?${e}`);if(!t.ok)throw Error("فشل في جلب المصروفات");let r=await t.json();s(r.expenses),P(r.pagination)}catch(e){console.error("خطأ في جلب المصروفات:",e)}finally{C(!1)}},[k,D,z.page,z.limit]),O=async()=>{try{let e=await fetch("/api/expenses?limit=1000");if(!e.ok)return;let t=await e.json(),s=t.expenses.reduce((e,t)=>e+t.amount,0),r=t.expenses.length,a=t.expenses.reduce((e,t)=>{let s=e.find(e=>e.category===t.category);return s?(s._sum.amount+=t.amount,s._count+=1):e.push({category:t.category,_sum:{amount:t.amount},_count:1}),e},[]);R({totalAmount:s,totalCount:r,byCategory:a})}catch(e){console.error("خطأ في جلب الإحصائيات:",e)}},L=e=>{G(e),I(!0)},Z=async e=>{if(confirm("هل أنت متأكد من حذف هذا المصروف؟\n\nهذا الإجراء لا يمكن التراجع عنه."))try{let t=await fetch(`/api/expenses/${e}`,{method:"DELETE"});if(!t.ok){let e=await t.json();alert(e.error||"فشل في حذف المصروف");return}alert("تم حذف المصروف بنجاح"),M(),O()}catch(e){console.error("خطأ في حذف المصروف:",e),alert("حدث خطأ في حذف المصروف")}},V=e?.user.role!=="VIEWER",U=e?.user.role==="ADMIN";return(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsx)("div",{className:"text-center mb-8",children:(0,r.jsxs)("div",{className:"bg-gradient-to-r from-red-600 to-orange-600 rounded-3xl shadow-2xl p-10 text-white relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-red-400 to-orange-500 opacity-30 animate-pulse"}),(0,r.jsxs)("div",{className:"relative z-10",children:[(0,r.jsx)("div",{className:"inline-flex items-center justify-center w-20 h-20 rounded-full mb-6 bg-white bg-opacity-20 backdrop-blur-sm",children:(0,r.jsx)(x.A,{className:"w-10 h-10 text-white"})}),(0,r.jsx)("h1",{className:"text-5xl font-black mb-4 text-white",children:"إدارة المصروفات"}),(0,r.jsx)("p",{className:"text-xl font-semibold mb-6 text-red-100",children:"عرض وإدارة مصروفات الديوان بكفاءة وسهولة"}),(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2 space-x-reverse",children:[(0,r.jsx)("div",{className:"h-1 w-16 rounded-full bg-white bg-opacity-60"}),(0,r.jsx)("div",{className:"h-1 w-8 rounded-full bg-white bg-opacity-40"}),(0,r.jsx)("div",{className:"h-1 w-16 rounded-full bg-white bg-opacity-60"})]})]})]})}),(0,r.jsx)("div",{className:"flex justify-center mb-8",children:(0,r.jsx)("div",{className:"bg-white rounded-2xl shadow-xl p-6 border border-gray-100",children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-4 justify-center",children:[(0,r.jsxs)(o.$,{onClick:()=>{let e=document.createElement("div");e.style.cssText=`
      position: absolute;
      top: -9999px;
      left: -9999px;
      width: 210mm;
      background: white;
      padding: 20px;
      font-family: Arial, sans-serif;
      direction: rtl;
      text-align: right;
      color: #000;
      font-size: 12px;
    `,e.innerHTML=`
      <div style="text-align: center; margin-bottom: 30px; border-bottom: 3px solid #dc2626; padding-bottom: 15px;">
        <h1 style="color: #dc2626; font-size: 24px; margin: 0; font-weight: bold;">ديوان أبو علوش</h1>
        <h2 style="color: #666; font-size: 18px; margin: 8px 0; font-weight: normal;">تقرير المصروفات</h2>
        <p style="color: #888; font-size: 12px; margin: 5px 0;">تاريخ التقرير: ${new Date().toLocaleDateString("ar-SA")}</p>
      </div>

      <div style="margin-bottom: 20px; background: #f8f9fa; padding: 15px; border-radius: 8px; border-right: 4px solid #dc2626;">
        <h3 style="color: #dc2626; font-size: 16px; margin: 0 0 15px 0; font-weight: bold;">ملخص المصروفات</h3>
        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;">
          <div style="text-align: center; background: white; padding: 10px; border-radius: 6px; border: 1px solid #e5e7eb;">
            <div style="color: #dc2626; font-size: 20px; font-weight: bold;">${q.totalAmount.toFixed(2)} د.أ</div>
            <div style="color: #666; font-size: 11px;">إجمالي المصروفات</div>
          </div>
          <div style="text-align: center; background: white; padding: 10px; border-radius: 6px; border: 1px solid #e5e7eb;">
            <div style="color: #dc2626; font-size: 20px; font-weight: bold;">${q.totalCount}</div>
            <div style="color: #666; font-size: 11px;">عدد المصروفات</div>
          </div>
          <div style="text-align: center; background: white; padding: 10px; border-radius: 6px; border: 1px solid #e5e7eb;">
            <div style="color: #dc2626; font-size: 20px; font-weight: bold;">${(q.totalCount>0?q.totalAmount/q.totalCount:0).toFixed(2)} د.أ</div>
            <div style="color: #666; font-size: 11px;">متوسط المصروف</div>
          </div>
        </div>
      </div>

      <div style="margin-bottom: 20px;">
        <h3 style="color: #dc2626; font-size: 16px; margin: 0 0 15px 0; font-weight: bold; border-bottom: 2px solid #dc2626; padding-bottom: 8px;">تفاصيل المصروفات</h3>
        <table style="width: 100%; border-collapse: collapse; font-size: 11px; background: white; border-radius: 8px; overflow: hidden;">
          <thead>
            <tr style="background: #dc2626; color: white;">
              <th style="padding: 10px 8px; text-align: right; font-weight: bold;">الوصف</th>
              <th style="padding: 10px 8px; text-align: center; font-weight: bold;">المبلغ (د.أ)</th>
              <th style="padding: 10px 8px; text-align: center; font-weight: bold;">الفئة</th>
              <th style="padding: 10px 8px; text-align: center; font-weight: bold;">المستفيد</th>
              <th style="padding: 10px 8px; text-align: center; font-weight: bold;">التاريخ</th>
            </tr>
          </thead>
          <tbody>
            ${t.map((e,t)=>`
              <tr style="background: ${t%2==0?"#ffffff":"#f8f9fa"}; border-bottom: 1px solid #e5e7eb;">
                <td style="padding: 8px; text-align: right; border-left: 1px solid #e5e7eb;">
                  <div style="font-weight: bold; color: #374151;">${e.description}</div>
                  ${e.notes?`<div style="font-size: 9px; color: #6b7280; margin-top: 2px;">ملاحظة: ${e.notes}</div>`:""}
                </td>
                <td style="padding: 8px; text-align: center; font-weight: bold; color: #dc2626; border-left: 1px solid #e5e7eb;">${e.amount.toFixed(2)}</td>
                <td style="padding: 8px; text-align: center; border-left: 1px solid #e5e7eb;">
                  <span style="background: #fef2f2; color: #dc2626; padding: 4px 8px; border-radius: 4px; font-size: 10px; font-weight: bold;">
                    ${(0,N.uF)(e.category)}
                  </span>
                </td>
                <td style="padding: 8px; text-align: center; color: #374151; border-left: 1px solid #e5e7eb;">${e.recipient||"غير محدد"}</td>
                <td style="padding: 8px; text-align: center; color: #6b7280; font-size: 10px;">${(0,N.Yq)(e.date)}</td>
              </tr>
            `).join("")}
          </tbody>
        </table>
      </div>

      <div style="margin-top: 30px; padding-top: 15px; border-top: 2px solid #e5e7eb; text-align: center; color: #6b7280; font-size: 10px;">
        <p style="margin: 0;">ديوان أبو علوش - نظام إدارة العائلة</p>
        <p style="margin: 5px 0 0 0;">تم إنشاء هذا التقرير في: ${new Date().toLocaleString("ar-SA")}</p>
      </div>
    `,document.body.appendChild(e),setTimeout(()=>{(0,$.default)(e,{scale:1.5,useCORS:!0,allowTaint:!0,backgroundColor:"#ffffff",logging:!1}).then(t=>{let s=t.toDataURL("image/png"),r=new T.default("p","mm","a4"),a=210*t.height/t.width;if(a<=297)r.addImage(s,"PNG",0,0,210,a);else{let e=a,t=0;for(r.addImage(s,"PNG",0,t,210,a),e-=297;e>=0;)t=e-a,r.addPage(),r.addImage(s,"PNG",0,t,210,a),e-=297}let l=`تقرير_مصروفات_الديوان_${new Date().toISOString().split("T")[0]}.pdf`;r.save(l),document.body.removeChild(e)}).catch(t=>{console.error("خطأ في إنشاء PDF:",t),document.body.removeChild(e),alert("حدث خطأ في تصدير التقرير")})},300)},className:"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold",children:[(0,r.jsx)(p.A,{className:"w-5 h-5 ml-2"}),"تصدير PDF"]}),V&&(0,r.jsxs)(o.$,{onClick:()=>{G(null),I(!0)},className:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold",children:[(0,r.jsx)(m.A,{className:"w-5 h-5 ml-2"}),"إضافة مصروف جديد"]})]})})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3",children:[(0,r.jsxs)(i.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-red-500 to-red-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-red-500 to-red-600 p-1 rounded-t-xl"}),(0,r.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,r.jsx)(i.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"إجمالي المصروفات"}),(0,r.jsx)("div",{className:"p-4 rounded-2xl shadow-lg",style:{backgroundColor:"#dc3545"},children:(0,r.jsx)(h.A,{className:"h-7 w-7 text-white"})})]}),(0,r.jsxs)(i.Wu,{className:"relative z-10",children:[(0,r.jsx)("div",{className:"text-4xl font-black mb-2",style:{color:"#191970"},children:(0,N.vv)(q.totalAmount)}),(0,r.jsx)("p",{className:"text-sm font-semibold",style:{color:"#6c757d"},children:"إجمالي المبالغ"})]})]}),(0,r.jsxs)(i.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-orange-500 to-orange-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-orange-500 to-orange-600 p-1 rounded-t-xl"}),(0,r.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,r.jsx)(i.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"عدد المصروفات"}),(0,r.jsx)("div",{className:"p-4 rounded-2xl shadow-lg",style:{backgroundColor:"#fd7e14"},children:(0,r.jsx)(x.A,{className:"h-7 w-7 text-white"})})]}),(0,r.jsxs)(i.Wu,{className:"relative z-10",children:[(0,r.jsx)("div",{className:"text-4xl font-black mb-2",style:{color:"#191970"},children:q.totalCount}),(0,r.jsx)("p",{className:"text-sm font-semibold",style:{color:"#6c757d"},children:"إجمالي العمليات"})]})]}),(0,r.jsxs)(i.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative sm:col-span-2 lg:col-span-1",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-yellow-500 to-yellow-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-yellow-500 to-yellow-600 p-1 rounded-t-xl"}),(0,r.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,r.jsx)(i.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"متوسط المصروف"}),(0,r.jsx)("div",{className:"p-4 rounded-2xl shadow-lg",style:{backgroundColor:"#ffc107"},children:(0,r.jsx)(u.A,{className:"h-7 w-7 text-white"})})]}),(0,r.jsxs)(i.Wu,{className:"relative z-10",children:[(0,r.jsx)("div",{className:"text-4xl font-black mb-2",style:{color:"#191970"},children:(0,N.vv)(q.totalCount>0?q.totalAmount/q.totalCount:0)}),(0,r.jsx)("p",{className:"text-sm font-semibold",style:{color:"#6c757d"},children:"متوسط القيمة"})]})]})]}),(0,r.jsx)(i.Zp,{className:"bg-white/80 backdrop-blur-sm border-gray-200 shadow-lg",children:(0,r.jsxs)(i.Wu,{className:"p-8",children:[(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)(g.A,{className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5 group-focus-within:text-red-500 transition-colors duration-200"}),(0,r.jsx)(d.p,{placeholder:"البحث في المصروفات... (الوصف، الجهة المستفيدة، الملاحظات)",value:k,onChange:e=>A(e.target.value),className:"h-12 pr-12 pl-4 border-2 border-gray-200 focus:border-red-500 focus:ring-red-100 rounded-xl text-base transition-all duration-200 bg-white/50 backdrop-blur-sm"}),k&&(0,r.jsx)("button",{onClick:()=>A(""),className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500 transition-colors duration-200",children:"✕"})]})}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsxs)(j.l6,{value:D,onValueChange:E,children:[(0,r.jsx)(j.bq,{className:"w-full sm:w-[200px] h-12 border-2 border-gray-200 focus:border-red-500 focus:ring-red-100 rounded-xl bg-white/50 backdrop-blur-sm",children:(0,r.jsx)(j.yv,{placeholder:"فلترة حسب الفئة"})}),(0,r.jsxs)(j.gC,{className:"rounded-xl border-2 border-gray-200 shadow-xl",children:[(0,r.jsx)(j.eb,{value:"all",className:"rounded-lg",children:"\uD83C\uDFE2 جميع الفئات"}),(0,r.jsx)(j.eb,{value:"MEETINGS",className:"rounded-lg",children:"\uD83E\uDD1D اجتماعات"}),(0,r.jsx)(j.eb,{value:"EVENTS",className:"rounded-lg",children:"\uD83C\uDF89 مناسبات"}),(0,r.jsx)(j.eb,{value:"MAINTENANCE",className:"rounded-lg",children:"\uD83D\uDD27 إصلاحات"}),(0,r.jsx)(j.eb,{value:"SOCIAL",className:"rounded-lg",children:"\uD83D\uDC65 اجتماعية"}),(0,r.jsx)(j.eb,{value:"GENERAL",className:"rounded-lg",children:"\uD83D\uDCCB عامة"})]})]}),(0,r.jsx)(o.$,{variant:"outline",size:"icon",className:"h-12 w-12 border-2 border-gray-200 hover:border-red-500 hover:bg-red-50 rounded-xl transition-all duration-200 bg-white/50 backdrop-blur-sm",children:(0,r.jsx)(b.A,{className:"h-5 w-5 text-gray-600"})})]})]}),(k||"all"!==D)&&(0,r.jsxs)("div",{className:"mt-4 flex items-center gap-2 text-sm text-gray-600",children:[(0,r.jsxs)("span",{children:["عرض ",t.length," من أصل ",q.totalCount," مصروف"]}),k&&(0,r.jsxs)("span",{className:"px-2 py-1 bg-red-100 text-red-700 rounded-lg",children:['البحث: "',k,'"']}),"all"!==D&&(0,r.jsxs)("span",{className:"px-2 py-1 bg-orange-100 text-orange-700 rounded-lg",children:["الفئة: ",(0,N.uF)(D)]})]})]})}),(0,r.jsxs)(i.Zp,{className:"bg-white/90 backdrop-blur-sm border-gray-200 shadow-xl overflow-hidden",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-red-600 via-red-700 to-orange-600 p-6",children:(0,r.jsxs)("h3",{className:"text-xl font-bold text-white flex items-center gap-3",children:[(0,r.jsx)(u.A,{className:"w-6 h-6"}),"قائمة المصروفات",(0,r.jsxs)("span",{className:"text-red-200",children:["(",t.length,")"]})]})}),(0,r.jsx)(i.Wu,{className:"p-0",children:w?(0,r.jsxs)("div",{className:"flex flex-col justify-center items-center h-64 bg-gradient-to-br from-gray-50 to-red-50",children:[(0,r.jsx)("div",{className:"w-12 h-12 border-4 border-red-200 border-t-red-600 rounded-full animate-spin mb-4"}),(0,r.jsx)("div",{className:"text-gray-600 font-medium",children:"جاري تحميل المصروفات..."})]}):0===t.length?(0,r.jsxs)("div",{className:"flex flex-col justify-center items-center h-64 bg-gradient-to-br from-gray-50 to-red-50",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4",children:(0,r.jsx)(u.A,{className:"w-8 h-8 text-red-600"})}),(0,r.jsx)("div",{className:"text-gray-600 font-medium mb-2",children:"لا توجد مصروفات"}),(0,r.jsx)("div",{className:"text-gray-500 text-sm",children:"ابدأ بإضافة مصروف جديد"})]}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)(c.XI,{children:[(0,r.jsx)(c.A0,{className:"bg-gray-50/80",children:(0,r.jsxs)(c.Hj,{className:"border-b-2 border-gray-200",children:[(0,r.jsx)(c.nd,{className:"text-right font-bold text-gray-800 py-4",children:"الوصف"}),(0,r.jsx)(c.nd,{className:"text-center font-bold text-gray-800 py-4",children:"المبلغ"}),(0,r.jsx)(c.nd,{className:"text-center font-bold text-gray-800 py-4",children:"الفئة"}),(0,r.jsx)(c.nd,{className:"text-center font-bold text-gray-800 py-4",children:"الجهة المستفيدة"}),(0,r.jsx)(c.nd,{className:"text-center font-bold text-gray-800 py-4",children:"التاريخ"}),(0,r.jsx)(c.nd,{className:"text-center font-bold text-gray-800 py-4",children:"الإجراءات"})]})}),(0,r.jsx)(c.BF,{children:t.map((e,t)=>(0,r.jsxs)(c.Hj,{className:`border-b border-gray-100 hover:bg-red-50/50 transition-all duration-200 ${t%2==0?"bg-white":"bg-gray-50/30"}`,children:[(0,r.jsx)(c.nA,{className:"py-4",children:(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("div",{className:"font-semibold text-gray-900",children:e.description}),e.notes&&(0,r.jsxs)("div",{className:"text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded-lg inline-block",children:["\uD83D\uDCAC ",e.notes]})]})}),(0,r.jsx)(c.nA,{className:"text-center py-4",children:(0,r.jsx)("div",{className:"font-bold text-lg text-red-700 bg-red-50 px-3 py-1 rounded-lg inline-block",children:(0,N.vv)(e.amount)})}),(0,r.jsx)(c.nA,{className:"text-center py-4",children:(0,r.jsx)(n.E,{variant:"secondary",className:`px-3 py-1 font-medium ${"MEETINGS"===e.category?"bg-blue-100 text-blue-800":"EVENTS"===e.category?"bg-purple-100 text-purple-800":"MAINTENANCE"===e.category?"bg-orange-100 text-orange-800":"SOCIAL"===e.category?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:(0,N.uF)(e.category)})}),(0,r.jsx)(c.nA,{className:"text-center py-4",children:(0,r.jsx)("div",{className:"text-gray-700 font-medium",children:e.recipient||(0,r.jsx)("span",{className:"text-gray-400 italic",children:"غير محدد"})})}),(0,r.jsx)(c.nA,{className:"text-center py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2 text-gray-600",children:[(0,r.jsx)(f.A,{className:"w-4 h-4 text-red-500"}),(0,r.jsx)("span",{className:"font-medium",children:(0,N.Yq)(e.date)})]})}),(0,r.jsx)(c.nA,{className:"text-center py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[V&&(0,r.jsx)(o.$,{variant:"ghost",size:"sm",onClick:()=>L(e),className:"h-9 w-9 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-all duration-200",title:"تعديل المصروف",children:(0,r.jsx)(y.A,{className:"w-4 h-4"})}),U&&(0,r.jsx)(o.$,{variant:"ghost",size:"sm",onClick:()=>Z(e.id),className:"h-9 w-9 p-0 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-all duration-200",title:"حذف المصروف",children:(0,r.jsx)(v.A,{className:"w-4 h-4"})})]})})]},e.id))})]})})})]}),z.pages>1&&(0,r.jsxs)("div",{className:"flex justify-center space-x-2 space-x-reverse",children:[(0,r.jsx)(o.$,{variant:"outline",disabled:1===z.page,onClick:()=>P(e=>({...e,page:e.page-1})),children:"السابق"}),(0,r.jsxs)("span",{className:"flex items-center px-4",children:["صفحة ",z.page," من ",z.pages]}),(0,r.jsx)(o.$,{variant:"outline",disabled:z.page===z.pages,onClick:()=>P(e=>({...e,page:e.page+1})),children:"التالي"})]}),q.byCategory.length>0&&(0,r.jsxs)(i.Zp,{children:[(0,r.jsx)(i.aR,{children:(0,r.jsx)(i.ZB,{children:"المصروفات حسب الفئة"})}),(0,r.jsx)(i.Wu,{children:(0,r.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-5",children:q.byCategory.map(e=>(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-500",children:(0,N.uF)(e.category)}),(0,r.jsx)("div",{className:"mt-1 text-lg font-semibold text-gray-900",children:(0,N.vv)(e._sum.amount||0)}),(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:[e._count," مصروف"]})]},e.category))})})]}),(0,r.jsx)(S,{open:_,onOpenChange:()=>{I(!1),G(null)},expense:F,onSuccess:()=>{M(),O()}})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23575:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\ابوعلوش\\\\diwan-abu-alosh\\\\src\\\\app\\\\dashboard\\\\expenses\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\expenses\\page.tsx","default")},23576:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("receipt",[["path",{d:"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z",key:"q3az6g"}],["path",{d:"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8",key:"1h4pet"}],["path",{d:"M12 17.5v-11",key:"1jc1ny"}]])},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:e=>{"use strict";e.exports=require("path")},34729:(e,t,s)=>{"use strict";s.d(t,{T:()=>o});var r=s(60687),a=s(43210),l=s(4780);let o=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...t}));o.displayName="Textarea"},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},75060:(e,t,s)=>{Promise.resolve().then(s.bind(s,23575))},77268:(e,t,s)=>{Promise.resolve().then(s.bind(s,14862))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,s)=>{"use strict";s.d(t,{J:()=>o});var r=s(60687),a=s(43210),l=s(4780);let o=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("label",{ref:s,className:(0,l.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e),...t}));o.displayName="Label"},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},98462:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>p,tree:()=>n});var r=s(65239),a=s(48088),l=s(88170),o=s.n(l),d=s(30893),i={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>d[e]);s.d(t,i);let n={children:["",{children:["dashboard",{children:["expenses",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,23575)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\expenses\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,63144)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\expenses\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/expenses/page",pathname:"/dashboard/expenses",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,5663,4999,3412,5442,7934,5498,1726,2131,5662,2635,4403,6329,5977,6154],()=>s(98462));module.exports=r})();