(()=>{var e={};e.id=6950,e.ids=[6950],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,s,r)=>{"use strict";r.d(s,{N:()=>n});var t=r(13581),a=r(85663),i=r(31183);let n={providers:[(0,t.A)({name:"credentials",credentials:{email:{label:"البريد الإلكتروني",type:"email"},password:{label:"كلمة المرور",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let s=await i.z.user.findUnique({where:{email:e.email}});return s&&await a.Ay.compare(e.password,s.password)?{id:s.id,email:s.email,name:s.name,role:s.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:s})=>(s&&(e.role=s.role),e),session:async({session:e,token:s})=>(s&&(e.user.id=s.sub,e.user.role=s.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,s,r)=>{"use strict";r.d(s,{z:()=>a});var t=r(96330);let a=globalThis.prisma??new t.PrismaClient},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},74266:(e,s,r)=>{"use strict";r.r(s),r.d(s,{patchFetch:()=>f,routeModule:()=>x,serverHooks:()=>h,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>w});var t={};r.r(t),r.d(t,{GET:()=>d,POST:()=>m});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),u=r(19854),p=r(12909),l=r(31183),c=r(85663);async function d(){try{let e=await (0,u.getServerSession)(p.N);if(!e?.user||"ADMIN"!==e.user.role)return o.NextResponse.json({message:"غير مصرح لك بالوصول"},{status:403});let s=await l.z.user.findMany({select:{id:!0,name:!0,email:!0,phone:!0,role:!0,createdAt:!0,updatedAt:!0,_count:{select:{createdMembers:!0,createdIncomes:!0,notifications:!0}}},orderBy:{createdAt:"desc"}});return o.NextResponse.json(s)}catch(e){return console.error("خطأ في جلب المستخدمين:",e),o.NextResponse.json({message:"حدث خطأ في الخادم"},{status:500})}}async function m(e){try{let s=await (0,u.getServerSession)(p.N);if(!s?.user||"ADMIN"!==s.user.role)return o.NextResponse.json({message:"غير مصرح لك بالوصول"},{status:403});let{name:r,email:t,phone:a,role:i,password:n}=await e.json();if(!r||!t||!n||!i)return o.NextResponse.json({message:"جميع الحقول مطلوبة"},{status:400});if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t))return o.NextResponse.json({message:"البريد الإلكتروني غير صحيح"},{status:400});if(await l.z.user.findUnique({where:{email:t}}))return o.NextResponse.json({message:"البريد الإلكتروني مستخدم بالفعل"},{status:400});if(!["ADMIN","DATA_ENTRY","VIEWER"].includes(i))return o.NextResponse.json({message:"الدور المحدد غير صحيح"},{status:400});if(n.length<6)return o.NextResponse.json({message:"كلمة المرور يجب أن تكون 6 أحرف على الأقل"},{status:400});let d=await c.Ay.hash(n,12),m=await l.z.user.create({data:{name:r,email:t,phone:a||null,role:i,password:d},select:{id:!0,name:!0,email:!0,phone:!0,role:!0,createdAt:!0}});return o.NextResponse.json(m,{status:201})}catch(e){return console.error("خطأ في إضافة المستخدم:",e),o.NextResponse.json({message:"حدث خطأ في الخادم"},{status:500})}}let x=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/users/route",pathname:"/api/admin/users",filename:"route",bundlePath:"app/api/admin/users/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\admin\\users\\route.ts",nextConfigOutput:"standalone",userland:t}),{workAsyncStorage:g,workUnitAsyncStorage:w,serverHooks:h}=x;function f(){return(0,n.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:w})}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4243,5663,4999,3412,580],()=>r(74266));module.exports=t})();