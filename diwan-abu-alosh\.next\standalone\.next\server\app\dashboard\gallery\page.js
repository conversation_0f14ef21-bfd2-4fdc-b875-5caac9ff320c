(()=>{var e={};e.id=984,e.ids=[984],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:e=>{"use strict";e.exports=require("path")},42809:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>O});var a=t(60687),r=t(43210);t(45386);var l=t(82136),i=t(29523),d=t(89667),n=t(44493),o=t(96834),c=t(63503),x=t(9005),h=t(82570),m=t(96474),u=t(16023),p=t(40228),g=t(58869),j=t(99270),b=t(11860),v=t(13861),f=t(63143),N=t(31158),y=t(88233),w=t(93372),A=t(41312),k=t(70334);function C({folders:e,onFolderClick:s,onEditFolder:t,onDeleteFolder:r,canEdit:l=!1,canDelete:d=!1,loading:c}){return c?(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:[...Array(8)].map((e,s)=>(0,a.jsx)(n.Zp,{className:"animate-pulse",children:(0,a.jsxs)(n.Wu,{className:"p-0",children:[(0,a.jsx)("div",{className:"aspect-video bg-gray-200 rounded-t-lg"}),(0,a.jsxs)("div",{className:"p-4 space-y-2",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]})},s))}):0===e.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(h.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"لا توجد مجلدات"}),(0,a.jsx)("p",{className:"text-gray-500",children:"لم يتم العثور على أي مجلدات"})]}):(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:e.map(e=>(0,a.jsx)(n.Zp,{className:"group hover:shadow-lg transition-all duration-200 border-2 hover:border-diwan-300 relative",children:(0,a.jsxs)(n.Wu,{className:"p-0",children:[(l||d)&&("activity"===e.type||"folder"===e.type)&&(0,a.jsxs)("div",{className:"absolute top-2 left-2 z-10 flex space-x-1 space-x-reverse opacity-0 group-hover:opacity-100 transition-opacity",children:[l&&t&&(0,a.jsx)(i.$,{variant:"secondary",size:"sm",className:"h-8 w-8 p-0 bg-white/90 hover:bg-white",onClick:s=>{s.stopPropagation(),t(e)},title:"تعديل المجلد",children:(0,a.jsx)(f.A,{className:"h-3 w-3"})}),d&&r&&(0,a.jsx)(i.$,{variant:"secondary",size:"sm",className:"h-8 w-8 p-0 bg-white/90 hover:bg-white text-red-600 hover:text-red-700",onClick:s=>{s.stopPropagation(),r(e)},title:"حذف المجلد",children:(0,a.jsx)(y.A,{className:"h-3 w-3"})})]}),(0,a.jsxs)("div",{className:"relative aspect-video overflow-hidden rounded-t-lg bg-gradient-to-br from-diwan-50 to-diwan-100 cursor-pointer",onClick:()=>s(e),children:[e.coverPhoto?(0,a.jsx)("img",{src:e.coverPhoto.imagePath,alt:e.coverPhoto.title,className:"w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"}):(0,a.jsxs)("div",{className:"w-full h-full flex flex-col items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100",children:[(0,a.jsx)("div",{className:"bg-diwan-100 rounded-full p-4 mb-3",children:(0,a.jsx)(h.A,{className:"w-8 h-8 text-diwan-600"})}),(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-1",children:"مجلد فارغ"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 text-center px-2",children:"folder"===e.type?"مجلد جديد":"لا توجد صور"}),0===e.photosCount&&(0,a.jsx)("div",{className:"mt-2 px-3 py-1 bg-yellow-100 text-yellow-700 rounded-full text-xs font-medium",children:"جديد"})]}),(0,a.jsx)("div",{className:"absolute top-2 right-2",children:(0,a.jsx)(o.E,{variant:"secondary",className:`text-xs ${"activity"===e.type?"bg-green-100 text-green-700":"folder"===e.type?"bg-purple-100 text-purple-700":"bg-blue-100 text-blue-700"}`,children:"activity"===e.type?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"w-3 h-3 ml-1"}),"نشاط"]}):"folder"===e.type?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(h.A,{className:"w-3 h-3 ml-1"}),"مجلد"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(A.A,{className:"w-3 h-3 ml-1"}),"عام"]})})}),(0,a.jsx)("div",{className:"absolute bottom-2 left-2",children:(0,a.jsxs)(o.E,{className:`text-xs ${0===e.photosCount?"bg-orange-500/80 text-white":"bg-black/70 text-white"}`,children:[(0,a.jsx)(x.A,{className:"w-3 h-3 ml-1"}),0===e.photosCount?"فارغ":`${e.photosCount} صورة`]})}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200 flex items-center justify-center",children:(0,a.jsx)(i.$,{variant:"secondary",size:"sm",className:"opacity-0 group-hover:opacity-100 transition-opacity duration-200",onClick:()=>s(e),children:0===e.photosCount?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.A,{className:"w-4 h-4 ml-2"}),"إضافة صور"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(v.A,{className:"w-4 h-4 ml-2"}),"عرض الصور"]})})})]}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900 truncate group-hover:text-diwan-700 transition-colors",children:e.title}),e.description&&(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1 line-clamp-2",children:e.description})]}),(0,a.jsx)(k.A,{className:"w-4 h-4 text-gray-400 group-hover:text-diwan-500 transition-colors flex-shrink-0 mr-2"})]}),(0,a.jsxs)("div",{className:"mt-3 flex items-center justify-between text-xs text-gray-500",children:[(0,a.jsx)("span",{children:1===e.photosCount?"صورة واحدة":2===e.photosCount?"صورتان":`${e.photosCount} صور`}),(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(h.A,{className:"w-3 h-3 ml-1"}),"مجلد"]})]})]})]})},e.id))})}function S({folders:e}){let s=e.reduce((e,s)=>e+s.photosCount,0),t=e.filter(e=>"activity"===e.type).length,r=e.filter(e=>"general"===e.type).length;return(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-4 gap-4 mb-6",children:[(0,a.jsx)(n.Zp,{children:(0,a.jsxs)(n.Wu,{className:"p-4 text-center",children:[(0,a.jsx)(h.A,{className:"w-8 h-8 text-blue-600 mx-auto mb-2"}),(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:e.length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"إجمالي المجلدات"})]})}),(0,a.jsx)(n.Zp,{children:(0,a.jsxs)(n.Wu,{className:"p-4 text-center",children:[(0,a.jsx)(x.A,{className:"w-8 h-8 text-green-600 mx-auto mb-2"}),(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:s}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"إجمالي الصور"})]})}),(0,a.jsx)(n.Zp,{children:(0,a.jsxs)(n.Wu,{className:"p-4 text-center",children:[(0,a.jsx)(p.A,{className:"w-8 h-8 text-purple-600 mx-auto mb-2"}),(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:t}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"مجلدات الأنشطة"})]})}),(0,a.jsx)(n.Zp,{children:(0,a.jsxs)(n.Wu,{className:"p-4 text-center",children:[(0,a.jsx)(A.A,{className:"w-8 h-8 text-orange-600 mx-auto mb-2"}),(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:r}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"المجلدات العامة"})]})})]})}var D=t(80013),$=t(34729),z=t(56085),P=t(97992);function E({open:e,onOpenChange:s,onSuccess:t}){let[l,n]=(0,r.useState)(""),[o,x]=(0,r.useState)(""),[u,g]=(0,r.useState)(""),[j,b]=(0,r.useState)(""),[v,f]=(0,r.useState)(""),[N,y]=(0,r.useState)(!1),[w,A]=(0,r.useState)(null),k=async e=>{if(e.preventDefault(),!l.trim())return void A("يرجى إدخال عنوان النشاط");if(!j)return void A("يرجى إدخال تاريخ بداية النشاط");y(!0),A(null);try{let e=await fetch("/api/gallery-folders",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:l.trim(),description:o.trim()||void 0,location:u.trim()||void 0,startDate:j||void 0,endDate:v||void 0})});if(!e.ok){let s=await e.json();throw Error(s.error||"فشل في إنشاء المجلد")}let a=await e.json();console.log("تم إنشاء المجلد بنجاح:",a),n(""),x(""),g(""),b(""),f(""),A(null),s(!1),setTimeout(()=>{t()},100)}catch(e){console.error("خطأ في إنشاء المجلد:",e),A(e.message||"حدث خطأ في إنشاء المجلد")}finally{y(!1)}};return(0,a.jsx)(c.lG,{open:e,onOpenChange:s,children:(0,a.jsxs)(c.Cf,{className:"max-w-[50vw] max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)(c.c7,{className:"text-center pb-6",children:[(0,a.jsx)("div",{className:"mx-auto w-16 h-16 bg-gradient-to-br from-diwan-500 to-diwan-600 rounded-full flex items-center justify-center mb-4",children:(0,a.jsx)(h.A,{className:"w-8 h-8 text-white"})}),(0,a.jsx)(c.L3,{className:"text-2xl font-bold text-diwan-600 mb-2",children:"إنشاء مجلد جديد"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"أنشئ مجلد جديد لتنظيم صور النشاط أو المناسبة"})]}),(0,a.jsxs)("form",{onSubmit:k,className:"space-y-8",children:[(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-gradient-to-r from-diwan-500 to-diwan-600 h-2 rounded-full transition-all duration-300",style:{width:`${l.trim()&&j?"100%":l.trim()||j?"50%":"25%"}%`}})}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3",children:(0,a.jsx)(z.A,{className:"w-4 h-4 text-white"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-blue-800",children:"معلومات المجلد"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)(D.J,{htmlFor:"title",className:"text-sm font-medium text-gray-700 flex items-center",children:[(0,a.jsx)(h.A,{className:"w-4 h-4 ml-2 text-diwan-600"}),"اسم المجلد *"]}),(0,a.jsx)(d.p,{id:"title",value:l,onChange:e=>n(e.target.value),placeholder:"مثال: حفل زفاف أحمد محمد",disabled:N,required:!0,className:"enhanced-border h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 bg-white"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)(D.J,{htmlFor:"location",className:"text-sm font-medium text-gray-700 flex items-center",children:[(0,a.jsx)(P.A,{className:"w-4 h-4 ml-2 text-green-600"}),"الموقع (اختياري)"]}),(0,a.jsx)(d.p,{id:"location",value:u,onChange:e=>g(e.target.value),placeholder:"مثال: قاعة الأفراح الكبرى",disabled:N,className:"enhanced-border h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 bg-white"})]})]}),(0,a.jsxs)("div",{className:"space-y-3 mt-6",children:[(0,a.jsx)(D.J,{htmlFor:"description",className:"text-sm font-medium text-gray-700",children:"وصف المجلد (اختياري)"}),(0,a.jsx)($.T,{id:"description",value:o,onChange:e=>x(e.target.value),placeholder:"أضف وصفاً مفصلاً للمجلد والمناسبة...",disabled:N,rows:4,className:"enhanced-border border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 bg-white resize-none"})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center mr-3",children:(0,a.jsx)(p.A,{className:"w-4 h-4 text-white"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-green-800",children:"تواريخ النشاط"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)(D.J,{htmlFor:"startDate",className:"text-sm font-medium text-gray-700 flex items-center",children:[(0,a.jsx)(p.A,{className:"w-4 h-4 ml-2 text-green-600"}),"تاريخ البداية *"]}),(0,a.jsx)(d.p,{id:"startDate",type:"date",value:j,onChange:e=>b(e.target.value),disabled:N,required:!0,className:"enhanced-border h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 bg-white"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)(D.J,{htmlFor:"endDate",className:"text-sm font-medium text-gray-700 flex items-center",children:[(0,a.jsx)(p.A,{className:"w-4 h-4 ml-2 text-orange-600"}),"تاريخ النهاية (اختياري)"]}),(0,a.jsx)(d.p,{id:"endDate",type:"date",value:v,onChange:e=>f(e.target.value),disabled:N,min:j,className:"enhanced-border h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 bg-white"})]})]}),(0,a.jsx)("div",{className:"mt-4 p-4 bg-gradient-to-r from-green-100 to-emerald-100 rounded-lg border border-green-200",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"w-5 h-5 text-green-600 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{className:"mr-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-green-800",children:"نصيحة مفيدة"}),(0,a.jsx)("p",{className:"text-sm text-green-700 mt-1",children:"إذا لم تحدد تاريخ النهاية، سيتم استخدام تاريخ البداية كتاريخ النهاية تلقائياً"})]})]})})]}),l.trim()&&(0,a.jsxs)("div",{className:"bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-6 border border-purple-100",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center mr-3",children:(0,a.jsx)(h.A,{className:"w-4 h-4 text-white"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-purple-800",children:"معاينة المجلد"})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg p-4 border border-purple-200",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-diwan-500 to-diwan-600 rounded-lg flex items-center justify-center mr-4",children:(0,a.jsx)(h.A,{className:"w-6 h-6 text-white"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"font-semibold text-gray-900",children:l}),o&&(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1 line-clamp-2",children:o}),(0,a.jsxs)("div",{className:"flex items-center mt-2 text-xs text-gray-500",children:[u&&(0,a.jsxs)("span",{className:"flex items-center ml-4",children:[(0,a.jsx)(P.A,{className:"w-3 h-3 ml-1"}),u]}),j&&(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(p.A,{className:"w-3 h-3 ml-1"}),new Date(j).toLocaleDateString("ar-JO")]})]})]}),(0,a.jsx)("div",{className:"text-right",children:(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"0 صورة"})})]})})]}),w&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-xl p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"w-5 h-5 text-red-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"mr-3",children:(0,a.jsx)("p",{className:"text-sm font-medium text-red-800",children:w})})]})}),(0,a.jsxs)(c.Es,{className:"gap-4 pt-6 border-t border-gray-100",children:[(0,a.jsx)(i.$,{type:"button",variant:"outline",onClick:()=>s(!1),disabled:N,className:"enhanced-button flex-1 h-14 border-2 border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 font-semibold text-base rounded-xl transition-all duration-200",children:"إلغاء"}),(0,a.jsx)(i.$,{type:"submit",disabled:N||!l.trim()||!j,className:"enhanced-button flex-1 h-14 bg-gradient-to-r from-purple-600 via-purple-700 to-indigo-700 hover:from-purple-700 hover:via-purple-800 hover:to-indigo-800 text-white font-bold text-base shadow-xl hover:shadow-2xl transition-all duration-300 rounded-xl border-0 transform hover:scale-105 active:scale-95",children:N?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-6 h-6 ml-2 border-3 border-white border-t-transparent rounded-full animate-spin"}),(0,a.jsx)("span",{className:"font-bold",children:"جاري الإنشاء..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"bg-white/20 rounded-full p-1 ml-2",children:(0,a.jsx)(m.A,{className:"w-5 h-5 text-white"})}),(0,a.jsx)("span",{className:"font-bold",children:"إنشاء المجلد"})]})})]})]})]})})}function q({open:e,onOpenChange:s,onSuccess:t,activity:l}){let[n,o]=(0,r.useState)(""),[x,h]=(0,r.useState)(""),[m,u]=(0,r.useState)(""),[g,j]=(0,r.useState)(""),[b,v]=(0,r.useState)(""),[N,y]=(0,r.useState)(!1),[w,A]=(0,r.useState)(null),k=async e=>{if(e.preventDefault(),l){if(!n.trim())return void A("يرجى إدخال عنوان النشاط");if(!g)return void A("يرجى إدخال تاريخ بداية النشاط");y(!0),A(null);try{let e=await fetch(`/api/activities/${l.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:n.trim(),description:x.trim()||void 0,location:m.trim()||void 0,startDate:new Date(g).toISOString(),endDate:b?new Date(b).toISOString():new Date(g).toISOString()})});if(!e.ok){let s=await e.json();throw Error(s.error||"فشل في تحديث النشاط")}t(),s(!1)}catch(e){console.error("خطأ في تحديث النشاط:",e),A(e.message||"حدث خطأ في تحديث النشاط")}finally{y(!1)}}};return l?(0,a.jsx)(c.lG,{open:e,onOpenChange:s,children:(0,a.jsxs)(c.Cf,{className:"max-w-md",children:[(0,a.jsx)(c.c7,{children:(0,a.jsxs)(c.L3,{className:"flex items-center",children:[(0,a.jsx)(f.A,{className:"w-5 h-5 ml-2"}),"تعديل النشاط"]})}),(0,a.jsxs)("form",{onSubmit:k,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(D.J,{htmlFor:"title",children:"عنوان النشاط *"}),(0,a.jsx)(d.p,{id:"title",value:n,onChange:e=>o(e.target.value),placeholder:"مثال: حفل زفاف أحمد محمد",disabled:N,required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(D.J,{htmlFor:"description",children:"وصف النشاط"}),(0,a.jsx)($.T,{id:"description",value:x,onChange:e=>h(e.target.value),placeholder:"وصف مختصر للنشاط (اختياري)",disabled:N,rows:3})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(D.J,{htmlFor:"location",children:"الموقع"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(P.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)(d.p,{id:"location",value:m,onChange:e=>u(e.target.value),placeholder:"موقع إقامة النشاط (اختياري)",disabled:N,className:"pr-10"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(D.J,{htmlFor:"startDate",children:"تاريخ البداية *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)(d.p,{id:"startDate",type:"date",value:g,onChange:e=>j(e.target.value),disabled:N,required:!0,className:"pr-10"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(D.J,{htmlFor:"endDate",children:"تاريخ النهاية"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)(d.p,{id:"endDate",type:"date",value:b,onChange:e=>v(e.target.value),disabled:N,min:g,className:"pr-10"})]})]}),w&&(0,a.jsx)("div",{className:"text-red-600 text-sm bg-red-50 p-3 rounded-md",children:w}),(0,a.jsxs)(c.Es,{children:[(0,a.jsx)(i.$,{type:"button",variant:"outline",onClick:()=>s(!1),disabled:N,children:"إلغاء"}),(0,a.jsx)(i.$,{type:"submit",disabled:N||!n.trim()||!g,className:"bg-diwan-600 hover:bg-diwan-700",children:N?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{className:"w-4 h-4 ml-2 animate-spin"}),"جاري التحديث..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{className:"w-4 h-4 ml-2"}),"حفظ التغييرات"]})})]})]})]})}):null}var F=t(16189);function O(){let{data:e}=(0,l.useSession)(),s=(0,F.useRouter)(),[t,A]=(0,r.useState)([]),[k,D]=(0,r.useState)([]),[$,z]=(0,r.useState)(!0),[P,O]=(0,r.useState)(""),[M,J]=(0,r.useState)("all"),[L,Z]=(0,r.useState)("folders"),[_,R]=(0,r.useState)(null),[I,W]=(0,r.useState)(!1),[T,G]=(0,r.useState)(!1),[U,B]=(0,r.useState)(!1),[H,V]=(0,r.useState)(!1),[K,X]=(0,r.useState)(null),Q=async()=>{try{z(!0);let e=await fetch("/api/gallery?groupBy=activity");if(!e.ok)throw Error("فشل في جلب المجلدات");let s=await e.json();D(s.folders||[])}catch(e){console.error("خطأ في جلب المجلدات:",e),D([])}finally{z(!1)}},Y=(0,r.useCallback)(async()=>{try{z(!0);let e=new URLSearchParams({search:P,category:M}),s=await fetch(`/api/gallery?${e}`);if(!s.ok)throw Error("فشل في جلب الصور");let t=await s.json();A(t.photos||[])}catch(e){console.error("خطأ في جلب الصور:",e),A([])}finally{z(!1)}},[P,M]),ee=e=>{R(e),W(!0)},es=async e=>{if(confirm("هل أنت متأكد من حذف هذه الصورة؟"))try{if(!(await fetch(`/api/gallery/${e}`,{method:"DELETE"})).ok)throw Error("فشل في حذف الصورة");Y()}catch(e){console.error("خطأ في حذف الصورة:",e),alert("حدث خطأ في حذف الصورة")}},et=async e=>{try{if("activity"===e.type){let s=await fetch(`/api/activities/${e.id}`);if(s.ok){let e=await s.json();X(e),V(!0)}}else if("folder"===e.type){let s=await fetch(`/api/gallery-folders/${e.id}`);if(s.ok){let e=await s.json();X(e),V(!0)}}}catch(e){console.error("خطأ في جلب بيانات المجلد:",e),alert("حدث خطأ في جلب بيانات المجلد")}},ea=async e=>{if("general"!==e.type&&confirm(e.photosCount>0?`هذا المجلد يحتوي على ${e.photosCount} صورة. لا يمكن حذفه إلا بعد حذف جميع الصور أو نقلها إلى مجلد آخر.`:`هل أنت متأكد من حذف المجلد "${e.title}"؟`))try{let s="activity"===e.type?`/api/activities/${e.id}`:`/api/gallery-folders/${e.id}`,t=await fetch(s,{method:"DELETE"});if(!t.ok){let e=await t.json();throw Error(e.error||"فشل في حذف المجلد")}Q()}catch(e){console.error("خطأ في حذف المجلد:",e),alert(e.message||"حدث خطأ في حذف المجلد")}},er=e?.user.role!=="VIEWER",el=e?.user.role==="ADMIN";return $?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"معرض الصور"}),(0,a.jsx)("p",{className:"text-gray-600",children:"عرض وإدارة صور الديوان والأنشطة"})]}),(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("div",{className:"text-gray-500",children:"جاري التحميل..."})})]}):(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsx)("div",{className:"text-center mb-8",children:(0,a.jsxs)("div",{className:"bg-gradient-to-r from-purple-600 to-pink-600 rounded-3xl shadow-2xl p-10 text-white relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-500 opacity-30 animate-pulse"}),(0,a.jsxs)("div",{className:"relative z-10",children:[(0,a.jsx)("div",{className:"inline-flex items-center justify-center w-20 h-20 rounded-full mb-6 bg-white bg-opacity-20 backdrop-blur-sm",children:(0,a.jsx)(x.A,{className:"w-10 h-10 text-white"})}),(0,a.jsx)("h1",{className:"text-5xl font-black mb-4 text-white",children:"معرض الصور"}),(0,a.jsx)("p",{className:"text-xl font-semibold mb-6 text-purple-100",children:"عرض وإدارة صور الديوان والأنشطة والمناسبات"}),(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 space-x-reverse",children:[(0,a.jsx)("div",{className:"h-1 w-16 rounded-full bg-white bg-opacity-60"}),(0,a.jsx)("div",{className:"h-1 w-8 rounded-full bg-white bg-opacity-40"}),(0,a.jsx)("div",{className:"h-1 w-16 rounded-full bg-white bg-opacity-60"})]})]})]})}),(0,a.jsx)("div",{className:"flex justify-center mb-8",children:(0,a.jsx)("div",{className:"bg-white rounded-2xl shadow-xl p-6 border border-gray-100",children:(0,a.jsxs)("div",{className:"flex flex-wrap gap-4 justify-center",children:[(0,a.jsxs)("div",{className:"flex bg-gray-100 rounded-xl p-1 border border-gray-200",children:[(0,a.jsxs)(i.$,{variant:"folders"===L?"default":"ghost",size:"sm",onClick:()=>Z("folders"),className:`text-sm transition-all px-4 py-2 rounded-lg ${"folders"===L?"bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-md":"text-gray-600 hover:bg-gray-200"}`,children:[(0,a.jsx)(h.A,{className:"w-4 h-4 ml-2"}),"المجلدات"]}),(0,a.jsxs)(i.$,{variant:"photos"===L?"default":"ghost",size:"sm",onClick:()=>Z("photos"),className:`text-sm transition-all px-4 py-2 rounded-lg ${"photos"===L?"bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-md":"text-gray-600 hover:bg-gray-200"}`,children:[(0,a.jsx)(x.A,{className:"w-4 h-4 ml-2"}),"جميع الصور"]})]}),er&&(0,a.jsxs)("div",{className:"flex gap-3",children:["folders"===L&&(0,a.jsxs)(i.$,{onClick:()=>B(!0),className:"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold",children:[(0,a.jsx)(m.A,{className:"w-5 h-5 ml-2"}),"إنشاء مجلد جديد"]}),(0,a.jsxs)(i.$,{onClick:()=>G(!0),className:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold",children:[(0,a.jsx)(u.A,{className:"w-5 h-5 ml-2"}),"رفع صور جديدة"]})]})]})})}),"folders"===L?(0,a.jsx)(S,{folders:k}):(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(n.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-500 to-blue-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-blue-600 p-1 rounded-t-xl"}),(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"إجمالي الصور"}),(0,a.jsx)("div",{className:"p-4 rounded-2xl shadow-lg",style:{backgroundColor:"#007bff"},children:(0,a.jsx)(x.A,{className:"h-7 w-7 text-white"})})]}),(0,a.jsxs)(n.Wu,{className:"relative z-10",children:[(0,a.jsx)("div",{className:"text-4xl font-black mb-2",style:{color:"#191970"},children:t.length}),(0,a.jsx)("p",{className:"text-sm font-semibold",style:{color:"#6c757d"},children:"صورة في المعرض"})]})]}),(0,a.jsxs)(n.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-green-500 to-green-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-green-500 to-green-600 p-1 rounded-t-xl"}),(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"صور الأنشطة"}),(0,a.jsx)("div",{className:"p-4 rounded-2xl shadow-lg",style:{backgroundColor:"#28a745"},children:(0,a.jsx)(p.A,{className:"h-7 w-7 text-white"})})]}),(0,a.jsxs)(n.Wu,{className:"relative z-10",children:[(0,a.jsx)("div",{className:"text-4xl font-black mb-2",style:{color:"#191970"},children:t.filter(e=>e.activityId).length}),(0,a.jsx)("p",{className:"text-sm font-semibold",style:{color:"#6c757d"},children:"صورة مرتبطة بالأنشطة"})]})]}),(0,a.jsxs)(n.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-purple-500 to-purple-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-purple-600 p-1 rounded-t-xl"}),(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"صور عامة"}),(0,a.jsx)("div",{className:"p-4 rounded-2xl shadow-lg",style:{backgroundColor:"#800020"},children:(0,a.jsx)(g.A,{className:"h-7 w-7 text-white"})})]}),(0,a.jsxs)(n.Wu,{className:"relative z-10",children:[(0,a.jsx)("div",{className:"text-4xl font-black mb-2",style:{color:"#191970"},children:t.filter(e=>!e.activityId).length}),(0,a.jsx)("p",{className:"text-sm font-semibold",style:{color:"#6c757d"},children:"صورة غير مصنفة"})]})]}),(0,a.jsxs)(n.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-orange-500 to-orange-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-orange-500 to-orange-600 p-1 rounded-t-xl"}),(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"المجلدات"}),(0,a.jsx)("div",{className:"p-4 rounded-2xl shadow-lg",style:{backgroundColor:"#fd7e14"},children:(0,a.jsx)(h.A,{className:"h-7 w-7 text-white"})})]}),(0,a.jsxs)(n.Wu,{className:"relative z-10",children:[(0,a.jsx)("div",{className:"text-4xl font-black mb-2",style:{color:"#191970"},children:k.length}),(0,a.jsx)("p",{className:"text-sm font-semibold",style:{color:"#6c757d"},children:"مجلد منظم"})]})]})]}),"photos"===L&&(0,a.jsx)(n.Zp,{className:"border-0 shadow-md",children:(0,a.jsxs)(n.Wu,{className:"pt-6",children:[(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(j.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,a.jsx)(d.p,{placeholder:"البحث في الصور بالعنوان أو الوصف...",value:P,onChange:e=>O(e.target.value),className:"search-input pr-12 h-11 border-gray-200 focus:border-diwan-500 focus:ring-diwan-500"})]}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)("select",{value:M,onChange:e=>J(e.target.value),className:"px-4 py-2 h-11 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-diwan-500 focus:border-diwan-500 bg-white min-w-[150px]",children:[{value:"all",label:"جميع الفئات"},{value:"activities",label:"الأنشطة"},{value:"members",label:"الأعضاء"},{value:"events",label:"المناسبات"},{value:"meetings",label:"الاجتماعات"},{value:"other",label:"أخرى"}].map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))}),(P||"all"!==M)&&(0,a.jsxs)(i.$,{variant:"outline",onClick:()=>{O(""),J("all")},className:"h-11 px-4 border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300 font-medium",children:[(0,a.jsx)(b.A,{className:"w-4 h-4 ml-2"}),"مسح الفلاتر"]})]})]}),P&&(0,a.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:[(0,a.jsx)("span",{className:"font-medium",children:t.length}),' نتيجة للبحث عن "',P,'"']})]})}),(0,a.jsx)(n.Zp,{className:"border-0 shadow-md",children:(0,a.jsx)(n.Wu,{className:"p-6",children:"folders"===L?(0,a.jsx)(C,{folders:k,onFolderClick:e=>{"folder"===e.type?s.push(`/dashboard/gallery/folder/${e.id}?type=folder`):"activity"===e.type?s.push(`/dashboard/gallery/folder/${e.id}?type=activity`):s.push("/dashboard/gallery/folder/general")},onEditFolder:et,onDeleteFolder:ea,canEdit:er,canDelete:el,loading:$}):(0,a.jsx)(a.Fragment,{children:0===t.length?(0,a.jsxs)("div",{className:"text-center py-16",children:[(0,a.jsx)("div",{className:"bg-gray-50 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6",children:(0,a.jsx)(x.A,{className:"w-12 h-12 text-gray-400"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"لا توجد صور"}),(0,a.jsx)("p",{className:"text-gray-500 mb-6",children:P?"لم يتم العثور على صور تطابق البحث":"ابدأ بإضافة صور إلى المعرض"}),er&&!P&&(0,a.jsxs)(i.$,{onClick:()=>G(!0),className:"bg-diwan-600 hover:bg-diwan-700 text-white font-semibold px-8 py-3 h-12 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200",children:[(0,a.jsx)(u.A,{className:"w-5 h-5 ml-2"}),"رفع أول صورة"]})]}):(0,a.jsx)("div",{className:"gallery-grid",children:t.map(e=>(0,a.jsxs)("div",{className:"gallery-card group relative bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden border border-gray-100",children:[(0,a.jsxs)("div",{className:"aspect-square overflow-hidden bg-gray-100 relative",children:[(0,a.jsx)("img",{src:e.imagePath,alt:e.title,className:"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500 cursor-pointer",onClick:()=>ee(e)}),(0,a.jsxs)("div",{className:"floating-buttons absolute top-3 left-3 flex gap-2",children:[(0,a.jsx)(i.$,{variant:"secondary",size:"sm",onClick:()=>ee(e),className:"h-9 w-9 p-0 bg-blue-600 hover:bg-blue-700 text-white shadow-lg border-0 rounded-full",title:"عرض الصورة",children:(0,a.jsx)(v.A,{className:"w-4 h-4"})}),er&&(0,a.jsx)(i.$,{variant:"secondary",size:"sm",className:"h-9 w-9 p-0 bg-green-600 hover:bg-green-700 text-white shadow-lg border-0 rounded-full",title:"تعديل الصورة",children:(0,a.jsx)(f.A,{className:"w-4 h-4"})}),(0,a.jsx)(i.$,{variant:"secondary",size:"sm",className:"h-9 w-9 p-0 bg-purple-600 hover:bg-purple-700 text-white shadow-lg border-0 rounded-full",title:"تحميل الصورة",onClick:()=>{let s=document.createElement("a");s.href=e.imagePath,s.download=e.title,s.click()},children:(0,a.jsx)(N.A,{className:"w-4 h-4"})}),el&&(0,a.jsx)(i.$,{variant:"secondary",size:"sm",onClick:()=>es(e.id),className:"h-9 w-9 p-0 bg-red-600 hover:bg-red-700 text-white shadow-lg border-0 rounded-full",title:"حذف الصورة",children:(0,a.jsx)(y.A,{className:"w-4 h-4"})})]}),(0,a.jsx)("div",{className:"absolute bottom-2 right-2",children:(0,a.jsx)(o.E,{variant:e.activityId?"default":"secondary",className:`text-xs ${e.activityId?"bg-diwan-600":"bg-gray-500"}`,children:e.activityId?"نشاط":"عام"})})]}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-gray-900 truncate mb-1",children:e.title}),e.description&&(0,a.jsx)("p",{className:"text-xs text-gray-500 line-clamp-2 mb-2",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-400",children:[(0,a.jsx)("span",{children:e.uploader.name}),(0,a.jsx)("span",{children:new Date(e.uploadedAt).toLocaleDateString("ar-JO")})]}),e.activity&&(0,a.jsx)("div",{className:"mt-2 text-xs text-diwan-600 bg-diwan-50 px-2 py-1 rounded truncate",children:e.activity.title})]})]},e.id))})})})}),(0,a.jsx)(c.lG,{open:I,onOpenChange:W,children:(0,a.jsx)(c.Cf,{className:"max-w-6xl max-h-[95vh] overflow-hidden p-0",children:_&&(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b bg-white",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:_.title}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["رفعت بواسطة ",_.uploader.name," في ",new Date(_.uploadedAt).toLocaleDateString("ar-JO")]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>{let e=document.createElement("a");e.href=_.imagePath,e.download=_.title,e.click()},className:"bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100 hover:border-blue-300 font-medium px-4 py-2 h-9",children:[(0,a.jsx)(N.A,{className:"w-4 h-4 ml-2"}),"تحميل"]}),er&&(0,a.jsxs)(i.$,{variant:"outline",size:"sm",className:"bg-green-50 border-green-200 text-green-700 hover:bg-green-100 hover:border-green-300 font-medium px-4 py-2 h-9",children:[(0,a.jsx)(f.A,{className:"w-4 h-4 ml-2"}),"تعديل"]}),el&&(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>{W(!1),es(_.id)},className:"bg-red-50 border-red-200 text-red-700 hover:bg-red-100 hover:border-red-300 font-medium px-4 py-2 h-9",children:[(0,a.jsx)(y.A,{className:"w-4 h-4 ml-2"}),"حذف"]})]})]}),(0,a.jsx)("div",{className:"flex-1 bg-gray-900 flex items-center justify-center p-4",children:(0,a.jsx)("img",{src:_.imagePath,alt:_.title,className:"max-w-full max-h-full object-contain rounded-lg shadow-2xl"})}),(0,a.jsx)("div",{className:"p-6 bg-gray-50 border-t",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2",children:[(0,a.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"الوصف"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed",children:_.description||"لا يوجد وصف لهذه الصورة"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-gray-900 mb-3",children:"التفاصيل"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-500",children:"الفئة:"}),(0,a.jsx)(o.E,{variant:_.activityId?"default":"secondary",className:"text-xs",children:_.activityId?"نشاط":"عام"})]}),_.activity&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-500",children:"النشاط:"}),(0,a.jsx)("span",{className:"text-diwan-600 font-medium",children:_.activity.title})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-500",children:"تاريخ الرفع:"}),(0,a.jsx)("span",{className:"text-gray-900",children:new Date(_.uploadedAt).toLocaleDateString("ar-JO")})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-500",children:"رفعت بواسطة:"}),(0,a.jsx)("span",{className:"text-gray-900",children:_.uploader.name})]})]})]})]})})]})})}),(0,a.jsx)(w.A,{open:T,onOpenChange:G,onSuccess:()=>{"folders"===L?Q():Y()}}),(0,a.jsx)(E,{open:U,onOpenChange:B,onSuccess:Q}),(0,a.jsx)(q,{open:H,onOpenChange:V,onSuccess:Q,activity:K})]})}},45386:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56085:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},74189:(e,s,t)=>{Promise.resolve().then(t.bind(t,77850))},77850:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\ابوعلوش\\\\diwan-abu-alosh\\\\src\\\\app\\\\dashboard\\\\gallery\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\gallery\\page.tsx","default")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83232:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>h,tree:()=>o});var a=t(65239),r=t(48088),l=t(88170),i=t.n(l),d=t(30893),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);t.d(s,n);let o={children:["",{children:["dashboard",{children:["gallery",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,77850)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\gallery\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,63144)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\gallery\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/gallery/page",pathname:"/dashboard/gallery",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96474:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},97992:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},98157:(e,s,t)=>{Promise.resolve().then(t.bind(t,42809))}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4243,5663,4999,3412,5442,7934,5498,1726,2131,5662,2635,5977,6154,3352],()=>t(83232));module.exports=a})();