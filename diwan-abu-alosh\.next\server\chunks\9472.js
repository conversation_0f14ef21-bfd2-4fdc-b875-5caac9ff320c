"use strict";exports.id=9472,exports.ids=[9472],exports.modules={23928:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},31158:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},46059:(e,t,n)=>{n.d(t,{C:()=>i});var r=n(43210),o=n(98599),a=n(66156),i=e=>{let{present:t,children:n}=e,i=function(e){var t,n;let[o,i]=r.useState(),s=r.useRef(null),l=r.useRef(e),d=r.useRef("none"),[c,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=u(s.current);d.current="mounted"===c?e:"none"},[c]),(0,a.N)(()=>{let t=s.current,n=l.current;if(n!==e){let r=d.current,o=u(t);e?f("MOUNT"):"none"===o||t?.display==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),l.current=e}},[e,f]),(0,a.N)(()=>{if(o){let e,t=o.ownerDocument.defaultView??window,n=n=>{let r=u(s.current).includes(n.animationName);if(n.target===o&&r&&(f("ANIMATION_END"),!l.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},r=e=>{e.target===o&&(d.current=u(s.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{s.current=e?getComputedStyle(e):null,i(e)},[])}}(t),s="function"==typeof n?n({present:i.isPresent}):r.Children.only(n),l=(0,o.s)(i.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s));return"function"==typeof n||i.isPresent?r.cloneElement(s,{ref:l}):null};function u(e){return e?.animationName||"none"}i.displayName="Presence"},55146:(e,t,n)=>{n.d(t,{B8:()=>E,UC:()=>C,bL:()=>D,l9:()=>F});var r=n(43210),o=n(70569),a=n(11273),i=n(72942),u=n(46059),s=n(14163),l=n(43),d=n(65551),c=n(96963),f=n(60687),m="Tabs",[p,v]=(0,a.A)(m,[i.RG]),b=(0,i.RG)(),[y,w]=p(m),g=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:o,defaultValue:a,orientation:i="horizontal",dir:u,activationMode:p="automatic",...v}=e,b=(0,l.jH)(u),[w,g]=(0,d.i)({prop:r,onChange:o,defaultProp:a??"",caller:m});return(0,f.jsx)(y,{scope:n,baseId:(0,c.B)(),value:w,onValueChange:g,orientation:i,dir:b,activationMode:p,children:(0,f.jsx)(s.sG.div,{dir:b,"data-orientation":i,...v,ref:t})})});g.displayName=m;var h="TabsList",N=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...o}=e,a=w(h,n),u=b(n);return(0,f.jsx)(i.bL,{asChild:!0,...u,orientation:a.orientation,dir:a.dir,loop:r,children:(0,f.jsx)(s.sG.div,{role:"tablist","aria-orientation":a.orientation,...o,ref:t})})});N.displayName=h;var A="TabsTrigger",T=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:a=!1,...u}=e,l=w(A,n),d=b(n),c=x(l.baseId,r),m=M(l.baseId,r),p=r===l.value;return(0,f.jsx)(i.q7,{asChild:!0,...d,focusable:!a,active:p,children:(0,f.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":m,"data-state":p?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:c,...u,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(r)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(r)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;p||a||!e||l.onValueChange(r)})})})});T.displayName=A;var R="TabsContent",I=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:o,forceMount:a,children:i,...l}=e,d=w(R,n),c=x(d.baseId,o),m=M(d.baseId,o),p=o===d.value,v=r.useRef(p);return r.useEffect(()=>{let e=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(u.C,{present:a||p,children:({present:n})=>(0,f.jsx)(s.sG.div,{"data-state":p?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":c,hidden:!n,id:m,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:v.current?"0s":void 0},children:n&&i})})});function x(e,t){return`${e}-trigger-${t}`}function M(e,t){return`${e}-content-${t}`}I.displayName=R;var D=g,E=N,F=T,C=I},72942:(e,t,n)=>{n.d(t,{RG:()=>N,bL:()=>F,q7:()=>C});var r=n(43210),o=n(70569),a=n(9510),i=n(98599),u=n(11273),s=n(96963),l=n(14163),d=n(13495),c=n(65551),f=n(43),m=n(60687),p="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},b="RovingFocusGroup",[y,w,g]=(0,a.N)(b),[h,N]=(0,u.A)(b,[g]),[A,T]=h(b),R=r.forwardRef((e,t)=>(0,m.jsx)(y.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(y.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(I,{...e,ref:t})})}));R.displayName=b;var I=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:a,loop:u=!1,dir:s,currentTabStopId:y,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:h,onEntryFocus:N,preventScrollOnEntryFocus:T=!1,...R}=e,I=r.useRef(null),x=(0,i.s)(t,I),M=(0,f.jH)(s),[D,F]=(0,c.i)({prop:y,defaultProp:g??null,onChange:h,caller:b}),[C,j]=r.useState(!1),O=(0,d.c)(N),L=w(n),U=r.useRef(!1),[G,S]=r.useState(0);return r.useEffect(()=>{let e=I.current;if(e)return e.addEventListener(p,O),()=>e.removeEventListener(p,O)},[O]),(0,m.jsx)(A,{scope:n,orientation:a,dir:M,loop:u,currentTabStopId:D,onItemFocus:r.useCallback(e=>F(e),[F]),onItemShiftTab:r.useCallback(()=>j(!0),[]),onFocusableItemAdd:r.useCallback(()=>S(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>S(e=>e-1),[]),children:(0,m.jsx)(l.sG.div,{tabIndex:C||0===G?-1:0,"data-orientation":a,...R,ref:x,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{U.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!U.current;if(e.target===e.currentTarget&&t&&!C){let t=new CustomEvent(p,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=L().filter(e=>e.focusable);E([e.find(e=>e.active),e.find(e=>e.id===D),...e].filter(Boolean).map(e=>e.ref.current),T)}}U.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>j(!1))})})}),x="RovingFocusGroupItem",M=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:a=!0,active:i=!1,tabStopId:u,children:d,...c}=e,f=(0,s.B)(),p=u||f,v=T(x,n),b=v.currentTabStopId===p,g=w(n),{onFocusableItemAdd:h,onFocusableItemRemove:N,currentTabStopId:A}=v;return r.useEffect(()=>{if(a)return h(),()=>N()},[a,h,N]),(0,m.jsx)(y.ItemSlot,{scope:n,id:p,focusable:a,active:i,children:(0,m.jsx)(l.sG.span,{tabIndex:b?0:-1,"data-orientation":v.orientation,...c,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?v.onItemFocus(p):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>v.onItemFocus(p)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void v.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return D[o]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=g().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=v.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>E(n))}}),children:"function"==typeof d?d({isCurrentTabStop:b,hasTabStop:null!=A}):d})})});M.displayName=x;var D={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function E(e,t=!1){let n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var F=R,C=M}};