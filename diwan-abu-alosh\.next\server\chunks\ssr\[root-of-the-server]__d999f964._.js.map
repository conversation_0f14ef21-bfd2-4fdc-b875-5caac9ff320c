{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { cn } from '@/lib/utils'\nimport { useAppearanceSettings } from '@/hooks/use-appearance-settings'\nimport {\n  Building2,\n  Home,\n  Users,\n  TrendingUp,\n  TrendingDown,\n  Image,\n  FileText,\n  Settings,\n  Bell,\n  // BarChart3,\n} from 'lucide-react'\n\nconst navigation = [\n  { name: 'لوحة التحكم', href: '/dashboard', icon: Home },\n  { name: 'الأعضاء', href: '/dashboard/members', icon: Users },\n  { name: 'الإيرادات', href: '/dashboard/incomes', icon: TrendingUp },\n  { name: 'المصروفات', href: '/dashboard/expenses', icon: TrendingDown },\n  { name: 'معرض الصور', href: '/dashboard/gallery', icon: Image },\n  { name: 'الإشعارات', href: '/dashboard/notifications', icon: Bell },\n  { name: 'التقارير', href: '/dashboard/reports', icon: FileText },\n  { name: 'التقارير المتقدمة', href: '/dashboard/reports-advanced', icon: FileText },\n  { name: 'الإعدادات', href: '/dashboard/settings', icon: Settings },\n]\n\nexport default function Sidebar() {\n  const pathname = usePathname()\n  const { settings } = useAppearanceSettings()\n\n  return (\n    <div className=\"fixed inset-y-0 right-0 z-50 w-64 shadow-2xl lg:block hidden sidebar bg-gradient-to-b from-slate-900 to-slate-800\">\n      {/* رأس الشريط الجانبي */}\n      <div className=\"flex h-20 items-center justify-center border-b border-slate-700 bg-gradient-to-r from-slate-800 to-slate-900\">\n        <div className=\"flex items-center space-x-3 space-x-reverse\">\n          {settings.logo ? (\n            <div className=\"p-2 rounded-xl bg-white bg-opacity-10 backdrop-blur-sm\">\n              <img \n                src={settings.logo} \n                alt=\"الشعار\" \n                className=\"h-10 w-10 object-contain\"\n              />\n            </div>\n          ) : (\n            <div className=\"p-2 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg\">\n              <Building2 className=\"h-10 w-10 text-white\" />\n            </div>\n          )}\n          <div className=\"text-right\">\n            <h1 className=\"text-lg font-bold text-white leading-tight\">{settings.brandName || 'ديوان آل أبو علوش'}</h1>\n            <p className=\"text-xs text-slate-300 font-medium\">نظام الإدارة</p>\n          </div>\n        </div>\n      </div>\n      \n      {/* القائمة الرئيسية */}\n      <nav className=\"mt-8 px-4\">\n        <ul className=\"space-y-2\">\n          {navigation.map((item) => {\n            const isActive = pathname === item.href\n            return (\n              <li key={item.name}>\n                <Link\n                  href={item.href}\n                  className={cn(\n                    'group flex items-center rounded-xl px-4 py-3 text-sm font-semibold transition-all duration-300 relative overflow-hidden',\n                    isActive\n                      ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg transform scale-105'\n                      : 'text-slate-300 hover:bg-slate-700 hover:text-white hover:transform hover:scale-105'\n                  )}\n                >\n                  {/* خلفية متحركة للعنصر النشط */}\n                  {isActive && (\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-blue-400 to-blue-500 opacity-20 animate-pulse\"></div>\n                  )}\n                  \n                  <item.icon\n                    className={cn(\n                      'ml-3 h-5 w-5 flex-shrink-0 transition-all duration-300',\n                      isActive ? 'text-white' : 'text-slate-400 group-hover:text-white'\n                    )}\n                  />\n                  <span className=\"relative z-10\">{item.name}</span>\n                  \n                  {/* مؤشر العنصر النشط */}\n                  {isActive && (\n                    <div className=\"absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-white rounded-r-full\"></div>\n                  )}\n                </Link>\n              </li>\n            )\n          })}\n        </ul>\n      </nav>\n      \n      {/* تذييل الشريط الجانبي */}\n      <div className=\"absolute bottom-0 w-full p-6 border-t border-slate-700 bg-gradient-to-r from-slate-800 to-slate-900\">\n        <div className=\"text-center\">\n          <div className=\"inline-flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 mb-3\">\n            <span className=\"text-white font-bold text-sm\">1.0</span>\n          </div>\n          <p className=\"text-xs text-slate-400 font-medium\">الإصدار 1.0.0</p>\n          <p className=\"text-xs text-slate-500 mt-1\">© 2024 ديوان آل أبو علوش</p>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAe,MAAM;QAAc,MAAM,mMAAA,CAAA,OAAI;IAAC;IACtD;QAAE,MAAM;QAAW,MAAM;QAAsB,MAAM,oMAAA,CAAA,QAAK;IAAC;IAC3D;QAAE,MAAM;QAAa,MAAM;QAAsB,MAAM,kNAAA,CAAA,aAAU;IAAC;IAClE;QAAE,MAAM;QAAa,MAAM;QAAuB,MAAM,sNAAA,CAAA,eAAY;IAAC;IACrE;QAAE,MAAM;QAAc,MAAM;QAAsB,MAAM,oMAAA,CAAA,QAAK;IAAC;IAC9D;QAAE,MAAM;QAAa,MAAM;QAA4B,MAAM,kMAAA,CAAA,OAAI;IAAC;IAClE;QAAE,MAAM;QAAY,MAAM;QAAsB,MAAM,8MAAA,CAAA,WAAQ;IAAC;IAC/D;QAAE,MAAM;QAAqB,MAAM;QAA+B,MAAM,8MAAA,CAAA,WAAQ;IAAC;IACjF;QAAE,MAAM;QAAa,MAAM;QAAuB,MAAM,0MAAA,CAAA,WAAQ;IAAC;CAClE;AAEc,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,wBAAqB,AAAD;IAEzC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,SAAS,IAAI,iBACZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,KAAK,SAAS,IAAI;gCAClB,KAAI;gCACJ,WAAU;;;;;;;;;;iDAId,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;sCAGzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA8C,SAAS,SAAS,IAAI;;;;;;8CAClF,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;;;;;;;;;;;;;0BAMxD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BACX,WAAW,GAAG,CAAC,CAAC;wBACf,MAAM,WAAW,aAAa,KAAK,IAAI;wBACvC,qBACE,8OAAC;sCACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2HACA,WACI,wFACA;;oCAIL,0BACC,8OAAC;wCAAI,WAAU;;;;;;kDAGjB,8OAAC,KAAK,IAAI;wCACR,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA,WAAW,eAAe;;;;;;kDAG9B,8OAAC;wCAAK,WAAU;kDAAiB,KAAK,IAAI;;;;;;oCAGzC,0BACC,8OAAC;wCAAI,WAAU;;;;;;;;;;;;2BAzBZ,KAAK,IAAI;;;;;oBA8BtB;;;;;;;;;;;0BAKJ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAA+B;;;;;;;;;;;sCAEjD,8OAAC;4BAAE,WAAU;sCAAqC;;;;;;sCAClD,8OAAC;4BAAE,WAAU;sCAA8B;;;;;;;;;;;;;;;;;;;;;;;AAKrD", "debugId": null}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { signOut } from 'next-auth/react'\nimport { Button } from '@/components/ui/button'\nimport { getRoleText } from '@/lib/utils'\nimport { LogOut, User, Home, Loader2 } from 'lucide-react'\n\ninterface HeaderProps {\n  user: {\n    name: string\n    email: string\n    role: string\n  }\n}\n\nexport default function Header({ user }: HeaderProps) {\n  const [isLoggingOut, setIsLoggingOut] = useState(false)\n\n  const handleSignOut = async () => {\n    setIsLoggingOut(true)\n\n    // تسجيل خروج فوري بدون انتظار\n    signOut({ callbackUrl: '/auth/signin', redirect: true })\n\n    // في حالة عدم نجاح التوجيه التلقائي، نوجه يدوياً\n    setTimeout(() => {\n      window.location.href = '/auth/signin'\n    }, 1000)\n  }\n\n  return (\n    <header className=\"bg-white shadow-lg border-b-2 border-gray-100\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-20 justify-between items-center\">\n          {/* الترحيب */}\n          <div className=\"flex items-center\">\n            <div className=\"bg-gradient-to-r from-blue-500 to-purple-600 p-3 rounded-2xl shadow-lg mr-4\">\n              <Home className=\"w-6 h-6 text-white\" />\n            </div>\n            <div>\n              <h2 className=\"text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n                مرحباً، {user.name}\n              </h2>\n              <p className=\"text-sm font-medium\" style={{ color: '#6c757d' }}>\n                أهلاً وسهلاً بك في نظام الإدارة\n              </p>\n            </div>\n          </div>\n\n          {/* معلومات المستخدم وأزرار التحكم */}\n          <div className=\"flex items-center space-x-6 space-x-reverse\">\n            {/* معلومات المستخدم */}\n            <div className=\"text-right bg-gradient-to-r from-gray-50 to-blue-50 p-4 rounded-2xl border border-gray-100\">\n              <p className=\"text-base font-bold\" style={{ color: '#333333' }}>{user.name}</p>\n              <p className=\"text-sm font-medium\" style={{ color: '#6c757d' }}>{getRoleText(user.role)}</p>\n            </div>\n\n            {/* أيقونة المستخدم وزر الخروج */}\n            <div className=\"flex items-center space-x-3 space-x-reverse\">\n              <div className=\"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg\">\n                <User className=\"w-6 h-6 text-white\" />\n              </div>\n\n              <Button\n                variant=\"ghost\"\n                size=\"lg\"\n                onClick={handleSignOut}\n                disabled={isLoggingOut}\n                className=\"text-gray-600 hover:text-white hover:bg-gradient-to-r hover:from-red-500 hover:to-red-600 transition-all duration-300 rounded-xl px-4 py-2 font-semibold disabled:opacity-50\"\n              >\n                {isLoggingOut ? (\n                  <>\n                    <Loader2 className=\"w-5 h-5 ml-2 animate-spin\" />\n                    جاري تسجيل الخروج...\n                  </>\n                ) : (\n                  <>\n                    <LogOut className=\"w-5 h-5 ml-2\" />\n                    تسجيل الخروج\n                  </>\n                )}\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AANA;;;;;;;AAgBe,SAAS,OAAO,EAAE,IAAI,EAAe;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,gBAAgB;QACpB,gBAAgB;QAEhB,8BAA8B;QAC9B,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;YAAE,aAAa;YAAgB,UAAU;QAAK;QAEtD,iDAAiD;QACjD,WAAW;YACT,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB,GAAG;IACL;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,mMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;4CAA+F;4CAClG,KAAK,IAAI;;;;;;;kDAEpB,8OAAC;wCAAE,WAAU;wCAAsB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;;;;;;;;;;;;;kCAOpE,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;wCAAsB,OAAO;4CAAE,OAAO;wCAAU;kDAAI,KAAK,IAAI;;;;;;kDAC1E,8OAAC;wCAAE,WAAU;wCAAsB,OAAO;4CAAE,OAAO;wCAAU;kDAAI,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,IAAI;;;;;;;;;;;;0CAIxF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAGlB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,6BACC;;8DACE,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAA8B;;yEAInD;;8DACE,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWvD", "debugId": null}}]}