(()=>{var e={};e.id=2701,e.ids=[2701],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>n});var s=r(13581),i=r(85663),a=r(31183);let n={providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"البريد الإلكتروني",type:"email"},password:{label:"كلمة المرور",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let t=await a.z.user.findUnique({where:{email:e.email}});return t&&await i.Ay.compare(e.password,t.password)?{id:t.id,email:t.email,name:t.name,role:t.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>i});var s=r(96330);let i=globalThis.prisma??new s.PrismaClient},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},49494:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>j,routeModule:()=>y,serverHooks:()=>f,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{DELETE:()=>v,GET:()=>x,PUT:()=>w});var i=r(96559),a=r(48088),n=r(37719),o=r(32190),u=r(19854),p=r(12909),c=r(31183),l=r(45697);let d=l.z.object({title:l.z.string().min(1,"عنوان النشاط مطلوب").optional(),description:l.z.string().optional(),location:l.z.string().optional(),startDate:l.z.string().optional(),endDate:l.z.string().optional()});async function x(e,{params:t}){try{if(!await (0,u.getServerSession)(p.N))return o.NextResponse.json({error:"غير مصرح"},{status:401});let{id:e}=await t,r=await c.z.activity.findUnique({where:{id:e},include:{createdBy:{select:{name:!0}},_count:{select:{participants:!0,photos:!0}}}});if(!r)return o.NextResponse.json({error:"النشاط غير موجود"},{status:404});return o.NextResponse.json(r)}catch(e){return console.error("خطأ في جلب النشاط:",e),o.NextResponse.json({error:"حدث خطأ في جلب النشاط"},{status:500})}}async function w(e,{params:t}){try{let r=await (0,u.getServerSession)(p.N);if(!r)return o.NextResponse.json({error:"غير مصرح"},{status:401});let{id:s}=await t,i=await e.json(),a=await c.z.activity.findUnique({where:{id:s}});if(!a)return o.NextResponse.json({error:"النشاط غير موجود"},{status:404});if(a.createdById!==r.user.id&&"ADMIN"!==r.user.role)return o.NextResponse.json({error:"غير مصرح لك بتعديل هذا النشاط"},{status:403});let n=d.parse(i),l={};if(n.title&&(l.title=n.title),void 0!==n.description&&(l.description=n.description),void 0!==n.location&&(l.location=n.location),n.startDate&&(l.startDate=new Date(n.startDate)),n.endDate?l.endDate=new Date(n.endDate):n.startDate&&(l.endDate=new Date(n.startDate)),l.startDate&&l.endDate&&l.endDate<l.startDate)return o.NextResponse.json({error:"تاريخ النهاية يجب أن يكون بعد تاريخ البداية"},{status:400});let x=await c.z.activity.update({where:{id:s},data:l,include:{createdBy:{select:{name:!0}},_count:{select:{participants:!0,photos:!0}}}});return o.NextResponse.json(x)}catch(e){if(e instanceof l.z.ZodError)return o.NextResponse.json({error:"بيانات غير صحيحة",details:e.errors},{status:400});return console.error("خطأ في تحديث النشاط:",e),o.NextResponse.json({error:"حدث خطأ في تحديث النشاط"},{status:500})}}async function v(e,{params:t}){try{let e=await (0,u.getServerSession)(p.N);if(!e)return o.NextResponse.json({error:"غير مصرح"},{status:401});let{id:r}=await t,s=await c.z.activity.findUnique({where:{id:r},include:{_count:{select:{photos:!0,participants:!0}}}});if(!s)return o.NextResponse.json({error:"النشاط غير موجود"},{status:404});if(s.createdById!==e.user.id&&"ADMIN"!==e.user.role)return o.NextResponse.json({error:"غير مصرح لك بحذف هذا النشاط"},{status:403});if(s._count.photos>0)return o.NextResponse.json({error:`لا يمكن حذف النشاط لأنه يحتوي على ${s._count.photos} صورة. يرجى حذف الصور أولاً أو نقلها إلى نشاط آخر.`},{status:400});return await c.z.activity.delete({where:{id:r}}),o.NextResponse.json({success:!0,message:"تم حذف النشاط بنجاح"})}catch(e){return console.error("خطأ في حذف النشاط:",e),o.NextResponse.json({error:"حدث خطأ في حذف النشاط"},{status:500})}}let y=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/activities/[id]/route",pathname:"/api/activities/[id]",filename:"route",bundlePath:"app/api/activities/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\activities\\[id]\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:m,serverHooks:f}=y;function j(){return(0,n.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:m})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,5663,4999,3412,580,5697],()=>r(49494));module.exports=s})();