{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/styles/gallery.css"], "sourcesContent": ["/* تحسينات خاصة بمعرض الصور */\n\n/* تأثيرات الحركة للصور */\n.gallery-image {\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.gallery-image:hover {\n  transform: scale(1.02);\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n}\n\n/* تأثير التحميل */\n.loading-shimmer {\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: shimmer 1.5s infinite;\n}\n\n@keyframes shimmer {\n  0% {\n    background-position: -200% 0;\n  }\n  100% {\n    background-position: 200% 0;\n  }\n}\n\n/* تحسين عرض النص */\n.line-clamp-2 {\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.line-clamp-3 {\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n/* تأثيرات الأزرار المحسنة */\n.gallery-button {\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n  overflow: hidden;\n}\n\n.gallery-button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);\n}\n\n.gallery-button::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n  transition: left 0.5s;\n}\n\n.gallery-button:hover::before {\n  left: 100%;\n}\n\n/* تحسين منطقة السحب والإفلات */\n.drop-zone {\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.drop-zone::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.1) 50%, transparent 70%);\n  transform: translateX(-100%);\n  transition: transform 0.6s ease;\n}\n\n.drop-zone:hover::before {\n  transform: translateX(100%);\n}\n\n/* تحسين البطاقات */\n.gallery-card {\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n  overflow: hidden;\n}\n\n.gallery-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.gallery-card:hover::before {\n  opacity: 1;\n}\n\n.gallery-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);\n}\n\n/* تحسين الشبكة المتجاوبة */\n.gallery-grid {\n  display: grid;\n  gap: 1.5rem;\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n}\n\n@media (max-width: 640px) {\n  .gallery-grid {\n    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n    gap: 1rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .gallery-grid {\n    grid-template-columns: 1fr;\n    gap: 0.75rem;\n  }\n}\n\n/* تحسين عارض الصور */\n.image-viewer {\n  -webkit-backdrop-filter: blur(10px);\n          backdrop-filter: blur(10px);\n  background: rgba(0, 0, 0, 0.8);\n}\n\n.image-viewer img {\n  max-height: 80vh;\n  max-width: 90vw;\n  -o-object-fit: contain;\n     object-fit: contain;\n  border-radius: 8px;\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);\n}\n\n/* تحسين الإحصائيات */\n.stats-card {\n  background: linear-gradient(135deg, var(--gradient-from) 0%, var(--gradient-to) 100%);\n  border: none;\n  position: relative;\n  overflow: hidden;\n}\n\n.stats-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.stats-card:hover::before {\n  opacity: 1;\n}\n\n/* تحسين الرأس المتدرج */\n.gradient-header {\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%);\n  position: relative;\n  overflow: hidden;\n}\n\n.gradient-header::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);\n  transform: translateX(-100%);\n  animation: shine 3s infinite;\n}\n\n@keyframes shine {\n  0% {\n    transform: translateX(-100%);\n  }\n  50% {\n    transform: translateX(100%);\n  }\n  100% {\n    transform: translateX(100%);\n  }\n}\n\n/* تحسين الأزرار العائمة */\n.floating-buttons {\n  opacity: 0;\n  transform: translateY(15px) scale(0.8);\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.gallery-card:hover .floating-buttons {\n  opacity: 1;\n  transform: translateY(0) scale(1);\n}\n\n/* تحسين أزرار التحكم */\n.floating-buttons button {\n  -webkit-backdrop-filter: blur(10px);\n          backdrop-filter: blur(10px);\n  transition: all 0.3s ease;\n  border: 2px solid rgba(255, 255, 255, 0.2);\n}\n\n.floating-buttons button:hover {\n  transform: scale(1.1);\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);\n}\n\n/* تأثيرات خاصة للأزرار الملونة */\n.floating-buttons .bg-blue-600:hover {\n  box-shadow: 0 8px 20px rgba(37, 99, 235, 0.4);\n}\n\n.floating-buttons .bg-green-600:hover {\n  box-shadow: 0 8px 20px rgba(22, 163, 74, 0.4);\n}\n\n.floating-buttons .bg-purple-600:hover {\n  box-shadow: 0 8px 20px rgba(147, 51, 234, 0.4);\n}\n\n.floating-buttons .bg-red-600:hover {\n  box-shadow: 0 8px 20px rgba(220, 38, 38, 0.4);\n}\n\n/* تحسين البحث */\n.search-input {\n  transition: all 0.3s ease;\n}\n\n.search-input:focus {\n  transform: scale(1.02);\n  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.2);\n}\n\n/* تحسين التحميل */\n.upload-progress {\n  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 50%, #3b82f6 100%);\n  background-size: 200% 100%;\n  animation: progress-shine 2s infinite;\n}\n\n@keyframes progress-shine {\n  0% {\n    background-position: -200% 0;\n  }\n  100% {\n    background-position: 200% 0;\n  }\n}\n\n/* تحسين الشارات */\n.badge-glow {\n  box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);\n  animation: glow 2s infinite alternate;\n}\n\n@keyframes glow {\n  from {\n    box-shadow: 0 0 5px rgba(59, 130, 246, 0.2);\n  }\n  to {\n    box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);\n  }\n}\n\n/* تحسين وضوح النصوص والأزرار */\n.enhanced-button {\n  font-weight: 600;\n  letter-spacing: 0.025em;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n  border: 2px solid transparent;\n  background-clip: padding-box;\n}\n\n.enhanced-button:hover {\n  letter-spacing: 0.05em;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n}\n\n/* تحسين التباين للنصوص */\n.high-contrast-text {\n  color: #1f2937;\n  font-weight: 600;\n  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);\n}\n\n.high-contrast-text-white {\n  color: #ffffff;\n  font-weight: 600;\n  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);\n}\n\n/* تحسين الحدود والظلال */\n.enhanced-border {\n  border: 2px solid rgba(0, 0, 0, 0.1);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.enhanced-border:hover {\n  border-color: rgba(0, 0, 0, 0.2);\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n}\n\n/* تحسين الأيقونات */\n.enhanced-icon {\n  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));\n}\n\n.enhanced-icon:hover {\n  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));\n  transform: scale(1.05);\n}\n"], "names": [], "mappings": "AAGA;;;;AAIA;;;;;AAMA;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;;AAQA;;;;;;AAMA;;;;;AAKA;;;;;;;;;;;AAWA;;;;AAKA;;;;;;AAMA;;;;;;;;;AAYA;;;;AAKA;;;;;;AAMA;;;;;;;;;AAYA;;;;AAIA;;;;;AAMA;;;;;;AAMA;EACE;;;;;;AAMF;EACE;;;;;;AAOF;;;;;AAMA;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;;;;AAYA;;;;AAKA;;;;;;AAMA;;;;;;;;;AAYA;;;;;;;;;;;;;;AAaA;;;;;;AAMA;;;;;AAMA;;;;;;AAOA;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;AAIA;;;;;AAMA;;;;;AAMA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;;AAMA;;;;;;AAMA;;;;;;AAOA;;;;;AAKA;;;;;AAMA;;;;AAIA", "debugId": null}}]}