"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8130],{47863:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},54416:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},66474:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},69074:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},78893:(e,t,r)=>{r.d(t,{UC:()=>eL,YJ:()=>eH,In:()=>eM,q7:()=>eB,VF:()=>eG,p4:()=>eV,JU:()=>e_,ZL:()=>eD,bL:()=>eI,wn:()=>eF,PP:()=>eO,wv:()=>eK,l9:()=>eP,WT:()=>eE,LM:()=>eA});var n=r(12115),l=r(47650);function o(e,[t,r]){return Math.min(r,Math.max(t,e))}var a=r(85185),i=r(37328),s=r(6101),u=r(46081),d=r(94315),c=r(19178),p=r(92293),f=r(25519),v=r(61285),h=r(38795),m=r(34378),w=r(63655),g=r(99708),x=r(39033),y=r(5845),b=r(52712),S=r(95155),C=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});n.forwardRef((e,t)=>(0,S.jsx)(w.sG.span,{...e,ref:t,style:{...C,...e.style}})).displayName="VisuallyHidden";var k=r(38168),j=r(93795),T=[" ","Enter","ArrowUp","ArrowDown"],N=[" ","Enter"],R="Select",[I,P,E]=(0,i.N)(R),[M,D]=(0,u.A)(R,[E,h.Bk]),L=(0,h.Bk)(),[A,H]=M(R),[_,B]=M(R),V=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:o,onOpenChange:a,value:i,defaultValue:s,onValueChange:u,dir:c,name:p,autoComplete:f,disabled:m,required:w,form:g}=e,x=L(t),[b,C]=n.useState(null),[k,j]=n.useState(null),[T,N]=n.useState(!1),P=(0,d.jH)(c),[E,M]=(0,y.i)({prop:l,defaultProp:null!=o&&o,onChange:a,caller:R}),[D,H]=(0,y.i)({prop:i,defaultProp:s,onChange:u,caller:R}),B=n.useRef(null),V=!b||g||!!b.closest("form"),[G,O]=n.useState(new Set),F=Array.from(G).map(e=>e.props.value).join(";");return(0,S.jsx)(h.bL,{...x,children:(0,S.jsxs)(A,{required:w,scope:t,trigger:b,onTriggerChange:C,valueNode:k,onValueNodeChange:j,valueNodeHasChildren:T,onValueNodeHasChildrenChange:N,contentId:(0,v.B)(),value:D,onValueChange:H,open:E,onOpenChange:M,dir:P,triggerPointerDownPosRef:B,disabled:m,children:[(0,S.jsx)(I.Provider,{scope:t,children:(0,S.jsx)(_,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{O(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{O(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),V?(0,S.jsxs)(ej,{"aria-hidden":!0,required:w,tabIndex:-1,name:p,autoComplete:f,value:D,onChange:e=>H(e.target.value),disabled:m,form:g,children:[void 0===D?(0,S.jsx)("option",{value:""}):null,Array.from(G)]},F):null]})})};V.displayName=R;var G="SelectTrigger",O=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...o}=e,i=L(r),u=H(G,r),d=u.disabled||l,c=(0,s.s)(t,u.onTriggerChange),p=P(r),f=n.useRef("touch"),[v,m,g]=eN(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===u.value),n=eR(t,e,r);void 0!==n&&u.onValueChange(n.value)}),x=e=>{d||(u.onOpenChange(!0),g()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,S.jsx)(h.Mz,{asChild:!0,...i,children:(0,S.jsx)(w.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":eT(u.value)?"":void 0,...o,ref:c,onClick:(0,a.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&x(e)}),onPointerDown:(0,a.m)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(x(e),e.preventDefault())}),onKeyDown:(0,a.m)(o.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&T.includes(e.key)&&(x(),e.preventDefault())})})})});O.displayName=G;var F="SelectValue",K=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:a="",...i}=e,u=H(F,r),{onValueNodeHasChildrenChange:d}=u,c=void 0!==o,p=(0,s.s)(t,u.onValueNodeChange);return(0,b.N)(()=>{d(c)},[d,c]),(0,S.jsx)(w.sG.span,{...i,ref:p,style:{pointerEvents:"none"},children:eT(u.value)?(0,S.jsx)(S.Fragment,{children:a}):o})});K.displayName=F;var W=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,S.jsx)(w.sG.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});W.displayName="SelectIcon";var U=e=>(0,S.jsx)(m.Z,{asChild:!0,...e});U.displayName="SelectPortal";var z="SelectContent",q=n.forwardRef((e,t)=>{let r=H(z,e.__scopeSelect),[o,a]=n.useState();return((0,b.N)(()=>{a(new DocumentFragment)},[]),r.open)?(0,S.jsx)(X,{...e,ref:t}):o?l.createPortal((0,S.jsx)(Y,{scope:e.__scopeSelect,children:(0,S.jsx)(I.Slot,{scope:e.__scopeSelect,children:(0,S.jsx)("div",{children:e.children})})}),o):null});q.displayName=z;var[Y,Z]=M(z),J=(0,g.TL)("SelectContent.RemoveScroll"),X=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:u,side:d,sideOffset:v,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:x,sticky:y,hideWhenDetached:b,avoidCollisions:C,...T}=e,N=H(z,r),[R,I]=n.useState(null),[E,M]=n.useState(null),D=(0,s.s)(t,e=>I(e)),[L,A]=n.useState(null),[_,B]=n.useState(null),V=P(r),[G,O]=n.useState(!1),F=n.useRef(!1);n.useEffect(()=>{if(R)return(0,k.Eq)(R)},[R]),(0,p.Oh)();let K=n.useCallback(e=>{let[t,...r]=V().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&E&&(E.scrollTop=0),r===n&&E&&(E.scrollTop=E.scrollHeight),null==r||r.focus(),document.activeElement!==l))return},[V,E]),W=n.useCallback(()=>K([L,R]),[K,L,R]);n.useEffect(()=>{G&&W()},[G,W]);let{onOpenChange:U,triggerPointerDownPosRef:q}=N;n.useEffect(()=>{if(R){let e={x:0,y:0},t=t=>{var r,n,l,o;e={x:Math.abs(Math.round(t.pageX)-(null!=(l=null==(r=q.current)?void 0:r.x)?l:0)),y:Math.abs(Math.round(t.pageY)-(null!=(o=null==(n=q.current)?void 0:n.y)?o:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():R.contains(r.target)||U(!1),document.removeEventListener("pointermove",t),q.current=null};return null!==q.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[R,U,q]),n.useEffect(()=>{let e=()=>U(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[U]);let[Z,X]=eN(e=>{let t=V().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eR(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),ee=n.useCallback((e,t,r)=>{let n=!F.current&&!r;(void 0!==N.value&&N.value===t||n)&&(A(e),n&&(F.current=!0))},[N.value]),et=n.useCallback(()=>null==R?void 0:R.focus(),[R]),er=n.useCallback((e,t,r)=>{let n=!F.current&&!r;(void 0!==N.value&&N.value===t||n)&&B(e)},[N.value]),en="popper"===l?$:Q,el=en===$?{side:d,sideOffset:v,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:x,sticky:y,hideWhenDetached:b,avoidCollisions:C}:{};return(0,S.jsx)(Y,{scope:r,content:R,viewport:E,onViewportChange:M,itemRefCallback:ee,selectedItem:L,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:W,selectedItemText:_,position:l,isPositioned:G,searchRef:Z,children:(0,S.jsx)(j.A,{as:J,allowPinchZoom:!0,children:(0,S.jsx)(f.n,{asChild:!0,trapped:N.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(o,e=>{var t;null==(t=N.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,S.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>N.onOpenChange(!1),children:(0,S.jsx)(en,{role:"listbox",id:N.contentId,"data-state":N.open?"open":"closed",dir:N.dir,onContextMenu:e=>e.preventDefault(),...T,...el,onPlaced:()=>O(!0),ref:D,style:{display:"flex",flexDirection:"column",outline:"none",...T.style},onKeyDown:(0,a.m)(T.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||X(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=V().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>K(t)),e.preventDefault()}})})})})})})});X.displayName="SelectContentImpl";var Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...a}=e,i=H(z,r),u=Z(z,r),[d,c]=n.useState(null),[p,f]=n.useState(null),v=(0,s.s)(t,e=>f(e)),h=P(r),m=n.useRef(!1),g=n.useRef(!0),{viewport:x,selectedItem:y,selectedItemText:C,focusSelectedItem:k}=u,j=n.useCallback(()=>{if(i.trigger&&i.valueNode&&d&&p&&x&&y&&C){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=C.getBoundingClientRect();if("rtl"!==i.dir){let l=n.left-t.left,a=r.left-l,i=e.left-a,s=e.width+i,u=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=s+"px",d.style.left=c+"px"}else{let l=t.right-n.right,a=window.innerWidth-r.right-l,i=window.innerWidth-e.right-a,s=e.width+i,u=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=s+"px",d.style.right=c+"px"}let a=h(),s=window.innerHeight-20,u=x.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),v=parseInt(c.paddingTop,10),w=parseInt(c.borderBottomWidth,10),g=f+v+u+parseInt(c.paddingBottom,10)+w,b=Math.min(5*y.offsetHeight,g),S=window.getComputedStyle(x),k=parseInt(S.paddingTop,10),j=parseInt(S.paddingBottom,10),T=e.top+e.height/2-10,N=y.offsetHeight/2,R=f+v+(y.offsetTop+N);if(R<=T){let e=a.length>0&&y===a[a.length-1].ref.current;d.style.bottom="0px";let t=Math.max(s-T,N+(e?j:0)+(p.clientHeight-x.offsetTop-x.offsetHeight)+w);d.style.height=R+t+"px"}else{let e=a.length>0&&y===a[0].ref.current;d.style.top="0px";let t=Math.max(T,f+x.offsetTop+(e?k:0)+N);d.style.height=t+(g-R)+"px",x.scrollTop=R-T+x.offsetTop}d.style.margin="".concat(10,"px 0"),d.style.minHeight=b+"px",d.style.maxHeight=s+"px",null==l||l(),requestAnimationFrame(()=>m.current=!0)}},[h,i.trigger,i.valueNode,d,p,x,y,C,i.dir,l]);(0,b.N)(()=>j(),[j]);let[T,N]=n.useState();(0,b.N)(()=>{p&&N(window.getComputedStyle(p).zIndex)},[p]);let R=n.useCallback(e=>{e&&!0===g.current&&(j(),null==k||k(),g.current=!1)},[j,k]);return(0,S.jsx)(ee,{scope:r,contentWrapper:d,shouldExpandOnScrollRef:m,onScrollButtonChange:R,children:(0,S.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:T},children:(0,S.jsx)(w.sG.div,{...a,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});Q.displayName="SelectItemAlignedPosition";var $=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,a=L(r);return(0,S.jsx)(h.UC,{...a,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});$.displayName="SelectPopperPosition";var[ee,et]=M(z,{}),er="SelectViewport",en=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...o}=e,i=Z(er,r),u=et(er,r),d=(0,s.s)(t,i.onViewportChange),c=n.useRef(0);return(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,S.jsx)(I.Slot,{scope:r,children:(0,S.jsx)(w.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=u;if((null==n?void 0:n.current)&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,a=Math.min(n,o),i=o-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});en.displayName=er;var el="SelectGroup",[eo,ea]=M(el),ei=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,v.B)();return(0,S.jsx)(eo,{scope:r,id:l,children:(0,S.jsx)(w.sG.div,{role:"group","aria-labelledby":l,...n,ref:t})})});ei.displayName=el;var es="SelectLabel",eu=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=ea(es,r);return(0,S.jsx)(w.sG.div,{id:l.id,...n,ref:t})});eu.displayName=es;var ed="SelectItem",[ec,ep]=M(ed),ef=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:o=!1,textValue:i,...u}=e,d=H(ed,r),c=Z(ed,r),p=d.value===l,[f,h]=n.useState(null!=i?i:""),[m,g]=n.useState(!1),x=(0,s.s)(t,e=>{var t;return null==(t=c.itemRefCallback)?void 0:t.call(c,e,l,o)}),y=(0,v.B)(),b=n.useRef("touch"),C=()=>{o||(d.onValueChange(l),d.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,S.jsx)(ec,{scope:r,value:l,disabled:o,textId:y,isSelected:p,onItemTextChange:n.useCallback(e=>{h(t=>{var r;return t||(null!=(r=null==e?void 0:e.textContent)?r:"").trim()})},[]),children:(0,S.jsx)(I.ItemSlot,{scope:r,value:l,disabled:o,textValue:f,children:(0,S.jsx)(w.sG.div,{role:"option","aria-labelledby":y,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...u,ref:x,onFocus:(0,a.m)(u.onFocus,()=>g(!0)),onBlur:(0,a.m)(u.onBlur,()=>g(!1)),onClick:(0,a.m)(u.onClick,()=>{"mouse"!==b.current&&C()}),onPointerUp:(0,a.m)(u.onPointerUp,()=>{"mouse"===b.current&&C()}),onPointerDown:(0,a.m)(u.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,a.m)(u.onPointerMove,e=>{if(b.current=e.pointerType,o){var t;null==(t=c.onItemLeave)||t.call(c)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(u.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=c.onItemLeave)||t.call(c)}}),onKeyDown:(0,a.m)(u.onKeyDown,e=>{var t;((null==(t=c.searchRef)?void 0:t.current)===""||" "!==e.key)&&(N.includes(e.key)&&C()," "===e.key&&e.preventDefault())})})})})});ef.displayName=ed;var ev="SelectItemText",eh=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,...i}=e,u=H(ev,r),d=Z(ev,r),c=ep(ev,r),p=B(ev,r),[f,v]=n.useState(null),h=(0,s.s)(t,e=>v(e),c.onItemTextChange,e=>{var t;return null==(t=d.itemTextRefCallback)?void 0:t.call(d,e,c.value,c.disabled)}),m=null==f?void 0:f.textContent,g=n.useMemo(()=>(0,S.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:x,onNativeOptionRemove:y}=p;return(0,b.N)(()=>(x(g),()=>y(g)),[x,y,g]),(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)(w.sG.span,{id:c.textId,...i,ref:h}),c.isSelected&&u.valueNode&&!u.valueNodeHasChildren?l.createPortal(i.children,u.valueNode):null]})});eh.displayName=ev;var em="SelectItemIndicator",ew=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ep(em,r).isSelected?(0,S.jsx)(w.sG.span,{"aria-hidden":!0,...n,ref:t}):null});ew.displayName=em;var eg="SelectScrollUpButton",ex=n.forwardRef((e,t)=>{let r=Z(eg,e.__scopeSelect),l=et(eg,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,b.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,S.jsx)(eS,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ex.displayName=eg;var ey="SelectScrollDownButton",eb=n.forwardRef((e,t)=>{let r=Z(ey,e.__scopeSelect),l=et(ey,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,b.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,S.jsx)(eS,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eb.displayName=ey;var eS=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...o}=e,i=Z("SelectScrollButton",r),s=n.useRef(null),u=P(r),d=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>d(),[d]),(0,b.N)(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[u]),(0,S.jsx)(w.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.m)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(l,50))}),onPointerMove:(0,a.m)(o.onPointerMove,()=>{var e;null==(e=i.onItemLeave)||e.call(i),null===s.current&&(s.current=window.setInterval(l,50))}),onPointerLeave:(0,a.m)(o.onPointerLeave,()=>{d()})})}),eC=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,S.jsx)(w.sG.div,{"aria-hidden":!0,...n,ref:t})});eC.displayName="SelectSeparator";var ek="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=L(r),o=H(ek,r),a=Z(ek,r);return o.open&&"popper"===a.position?(0,S.jsx)(h.i3,{...l,...n,ref:t}):null}).displayName=ek;var ej=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,...o}=e,a=n.useRef(null),i=(0,s.s)(t,a),u=function(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(l);return n.useEffect(()=>{let e=a.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(u!==l&&t){let r=new Event("change",{bubbles:!0});t.call(e,l),e.dispatchEvent(r)}},[u,l]),(0,S.jsx)(w.sG.select,{...o,style:{...C,...o.style},ref:i,defaultValue:l})});function eT(e){return""===e||void 0===e}function eN(e){let t=(0,x.c)(e),r=n.useRef(""),l=n.useRef(0),o=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,o,a]}function eR(e,t,r){var n,l;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,i=(n=e,l=Math.max(a,0),n.map((e,t)=>n[(l+t)%n.length]));1===o.length&&(i=i.filter(e=>e!==r));let s=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return s!==r?s:void 0}ej.displayName="SelectBubbleInput";var eI=V,eP=O,eE=K,eM=W,eD=U,eL=q,eA=en,eH=ei,e_=eu,eB=ef,eV=eh,eG=ew,eO=ex,eF=eb,eK=eC}}]);