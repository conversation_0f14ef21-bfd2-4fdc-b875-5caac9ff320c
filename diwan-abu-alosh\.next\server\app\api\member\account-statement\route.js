(()=>{var e={};e.id=1276,e.ids=[1276],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>n});var s=r(96330);let n=globalThis.prisma??new s.PrismaClient},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},52828:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>l,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{GET:()=>p});var n=r(96559),a=r(48088),o=r(37719),i=r(32190),u=r(31183),c=r(43205),m=r.n(c);async function p(e){try{let t,r=e.cookies.get("member-token")?.value;if(!r)return i.NextResponse.json({error:"غير مصرح - يجب تسجيل الدخول"},{status:401});try{t=m().verify(r,process.env.NEXTAUTH_SECRET||"fallback-secret")}catch{return i.NextResponse.json({error:"رمز المصادقة غير صالح"},{status:401})}if(!t.userId)return i.NextResponse.json({error:"جلسة غير صالحة - معرف المستخدم مفقود"},{status:401});let s=await u.z.user.findUnique({where:{id:t.userId},include:{memberUser:{include:{member:!0}}}});if(!s||!s.memberUser)return i.NextResponse.json({error:"المستخدم غير موجود"},{status:404});if(!s.memberUser.isActive)return i.NextResponse.json({error:"حسابك غير مفعل"},{status:403});if(!s.memberUser.canViewAccountStatement)return i.NextResponse.json({error:"ليس لديك صلاحية لعرض كشف الحساب"},{status:403});let n=s.memberUser.member,a=await u.z.member.findUnique({where:{id:n.id},include:{incomes:{orderBy:{date:"desc"},include:{createdBy:{select:{name:!0}}}}}});if(!a)return i.NextResponse.json({error:"العضو غير موجود"},{status:404});let o=a.incomes.reduce((e,t)=>e+t.amount,0),c=a.incomes.length,p={};a.incomes.forEach(e=>{p[e.type]||(p[e.type]={count:0,amount:0}),p[e.type].count++,p[e.type].amount+=e.amount});let l=new Date().getFullYear(),d=Array.from({length:12},(e,t)=>{let r=t+1,s=a.incomes.filter(e=>{let r=new Date(e.date);return r.getFullYear()===l&&r.getMonth()===t});return{month:r,monthName:`شهر${r}`,count:s.length,amount:s.reduce((e,t)=>e+t.amount,0)}}),x={member:{id:a.id,name:a.name,phone:a.phone,email:a.email,address:a.address,photo:a.photo,status:a.status,createdAt:a.createdAt},summary:{totalAmount:o,transactionCount:c,averageMonthlyContribution:c>0?o/Math.max(1,d.filter(e=>e.count>0).length):0,firstTransactionDate:a.incomes.length>0?a.incomes[a.incomes.length-1].date:null,lastTransactionDate:a.incomes.length>0?a.incomes[0].date:null},byType:p,monthlyData:d,recentTransactions:a.incomes.slice(0,10),allTransactions:a.incomes,period:{startDate:`${l}-01-01`,endDate:`${l}-12-31`,year:l}};return i.NextResponse.json(x)}catch(e){return console.error("خطأ في جلب كشف حساب العضو:",e),i.NextResponse.json({error:"حدث خطأ في جلب كشف الحساب"},{status:500})}}let l=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/member/account-statement/route",pathname:"/api/member/account-statement",filename:"route",bundlePath:"app/api/member/account-statement/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\member\\account-statement\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:x,serverHooks:h}=l;function f(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:x})}},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,580,3205],()=>r(52828));module.exports=s})();