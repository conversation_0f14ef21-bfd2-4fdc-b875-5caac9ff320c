import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'

// تسجيل دخول العضو
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, password } = body

    if (!email || !password) {
      return NextResponse.json(
        { message: 'البريد الإلكتروني وكلمة المرور مطلوبان' },
        { status: 400 }
      )
    }

    // البحث عن العضو
    const member = await prisma.member.findFirst({
      where: { 
        email: email.toLowerCase(),
        status: 'ACTIVE' // فقط الأعضاء النشطين
      }
    })

    if (!member) {
      return NextResponse.json(
        { message: 'البريد الإلكتروني غير صحيح أو العضو غير نشط' },
        { status: 401 }
      )
    }

    // التحقق من وجود كلمة مرور
    if (!member.password) {
      return NextResponse.json(
        { message: 'لم يتم تعيين كلمة مرور لهذا العضو. يرجى التواصل مع الإدارة' },
        { status: 401 }
      )
    }

    // التحقق من كلمة المرور
    const isPasswordValid = await bcrypt.compare(password, member.password)
    if (!isPasswordValid) {
      return NextResponse.json(
        { message: 'كلمة المرور غير صحيحة' },
        { status: 401 }
      )
    }

    // تحديث آخر تسجيل دخول
    await prisma.member.update({
      where: { id: member.id },
      data: { lastLogin: new Date() }
    })

    // إنشاء JWT token
    const token = jwt.sign(
      { 
        memberId: member.id,
        memberName: member.name,
        memberEmail: member.email,
        type: 'member' // نوع المستخدم
      },
      process.env.NEXTAUTH_SECRET || 'fallback-secret',
      { expiresIn: '24h' }
    )

    // إنشاء الاستجابة مع الكوكيز
    const response = NextResponse.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      member: {
        id: member.id,
        name: member.name,
        email: member.email,
        phone: member.phone
      }
    })

    // تعيين الكوكيز
    response.cookies.set('member-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 24 * 60 * 60 // 24 ساعة
    })

    return response
  } catch (error) {
    console.error('خطأ في تسجيل دخول العضو:', error)
    return NextResponse.json(
      { message: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// تسجيل خروج العضو
export async function DELETE() {
  try {
    const response = NextResponse.json({
      success: true,
      message: 'تم تسجيل الخروج بنجاح'
    })

    // حذف الكوكيز
    response.cookies.set('member-token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 0
    })

    return response
  } catch (error) {
    console.error('خطأ في تسجيل خروج العضو:', error)
    return NextResponse.json(
      { message: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// التحقق من حالة تسجيل الدخول
export async function GET(request: NextRequest) {
  try {
    const token = request.cookies.get('member-token')?.value

    if (!token) {
      return NextResponse.json(
        { authenticated: false, message: 'غير مسجل الدخول' },
        { status: 401 }
      )
    }

    // التحقق من صحة التوكن
    const decoded = jwt.verify(token, process.env.NEXTAUTH_SECRET || 'fallback-secret') as { type: string; memberId: string }

    if (decoded.type !== 'member') {
      return NextResponse.json(
        { authenticated: false, message: 'نوع المستخدم غير صحيح' },
        { status: 401 }
      )
    }

    // التحقق من وجود العضو في قاعدة البيانات
    const member = await prisma.member.findFirst({
      where: { 
        id: decoded.memberId,
        status: 'ACTIVE'
      }
    })

    if (!member) {
      return NextResponse.json(
        { authenticated: false, message: 'العضو غير موجود أو غير نشط' },
        { status: 401 }
      )
    }

    return NextResponse.json({
      authenticated: true,
      member: {
        id: member.id,
        name: member.name,
        email: member.email,
        phone: member.phone
      }
    })
  } catch (error) {
    console.error('خطأ في التحقق من حالة تسجيل الدخول:', error)
    return NextResponse.json(
      { authenticated: false, message: 'خطأ في التحقق من الهوية' },
      { status: 401 }
    )
  }
}
