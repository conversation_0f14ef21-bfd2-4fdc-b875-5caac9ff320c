(()=>{var e={};e.id=4093,e.ids=[4093],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,s,t)=>{"use strict";t.d(s,{N:()=>n});var r=t(13581),a=t(85663),i=t(31183);let n={providers:[(0,r.A)({name:"credentials",credentials:{email:{label:"البريد الإلكتروني",type:"email"},password:{label:"كلمة المرور",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let s=await i.z.user.findUnique({where:{email:e.email}});return s&&await a.Ay.compare(e.password,s.password)?{id:s.id,email:s.email,name:s.name,role:s.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:s})=>(s&&(e.role=s.role),e),session:async({session:e,token:s})=>(s&&(e.user.id=s.sub,e.user.role=s.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},13798:(e,s,t)=>{"use strict";t.r(s),t.d(s,{patchFetch:()=>b,routeModule:()=>w,serverHooks:()=>h,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>g});var r={};t.r(r),t.d(r,{DELETE:()=>c,PUT:()=>m});var a=t(96559),i=t(48088),n=t(37719),u=t(32190),o=t(19854),p=t(12909),d=t(31183),l=t(85663);async function m(e,{params:s}){try{let t=await (0,o.getServerSession)(p.N);if(!t)return u.NextResponse.json({message:"غير مصرح"},{status:401});if(!["ADMIN","DATA_ENTRY"].includes(t.user.role))return u.NextResponse.json({message:"ليس لديك صلاحية لتعديل كلمات مرور الأعضاء"},{status:403});let{password:r}=await e.json(),{id:a}=await s;if(!r)return u.NextResponse.json({message:"كلمة المرور مطلوبة"},{status:400});if(r.length<6)return u.NextResponse.json({message:"كلمة المرور يجب أن تكون على الأقل 6 أحرف"},{status:400});let i=await d.z.member.findUnique({where:{id:a},select:{id:!0,name:!0,email:!0}});if(!i)return u.NextResponse.json({message:"العضو غير موجود"},{status:404});if(!i.email)return u.NextResponse.json({message:"لا يمكن تعيين كلمة مرور للعضو بدون بريد إلكتروني"},{status:400});let n=await l.Ay.hash(r,12),m=await d.z.memberUser.findFirst({where:{memberId:a},include:{user:!0}}),c=await d.z.member.update({where:{id:a},data:{password:n,updatedAt:new Date},select:{id:!0,name:!0,email:!0,updatedAt:!0}});if(m&&m.user)await d.z.user.update({where:{id:m.user.id},data:{password:n,updatedAt:new Date}});else{let e=await d.z.user.create({data:{name:i.name,email:i.email,password:n,role:"VIEWER",phone:null}});await d.z.memberUser.create({data:{memberId:a,userId:e.id,isActive:!0,canViewAccountStatement:!0,canViewGallery:!0}})}return u.NextResponse.json({success:!0,message:"تم تحديث كلمة مرور العضو بنجاح",member:c})}catch(e){return console.error("خطأ في تحديث كلمة مرور العضو:",e),u.NextResponse.json({message:"حدث خطأ في تحديث كلمة مرور العضو"},{status:500})}}async function c(e,{params:s}){try{let e=await (0,o.getServerSession)(p.N);if(!e)return u.NextResponse.json({message:"غير مصرح"},{status:401});if("ADMIN"!==e.user.role)return u.NextResponse.json({message:"ليس لديك صلاحية لحذف كلمات مرور الأعضاء"},{status:403});let{id:t}=await s;if(!await d.z.member.findUnique({where:{id:t},select:{id:!0,name:!0,email:!0}}))return u.NextResponse.json({message:"العضو غير موجود"},{status:404});let r=await d.z.memberUser.findFirst({where:{memberId:t},include:{user:!0}}),a=await d.z.member.update({where:{id:t},data:{password:null,lastLogin:null,updatedAt:new Date},select:{id:!0,name:!0,email:!0,updatedAt:!0}});return r&&r.user&&await d.z.memberUser.update({where:{id:r.id},data:{isActive:!1,updatedAt:new Date}}),u.NextResponse.json({success:!0,message:"تم إلغاء تفعيل تسجيل دخول العضو بنجاح",member:a})}catch(e){return console.error("خطأ في حذف كلمة مرور العضو:",e),u.NextResponse.json({message:"حدث خطأ في حذف كلمة مرور العضو"},{status:500})}}let w=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/members/[id]/password/route",pathname:"/api/members/[id]/password",filename:"route",bundlePath:"app/api/members/[id]/password/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\members\\[id]\\password\\route.ts",nextConfigOutput:"standalone",userland:r}),{workAsyncStorage:x,workUnitAsyncStorage:g,serverHooks:h}=w;function b(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:g})}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,s,t)=>{"use strict";t.d(s,{z:()=>a});var r=t(96330);let a=globalThis.prisma??new r.PrismaClient},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,5663,4999,3412,580],()=>t(13798));module.exports=r})();