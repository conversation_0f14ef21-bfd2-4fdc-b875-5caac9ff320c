'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { formatCurrency, formatDate } from '@/lib/utils'
import {
  AlertTriangle,
  FileText,
  Download,
  Clock,
  Heart,
  Users,
  Phone,
  // Mail,
  // Calendar,
  DollarSign,
  MessageCircle
} from 'lucide-react'

interface Member {
  id: string
  name: string
  phone?: string
  email?: string
  status: 'ACTIVE' | 'INACTIVE' | 'LATE' | 'NON_COMPLIANT'
  createdAt: string
  incomes: {
    id: string
    amount: number
    date: string
    source: string
  }[]
}

interface EmergencyContribution {
  id: string
  amount: number
  date: string
  description: string
  member?: {
    name: string
  }
}

interface HelperReportsProps {
  onExportPDF: (data: any, type: string) => void
  onExportCSV: (data: any, type: string) => void
}

export default function HelperReports({ onExportPDF, onExportCSV }: HelperReportsProps) {
  const [members, setMembers] = useState<Member[]>([])
  const [emergencyContributions, setEmergencyContributions] = useState<EmergencyContribution[]>([])
  const [loading, setLoading] = useState(true)
  const [latePaymentThreshold, setLatePaymentThreshold] = useState(30) // أيام
  const [sendingNotifications, setSendingNotifications] = useState(false)

  // جلب البيانات
  const fetchData = async () => {
    try {
      setLoading(true)
      
      const [membersRes, incomesRes] = await Promise.all([
        fetch('/api/members?limit=1000&includeIncomes=true'),
        fetch('/api/incomes?limit=1000') // جميع الإيرادات للبحث عن الطوارئ
      ])

      if (!membersRes.ok || !incomesRes.ok) {
        throw new Error('فشل في جلب البيانات')
      }

      const [membersData, incomesData] = await Promise.all([
        membersRes.json(),
        incomesRes.json()
      ])

      setMembers(membersData.members || [])
      
      // فلترة التبرعات الطارئة (التي تحتوي على كلمات مثل "طوارئ" أو "وفاة" أو "كارثة")
      const emergencyKeywords = ['طوارئ', 'وفاة', 'كارثة', 'مساعدة', 'عاجل', 'طارئ', 'مرض', 'علاج', 'عملية', 'مستشفى']
      const emergencyIncomes = (incomesData.incomes || []).filter((income: any) =>
        emergencyKeywords.some(keyword =>
          income.source?.toLowerCase().includes(keyword) ||
          income.description?.toLowerCase().includes(keyword) ||
          income.type === 'EMERGENCY' // إذا كان هناك نوع خاص للطوارئ
        )
      )



      setEmergencyContributions(emergencyIncomes)
    } catch (error) {
      console.error('خطأ في جلب البيانات:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  // حساب الأعضاء المتأخرين
  const getLateMembers = () => {
    if (!members || !Array.isArray(members)) {
      return []
    }

    const now = new Date()
    const thresholdDate = new Date(now.getTime() - latePaymentThreshold * 24 * 60 * 60 * 1000)



    // const filteredMembers = members.filter(member => {
      // إذا كان العضو غير نشط، لا نعتبره متأخر
      if (member.status !== 'ACTIVE' && member.status !== 'LATE') {
        return false
      }

      // إذا لم يكن له أي مساهمات
      if (!member.incomes || !Array.isArray(member.incomes) || member.incomes.length === 0) {
        const joinDate = new Date(member.createdAt)
        return joinDate < thresholdDate
      }

      // آخر مساهمة - تصفية التواريخ الصحيحة فقط
      const validDates = member.incomes
        .filter(income => income && income.date)
        .map(income => new Date(income.date))
        .filter(date => !isNaN(date.getTime()))

      if (validDates.length === 0) {
        const joinDate = new Date(member.createdAt)
        return joinDate < thresholdDate
      }

      const lastContribution = new Date(Math.max(...validDates.map(date => date.getTime())))
      return lastContribution < thresholdDate
    }).map(member => {
      const totalContributions = (member.incomes || []).reduce((sum, income) => sum + (income?.amount || 0), 0)

      // تصفية التواريخ الصحيحة فقط
      const validDates = (member.incomes || [])
        .filter(income => income && income.date)
        .map(income => new Date(income.date))
        .filter(date => !isNaN(date.getTime()))

      const lastContribution = validDates.length > 0
        ? new Date(Math.max(...validDates.map(date => date.getTime())))
        : new Date(member.createdAt)
      
      const daysSinceLastPayment = Math.floor((now.getTime() - lastContribution.getTime()) / (24 * 60 * 60 * 1000))
      
      // حساب المبلغ المستحق (افتراضياً 50 دينار شهرياً)
      const monthlyDue = 50
      const monthsLate = Math.floor(daysSinceLastPayment / 30)
      const amountDue = monthsLate * monthlyDue

      return {
        ...member,
        totalContributions,
        lastContribution,
        daysSinceLastPayment,
        monthsLate,
        amountDue
      }
    // }).sort((a, b) => b.daysSinceLastPayment - a.daysSinceLastPayment)
  }

  // تصدير تقرير الأعضاء المتأخرين
  const handleExportLateMembers = (format: 'pdf' | 'csv') => {
    const lateMembers = getLateMembers() || []

    const reportData = {
      title: 'تقرير الأعضاء المتأخرين عن الدفع',
      date: new Date().toLocaleDateString('ar-JO'),
      threshold: `${latePaymentThreshold} يوم`,
      summary: {
        totalLateMembers: lateMembers.length,
        totalAmountDue: lateMembers.reduce((sum, member) => sum + (member?.amountDue || 0), 0)
      },
      lateMembers: lateMembers.map(member => ({
        name: member.name,
        phone: member.phone || 'غير محدد',
        email: member.email || 'غير محدد',
        status: getStatusLabel(member.status),
        lastContribution: member.lastContribution && !isNaN(member.lastContribution.getTime())
          ? formatDate(member.lastContribution.toISOString())
          : 'غير محدد',
        daysSinceLastPayment: member.daysSinceLastPayment,
        monthsLate: member.monthsLate,
        amountDue: member.amountDue,
        totalContributions: member.totalContributions
      }))
    }

    if (format === 'pdf') {
      onExportPDF(reportData, 'late-members')
    } else {
      onExportCSV(reportData, 'late-members')
    }
  }

  // إرسال إشعارات للأعضاء المتأخرين
  const handleSendNotifications = async () => {
    const lateMembers = getLateMembers() || []
    const membersWithPhone = lateMembers.filter(member => member?.phone)

    if (membersWithPhone.length === 0) {
      alert('لا يوجد أعضاء متأخرون لديهم أرقام هواتف')
      return
    }

    const confirmed = confirm(`هل تريد إرسال إشعارات واتساب لـ ${membersWithPhone.length} عضو متأخر؟`)
    if (!confirmed) return

    setSendingNotifications(true)

    try {
      // إرسال رسائل واتساب
      membersWithPhone.forEach((member, index) => {
        setTimeout(() => {
          const message = `السلام عليكم ${member.name}،\n\nنذكركم بسداد المساهمة المستحقة.\n\nالمبلغ المستحق: ${formatCurrency(member.amountDue)}\nأيام التأخير: ${member.daysSinceLastPayment} يوم\n\nشكراً لتعاونكم\nديوان آل أبو علوش`
          const phoneNumber = member.phone.replace(/\D/g, '')
          window.open(`https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`, '_blank')
        }, index * 2000) // تأخير 2 ثانية بين كل رسالة
      })

      alert(`تم فتح ${membersWithPhone.length} نافذة واتساب لإرسال الإشعارات`)
    } catch (error) {
      console.error('خطأ في إرسال الإشعارات:', error)
      alert('حدث خطأ في إرسال الإشعارات')
    } finally {
      setSendingNotifications(false)
    }
  }

  // تصدير تقرير الطوارئ
  const handleExportEmergencyReport = (format: 'pdf' | 'csv') => {
    const totalEmergencyAmount = (emergencyContributions || []).reduce((sum, contribution) => sum + (contribution?.amount || 0), 0)
    
    const reportData = {
      title: 'تقرير الطوارئ والمساعدات',
      date: new Date().toLocaleDateString('ar-JO'),
      summary: {
        totalContributions: emergencyContributions.length,
        totalAmount: totalEmergencyAmount,
        averageContribution: emergencyContributions.length > 0 ? totalEmergencyAmount / emergencyContributions.length : 0
      },
      contributions: emergencyContributions.map(contribution => ({
        date: formatDate(contribution.date),
        memberName: contribution.member?.name || 'غير محدد',
        amount: contribution.amount,
        description: contribution.description,
        purpose: contribution.description // يمكن تحسينه لاستخراج الغرض
      }))
    }

    if (format === 'pdf') {
      onExportPDF(reportData, 'emergency-report')
    } else {
      onExportCSV(reportData, 'emergency-report')
    }
  }

  // الحصول على تسمية الحالة
  const getStatusLabel = (status: string) => {
    const statusLabels = {
      'ACTIVE': 'نشط',
      'INACTIVE': 'غير نشط',
      'LATE': 'متأخر',
      'NON_COMPLIANT': 'غير ملتزم'
    }
    return statusLabels[status as keyof typeof statusLabels] || status
  }

  // الحصول على لون الحالة
  const getStatusColor = (status: string) => {
    const statusColors = {
      'ACTIVE': 'bg-green-100 text-green-800',
      'INACTIVE': 'bg-gray-100 text-gray-800',
      'LATE': 'bg-yellow-100 text-yellow-800',
      'NON_COMPLIANT': 'bg-red-100 text-red-800'
    }
    return statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-gray-500">جاري تحميل البيانات...</div>
      </div>
    )
  }



  const lateMembers = getLateMembers() || []
  const totalAmountDue = lateMembers.reduce((sum, member) => sum + (member.amountDue || 0), 0)
  const totalEmergencyAmount = (emergencyContributions || []).reduce((sum, contribution) => sum + (contribution.amount || 0), 0)

  return (
    <div className="space-y-6">
      {/* عنوان القسم */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-bold text-gray-900 flex items-center">
            <AlertTriangle className="w-5 h-5 ml-2" />
            التقارير المساعدة
          </h2>
          <p className="text-gray-600">تقارير الأعضاء المتأخرين وحالات الطوارئ</p>
        </div>
      </div>

      {/* الإحصائيات السريعة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <Clock className="w-8 h-8 text-yellow-500" />
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">الأعضاء المتأخرون</p>
                <p className="text-2xl font-bold text-yellow-600">{lateMembers.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <DollarSign className="w-8 h-8 text-red-500" />
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">المبالغ المستحقة</p>
                <p className="text-2xl font-bold text-red-600">{formatCurrency(totalAmountDue)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <Heart className="w-8 h-8 text-purple-500" />
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">تبرعات الطوارئ</p>
                <p className="text-2xl font-bold text-purple-600">{emergencyContributions.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <DollarSign className="w-8 h-8 text-green-500" />
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">مبلغ الطوارئ</p>
                <p className="text-2xl font-bold text-green-600">{formatCurrency(totalEmergencyAmount)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* تقرير الأعضاء المتأخرين */}
      <Card>
        <CardHeader>
          <CardTitle className="flex justify-between items-center">
            <span className="flex items-center">
              <Clock className="w-5 h-5 ml-2" />
              الأعضاء المتأخرون عن الدفع
            </span>
            <div className="flex space-x-2 space-x-reverse">
              <div className="flex items-center space-x-2 space-x-reverse">
                <label className="text-sm text-gray-600">عتبة التأخير (أيام):</label>
                <Input
                  type="number"
                  value={latePaymentThreshold}
                  onChange={(e) => setLatePaymentThreshold(Number(e.target.value))}
                  className="w-20"
                  min="1"
                />
              </div>
              <Button
                onClick={() => handleExportLateMembers('pdf')}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                <FileText className="w-4 h-4 ml-2" />
                PDF
              </Button>
              <Button
                onClick={() => handleExportLateMembers('csv')}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                <Download className="w-4 h-4 ml-2" />
                CSV
              </Button>
              <Button
                onClick={handleSendNotifications}
                disabled={sendingNotifications || lateMembers.length === 0}
                className="bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50"
              >
                <Phone className="w-4 h-4 ml-2" />
                {sendingNotifications ? 'جاري الإرسال...' : 'إرسال إشعارات'}
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {lateMembers.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-500 mb-4">
                <div className="mb-2">✅ لا يوجد أعضاء متأخرون عن الدفع حالياً</div>
                <div className="text-sm">عتبة التأخير الحالية: {latePaymentThreshold} يوم</div>
                <div className="text-sm">عدد الأعضاء الكلي: {members.length}</div>
              </div>
              {members.length > 0 && (
                <div className="text-sm text-blue-600">
                  جميع الأعضاء النشطين لديهم مساهمات حديثة (خلال آخر {latePaymentThreshold} يوم)
                </div>
              )}
              {members.length === 0 && (
                <div className="text-sm text-orange-600">
                  لا توجد بيانات أعضاء في النظام
                </div>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="text-right p-3 font-medium text-gray-700">الاسم</th>
                    <th className="text-right p-3 font-medium text-gray-700">الهاتف</th>
                    <th className="text-right p-3 font-medium text-gray-700">الحالة</th>
                    <th className="text-right p-3 font-medium text-gray-700">آخر دفعة</th>
                    <th className="text-right p-3 font-medium text-gray-700">أيام التأخير</th>
                    <th className="text-right p-3 font-medium text-gray-700">المبلغ المستحق</th>
                    <th className="text-right p-3 font-medium text-gray-700">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {lateMembers.map((member) => (
                    <tr key={member.id} className="border-b hover:bg-gray-50">
                      <td className="p-3 font-medium">{member.name}</td>
                      <td className="p-3">
                        <div className="flex items-center">
                          <Phone className="w-4 h-4 ml-2 text-gray-400" />
                          {member.phone || 'غير محدد'}
                        </div>
                      </td>
                      <td className="p-3">
                        <Badge className={getStatusColor(member.status)}>
                          {getStatusLabel(member.status)}
                        </Badge>
                      </td>
                      <td className="p-3">
                        {member.lastContribution && !isNaN(member.lastContribution.getTime())
                          ? formatDate(member.lastContribution.toISOString())
                          : 'غير محدد'
                        }
                      </td>
                      <td className="p-3">
                        <span className="font-medium text-red-600">
                          {member.daysSinceLastPayment} يوم
                        </span>
                      </td>
                      <td className="p-3">
                        <span className="font-bold text-red-600">
                          {formatCurrency(member.amountDue)}
                        </span>
                      </td>
                      <td className="p-3">
                        <div className="flex space-x-1 space-x-reverse">
                          <Button size="sm" variant="outline">
                            إشعار
                          </Button>
                          {member.phone && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                const message = `السلام عليكم ${member.name}،\n\nنذكركم بسداد المساهمة المستحقة.\n\nالمبلغ المستحق: ${formatCurrency(member.amountDue)}\nأيام التأخير: ${member.daysSinceLastPayment} يوم\n\nشكراً لتعاونكم\nديوان آل أبو علوش`
                                const phoneNumber = member.phone.replace(/\D/g, '')
                                window.open(`https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`, '_blank')
                              }}
                              className="text-green-600 hover:text-green-700"
                            >
                              <MessageCircle className="w-3 h-3 ml-1" />
                              واتساب
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* تقرير الطوارئ */}
      <Card>
        <CardHeader>
          <CardTitle className="flex justify-between items-center">
            <span className="flex items-center">
              <Heart className="w-5 h-5 ml-2" />
              تقرير الطوارئ والمساعدات
            </span>
            <div className="flex space-x-2 space-x-reverse">
              <Button
                onClick={() => handleExportEmergencyReport('pdf')}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                <FileText className="w-4 h-4 ml-2" />
                PDF
              </Button>
              <Button
                onClick={() => handleExportEmergencyReport('csv')}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                <Download className="w-4 h-4 ml-2" />
                CSV
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {emergencyContributions.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-500 mb-4">
                <div className="mb-2">ℹ️ لا توجد تبرعات طوارئ مسجلة</div>
                <div className="text-sm mb-2">يتم البحث التلقائي عن الكلمات المفتاحية في مصدر أو وصف التبرع:</div>
                <div className="text-xs bg-gray-100 p-2 rounded inline-block">
                  طوارئ، وفاة، كارثة، مساعدة، عاجل، طارئ، مرض، علاج، عملية، مستشفى
                </div>
              </div>
              <div className="text-sm text-blue-600">
                لإظهار تبرع كطارئ، تأكد من تضمين إحدى هذه الكلمات في مصدر التبرع أو وصفه
              </div>
            </div>
          ) : (
            <>
              {/* ملخص الطوارئ */}
              <div className="mb-6 p-4 bg-purple-50 rounded-lg">
                <h3 className="font-medium text-purple-900 mb-2">ملخص تبرعات الطوارئ</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <span className="text-sm text-purple-600">إجمالي التبرعات:</span>
                    <div className="font-bold text-purple-900">{emergencyContributions.length}</div>
                  </div>
                  <div>
                    <span className="text-sm text-purple-600">إجمالي المبلغ:</span>
                    <div className="font-bold text-purple-900">{formatCurrency(totalEmergencyAmount)}</div>
                  </div>
                  <div>
                    <span className="text-sm text-purple-600">متوسط التبرع:</span>
                    <div className="font-bold text-purple-900">
                      {formatCurrency(emergencyContributions.length > 0 ? totalEmergencyAmount / emergencyContributions.length : 0)}
                    </div>
                  </div>
                </div>
              </div>

              {/* جدول تبرعات الطوارئ */}
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-right p-3 font-medium text-gray-700">التاريخ</th>
                      <th className="text-right p-3 font-medium text-gray-700">المتبرع</th>
                      <th className="text-right p-3 font-medium text-gray-700">المبلغ</th>
                      <th className="text-right p-3 font-medium text-gray-700">الغرض</th>
                    </tr>
                  </thead>
                  <tbody>
                    {emergencyContributions.map((contribution) => (
                      <tr key={contribution.id} className="border-b hover:bg-gray-50">
                        <td className="p-3">{formatDate(contribution.date)}</td>
                        <td className="p-3">
                          <div className="flex items-center">
                            <Users className="w-4 h-4 ml-2 text-gray-400" />
                            {contribution.member?.name || 'غير محدد'}
                          </div>
                        </td>
                        <td className="p-3 font-bold text-green-600">
                          {formatCurrency(contribution.amount)}
                        </td>
                        <td className="p-3 text-sm">{contribution.description}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
