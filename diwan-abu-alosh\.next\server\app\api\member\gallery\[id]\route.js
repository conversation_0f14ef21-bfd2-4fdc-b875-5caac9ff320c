(()=>{var e={};e.id=715,e.ids=[715],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5006:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>d,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>l});var o=t(96559),n=t(48088),i=t(37719),a=t(32190),u=t(31183),p=t(43205),c=t.n(p);async function l(e,{params:r}){try{let t,s=e.cookies.get("member-token")?.value;if(!s)return a.NextResponse.json({error:"غير مصرح - يجب تسجيل الدخول"},{status:401});try{t=c().verify(s,process.env.NEXTAUTH_SECRET||"fallback-secret")}catch{return a.NextResponse.json({error:"رمز المصادقة غير صالح"},{status:401})}if(!t.userId)return a.NextResponse.json({error:"جلسة غير صالحة - معرف المستخدم مفقود"},{status:401});let o=await u.z.user.findUnique({where:{id:t.userId},include:{memberUser:{include:{member:!0}}}});if(!o||!o.memberUser)return a.NextResponse.json({error:"المستخدم غير موجود"},{status:404});if(!o.memberUser.isActive)return a.NextResponse.json({error:"حسابك غير مفعل"},{status:403});if(!o.memberUser.canViewGallery)return a.NextResponse.json({error:"ليس لديك صلاحية لعرض المعرض"},{status:403});let{id:n}=await r,i=await u.z.galleryFolder.findUnique({where:{id:n},include:{creator:{select:{name:!0}},photos:{include:{uploader:{select:{name:!0}}},orderBy:{createdAt:"desc"}},_count:{select:{photos:!0}}}});if(!i)return a.NextResponse.json({error:"المجلد غير موجود"},{status:404});return a.NextResponse.json(i)}catch(e){return console.error("خطأ في جلب المجلد للعضو:",e),a.NextResponse.json({error:"حدث خطأ في جلب المجلد"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/member/gallery/[id]/route",pathname:"/api/member/gallery/[id]",filename:"route",bundlePath:"app/api/member/gallery/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\member\\gallery\\[id]\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:x,serverHooks:f}=d;function v(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:x})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>o});var s=t(96330);let o=globalThis.prisma??new s.PrismaClient},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,3205],()=>t(5006));module.exports=s})();