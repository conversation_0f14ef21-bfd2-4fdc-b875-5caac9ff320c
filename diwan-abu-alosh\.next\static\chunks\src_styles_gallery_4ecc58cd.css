/* [project]/src/styles/gallery.css [app-client] (css) */
.gallery-image {
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
}

.gallery-image:hover {
  transform: scale(1.02);
  box-shadow: 0 20px 25px -5px #0000001a, 0 10px 10px -5px #0000000a;
}

.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) 0 0 / 200% 100%;
  animation: 1.5s infinite shimmer;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

.line-clamp-2 {
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}

.line-clamp-3 {
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}

.gallery-button {
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
  position: relative;
  overflow: hidden;
}

.gallery-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px #0003;
}

.gallery-button:before {
  content: "";
  background: linear-gradient(90deg, #0000, #fff3, #0000);
  width: 100%;
  height: 100%;
  transition: left .5s;
  position: absolute;
  top: 0;
  left: -100%;
}

.gallery-button:hover:before {
  left: 100%;
}

.drop-zone {
  transition: all .3s;
  position: relative;
  overflow: hidden;
}

.drop-zone:before {
  content: "";
  background: linear-gradient(45deg, #0000 30%, #3b82f61a 50%, #0000 70%);
  transition: transform .6s;
  position: absolute;
  inset: 0;
  transform: translateX(-100%);
}

.drop-zone:hover:before {
  transform: translateX(100%);
}

.gallery-card {
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
  position: relative;
  overflow: hidden;
}

.gallery-card:before {
  content: "";
  opacity: 0;
  background: linear-gradient(135deg, #ffffff1a 0%, #fff0 100%);
  transition: opacity .3s;
  position: absolute;
  inset: 0;
}

.gallery-card:hover:before {
  opacity: 1;
}

.gallery-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 25px 50px -12px #00000040;
}

.gallery-grid {
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  display: grid;
}

@media (width <= 640px) {
  .gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
  }
}

@media (width <= 480px) {
  .gallery-grid {
    grid-template-columns: 1fr;
    gap: .75rem;
  }
}

.image-viewer {
  backdrop-filter: blur(10px);
  background: #000c;
}

.image-viewer img {
  -o-object-fit: contain;
  object-fit: contain;
  border-radius: 8px;
  max-width: 90vw;
  max-height: 80vh;
  box-shadow: 0 25px 50px -12px #00000080;
}

.stats-card {
  background: linear-gradient(135deg, var(--gradient-from) 0%, var(--gradient-to) 100%);
  border: none;
  position: relative;
  overflow: hidden;
}

.stats-card:before {
  content: "";
  opacity: 0;
  background: linear-gradient(135deg, #ffffff1a 0%, #ffffff0d 100%);
  transition: opacity .3s;
  position: absolute;
  inset: 0;
}

.stats-card:hover:before {
  opacity: 1;
}

.gradient-header {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%);
  position: relative;
  overflow: hidden;
}

.gradient-header:before {
  content: "";
  background: linear-gradient(45deg, #0000 30%, #ffffff1a 50%, #0000 70%);
  animation: 3s infinite shine;
  position: absolute;
  inset: 0;
  transform: translateX(-100%);
}

@keyframes shine {
  0% {
    transform: translateX(-100%);
  }

  50% {
    transform: translateX(100%);
  }

  100% {
    transform: translateX(100%);
  }
}

.floating-buttons {
  opacity: 0;
  transition: all .4s cubic-bezier(.4, 0, .2, 1);
  transform: translateY(15px)scale(.8);
}

.gallery-card:hover .floating-buttons {
  opacity: 1;
  transform: translateY(0)scale(1);
}

.floating-buttons button {
  backdrop-filter: blur(10px);
  border: 2px solid #fff3;
  transition: all .3s;
}

.floating-buttons button:hover {
  transform: scale(1.1);
  box-shadow: 0 8px 20px #0000004d;
}

.floating-buttons .bg-blue-600:hover {
  box-shadow: 0 8px 20px #2563eb66;
}

.floating-buttons .bg-green-600:hover {
  box-shadow: 0 8px 20px #16a34a66;
}

.floating-buttons .bg-purple-600:hover {
  box-shadow: 0 8px 20px #9333ea66;
}

.floating-buttons .bg-red-600:hover {
  box-shadow: 0 8px 20px #dc262666;
}

.search-input {
  transition: all .3s;
}

.search-input:focus {
  transform: scale(1.02);
  box-shadow: 0 10px 25px -5px #3b82f633;
}

.upload-progress {
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 50%, #3b82f6 100%) 0 0 / 200% 100%;
  animation: 2s infinite progress-shine;
}

@keyframes progress-shine {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

.badge-glow {
  animation: 2s infinite alternate glow;
  box-shadow: 0 0 10px #3b82f64d;
}

@keyframes glow {
  from {
    box-shadow: 0 0 5px #3b82f633;
  }

  to {
    box-shadow: 0 0 15px #3b82f666;
  }
}

.enhanced-button {
  letter-spacing: .025em;
  text-shadow: 0 1px 2px #0000001a;
  background-clip: padding-box;
  border: 2px solid #0000;
  font-weight: 600;
}

.enhanced-button:hover {
  letter-spacing: .05em;
  text-shadow: 0 2px 4px #0003;
}

.high-contrast-text {
  color: #1f2937;
  text-shadow: 0 1px 2px #fffc;
  font-weight: 600;
}

.high-contrast-text-white {
  color: #fff;
  text-shadow: 0 1px 3px #00000080;
  font-weight: 600;
}

.enhanced-border {
  border: 2px solid #0000001a;
  box-shadow: 0 2px 8px #0000001a;
}

.enhanced-border:hover {
  border-color: #0003;
  box-shadow: 0 4px 16px #00000026;
}

.enhanced-icon {
  filter: drop-shadow(0 1px 2px #0000001a);
}

.enhanced-icon:hover {
  filter: drop-shadow(0 2px 4px #0003);
  transform: scale(1.05);
}


/*# sourceMappingURL=src_styles_gallery_4ecc58cd.css.map*/