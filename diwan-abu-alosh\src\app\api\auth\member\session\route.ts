import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import jwt from 'jsonwebtoken'

export async function GET(request: NextRequest) {
  try {
    const token = request.cookies.get('member-token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'غير مصرح' },
        { status: 401 }
      )
    }

    // التحقق من صحة التوكن
    let decoded: { userId: string }
    try {
      decoded = jwt.verify(token, process.env.NEXTAUTH_SECRET || 'fallback-secret')
    } catch (error) {
      // تسجيل الأخطاء الحقيقية فقط (ليس الأخطاء العادية)
      if (error instanceof jwt.TokenExpiredError) {
        // انتهاء صلاحية عادي - لا نحتاج لتسجيل خطأ
      } else if (error instanceof jwt.JsonWebTokenError) {
        // توكن تالف أو غير صحيح - لا نحتاج لتسجيل خطأ
      } else {
        console.error('خطأ في التحقق من التوكن:', error)
      }
      return NextResponse.json(
        { error: 'جلسة غير صالحة' },
        { status: 401 }
      )
    }

    // التحقق من وجود userId في التوكن
    if (!decoded.userId) {
      return NextResponse.json(
        { error: 'جلسة غير صالحة' },
        { status: 401 }
      )
    }

    // جلب بيانات المستخدم والعضو
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      include: {
        memberUser: {
          include: {
            member: true
          }
        }
      }
    })

    if (!user || !user.memberUser) {
      return NextResponse.json(
        { error: 'المستخدم غير موجود' },
        { status: 404 }
      )
    }

    // التحقق من أن الحساب مفعل
    if (!user.memberUser.isActive) {
      return NextResponse.json(
        { error: 'حسابك غير مفعل' },
        { status: 403 }
      )
    }

    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        member: user.memberUser.member,
        permissions: {
          canViewAccountStatement: user.memberUser.canViewAccountStatement,
          canViewGallery: user.memberUser.canViewGallery
        }
      }
    })
  } catch (error) {
    console.error('خطأ في التحقق من جلسة العضو:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في التحقق من الجلسة' },
      { status: 500 }
    )
  }
}
