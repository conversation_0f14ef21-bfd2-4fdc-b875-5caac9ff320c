{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Cred<PERSON><PERSON><PERSON><PERSON>ider from 'next-auth/providers/credentials'\nimport bcrypt from 'bcryptjs'\nimport { prisma } from './prisma'\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'البريد الإلكتروني', type: 'email' },\n        password: { label: 'كلمة المرور', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          role: user.role,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAqB,MAAM;gBAAQ;gBACnD,UAAU;oBAAE,OAAO;oBAAe,MAAM;gBAAW;YACrD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;YACxB;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;YAChC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/app/api/gallery-folders/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\nimport { z } from 'zod'\n\n// Schema للتحقق من صحة البيانات\nconst updateGalleryFolderSchema = z.object({\n  title: z.string().min(1, 'العنوان مطلوب').optional(),\n  description: z.string().optional(),\n  location: z.string().optional(),\n  startDate: z.string().optional(),\n  endDate: z.string().optional(),\n})\n\n// GET - جلب مجلد محدد\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const session = await getServerSession(authOptions)\n    if (!session) {\n      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })\n    }\n\n    const folder = await prisma.galleryFolder.findUnique({\n      where: { id: params.id },\n      include: {\n        creator: {\n          select: {\n            name: true,\n          },\n        },\n        photos: {\n          include: {\n            uploader: {\n              select: {\n                name: true,\n              },\n            },\n          },\n          orderBy: { createdAt: 'desc' },\n        },\n        _count: {\n          select: {\n            photos: true,\n          },\n        },\n      },\n    })\n\n    if (!folder) {\n      return NextResponse.json({ error: 'المجلد غير موجود' }, { status: 404 })\n    }\n\n    return NextResponse.json(folder)\n  } catch (error) {\n    console.error('خطأ في جلب المجلد:', error)\n    return NextResponse.json(\n      { error: 'حدث خطأ في جلب المجلد' },\n      { status: 500 }\n    )\n  }\n}\n\n// PUT - تحديث مجلد\nexport async function PUT(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const session = await getServerSession(authOptions)\n    if (!session) {\n      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })\n    }\n\n    // التحقق من الصلاحيات\n    if (session.user.role === 'VIEWER') {\n      return NextResponse.json({ error: 'ليس لديك صلاحية لتعديل المجلدات' }, { status: 403 })\n    }\n\n    const body = await request.json()\n    \n    // التحقق من صحة البيانات\n    const validatedData = updateGalleryFolderSchema.parse(body)\n\n    // التحقق من وجود المجلد\n    const existingFolder = await prisma.galleryFolder.findUnique({\n      where: { id: params.id },\n    })\n\n    if (!existingFolder) {\n      return NextResponse.json({ error: 'المجلد غير موجود' }, { status: 404 })\n    }\n\n    // تحضير البيانات للتحديث\n    const updateData: Record<string, unknown> = {}\n\n    if (validatedData.title !== undefined) {\n      updateData.title = validatedData.title\n    }\n\n    if (validatedData.description !== undefined) {\n      updateData.description = validatedData.description\n    }\n\n    if (validatedData.location !== undefined) {\n      updateData.location = validatedData.location\n    }\n\n    if (validatedData.startDate !== undefined) {\n      updateData.startDate = validatedData.startDate ? new Date(validatedData.startDate) : null\n    }\n\n    if (validatedData.endDate !== undefined) {\n      updateData.endDate = validatedData.endDate ? new Date(validatedData.endDate) : null\n    }\n\n    // تحديث المجلد\n    const updatedFolder = await prisma.galleryFolder.update({\n      where: { id: params.id },\n      data: updateData,\n      include: {\n        creator: {\n          select: {\n            name: true,\n          },\n        },\n        _count: {\n          select: {\n            photos: true,\n          },\n        },\n      },\n    })\n\n    return NextResponse.json(updatedFolder)\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { error: 'بيانات غير صحيحة', details: error.errors },\n        { status: 400 }\n      )\n    }\n\n    console.error('خطأ في تحديث المجلد:', error)\n    return NextResponse.json(\n      { error: 'حدث خطأ في تحديث المجلد' },\n      { status: 500 }\n    )\n  }\n}\n\n// DELETE - حذف مجلد\nexport async function DELETE(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const session = await getServerSession(authOptions)\n    if (!session) {\n      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })\n    }\n\n    // التحقق من الصلاحيات\n    if (session.user.role !== 'ADMIN') {\n      return NextResponse.json({ error: 'ليس لديك صلاحية لحذف المجلدات' }, { status: 403 })\n    }\n\n    // التحقق من وجود المجلد\n    const existingFolder = await prisma.galleryFolder.findUnique({\n      where: { id: params.id },\n      include: {\n        _count: {\n          select: {\n            photos: true,\n          },\n        },\n      },\n    })\n\n    if (!existingFolder) {\n      return NextResponse.json({ error: 'المجلد غير موجود' }, { status: 404 })\n    }\n\n    // التحقق من وجود صور في المجلد\n    if (existingFolder._count.photos > 0) {\n      return NextResponse.json(\n        { error: 'لا يمكن حذف المجلد لأنه يحتوي على صور. يرجى حذف الصور أولاً أو نقلها إلى مجلد آخر.' },\n        { status: 400 }\n      )\n    }\n\n    // حذف المجلد\n    await prisma.galleryFolder.delete({\n      where: { id: params.id },\n    })\n\n    return NextResponse.json({ message: 'تم حذف المجلد بنجاح' })\n  } catch (error) {\n    console.error('خطأ في حذف المجلد:', error)\n    return NextResponse.json(\n      { error: 'حدث خطأ في حذف المجلد' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAAA;;;;;;AAEA,gCAAgC;AAChC,MAAM,4BAA4B,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,iBAAiB,QAAQ;IAClD,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC9B;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAW,GAAG;gBAAE,QAAQ;YAAI;QAChE;QAEA,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,UAAU,CAAC;YACnD,OAAO;gBAAE,IAAI,OAAO,EAAE;YAAC;YACvB,SAAS;gBACP,SAAS;oBACP,QAAQ;wBACN,MAAM;oBACR;gBACF;gBACA,QAAQ;oBACN,SAAS;wBACP,UAAU;4BACR,QAAQ;gCACN,MAAM;4BACR;wBACF;oBACF;oBACA,SAAS;wBAAE,WAAW;oBAAO;gBAC/B;gBACA,QAAQ;oBACN,QAAQ;wBACN,QAAQ;oBACV;gBACF;YACF;QACF;QAEA,IAAI,CAAC,QAAQ;YACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmB,GAAG;gBAAE,QAAQ;YAAI;QACxE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAW,GAAG;gBAAE,QAAQ;YAAI;QAChE;QAEA,sBAAsB;QACtB,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,UAAU;YAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAkC,GAAG;gBAAE,QAAQ;YAAI;QACvF;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,yBAAyB;QACzB,MAAM,gBAAgB,0BAA0B,KAAK,CAAC;QAEtD,wBAAwB;QACxB,MAAM,iBAAiB,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,UAAU,CAAC;YAC3D,OAAO;gBAAE,IAAI,OAAO,EAAE;YAAC;QACzB;QAEA,IAAI,CAAC,gBAAgB;YACnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmB,GAAG;gBAAE,QAAQ;YAAI;QACxE;QAEA,yBAAyB;QACzB,MAAM,aAAsC,CAAC;QAE7C,IAAI,cAAc,KAAK,KAAK,WAAW;YACrC,WAAW,KAAK,GAAG,cAAc,KAAK;QACxC;QAEA,IAAI,cAAc,WAAW,KAAK,WAAW;YAC3C,WAAW,WAAW,GAAG,cAAc,WAAW;QACpD;QAEA,IAAI,cAAc,QAAQ,KAAK,WAAW;YACxC,WAAW,QAAQ,GAAG,cAAc,QAAQ;QAC9C;QAEA,IAAI,cAAc,SAAS,KAAK,WAAW;YACzC,WAAW,SAAS,GAAG,cAAc,SAAS,GAAG,IAAI,KAAK,cAAc,SAAS,IAAI;QACvF;QAEA,IAAI,cAAc,OAAO,KAAK,WAAW;YACvC,WAAW,OAAO,GAAG,cAAc,OAAO,GAAG,IAAI,KAAK,cAAc,OAAO,IAAI;QACjF;QAEA,eAAe;QACf,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YACtD,OAAO;gBAAE,IAAI,OAAO,EAAE;YAAC;YACvB,MAAM;YACN,SAAS;gBACP,SAAS;oBACP,QAAQ;wBACN,MAAM;oBACR;gBACF;gBACA,QAAQ;oBACN,QAAQ;wBACN,QAAQ;oBACV;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,mLAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAoB,SAAS,MAAM,MAAM;YAAC,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA0B,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAW,GAAG;gBAAE,QAAQ;YAAI;QAChE;QAEA,sBAAsB;QACtB,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAgC,GAAG;gBAAE,QAAQ;YAAI;QACrF;QAEA,wBAAwB;QACxB,MAAM,iBAAiB,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,UAAU,CAAC;YAC3D,OAAO;gBAAE,IAAI,OAAO,EAAE;YAAC;YACvB,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,QAAQ;oBACV;gBACF;YACF;QACF;QAEA,IAAI,CAAC,gBAAgB;YACnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmB,GAAG;gBAAE,QAAQ;YAAI;QACxE;QAEA,+BAA+B;QAC/B,IAAI,eAAe,MAAM,CAAC,MAAM,GAAG,GAAG;YACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqF,GAC9F;gBAAE,QAAQ;YAAI;QAElB;QAEA,aAAa;QACb,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YAChC,OAAO;gBAAE,IAAI,OAAO,EAAE;YAAC;QACzB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAsB;IAC5D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}