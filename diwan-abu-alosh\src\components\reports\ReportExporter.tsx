'use client'

import { formatCurrency, formatDate } from '@/lib/utils'

// دالة تصدير PDF متقدمة
export const exportToPDF = async (data: any, reportType: string) => {
  try {
    const html2pdf = (await import('html2pdf.js')).default as any

    let htmlContent = ''

    switch (reportType) {
      case 'members-list':
        htmlContent = generateMembersListHTML(data)
        break
      case 'member-detail':
        htmlContent = generateMemberDetailHTML(data)
        break
      case 'member-statement':
        htmlContent = generateMemberStatementHTML(data)
        break
      case 'incomes-detailed':
        htmlContent = generateIncomesDetailedHTML(data)
        break
      case 'incomes-summary':
        htmlContent = generateIncomesSummaryHTML(data)
        break
      case 'incomes-by-type':
        htmlContent = generateIncomesByTypeHTML(data)
        break
      case 'incomes-by-member':
        htmlContent = generateIncomesByMemberHTML(data)
        break
      case 'expenses-detailed':
        htmlContent = generateExpensesDetailedHTML(data)
        break
      case 'expenses-summary':
        htmlContent = generateExpensesSummaryHTML(data)
        break
      case 'expenses-by-category':
        htmlContent = generateExpensesByCategoryHTML(data)
        break
      case 'expenses-by-recipient':
        htmlContent = generateExpensesByRecipientHTML(data)
        break
      case 'general-summary':
        htmlContent = generateGeneralSummaryHTML(data)
        break
      case 'comparison-report':
        htmlContent = generateComparisonReportHTML(data)
        break
      case 'late-members':
        htmlContent = generateLateMembersHTML(data)
        break
      case 'emergency-report':
        htmlContent = generateEmergencyReportHTML(data)
        break
      default:
        throw new Error('نوع التقرير غير مدعوم')
    }

    const element = document.createElement('div')
    element.innerHTML = htmlContent

    const opt = {
      margin: 0.5,
      filename: `${data.title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`,
      image: { type: 'jpeg', quality: 0.98 },
      html2canvas: { scale: 2, useCORS: true },
      jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }
    }

    await html2pdf().set(opt).from(element).save()

  } catch (error) {
    console.error('خطأ في تصدير PDF:', error)
    alert('حدث خطأ في تصدير التقرير')
  }
}

// دالة تصدير CSV
export const exportToCSV = (data: any, reportType: string) => {
  try {
    let csvContent = ''

    switch (reportType) {
      case 'members-list':
        csvContent = generateMembersListCSV(data)
        break
      case 'member-detail':
        csvContent = generateMemberDetailCSV(data)
        break
      case 'member-statement':
        csvContent = generateMemberStatementCSV(data)
        break
      case 'incomes-detailed':
        csvContent = generateIncomesDetailedCSV(data)
        break
      case 'incomes-summary':
        csvContent = generateIncomesSummaryCSV(data)
        break
      case 'incomes-by-type':
        csvContent = generateIncomesByTypeCSV(data)
        break
      case 'incomes-by-member':
        csvContent = generateIncomesByMemberCSV(data)
        break
      case 'expenses-detailed':
        csvContent = generateExpensesDetailedCSV(data)
        break
      case 'expenses-summary':
        csvContent = generateExpensesSummaryCSV(data)
        break
      case 'expenses-by-category':
        csvContent = generateExpensesByCategoryCSV(data)
        break
      case 'expenses-by-recipient':
        csvContent = generateExpensesByRecipientCSV(data)
        break
      case 'general-summary':
        csvContent = generateGeneralSummaryCSV(data)
        break
      case 'comparison-report':
        csvContent = generateComparisonReportCSV(data)
        break
      case 'late-members':
        csvContent = generateLateMembersCSV(data)
        break
      case 'emergency-report':
        csvContent = generateEmergencyReportCSV(data)
        break
      default:
        throw new Error('نوع التقرير غير مدعوم')
    }

    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `${data.title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.csv`
    link.click()

  } catch (error) {
    console.error('خطأ في تصدير CSV:', error)
    alert('حدث خطأ في تصدير التقرير')
  }
}

// دالة إنشاء HTML أساسية
const generateBaseHTML = (title: string, content: string) => {
  return `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>${title}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: white;
            padding: 20px;
            direction: rtl;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 20px;
        }

        .header h1 {
            color: #2563eb;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header .subtitle {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .section {
            margin-bottom: 30px;
            page-break-inside: avoid;
        }

        .section h2 {
            color: #2563eb;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 5px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .table th,
        .table td {
            padding: 8px 12px;
            text-align: right;
            border-bottom: 1px solid #e2e8f0;
            font-size: 12px;
        }

        .table th {
            background: #f1f5f9;
            font-weight: 600;
            color: #475569;
        }

        .income {
            color: #059669;
            font-weight: 600;
        }

        .expense {
            color: #dc2626;
            font-weight: 600;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 30px;
        }

        .summary-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }

        .summary-card h3 {
            color: #475569;
            font-size: 12px;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .summary-card .value {
            font-size: 18px;
            font-weight: 700;
            color: #1e293b;
        }

        @media print {
            body { padding: 0; }
            .section { page-break-inside: avoid; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>${title}</h1>
        <div class="subtitle">ديوان أبو علوش</div>
        <div class="subtitle">تاريخ الإنشاء: ${new Date().toLocaleDateString('ar-JO')}</div>
    </div>
    ${content}
</body>
</html>`
}

// دوال إنشاء HTML للتقارير المختلفة
const generateMembersListHTML = (data: any) => {
  const content = `
    <div class="section">
        <h2>قائمة الأعضاء</h2>
        <table class="table">
            <thead>
                <tr>
                    <th>رقم العضو</th>
                    <th>الاسم الكامل</th>
                    <th>رقم الهاتف</th>
                    <th>تاريخ الانضمام</th>
                    <th>الحالة</th>
                    <th>إجمالي المساهمات</th>
                    <th>عدد المساهمات</th>
                </tr>
            </thead>
            <tbody>
                ${data.members.map((member: any) => `
                    <tr>
                        <td>${member.memberNumber}</td>
                        <td>${member.name}</td>
                        <td>${member.phone}</td>
                        <td>${member.joinDate}</td>
                        <td>${member.status}</td>
                        <td class="income">${formatCurrency(member.totalContributions)}</td>
                        <td>${member.contributionsCount}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    </div>
  `
  return generateBaseHTML(data.title, content)
}

const generateMemberDetailHTML = (data: any) => {
  const content = `
    <div class="section">
        <h2>معلومات العضو</h2>
        <div class="summary-grid">
            <div class="summary-card">
                <h3>الاسم</h3>
                <div class="value">${data.member.name}</div>
            </div>
            <div class="summary-card">
                <h3>الهاتف</h3>
                <div class="value">${data.member.phone}</div>
            </div>
            <div class="summary-card">
                <h3>إجمالي المساهمات</h3>
                <div class="value income">${formatCurrency(data.member.totalContributions)}</div>
            </div>
            <div class="summary-card">
                <h3>عدد المساهمات</h3>
                <div class="value">${data.member.contributionsCount}</div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>تفاصيل المساهمات</h2>
        <table class="table">
            <thead>
                <tr>
                    <th>التاريخ</th>
                    <th>المبلغ</th>
                    <th>المصدر</th>
                    <th>الوصف</th>
                </tr>
            </thead>
            <tbody>
                ${data.contributions.map((contribution: any) => `
                    <tr>
                        <td>${contribution.date}</td>
                        <td class="income">${formatCurrency(contribution.amount)}</td>
                        <td>${contribution.source}</td>
                        <td>${contribution.description}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    </div>
  `
  return generateBaseHTML(data.title, content)
}

// دوال إنشاء CSV للتقارير المختلفة
const generateMembersListCSV = (data: any) => {
  const headers = ['رقم العضو', 'الاسم الكامل', 'رقم الهاتف', 'تاريخ الانضمام', 'الحالة', 'إجمالي المساهمات', 'عدد المساهمات']
  const rows = data.members.map((member: any) => [
    member.memberNumber,
    member.name,
    member.phone,
    member.joinDate,
    member.status,
    formatCurrency(member.totalContributions),
    member.contributionsCount
  ])

  return [headers, ...rows].map(row => row.join(',')).join('\n')
}

const generateMemberDetailCSV = (data: any) => {
  const memberInfo = [
    ['معلومات العضو'],
    ['الاسم', data.member.name],
    ['الهاتف', data.member.phone],
    ['إجمالي المساهمات', formatCurrency(data.member.totalContributions)],
    ['عدد المساهمات', data.member.contributionsCount],
    [],
    ['تفاصيل المساهمات'],
    ['التاريخ', 'المبلغ', 'المصدر', 'الوصف'],
    ...data.contributions.map((contribution: any) => [
      contribution.date,
      formatCurrency(contribution.amount),
      contribution.source,
      contribution.description
    ])
  ]

  return memberInfo.map(row => Array.isArray(row) ? row.join(',') : row).join('\n')
}

// دوال إضافية لتقارير الإيرادات
const generateIncomesDetailedHTML = (data: any) => {
  const content = `
    <div class="section">
        <h2>ملخص الإيرادات</h2>
        <div class="summary-grid">
            <div class="summary-card">
                <h3>إجمالي الإيرادات</h3>
                <div class="value income">${formatCurrency(data.statistics.total)}</div>
            </div>
            <div class="summary-card">
                <h3>عدد المعاملات</h3>
                <div class="value">${data.statistics.count}</div>
            </div>
            <div class="summary-card">
                <h3>متوسط المبلغ</h3>
                <div class="value">${formatCurrency(data.statistics.average)}</div>
            </div>
            <div class="summary-card">
                <h3>الفترة</h3>
                <div class="value">${data.period}</div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>تفاصيل الإيرادات</h2>
        <table class="table">
            <thead>
                <tr>
                    <th>التاريخ</th>
                    <th>العضو</th>
                    <th>النوع</th>
                    <th>المصدر</th>
                    <th>المبلغ</th>
                    <th>الوصف</th>
                </tr>
            </thead>
            <tbody>
                ${data.incomes.map((income: any) => `
                    <tr>
                        <td>${income.date}</td>
                        <td>${income.memberName}</td>
                        <td>${income.type}</td>
                        <td>${income.source}</td>
                        <td class="income">${formatCurrency(income.amount)}</td>
                        <td>${income.description}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    </div>
  `
  return generateBaseHTML(data.title, content)
}

const generateExpensesDetailedHTML = (data: any) => {
  const content = `
    <div class="section">
        <h2>ملخص المصروفات</h2>
        <div class="summary-grid">
            <div class="summary-card">
                <h3>إجمالي المصروفات</h3>
                <div class="value expense">${formatCurrency(data.statistics.total)}</div>
            </div>
            <div class="summary-card">
                <h3>عدد المعاملات</h3>
                <div class="value">${data.statistics.count}</div>
            </div>
            <div class="summary-card">
                <h3>متوسط المبلغ</h3>
                <div class="value">${formatCurrency(data.statistics.average)}</div>
            </div>
            <div class="summary-card">
                <h3>الفترة</h3>
                <div class="value">${data.period}</div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>تفاصيل المصروفات</h2>
        <table class="table">
            <thead>
                <tr>
                    <th>التاريخ</th>
                    <th>الوصف</th>
                    <th>الفئة</th>
                    <th>المبلغ</th>
                    <th>المستفيد</th>
                    <th>المسؤول</th>
                </tr>
            </thead>
            <tbody>
                ${data.expenses.map((expense: any) => `
                    <tr>
                        <td>${expense.date}</td>
                        <td>${expense.description}</td>
                        <td>${expense.category}</td>
                        <td class="expense">${formatCurrency(expense.amount)}</td>
                        <td>${expense.recipient}</td>
                        <td>${expense.createdBy}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    </div>
  `
  return generateBaseHTML(data.title, content)
}

const generateLateMembersHTML = (data: any) => {
  const content = `
    <div class="section">
        <h2>ملخص الأعضاء المتأخرين</h2>
        <div class="summary-grid">
            <div class="summary-card">
                <h3>عدد الأعضاء المتأخرين</h3>
                <div class="value">${data.summary.totalLateMembers}</div>
            </div>
            <div class="summary-card">
                <h3>إجمالي المبالغ المستحقة</h3>
                <div class="value expense">${formatCurrency(data.summary.totalAmountDue)}</div>
            </div>
            <div class="summary-card">
                <h3>عتبة التأخير</h3>
                <div class="value">${data.threshold}</div>
            </div>
            <div class="summary-card">
                <h3>تاريخ التقرير</h3>
                <div class="value">${data.date}</div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>قائمة الأعضاء المتأخرين</h2>
        <table class="table">
            <thead>
                <tr>
                    <th>الاسم</th>
                    <th>الهاتف</th>
                    <th>الحالة</th>
                    <th>آخر مساهمة</th>
                    <th>أيام التأخير</th>
                    <th>الأشهر المتأخرة</th>
                    <th>المبلغ المستحق</th>
                </tr>
            </thead>
            <tbody>
                ${data.lateMembers.map((member: any) => `
                    <tr>
                        <td>${member.name}</td>
                        <td>${member.phone}</td>
                        <td>${member.status}</td>
                        <td>${member.lastContribution}</td>
                        <td>${member.daysSinceLastPayment}</td>
                        <td>${member.monthsLate}</td>
                        <td class="expense">${formatCurrency(member.amountDue)}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    </div>
  `
  return generateBaseHTML(data.title, content)
}

// دوال CSV للتقارير الإضافية
const generateIncomesDetailedCSV = (data: any) => {
  const headers = ['التاريخ', 'العضو', 'النوع', 'المصدر', 'المبلغ', 'الوصف']
  const rows = data.incomes.map((income: any) => [
    income.date,
    income.memberName,
    income.type,
    income.source,
    formatCurrency(income.amount),
    income.description
  ])

  return [
    [`${data.title} - ${data.date}`],
    [`الفترة: ${data.period}`],
    [`إجمالي الإيرادات: ${formatCurrency(data.statistics.total)}`],
    [`عدد المعاملات: ${data.statistics.count}`],
    [],
    headers,
    ...rows
  ].map(row => Array.isArray(row) ? row.join(',') : row).join('\n')
}

const generateExpensesDetailedCSV = (data: any) => {
  const headers = ['التاريخ', 'الوصف', 'الفئة', 'المبلغ', 'المستفيد', 'المسؤول']
  const rows = data.expenses.map((expense: any) => [
    expense.date,
    expense.description,
    expense.category,
    formatCurrency(expense.amount),
    expense.recipient,
    expense.createdBy
  ])

  return [
    [`${data.title} - ${data.date}`],
    [`الفترة: ${data.period}`],
    [`إجمالي المصروفات: ${formatCurrency(data.statistics.total)}`],
    [`عدد المعاملات: ${data.statistics.count}`],
    [],
    headers,
    ...rows
  ].map(row => Array.isArray(row) ? row.join(',') : row).join('\n')
}

const generateLateMembersCSV = (data: any) => {
  const headers = ['الاسم', 'الهاتف', 'الحالة', 'آخر مساهمة', 'أيام التأخير', 'الأشهر المتأخرة', 'المبلغ المستحق']
  const rows = data.lateMembers.map((member: any) => [
    member.name,
    member.phone,
    member.status,
    member.lastContribution,
    member.daysSinceLastPayment,
    member.monthsLate,
    formatCurrency(member.amountDue)
  ])

  return [
    [`${data.title} - ${data.date}`],
    [`عتبة التأخير: ${data.threshold}`],
    [`عدد الأعضاء المتأخرين: ${data.summary.totalLateMembers}`],
    [`إجمالي المبالغ المستحقة: ${formatCurrency(data.summary.totalAmountDue)}`],
    [],
    headers,
    ...rows
  ].map(row => Array.isArray(row) ? row.join(',') : row).join('\n')
}

// دوال مساعدة للتقارير الأخرى (يمكن إضافة المزيد حسب الحاجة)
const generateMemberStatementHTML = (data: any) => {
  return generateMemberDetailHTML(data) // نفس تنسيق التقرير المفصل
}

const generateMemberStatementCSV = (data: any) => {
  return generateMemberDetailCSV(data) // نفس تنسيق التقرير المفصل
}

const generateIncomesSummaryHTML = (data: any) => {
  const content = `
    <div class="section">
        <h2>ملخص الإيرادات</h2>
        <div class="summary-grid">
            <div class="summary-card">
                <h3>إجمالي المبلغ</h3>
                <div class="value income">${formatCurrency(data.summary.totalAmount)}</div>
            </div>
            <div class="summary-card">
                <h3>عدد المعاملات</h3>
                <div class="value">${data.summary.totalCount}</div>
            </div>
            <div class="summary-card">
                <h3>متوسط المبلغ</h3>
                <div class="value">${formatCurrency(data.summary.averageAmount)}</div>
            </div>
            <div class="summary-card">
                <h3>الفترة</h3>
                <div class="value">${data.period}</div>
            </div>
        </div>
    </div>
  `
  return generateBaseHTML(data.title, content)
}

const generateIncomesSummaryCSV = (data: any) => {
  return [
    [`${data.title} - ${data.date}`],
    [`الفترة: ${data.period}`],
    ['البيان', 'القيمة'],
    ['إجمالي المبلغ', formatCurrency(data.summary.totalAmount)],
    ['عدد المعاملات', data.summary.totalCount],
    ['متوسط المبلغ', formatCurrency(data.summary.averageAmount)]
  ].map(row => Array.isArray(row) ? row.join(',') : row).join('\n')
}

// دوال أساسية للتقارير الأخرى
const generateIncomesByTypeHTML = (data: any) => generateIncomesSummaryHTML(data)
const generateIncomesByMemberHTML = (data: any) => generateIncomesSummaryHTML(data)
const generateExpensesSummaryHTML = (data: any) => generateIncomesSummaryHTML(data)
const generateExpensesByCategoryHTML = (data: any) => generateIncomesSummaryHTML(data)
const generateExpensesByRecipientHTML = (data: any) => generateIncomesSummaryHTML(data)
const generateGeneralSummaryHTML = (data: any) => generateIncomesSummaryHTML(data)
const generateComparisonReportHTML = (data: any) => generateIncomesSummaryHTML(data)
const generateEmergencyReportHTML = (data: any) => generateIncomesSummaryHTML(data)

const generateIncomesByTypeCSV = (data: any) => generateIncomesSummaryCSV(data)
const generateIncomesByMemberCSV = (data: any) => generateIncomesSummaryCSV(data)
const generateExpensesSummaryCSV = (data: any) => generateIncomesSummaryCSV(data)
const generateExpensesByCategoryCSV = (data: any) => generateIncomesSummaryCSV(data)
const generateExpensesByRecipientCSV = (data: any) => generateIncomesSummaryCSV(data)
const generateGeneralSummaryCSV = (data: any) => generateIncomesSummaryCSV(data)
const generateComparisonReportCSV = (data: any) => generateIncomesSummaryCSV(data)
const generateEmergencyReportCSV = (data: any) => generateIncomesSummaryCSV(data)