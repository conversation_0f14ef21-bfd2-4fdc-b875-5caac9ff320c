.gallery-image{transition:all .3s cubic-bezier(.4,0,.2,1)}.gallery-image:hover{transform:scale(1.02);box-shadow:0 20px 25px -5px rgba(0,0,0,.1),0 10px 10px -5px rgba(0,0,0,.04)}.loading-shimmer{background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0 50%,#f0f0f0 75%);background-size:200% 100%;animation:shimmer 1.5s infinite}@keyframes shimmer{0%{background-position:-200% 0}to{background-position:200% 0}}.line-clamp-2{-webkit-line-clamp:2}.line-clamp-2,.line-clamp-3{display:-webkit-box;-webkit-box-orient:vertical;overflow:hidden}.line-clamp-3{-webkit-line-clamp:3}.gallery-button{transition:all .3s cubic-bezier(.4,0,.2,1);position:relative;overflow:hidden}.gallery-button:hover{transform:translateY(-2px);box-shadow:0 8px 25px rgba(0,0,0,.2)}.gallery-button:before{content:"";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s}.gallery-button:hover:before{left:100%}.drop-zone{transition:all .3s ease;position:relative;overflow:hidden}.drop-zone:before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(45deg,transparent 30%,rgba(59,130,246,.1) 50%,transparent 70%);transform:translateX(-100%);transition:transform .6s ease}.drop-zone:hover:before{transform:translateX(100%)}.gallery-card{transition:all .3s cubic-bezier(.4,0,.2,1);position:relative;overflow:hidden}.gallery-card:before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(135deg,rgba(255,255,255,.1),rgba(255,255,255,0));opacity:0;transition:opacity .3s ease}.gallery-card:hover:before{opacity:1}.gallery-card:hover{transform:translateY(-4px);box-shadow:0 25px 50px -12px rgba(0,0,0,.25)}.gallery-grid{display:grid;gap:1.5rem;grid-template-columns:repeat(auto-fill,minmax(280px,1fr))}@media (max-width:640px){.gallery-grid{grid-template-columns:repeat(auto-fill,minmax(250px,1fr));gap:1rem}}@media (max-width:480px){.gallery-grid{grid-template-columns:1fr;gap:.75rem}}.image-viewer{-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);background:rgba(0,0,0,.8)}.image-viewer img{max-height:80vh;max-width:90vw;-o-object-fit:contain;object-fit:contain;border-radius:8px;box-shadow:0 25px 50px -12px rgba(0,0,0,.5)}.stats-card{background:linear-gradient(135deg,var(--gradient-from) 0,var(--gradient-to) 100%);border:none;position:relative;overflow:hidden}.stats-card:before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(135deg,rgba(255,255,255,.1),rgba(255,255,255,.05));opacity:0;transition:opacity .3s ease}.stats-card:hover:before{opacity:1}.gradient-header{background:linear-gradient(135deg,#3b82f6,#1d4ed8 50%,#1e40af);position:relative;overflow:hidden}.gradient-header:before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(45deg,transparent 30%,rgba(255,255,255,.1) 50%,transparent 70%);transform:translateX(-100%);animation:shine 3s infinite}@keyframes shine{0%{transform:translateX(-100%)}50%{transform:translateX(100%)}to{transform:translateX(100%)}}.floating-buttons{opacity:0;transform:translateY(15px) scale(.8);transition:all .4s cubic-bezier(.4,0,.2,1)}.gallery-card:hover .floating-buttons{opacity:1;transform:translateY(0) scale(1)}.floating-buttons button{-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);transition:all .3s ease;border:2px solid rgba(255,255,255,.2)}.floating-buttons button:hover{transform:scale(1.1);box-shadow:0 8px 20px rgba(0,0,0,.3)}.floating-buttons .bg-blue-600:hover{box-shadow:0 8px 20px rgba(37,99,235,.4)}.floating-buttons .bg-green-600:hover{box-shadow:0 8px 20px rgba(22,163,74,.4)}.floating-buttons .bg-purple-600:hover{box-shadow:0 8px 20px rgba(147,51,234,.4)}.floating-buttons .bg-red-600:hover{box-shadow:0 8px 20px rgba(220,38,38,.4)}.search-input{transition:all .3s ease}.search-input:focus{transform:scale(1.02);box-shadow:0 10px 25px -5px rgba(59,130,246,.2)}.upload-progress{background:linear-gradient(90deg,#3b82f6,#1d4ed8 50%,#3b82f6);background-size:200% 100%;animation:progress-shine 2s infinite}@keyframes progress-shine{0%{background-position:-200% 0}to{background-position:200% 0}}.badge-glow{box-shadow:0 0 10px rgba(59,130,246,.3);animation:glow 2s infinite alternate}@keyframes glow{0%{box-shadow:0 0 5px rgba(59,130,246,.2)}to{box-shadow:0 0 15px rgba(59,130,246,.4)}}.enhanced-button{font-weight:600;letter-spacing:.025em;text-shadow:0 1px 2px rgba(0,0,0,.1);border:2px solid transparent;background-clip:padding-box}.enhanced-button:hover{letter-spacing:.05em;text-shadow:0 2px 4px rgba(0,0,0,.2)}.high-contrast-text{color:#1f2937;font-weight:600;text-shadow:0 1px 2px rgba(255,255,255,.8)}.high-contrast-text-white{color:#ffffff;font-weight:600;text-shadow:0 1px 3px rgba(0,0,0,.5)}.enhanced-border{border:2px solid rgba(0,0,0,.1);box-shadow:0 2px 8px rgba(0,0,0,.1)}.enhanced-border:hover{border-color:rgba(0,0,0,.2);box-shadow:0 4px 16px rgba(0,0,0,.15)}.enhanced-icon{filter:drop-shadow(0 1px 2px rgba(0,0,0,.1))}.enhanced-icon:hover{filter:drop-shadow(0 2px 4px rgba(0,0,0,.2));transform:scale(1.05)}