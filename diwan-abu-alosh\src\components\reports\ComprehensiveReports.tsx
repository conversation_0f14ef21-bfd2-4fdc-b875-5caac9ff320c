'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
// import { Input } from '@/components/ui/input'
// import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { formatCurrency, formatDate } from '@/lib/utils'
import MemberSearchDialog from '@/components/members/member-search-dialog'
import {
  BarChart3,
  FileText,
  Download,
  Search,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  Calculator,
  PieChart
} from 'lucide-react'

interface Member {
  id: string
  name: string
  status: string
  incomes: { amount: number; date: string }[]
}

interface ComprehensiveReportsProps {
  onExportPDF: (data: any, type: string) => void
  onExportCSV: (data: any, type: string) => void
}

export default function ComprehensiveReports({ onExportPDF, onExportCSV }: ComprehensiveReportsProps) {
  const [members, setMembers] = useState<Member[]>([])
  const [selectedMember, setSelectedMember] = useState<string>('')
  const [loading, setLoading] = useState(true)
  const [reportData, setReportData] = useState<any>(null)
  const [isMemberSearchOpen, setIsMemberSearchOpen] = useState(false)
  const [selectedMemberName, setSelectedMemberName] = useState<string>('')

  // جلب البيانات الشاملة
  const fetchComprehensiveData = async () => {
    try {
      setLoading(true)
      
      // جلب بيانات التقارير الأساسية
      const [membersRes, reportsRes] = await Promise.all([
        fetch('/api/members?limit=1000'),
        fetch('/api/reports?period=all-time')
      ])

      if (!membersRes.ok || !reportsRes.ok) {
        throw new Error('فشل في جلب البيانات')
      }

      const [membersData, reportsData] = await Promise.all([
        membersRes.json(),
        reportsRes.json()
      ])

      setMembers(membersData.members || [])
      setReportData(reportsData)
    } catch (error) {
      console.error('خطأ في جلب البيانات:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchComprehensiveData()
  }, [])

  // فتح نافذة البحث عن عضو
  const handleOpenMemberSearch = () => {
    setIsMemberSearchOpen(true)
  }

  // اختيار عضو من نافذة البحث
  const handleSelectMemberForStatement = (memberId: string) => {
    const member = members.find(m => m.id === memberId)
    setSelectedMember(memberId)
    setSelectedMemberName(member?.name || '')
    setIsMemberSearchOpen(false)
  }

  // حساب كشف حساب العضو
  const getMemberAccountStatement = (memberId: string) => {
    const member = members.find(m => m.id === memberId)
    if (!member) return null

    const totalContributions = member.incomes.reduce((sum, income) => sum + income.amount, 0)
    const contributionsCount = member.incomes.length

    // حساب حصة المصاريف (افتراضياً نقسم المصاريف على جميع الأعضاء النشطين)
    const activeMembers = members.filter(m => m.status === 'ACTIVE').length
    const memberExpenseShare = activeMembers > 0 ? (reportData?.summary?.totalExpenses || 0) / activeMembers : 0

    const netBalance = totalContributions - memberExpenseShare

    return {
      member,
      totalContributions,
      contributionsCount,
      memberExpenseShare,
      netBalance,
      status: netBalance >= 0 ? 'دائن' : 'مدين'
    }
  }

  // تصدير كشف حساب العضو
  const handleExportMemberStatement = (memberId: string, format: 'pdf' | 'csv') => {
    const statement = getMemberAccountStatement(memberId)
    if (!statement) return

    const reportData = {
      title: `كشف حساب العضو: ${statement.member.name}`,
      date: new Date().toLocaleDateString('ar-JO'),
      member: statement.member,
      statement: {
        totalContributions: statement.totalContributions,
        contributionsCount: statement.contributionsCount,
        memberExpenseShare: statement.memberExpenseShare,
        netBalance: statement.netBalance,
        status: statement.status
      },
      contributions: statement.member.incomes.map(income => ({
        date: formatDate(income.date),
        amount: income.amount
      }))
    }

    if (format === 'pdf') {
      onExportPDF(reportData, 'member-statement')
    } else {
      onExportCSV(reportData, 'member-statement')
    }
  }

  // تصدير ملخص الحسابات العامة
  const handleExportGeneralSummary = (format: 'pdf' | 'csv') => {
    if (!reportData) return

    const coverageRatio = reportData.summary.totalIncomes > 0 
      ? (reportData.summary.totalIncomes / (reportData.summary.totalIncomes + Math.abs(reportData.summary.balance))) * 100
      : 0

    const summaryData = {
      title: 'ملخص الحسابات العامة',
      date: new Date().toLocaleDateString('ar-JO'),
      summary: {
        totalMembers: reportData.summary.totalMembers,
        activeMembers: reportData.summary.activeMembers,
        totalIncomes: reportData.summary.totalIncomes,
        totalExpenses: reportData.summary.totalExpenses,
        currentBalance: reportData.summary.balance,
        coverageRatio: coverageRatio.toFixed(1),
        totalActivities: reportData.summary.totalActivities
      },
      monthlyData: reportData.monthlyData || []
    }

    if (format === 'pdf') {
      onExportPDF(summaryData, 'general-summary')
    } else {
      onExportCSV(summaryData, 'general-summary')
    }
  }

  // تصدير تقرير المقارنة
  const handleExportComparison = (format: 'pdf' | 'csv') => {
    if (!reportData) return

    const monthlyComparison = (reportData.monthlyData || []).map((month: any) => ({
      month: new Date(month.month + '-01').toLocaleDateString('ar-JO', { year: 'numeric', month: 'long' }),
      incomes: month.incomes,
      expenses: month.expenses,
      balance: month.balance,
      growth: month.incomes > month.expenses ? 'نمو' : 'تراجع',
      efficiency: month.expenses > 0 ? ((month.incomes / month.expenses) * 100).toFixed(1) + '%' : 'غير محدد'
    }))

    const comparisonData = {
      title: 'تقرير مقارنة الإيرادات والمصروفات',
      date: new Date().toLocaleDateString('ar-JO'),
      summary: {
        totalIncomes: reportData.summary.totalIncomes,
        totalExpenses: reportData.summary.totalExpenses,
        netResult: reportData.summary.balance,
        averageMonthlyIncome: reportData.monthlyData.length > 0 
          ? reportData.summary.totalIncomes / reportData.monthlyData.length 
          : 0,
        averageMonthlyExpense: reportData.monthlyData.length > 0 
          ? reportData.summary.totalExpenses / reportData.monthlyData.length 
          : 0
      },
      monthlyComparison,
      insights: [
        reportData.summary.balance >= 0 ? 'الوضع المالي مستقر' : 'يحتاج مراجعة الإنفاق',
        reportData.summary.totalIncomes > reportData.summary.totalExpenses ? 'الإيرادات تفوق المصروفات' : 'المصروفات تفوق الإيرادات',
        `معدل الكفاءة المالية: ${reportData.summary.totalExpenses > 0 ? ((reportData.summary.totalIncomes / reportData.summary.totalExpenses) * 100).toFixed(1) : 'غير محدد'}%`
      ]
    }

    if (format === 'pdf') {
      onExportPDF(comparisonData, 'comparison-report')
    } else {
      onExportCSV(comparisonData, 'comparison-report')
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-gray-500">جاري تحميل البيانات الشاملة...</div>
      </div>
    )
  }

  if (!reportData) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-gray-500">لا توجد بيانات متاحة</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* عنوان القسم */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-bold text-gray-900 flex items-center">
            <BarChart3 className="w-5 h-5 ml-2" />
            التقارير الشاملة
          </h2>
          <p className="text-gray-600">تقارير متقدمة وكشوف حسابات شاملة</p>
        </div>
      </div>

      {/* الإحصائيات العامة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <TrendingUp className="w-8 h-8 text-green-500" />
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي الإيرادات</p>
                <p className="text-2xl font-bold text-green-600">
                  {formatCurrency(reportData.summary.totalIncomes)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <TrendingDown className="w-8 h-8 text-red-500" />
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي المصروفات</p>
                <p className="text-2xl font-bold text-red-600">
                  {formatCurrency(reportData.summary.totalExpenses)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <DollarSign className={`w-8 h-8 ${reportData.summary.balance >= 0 ? 'text-green-500' : 'text-red-500'}`} />
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">الرصيد الحالي</p>
                <p className={`text-2xl font-bold ${reportData.summary.balance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatCurrency(reportData.summary.balance)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <Users className="w-8 h-8 text-blue-500" />
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">الأعضاء النشطون</p>
                <p className="text-2xl font-bold text-blue-600">
                  {reportData.summary.activeMembers} / {reportData.summary.totalMembers}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* كشف حساب العضو */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calculator className="w-5 h-5 ml-2" />
            كشف حساب العضو
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">اختر العضو</label>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={handleOpenMemberSearch}
                    className="flex-1 justify-start text-gray-500"
                  >
                    <Search className="w-4 h-4 ml-2" />
                    {selectedMemberName || "ابحث عن عضو لعرض كشف حسابه"}
                  </Button>
                  {selectedMember && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSelectedMember('')
                        setSelectedMemberName('')
                      }}
                      className="text-gray-500 hover:text-red-600"
                    >
                      ✕
                    </Button>
                  )}
                </div>
              </div>
              
              {selectedMember && (
                <div className="flex items-end space-x-2 space-x-reverse">
                  <Button
                    onClick={() => handleExportMemberStatement(selectedMember, 'pdf')}
                    className="bg-red-600 hover:bg-red-700 text-white"
                  >
                    <FileText className="w-4 h-4 ml-2" />
                    تصدير PDF
                  </Button>
                  <Button
                    onClick={() => handleExportMemberStatement(selectedMember, 'csv')}
                    className="bg-green-600 hover:bg-green-700 text-white"
                  >
                    <Download className="w-4 h-4 ml-2" />
                    تصدير CSV
                  </Button>
                </div>
              )}
            </div>

            {/* عرض كشف الحساب */}
            {selectedMember && (() => {
              const statement = getMemberAccountStatement(selectedMember)
              if (!statement) return null

              return (
                <div className="mt-6 p-4 border border-gray-200 rounded-lg bg-gray-50">
                  <h3 className="font-medium text-gray-900 mb-4">كشف حساب: {statement.member.name}</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-600">إجمالي المساهمات:</span>
                        <span className="font-medium text-green-600">
                          {formatCurrency(statement.totalContributions)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">عدد المساهمات:</span>
                        <span className="font-medium">{statement.contributionsCount}</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-600">حصة المصاريف:</span>
                        <span className="font-medium text-red-600">
                          {formatCurrency(statement.memberExpenseShare)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">الرصيد الصافي:</span>
                        <span className={`font-bold ${statement.netBalance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {formatCurrency(statement.netBalance)} ({statement.status})
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })()}
          </div>
        </CardContent>
      </Card>

      {/* التقارير الشاملة الأخرى */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* ملخص الحسابات العامة */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <PieChart className="w-5 h-5 ml-2" />
              ملخص الحسابات العامة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">نسبة التغطية المالية:</span>
                  <span className="font-medium">
                    {reportData.summary.totalIncomes > 0 
                      ? ((reportData.summary.totalIncomes / (reportData.summary.totalIncomes + Math.abs(reportData.summary.balance))) * 100).toFixed(1)
                      : '0'
                    }%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">معدل الإنفاق:</span>
                  <span className="font-medium">
                    {reportData.summary.totalIncomes > 0 
                      ? ((reportData.summary.totalExpenses / reportData.summary.totalIncomes) * 100).toFixed(1)
                      : '0'
                    }%
                  </span>
                </div>
              </div>
              
              <div className="flex space-x-2 space-x-reverse">
                <Button
                  size="sm"
                  onClick={() => handleExportGeneralSummary('pdf')}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  <FileText className="w-4 h-4 ml-2" />
                  PDF
                </Button>
                <Button
                  size="sm"
                  onClick={() => handleExportGeneralSummary('csv')}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  <Download className="w-4 h-4 ml-2" />
                  CSV
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* تقرير المقارنة */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="w-5 h-5 ml-2" />
              تقرير المقارنة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">الاتجاه العام:</span>
                  <Badge className={reportData.summary.balance >= 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                    {reportData.summary.balance >= 0 ? 'إيجابي' : 'سلبي'}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">كفاءة الإنفاق:</span>
                  <span className="font-medium">
                    {reportData.summary.totalExpenses > 0 
                      ? ((reportData.summary.totalIncomes / reportData.summary.totalExpenses) * 100).toFixed(1)
                      : 'غير محدد'
                    }%
                  </span>
                </div>
              </div>
              
              <div className="flex space-x-2 space-x-reverse">
                <Button
                  size="sm"
                  onClick={() => handleExportComparison('pdf')}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  <FileText className="w-4 h-4 ml-2" />
                  PDF
                </Button>
                <Button
                  size="sm"
                  onClick={() => handleExportComparison('csv')}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  <Download className="w-4 h-4 ml-2" />
                  CSV
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* نافذة البحث عن عضو */}
      <MemberSearchDialog
        open={isMemberSearchOpen}
        onOpenChange={setIsMemberSearchOpen}
        onSelectMember={handleSelectMemberForStatement}
        title="اختيار عضو لكشف الحساب"
        description="ابحث عن العضو المطلوب لعرض كشف حسابه في التقارير الشاملة"
      />
    </div>
  )
}
