(()=>{var e={};e.id=2026,e.ids=[2026],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>i});var s=t(96330);let i=globalThis.prisma??new s.PrismaClient},43084:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>l,serverHooks:()=>h,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>b});var s={};t.r(s),t.d(s,{POST:()=>d});var i=t(96559),n=t(48088),a=t(37719),o=t(32190),u=t(31183),m=t(85663),p=t(43205),c=t.n(p);async function d(e){try{let{email:r,password:t}=await e.json();if(!r||!t)return o.NextResponse.json({error:"البريد الإلكتروني وكلمة المرور مطلوبان"},{status:400});let s=await u.z.user.findUnique({where:{email:r},include:{memberUser:{include:{member:!0}}}});if(!s||!s.memberUser||!await m.Ay.compare(t,s.password))return o.NextResponse.json({error:"البريد الإلكتروني أو كلمة المرور غير صحيحة"},{status:401});if(!s.memberUser.isActive)return o.NextResponse.json({error:"حسابك غير مفعل. يرجى التواصل مع الإدارة"},{status:403});let i=c().sign({userId:s.id,memberId:s.memberUser.memberId,role:"MEMBER"},process.env.NEXTAUTH_SECRET||"fallback-secret",{expiresIn:"24h"}),n=o.NextResponse.json({success:!0,user:{id:s.id,email:s.email,name:s.name,member:s.memberUser.member,permissions:{canViewAccountStatement:s.memberUser.canViewAccountStatement,canViewGallery:s.memberUser.canViewGallery}}});return n.cookies.set("member-token",i,{httpOnly:!0,secure:!0,sameSite:"lax",maxAge:86400}),n}catch(e){return console.error("خطأ في تسجيل دخول العضو:",e),o.NextResponse.json({error:"حدث خطأ في تسجيل الدخول"},{status:500})}}let l=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/member/signin/route",pathname:"/api/auth/member/signin",filename:"route",bundlePath:"app/api/auth/member/signin/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\member\\signin\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:b,serverHooks:h}=l;function w(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:b})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,5663,580,3205],()=>t(43084));module.exports=s})();