'use client'

import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Download,
  Filter,
  TrendingDown,
  DollarSign,
  Receipt,
  Calendar
} from 'lucide-react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { formatCurrency, formatDate, getExpenseCategoryText } from '@/lib/utils'
import ExpenseDialog from '@/components/expenses/expense-dialog'
import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'

interface Expense {
  id: string
  amount: number
  date: string
  description: string
  category: string
  recipient?: string
  notes?: string
  createdBy: {
    name: string
  }
}

interface ExpensesResponse {
  expenses: Expense[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

export default function ExpensesPage() {
  const { data: session } = useSession()
  const [expenses, setExpenses] = useState<Expense[]>([])
  const [loading, setLoading] = useState(true)
  const [search, setSearch] = useState('')
  const [category, setCategory] = useState('all')
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0,
  })
  const [stats, setStats] = useState({
    totalAmount: 0,
    totalCount: 0,
    byCategory: [] as Array<{ category: string; _sum: { amount: number }; _count: number }>
  })
  const [isExpenseDialogOpen, setIsExpenseDialogOpen] = useState(false)
  const [editingExpense, setEditingExpense] = useState<Expense | null>(null)

  // جلب المصروفات
  const fetchExpenses = useCallback(async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        search,
        category,
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
      })

      const response = await fetch(`/api/expenses?${params}`)
      if (!response.ok) throw new Error('فشل في جلب المصروفات')

      const data: ExpensesResponse = await response.json()
      setExpenses(data.expenses)
      setPagination(data.pagination)
    } catch (error) {
      console.error('خطأ في جلب المصروفات:', error)
    } finally {
      setLoading(false)
    }
  }, [search, category, pagination.page, pagination.limit])

  // جلب الإحصائيات
  const fetchStats = async () => {
    try {
      const response = await fetch('/api/expenses?limit=1000')
      if (!response.ok) return

      const data: ExpensesResponse = await response.json()
      const totalAmount = data.expenses.reduce((sum, expense) => sum + expense.amount, 0)
      const totalCount = data.expenses.length

      // حساب الإحصائيات حسب الفئة
      const byCategory = data.expenses.reduce((acc: Array<{ category: string; _sum: { amount: number }; _count: number }>, expense) => {
        const existing = acc.find((item) => item.category === expense.category)
        if (existing) {
          existing._sum.amount += expense.amount
          existing._count += 1
        } else {
          acc.push({
            category: expense.category,
            _sum: { amount: expense.amount },
            _count: 1
          })
        }
        return acc
      }, [])

      setStats({ totalAmount, totalCount, byCategory })
    } catch (error) {
      console.error('خطأ في جلب الإحصائيات:', error)
    }
  }

  useEffect(() => {
    fetchExpenses()
  }, [fetchExpenses])

  useEffect(() => {
    fetchStats()
  }, [])

  // تعديل مصروف
  const handleEditExpense = (expense: Expense) => {
    setEditingExpense(expense)
    setIsExpenseDialogOpen(true)
  }

  // حذف مصروف
  const handleDeleteExpense = async (id: string) => {
    if (!confirm('هل أنت متأكد من حذف هذا المصروف؟\n\nهذا الإجراء لا يمكن التراجع عنه.')) return

    try {
      const response = await fetch(`/api/expenses/${id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        alert(error.error || 'فشل في حذف المصروف')
        return
      }

      alert('تم حذف المصروف بنجاح')
      fetchExpenses()
      fetchStats()
    } catch (error) {
      console.error('خطأ في حذف المصروف:', error)
      alert('حدث خطأ في حذف المصروف')
    }
  }

  // إضافة مصروف جديد
  const handleAddNewExpense = () => {
    setEditingExpense(null)
    setIsExpenseDialogOpen(true)
  }

  // إغلاق نافذة التعديل
  const handleCloseDialog = () => {
    setIsExpenseDialogOpen(false)
    setEditingExpense(null)
  }



  // تصدير البيانات PDF باللغة العربية
  const handleExportExpensesPDF = () => {
    // إنشاء عنصر HTML مؤقت للطباعة
    const printElement = document.createElement('div')
    printElement.style.cssText = `
      position: absolute;
      top: -9999px;
      left: -9999px;
      width: 210mm;
      background: white;
      padding: 20px;
      font-family: Arial, sans-serif;
      direction: rtl;
      text-align: right;
      color: #000;
      font-size: 12px;
    `

    // محتوى التقرير باللغة العربية
    printElement.innerHTML = `
      <div style="text-align: center; margin-bottom: 30px; border-bottom: 3px solid #dc2626; padding-bottom: 15px;">
        <h1 style="color: #dc2626; font-size: 24px; margin: 0; font-weight: bold;">ديوان أبو علوش</h1>
        <h2 style="color: #666; font-size: 18px; margin: 8px 0; font-weight: normal;">تقرير المصروفات</h2>
        <p style="color: #888; font-size: 12px; margin: 5px 0;">تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
      </div>

      <div style="margin-bottom: 20px; background: #f8f9fa; padding: 15px; border-radius: 8px; border-right: 4px solid #dc2626;">
        <h3 style="color: #dc2626; font-size: 16px; margin: 0 0 15px 0; font-weight: bold;">ملخص المصروفات</h3>
        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;">
          <div style="text-align: center; background: white; padding: 10px; border-radius: 6px; border: 1px solid #e5e7eb;">
            <div style="color: #dc2626; font-size: 20px; font-weight: bold;">${stats.totalAmount.toFixed(2)} د.أ</div>
            <div style="color: #666; font-size: 11px;">إجمالي المصروفات</div>
          </div>
          <div style="text-align: center; background: white; padding: 10px; border-radius: 6px; border: 1px solid #e5e7eb;">
            <div style="color: #dc2626; font-size: 20px; font-weight: bold;">${stats.totalCount}</div>
            <div style="color: #666; font-size: 11px;">عدد المصروفات</div>
          </div>
          <div style="text-align: center; background: white; padding: 10px; border-radius: 6px; border: 1px solid #e5e7eb;">
            <div style="color: #dc2626; font-size: 20px; font-weight: bold;">${(stats.totalCount > 0 ? stats.totalAmount / stats.totalCount : 0).toFixed(2)} د.أ</div>
            <div style="color: #666; font-size: 11px;">متوسط المصروف</div>
          </div>
        </div>
      </div>

      <div style="margin-bottom: 20px;">
        <h3 style="color: #dc2626; font-size: 16px; margin: 0 0 15px 0; font-weight: bold; border-bottom: 2px solid #dc2626; padding-bottom: 8px;">تفاصيل المصروفات</h3>
        <table style="width: 100%; border-collapse: collapse; font-size: 11px; background: white; border-radius: 8px; overflow: hidden;">
          <thead>
            <tr style="background: #dc2626; color: white;">
              <th style="padding: 10px 8px; text-align: right; font-weight: bold;">الوصف</th>
              <th style="padding: 10px 8px; text-align: center; font-weight: bold;">المبلغ (د.أ)</th>
              <th style="padding: 10px 8px; text-align: center; font-weight: bold;">الفئة</th>
              <th style="padding: 10px 8px; text-align: center; font-weight: bold;">المستفيد</th>
              <th style="padding: 10px 8px; text-align: center; font-weight: bold;">التاريخ</th>
            </tr>
          </thead>
          <tbody>
            ${expenses.map((expense, index) => `
              <tr style="background: ${index % 2 === 0 ? '#ffffff' : '#f8f9fa'}; border-bottom: 1px solid #e5e7eb;">
                <td style="padding: 8px; text-align: right; border-left: 1px solid #e5e7eb;">
                  <div style="font-weight: bold; color: #374151;">${expense.description}</div>
                  ${expense.notes ? `<div style="font-size: 9px; color: #6b7280; margin-top: 2px;">ملاحظة: ${expense.notes}</div>` : ''}
                </td>
                <td style="padding: 8px; text-align: center; font-weight: bold; color: #dc2626; border-left: 1px solid #e5e7eb;">${expense.amount.toFixed(2)}</td>
                <td style="padding: 8px; text-align: center; border-left: 1px solid #e5e7eb;">
                  <span style="background: #fef2f2; color: #dc2626; padding: 4px 8px; border-radius: 4px; font-size: 10px; font-weight: bold;">
                    ${getExpenseCategoryText(expense.category)}
                  </span>
                </td>
                <td style="padding: 8px; text-align: center; color: #374151; border-left: 1px solid #e5e7eb;">${expense.recipient || 'غير محدد'}</td>
                <td style="padding: 8px; text-align: center; color: #6b7280; font-size: 10px;">${formatDate(expense.date)}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>

      <div style="margin-top: 30px; padding-top: 15px; border-top: 2px solid #e5e7eb; text-align: center; color: #6b7280; font-size: 10px;">
        <p style="margin: 0;">ديوان أبو علوش - نظام إدارة العائلة</p>
        <p style="margin: 5px 0 0 0;">تم إنشاء هذا التقرير في: ${new Date().toLocaleString('ar-SA')}</p>
      </div>
    `

    document.body.appendChild(printElement)

    // انتظار قصير ثم إنشاء PDF
    setTimeout(() => {
      html2canvas(printElement, {
        scale: 1.5,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        logging: false
      }).then(canvas => {
        const imgData = canvas.toDataURL('image/png')
        const pdf = new jsPDF('p', 'mm', 'a4')

        const imgWidth = 210
        const pageHeight = 297
        const imgHeight = (canvas.height * imgWidth) / canvas.width

        if (imgHeight <= pageHeight) {
          // صفحة واحدة
          pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight)
        } else {
          // صفحات متعددة
          let heightLeft = imgHeight
          let position = 0

          pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
          heightLeft -= pageHeight

          while (heightLeft >= 0) {
            position = heightLeft - imgHeight
            pdf.addPage()
            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
            heightLeft -= pageHeight
          }
        }

        // حفظ الملف
        const fileName = `تقرير_مصروفات_الديوان_${new Date().toISOString().split('T')[0]}.pdf`
        pdf.save(fileName)

        // إزالة العنصر
        document.body.removeChild(printElement)
      }).catch(error => {
        console.error('خطأ في إنشاء PDF:', error)
        document.body.removeChild(printElement)
        alert('حدث خطأ في تصدير التقرير')
      })
    }, 300)
  }

  const canEdit = session?.user.role !== 'VIEWER'
  const canDelete = session?.user.role === 'ADMIN'

  return (
    <div className="space-y-8">
      {/* رأس الصفحة المحسن */}
      <div className="text-center mb-8">
        <div className="bg-gradient-to-r from-red-600 to-orange-600 rounded-3xl shadow-2xl p-10 text-white relative overflow-hidden">
          {/* خلفية متحركة */}
          <div className="absolute inset-0 bg-gradient-to-r from-red-400 to-orange-500 opacity-30 animate-pulse"></div>

          {/* المحتوى */}
          <div className="relative z-10">
            <div className="inline-flex items-center justify-center w-20 h-20 rounded-full mb-6 bg-white bg-opacity-20 backdrop-blur-sm">
              <TrendingDown className="w-10 h-10 text-white" />
            </div>

            <h1 className="text-5xl font-black mb-4 text-white">
              إدارة المصروفات
            </h1>

            <p className="text-xl font-semibold mb-6 text-red-100">
              عرض وإدارة مصروفات الديوان بكفاءة وسهولة
            </p>

            <div className="flex items-center justify-center space-x-2 space-x-reverse">
              <div className="h-1 w-16 rounded-full bg-white bg-opacity-60"></div>
              <div className="h-1 w-8 rounded-full bg-white bg-opacity-40"></div>
              <div className="h-1 w-16 rounded-full bg-white bg-opacity-60"></div>
            </div>
          </div>
        </div>
      </div>

      {/* أزرار الإجراءات */}
      <div className="flex justify-center mb-8">
        <div className="bg-white rounded-2xl shadow-xl p-6 border border-gray-100">
          <div className="flex flex-wrap gap-4 justify-center">
            <Button
              onClick={handleExportExpensesPDF}
              className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold"
            >
              <Download className="w-5 h-5 ml-2" />
              تصدير PDF
            </Button>
            {canEdit && (
              <Button
                onClick={handleAddNewExpense}
                className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold"
              >
                <Plus className="w-5 h-5 ml-2" />
                إضافة مصروف جديد
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* إحصائيات سريعة محسنة */}
      <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
        <Card className="group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative">
          <div className="absolute inset-0 bg-gradient-to-br from-red-500 to-red-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>

          <div className="bg-gradient-to-r from-red-500 to-red-600 p-1 rounded-t-xl"></div>

          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative z-10">
            <CardTitle className="text-sm font-bold" style={{ color: '#333333' }}>إجمالي المصروفات</CardTitle>
            <div className="p-4 rounded-2xl shadow-lg" style={{ backgroundColor: '#dc3545' }}>
              <DollarSign className="h-7 w-7 text-white" />
            </div>
          </CardHeader>

          <CardContent className="relative z-10">
            <div className="text-4xl font-black mb-2" style={{ color: '#191970' }}>
              {formatCurrency(stats.totalAmount)}
            </div>
            <p className="text-sm font-semibold" style={{ color: '#6c757d' }}>
              إجمالي المبالغ
            </p>
          </CardContent>
        </Card>

        <Card className="group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative">
          <div className="absolute inset-0 bg-gradient-to-br from-orange-500 to-orange-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>

          <div className="bg-gradient-to-r from-orange-500 to-orange-600 p-1 rounded-t-xl"></div>

          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative z-10">
            <CardTitle className="text-sm font-bold" style={{ color: '#333333' }}>عدد المصروفات</CardTitle>
            <div className="p-4 rounded-2xl shadow-lg" style={{ backgroundColor: '#fd7e14' }}>
              <TrendingDown className="h-7 w-7 text-white" />
            </div>
          </CardHeader>

          <CardContent className="relative z-10">
            <div className="text-4xl font-black mb-2" style={{ color: '#191970' }}>
              {stats.totalCount}
            </div>
            <p className="text-sm font-semibold" style={{ color: '#6c757d' }}>
              إجمالي العمليات
            </p>
          </CardContent>
        </Card>

        <Card className="group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative sm:col-span-2 lg:col-span-1">
          <div className="absolute inset-0 bg-gradient-to-br from-yellow-500 to-yellow-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>

          <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 p-1 rounded-t-xl"></div>

          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative z-10">
            <CardTitle className="text-sm font-bold" style={{ color: '#333333' }}>متوسط المصروف</CardTitle>
            <div className="p-4 rounded-2xl shadow-lg" style={{ backgroundColor: '#ffc107' }}>
              <Receipt className="h-7 w-7 text-white" />
            </div>
          </CardHeader>

          <CardContent className="relative z-10">
            <div className="text-4xl font-black mb-2" style={{ color: '#191970' }}>
              {formatCurrency(stats.totalCount > 0 ? stats.totalAmount / stats.totalCount : 0)}
            </div>
            <p className="text-sm font-semibold" style={{ color: '#6c757d' }}>
              متوسط القيمة
            </p>
          </CardContent>
        </Card>
      </div>

      {/* أدوات البحث والتصفية محسنة */}
      <Card className="bg-white/80 backdrop-blur-sm border-gray-200 shadow-lg">
        <CardContent className="p-8">
          <div className="flex flex-col lg:flex-row gap-6">
            <div className="flex-1">
              <div className="relative group">
                <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5 group-focus-within:text-red-500 transition-colors duration-200" />
                <Input
                  placeholder="البحث في المصروفات... (الوصف، الجهة المستفيدة، الملاحظات)"
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="h-12 pr-12 pl-4 border-2 border-gray-200 focus:border-red-500 focus:ring-red-100 rounded-xl text-base transition-all duration-200 bg-white/50 backdrop-blur-sm"
                />
                {search && (
                  <button
                    onClick={() => setSearch('')}
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500 transition-colors duration-200"
                  >
                    ✕
                  </button>
                )}
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <Select value={category} onValueChange={setCategory}>
                <SelectTrigger className="w-full sm:w-[200px] h-12 border-2 border-gray-200 focus:border-red-500 focus:ring-red-100 rounded-xl bg-white/50 backdrop-blur-sm">
                  <SelectValue placeholder="فلترة حسب الفئة" />
                </SelectTrigger>
                <SelectContent className="rounded-xl border-2 border-gray-200 shadow-xl">
                  <SelectItem value="all" className="rounded-lg">🏢 جميع الفئات</SelectItem>
                  <SelectItem value="MEETINGS" className="rounded-lg">🤝 اجتماعات</SelectItem>
                  <SelectItem value="EVENTS" className="rounded-lg">🎉 مناسبات</SelectItem>
                  <SelectItem value="MAINTENANCE" className="rounded-lg">🔧 إصلاحات</SelectItem>
                  <SelectItem value="SOCIAL" className="rounded-lg">👥 اجتماعية</SelectItem>
                  <SelectItem value="GENERAL" className="rounded-lg">📋 عامة</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                size="icon"
                className="h-12 w-12 border-2 border-gray-200 hover:border-red-500 hover:bg-red-50 rounded-xl transition-all duration-200 bg-white/50 backdrop-blur-sm"
              >
                <Filter className="h-5 w-5 text-gray-600" />
              </Button>
            </div>
          </div>
          {(search || category !== 'all') && (
            <div className="mt-4 flex items-center gap-2 text-sm text-gray-600">
              <span>عرض {expenses.length} من أصل {stats.totalCount} مصروف</span>
              {search && (
                <span className="px-2 py-1 bg-red-100 text-red-700 rounded-lg">
                  البحث: &quot;{search}&quot;
                </span>
              )}
              {category !== 'all' && (
                <span className="px-2 py-1 bg-orange-100 text-orange-700 rounded-lg">
                  الفئة: {getExpenseCategoryText(category)}
                </span>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* جدول المصروفات المحسن */}
      <Card className="bg-white/90 backdrop-blur-sm border-gray-200 shadow-xl overflow-hidden">
        <div className="bg-gradient-to-r from-red-600 via-red-700 to-orange-600 p-6">
          <h3 className="text-xl font-bold text-white flex items-center gap-3">
            <Receipt className="w-6 h-6" />
            قائمة المصروفات
            <span className="text-red-200">({expenses.length})</span>
          </h3>
        </div>
        <CardContent className="p-0">
          {loading ? (
            <div className="flex flex-col justify-center items-center h-64 bg-gradient-to-br from-gray-50 to-red-50">
              <div className="w-12 h-12 border-4 border-red-200 border-t-red-600 rounded-full animate-spin mb-4"></div>
              <div className="text-gray-600 font-medium">جاري تحميل المصروفات...</div>
            </div>
          ) : expenses.length === 0 ? (
            <div className="flex flex-col justify-center items-center h-64 bg-gradient-to-br from-gray-50 to-red-50">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
                <Receipt className="w-8 h-8 text-red-600" />
              </div>
              <div className="text-gray-600 font-medium mb-2">لا توجد مصروفات</div>
              <div className="text-gray-500 text-sm">ابدأ بإضافة مصروف جديد</div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader className="bg-gray-50/80">
                  <TableRow className="border-b-2 border-gray-200">
                    <TableHead className="text-right font-bold text-gray-800 py-4">الوصف</TableHead>
                    <TableHead className="text-center font-bold text-gray-800 py-4">المبلغ</TableHead>
                    <TableHead className="text-center font-bold text-gray-800 py-4">الفئة</TableHead>
                    <TableHead className="text-center font-bold text-gray-800 py-4">الجهة المستفيدة</TableHead>
                    <TableHead className="text-center font-bold text-gray-800 py-4">التاريخ</TableHead>
                    <TableHead className="text-center font-bold text-gray-800 py-4">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {expenses.map((expense, index) => (
                    <TableRow
                      key={expense.id}
                      className={`border-b border-gray-100 hover:bg-red-50/50 transition-all duration-200 ${
                        index % 2 === 0 ? 'bg-white' : 'bg-gray-50/30'
                      }`}
                    >
                      <TableCell className="py-4">
                        <div className="space-y-1">
                          <div className="font-semibold text-gray-900">{expense.description}</div>
                          {expense.notes && (
                            <div className="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded-lg inline-block">
                              💬 {expense.notes}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-center py-4">
                        <div className="font-bold text-lg text-red-700 bg-red-50 px-3 py-1 rounded-lg inline-block">
                          {formatCurrency(expense.amount)}
                        </div>
                      </TableCell>
                      <TableCell className="text-center py-4">
                        <Badge
                          variant="secondary"
                          className={`px-3 py-1 font-medium ${
                            expense.category === 'MEETINGS' ? 'bg-blue-100 text-blue-800' :
                            expense.category === 'EVENTS' ? 'bg-purple-100 text-purple-800' :
                            expense.category === 'MAINTENANCE' ? 'bg-orange-100 text-orange-800' :
                            expense.category === 'SOCIAL' ? 'bg-green-100 text-green-800' :
                            'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {getExpenseCategoryText(expense.category)}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-center py-4">
                        <div className="text-gray-700 font-medium">
                          {expense.recipient || (
                            <span className="text-gray-400 italic">غير محدد</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-center py-4">
                        <div className="flex items-center justify-center gap-2 text-gray-600">
                          <Calendar className="w-4 h-4 text-red-500" />
                          <span className="font-medium">{formatDate(expense.date)}</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-center py-4">
                        <div className="flex items-center justify-center gap-2">
                          {canEdit && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditExpense(expense)}
                              className="h-9 w-9 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-all duration-200"
                              title="تعديل المصروف"
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                          )}
                          {canDelete && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteExpense(expense.id)}
                              className="h-9 w-9 p-0 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-all duration-200"
                              title="حذف المصروف"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* التصفح */}
      {pagination.pages > 1 && (
        <div className="flex justify-center space-x-2 space-x-reverse">
          <Button
            variant="outline"
            disabled={pagination.page === 1}
            onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
          >
            السابق
          </Button>
          <span className="flex items-center px-4">
            صفحة {pagination.page} من {pagination.pages}
          </span>
          <Button
            variant="outline"
            disabled={pagination.page === pagination.pages}
            onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
          >
            التالي
          </Button>
        </div>
      )}

      {/* إحصائيات حسب الفئة */}
      {stats.byCategory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>المصروفات حسب الفئة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-5">
              {stats.byCategory.map((categoryStats) => (
                <div key={categoryStats.category} className="border border-gray-200 rounded-lg p-4">
                  <div className="text-sm font-medium text-gray-500">
                    {getExpenseCategoryText(categoryStats.category)}
                  </div>
                  <div className="mt-1 text-lg font-semibold text-gray-900">
                    {formatCurrency(categoryStats._sum.amount || 0)}
                  </div>
                  <div className="text-xs text-gray-400">
                    {categoryStats._count} مصروف
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* حوار إضافة/تعديل المصروف */}
      <ExpenseDialog
        open={isExpenseDialogOpen}
        onOpenChange={handleCloseDialog}
        expense={editingExpense}
        onSuccess={() => {
          fetchExpenses()
          fetchStats()
        }}
      />
    </div>
  )
}
