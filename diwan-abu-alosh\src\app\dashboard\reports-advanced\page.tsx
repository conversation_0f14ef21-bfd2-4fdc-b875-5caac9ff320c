'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { exportToPDF, exportToCSV } from '@/components/reports/ReportExporter'
import MembersReports from '@/components/reports/MembersReports'
import IncomesReports from '@/components/reports/IncomesReports'
import ExpensesReports from '@/components/reports/ExpensesReports'
import ComprehensiveReports from '@/components/reports/ComprehensiveReports'
import HelperReports from '@/components/reports/HelperReports'
import {
  Users,
  TrendingUp,
  TrendingDown,
  BarChart3,
  AlertTriangle,
  FileText,
  Calculator
} from 'lucide-react'

export default function AdvancedReportsPage() {
  const { data: session } = useSession()
  const [activeTab, setActiveTab] = useState('members')

  // دالة معالجة تصدير PDF
  const handleExportPDF = async (data: unknown, type: string) => {
    await exportToPDF(data, type)
  }

  // دالة معالجة تصدير CSV
  const handleExportCSV = (data: unknown, type: string) => {
    exportToCSV(data, type)
  }

  if (!session) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-gray-500">يرجى تسجيل الدخول للوصول إلى التقارير</div>
      </div>
    )
  }

  return (
    <div className="space-y-6 arabic-text">
      {/* العنوان الرئيسي */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900" style={{ fontFamily: 'Cairo, Almarai, sans-serif' }}>
            نظام التقارير المتقدم
          </h1>
          <p className="text-gray-600 mt-2" style={{ fontFamily: 'Cairo, Almarai, sans-serif' }}>
            تقارير شاملة ومفصلة لجميع جوانب إدارة ديوان أبو علوش
          </p>
        </div>
        <div className="flex items-center space-x-2 space-x-reverse">
          <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
            نظام متقدم
          </div>
        </div>
      </div>

      {/* نظرة عامة سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setActiveTab('members')}>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <Users className={`w-8 h-8 ${activeTab === 'members' ? 'text-blue-600' : 'text-gray-400'}`} />
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">تقارير الأعضاء</p>
                <p className="text-xs text-gray-500">قوائم وكشوف مفصلة</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setActiveTab('incomes')}>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <TrendingUp className={`w-8 h-8 ${activeTab === 'incomes' ? 'text-green-600' : 'text-gray-400'}`} />
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">تقارير الإيرادات</p>
                <p className="text-xs text-gray-500">تحليل المساهمات</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setActiveTab('expenses')}>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <TrendingDown className={`w-8 h-8 ${activeTab === 'expenses' ? 'text-red-600' : 'text-gray-400'}`} />
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">تقارير المصروفات</p>
                <p className="text-xs text-gray-500">تحليل النفقات</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setActiveTab('comprehensive')}>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <BarChart3 className={`w-8 h-8 ${activeTab === 'comprehensive' ? 'text-purple-600' : 'text-gray-400'}`} />
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">التقارير الشاملة</p>
                <p className="text-xs text-gray-500">كشوف ومقارنات</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setActiveTab('helper')}>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <AlertTriangle className={`w-8 h-8 ${activeTab === 'helper' ? 'text-orange-600' : 'text-gray-400'}`} />
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">التقارير المساعدة</p>
                <p className="text-xs text-gray-500">متأخرين وطوارئ</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* التبويبات الرئيسية */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="members" className="flex items-center space-x-2 space-x-reverse">
            <Users className="w-4 h-4" />
            <span>تقارير الأعضاء</span>
          </TabsTrigger>
          <TabsTrigger value="incomes" className="flex items-center space-x-2 space-x-reverse">
            <TrendingUp className="w-4 h-4" />
            <span>تقارير الإيرادات</span>
          </TabsTrigger>
          <TabsTrigger value="expenses" className="flex items-center space-x-2 space-x-reverse">
            <TrendingDown className="w-4 h-4" />
            <span>تقارير المصروفات</span>
          </TabsTrigger>
          <TabsTrigger value="comprehensive" className="flex items-center space-x-2 space-x-reverse">
            <BarChart3 className="w-4 h-4" />
            <span>التقارير الشاملة</span>
          </TabsTrigger>
          <TabsTrigger value="helper" className="flex items-center space-x-2 space-x-reverse">
            <AlertTriangle className="w-4 h-4" />
            <span>التقارير المساعدة</span>
          </TabsTrigger>
        </TabsList>

        {/* محتوى التبويبات */}
        <TabsContent value="members" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="w-5 h-5 ml-2" />
                تقارير الأعضاء
              </CardTitle>
              <p className="text-sm text-gray-600">
                تقارير شاملة عن الأعضاء تشمل قائمة الأعضاء والتقارير المفصلة لكل عضو
              </p>
            </CardHeader>
            <CardContent>
              <MembersReports 
                onExportPDF={handleExportPDF}
                onExportCSV={handleExportCSV}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="incomes" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="w-5 h-5 ml-2" />
                تقارير الإيرادات
              </CardTitle>
              <p className="text-sm text-gray-600">
                تقارير مفصلة عن الإيرادات والمساهمات حسب الفترة والنوع والعضو
              </p>
            </CardHeader>
            <CardContent>
              <IncomesReports 
                onExportPDF={handleExportPDF}
                onExportCSV={handleExportCSV}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="expenses" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingDown className="w-5 h-5 ml-2" />
                تقارير المصروفات
              </CardTitle>
              <p className="text-sm text-gray-600">
                تقارير شاملة عن المصروفات والنفقات حسب الفئة والمستفيد والفترة الزمنية
              </p>
            </CardHeader>
            <CardContent>
              <ExpensesReports 
                onExportPDF={handleExportPDF}
                onExportCSV={handleExportCSV}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="comprehensive" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calculator className="w-5 h-5 ml-2" />
                التقارير الشاملة
              </CardTitle>
              <p className="text-sm text-gray-600">
                كشوف حسابات الأعضاء وملخص الحسابات العامة وتقارير المقارنة
              </p>
            </CardHeader>
            <CardContent>
              <ComprehensiveReports 
                onExportPDF={handleExportPDF}
                onExportCSV={handleExportCSV}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="helper" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="w-5 h-5 ml-2" />
                التقارير المساعدة
              </CardTitle>
              <p className="text-sm text-gray-600">
                تقارير الأعضاء المتأخرين عن الدفع وتقارير الطوارئ والمساعدات
              </p>
            </CardHeader>
            <CardContent>
              <HelperReports 
                onExportPDF={handleExportPDF}
                onExportCSV={handleExportCSV}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* معلومات إضافية */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="pt-6">
          <div className="flex items-start space-x-3 space-x-reverse">
            <FileText className="w-5 h-5 text-blue-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-blue-900 mb-2">ميزات نظام التقارير المتقدم</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• تصدير جميع التقارير بصيغة PDF مع دعم كامل للغة العربية</li>
                <li>• تصدير البيانات بصيغة CSV للتحليل في برامج أخرى</li>
                <li>• فلترة متقدمة حسب التاريخ والنوع والعضو</li>
                <li>• بحث ذكي في جميع البيانات</li>
                <li>• إحصائيات وتحليلات تفاعلية</li>
                <li>• تقارير الأعضاء المتأخرين مع إمكانية إرسال تذكيرات واتساب</li>
                <li>• كشوف حسابات مفصلة لكل عضو</li>
                <li>• تقارير مقارنة شهرية وسنوية</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
