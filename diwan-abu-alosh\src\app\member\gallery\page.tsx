'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { 
  ArrowLeft, 
  Camera, 
  Search,
  LogOut,
  Folder,
  Image as ImageIcon,
  Eye,
  Calendar
} from 'lucide-react'
import Image from 'next/image'

interface Member {
  id: string
  name: string
  email: string
  phone?: string
}

interface GalleryFolder {
  id: string
  title: string
  name: string
  description?: string
  createdAt: string
  _count: {
    photos: number
  }
}

interface GalleryPhoto {
  id: string
  title: string
  filename: string
  originalName: string
  imagePath: string
  description?: string
  createdAt: string
  folder?: {
    id: string
    name: string
  }
}

export default function MemberGallery() {
  const [member, setMember] = useState<Member | null>(null)
  const [folders, setFolders] = useState<GalleryFolder[]>([])
  const [photos, setPhotos] = useState<GalleryPhoto[]>([])
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [loading, setLoading] = useState(true)
  const [selectedPhoto, setSelectedPhoto] = useState<GalleryPhoto | null>(null)
  const router = useRouter()

  useEffect(() => {
    checkAuthAndLoadData()
  }, [])

  const checkAuthAndLoadData = async () => {
    try {
      // التحقق من المصادقة
      const authResponse = await fetch('/api/auth/member/session')
      const authData = await authResponse.json()

      if (!authResponse.ok) {
        router.push('/member/signin')
        return
      }

      setMember(authData.user.member)

      // جلب المجلدات والصور
      await loadGalleryData()
    } catch (error) {
      console.error('خطأ في جلب البيانات:', error)
      router.push('/member/signin')
    } finally {
      setLoading(false)
    }
  }

  const loadGalleryData = async () => {
    try {
      const response = await fetch('/api/member/gallery')
      if (response.ok) {
        const data = await response.json()
        setFolders(data.folders)
        setPhotos(data.unfolderPhotos)
      } else {
        console.error('خطأ في جلب المعرض:', response.status)
      }
    } catch (error) {
      console.error('خطأ في جلب المعرض:', error)
    }
  }

  const loadFolderPhotos = async (folderId: string) => {
    try {
      const response = await fetch(`/api/member/gallery/${folderId}`)
      if (response.ok) {
        const data = await response.json()
        setPhotos(data.photos)
      } else {
        console.error('خطأ في جلب صور المجلد:', response.status)
      }
    } catch (error) {
      console.error('خطأ في جلب صور المجلد:', error)
    }
  }

  const handleLogout = async () => {
    // حذف الكوكيز فوراً
    document.cookie = 'member-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;'

    // توجيه فوري
    window.location.href = '/member/signin'

    // إرسال طلب للخادم في الخلفية
    fetch('/api/auth/member/signout', { method: 'POST' }).catch(() => {})
  }

  const handleFolderSelect = (folderId: string) => {
    setSelectedFolder(folderId)
    loadFolderPhotos(folderId)
  }

  const handleBackToFolders = () => {
    setSelectedFolder(null)
    loadGalleryData()
  }

  const filteredPhotos = photos.filter(photo =>
    (photo.title || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    photo.description?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA')
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل المعرض...</p>
        </div>
      </div>
    )
  }

  if (!member) {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      {/* الهيدر */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-3">
              <Button
                onClick={() => router.push('/member/dashboard')}
                variant="outline"
                size="sm"
                className="border-gray-300"
              >
                <ArrowLeft className="w-4 h-4 ml-2" />
                العودة
              </Button>
              <div className="flex items-center gap-2">
                <Camera className="w-6 h-6 text-indigo-600" />
                <h1 className="text-lg font-bold text-gray-900">معرض الصور</h1>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Button
                onClick={handleLogout}
                variant="outline"
                size="sm"
                className="text-red-600 border-red-200 hover:bg-red-50"
              >
                <LogOut className="w-4 h-4 ml-2" />
                خروج
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* المحتوى الرئيسي */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* شريط البحث */}
        <div className="mb-8">
          <div className="relative max-w-md">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <Input
              type="text"
              placeholder="البحث في الصور..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pr-10"
            />
          </div>
        </div>

        {!selectedFolder ? (
          // عرض المجلدات
          <div>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                <Folder className="w-6 h-6 text-indigo-600" />
                مجلدات الصور
              </h2>
              <p className="text-gray-600">{folders.length} مجلد</p>
            </div>

            {folders.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {folders.map((folder) => (
                  <Card 
                    key={folder.id} 
                    className="cursor-pointer hover:shadow-lg transition-all duration-300 border-2 hover:border-indigo-200"
                    onClick={() => handleFolderSelect(folder.id)}
                  >
                    <CardHeader className="pb-4">
                      <CardTitle className="flex items-center gap-3">
                        <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                          <Folder className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">{folder.title}</h3>
                          <p className="text-sm text-gray-600">{folder._count.photos} صورة</p>
                        </div>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {folder.description && (
                        <p className="text-gray-600 mb-3">{folder.description}</p>
                      )}
                      <div className="flex items-center justify-between text-sm text-gray-500">
                        <span className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          {formatDate(folder.createdAt)}
                        </span>
                        <span className="flex items-center gap-1">
                          <Eye className="w-4 h-4" />
                          عرض الصور
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Folder className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">لا توجد مجلدات</h3>
                <p className="text-gray-600">لم يتم إنشاء أي مجلدات في المعرض بعد</p>
              </div>
            )}
          </div>
        ) : (
          // عرض الصور
          <div>
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <Button
                  onClick={handleBackToFolders}
                  variant="outline"
                  size="sm"
                >
                  <ArrowLeft className="w-4 h-4 ml-2" />
                  العودة للمجلدات
                </Button>
                <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                  <ImageIcon className="w-6 h-6 text-indigo-600" />
                  الصور
                </h2>
              </div>
              <p className="text-gray-600">{filteredPhotos.length} صورة</p>
            </div>

            {filteredPhotos.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {filteredPhotos.map((photo) => (
                  <Card 
                    key={photo.id} 
                    className="cursor-pointer hover:shadow-lg transition-all duration-300 overflow-hidden"
                    onClick={() => setSelectedPhoto(photo)}
                  >
                    <div className="aspect-square relative">
                      <Image
                        src={photo.imagePath}
                        alt={photo.title || 'صورة'}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <CardContent className="p-4">
                      <h3 className="font-semibold text-gray-900 truncate">{photo.title || 'صورة'}</h3>
                      {photo.description && (
                        <p className="text-sm text-gray-600 mt-1 line-clamp-2">{photo.description}</p>
                      )}
                      <p className="text-xs text-gray-500 mt-2">{formatDate(photo.createdAt)}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <ImageIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">لا توجد صور</h3>
                <p className="text-gray-600">لا توجد صور في هذا المجلد</p>
              </div>
            )}
          </div>
        )}

        {/* مودال عرض الصورة */}
        {selectedPhoto && (
          <div 
            className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
            onClick={() => setSelectedPhoto(null)}
          >
            <div className="max-w-4xl max-h-full bg-white rounded-lg overflow-hidden">
              <div className="relative">
                <Image
                  src={selectedPhoto.imagePath}
                  alt={selectedPhoto.title || 'صورة'}
                  width={800}
                  height={600}
                  className="w-full h-auto"
                />
                <Button
                  onClick={() => setSelectedPhoto(null)}
                  className="absolute top-4 left-4 bg-black bg-opacity-50 text-white hover:bg-opacity-75"
                  size="sm"
                >
                  إغلاق
                </Button>
              </div>
              <div className="p-4">
                <h3 className="font-semibold text-gray-900">{selectedPhoto.title || 'صورة'}</h3>
                {selectedPhoto.description && (
                  <p className="text-gray-600 mt-2">{selectedPhoto.description}</p>
                )}
                <p className="text-sm text-gray-500 mt-2">{formatDate(selectedPhoto.createdAt)}</p>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  )
}
