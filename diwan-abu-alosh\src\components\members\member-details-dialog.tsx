'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog'
// import { Badge } from '@/components/ui/badge'
import { formatDate, formatCurrency, getMemberStatusText, getMemberStatusColor } from '@/lib/utils'
import { 
  User, 
  Phone, 
  // Mail,
  MapPin, 
  Calendar, 
  // DollarSign,
  Activity,
  FileText
} from 'lucide-react'

interface Member {
  id: string
  name: string
  phone?: string
  address?: string
  notes?: string
  status: string
  createdAt: string
  _count: {
    incomes: number
  }
}

interface MemberDetailsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  member?: Member | null
}

interface MemberDetails {
  member: Member
  incomes: Array<{
    id: string
    amount: number
    date: string
    source: string
    type: string
    description?: string
  }>
  activities: Array<{
    id: string
    title: string
    startDate: string
    location?: string
  }>
  totalContributions: number
}

export default function MemberDetailsDialog({
  open,
  onOpenChange,
  member,
}: MemberDetailsDialogProps) {
  const [details, setDetails] = useState<MemberDetails | null>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (open && member) {
      fetchMemberDetails()
    }
  }, [open, member])

  const fetchMemberDetails = async () => {
    if (!member) return

    try {
      setLoading(true)
      const response = await fetch(`/api/members/${member.id}`)
      if (!response.ok) throw new Error('فشل في جلب تفاصيل العضو')

      const data = await response.json()
      
      // حساب إجمالي المساهمات
      const totalContributions = data.incomes?.reduce((sum: number, income: any) => sum + income.amount, 0) || 0
      
      setDetails({
        member: data,
        incomes: data.incomes || [],
        activities: data.activityParticipants?.map((p: any) => p.activity) || [],
        totalContributions,
      })
    } catch (error) {
      console.error('خطأ في جلب تفاصيل العضو:', error)
    } finally {
      setLoading(false)
    }
  }

  if (!member) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[50vw] max-h-[90vh] overflow-y-auto">
        <DialogClose onOpenChange={onOpenChange} />
        <DialogHeader className="p-6 pb-0">
          <DialogTitle className="flex items-center space-x-2 space-x-reverse">
            <User className="w-5 h-5 text-diwan-600" />
            <span>تفاصيل العضو: {member.name}</span>
          </DialogTitle>
        </DialogHeader>

        <div className="p-6 pt-0 space-y-6">
          {loading ? (
            <div className="flex justify-center items-center h-32">
              <div className="text-gray-500">جاري التحميل...</div>
            </div>
          ) : details ? (
            <>
              {/* المعلومات الأساسية */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">المعلومات الأساسية</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <User className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-600">الاسم:</span>
                    <span className="font-medium">{details.member.name}</span>
                  </div>
                  
                  {details.member.phone && (
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <Phone className="w-4 h-4 text-gray-500" />
                      <span className="text-sm text-gray-600">الهاتف:</span>
                      <span className="font-medium">{details.member.phone}</span>
                    </div>
                  )}

                  {details.member.address && (
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <MapPin className="w-4 h-4 text-gray-500" />
                      <span className="text-sm text-gray-600">العنوان:</span>
                      <span className="font-medium">{details.member.address}</span>
                    </div>
                  )}
                  
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <Calendar className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-600">تاريخ الانضمام:</span>
                    <span className="font-medium">{formatDate(details.member.createdAt)}</span>
                  </div>
                  
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <Activity className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-600">الحالة:</span>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getMemberStatusColor(details.member.status)}`}>
                      {getMemberStatusText(details.member.status)}
                    </span>
                  </div>
                </div>
                
                {details.member.notes && (
                  <div className="mt-4">
                    <div className="flex items-center space-x-2 space-x-reverse mb-2">
                      <FileText className="w-4 h-4 text-gray-500" />
                      <span className="text-sm text-gray-600">ملاحظات:</span>
                    </div>
                    <p className="text-sm bg-white p-3 rounded border">{details.member.notes}</p>
                  </div>
                )}
              </div>

              {/* الإحصائيات المالية */}
              <div className="bg-green-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">الإحصائيات المالية</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {formatCurrency(details.totalContributions)}
                    </div>
                    <div className="text-sm text-gray-600">إجمالي المساهمات</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {details.incomes.length}
                    </div>
                    <div className="text-sm text-gray-600">عدد الإيرادات</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {details.incomes.length > 0 ? formatCurrency(details.totalContributions / details.incomes.length) : formatCurrency(0)}
                    </div>
                    <div className="text-sm text-gray-600">متوسط المساهمة</div>
                  </div>
                </div>
              </div>

              {/* آخر الإيرادات */}
              {details.incomes.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">آخر الإيرادات</h3>
                  <div className="space-y-2 max-h-48 overflow-y-auto">
                    {details.incomes.slice(0, 10).map((income) => (
                      <div key={income.id} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                        <div>
                          <div className="font-medium">{income.source}</div>
                          {income.description && (
                            <div className="text-sm text-gray-600">{income.description}</div>
                          )}
                          <div className="text-xs text-gray-500">{formatDate(income.date)}</div>
                        </div>
                        <div className="text-green-600 font-semibold">
                          {formatCurrency(income.amount)}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* الأنشطة المشارك فيها */}
              {details.activities.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">الأنشطة المشارك فيها</h3>
                  <div className="space-y-2 max-h-48 overflow-y-auto">
                    {details.activities.slice(0, 10).map((activity) => (
                      <div key={activity.id} className="flex justify-between items-center p-3 bg-blue-50 rounded">
                        <div>
                          <div className="font-medium">{activity.title}</div>
                          {activity.location && (
                            <div className="text-sm text-gray-600">📍 {activity.location}</div>
                          )}
                        </div>
                        <div className="text-sm text-gray-500">
                          {formatDate(activity.startDate)}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="text-center text-gray-500">لا توجد تفاصيل متاحة</div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
