import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET - تصدير البيانات
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const format = searchParams.get('format') || 'json'
    const table = searchParams.get('table') || 'all'

    const data: Record<string, unknown> = {}

    // جلب البيانات حسب الجدول المطلوب
    if (table === 'all' || table === 'members') {
      data.members = await prisma.member.findMany({
        include: {
          createdBy: {
            select: { name: true, email: true }
          }
        }
      })
    }

    if (table === 'all' || table === 'incomes') {
      data.incomes = await prisma.income.findMany({
        include: {
          member: {
            select: { name: true }
          },
          createdBy: {
            select: { name: true, email: true }
          }
        }
      })
    }

    if (table === 'all' || table === 'expenses') {
      data.expenses = await prisma.expense.findMany({
        include: {
          createdBy: {
            select: { name: true, email: true }
          }
        }
      })
    }

    if (table === 'all' || table === 'activities') {
      data.activities = await prisma.activity.findMany({
        include: {
          createdBy: {
            select: { name: true, email: true }
          },
          participants: {
            include: {
              member: {
                select: { name: true }
              }
            }
          }
        }
      })
    }

    if (table === 'all' || table === 'gallery') {
      data.gallery = {
        folders: await prisma.galleryFolder.findMany({
          include: {
            creator: {
              select: { name: true, email: true }
            }
          }
        }),
        photos: await prisma.galleryPhoto.findMany({
          include: {
            uploader: {
              select: { name: true, email: true }
            },
            folder: {
              select: { title: true }
            },
            activity: {
              select: { title: true }
            }
          }
        })
      }
    }

    if (table === 'all' || table === 'notifications') {
      data.notifications = await prisma.notification.findMany({
        include: {
          user: {
            select: { name: true, email: true }
          }
        }
      })
    }

    if (table === 'all' || table === 'settings') {
      data.settings = await prisma.settings.findMany()
    }

    // تحويل البيانات حسب التنسيق المطلوب
    let responseData: string
    let contentType: string
    let filename: string

    const timestamp = new Date().toISOString().split('T')[0]

    switch (format.toLowerCase()) {
      case 'json':
        responseData = JSON.stringify(data, null, 2)
        contentType = 'application/json'
        filename = `diwan-data-${timestamp}.json`
        break

      case 'csv':
        // تحويل إلى CSV (مبسط للأعضاء فقط كمثال)
        if (data.members) {
          const csvHeaders = 'الاسم,الهاتف,البريد الإلكتروني,العنوان,الحالة,تاريخ الإنشاء\n'
          const csvRows = (data.members as Array<{
            name: string;
            phone?: string;
            email?: string;
            address?: string;
            status: string;
            createdAt: string;
          }>).map((member) =>
            `"${member.name}","${member.phone || ''}","${member.email || ''}","${member.address || ''}","${member.status}","${new Date(member.createdAt).toLocaleDateString('ar-SA')}"`
          ).join('\n')
          responseData = csvHeaders + csvRows
        } else {
          responseData = 'لا توجد بيانات للتصدير'
        }
        contentType = 'text/csv; charset=utf-8'
        filename = `diwan-members-${timestamp}.csv`
        break

      case 'xlsx':
        // للـ Excel، نحتاج مكتبة خاصة، هنا سنرجع JSON مؤقتاً
        responseData = JSON.stringify(data, null, 2)
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        filename = `diwan-data-${timestamp}.xlsx`
        break

      case 'pdf':
        // للـ PDF، نحتاج مكتبة خاصة، هنا سنرجع نص مؤقتاً
        responseData = `تقرير ديوان آل أبو علوش - ${timestamp}\n\n${JSON.stringify(data, null, 2)}`
        contentType = 'application/pdf'
        filename = `diwan-report-${timestamp}.pdf`
        break

      default:
        return NextResponse.json({ error: 'تنسيق غير مدعوم' }, { status: 400 })
    }

    // إنشاء الاستجابة مع الملف
    const response = new NextResponse(responseData, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Cache-Control': 'no-cache'
      }
    })

    return response

  } catch (error) {
    console.error('خطأ في تصدير البيانات:', error)
    return NextResponse.json({ error: 'خطأ في الخادم' }, { status: 500 })
  }
}

// POST - تصدير بيانات مخصصة
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    const { tables, format, filters, dateRange } = await request.json()

    const data: Record<string, unknown> = {}

    // تطبيق الفلاتر والتواريخ
    const whereClause: Record<string, unknown> = {}
    if (dateRange?.start && dateRange?.end) {
      whereClause.createdAt = {
        gte: new Date(dateRange.start),
        lte: new Date(dateRange.end)
      }
    }

    // جلب البيانات المطلوبة فقط
    for (const table of tables) {
      switch (table) {
        case 'members':
          data.members = await prisma.member.findMany({
            where: {
              ...whereClause,
              ...(filters?.memberStatus && { status: filters.memberStatus })
            },
            include: {
              createdBy: {
                select: { name: true, email: true }
              }
            }
          })
          break

        case 'incomes':
          data.incomes = await prisma.income.findMany({
            where: {
              ...whereClause,
              ...(filters?.incomeType && { type: filters.incomeType }),
              ...(filters?.memberId && { memberId: filters.memberId })
            },
            include: {
              member: {
                select: { name: true }
              },
              createdBy: {
                select: { name: true, email: true }
              }
            }
          })
          break

        case 'expenses':
          data.expenses = await prisma.expense.findMany({
            where: {
              ...whereClause,
              ...(filters?.expenseCategory && { category: filters.expenseCategory })
            },
            include: {
              createdBy: {
                select: { name: true, email: true }
              }
            }
          })
          break

        // يمكن إضافة المزيد من الجداول هنا
      }
    }

    // إرجاع البيانات كـ JSON للمعالجة في الواجهة الأمامية
    return NextResponse.json({
      success: true,
      data,
      exportInfo: {
        timestamp: new Date().toISOString(),
        tables,
        format,
        recordCount: Object.values(data).reduce((total: number, tableData: unknown) =>
          total + (Array.isArray(tableData) ? tableData.length : 0), 0
        )
      }
    })

  } catch (error) {
    console.error('خطأ في التصدير المخصص:', error)
    return NextResponse.json({ error: 'خطأ في الخادم' }, { status: 500 })
  }
}
