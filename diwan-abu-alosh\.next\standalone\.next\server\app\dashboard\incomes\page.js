(()=>{var e={};e.id=346,e.ids=[346],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,s,a)=>{"use strict";a.d(s,{A0:()=>d,BF:()=>o,Hj:()=>n,XI:()=>i,nA:()=>m,nd:()=>c});var r=a(60687),t=a(43210),l=a(4780);let i=t.forwardRef(({className:e,...s},a)=>(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:a,className:(0,l.cn)("w-full caption-bottom text-sm",e),...s})}));i.displayName="Table";let d=t.forwardRef(({className:e,...s},a)=>(0,r.jsx)("thead",{ref:a,className:(0,l.cn)("[&_tr]:border-b",e),...s}));d.displayName="TableHeader";let o=t.forwardRef(({className:e,...s},a)=>(0,r.jsx)("tbody",{ref:a,className:(0,l.cn)("[&_tr:last-child]:border-0",e),...s}));o.displayName="TableBody",t.forwardRef(({className:e,...s},a)=>(0,r.jsx)("tfoot",{ref:a,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let n=t.forwardRef(({className:e,...s},a)=>(0,r.jsx)("tr",{ref:a,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));n.displayName="TableRow";let c=t.forwardRef(({className:e,...s},a)=>(0,r.jsx)("th",{ref:a,className:(0,l.cn)("h-12 px-4 text-right align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...s}));c.displayName="TableHead";let m=t.forwardRef(({className:e,...s},a)=>(0,r.jsx)("td",{ref:a,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));m.displayName="TableCell"},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},18528:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\ابوعلوش\\\\diwan-abu-alosh\\\\src\\\\app\\\\dashboard\\\\incomes\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\incomes\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26435:(e,s,a)=>{Promise.resolve().then(a.bind(a,18528))},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34729:(e,s,a)=>{"use strict";a.d(s,{T:()=>i});var r=a(60687),t=a(43210),l=a(4780);let i=t.forwardRef(({className:e,...s},a)=>(0,r.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...s}));i.displayName="Textarea"},49144:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>n});var r=a(65239),t=a(48088),l=a(88170),i=a.n(l),d=a(30893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);a.d(s,o);let n={children:["",{children:["dashboard",{children:["incomes",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,18528)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\incomes\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,63144)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\incomes\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/dashboard/incomes/page",pathname:"/dashboard/incomes",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70855:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>R});var r=a(60687),t=a(43210),l=a(82136),i=a(29523),d=a(89667),o=a(44493),n=a(96834),c=a(6211),m=a(25541),x=a(10022),h=a(96474),u=a(23928),p=a(41312),g=a(99270),b=a(80462),f=a(40228),v=a(63143),j=a(88233),N=a(4780),y=a(27605),w=a(57335),k=a(9275),C=a(80013),A=a(34729),T=a(63503),S=a(15079),D=a(11860),$=a(58869);let I=k.z.object({amount:k.z.number().positive("المبلغ يجب أن يكون أكبر من صفر"),date:k.z.string().min(1,"التاريخ مطلوب"),source:k.z.string().min(1,"مصدر الإيراد مطلوب"),type:k.z.enum(["SUBSCRIPTION","DONATION","EVENT","OTHER"]).default("SUBSCRIPTION"),description:k.z.string().optional(),notes:k.z.string().optional(),memberId:k.z.string().min(1,"يجب اختيار عضو")});function O({open:e,onOpenChange:s,onSuccess:a,income:l=null}){let[o,n]=(0,t.useState)(!1),[c,m]=(0,t.useState)([]),[x,h]=(0,t.useState)([]),[p,b]=(0,t.useState)(""),[f,v]=(0,t.useState)(null),[j,N]=(0,t.useState)(!1),k=(0,t.useRef)(null),{register:O,handleSubmit:P,reset:z,setValue:R,watch:E,formState:{errors:_}}=(0,y.mN)({resolver:(0,w.u)(I),defaultValues:{amount:0,date:new Date().toISOString().split("T")[0],source:"",type:"SUBSCRIPTION",description:"",notes:"",memberId:""}}),q=E("type"),F=e=>{v(e),b(e.name),N(!1),R("memberId",e.id)},U=()=>{v(null),b(""),R("memberId","")},B=async e=>{try{n(!0);let r={...e,amount:Number(e.amount),date:new Date(e.date),memberId:e.memberId,description:e.description?.trim()||null,notes:e.notes?.trim()||null},t=!!l,i=t?`/api/incomes/${l.id}`:"/api/incomes",d=await fetch(i,{method:t?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!d.ok){let e=await d.json();throw Error(e.error||"حدث خطأ")}alert(t?"تم تعديل الإيراد بنجاح":"تم إضافة الإيراد بنجاح"),s(!1),a?.()}catch(e){console.error("خطأ في حفظ الإيراد:",e),alert(e.message||"حدث خطأ في حفظ الإيراد")}finally{n(!1)}};return(0,r.jsx)(T.lG,{open:e,onOpenChange:s,children:(0,r.jsxs)(T.Cf,{className:"max-w-[50vw] max-h-[95vh] overflow-y-auto bg-white rounded-2xl shadow-2xl border-0",children:[(0,r.jsxs)(T.c7,{className:"relative p-8 pb-6 border-b border-gray-100 bg-gradient-to-br from-diwan-50 via-blue-50 to-indigo-50 rounded-t-2xl",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-diwan-600/5 via-blue-600/5 to-indigo-600/5 rounded-t-2xl"}),(0,r.jsxs)("div",{className:"relative flex items-center gap-4",children:[(0,r.jsx)("div",{className:"p-4 bg-white rounded-2xl shadow-lg border border-diwan-200/50 backdrop-blur-sm",children:(0,r.jsx)(u.A,{className:"w-7 h-7 text-diwan-600"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)(T.L3,{className:"text-2xl font-bold text-gray-900 mb-2",children:l?"تعديل الإيراد":"إضافة إيراد جديد"}),(0,r.jsx)("p",{className:"text-gray-600 text-base leading-relaxed",children:l?"قم بتعديل بيانات الإيراد بعناية لضمان دقة السجلات المالية":"قم بإدخال بيانات الإيراد الجديد بعناية لضمان دقة السجلات المالية"})]})]})]}),(0,r.jsxs)("form",{onSubmit:P(B),className:"p-8 space-y-8 bg-gray-50/30",children:[(0,r.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border border-gray-100",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-diwan-600 rounded-full"}),"المعلومات الأساسية"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(C.J,{htmlFor:"amount",className:"text-sm font-medium text-gray-700 flex items-center gap-1",children:["المبلغ (دينار أردني)",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(d.p,{id:"amount",type:"number",step:"0.01",min:"0",...O("amount",{valueAsNumber:!0}),className:`h-12 text-lg font-medium transition-all duration-200 ${_.amount?"border-red-300 focus:border-red-500 focus:ring-red-200":"border-gray-200 focus:border-diwan-500 focus:ring-diwan-100"}`,placeholder:"0.00"}),(0,r.jsx)("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm",children:"د.أ"})]}),_.amount&&(0,r.jsxs)("p",{className:"text-sm text-red-500 flex items-center gap-1",children:[(0,r.jsx)("span",{className:"w-1 h-1 bg-red-500 rounded-full"}),_.amount.message]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(C.J,{htmlFor:"date",className:"text-sm font-medium text-gray-700 flex items-center gap-1",children:["التاريخ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)(d.p,{id:"date",type:"date",...O("date"),className:`h-12 transition-all duration-200 ${_.date?"border-red-300 focus:border-red-500 focus:ring-red-200":"border-gray-200 focus:border-diwan-500 focus:ring-diwan-100"}`}),_.date&&(0,r.jsxs)("p",{className:"text-sm text-red-500 flex items-center gap-1",children:[(0,r.jsx)("span",{className:"w-1 h-1 bg-red-500 rounded-full"}),_.date.message]})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border border-gray-100",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-gold-600 rounded-full"}),"تفاصيل الإيراد"]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(C.J,{htmlFor:"source",className:"text-sm font-medium text-gray-700 flex items-center gap-1",children:["مصدر الإيراد",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)(d.p,{id:"source",...O("source"),placeholder:"مثال: اشتراك شهري، تبرع، رسوم فعالية",className:`h-12 transition-all duration-200 ${_.source?"border-red-300 focus:border-red-500 focus:ring-red-200":"border-gray-200 focus:border-diwan-500 focus:ring-diwan-100"}`}),_.source&&(0,r.jsxs)("p",{className:"text-sm text-red-500 flex items-center gap-1",children:[(0,r.jsx)("span",{className:"w-1 h-1 bg-red-500 rounded-full"}),_.source.message]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(C.J,{htmlFor:"type",className:"text-sm font-medium text-gray-700",children:"نوع الإيراد"}),(0,r.jsxs)(S.l6,{value:q,onValueChange:e=>R("type",e),children:[(0,r.jsx)(S.bq,{className:"h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-100 transition-all duration-200",children:(0,r.jsx)(S.yv,{placeholder:"اختر نوع الإيراد"})}),(0,r.jsxs)(S.gC,{className:"border-gray-200 shadow-lg",children:[(0,r.jsx)(S.eb,{value:"SUBSCRIPTION",className:"hover:bg-diwan-50 focus:bg-diwan-50",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"اشتراكات"]})}),(0,r.jsx)(S.eb,{value:"DONATION",className:"hover:bg-green-50 focus:bg-green-50",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"تبرعات"]})}),(0,r.jsx)(S.eb,{value:"EVENT",className:"hover:bg-purple-50 focus:bg-purple-50",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-purple-500 rounded-full"}),"فعاليات"]})}),(0,r.jsx)(S.eb,{value:"OTHER",className:"hover:bg-gray-50 focus:bg-gray-50",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-gray-500 rounded-full"}),"أخرى"]})})]})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(C.J,{htmlFor:"memberSearch",className:"text-sm font-medium text-gray-700 flex items-center gap-1",children:["العضو",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("div",{className:"relative",ref:k,children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(g.A,{className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,r.jsx)(d.p,{id:"memberSearch",type:"text",placeholder:"ابحث عن عضو...",value:p,onChange:e=>b(e.target.value),onFocus:()=>{N(!0),""===p.trim()&&h(c.slice(0,10))},className:`h-12 pr-12 pl-12 transition-all duration-200 ${_.memberId?"border-red-300 focus:border-red-500 focus:ring-red-200":"border-gray-200 focus:border-diwan-500 focus:ring-diwan-100"}`}),f&&(0,r.jsx)("button",{type:"button",onClick:U,className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500 transition-colors duration-200",children:(0,r.jsx)(D.A,{className:"w-5 h-5"})})]}),j&&(0,r.jsx)("div",{className:"absolute z-50 w-full mt-2 bg-white border border-gray-200 rounded-xl shadow-xl max-h-64 overflow-y-auto",children:0===x.length?(0,r.jsx)("div",{className:"p-4 text-gray-500 text-center",children:(0,r.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,r.jsx)(g.A,{className:"w-8 h-8 text-gray-300"}),(0,r.jsx)("span",{className:"text-sm",children:""===p.trim()?"ابدأ بالكتابة للبحث عن عضو":"لا توجد نتائج مطابقة"})]})}):(0,r.jsx)(r.Fragment,{children:x.map(e=>(0,r.jsx)("div",{className:"p-4 hover:bg-diwan-50 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors duration-150",onClick:()=>F(e),children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-diwan-100 rounded-full flex items-center justify-center",children:(0,r.jsx)($.A,{className:"w-4 h-4 text-diwan-600"})}),(0,r.jsx)("span",{className:"font-medium text-gray-900",children:e.name})]})},e.id))})})]}),_.memberId&&(0,r.jsxs)("p",{className:"text-sm text-red-500 flex items-center gap-1",children:[(0,r.jsx)("span",{className:"w-1 h-1 bg-red-500 rounded-full"}),_.memberId.message]}),f&&(0,r.jsx)("div",{className:"mt-3 p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,r.jsx)($.A,{className:"w-4 h-4 text-green-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-green-800 font-semibold",children:f.name}),(0,r.jsx)("p",{className:"text-green-600 text-xs",children:"العضو المحدد"})]})]}),(0,r.jsx)("button",{type:"button",onClick:U,className:"text-green-600 hover:text-red-500 transition-colors duration-200 p-1 rounded-full hover:bg-white/50",children:(0,r.jsx)(D.A,{className:"w-4 h-4"})})]})})]})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border border-gray-100",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-indigo-600 rounded-full"}),"معلومات إضافية"]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(C.J,{htmlFor:"description",className:"text-sm font-medium text-gray-700",children:"الوصف (اختياري)"}),(0,r.jsx)(d.p,{id:"description",...O("description"),placeholder:"وصف إضافي للإيراد",className:"h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-100 transition-all duration-200"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(C.J,{htmlFor:"notes",className:"text-sm font-medium text-gray-700",children:"ملاحظات (اختياري)"}),(0,r.jsx)(A.T,{id:"notes",...O("notes"),placeholder:"أي ملاحظات إضافية حول هذا الإيراد",rows:4,className:"border-gray-200 focus:border-diwan-500 focus:ring-diwan-100 transition-all duration-200 resize-none"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border border-gray-100 mt-6",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row justify-end gap-4 sm:gap-3",children:[(0,r.jsxs)(i.$,{type:"button",variant:"outline",onClick:()=>s(!1),disabled:o,className:"group relative h-14 px-8 border-2 border-gray-300 text-gray-700 hover:border-red-400 hover:text-red-600 bg-white hover:bg-red-50 transition-all duration-300 font-semibold rounded-xl shadow-sm hover:shadow-md transform hover:scale-[1.02] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-5 h-5 rounded-full bg-gray-200 group-hover:bg-red-200 transition-colors duration-300 flex items-center justify-center",children:(0,r.jsx)(D.A,{className:"w-3 h-3 text-gray-600 group-hover:text-red-600 transition-colors duration-300"})}),(0,r.jsx)("span",{className:"text-base",children:"إلغاء"})]}),(0,r.jsx)("div",{className:"absolute inset-0 rounded-xl bg-gradient-to-r from-red-50 to-pink-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"})]}),(0,r.jsxs)(i.$,{type:"submit",disabled:o,className:"group relative h-14 px-10 bg-gradient-to-r from-diwan-600 via-diwan-700 to-blue-600 hover:from-diwan-700 hover:via-diwan-800 hover:to-blue-700 text-white font-bold rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] disabled:opacity-70 disabled:cursor-not-allowed disabled:transform-none overflow-hidden",children:[o?(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"w-5 h-5 border-3 border-white/30 border-t-white rounded-full animate-spin"}),(0,r.jsx)("div",{className:"absolute inset-0 w-5 h-5 border-3 border-transparent border-t-white/60 rounded-full animate-ping"})]}),(0,r.jsx)("span",{className:"text-base",children:"جاري الحفظ..."})]}):(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center group-hover:bg-white/30 transition-all duration-300",children:(0,r.jsx)(u.A,{className:"w-4 h-4 text-white"})}),(0,r.jsx)("span",{className:"text-base",children:l?"حفظ التعديلات":"حفظ الإيراد"}),(0,r.jsx)("div",{className:"w-2 h-2 rounded-full bg-white/40 group-hover:bg-white/60 transition-all duration-300"})]}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 transform translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 ease-out"}),(0,r.jsx)("div",{className:"absolute inset-0 rounded-xl bg-gradient-to-r from-diwan-400/20 via-blue-400/20 to-indigo-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl -z-10"})]})]}),(0,r.jsx)("div",{className:"flex items-center justify-center mt-4 pt-4 border-t border-gray-100",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 rounded-full bg-diwan-200"}),(0,r.jsx)("div",{className:"w-1 h-1 rounded-full bg-diwan-300"}),(0,r.jsx)("div",{className:"w-2 h-2 rounded-full bg-diwan-200"})]})})]})]})]})})}var P=a(4403),z=a(94424);function R(){let{data:e}=(0,l.useSession)(),[s,a]=(0,t.useState)([]),[y,w]=(0,t.useState)(!0),[k,C]=(0,t.useState)(""),[A,T]=(0,t.useState)("all"),[S,D]=(0,t.useState)({page:1,limit:10,total:0,pages:0}),[$,I]=(0,t.useState)({totalAmount:0,totalCount:0,byType:[]}),[R,E]=(0,t.useState)(!1),[_,q]=(0,t.useState)(null),F=(0,t.useCallback)(async()=>{try{w(!0);let e=new URLSearchParams({search:k,type:A,page:S.page.toString(),limit:S.limit.toString()}),s=await fetch(`/api/incomes?${e}`);if(!s.ok)throw Error("فشل في جلب الإيرادات");let r=await s.json();a(r.incomes),D(r.pagination)}catch(e){console.error("خطأ في جلب الإيرادات:",e)}finally{w(!1)}},[k,A,S.page,S.limit]),U=async()=>{try{let e=await fetch("/api/incomes?limit=1000");if(!e.ok)return;let s=await e.json(),a=s.incomes.reduce((e,s)=>e+s.amount,0),r=s.incomes.length,t=s.incomes.reduce((e,s)=>{let a=e.find(e=>e.type===s.type);return a?(a._sum.amount+=s.amount,a._count+=1):e.push({type:s.type,_sum:{amount:s.amount},_count:1}),e},[]);I({totalAmount:a,totalCount:r,byType:t})}catch(e){console.error("خطأ في جلب الإحصائيات:",e)}},B=e=>{q(e),E(!0)},H=async e=>{if(confirm("هل أنت متأكد من حذف هذا الإيراد؟\n\nهذا الإجراء لا يمكن التراجع عنه."))try{let s=await fetch(`/api/incomes/${e}`,{method:"DELETE"});if(!s.ok){let e=await s.json();alert(e.error||"فشل في حذف الإيراد");return}alert("تم حذف الإيراد بنجاح"),F(),U()}catch(e){console.error("خطأ في حذف الإيراد:",e),alert("حدث خطأ في حذف الإيراد")}},Z=async()=>{try{let e=await fetch("/api/incomes?limit=1000");if(!e.ok)throw Error("فشل في جلب البيانات");let s=(await e.json()).incomes,a=s.reduce((e,s)=>e+s.amount,0),r=s.length,t=s.reduce((e,s)=>{let a=e.find(e=>e.type===s.type);return a?(a.amount+=s.amount,a.count+=1):e.push({type:s.type,amount:s.amount,count:1}),e},[]),l=new Date().toLocaleDateString("ar-SA",{year:"numeric",month:"long",day:"numeric"}),i=`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
          <meta charset="UTF-8">
          <style>
            @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;600;700&display=swap');

            * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
            }

            body {
              font-family: 'Noto Sans Arabic', Arial, sans-serif;
              direction: rtl;
              background: white;
              color: #333;
              line-height: 1.6;
              padding: 20px;
            }

            .header {
              text-align: center;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              padding: 30px;
              border-radius: 15px;
              margin-bottom: 30px;
              box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            }

            .header h1 {
              font-size: 32px;
              font-weight: 700;
              margin-bottom: 10px;
            }

            .header h2 {
              font-size: 24px;
              font-weight: 600;
              margin-bottom: 15px;
            }

            .header .date {
              font-size: 16px;
              opacity: 0.9;
            }

            .stats {
              display: grid;
              grid-template-columns: repeat(3, 1fr);
              gap: 20px;
              margin-bottom: 30px;
            }

            .stat-card {
              background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
              color: white;
              padding: 25px;
              border-radius: 15px;
              text-align: center;
              box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            }

            .stat-card.green {
              background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            }

            .stat-card.blue {
              background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            }

            .stat-card h3 {
              font-size: 16px;
              margin-bottom: 10px;
              font-weight: 600;
            }

            .stat-card .value {
              font-size: 24px;
              font-weight: 700;
            }

            .table-container {
              background: white;
              border-radius: 15px;
              overflow: hidden;
              box-shadow: 0 10px 30px rgba(0,0,0,0.1);
              margin-bottom: 30px;
            }

            .table-header {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              padding: 20px;
              text-align: center;
            }

            .table-header h3 {
              font-size: 20px;
              font-weight: 600;
            }

            table {
              width: 100%;
              border-collapse: collapse;
              font-size: 14px;
            }

            th {
              background: #f8f9fa;
              padding: 15px 10px;
              text-align: center;
              font-weight: 600;
              color: #495057;
              border-bottom: 2px solid #dee2e6;
            }

            td {
              padding: 12px 10px;
              text-align: center;
              border-bottom: 1px solid #dee2e6;
            }

            tr:nth-child(even) {
              background-color: #f8f9fa;
            }

            .amount {
              font-weight: 700;
              color: #28a745;
            }

            .type-badge {
              padding: 5px 12px;
              border-radius: 20px;
              font-size: 12px;
              font-weight: 600;
              color: white;
            }

            .type-subscription { background: #007bff; }
            .type-donation { background: #28a745; }
            .type-event { background: #6f42c1; }
            .type-other { background: #6c757d; }

            .summary {
              background: white;
              border-radius: 15px;
              padding: 25px;
              box-shadow: 0 10px 30px rgba(0,0,0,0.1);
              margin-bottom: 30px;
            }

            .summary h3 {
              color: #495057;
              margin-bottom: 20px;
              font-size: 18px;
              font-weight: 600;
              text-align: center;
            }

            .summary-grid {
              display: grid;
              grid-template-columns: repeat(2, 1fr);
              gap: 15px;
            }

            .summary-item {
              background: #f8f9fa;
              padding: 15px;
              border-radius: 10px;
              border-right: 4px solid #007bff;
            }

            .summary-item .label {
              font-weight: 600;
              color: #495057;
              margin-bottom: 5px;
            }

            .summary-item .value {
              font-size: 16px;
              font-weight: 700;
              color: #007bff;
            }

            .footer {
              text-align: center;
              margin-top: 40px;
              padding: 20px;
              background: #f8f9fa;
              border-radius: 10px;
              color: #6c757d;
            }

            @media print {
              body { padding: 0; }
              .header { margin-bottom: 20px; }
              .stats { margin-bottom: 20px; }
              .table-container { margin-bottom: 20px; }
              .summary { margin-bottom: 20px; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>تقرير الإيرادات</h1>
            <h2>ديوان أبو علوش</h2>
            <div class="date">تاريخ التقرير: ${l}</div>
          </div>

          <div class="stats">
            <div class="stat-card">
              <h3>إجمالي الإيرادات</h3>
              <div class="value">${(0,N.vv)(a)}</div>
            </div>
            <div class="stat-card green">
              <h3>عدد الإيرادات</h3>
              <div class="value">${r} إيراد</div>
            </div>
            <div class="stat-card blue">
              <h3>متوسط الإيراد</h3>
              <div class="value">${(0,N.vv)(r>0?a/r:0)}</div>
            </div>
          </div>

          <div class="table-container">
            <div class="table-header">
              <h3>تفاصيل الإيرادات</h3>
            </div>
            <table>
              <thead>
                <tr>
                  <th>المصدر</th>
                  <th>المبلغ (د.أ)</th>
                  <th>النوع</th>
                  <th>العضو</th>
                  <th>التاريخ</th>
                </tr>
              </thead>
              <tbody>
                ${s.map(e=>`
                  <tr>
                    <td>${e.source}</td>
                    <td class="amount">${e.amount.toFixed(2)}</td>
                    <td>
                      <span class="type-badge type-${e.type.toLowerCase()}">
                        ${(0,N.Tk)(e.type)}
                      </span>
                    </td>
                    <td>${e.member?.name||"غير محدد"}</td>
                    <td>${(0,N.Yq)(e.date)}</td>
                  </tr>
                `).join("")}
              </tbody>
            </table>
          </div>

          <div class="summary">
            <h3>ملخص الإيرادات حسب النوع</h3>
            <div class="summary-grid">
              ${t.map(e=>`
                <div class="summary-item">
                  <div class="label">${(0,N.Tk)(e.type)}</div>
                  <div class="value">${(0,N.vv)(e.amount)} (${e.count} إيراد)</div>
                </div>
              `).join("")}
            </div>
          </div>

          <div class="footer">
            <p>ديوان أبو علوش - نظام إدارة العائلة</p>
            <p>تم إنشاء هذا التقرير في ${l}</p>
          </div>
        </body>
        </html>
      `,d=document.createElement("div");d.innerHTML=i,d.style.position="absolute",d.style.left="-9999px",d.style.top="0",d.style.width="210mm",document.body.appendChild(d),await new Promise(e=>setTimeout(e,1e3));let o=await (0,z.default)(d,{scale:2,useCORS:!0,allowTaint:!0,backgroundColor:"#ffffff",width:794,height:1123});document.body.removeChild(d);let n=new P.default("p","mm","a4"),c=o.toDataURL("image/png"),m=210*o.height/o.width,x=m,h=0;for(n.addImage(c,"PNG",0,h,210,m),x-=297;x>=0;)h=x-m,n.addPage(),n.addImage(c,"PNG",0,h,210,m),x-=297;let u=`تقرير_الإيرادات_${new Date().toISOString().split("T")[0]}.pdf`;n.save(u),setTimeout(()=>{alert("تم إنشاء تقرير PDF بنجاح! ✅")},500)}catch(e){console.error("خطأ في تصدير PDF:",e),alert("حدث خطأ في تصدير التقرير ❌")}},G=e?.user.role!=="VIEWER",J=e?.user.role==="ADMIN";return(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsx)("div",{className:"text-center mb-8",children:(0,r.jsxs)("div",{className:"bg-gradient-to-r from-green-600 to-emerald-600 rounded-3xl shadow-2xl p-10 text-white relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-500 opacity-30 animate-pulse"}),(0,r.jsxs)("div",{className:"relative z-10",children:[(0,r.jsx)("div",{className:"inline-flex items-center justify-center w-20 h-20 rounded-full mb-6 bg-white bg-opacity-20 backdrop-blur-sm",children:(0,r.jsx)(m.A,{className:"w-10 h-10 text-white"})}),(0,r.jsx)("h1",{className:"text-5xl font-black mb-4 text-white",children:"إدارة الإيرادات"}),(0,r.jsx)("p",{className:"text-xl font-semibold mb-6 text-green-100",children:"عرض وإدارة إيرادات الديوان بكفاءة وسهولة"}),(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2 space-x-reverse",children:[(0,r.jsx)("div",{className:"h-1 w-16 rounded-full bg-white bg-opacity-60"}),(0,r.jsx)("div",{className:"h-1 w-8 rounded-full bg-white bg-opacity-40"}),(0,r.jsx)("div",{className:"h-1 w-16 rounded-full bg-white bg-opacity-60"})]})]})]})}),(0,r.jsx)("div",{className:"flex justify-center mb-8",children:(0,r.jsx)("div",{className:"bg-white rounded-2xl shadow-xl p-6 border border-gray-100",children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-4 justify-center",children:[(0,r.jsxs)(i.$,{onClick:Z,className:"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold",children:[(0,r.jsx)(x.A,{className:"w-5 h-5 ml-2"}),"تصدير PDF"]}),G&&(0,r.jsxs)(i.$,{onClick:()=>{q(null),E(!0)},className:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold",children:[(0,r.jsx)(h.A,{className:"w-5 h-5 ml-2"}),"إضافة إيراد جديد"]})]})})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3",children:[(0,r.jsxs)(o.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-green-500 to-green-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-green-500 to-green-600 p-1 rounded-t-xl"}),(0,r.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,r.jsx)(o.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"إجمالي الإيرادات"}),(0,r.jsx)("div",{className:"p-4 rounded-2xl shadow-lg",style:{backgroundColor:"#28a745"},children:(0,r.jsx)(u.A,{className:"h-7 w-7 text-white"})})]}),(0,r.jsxs)(o.Wu,{className:"relative z-10",children:[(0,r.jsx)("div",{className:"text-4xl font-black mb-2",style:{color:"#191970"},children:(0,N.vv)($.totalAmount)}),(0,r.jsx)("p",{className:"text-sm font-semibold",style:{color:"#6c757d"},children:"إجمالي المبالغ"})]})]}),(0,r.jsxs)(o.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-500 to-blue-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-blue-600 p-1 rounded-t-xl"}),(0,r.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,r.jsx)(o.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"عدد الإيرادات"}),(0,r.jsx)("div",{className:"p-4 rounded-2xl shadow-lg",style:{backgroundColor:"#007bff"},children:(0,r.jsx)(m.A,{className:"h-7 w-7 text-white"})})]}),(0,r.jsxs)(o.Wu,{className:"relative z-10",children:[(0,r.jsx)("div",{className:"text-4xl font-black mb-2",style:{color:"#191970"},children:$.totalCount}),(0,r.jsx)("p",{className:"text-sm font-semibold",style:{color:"#6c757d"},children:"إجمالي العمليات"})]})]}),(0,r.jsxs)(o.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative sm:col-span-2 lg:col-span-1",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-purple-500 to-purple-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-purple-600 p-1 rounded-t-xl"}),(0,r.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,r.jsx)(o.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"متوسط الإيراد"}),(0,r.jsx)("div",{className:"p-4 rounded-2xl shadow-lg",style:{backgroundColor:"#800020"},children:(0,r.jsx)(p.A,{className:"h-7 w-7 text-white"})})]}),(0,r.jsxs)(o.Wu,{className:"relative z-10",children:[(0,r.jsx)("div",{className:"text-4xl font-black mb-2",style:{color:"#191970"},children:(0,N.vv)($.totalCount>0?$.totalAmount/$.totalCount:0)}),(0,r.jsx)("p",{className:"text-sm font-semibold",style:{color:"#6c757d"},children:"متوسط القيمة"})]})]})]}),(0,r.jsxs)(o.Zp,{className:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden",children:[(0,r.jsxs)("div",{className:"bg-gradient-to-r from-gray-50 to-blue-50 p-6 border-b border-gray-100",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-diwan-600 rounded-full"}),"البحث والتصفية"]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:"ابحث وصفي الإيرادات حسب المعايير المختلفة"})]}),(0,r.jsx)(o.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)("div",{className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400",children:(0,r.jsx)(g.A,{className:"w-5 h-5"})}),(0,r.jsx)(d.p,{placeholder:"البحث في الإيرادات (المصدر، الوصف، العضو...)",value:k,onChange:e=>C(e.target.value),className:"h-12 pr-12 text-base border-gray-200 focus:border-diwan-500 focus:ring-diwan-100 transition-all duration-200 rounded-xl"})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("select",{value:A,onChange:e=>T(e.target.value),className:"h-12 px-4 pr-10 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-diwan-500 focus:ring-4 focus:ring-diwan-100 transition-all duration-200 bg-white text-gray-700 font-medium min-w-[200px] appearance-none cursor-pointer",children:[(0,r.jsx)("option",{value:"all",children:"\uD83D\uDD0D جميع الأنواع"}),(0,r.jsx)("option",{value:"SUBSCRIPTION",children:"\uD83D\uDCB3 اشتراكات"}),(0,r.jsx)("option",{value:"DONATION",children:"\uD83D\uDC9D تبرعات"}),(0,r.jsx)("option",{value:"EVENT",children:"\uD83C\uDF89 فعاليات"}),(0,r.jsx)("option",{value:"OTHER",children:"\uD83D\uDCCB أخرى"})]}),(0,r.jsx)("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none",children:(0,r.jsx)(b.A,{className:"w-4 h-4 text-gray-400"})})]})]})})]}),(0,r.jsxs)(o.Zp,{className:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden",children:[(0,r.jsxs)("div",{className:"bg-gradient-to-r from-gray-50 to-blue-50 p-6 border-b border-gray-100",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-600 rounded-full"}),"قائمة الإيرادات"]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:"جميع إيرادات الديوان مع تفاصيلها الكاملة"})]}),(0,r.jsx)(o.Wu,{className:"p-0",children:y?(0,r.jsxs)("div",{className:"flex flex-col justify-center items-center h-64 bg-gray-50",children:[(0,r.jsx)("div",{className:"w-12 h-12 border-4 border-diwan-600 border-t-transparent rounded-full animate-spin mb-4"}),(0,r.jsx)("div",{className:"text-gray-600 font-medium",children:"جاري تحميل الإيرادات..."}),(0,r.jsx)("div",{className:"text-gray-400 text-sm mt-1",children:"يرجى الانتظار"})]}):0===s.length?(0,r.jsxs)("div",{className:"flex flex-col justify-center items-center h-64 bg-gray-50",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mb-4",children:(0,r.jsx)(m.A,{className:"w-8 h-8 text-gray-400"})}),(0,r.jsx)("div",{className:"text-gray-600 font-medium mb-2",children:"لا توجد إيرادات"}),(0,r.jsx)("div",{className:"text-gray-400 text-sm",children:"ابدأ بإضافة إيراد جديد"})]}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)(c.XI,{children:[(0,r.jsx)(c.A0,{children:(0,r.jsxs)(c.Hj,{className:"bg-gray-50 hover:bg-gray-50",children:[(0,r.jsx)(c.nd,{className:"font-semibold text-gray-700 py-4",children:"المصدر"}),(0,r.jsx)(c.nd,{className:"font-semibold text-gray-700 py-4",children:"المبلغ"}),(0,r.jsx)(c.nd,{className:"font-semibold text-gray-700 py-4",children:"النوع"}),(0,r.jsx)(c.nd,{className:"font-semibold text-gray-700 py-4",children:"العضو"}),(0,r.jsx)(c.nd,{className:"font-semibold text-gray-700 py-4",children:"التاريخ"}),(0,r.jsx)(c.nd,{className:"font-semibold text-gray-700 py-4 text-center",children:"الإجراءات"})]})}),(0,r.jsx)(c.BF,{children:s.map(e=>(0,r.jsxs)(c.Hj,{className:"hover:bg-blue-50/50 transition-colors duration-200 border-b border-gray-100",children:[(0,r.jsx)(c.nA,{className:"py-4",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-diwan-100 to-blue-100 rounded-xl flex items-center justify-center flex-shrink-0",children:(0,r.jsx)(u.A,{className:"w-5 h-5 text-diwan-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-semibold text-gray-900 mb-1",children:e.source}),e.description&&(0,r.jsx)("div",{className:"text-sm text-gray-500 leading-relaxed",children:e.description})]})]})}),(0,r.jsx)(c.nA,{className:"py-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full animate-pulse"}),(0,r.jsx)("div",{className:"font-bold text-lg text-green-600",children:(0,N.vv)(e.amount)})]})}),(0,r.jsx)(c.nA,{className:"py-4",children:(0,r.jsx)(n.E,{variant:"secondary",className:`px-3 py-1 rounded-full font-medium ${"SUBSCRIPTION"===e.type?"bg-blue-100 text-blue-700 border-blue-200":"DONATION"===e.type?"bg-green-100 text-green-700 border-green-200":"EVENT"===e.type?"bg-purple-100 text-purple-700 border-purple-200":"bg-gray-100 text-gray-700 border-gray-200"}`,children:(0,N.Tk)(e.type)})}),(0,r.jsx)(c.nA,{className:"py-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(p.A,{className:"w-4 h-4 text-gray-600"})}),(0,r.jsx)("span",{className:"font-medium text-gray-700",children:e.member?.name||"غير محدد"})]})}),(0,r.jsx)(c.nA,{className:"py-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-2 text-gray-600",children:[(0,r.jsx)(f.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"font-medium",children:(0,N.Yq)(e.date)})]})}),(0,r.jsx)(c.nA,{className:"py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[G&&(0,r.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>B(e),className:"h-9 w-9 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-all duration-200",title:"تعديل",children:(0,r.jsx)(v.A,{className:"w-4 h-4"})}),J&&(0,r.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>H(e.id),className:"h-9 w-9 p-0 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-all duration-200",title:"حذف",children:(0,r.jsx)(j.A,{className:"w-4 h-4"})})]})})]},e.id))})]})})})]}),S.pages>1&&(0,r.jsx)(o.Zp,{className:"bg-white rounded-2xl shadow-lg border border-gray-100",children:(0,r.jsx)(o.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-center gap-4",children:[(0,r.jsx)("div",{className:"flex items-center gap-2 text-gray-600",children:(0,r.jsxs)("span",{className:"text-sm font-medium",children:["عرض ",(S.page-1)*S.limit+1," - ",Math.min(S.page*S.limit,S.total)," من ",S.total," إيراد"]})}),(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(i.$,{variant:"outline",disabled:1===S.page,onClick:()=>D(e=>({...e,page:e.page-1})),className:"h-10 px-4 border-2 border-gray-200 hover:border-diwan-500 hover:text-diwan-600 transition-all duration-200 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed",children:(0,r.jsx)("span",{className:"font-medium",children:"السابق"})}),(0,r.jsx)("div",{className:"flex items-center gap-2",children:Array.from({length:Math.min(5,S.pages)},(e,s)=>{let a=s+1;return(0,r.jsx)("button",{onClick:()=>D(e=>({...e,page:a})),className:`w-10 h-10 rounded-lg font-semibold transition-all duration-200 ${S.page===a?"bg-diwan-600 text-white shadow-lg":"bg-gray-100 text-gray-600 hover:bg-diwan-100 hover:text-diwan-600"}`,children:a},a)})}),(0,r.jsx)(i.$,{variant:"outline",disabled:S.page===S.pages,onClick:()=>D(e=>({...e,page:e.page+1})),className:"h-10 px-4 border-2 border-gray-200 hover:border-diwan-500 hover:text-diwan-600 transition-all duration-200 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed",children:(0,r.jsx)("span",{className:"font-medium",children:"التالي"})})]})]})})}),$.byType.length>0&&(0,r.jsxs)(o.Zp,{className:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden",children:[(0,r.jsxs)("div",{className:"bg-gradient-to-r from-gray-50 to-purple-50 p-6 border-b border-gray-100",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-purple-600 rounded-full"}),"الإيرادات حسب النوع"]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:"توزيع الإيرادات على الأنواع المختلفة"})]}),(0,r.jsx)(o.Wu,{className:"p-6",children:(0,r.jsx)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4",children:$.byType.map((e,s)=>{let a=[{bg:"from-blue-50 to-indigo-50",border:"border-blue-200",text:"text-blue-700",icon:"bg-blue-100",dot:"bg-blue-500"},{bg:"from-green-50 to-emerald-50",border:"border-green-200",text:"text-green-700",icon:"bg-green-100",dot:"bg-green-500"},{bg:"from-purple-50 to-pink-50",border:"border-purple-200",text:"text-purple-700",icon:"bg-purple-100",dot:"bg-purple-500"},{bg:"from-orange-50 to-red-50",border:"border-orange-200",text:"text-orange-700",icon:"bg-orange-100",dot:"bg-orange-500"}],t=a[s%a.length];return(0,r.jsxs)("div",{className:`relative overflow-hidden bg-gradient-to-br ${t.bg} ${t.border} border-2 rounded-2xl p-6 hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02]`,children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:`p-3 ${t.icon} rounded-xl shadow-sm`,children:["SUBSCRIPTION"===e.type&&(0,r.jsx)(p.A,{className:"w-5 h-5 text-blue-600"}),"DONATION"===e.type&&(0,r.jsx)(u.A,{className:"w-5 h-5 text-green-600"}),"EVENT"===e.type&&(0,r.jsx)(f.A,{className:"w-5 h-5 text-purple-600"}),"OTHER"===e.type&&(0,r.jsx)(m.A,{className:"w-5 h-5 text-orange-600"})]}),(0,r.jsx)("div",{className:`w-3 h-3 ${t.dot} rounded-full animate-pulse`})]}),(0,r.jsx)("div",{className:"text-sm font-semibold text-gray-600 mb-2",children:(0,N.Tk)(e.type)}),(0,r.jsx)("div",{className:`text-2xl font-bold ${t.text} mb-2`,children:(0,N.vv)(e._sum.amount||0)}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:`w-2 h-2 ${t.dot} rounded-full`}),(0,r.jsxs)("span",{className:"text-sm text-gray-500 font-medium",children:[e._count," إيراد"]})]})]})]},e.type)})})})]}),(0,r.jsx)(O,{open:R,onOpenChange:()=>{E(!1),q(null)},income:_,onSuccess:()=>{F(),U()}})]})}},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80013:(e,s,a)=>{"use strict";a.d(s,{J:()=>i});var r=a(60687),t=a(43210),l=a(4780);let i=t.forwardRef(({className:e,...s},a)=>(0,r.jsx)("label",{ref:a,className:(0,l.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e),...s}));i.displayName="Label"},81630:e=>{"use strict";e.exports=require("http")},92091:(e,s,a)=>{Promise.resolve().then(a.bind(a,70855))},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),r=s.X(0,[4243,5663,4999,3412,5442,7934,5498,1726,2131,5662,2635,4403,6329,5977,6154],()=>a(49144));module.exports=r})();