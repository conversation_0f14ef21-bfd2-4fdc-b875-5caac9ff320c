'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { useAppearanceSettings } from '@/hooks/use-appearance-settings'
import {
  Building2,
  Home,
  Users,
  TrendingUp,
  TrendingDown,
  Image,
  FileText,
  Settings,
  Bell,
  // BarChart3,
} from 'lucide-react'

const navigation = [
  { name: 'لوحة التحكم', href: '/dashboard', icon: Home },
  { name: 'الأعضاء', href: '/dashboard/members', icon: Users },
  { name: 'الإيرادات', href: '/dashboard/incomes', icon: TrendingUp },
  { name: 'المصروفات', href: '/dashboard/expenses', icon: TrendingDown },
  { name: 'معرض الصور', href: '/dashboard/gallery', icon: Image },
  { name: 'الإشعارات', href: '/dashboard/notifications', icon: Bell },
  { name: 'التقارير', href: '/dashboard/reports', icon: FileText },
  { name: 'التقارير المتقدمة', href: '/dashboard/reports-advanced', icon: FileText },
  { name: 'الإعدادات', href: '/dashboard/settings', icon: Settings },
]

export default function Sidebar() {
  const pathname = usePathname()
  const { settings } = useAppearanceSettings()

  return (
    <div className="fixed inset-y-0 right-0 z-50 w-64 shadow-2xl lg:block hidden sidebar bg-gradient-to-b from-slate-900 to-slate-800">
      {/* رأس الشريط الجانبي */}
      <div className="flex h-20 items-center justify-center border-b border-slate-700 bg-gradient-to-r from-slate-800 to-slate-900">
        <div className="flex items-center space-x-3 space-x-reverse">
          {settings.logo ? (
            <div className="p-2 rounded-xl bg-white bg-opacity-10 backdrop-blur-sm">
              <img 
                src={settings.logo} 
                alt="الشعار" 
                className="h-10 w-10 object-contain"
              />
            </div>
          ) : (
            <div className="p-2 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg">
              <Building2 className="h-10 w-10 text-white" />
            </div>
          )}
          <div className="text-right">
            <h1 className="text-lg font-bold text-white leading-tight">{settings.brandName || 'ديوان آل أبو علوش'}</h1>
            <p className="text-xs text-slate-300 font-medium">نظام الإدارة</p>
          </div>
        </div>
      </div>
      
      {/* القائمة الرئيسية */}
      <nav className="mt-8 px-4">
        <ul className="space-y-2">
          {navigation.map((item) => {
            const isActive = pathname === item.href
            return (
              <li key={item.name}>
                <Link
                  href={item.href}
                  className={cn(
                    'group flex items-center rounded-xl px-4 py-3 text-sm font-semibold transition-all duration-300 relative overflow-hidden',
                    isActive
                      ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg transform scale-105'
                      : 'text-slate-300 hover:bg-slate-700 hover:text-white hover:transform hover:scale-105'
                  )}
                >
                  {/* خلفية متحركة للعنصر النشط */}
                  {isActive && (
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-blue-500 opacity-20 animate-pulse"></div>
                  )}
                  
                  <item.icon
                    className={cn(
                      'ml-3 h-5 w-5 flex-shrink-0 transition-all duration-300',
                      isActive ? 'text-white' : 'text-slate-400 group-hover:text-white'
                    )}
                  />
                  <span className="relative z-10">{item.name}</span>
                  
                  {/* مؤشر العنصر النشط */}
                  {isActive && (
                    <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-white rounded-r-full"></div>
                  )}
                </Link>
              </li>
            )
          })}
        </ul>
      </nav>
      
      {/* تذييل الشريط الجانبي */}
      <div className="absolute bottom-0 w-full p-6 border-t border-slate-700 bg-gradient-to-r from-slate-800 to-slate-900">
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 mb-3">
            <span className="text-white font-bold text-sm">1.0</span>
          </div>
          <p className="text-xs text-slate-400 font-medium">الإصدار 1.0.0</p>
          <p className="text-xs text-slate-500 mt-1">© 2024 ديوان آل أبو علوش</p>
        </div>
      </div>
    </div>
  )
}
