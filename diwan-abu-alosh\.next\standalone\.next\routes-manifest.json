{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}], "regex": "^(?:/(.*))(?:/)?$"}], "dynamicRoutes": [{"page": "/api/activities/[id]", "regex": "^/api/activities/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/activities/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/admin/users/[id]", "regex": "^/api/admin/users/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/users/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/auth/[...next<PERSON>h]", "regex": "^/api/auth/(.+?)(?:/)?$", "routeKeys": {"nxtPnextauth": "nxtPnextauth"}, "namedRegex": "^/api/auth/(?<nxtPnextauth>.+?)(?:/)?$"}, {"page": "/api/expenses/[id]", "regex": "^/api/expenses/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/expenses/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/gallery/[id]", "regex": "^/api/gallery/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/gallery/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/gallery-folders/[id]", "regex": "^/api/gallery\\-folders/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/gallery\\-folders/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/incomes/[id]", "regex": "^/api/incomes/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/incomes/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/member/gallery/[id]", "regex": "^/api/member/gallery/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/member/gallery/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/members/[id]", "regex": "^/api/members/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/members/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/members/[id]/account-statement", "regex": "^/api/members/([^/]+?)/account\\-statement(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/members/(?<nxtPid>[^/]+?)/account\\-statement(?:/)?$"}, {"page": "/api/members/[id]/password", "regex": "^/api/members/([^/]+?)/password(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/members/(?<nxtPid>[^/]+?)/password(?:/)?$"}, {"page": "/api/members/[id]/quick-stats", "regex": "^/api/members/([^/]+?)/quick\\-stats(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/members/(?<nxtPid>[^/]+?)/quick\\-stats(?:/)?$"}, {"page": "/dashboard/gallery/folder/[id]", "regex": "^/dashboard/gallery/folder/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/gallery/folder/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/auth/clear-session", "regex": "^/auth/clear\\-session(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/clear\\-session(?:/)?$"}, {"page": "/auth/signin", "regex": "^/auth/signin(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/signin(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/dashboard/expenses", "regex": "^/dashboard/expenses(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/expenses(?:/)?$"}, {"page": "/dashboard/gallery", "regex": "^/dashboard/gallery(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/gallery(?:/)?$"}, {"page": "/dashboard/incomes", "regex": "^/dashboard/incomes(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/incomes(?:/)?$"}, {"page": "/dashboard/members", "regex": "^/dashboard/members(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/members(?:/)?$"}, {"page": "/dashboard/members-simple", "regex": "^/dashboard/members\\-simple(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/members\\-simple(?:/)?$"}, {"page": "/dashboard/notifications", "regex": "^/dashboard/notifications(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/notifications(?:/)?$"}, {"page": "/dashboard/reports", "regex": "^/dashboard/reports(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/reports(?:/)?$"}, {"page": "/dashboard/reports-advanced", "regex": "^/dashboard/reports\\-advanced(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/reports\\-advanced(?:/)?$"}, {"page": "/dashboard/settings", "regex": "^/dashboard/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/settings(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/member/account-statement", "regex": "^/member/account\\-statement(?:/)?$", "routeKeys": {}, "namedRegex": "^/member/account\\-statement(?:/)?$"}, {"page": "/member/dashboard", "regex": "^/member/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/member/dashboard(?:/)?$"}, {"page": "/member/gallery", "regex": "^/member/gallery(?:/)?$", "routeKeys": {}, "namedRegex": "^/member/gallery(?:/)?$"}, {"page": "/member/login", "regex": "^/member/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/member/login(?:/)?$"}, {"page": "/member/signin", "regex": "^/member/signin(?:/)?$", "routeKeys": {}, "namedRegex": "^/member/signin(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}