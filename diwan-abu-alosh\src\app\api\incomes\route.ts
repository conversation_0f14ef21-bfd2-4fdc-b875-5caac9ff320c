import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { incomeSchema } from '@/lib/validations'
import bcrypt from 'bcryptjs'

// GET - جلب جميع الإيرادات مع البحث والتصفية
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search') || ''
    const type = searchParams.get('type') || 'all'
    const memberId = searchParams.get('memberId') || ''
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    // بناء شروط البحث
    const where: Record<string, unknown> = {}
    
    if (search) {
      where.OR = [
        { source: { contains: search } },
        { description: { contains: search } },
        { member: { name: { contains: search } } },
      ]
    }

    if (type !== 'all') {
      where.type = type
    }

    if (memberId) {
      where.memberId = memberId
    }

    // جلب الإيرادات مع العد الكلي
    const [incomes, total] = await Promise.all([
      prisma.income.findMany({
        where,
        skip,
        take: limit,
        orderBy: { date: 'desc' },
        include: {
          member: {
            select: {
              id: true,
              name: true,
            },
          },
          createdBy: {
            select: {
              name: true,
            },
          },
        },
      }),
      prisma.income.count({ where }),
    ])

    return NextResponse.json({
      incomes,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('خطأ في جلب الإيرادات:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في جلب الإيرادات' },
      { status: 500 }
    )
  }
}

// POST - إضافة إيراد جديد
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // التحقق من الصلاحيات
    if (session.user.role === 'VIEWER') {
      return NextResponse.json(
        { error: 'ليس لديك صلاحية لإضافة الإيرادات' },
        { status: 403 }
      )
    }

    const body = await request.json()
    
    // تحويل التاريخ من string إلى Date
    if (body.date) {
      body.date = new Date(body.date)
    }

    // تنظيف البيانات قبل التحقق
    const cleanedBody = {
      ...body,
      description: body.description?.trim() || undefined,
      notes: body.notes?.trim() || undefined,
      memberId: body.memberId === 'none' || !body.memberId ? undefined : body.memberId,
    }

    console.log('البيانات المنظفة قبل التحقق:', cleanedBody)

    // التحقق من صحة البيانات
    const validatedData = incomeSchema.parse(cleanedBody)

    // التحقق من وجود العضو إذا تم تحديده
    if (validatedData.memberId) {
      console.log('التحقق من العضو:', validatedData.memberId)
      const member = await prisma.member.findUnique({
        where: { id: validatedData.memberId },
      })
      if (!member) {
        console.log('العضو غير موجود:', validatedData.memberId)
        return NextResponse.json(
          { error: 'العضو المحدد غير موجود' },
          { status: 400 }
        )
      }
      console.log('العضو موجود:', member.name)
    } else {
      console.log('لا يوجد عضو محدد')
    }

    // التحقق من وجود المستخدم أو إنشاؤه إذا لم يكن موجوداً
    let user = await prisma.user.findUnique({
      where: { id: session.user.id },
    })

    if (!user) {
      // إنشاء المستخدم إذا لم يكن موجوداً
      const hashedPassword = await bcrypt.hash('123456', 10)

      user = await prisma.user.create({
        data: {
          id: session.user.id,
          email: session.user.email || '<EMAIL>',
          name: session.user.name || 'مدير النظام',
          password: hashedPassword,
          role: 'ADMIN'
        }
      })
      console.log('تم إنشاء مستخدم جديد:', user.email)
    }

    console.log('إنشاء إيراد جديد:', {
      ...validatedData,
      createdById: session.user.id,
      userId: user.id,
      userName: user.name
    })

    // إنشاء الإيراد الجديد
    const income = await prisma.income.create({
      data: {
        amount: validatedData.amount,
        date: validatedData.date,
        source: validatedData.source,
        type: validatedData.type,
        description: validatedData.description || null,
        notes: validatedData.notes || null,
        memberId: validatedData.memberId || null,
        createdById: session.user.id,
      },
      include: {
        member: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    })

    return NextResponse.json(income, { status: 201 })
  } catch (error: unknown) {
    console.error('خطأ في إضافة الإيراد:', error)

    if (error.name === 'ZodError') {
      console.log('أخطاء التحقق من البيانات:', error.errors)
      const errorMessages = (error as { errors: Array<{ path: string[]; message: string }> }).errors.map((err) => `${err.path.join('.')}: ${err.message}`).join(', ')
      return NextResponse.json(
        {
          error: `بيانات غير صحيحة: ${errorMessages}`,
          details: error.errors
        },
        { status: 400 }
      )
    }

    // خطأ في قاعدة البيانات
    if (error.code === 'P2003') {
      return NextResponse.json(
        { error: 'خطأ في المرجع: تأكد من صحة معرف العضو أو المستخدم' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'حدث خطأ في إضافة الإيراد: ' + error.message },
      { status: 500 }
    )
  }
}
