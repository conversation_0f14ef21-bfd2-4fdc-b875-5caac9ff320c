{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Cred<PERSON><PERSON><PERSON><PERSON>ider from 'next-auth/providers/credentials'\nimport bcrypt from 'bcryptjs'\nimport { prisma } from './prisma'\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'البريد الإلكتروني', type: 'email' },\n        password: { label: 'كلمة المرور', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          role: user.role,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAqB,MAAM;gBAAQ;gBACnD,UAAU;oBAAE,OAAO;oBAAe,MAAM;gBAAW;YACrD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;YACxB;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;YAChC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/app/api/reports/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\n\n// دالة لحساب نطاق التاريخ بناءً على الفترة المحددة\nconst getDateRange = (period: string) => {\n  const now = new Date()\n  const currentYear = now.getFullYear()\n  const currentMonth = now.getMonth()\n\n  switch (period) {\n    case 'current-month':\n      return {\n        startDate: new Date(currentYear, currentMonth, 1),\n        endDate: new Date(currentYear, currentMonth + 1, 0, 23, 59, 59)\n      }\n    case 'last-month':\n      return {\n        startDate: new Date(currentYear, currentMonth - 1, 1),\n        endDate: new Date(currentYear, currentMonth, 0, 23, 59, 59)\n      }\n    case 'current-year':\n      return {\n        startDate: new Date(currentYear, 0, 1),\n        endDate: new Date(currentYear, 11, 31, 23, 59, 59)\n      }\n    case 'last-year':\n      return {\n        startDate: new Date(currentYear - 1, 0, 1),\n        endDate: new Date(currentYear - 1, 11, 31, 23, 59, 59)\n      }\n    case 'all-time':\n    default:\n      return {\n        startDate: new Date(2020, 0, 1), // تاريخ بداية افتراضي\n        endDate: now\n      }\n  }\n}\n\n// دالة لجلب البيانات الشهرية\nconst getMonthlyData = async (startDate: Date, endDate: Date) => {\n  const monthlyData = []\n  const start = new Date(startDate.getFullYear(), startDate.getMonth(), 1)\n  const end = new Date(endDate.getFullYear(), endDate.getMonth() + 1, 0)\n\n  for (let d = new Date(start); d <= end; d.setMonth(d.getMonth() + 1)) {\n    const monthStart = new Date(d.getFullYear(), d.getMonth(), 1)\n    const monthEnd = new Date(d.getFullYear(), d.getMonth() + 1, 0, 23, 59, 59)\n\n    const [incomesData, expensesData] = await Promise.all([\n      prisma.income.aggregate({\n        where: {\n          date: {\n            gte: monthStart,\n            lte: monthEnd\n          }\n        },\n        _sum: { amount: true }\n      }),\n      prisma.expense.aggregate({\n        where: {\n          date: {\n            gte: monthStart,\n            lte: monthEnd\n          }\n        },\n        _sum: { amount: true }\n      })\n    ])\n\n    const incomes = incomesData._sum.amount || 0\n    const expenses = expensesData._sum.amount || 0\n\n    monthlyData.push({\n      month: `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}`,\n      incomes,\n      expenses,\n      balance: incomes - expenses\n    })\n  }\n\n  return monthlyData\n}\n\n// دالة لجلب البيانات الحقيقية من قاعدة البيانات\nconst generateReportsData = async (period: string) => {\n  const { startDate, endDate } = getDateRange(period)\n\n  // جلب إحصائيات الأعضاء\n  const [totalMembers, activeMembers] = await Promise.all([\n    prisma.member.count(),\n    prisma.member.count({\n      where: { status: 'ACTIVE' }\n    })\n  ])\n\n  // جلب إحصائيات الإيرادات والمصروفات\n  const [incomesData, expensesData] = await Promise.all([\n    prisma.income.aggregate({\n      where: {\n        date: {\n          gte: startDate,\n          lte: endDate\n        }\n      },\n      _sum: { amount: true },\n      _count: true\n    }),\n    prisma.expense.aggregate({\n      where: {\n        date: {\n          gte: startDate,\n          lte: endDate\n        }\n      },\n      _sum: { amount: true },\n      _count: true\n    })\n  ])\n\n  const totalIncomes = incomesData._sum.amount || 0\n  const totalExpenses = expensesData._sum.amount || 0\n  const balance = totalIncomes - totalExpenses\n\n  // جلب عدد الأنشطة\n  const totalActivities = await prisma.activity.count({\n    where: {\n      startDate: {\n        gte: startDate,\n        lte: endDate\n      }\n    }\n  })\n\n  // جلب أكثر الأعضاء مساهمة\n  const topContributors = await prisma.member.findMany({\n    include: {\n      incomes: {\n        where: {\n          date: {\n            gte: startDate,\n            lte: endDate\n          }\n        },\n        select: { amount: true }\n      }\n    }\n  })\n\n  // حساب إجمالي المساهمات لكل عضو وترتيبهم\n  const topContributorsWithTotals = topContributors\n    .map(member => ({\n      id: member.id,\n      name: member.name,\n      totalContributions: member.incomes.reduce((sum, income) => sum + income.amount, 0),\n      incomes: member.incomes\n    }))\n    .filter(member => member.totalContributions > 0)\n    .sort((a, b) => b.totalContributions - a.totalContributions)\n    .slice(0, 10)\n\n  return {\n    totalMembers,\n    activeMembers,\n    totalIncomes,\n    totalExpenses,\n    balance,\n    totalActivities,\n    topContributors: topContributorsWithTotals,\n    startDate,\n    endDate\n  }\n}\n\n// GET - جلب بيانات التقارير\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    if (!session) {\n      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })\n    }\n\n    const { searchParams } = new URL(request.url)\n    const period = searchParams.get('period') || 'current-year'\n\n    // جلب البيانات الأساسية\n    const basicData = await generateReportsData(period)\n\n    // جلب آخر المعاملات (إيرادات ومصروفات)\n    const [recentIncomes, recentExpenses] = await Promise.all([\n      prisma.income.findMany({\n        where: {\n          date: {\n            gte: basicData.startDate,\n            lte: basicData.endDate\n          }\n        },\n        include: {\n          member: {\n            select: { name: true }\n          }\n        },\n        orderBy: { date: 'desc' },\n        take: 15\n      }),\n      prisma.expense.findMany({\n        where: {\n          date: {\n            gte: basicData.startDate,\n            lte: basicData.endDate\n          }\n        },\n        orderBy: { date: 'desc' },\n        take: 15\n      })\n    ])\n\n    // دمج المعاملات وترتيبها حسب التاريخ\n    const recentTransactions = [\n      ...recentIncomes.map(income => ({\n        type: 'income' as const,\n        id: income.id,\n        amount: income.amount,\n        date: income.date.toISOString(),\n        description: income.description || income.source,\n        memberName: income.member?.name\n      })),\n      ...recentExpenses.map(expense => ({\n        type: 'expense' as const,\n        id: expense.id,\n        amount: expense.amount,\n        date: expense.date.toISOString(),\n        description: expense.description,\n        memberName: expense.recipient\n      }))\n    ].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()).slice(0, 20)\n\n    // جلب البيانات الشهرية\n    const monthlyData = await getMonthlyData(basicData.startDate, basicData.endDate)\n\n    // تجميع البيانات النهائية\n    const reportsData = {\n      summary: {\n        totalMembers: basicData.totalMembers,\n        activeMembers: basicData.activeMembers,\n        totalIncomes: basicData.totalIncomes,\n        totalExpenses: basicData.totalExpenses,\n        balance: basicData.balance,\n        totalActivities: basicData.totalActivities\n      },\n      topContributors: basicData.topContributors,\n      recentTransactions,\n      monthlyData\n    }\n\n    return NextResponse.json(reportsData)\n  } catch (error) {\n    console.error('خطأ في جلب بيانات التقارير:', error)\n    return NextResponse.json(\n      { error: 'حدث خطأ في جلب بيانات التقارير' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,mDAAmD;AACnD,MAAM,eAAe,CAAC;IACpB,MAAM,MAAM,IAAI;IAChB,MAAM,cAAc,IAAI,WAAW;IACnC,MAAM,eAAe,IAAI,QAAQ;IAEjC,OAAQ;QACN,KAAK;YACH,OAAO;gBACL,WAAW,IAAI,KAAK,aAAa,cAAc;gBAC/C,SAAS,IAAI,KAAK,aAAa,eAAe,GAAG,GAAG,IAAI,IAAI;YAC9D;QACF,KAAK;YACH,OAAO;gBACL,WAAW,IAAI,KAAK,aAAa,eAAe,GAAG;gBACnD,SAAS,IAAI,KAAK,aAAa,cAAc,GAAG,IAAI,IAAI;YAC1D;QACF,KAAK;YACH,OAAO;gBACL,WAAW,IAAI,KAAK,aAAa,GAAG;gBACpC,SAAS,IAAI,KAAK,aAAa,IAAI,IAAI,IAAI,IAAI;YACjD;QACF,KAAK;YACH,OAAO;gBACL,WAAW,IAAI,KAAK,cAAc,GAAG,GAAG;gBACxC,SAAS,IAAI,KAAK,cAAc,GAAG,IAAI,IAAI,IAAI,IAAI;YACrD;QACF,KAAK;QACL;YACE,OAAO;gBACL,WAAW,IAAI,KAAK,MAAM,GAAG;gBAC7B,SAAS;YACX;IACJ;AACF;AAEA,6BAA6B;AAC7B,MAAM,iBAAiB,OAAO,WAAiB;IAC7C,MAAM,cAAc,EAAE;IACtB,MAAM,QAAQ,IAAI,KAAK,UAAU,WAAW,IAAI,UAAU,QAAQ,IAAI;IACtE,MAAM,MAAM,IAAI,KAAK,QAAQ,WAAW,IAAI,QAAQ,QAAQ,KAAK,GAAG;IAEpE,IAAK,IAAI,IAAI,IAAI,KAAK,QAAQ,KAAK,KAAK,EAAE,QAAQ,CAAC,EAAE,QAAQ,KAAK,GAAI;QACpE,MAAM,aAAa,IAAI,KAAK,EAAE,WAAW,IAAI,EAAE,QAAQ,IAAI;QAC3D,MAAM,WAAW,IAAI,KAAK,EAAE,WAAW,IAAI,EAAE,QAAQ,KAAK,GAAG,GAAG,IAAI,IAAI;QAExE,MAAM,CAAC,aAAa,aAAa,GAAG,MAAM,QAAQ,GAAG,CAAC;YACpD,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,SAAS,CAAC;gBACtB,OAAO;oBACL,MAAM;wBACJ,KAAK;wBACL,KAAK;oBACP;gBACF;gBACA,MAAM;oBAAE,QAAQ;gBAAK;YACvB;YACA,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,SAAS,CAAC;gBACvB,OAAO;oBACL,MAAM;wBACJ,KAAK;wBACL,KAAK;oBACP;gBACF;gBACA,MAAM;oBAAE,QAAQ;gBAAK;YACvB;SACD;QAED,MAAM,UAAU,YAAY,IAAI,CAAC,MAAM,IAAI;QAC3C,MAAM,WAAW,aAAa,IAAI,CAAC,MAAM,IAAI;QAE7C,YAAY,IAAI,CAAC;YACf,OAAO,GAAG,EAAE,WAAW,GAAG,CAAC,EAAE,OAAO,EAAE,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG,MAAM;YACxE;YACA;YACA,SAAS,UAAU;QACrB;IACF;IAEA,OAAO;AACT;AAEA,gDAAgD;AAChD,MAAM,sBAAsB,OAAO;IACjC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,aAAa;IAE5C,uBAAuB;IACvB,MAAM,CAAC,cAAc,cAAc,GAAG,MAAM,QAAQ,GAAG,CAAC;QACtD,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,KAAK;QACnB,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,KAAK,CAAC;YAClB,OAAO;gBAAE,QAAQ;YAAS;QAC5B;KACD;IAED,oCAAoC;IACpC,MAAM,CAAC,aAAa,aAAa,GAAG,MAAM,QAAQ,GAAG,CAAC;QACpD,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,SAAS,CAAC;YACtB,OAAO;gBACL,MAAM;oBACJ,KAAK;oBACL,KAAK;gBACP;YACF;YACA,MAAM;gBAAE,QAAQ;YAAK;YACrB,QAAQ;QACV;QACA,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,SAAS,CAAC;YACvB,OAAO;gBACL,MAAM;oBACJ,KAAK;oBACL,KAAK;gBACP;YACF;YACA,MAAM;gBAAE,QAAQ;YAAK;YACrB,QAAQ;QACV;KACD;IAED,MAAM,eAAe,YAAY,IAAI,CAAC,MAAM,IAAI;IAChD,MAAM,gBAAgB,aAAa,IAAI,CAAC,MAAM,IAAI;IAClD,MAAM,UAAU,eAAe;IAE/B,kBAAkB;IAClB,MAAM,kBAAkB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;QAClD,OAAO;YACL,WAAW;gBACT,KAAK;gBACL,KAAK;YACP;QACF;IACF;IAEA,0BAA0B;IAC1B,MAAM,kBAAkB,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;QACnD,SAAS;YACP,SAAS;gBACP,OAAO;oBACL,MAAM;wBACJ,KAAK;wBACL,KAAK;oBACP;gBACF;gBACA,QAAQ;oBAAE,QAAQ;gBAAK;YACzB;QACF;IACF;IAEA,yCAAyC;IACzC,MAAM,4BAA4B,gBAC/B,GAAG,CAAC,CAAA,SAAU,CAAC;YACd,IAAI,OAAO,EAAE;YACb,MAAM,OAAO,IAAI;YACjB,oBAAoB,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,MAAM,EAAE;YAChF,SAAS,OAAO,OAAO;QACzB,CAAC,GACA,MAAM,CAAC,CAAA,SAAU,OAAO,kBAAkB,GAAG,GAC7C,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,kBAAkB,GAAG,EAAE,kBAAkB,EAC1D,KAAK,CAAC,GAAG;IAEZ,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA,iBAAiB;QACjB;QACA;IACF;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAW,GAAG;gBAAE,QAAQ;YAAI;QAChE;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAE7C,wBAAwB;QACxB,MAAM,YAAY,MAAM,oBAAoB;QAE5C,uCAAuC;QACvC,MAAM,CAAC,eAAe,eAAe,GAAG,MAAM,QAAQ,GAAG,CAAC;YACxD,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;gBACrB,OAAO;oBACL,MAAM;wBACJ,KAAK,UAAU,SAAS;wBACxB,KAAK,UAAU,OAAO;oBACxB;gBACF;gBACA,SAAS;oBACP,QAAQ;wBACN,QAAQ;4BAAE,MAAM;wBAAK;oBACvB;gBACF;gBACA,SAAS;oBAAE,MAAM;gBAAO;gBACxB,MAAM;YACR;YACA,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;gBACtB,OAAO;oBACL,MAAM;wBACJ,KAAK,UAAU,SAAS;wBACxB,KAAK,UAAU,OAAO;oBACxB;gBACF;gBACA,SAAS;oBAAE,MAAM;gBAAO;gBACxB,MAAM;YACR;SACD;QAED,qCAAqC;QACrC,MAAM,qBAAqB;eACtB,cAAc,GAAG,CAAC,CAAA,SAAU,CAAC;oBAC9B,MAAM;oBACN,IAAI,OAAO,EAAE;oBACb,QAAQ,OAAO,MAAM;oBACrB,MAAM,OAAO,IAAI,CAAC,WAAW;oBAC7B,aAAa,OAAO,WAAW,IAAI,OAAO,MAAM;oBAChD,YAAY,OAAO,MAAM,EAAE;gBAC7B,CAAC;eACE,eAAe,GAAG,CAAC,CAAA,UAAW,CAAC;oBAChC,MAAM;oBACN,IAAI,QAAQ,EAAE;oBACd,QAAQ,QAAQ,MAAM;oBACtB,MAAM,QAAQ,IAAI,CAAC,WAAW;oBAC9B,aAAa,QAAQ,WAAW;oBAChC,YAAY,QAAQ,SAAS;gBAC/B,CAAC;SACF,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,IAAI,KAAK,CAAC,GAAG;QAEnF,uBAAuB;QACvB,MAAM,cAAc,MAAM,eAAe,UAAU,SAAS,EAAE,UAAU,OAAO;QAE/E,0BAA0B;QAC1B,MAAM,cAAc;YAClB,SAAS;gBACP,cAAc,UAAU,YAAY;gBACpC,eAAe,UAAU,aAAa;gBACtC,cAAc,UAAU,YAAY;gBACpC,eAAe,UAAU,aAAa;gBACtC,SAAS,UAAU,OAAO;gBAC1B,iBAAiB,UAAU,eAAe;YAC5C;YACA,iBAAiB,UAAU,eAAe;YAC1C;YACA;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAiC,GAC1C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}