(()=>{var e={};e.id=5813,e.ids=[5813],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>n});var s=r(13581),i=r(85663),a=r(31183);let n={providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"البريد الإلكتروني",type:"email"},password:{label:"كلمة المرور",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let t=await a.z.user.findUnique({where:{email:e.email}});return t&&await i.Ay.compare(e.password,t.password)?{id:t.id,email:t.email,name:t.name,role:t.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},26182:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>g,serverHooks:()=>y,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>w});var s={};r.r(s),r.d(s,{GET:()=>x,POST:()=>m});var i=r(96559),a=r(48088),n=r(37719),o=r(32190),u=r(19854),c=r(12909),p=r(31183),l=r(45697);let d=l.z.object({title:l.z.string().min(1,"عنوان النشاط مطلوب"),description:l.z.string().optional(),location:l.z.string().optional(),startDate:l.z.string().min(1,"تاريخ البداية مطلوب"),endDate:l.z.string().optional()});async function x(e){try{if(!await (0,u.getServerSession)(c.N))return o.NextResponse.json({error:"غير مصرح"},{status:401});let{searchParams:t}=new URL(e.url),r=t.get("search")||"",s=parseInt(t.get("page")||"1"),i=parseInt(t.get("limit")||"20"),a=(s-1)*i,n={};r&&(n.OR=[{title:{contains:r}},{description:{contains:r}},{location:{contains:r}}]);let l=await p.z.activity.findMany({where:n,include:{createdBy:{select:{name:!0}},_count:{select:{participants:!0,photos:!0}}},orderBy:{startDate:"desc"},skip:a,take:i}),d=await p.z.activity.count({where:n});return o.NextResponse.json({activities:l,pagination:{page:s,limit:i,total:d,pages:Math.ceil(d/i)}})}catch(e){return console.error("خطأ في جلب الأنشطة:",e),o.NextResponse.json({error:"حدث خطأ في جلب الأنشطة"},{status:500})}}async function m(e){try{let t=await (0,u.getServerSession)(c.N);if(!t)return o.NextResponse.json({error:"غير مصرح"},{status:401});if("VIEWER"===t.user.role)return o.NextResponse.json({error:"غير مصرح لك بإنشاء الأنشطة"},{status:403});let r=await e.json(),s=d.parse(r),i=new Date(s.startDate),a=s.endDate?new Date(s.endDate):i;if(a<i)return o.NextResponse.json({error:"تاريخ النهاية يجب أن يكون بعد تاريخ البداية"},{status:400});let n=await p.z.activity.create({data:{title:s.title,description:s.description,location:s.location,startDate:i,endDate:a,createdById:t.user.id},include:{createdBy:{select:{name:!0}},_count:{select:{participants:!0,photos:!0}}}});return o.NextResponse.json(n,{status:201})}catch(e){if(e instanceof l.z.ZodError)return o.NextResponse.json({error:"بيانات غير صحيحة",details:e.errors},{status:400});return console.error("خطأ في إضافة النشاط:",e),o.NextResponse.json({error:"حدث خطأ في إضافة النشاط"},{status:500})}}let g=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/activities/route",pathname:"/api/activities",filename:"route",bundlePath:"app/api/activities/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\activities\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:v,workUnitAsyncStorage:w,serverHooks:y}=g;function h(){return(0,n.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:w})}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>i});var s=r(96330);let i=globalThis.prisma??new s.PrismaClient},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,5663,4999,3412,580,5697],()=>r(26182));module.exports=s})();