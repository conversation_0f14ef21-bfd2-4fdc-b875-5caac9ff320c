'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useMemberAuthState } from '@/hooks/use-member-auth'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { LogIn, Eye, EyeOff, Building2, Loader2, ArrowRight, Users } from 'lucide-react'

export default function MemberSignInPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const { user, loading, signIn, signOut } = useMemberAuthState()
  const router = useRouter()

  // إذا كان المستخدم مسجل دخول بالفعل، عرض خيار الانتقال للوحة التحكم أو تسجيل خروج
  useEffect(() => {
    // لا نقوم بالتوجيه التلقائي، بل نعطي المستخدم خيارات
  }, [user, loading, router])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    try {
      const result = await signIn(email, password)

      if (result.success) {
        router.push('/member/dashboard')
      } else {
        setError(result.error || 'حدث خطأ في تسجيل الدخول')
      }
    } catch {
      setError('حدث خطأ في تسجيل الدخول')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSignOut = async () => {
    setIsLoading(true)
    try {
      await signOut()
      setError('')
      setEmail('')
      setPassword('')
    } catch {
      setError('حدث خطأ في تسجيل الخروج')
    } finally {
      setIsLoading(false)
    }
  }

  // عرض شاشة التحميل أثناء التحقق من الجلسة
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  // إذا كان المستخدم مسجل دخول، عرض خيارات
  if (user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
        {/* زر العودة */}
        <Button
          onClick={() => router.push('/')}
          variant="ghost"
          className="absolute top-4 right-4 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
        >
          <ArrowRight className="w-4 h-4 ml-2" />
          العودة للصفحة الرئيسية
        </Button>

        <Card className="w-full max-w-md shadow-xl">
          <CardHeader className="text-center space-y-4">
            <div className="mx-auto w-20 h-20 bg-gradient-to-br from-green-600 to-emerald-600 rounded-full flex items-center justify-center shadow-lg">
              <Users className="w-10 h-10 text-white" />
            </div>
            <div>
              <CardTitle className="text-2xl font-bold text-gray-900">
                مرحباً {user.member.name}
              </CardTitle>
              <CardDescription className="text-lg mt-2">
                أنت مسجل دخول بالفعل
              </CardDescription>
            </div>
          </CardHeader>

          <CardContent className="space-y-4">
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg text-sm text-center">
              ✅ تم تسجيل الدخول بنجاح
            </div>

            <div className="space-y-3">
              <Button
                onClick={() => router.push('/member/dashboard')}
                className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium py-2.5"
              >
                <div className="flex items-center gap-2">
                  <Building2 className="w-4 h-4" />
                  الانتقال للوحة التحكم
                </div>
              </Button>

              <Button
                onClick={handleSignOut}
                variant="outline"
                className="w-full border-red-300 text-red-600 hover:bg-red-50 font-medium py-2.5"
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    جاري تسجيل الخروج...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <LogIn className="w-4 h-4" />
                    تسجيل خروج وتسجيل دخول بحساب آخر
                  </div>
                )}
              </Button>
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm">
                {error}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      {/* زر العودة */}
      <Button
        onClick={() => router.push('/')}
        variant="ghost"
        className="absolute top-4 right-4 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
      >
        <ArrowRight className="w-4 h-4 ml-2" />
        العودة للصفحة الرئيسية
      </Button>

      <Card className="w-full max-w-md shadow-xl">
        <CardHeader className="text-center space-y-4">
          <div className="mx-auto w-20 h-20 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center shadow-lg">
            <Users className="w-10 h-10 text-white" />
          </div>
          <div>
            <CardTitle className="text-2xl font-bold text-gray-900">
              ديوان آل أبو علوش
            </CardTitle>
            <CardDescription className="text-lg mt-2">
              بوابة الأعضاء
            </CardDescription>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm">
                {error}
              </div>
            )}

            <div className="space-y-2">
              <label htmlFor="email" className="text-sm font-medium text-gray-700">
                البريد الإلكتروني
              </label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="أدخل بريدك الإلكتروني"
                required
                className="text-left"
                dir="ltr"
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="password" className="text-sm font-medium text-gray-700">
                كلمة المرور
              </label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="••••••••"
                  required
                  className="text-left pr-10"
                  dir="ltr"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
            </div>

            <Button
              type="submit"
              className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium py-2.5"
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  جاري تسجيل الدخول...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  تسجيل الدخول
                </div>
              )}
            </Button>
          </form>

          <div className="text-center">
            <div className="text-sm text-gray-600 bg-gray-50 p-4 rounded-lg">
              <p className="font-medium mb-2">للحصول على بيانات الدخول:</p>
              <p className="text-xs">يرجى التواصل مع إدارة الديوان</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
