(()=>{var e={};e.id=5724,e.ids=[5724],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>a});var s=r(13581),i=r(85663),o=r(31183);let a={providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"البريد الإلكتروني",type:"email"},password:{label:"كلمة المرور",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let t=await o.z.user.findUnique({where:{email:e.email}});return t&&await i.Ay.compare(e.password,t.password)?{id:t.id,email:t.email,name:t.name,role:t.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>i});var s=r(96330);let i=globalThis.prisma??new s.PrismaClient},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59746:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>h,serverHooks:()=>m,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{GET:()=>y,POST:()=>g});var i=r(96559),o=r(48088),a=r(37719),n=r(32190),l=r(19854),u=r(12909),c=r(31183),d=r(45697);let p=d.z.object({title:d.z.string().min(1,"العنوان مطلوب"),description:d.z.string().optional(),imagePath:d.z.string().min(1,"مسار الصورة مطلوب"),activityId:d.z.string().optional(),folderId:d.z.string().optional()});async function y(e){try{if(!await (0,l.getServerSession)(u.N))return n.NextResponse.json({error:"غير مصرح"},{status:401});let{searchParams:t}=new URL(e.url),r=t.get("search")||"",s=t.get("category")||"all",i=parseInt(t.get("page")||"1"),o=parseInt(t.get("limit")||"20"),a=t.get("groupBy")||"none",d=t.get("activityId"),p=t.get("folderId"),y=(i-1)*o;if(d||p){let e={};d?e="null"===d?{activityId:null,folderId:null}:{activityId:d}:p&&(e="null"===p?{folderId:null,activityId:null}:{folderId:p}),r&&(e.OR=[{title:{contains:r}},{description:{contains:r}}]);let t=await c.z.galleryPhoto.findMany({where:e,include:{uploader:{select:{name:!0}},activity:{select:{id:!0,title:!0}},folder:{select:{id:!0,title:!0}}},orderBy:{createdAt:"desc"},skip:y,take:o}),s=await c.z.galleryPhoto.count({where:e});return n.NextResponse.json({photos:t,pagination:{page:i,limit:o,total:s,pages:Math.ceil(s/o)}})}if("activity"===a){let e=await c.z.galleryFolder.findMany({include:{photos:{take:1,orderBy:{createdAt:"desc"}},_count:{select:{photos:!0}}},orderBy:{createdAt:"desc"}}),t=await c.z.activity.findMany({include:{photos:{take:1,orderBy:{createdAt:"desc"}},_count:{select:{photos:!0}}},orderBy:{createdAt:"desc"}}),r=await c.z.galleryPhoto.count({where:{AND:[{activityId:null},{folderId:null}]}}),s=r>0?await c.z.galleryPhoto.findMany({where:{AND:[{activityId:null},{folderId:null}]},take:1,orderBy:{createdAt:"desc"}}):[];return n.NextResponse.json({folders:[...e.map(e=>({id:e.id,title:e.title,description:e.description,photosCount:e._count.photos,coverPhoto:e.photos[0]||null,type:"folder"})),...t.map(e=>({id:e.id,title:e.title,description:e.description,photosCount:e._count.photos,coverPhoto:e.photos[0]||null,type:"activity"})),...r>0?[{id:"general",title:"الصور العامة",description:"صور غير مرتبطة بأنشطة أو مجلدات محددة",photosCount:r,coverPhoto:s[0]||null,type:"general"}]:[]]})}let g={};r&&(g.OR=[{title:{contains:r}},{description:{contains:r}}]),"all"!==s&&("activities"===s?g.activityId={not:null}:"members"===s&&(g.activityId=null));let h=await c.z.galleryPhoto.findMany({where:g,include:{uploader:{select:{name:!0}},activity:{select:{id:!0,title:!0}}},orderBy:{createdAt:"desc"},skip:y,take:o}),v=await c.z.galleryPhoto.count({where:g});return n.NextResponse.json({photos:h,pagination:{page:i,limit:o,total:v,pages:Math.ceil(v/o)}})}catch(e){return console.error("خطأ في جلب الصور:",e),n.NextResponse.json({error:"حدث خطأ في جلب الصور"},{status:500})}}async function g(e){try{let t=await (0,l.getServerSession)(u.N);if(!t)return n.NextResponse.json({error:"غير مصرح"},{status:401});let r=await e.json(),s=p.parse(r),i=await c.z.galleryPhoto.create({data:{...s,uploadedBy:t.user.id},include:{uploader:{select:{name:!0}},activity:{select:{id:!0,title:!0}}}});return n.NextResponse.json(i,{status:201})}catch(e){if(e instanceof d.z.ZodError)return n.NextResponse.json({error:"بيانات غير صحيحة",details:e.errors},{status:400});return console.error("خطأ في إضافة الصورة:",e),n.NextResponse.json({error:"حدث خطأ في إضافة الصورة"},{status:500})}}let h=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/gallery/route",pathname:"/api/gallery",filename:"route",bundlePath:"app/api/gallery/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:v,workUnitAsyncStorage:x,serverHooks:m}=h;function w(){return(0,a.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:x})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,5663,4999,3412,580,5697],()=>r(59746));module.exports=s})();