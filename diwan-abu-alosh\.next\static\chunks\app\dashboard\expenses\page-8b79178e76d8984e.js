(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5343],{46308:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("receipt",[["path",{d:"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z",key:"q3az6g"}],["path",{d:"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8",key:"1h4pet"}],["path",{d:"M12 17.5v-11",key:"1jc1ny"}]])},68500:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},85057:(e,t,s)=>{"use strict";s.d(t,{J:()=>n});var r=s(95155),a=s(12115),l=s(59434);let n=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("label",{ref:t,className:(0,l.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",s),...a})});n.displayName="Label"},85127:(e,t,s)=>{"use strict";s.d(t,{A0:()=>o,BF:()=>d,Hj:()=>i,XI:()=>n,nA:()=>x,nd:()=>c});var r=s(95155),a=s(12115),l=s(59434);let n=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:t,className:(0,l.cn)("w-full caption-bottom text-sm",s),...a})})});n.displayName="Table";let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("thead",{ref:t,className:(0,l.cn)("[&_tr]:border-b",s),...a})});o.displayName="TableHeader";let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("tbody",{ref:t,className:(0,l.cn)("[&_tr:last-child]:border-0",s),...a})});d.displayName="TableBody",a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("tfoot",{ref:t,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",s),...a})}).displayName="TableFooter";let i=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("tr",{ref:t,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",s),...a})});i.displayName="TableRow";let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("th",{ref:t,className:(0,l.cn)("h-12 px-4 text-right align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",s),...a})});c.displayName="TableHead";let x=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("td",{ref:t,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",s),...a})});x.displayName="TableCell"},88539:(e,t,s)=>{"use strict";s.d(t,{T:()=>n});var r=s(95155),a=s(12115),l=s(59434);let n=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:t,...a})});n.displayName="Textarea"},89084:(e,t,s)=>{Promise.resolve().then(s.bind(s,91898))},91788:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},91898:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>F});var r=s(95155),a=s(12115),l=s(12108),n=s(30285),o=s(62523),d=s(66695),i=s(26126),c=s(85127),x=s(68500),m=s(91788),g=s(84616),h=s(55868),p=s(46308),u=s(47924),b=s(66932),f=s(69074),y=s(13717),j=s(62525),N=s(59409),v=s(59434),w=s(62177),A=s(48778),C=s(71153),E=s(85057),k=s(88539),D=s(54165);let S=C.z.object({amount:C.z.number().positive("المبلغ يجب أن يكون أكبر من صفر"),date:C.z.string().min(1,"التاريخ مطلوب"),description:C.z.string().min(1,"الوصف مطلوب"),category:C.z.enum(["MEETINGS","EVENTS","MAINTENANCE","SOCIAL","GENERAL"]).default("GENERAL"),recipient:C.z.string().optional(),notes:C.z.string().optional()});function z(e){let{open:t,onOpenChange:s,onSuccess:l,expense:d=null}=e,[i,c]=(0,a.useState)(!1),{register:x,handleSubmit:m,reset:g,setValue:u,watch:b,formState:{errors:f}}=(0,w.mN)({resolver:(0,A.u)(S),defaultValues:{amount:0,date:new Date().toISOString().split("T")[0],description:"",category:"GENERAL",recipient:"",notes:""}}),y=b("category");(0,a.useEffect)(()=>{t&&(d?g({amount:d.amount,date:d.date.split("T")[0],description:d.description,category:d.category,recipient:d.recipient||"",notes:d.notes||""}):g({amount:0,date:new Date().toISOString().split("T")[0],description:"",category:"GENERAL",recipient:"",notes:""}))},[t,d,g]);let j=async e=>{try{var t,r;c(!0);let a={amount:Number(e.amount),date:new Date(e.date),description:e.description.trim(),category:e.category,recipient:(null==(t=e.recipient)?void 0:t.trim())||null,notes:(null==(r=e.notes)?void 0:r.trim())||null},n=!!d,o=n?"/api/expenses/".concat(d.id):"/api/expenses",i=await fetch(o,{method:n?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!i.ok){let e=await i.json();throw Error(e.error||"حدث خطأ")}alert(n?"تم تحديث المصروف بنجاح!":"تم إضافة المصروف بنجاح!"),null==l||l(),s(!1)}catch(e){console.error("خطأ في حفظ المصروف:",e),alert(e.message||"حدث خطأ في حفظ المصروف")}finally{c(!1)}};return(0,r.jsx)(D.lG,{open:t,onOpenChange:s,children:(0,r.jsxs)(D.Cf,{className:"max-w-[50vw] max-h-[90vh] overflow-y-auto",children:[(0,r.jsx)(D.c7,{className:"pb-6 border-b border-gray-100",children:(0,r.jsxs)(D.L3,{className:"text-2xl font-bold text-gray-900 flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-red-500 to-orange-500 rounded-xl flex items-center justify-center",children:(0,r.jsx)(p.A,{className:"w-6 h-6 text-white"})}),d?"تعديل المصروف":"إضافة مصروف جديد"]})}),(0,r.jsxs)("form",{onSubmit:m(j),className:"p-8 space-y-8 bg-gray-50/30",children:[(0,r.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border border-gray-100",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-red-600 rounded-full"}),"المعلومات الأساسية"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(E.J,{htmlFor:"amount",className:"text-sm font-medium text-gray-700 flex items-center gap-1",children:["المبلغ (دينار أردني)",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(o.p,{id:"amount",type:"number",step:"0.01",min:"0",...x("amount",{valueAsNumber:!0}),className:"h-12 pr-12 transition-all duration-200 ".concat(f.amount?"border-red-300 focus:border-red-500 focus:ring-red-200":"border-gray-200 focus:border-red-500 focus:ring-red-100"),placeholder:"0.00"}),(0,r.jsx)("div",{className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400",children:(0,r.jsx)(h.A,{className:"w-5 h-5"})})]}),f.amount&&(0,r.jsxs)("p",{className:"text-red-500 text-sm flex items-center gap-1",children:[(0,r.jsx)("span",{className:"w-1 h-1 bg-red-500 rounded-full"}),f.amount.message]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(E.J,{htmlFor:"date",className:"text-sm font-medium text-gray-700 flex items-center gap-1",children:["التاريخ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)(o.p,{id:"date",type:"date",...x("date"),className:"h-12 transition-all duration-200 ".concat(f.date?"border-red-300 focus:border-red-500 focus:ring-red-200":"border-gray-200 focus:border-red-500 focus:ring-red-100")}),f.date&&(0,r.jsxs)("p",{className:"text-red-500 text-sm flex items-center gap-1",children:[(0,r.jsx)("span",{className:"w-1 h-1 bg-red-500 rounded-full"}),f.date.message]})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border border-gray-100",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-orange-600 rounded-full"}),"تفاصيل المصروف"]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(E.J,{htmlFor:"description",className:"text-sm font-medium text-gray-700 flex items-center gap-1",children:["الوصف",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)(o.p,{id:"description",...x("description"),placeholder:"مثال: شراء مستلزمات، دفع فواتير، تكاليف فعالية",className:"h-12 transition-all duration-200 ".concat(f.description?"border-red-300 focus:border-red-500 focus:ring-red-200":"border-gray-200 focus:border-red-500 focus:ring-red-100")}),f.description&&(0,r.jsxs)("p",{className:"text-red-500 text-sm flex items-center gap-1",children:[(0,r.jsx)("span",{className:"w-1 h-1 bg-red-500 rounded-full"}),f.description.message]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(E.J,{className:"text-sm font-medium text-gray-700",children:"الفئة"}),(0,r.jsxs)(N.l6,{value:y,onValueChange:e=>u("category",e),children:[(0,r.jsx)(N.bq,{className:"h-12 border-gray-200 focus:border-red-500 focus:ring-red-100",children:(0,r.jsx)(N.yv,{placeholder:"اختر الفئة"})}),(0,r.jsxs)(N.gC,{children:[(0,r.jsx)(N.eb,{value:"GENERAL",children:"\uD83C\uDFE2 عامة"}),(0,r.jsx)(N.eb,{value:"MEETINGS",children:"\uD83E\uDD1D اجتماعات"}),(0,r.jsx)(N.eb,{value:"EVENTS",children:"\uD83C\uDF89 مناسبات"}),(0,r.jsx)(N.eb,{value:"MAINTENANCE",children:"\uD83D\uDD27 إصلاحات"}),(0,r.jsx)(N.eb,{value:"SOCIAL",children:"\uD83D\uDC65 اجتماعية"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(E.J,{htmlFor:"recipient",className:"text-sm font-medium text-gray-700",children:"الجهة المستفيدة"}),(0,r.jsx)(o.p,{id:"recipient",...x("recipient"),placeholder:"مثال: شركة، مورد، مقاول",className:"h-12 border-gray-200 focus:border-red-500 focus:ring-red-100 transition-all duration-200"})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(E.J,{htmlFor:"notes",className:"text-sm font-medium text-gray-700",children:"ملاحظات إضافية"}),(0,r.jsx)(k.T,{id:"notes",...x("notes"),placeholder:"أي ملاحظات أو تفاصيل إضافية...",className:"min-h-[100px] border-gray-200 focus:border-red-500 focus:ring-red-100 transition-all duration-200 resize-none"})]})]})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-4 pt-6 border-t border-gray-100",children:[(0,r.jsx)(n.$,{type:"button",variant:"outline",onClick:()=>s(!1),className:"h-12 px-8 border-2 border-gray-300 text-gray-700 hover:border-gray-400 hover:text-gray-800 transition-all duration-200 font-medium",disabled:i,children:"إلغاء"}),(0,r.jsx)(n.$,{type:"submit",disabled:i,className:"h-12 px-8 bg-gradient-to-r from-red-600 via-red-700 to-orange-600 hover:from-red-700 hover:via-red-800 hover:to-orange-700 text-white font-bold transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-xl",children:i?(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),(0,r.jsx)("span",{children:"جاري الحفظ..."})]}):(0,r.jsx)("span",{children:d?"تحديث المصروف":"إضافة المصروف"})})]})]})]})})}var T=s(26597),R=s(52699),I=s.n(R);function F(){let{data:e}=(0,l.useSession)(),[t,s]=(0,a.useState)([]),[w,A]=(0,a.useState)(!0),[C,E]=(0,a.useState)(""),[k,D]=(0,a.useState)("all"),[S,R]=(0,a.useState)({page:1,limit:10,total:0,pages:0}),[F,L]=(0,a.useState)({totalAmount:0,totalCount:0,byCategory:[]}),[_,M]=(0,a.useState)(!1),[G,O]=(0,a.useState)(null),Z=(0,a.useCallback)(async()=>{try{A(!0);let e=new URLSearchParams({search:C,category:k,page:S.page.toString(),limit:S.limit.toString()}),t=await fetch("/api/expenses?".concat(e));if(!t.ok)throw Error("فشل في جلب المصروفات");let r=await t.json();s(r.expenses),R(r.pagination)}catch(e){console.error("خطأ في جلب المصروفات:",e)}finally{A(!1)}},[C,k,S.page,S.limit]),P=async()=>{try{let e=await fetch("/api/expenses?limit=1000");if(!e.ok)return;let t=await e.json(),s=t.expenses.reduce((e,t)=>e+t.amount,0),r=t.expenses.length,a=t.expenses.reduce((e,t)=>{let s=e.find(e=>e.category===t.category);return s?(s._sum.amount+=t.amount,s._count+=1):e.push({category:t.category,_sum:{amount:t.amount},_count:1}),e},[]);L({totalAmount:s,totalCount:r,byCategory:a})}catch(e){console.error("خطأ في جلب الإحصائيات:",e)}};(0,a.useEffect)(()=>{Z()},[Z]),(0,a.useEffect)(()=>{P()},[]);let V=e=>{O(e),M(!0)},$=async e=>{if(confirm("هل أنت متأكد من حذف هذا المصروف؟\n\nهذا الإجراء لا يمكن التراجع عنه."))try{let t=await fetch("/api/expenses/".concat(e),{method:"DELETE"});if(!t.ok){let e=await t.json();alert(e.error||"فشل في حذف المصروف");return}alert("تم حذف المصروف بنجاح"),Z(),P()}catch(e){console.error("خطأ في حذف المصروف:",e),alert("حدث خطأ في حذف المصروف")}},B=(null==e?void 0:e.user.role)!=="VIEWER",H=(null==e?void 0:e.user.role)==="ADMIN";return(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsx)("div",{className:"text-center mb-8",children:(0,r.jsxs)("div",{className:"bg-gradient-to-r from-red-600 to-orange-600 rounded-3xl shadow-2xl p-10 text-white relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-red-400 to-orange-500 opacity-30 animate-pulse"}),(0,r.jsxs)("div",{className:"relative z-10",children:[(0,r.jsx)("div",{className:"inline-flex items-center justify-center w-20 h-20 rounded-full mb-6 bg-white bg-opacity-20 backdrop-blur-sm",children:(0,r.jsx)(x.A,{className:"w-10 h-10 text-white"})}),(0,r.jsx)("h1",{className:"text-5xl font-black mb-4 text-white",children:"إدارة المصروفات"}),(0,r.jsx)("p",{className:"text-xl font-semibold mb-6 text-red-100",children:"عرض وإدارة مصروفات الديوان بكفاءة وسهولة"}),(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2 space-x-reverse",children:[(0,r.jsx)("div",{className:"h-1 w-16 rounded-full bg-white bg-opacity-60"}),(0,r.jsx)("div",{className:"h-1 w-8 rounded-full bg-white bg-opacity-40"}),(0,r.jsx)("div",{className:"h-1 w-16 rounded-full bg-white bg-opacity-60"})]})]})]})}),(0,r.jsx)("div",{className:"flex justify-center mb-8",children:(0,r.jsx)("div",{className:"bg-white rounded-2xl shadow-xl p-6 border border-gray-100",children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-4 justify-center",children:[(0,r.jsxs)(n.$,{onClick:()=>{let e=document.createElement("div");e.style.cssText="\n      position: absolute;\n      top: -9999px;\n      left: -9999px;\n      width: 210mm;\n      background: white;\n      padding: 20px;\n      font-family: Arial, sans-serif;\n      direction: rtl;\n      text-align: right;\n      color: #000;\n      font-size: 12px;\n    ",e.innerHTML='\n      <div style="text-align: center; margin-bottom: 30px; border-bottom: 3px solid #dc2626; padding-bottom: 15px;">\n        <h1 style="color: #dc2626; font-size: 24px; margin: 0; font-weight: bold;">ديوان أبو علوش</h1>\n        <h2 style="color: #666; font-size: 18px; margin: 8px 0; font-weight: normal;">تقرير المصروفات</h2>\n        <p style="color: #888; font-size: 12px; margin: 5px 0;">تاريخ التقرير: '.concat(new Date().toLocaleDateString("ar-SA"),'</p>\n      </div>\n\n      <div style="margin-bottom: 20px; background: #f8f9fa; padding: 15px; border-radius: 8px; border-right: 4px solid #dc2626;">\n        <h3 style="color: #dc2626; font-size: 16px; margin: 0 0 15px 0; font-weight: bold;">ملخص المصروفات</h3>\n        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;">\n          <div style="text-align: center; background: white; padding: 10px; border-radius: 6px; border: 1px solid #e5e7eb;">\n            <div style="color: #dc2626; font-size: 20px; font-weight: bold;">').concat(F.totalAmount.toFixed(2),' د.أ</div>\n            <div style="color: #666; font-size: 11px;">إجمالي المصروفات</div>\n          </div>\n          <div style="text-align: center; background: white; padding: 10px; border-radius: 6px; border: 1px solid #e5e7eb;">\n            <div style="color: #dc2626; font-size: 20px; font-weight: bold;">').concat(F.totalCount,'</div>\n            <div style="color: #666; font-size: 11px;">عدد المصروفات</div>\n          </div>\n          <div style="text-align: center; background: white; padding: 10px; border-radius: 6px; border: 1px solid #e5e7eb;">\n            <div style="color: #dc2626; font-size: 20px; font-weight: bold;">').concat((F.totalCount>0?F.totalAmount/F.totalCount:0).toFixed(2),' د.أ</div>\n            <div style="color: #666; font-size: 11px;">متوسط المصروف</div>\n          </div>\n        </div>\n      </div>\n\n      <div style="margin-bottom: 20px;">\n        <h3 style="color: #dc2626; font-size: 16px; margin: 0 0 15px 0; font-weight: bold; border-bottom: 2px solid #dc2626; padding-bottom: 8px;">تفاصيل المصروفات</h3>\n        <table style="width: 100%; border-collapse: collapse; font-size: 11px; background: white; border-radius: 8px; overflow: hidden;">\n          <thead>\n            <tr style="background: #dc2626; color: white;">\n              <th style="padding: 10px 8px; text-align: right; font-weight: bold;">الوصف</th>\n              <th style="padding: 10px 8px; text-align: center; font-weight: bold;">المبلغ (د.أ)</th>\n              <th style="padding: 10px 8px; text-align: center; font-weight: bold;">الفئة</th>\n              <th style="padding: 10px 8px; text-align: center; font-weight: bold;">المستفيد</th>\n              <th style="padding: 10px 8px; text-align: center; font-weight: bold;">التاريخ</th>\n            </tr>\n          </thead>\n          <tbody>\n            ').concat(t.map((e,t)=>'\n              <tr style="background: '.concat(t%2==0?"#ffffff":"#f8f9fa",'; border-bottom: 1px solid #e5e7eb;">\n                <td style="padding: 8px; text-align: right; border-left: 1px solid #e5e7eb;">\n                  <div style="font-weight: bold; color: #374151;">').concat(e.description,"</div>\n                  ").concat(e.notes?'<div style="font-size: 9px; color: #6b7280; margin-top: 2px;">ملاحظة: '.concat(e.notes,"</div>"):"",'\n                </td>\n                <td style="padding: 8px; text-align: center; font-weight: bold; color: #dc2626; border-left: 1px solid #e5e7eb;">').concat(e.amount.toFixed(2),'</td>\n                <td style="padding: 8px; text-align: center; border-left: 1px solid #e5e7eb;">\n                  <span style="background: #fef2f2; color: #dc2626; padding: 4px 8px; border-radius: 4px; font-size: 10px; font-weight: bold;">\n                    ').concat((0,v.uF)(e.category),'\n                  </span>\n                </td>\n                <td style="padding: 8px; text-align: center; color: #374151; border-left: 1px solid #e5e7eb;">').concat(e.recipient||"غير محدد",'</td>\n                <td style="padding: 8px; text-align: center; color: #6b7280; font-size: 10px;">').concat((0,v.Yq)(e.date),"</td>\n              </tr>\n            ")).join(""),'\n          </tbody>\n        </table>\n      </div>\n\n      <div style="margin-top: 30px; padding-top: 15px; border-top: 2px solid #e5e7eb; text-align: center; color: #6b7280; font-size: 10px;">\n        <p style="margin: 0;">ديوان أبو علوش - نظام إدارة العائلة</p>\n        <p style="margin: 5px 0 0 0;">تم إنشاء هذا التقرير في: ').concat(new Date().toLocaleString("ar-SA"),"</p>\n      </div>\n    "),document.body.appendChild(e),setTimeout(()=>{I()(e,{scale:1.5,useCORS:!0,allowTaint:!0,backgroundColor:"#ffffff",logging:!1}).then(t=>{let s=t.toDataURL("image/png"),r=new T.default("p","mm","a4"),a=210*t.height/t.width;if(a<=297)r.addImage(s,"PNG",0,0,210,a);else{let e=a,t=0;for(r.addImage(s,"PNG",0,t,210,a),e-=297;e>=0;)t=e-a,r.addPage(),r.addImage(s,"PNG",0,t,210,a),e-=297}let l="تقرير_مصروفات_الديوان_".concat(new Date().toISOString().split("T")[0],".pdf");r.save(l),document.body.removeChild(e)}).catch(t=>{console.error("خطأ في إنشاء PDF:",t),document.body.removeChild(e),alert("حدث خطأ في تصدير التقرير")})},300)},className:"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold",children:[(0,r.jsx)(m.A,{className:"w-5 h-5 ml-2"}),"تصدير PDF"]}),B&&(0,r.jsxs)(n.$,{onClick:()=>{O(null),M(!0)},className:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold",children:[(0,r.jsx)(g.A,{className:"w-5 h-5 ml-2"}),"إضافة مصروف جديد"]})]})})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3",children:[(0,r.jsxs)(d.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-red-500 to-red-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-red-500 to-red-600 p-1 rounded-t-xl"}),(0,r.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,r.jsx)(d.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"إجمالي المصروفات"}),(0,r.jsx)("div",{className:"p-4 rounded-2xl shadow-lg",style:{backgroundColor:"#dc3545"},children:(0,r.jsx)(h.A,{className:"h-7 w-7 text-white"})})]}),(0,r.jsxs)(d.Wu,{className:"relative z-10",children:[(0,r.jsx)("div",{className:"text-4xl font-black mb-2",style:{color:"#191970"},children:(0,v.vv)(F.totalAmount)}),(0,r.jsx)("p",{className:"text-sm font-semibold",style:{color:"#6c757d"},children:"إجمالي المبالغ"})]})]}),(0,r.jsxs)(d.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-orange-500 to-orange-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-orange-500 to-orange-600 p-1 rounded-t-xl"}),(0,r.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,r.jsx)(d.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"عدد المصروفات"}),(0,r.jsx)("div",{className:"p-4 rounded-2xl shadow-lg",style:{backgroundColor:"#fd7e14"},children:(0,r.jsx)(x.A,{className:"h-7 w-7 text-white"})})]}),(0,r.jsxs)(d.Wu,{className:"relative z-10",children:[(0,r.jsx)("div",{className:"text-4xl font-black mb-2",style:{color:"#191970"},children:F.totalCount}),(0,r.jsx)("p",{className:"text-sm font-semibold",style:{color:"#6c757d"},children:"إجمالي العمليات"})]})]}),(0,r.jsxs)(d.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative sm:col-span-2 lg:col-span-1",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-yellow-500 to-yellow-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-yellow-500 to-yellow-600 p-1 rounded-t-xl"}),(0,r.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,r.jsx)(d.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"متوسط المصروف"}),(0,r.jsx)("div",{className:"p-4 rounded-2xl shadow-lg",style:{backgroundColor:"#ffc107"},children:(0,r.jsx)(p.A,{className:"h-7 w-7 text-white"})})]}),(0,r.jsxs)(d.Wu,{className:"relative z-10",children:[(0,r.jsx)("div",{className:"text-4xl font-black mb-2",style:{color:"#191970"},children:(0,v.vv)(F.totalCount>0?F.totalAmount/F.totalCount:0)}),(0,r.jsx)("p",{className:"text-sm font-semibold",style:{color:"#6c757d"},children:"متوسط القيمة"})]})]})]}),(0,r.jsx)(d.Zp,{className:"bg-white/80 backdrop-blur-sm border-gray-200 shadow-lg",children:(0,r.jsxs)(d.Wu,{className:"p-8",children:[(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)(u.A,{className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5 group-focus-within:text-red-500 transition-colors duration-200"}),(0,r.jsx)(o.p,{placeholder:"البحث في المصروفات... (الوصف، الجهة المستفيدة، الملاحظات)",value:C,onChange:e=>E(e.target.value),className:"h-12 pr-12 pl-4 border-2 border-gray-200 focus:border-red-500 focus:ring-red-100 rounded-xl text-base transition-all duration-200 bg-white/50 backdrop-blur-sm"}),C&&(0,r.jsx)("button",{onClick:()=>E(""),className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500 transition-colors duration-200",children:"✕"})]})}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsxs)(N.l6,{value:k,onValueChange:D,children:[(0,r.jsx)(N.bq,{className:"w-full sm:w-[200px] h-12 border-2 border-gray-200 focus:border-red-500 focus:ring-red-100 rounded-xl bg-white/50 backdrop-blur-sm",children:(0,r.jsx)(N.yv,{placeholder:"فلترة حسب الفئة"})}),(0,r.jsxs)(N.gC,{className:"rounded-xl border-2 border-gray-200 shadow-xl",children:[(0,r.jsx)(N.eb,{value:"all",className:"rounded-lg",children:"\uD83C\uDFE2 جميع الفئات"}),(0,r.jsx)(N.eb,{value:"MEETINGS",className:"rounded-lg",children:"\uD83E\uDD1D اجتماعات"}),(0,r.jsx)(N.eb,{value:"EVENTS",className:"rounded-lg",children:"\uD83C\uDF89 مناسبات"}),(0,r.jsx)(N.eb,{value:"MAINTENANCE",className:"rounded-lg",children:"\uD83D\uDD27 إصلاحات"}),(0,r.jsx)(N.eb,{value:"SOCIAL",className:"rounded-lg",children:"\uD83D\uDC65 اجتماعية"}),(0,r.jsx)(N.eb,{value:"GENERAL",className:"rounded-lg",children:"\uD83D\uDCCB عامة"})]})]}),(0,r.jsx)(n.$,{variant:"outline",size:"icon",className:"h-12 w-12 border-2 border-gray-200 hover:border-red-500 hover:bg-red-50 rounded-xl transition-all duration-200 bg-white/50 backdrop-blur-sm",children:(0,r.jsx)(b.A,{className:"h-5 w-5 text-gray-600"})})]})]}),(C||"all"!==k)&&(0,r.jsxs)("div",{className:"mt-4 flex items-center gap-2 text-sm text-gray-600",children:[(0,r.jsxs)("span",{children:["عرض ",t.length," من أصل ",F.totalCount," مصروف"]}),C&&(0,r.jsxs)("span",{className:"px-2 py-1 bg-red-100 text-red-700 rounded-lg",children:['البحث: "',C,'"']}),"all"!==k&&(0,r.jsxs)("span",{className:"px-2 py-1 bg-orange-100 text-orange-700 rounded-lg",children:["الفئة: ",(0,v.uF)(k)]})]})]})}),(0,r.jsxs)(d.Zp,{className:"bg-white/90 backdrop-blur-sm border-gray-200 shadow-xl overflow-hidden",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-red-600 via-red-700 to-orange-600 p-6",children:(0,r.jsxs)("h3",{className:"text-xl font-bold text-white flex items-center gap-3",children:[(0,r.jsx)(p.A,{className:"w-6 h-6"}),"قائمة المصروفات",(0,r.jsxs)("span",{className:"text-red-200",children:["(",t.length,")"]})]})}),(0,r.jsx)(d.Wu,{className:"p-0",children:w?(0,r.jsxs)("div",{className:"flex flex-col justify-center items-center h-64 bg-gradient-to-br from-gray-50 to-red-50",children:[(0,r.jsx)("div",{className:"w-12 h-12 border-4 border-red-200 border-t-red-600 rounded-full animate-spin mb-4"}),(0,r.jsx)("div",{className:"text-gray-600 font-medium",children:"جاري تحميل المصروفات..."})]}):0===t.length?(0,r.jsxs)("div",{className:"flex flex-col justify-center items-center h-64 bg-gradient-to-br from-gray-50 to-red-50",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4",children:(0,r.jsx)(p.A,{className:"w-8 h-8 text-red-600"})}),(0,r.jsx)("div",{className:"text-gray-600 font-medium mb-2",children:"لا توجد مصروفات"}),(0,r.jsx)("div",{className:"text-gray-500 text-sm",children:"ابدأ بإضافة مصروف جديد"})]}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)(c.XI,{children:[(0,r.jsx)(c.A0,{className:"bg-gray-50/80",children:(0,r.jsxs)(c.Hj,{className:"border-b-2 border-gray-200",children:[(0,r.jsx)(c.nd,{className:"text-right font-bold text-gray-800 py-4",children:"الوصف"}),(0,r.jsx)(c.nd,{className:"text-center font-bold text-gray-800 py-4",children:"المبلغ"}),(0,r.jsx)(c.nd,{className:"text-center font-bold text-gray-800 py-4",children:"الفئة"}),(0,r.jsx)(c.nd,{className:"text-center font-bold text-gray-800 py-4",children:"الجهة المستفيدة"}),(0,r.jsx)(c.nd,{className:"text-center font-bold text-gray-800 py-4",children:"التاريخ"}),(0,r.jsx)(c.nd,{className:"text-center font-bold text-gray-800 py-4",children:"الإجراءات"})]})}),(0,r.jsx)(c.BF,{children:t.map((e,t)=>(0,r.jsxs)(c.Hj,{className:"border-b border-gray-100 hover:bg-red-50/50 transition-all duration-200 ".concat(t%2==0?"bg-white":"bg-gray-50/30"),children:[(0,r.jsx)(c.nA,{className:"py-4",children:(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("div",{className:"font-semibold text-gray-900",children:e.description}),e.notes&&(0,r.jsxs)("div",{className:"text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded-lg inline-block",children:["\uD83D\uDCAC ",e.notes]})]})}),(0,r.jsx)(c.nA,{className:"text-center py-4",children:(0,r.jsx)("div",{className:"font-bold text-lg text-red-700 bg-red-50 px-3 py-1 rounded-lg inline-block",children:(0,v.vv)(e.amount)})}),(0,r.jsx)(c.nA,{className:"text-center py-4",children:(0,r.jsx)(i.E,{variant:"secondary",className:"px-3 py-1 font-medium ".concat("MEETINGS"===e.category?"bg-blue-100 text-blue-800":"EVENTS"===e.category?"bg-purple-100 text-purple-800":"MAINTENANCE"===e.category?"bg-orange-100 text-orange-800":"SOCIAL"===e.category?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"),children:(0,v.uF)(e.category)})}),(0,r.jsx)(c.nA,{className:"text-center py-4",children:(0,r.jsx)("div",{className:"text-gray-700 font-medium",children:e.recipient||(0,r.jsx)("span",{className:"text-gray-400 italic",children:"غير محدد"})})}),(0,r.jsx)(c.nA,{className:"text-center py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2 text-gray-600",children:[(0,r.jsx)(f.A,{className:"w-4 h-4 text-red-500"}),(0,r.jsx)("span",{className:"font-medium",children:(0,v.Yq)(e.date)})]})}),(0,r.jsx)(c.nA,{className:"text-center py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[B&&(0,r.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>V(e),className:"h-9 w-9 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-all duration-200",title:"تعديل المصروف",children:(0,r.jsx)(y.A,{className:"w-4 h-4"})}),H&&(0,r.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>$(e.id),className:"h-9 w-9 p-0 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-all duration-200",title:"حذف المصروف",children:(0,r.jsx)(j.A,{className:"w-4 h-4"})})]})})]},e.id))})]})})})]}),S.pages>1&&(0,r.jsxs)("div",{className:"flex justify-center space-x-2 space-x-reverse",children:[(0,r.jsx)(n.$,{variant:"outline",disabled:1===S.page,onClick:()=>R(e=>({...e,page:e.page-1})),children:"السابق"}),(0,r.jsxs)("span",{className:"flex items-center px-4",children:["صفحة ",S.page," من ",S.pages]}),(0,r.jsx)(n.$,{variant:"outline",disabled:S.page===S.pages,onClick:()=>R(e=>({...e,page:e.page+1})),children:"التالي"})]}),F.byCategory.length>0&&(0,r.jsxs)(d.Zp,{children:[(0,r.jsx)(d.aR,{children:(0,r.jsx)(d.ZB,{children:"المصروفات حسب الفئة"})}),(0,r.jsx)(d.Wu,{children:(0,r.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-5",children:F.byCategory.map(e=>(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-500",children:(0,v.uF)(e.category)}),(0,r.jsx)("div",{className:"mt-1 text-lg font-semibold text-gray-900",children:(0,v.vv)(e._sum.amount||0)}),(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:[e._count," مصروف"]})]},e.category))})})]}),(0,r.jsx)(z,{open:_,onOpenChange:()=>{M(!1),O(null)},expense:G,onSuccess:()=>{Z(),P()}})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4316,3930,1778,2108,3942,5217,8130,913,1070,3068,8441,1684,7358],()=>t(89084)),_N_E=e.O()}]);