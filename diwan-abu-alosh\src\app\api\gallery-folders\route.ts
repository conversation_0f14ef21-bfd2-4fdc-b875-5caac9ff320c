import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Schema للتحقق من صحة البيانات
const galleryFolderSchema = z.object({
  title: z.string().min(1, 'العنوان مطلوب'),
  description: z.string().optional(),
  location: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
})

// GET - جلب جميع المجلدات
export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    const folders = await prisma.galleryFolder.findMany({
      include: {
        creator: {
          select: {
            name: true,
          },
        },
        photos: {
          take: 1,
          orderBy: { createdAt: 'desc' },
        },
        _count: {
          select: {
            photos: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    })

    // تحويل البيانات لتتناسب مع واجهة المعرض
    const formattedFolders = folders.map(folder => ({
      id: folder.id,
      title: folder.title,
      description: folder.description,
      photosCount: folder._count.photos,
      coverPhoto: folder.photos[0] || null,
      type: 'folder' as const,
      location: folder.location,
      startDate: folder.startDate,
      endDate: folder.endDate,
      createdAt: folder.createdAt,
      creator: folder.creator,
    }))

    return NextResponse.json({ folders: formattedFolders })
  } catch (error) {
    console.error('خطأ في جلب المجلدات:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في جلب المجلدات' },
      { status: 500 }
    )
  }
}

// POST - إنشاء مجلد جديد
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // التحقق من الصلاحيات
    if (session.user.role === 'VIEWER') {
      return NextResponse.json({ error: 'ليس لديك صلاحية لإنشاء مجلدات' }, { status: 403 })
    }

    const body = await request.json()
    
    // التحقق من صحة البيانات
    const validatedData = galleryFolderSchema.parse(body)

    // تحويل التواريخ إذا كانت موجودة
    const folderData: Record<string, unknown> = {
      title: validatedData.title,
      description: validatedData.description,
      location: validatedData.location,
      createdBy: session.user.id,
    }

    if (validatedData.startDate) {
      folderData.startDate = new Date(validatedData.startDate)
    }

    if (validatedData.endDate) {
      folderData.endDate = new Date(validatedData.endDate)
    } else if (validatedData.startDate) {
      // إذا لم يتم تحديد تاريخ النهاية، استخدم تاريخ البداية
      folderData.endDate = new Date(validatedData.startDate)
    }

    // إنشاء المجلد في قاعدة البيانات
    const folder = await prisma.galleryFolder.create({
      data: folderData,
      include: {
        creator: {
          select: {
            name: true,
          },
        },
        _count: {
          select: {
            photos: true,
          },
        },
      },
    })

    // تحويل البيانات لتتناسب مع واجهة المعرض
    const formattedFolder = {
      id: folder.id,
      title: folder.title,
      description: folder.description,
      photosCount: folder._count.photos,
      coverPhoto: null,
      type: 'folder' as const,
      location: folder.location,
      startDate: folder.startDate,
      endDate: folder.endDate,
      createdAt: folder.createdAt,
      creator: folder.creator,
    }

    return NextResponse.json(formattedFolder, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'بيانات غير صحيحة', details: error.errors },
        { status: 400 }
      )
    }

    console.error('خطأ في إنشاء المجلد:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في إنشاء المجلد' },
      { status: 500 }
    )
  }
}
