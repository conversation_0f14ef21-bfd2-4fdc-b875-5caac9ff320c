(()=>{var e={};e.id=1588,e.ids=[1588],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,s)=>{"use strict";s.d(r,{z:()=>n});var t=s(96330);let n=globalThis.prisma??new t.PrismaClient},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},84610:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>v,routeModule:()=>d,serverHooks:()=>b,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var t={};s.r(t),s.d(t,{GET:()=>m});var n=s(96559),o=s(48088),i=s(37719),a=s(32190),u=s(31183),p=s(43205),c=s.n(p);async function m(e){try{let r,s=e.cookies.get("member-token")?.value;if(!s)return a.NextResponse.json({error:"غير مصرح"},{status:401});try{r=c().verify(s,process.env.NEXTAUTH_SECRET||"fallback-secret")}catch(e){return e instanceof c().TokenExpiredError||e instanceof c().JsonWebTokenError||console.error("خطأ في التحقق من التوكن:",e),a.NextResponse.json({error:"جلسة غير صالحة"},{status:401})}if(!r.userId)return a.NextResponse.json({error:"جلسة غير صالحة"},{status:401});let t=await u.z.user.findUnique({where:{id:r.userId},include:{memberUser:{include:{member:!0}}}});if(!t||!t.memberUser)return a.NextResponse.json({error:"المستخدم غير موجود"},{status:404});if(!t.memberUser.isActive)return a.NextResponse.json({error:"حسابك غير مفعل"},{status:403});return a.NextResponse.json({user:{id:t.id,email:t.email,name:t.name,member:t.memberUser.member,permissions:{canViewAccountStatement:t.memberUser.canViewAccountStatement,canViewGallery:t.memberUser.canViewGallery}}})}catch(e){return console.error("خطأ في التحقق من جلسة العضو:",e),a.NextResponse.json({error:"حدث خطأ في التحقق من الجلسة"},{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/member/session/route",pathname:"/api/auth/member/session",filename:"route",bundlePath:"app/api/auth/member/session/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\auth\\member\\session\\route.ts",nextConfigOutput:"standalone",userland:t}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:b}=d;function v(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4243,580,3205],()=>s(84610));module.exports=t})();