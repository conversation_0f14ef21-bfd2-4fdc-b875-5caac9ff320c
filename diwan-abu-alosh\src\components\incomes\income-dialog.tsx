'use client'

import { useState, useEffect, useRef } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { DollarSign, Search, X, User } from 'lucide-react'

const incomeSchema = z.object({
  amount: z.number().positive('المبلغ يجب أن يكون أكبر من صفر'),
  date: z.string().min(1, 'التاريخ مطلوب'),
  source: z.string().min(1, 'مصدر الإيراد مطلوب'),
  type: z.enum(['SUBSCRIPTION', 'DONATION', 'EVENT', 'OTHER']).default('SUBSCRIPTION'),
  description: z.string().optional(),
  notes: z.string().optional(),
  memberId: z.string().min(1, 'يجب اختيار عضو'),
})

type IncomeInput = z.infer<typeof incomeSchema>

interface IncomeDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
  income?: {
    id: string
    amount: number
    date: string
    source: string
    type: string
    description?: string
    notes?: string
    member?: {
      id: string
      name: string
    }
  } | null
}

interface Member {
  id: string
  name: string
}

export default function IncomeDialog({
  open,
  onOpenChange,
  onSuccess,
  income = null,
}: IncomeDialogProps) {
  const [loading, setLoading] = useState(false)
  const [members, setMembers] = useState<Member[]>([])
  const [filteredMembers, setFilteredMembers] = useState<Member[]>([])
  const [memberSearch, setMemberSearch] = useState('')
  const [selectedMember, setSelectedMember] = useState<Member | null>(null)
  const [showMemberDropdown, setShowMemberDropdown] = useState(false)
  const memberSearchRef = useRef<HTMLDivElement>(null)

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm<IncomeInput>({
    resolver: zodResolver(incomeSchema),
    defaultValues: {
      amount: 0,
      date: new Date().toISOString().split('T')[0], // التاريخ الحالي
      source: '',
      type: 'SUBSCRIPTION',
      description: '',
      notes: '',
      memberId: '',
    },
  })

  const selectedType = watch('type')

  // جلب قائمة الأعضاء
  const fetchMembers = async () => {
    try {
      const response = await fetch('/api/members?limit=1000')
      if (response.ok) {
        const data = await response.json()
        setMembers(data.members || [])
      }
    } catch (error) {
      console.error('خطأ في جلب الأعضاء:', error)
    }
  }

  useEffect(() => {
    if (open) {
      fetchMembers()

      if (income) {
        // تعديل إيراد موجود
        reset({
          amount: income.amount,
          date: income.date.split('T')[0], // تحويل التاريخ للصيغة المطلوبة
          source: income.source,
          type: income.type as any,
          description: income.description || '',
          notes: income.notes || '',
          memberId: income.member?.id || '',
        })

        if (income.member) {
          setSelectedMember(income.member)
          setMemberSearch(income.member.name)
        } else {
          setSelectedMember(null)
          setMemberSearch('')
        }
      } else {
        // إضافة إيراد جديد
        reset({
          amount: 0,
          date: new Date().toISOString().split('T')[0],
          source: '',
          type: 'SUBSCRIPTION',
          description: '',
          notes: '',
          memberId: '',
        })
        setSelectedMember(null)
        setMemberSearch('')
      }

      setShowMemberDropdown(false)
    }
  }, [open, income, reset])

  // تصفية الأعضاء عند البحث
  useEffect(() => {
    if (memberSearch.trim() === '') {
      setFilteredMembers(members.slice(0, 10)) // عرض أول 10 أعضاء
    } else {
      const filtered = members.filter(member =>
        member.name.toLowerCase().includes(memberSearch.toLowerCase())
      ).slice(0, 10) // عرض أول 10 نتائج
      setFilteredMembers(filtered)
    }
  }, [memberSearch, members])

  // إغلاق القائمة عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (memberSearchRef.current && !memberSearchRef.current.contains(event.target as Node)) {
        setShowMemberDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // اختيار عضو
  const handleSelectMember = (member: Member) => {
    setSelectedMember(member)
    setMemberSearch(member.name)
    setShowMemberDropdown(false)
    setValue('memberId', member.id)
  }

  // إزالة اختيار العضو
  const handleClearMember = () => {
    setSelectedMember(null)
    setMemberSearch('')
    setValue('memberId', '')
  }

  // فتح قائمة البحث
  const handleMemberSearchFocus = () => {
    setShowMemberDropdown(true)
    if (memberSearch.trim() === '') {
      setFilteredMembers(members.slice(0, 10))
    }
  }

  const onSubmit = async (data: IncomeInput) => {
    try {
      setLoading(true)

      // تحويل البيانات
      const submitData = {
        ...data,
        amount: Number(data.amount),
        date: new Date(data.date),
        memberId: data.memberId,
        description: data.description?.trim() || null,
        notes: data.notes?.trim() || null,
      }

      const isEditing = !!income
      const url = isEditing ? `/api/incomes/${income.id}` : '/api/incomes'
      const method = isEditing ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'حدث خطأ')
      }

      alert(isEditing ? 'تم تعديل الإيراد بنجاح' : 'تم إضافة الإيراد بنجاح')
      onOpenChange(false)
      onSuccess?.()
    } catch (error: any) {
      console.error('خطأ في حفظ الإيراد:', error)
      alert(error.message || 'حدث خطأ في حفظ الإيراد')
    } finally {
      setLoading(false)
    }
  }

  // const getIncomeTypeText = (type: string) => {
  //   switch (type) {
  //     case 'SUBSCRIPTION': return 'اشتراكات'
  //     case 'DONATION': return 'تبرعات'
  //     case 'EVENT': return 'فعاليات'
  //     case 'OTHER': return 'أخرى'
  //     default: return type
  //   }
  // }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[50vw] max-h-[95vh] overflow-y-auto bg-white rounded-2xl shadow-2xl border-0">
        <DialogHeader className="relative p-8 pb-6 border-b border-gray-100 bg-gradient-to-br from-diwan-50 via-blue-50 to-indigo-50 rounded-t-2xl">
          <div className="absolute inset-0 bg-gradient-to-br from-diwan-600/5 via-blue-600/5 to-indigo-600/5 rounded-t-2xl"></div>
          <div className="relative flex items-center gap-4">
            <div className="p-4 bg-white rounded-2xl shadow-lg border border-diwan-200/50 backdrop-blur-sm">
              <DollarSign className="w-7 h-7 text-diwan-600" />
            </div>
            <div className="flex-1">
              <DialogTitle className="text-2xl font-bold text-gray-900 mb-2">
                {income ? 'تعديل الإيراد' : 'إضافة إيراد جديد'}
              </DialogTitle>
              <p className="text-gray-600 text-base leading-relaxed">
                {income
                  ? 'قم بتعديل بيانات الإيراد بعناية لضمان دقة السجلات المالية'
                  : 'قم بإدخال بيانات الإيراد الجديد بعناية لضمان دقة السجلات المالية'
                }
              </p>
            </div>
          </div>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="p-8 space-y-8 bg-gray-50/30">
          {/* القسم الأول: المعلومات الأساسية */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <div className="w-2 h-2 bg-diwan-600 rounded-full"></div>
              المعلومات الأساسية
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <Label htmlFor="amount" className="text-sm font-medium text-gray-700 flex items-center gap-1">
                  المبلغ (دينار أردني)
                  <span className="text-red-500">*</span>
                </Label>
                <div className="relative">
                  <Input
                    id="amount"
                    type="number"
                    step="0.01"
                    min="0"
                    {...register('amount', { valueAsNumber: true })}
                    className={`h-12 text-lg font-medium transition-all duration-200 ${
                      errors.amount
                        ? 'border-red-300 focus:border-red-500 focus:ring-red-200'
                        : 'border-gray-200 focus:border-diwan-500 focus:ring-diwan-100'
                    }`}
                    placeholder="0.00"
                  />
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm">
                    د.أ
                  </div>
                </div>
                {errors.amount && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                    {errors.amount.message}
                  </p>
                )}
              </div>

              <div className="space-y-3">
                <Label htmlFor="date" className="text-sm font-medium text-gray-700 flex items-center gap-1">
                  التاريخ
                  <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="date"
                  type="date"
                  {...register('date')}
                  className={`h-12 transition-all duration-200 ${
                    errors.date
                      ? 'border-red-300 focus:border-red-500 focus:ring-red-200'
                      : 'border-gray-200 focus:border-diwan-500 focus:ring-diwan-100'
                  }`}
                />
                {errors.date && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                    {errors.date.message}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* القسم الثاني: تفاصيل الإيراد */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <div className="w-2 h-2 bg-gold-600 rounded-full"></div>
              تفاصيل الإيراد
            </h3>
            <div className="space-y-6">
              <div className="space-y-3">
                <Label htmlFor="source" className="text-sm font-medium text-gray-700 flex items-center gap-1">
                  مصدر الإيراد
                  <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="source"
                  {...register('source')}
                  placeholder="مثال: اشتراك شهري، تبرع، رسوم فعالية"
                  className={`h-12 transition-all duration-200 ${
                    errors.source
                      ? 'border-red-300 focus:border-red-500 focus:ring-red-200'
                      : 'border-gray-200 focus:border-diwan-500 focus:ring-diwan-100'
                  }`}
                />
                {errors.source && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                    {errors.source.message}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="type" className="text-sm font-medium text-gray-700">
                    نوع الإيراد
                  </Label>
                  <Select
                    value={selectedType}
                    onValueChange={(value) => setValue('type', value as any)}
                  >
                    <SelectTrigger className="h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-100 transition-all duration-200">
                      <SelectValue placeholder="اختر نوع الإيراد" />
                    </SelectTrigger>
                    <SelectContent className="border-gray-200 shadow-lg">
                      <SelectItem value="SUBSCRIPTION" className="hover:bg-diwan-50 focus:bg-diwan-50">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          اشتراكات
                        </div>
                      </SelectItem>
                      <SelectItem value="DONATION" className="hover:bg-green-50 focus:bg-green-50">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          تبرعات
                        </div>
                      </SelectItem>
                      <SelectItem value="EVENT" className="hover:bg-purple-50 focus:bg-purple-50">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                          فعاليات
                        </div>
                      </SelectItem>
                      <SelectItem value="OTHER" className="hover:bg-gray-50 focus:bg-gray-50">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
                          أخرى
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-3">
                  <Label htmlFor="memberSearch" className="text-sm font-medium text-gray-700 flex items-center gap-1">
                    العضو
                    <span className="text-red-500">*</span>
                  </Label>
                  <div className="relative" ref={memberSearchRef}>
                    <div className="relative">
                      <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                      <Input
                        id="memberSearch"
                        type="text"
                        placeholder="ابحث عن عضو..."
                        value={memberSearch}
                        onChange={(e) => setMemberSearch(e.target.value)}
                        onFocus={handleMemberSearchFocus}
                        className={`h-12 pr-12 pl-12 transition-all duration-200 ${
                          errors.memberId
                            ? 'border-red-300 focus:border-red-500 focus:ring-red-200'
                            : 'border-gray-200 focus:border-diwan-500 focus:ring-diwan-100'
                        }`}
                      />
                      {selectedMember && (
                        <button
                          type="button"
                          onClick={handleClearMember}
                          className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500 transition-colors duration-200"
                        >
                          <X className="w-5 h-5" />
                        </button>
                      )}
                    </div>

                    {/* قائمة النتائج */}
                    {showMemberDropdown && (
                      <div className="absolute z-50 w-full mt-2 bg-white border border-gray-200 rounded-xl shadow-xl max-h-64 overflow-y-auto">
                        {filteredMembers.length === 0 ? (
                          <div className="p-4 text-gray-500 text-center">
                            <div className="flex flex-col items-center gap-2">
                              <Search className="w-8 h-8 text-gray-300" />
                              <span className="text-sm">
                                {memberSearch.trim() === '' ? 'ابدأ بالكتابة للبحث عن عضو' : 'لا توجد نتائج مطابقة'}
                              </span>
                            </div>
                          </div>
                        ) : (
                          <>
                            {filteredMembers.map((member) => (
                              <div
                                key={member.id}
                                className="p-4 hover:bg-diwan-50 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors duration-150"
                                onClick={() => handleSelectMember(member)}
                              >
                                <div className="flex items-center gap-3">
                                  <div className="w-8 h-8 bg-diwan-100 rounded-full flex items-center justify-center">
                                    <User className="w-4 h-4 text-diwan-600" />
                                  </div>
                                  <span className="font-medium text-gray-900">{member.name}</span>
                                </div>
                              </div>
                            ))}
                          </>
                        )}
                      </div>
                    )}
                  </div>

                  {/* رسالة الخطأ */}
                  {errors.memberId && (
                    <p className="text-sm text-red-500 flex items-center gap-1">
                      <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                      {errors.memberId.message}
                    </p>
                  )}

                  {/* عرض العضو المختار */}
                  {selectedMember && (
                    <div className="mt-3 p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <User className="w-4 h-4 text-green-600" />
                          </div>
                          <div>
                            <span className="text-green-800 font-semibold">{selectedMember.name}</span>
                            <p className="text-green-600 text-xs">العضو المحدد</p>
                          </div>
                        </div>
                        <button
                          type="button"
                          onClick={handleClearMember}
                          className="text-green-600 hover:text-red-500 transition-colors duration-200 p-1 rounded-full hover:bg-white/50"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* القسم الثالث: معلومات إضافية */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <div className="w-2 h-2 bg-indigo-600 rounded-full"></div>
              معلومات إضافية
            </h3>
            <div className="space-y-6">
              <div className="space-y-3">
                <Label htmlFor="description" className="text-sm font-medium text-gray-700">
                  الوصف (اختياري)
                </Label>
                <Input
                  id="description"
                  {...register('description')}
                  placeholder="وصف إضافي للإيراد"
                  className="h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-100 transition-all duration-200"
                />
              </div>

              <div className="space-y-3">
                <Label htmlFor="notes" className="text-sm font-medium text-gray-700">
                  ملاحظات (اختياري)
                </Label>
                <Textarea
                  id="notes"
                  {...register('notes')}
                  placeholder="أي ملاحظات إضافية حول هذا الإيراد"
                  rows={4}
                  className="border-gray-200 focus:border-diwan-500 focus:ring-diwan-100 transition-all duration-200 resize-none"
                />
              </div>
            </div>
          </div>

          {/* أزرار التحكم */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 mt-6">
            <div className="flex flex-col sm:flex-row justify-end gap-4 sm:gap-3">
              {/* زر الإلغاء */}
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
                className="group relative h-14 px-8 border-2 border-gray-300 text-gray-700 hover:border-red-400 hover:text-red-600 bg-white hover:bg-red-50 transition-all duration-300 font-semibold rounded-xl shadow-sm hover:shadow-md transform hover:scale-[1.02] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                <div className="flex items-center gap-3">
                  <div className="w-5 h-5 rounded-full bg-gray-200 group-hover:bg-red-200 transition-colors duration-300 flex items-center justify-center">
                    <X className="w-3 h-3 text-gray-600 group-hover:text-red-600 transition-colors duration-300" />
                  </div>
                  <span className="text-base">إلغاء</span>
                </div>
                {/* تأثير الخلفية المتحركة */}
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-red-50 to-pink-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div>
              </Button>

              {/* زر الحفظ */}
              <Button
                type="submit"
                disabled={loading}
                className="group relative h-14 px-10 bg-gradient-to-r from-diwan-600 via-diwan-700 to-blue-600 hover:from-diwan-700 hover:via-diwan-800 hover:to-blue-700 text-white font-bold rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] disabled:opacity-70 disabled:cursor-not-allowed disabled:transform-none overflow-hidden"
              >
                {loading ? (
                  <div className="flex items-center gap-3">
                    <div className="relative">
                      <div className="w-5 h-5 border-3 border-white/30 border-t-white rounded-full animate-spin"></div>
                      <div className="absolute inset-0 w-5 h-5 border-3 border-transparent border-t-white/60 rounded-full animate-ping"></div>
                    </div>
                    <span className="text-base">جاري الحفظ...</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center group-hover:bg-white/30 transition-all duration-300">
                      <DollarSign className="w-4 h-4 text-white" />
                    </div>
                    <span className="text-base">{income ? 'حفظ التعديلات' : 'حفظ الإيراد'}</span>
                    <div className="w-2 h-2 rounded-full bg-white/40 group-hover:bg-white/60 transition-all duration-300"></div>
                  </div>
                )}

                {/* تأثير الضوء المتحرك */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 transform translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 ease-out"></div>

                {/* تأثير الخلفية المتوهجة */}
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-diwan-400/20 via-blue-400/20 to-indigo-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl -z-10"></div>
              </Button>
            </div>

            {/* خط فاصل زخرفي */}
            <div className="flex items-center justify-center mt-4 pt-4 border-t border-gray-100">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-diwan-200"></div>
                <div className="w-1 h-1 rounded-full bg-diwan-300"></div>
                <div className="w-2 h-2 rounded-full bg-diwan-200"></div>
              </div>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
