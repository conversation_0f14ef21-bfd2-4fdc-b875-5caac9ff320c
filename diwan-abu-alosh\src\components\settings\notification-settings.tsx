'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
// import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
// import { Button } from '@/components/ui/button'
// import { Badge } from '@/components/ui/badge'
// import { Textarea } from '@/components/ui/textarea'
import {
  Bell,
  Clock,
  Users,
  DollarSign,
  AlertTriangle,
  Settings
} from 'lucide-react'

interface NotificationSettingsProps {
  settings: any
  onChange: (settings: any) => void
  canEdit: boolean
}

interface NotificationSettingsData {
  // إعدادات عامة للإشعارات
  enableNotifications: boolean
  enableSounds: boolean
  enableDesktopNotifications: boolean
  enableEmailNotifications: boolean
  enableSMSNotifications: boolean
  
  // إعدادات الإشعارات حسب النوع
  memberNotifications: {
    newMember: boolean
    memberUpdate: boolean
    memberStatusChange: boolean
    memberPayment: boolean
  }
  
  incomeNotifications: {
    newIncome: boolean
    incomeUpdate: boolean
    paymentReminder: boolean
    paymentOverdue: boolean
  }
  
  expenseNotifications: {
    newExpense: boolean
    expenseUpdate: boolean
    budgetAlert: boolean
    expenseApproval: boolean
  }
  
  systemNotifications: {
    systemUpdate: boolean
    securityAlert: boolean
    backupComplete: boolean
    errorAlert: boolean
  }
  
  // إعدادات التوقيت
  quietHours: {
    enabled: boolean
    startTime: string
    endTime: string
  }
  
  // إعدادات البريد الإلكتروني
  emailSettings: {
    smtpServer: string
    smtpPort: number
    smtpUsername: string
    smtpPassword: string
    fromEmail: string
    fromName: string
    enableSSL: boolean
  }
  
  // إعدادات الرسائل النصية
  smsSettings: {
    provider: string
    apiKey: string
    senderName: string
  }
  
  // قوالب الإشعارات
  templates: {
    welcomeMessage: string
    paymentReminder: string
    paymentConfirmation: string
    systemAlert: string
  }
}

const defaultSettings: NotificationSettingsData = {
  enableNotifications: true,
  enableSounds: true,
  enableDesktopNotifications: true,
  enableEmailNotifications: false,
  enableSMSNotifications: false,
  
  memberNotifications: {
    newMember: true,
    memberUpdate: true,
    memberStatusChange: true,
    memberPayment: true
  },
  
  incomeNotifications: {
    newIncome: true,
    incomeUpdate: true,
    paymentReminder: true,
    paymentOverdue: true
  },
  
  expenseNotifications: {
    newExpense: true,
    expenseUpdate: true,
    budgetAlert: true,
    expenseApproval: true
  },
  
  systemNotifications: {
    systemUpdate: true,
    securityAlert: true,
    backupComplete: true,
    errorAlert: true
  },
  
  quietHours: {
    enabled: false,
    startTime: '22:00',
    endTime: '08:00'
  },
  
  emailSettings: {
    smtpServer: '',
    smtpPort: 587,
    smtpUsername: '',
    smtpPassword: '',
    fromEmail: '',
    fromName: 'ديوان آل أبو علوش',
    enableSSL: true
  },
  
  smsSettings: {
    provider: '',
    apiKey: '',
    senderName: 'ديوان آل أبو علوش'
  },
  
  templates: {
    welcomeMessage: 'مرحباً بك في ديوان آل أبو علوش',
    paymentReminder: 'تذكير: يرجى دفع الاشتراك الشهري',
    paymentConfirmation: 'تم استلام دفعتك بنجاح',
    systemAlert: 'تنبيه من النظام'
  }
}

export default function NotificationSettings({ settings, onChange, canEdit }: NotificationSettingsProps) {
  const [localSettings, setLocalSettings] = useState<NotificationSettingsData>(defaultSettings)
  // const [testingEmail, setTestingEmail] = useState(false)
  // const [testingSMS, setTestingSMS] = useState(false)

  useEffect(() => {
    if (settings) {
      setLocalSettings({ ...defaultSettings, ...settings })
    }
  }, [settings])

  const handleChange = (key: string, value: any) => {
    const keys = key.split('.')
    let newSettings = { ...localSettings }
    
    if (keys.length === 1) {
      newSettings = { ...newSettings, [keys[0]]: value }
    } else if (keys.length === 2) {
      newSettings = {
        ...newSettings,
        [keys[0]]: {
          ...newSettings[keys[0] as keyof NotificationSettingsData],
          [keys[1]]: value
        }
      }
    }
    
    setLocalSettings(newSettings)
    onChange(newSettings)
  }

  // const testEmailSettings = async () => {
  //   setTestingEmail(true)
  //   try {
  //     // هنا يمكن إضافة API call لاختبار إعدادات البريد الإلكتروني
  //     await new Promise(resolve => setTimeout(resolve, 2000)) // محاكاة
  //     alert('تم إرسال رسالة اختبار بنجاح!')
  //   } catch {
  //     alert('فشل في إرسال رسالة الاختبار')
  //   } finally {
  //     setTestingEmail(false)
  //   }
  // }

  // const testSMSSettings = async () => {
  //   setTestingSMS(true)
  //   try {
  //     // هنا يمكن إضافة API call لاختبار إعدادات الرسائل النصية
  //     await new Promise(resolve => setTimeout(resolve, 2000)) // محاكاة
  //     alert('تم إرسال رسالة نصية اختبارية بنجاح!')
  //   } catch {
  //     alert('فشل في إرسال الرسالة النصية الاختبارية')
  //   } finally {
  //     setTestingSMS(false)
  //   }
  // }

  return (
    <div className="space-y-6">
      {/* General settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="w-5 h-5 text-blue-600" />
            الإعدادات العامة للإشعارات
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>تفعيل الإشعارات</Label>
                <p className="text-sm text-gray-600">تفعيل أو إلغاء جميع الإشعارات</p>
              </div>
              <Switch
                checked={localSettings.enableNotifications}
                onCheckedChange={(checked) => handleChange('enableNotifications', checked)}
                disabled={!canEdit}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>الأصوات</Label>
                <p className="text-sm text-gray-600">تشغيل أصوات الإشعارات</p>
              </div>
              <Switch
                checked={localSettings.enableSounds}
                onCheckedChange={(checked) => handleChange('enableSounds', checked)}
                disabled={!canEdit || !localSettings.enableNotifications}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>إشعارات سطح المكتب</Label>
                <p className="text-sm text-gray-600">عرض إشعارات في المتصفح</p>
              </div>
              <Switch
                checked={localSettings.enableDesktopNotifications}
                onCheckedChange={(checked) => handleChange('enableDesktopNotifications', checked)}
                disabled={!canEdit || !localSettings.enableNotifications}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>إشعارات البريد الإلكتروني</Label>
                <p className="text-sm text-gray-600">إرسال إشعارات عبر البريد الإلكتروني</p>
              </div>
              <Switch
                checked={localSettings.enableEmailNotifications}
                onCheckedChange={(checked) => handleChange('enableEmailNotifications', checked)}
                disabled={!canEdit || !localSettings.enableNotifications}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>الرسائل النصية</Label>
                <p className="text-sm text-gray-600">إرسال إشعارات عبر الرسائل النصية</p>
              </div>
              <Switch
                checked={localSettings.enableSMSNotifications}
                onCheckedChange={(checked) => handleChange('enableSMSNotifications', checked)}
                disabled={!canEdit || !localSettings.enableNotifications}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Member notifications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5 text-green-600" />
            إشعارات الأعضاء
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>عضو جديد</Label>
                <p className="text-sm text-gray-600">إشعار عند إضافة عضو جديد</p>
              </div>
              <Switch
                checked={localSettings.memberNotifications.newMember}
                onCheckedChange={(checked) => handleChange('memberNotifications.newMember', checked)}
                disabled={!canEdit || !localSettings.enableNotifications}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>تحديث بيانات العضو</Label>
                <p className="text-sm text-gray-600">إشعار عند تحديث بيانات عضو</p>
              </div>
              <Switch
                checked={localSettings.memberNotifications.memberUpdate}
                onCheckedChange={(checked) => handleChange('memberNotifications.memberUpdate', checked)}
                disabled={!canEdit || !localSettings.enableNotifications}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>تغيير حالة العضو</Label>
                <p className="text-sm text-gray-600">إشعار عند تغيير حالة العضو</p>
              </div>
              <Switch
                checked={localSettings.memberNotifications.memberStatusChange}
                onCheckedChange={(checked) => handleChange('memberNotifications.memberStatusChange', checked)}
                disabled={!canEdit || !localSettings.enableNotifications}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>دفعات الأعضاء</Label>
                <p className="text-sm text-gray-600">إشعار عند دفع الاشتراكات</p>
              </div>
              <Switch
                checked={localSettings.memberNotifications.memberPayment}
                onCheckedChange={(checked) => handleChange('memberNotifications.memberPayment', checked)}
                disabled={!canEdit || !localSettings.enableNotifications}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Revenue notifications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="w-5 h-5 text-emerald-600" />
            إشعارات الإيرادات
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>إيراد جديد</Label>
                <p className="text-sm text-gray-600">إشعار عند إضافة إيراد جديد</p>
              </div>
              <Switch
                checked={localSettings.incomeNotifications.newIncome}
                onCheckedChange={(checked) => handleChange('incomeNotifications.newIncome', checked)}
                disabled={!canEdit || !localSettings.enableNotifications}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>تحديث الإيراد</Label>
                <p className="text-sm text-gray-600">إشعار عند تحديث إيراد</p>
              </div>
              <Switch
                checked={localSettings.incomeNotifications.incomeUpdate}
                onCheckedChange={(checked) => handleChange('incomeNotifications.incomeUpdate', checked)}
                disabled={!canEdit || !localSettings.enableNotifications}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>تذكير الدفع</Label>
                <p className="text-sm text-gray-600">تذكير الأعضاء بموعد الدفع</p>
              </div>
              <Switch
                checked={localSettings.incomeNotifications.paymentReminder}
                onCheckedChange={(checked) => handleChange('incomeNotifications.paymentReminder', checked)}
                disabled={!canEdit || !localSettings.enableNotifications}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>تأخير الدفع</Label>
                <p className="text-sm text-gray-600">تنبيه عند تأخر الدفع</p>
              </div>
              <Switch
                checked={localSettings.incomeNotifications.paymentOverdue}
                onCheckedChange={(checked) => handleChange('incomeNotifications.paymentOverdue', checked)}
                disabled={!canEdit || !localSettings.enableNotifications}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Expense notifications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-red-600" />
            إشعارات المصروفات
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>مصروف جديد</Label>
                <p className="text-sm text-gray-600">إشعار عند إضافة مصروف جديد</p>
              </div>
              <Switch
                checked={localSettings.expenseNotifications.newExpense}
                onCheckedChange={(checked) => handleChange('expenseNotifications.newExpense', checked)}
                disabled={!canEdit || !localSettings.enableNotifications}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>تحديث المصروف</Label>
                <p className="text-sm text-gray-600">إشعار عند تحديث مصروف</p>
              </div>
              <Switch
                checked={localSettings.expenseNotifications.expenseUpdate}
                onCheckedChange={(checked) => handleChange('expenseNotifications.expenseUpdate', checked)}
                disabled={!canEdit || !localSettings.enableNotifications}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>تنبيه الميزانية</Label>
                <p className="text-sm text-gray-600">تنبيه عند تجاوز حد الميزانية</p>
              </div>
              <Switch
                checked={localSettings.expenseNotifications.budgetAlert}
                onCheckedChange={(checked) => handleChange('expenseNotifications.budgetAlert', checked)}
                disabled={!canEdit || !localSettings.enableNotifications}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>موافقة المصروف</Label>
                <p className="text-sm text-gray-600">إشعار عند الحاجة لموافقة على مصروف</p>
              </div>
              <Switch
                checked={localSettings.expenseNotifications.expenseApproval}
                onCheckedChange={(checked) => handleChange('expenseNotifications.expenseApproval', checked)}
                disabled={!canEdit || !localSettings.enableNotifications}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* System notifications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5 text-purple-600" />
            إشعارات النظام
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>تحديثات النظام</Label>
                <p className="text-sm text-gray-600">إشعار عند توفر تحديثات</p>
              </div>
              <Switch
                checked={localSettings.systemNotifications.systemUpdate}
                onCheckedChange={(checked) => handleChange('systemNotifications.systemUpdate', checked)}
                disabled={!canEdit || !localSettings.enableNotifications}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>تنبيهات الأمان</Label>
                <p className="text-sm text-gray-600">تنبيهات أمنية مهمة</p>
              </div>
              <Switch
                checked={localSettings.systemNotifications.securityAlert}
                onCheckedChange={(checked) => handleChange('systemNotifications.securityAlert', checked)}
                disabled={!canEdit || !localSettings.enableNotifications}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>اكتمال النسخ الاحتياطي</Label>
                <p className="text-sm text-gray-600">إشعار عند اكتمال النسخ الاحتياطي</p>
              </div>
              <Switch
                checked={localSettings.systemNotifications.backupComplete}
                onCheckedChange={(checked) => handleChange('systemNotifications.backupComplete', checked)}
                disabled={!canEdit || !localSettings.enableNotifications}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>تنبيهات الأخطاء</Label>
                <p className="text-sm text-gray-600">إشعار عند حدوث أخطاء في النظام</p>
              </div>
              <Switch
                checked={localSettings.systemNotifications.errorAlert}
                onCheckedChange={(checked) => handleChange('systemNotifications.errorAlert', checked)}
                disabled={!canEdit || !localSettings.enableNotifications}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quiet hours */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5 text-orange-600" />
            ساعات الهدوء
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label>تفعيل ساعات الهدوء</Label>
              <p className="text-sm text-gray-600">إيقاف الإشعارات في أوقات محددة</p>
            </div>
            <Switch
              checked={localSettings.quietHours.enabled}
              onCheckedChange={(checked) => handleChange('quietHours.enabled', checked)}
              disabled={!canEdit || !localSettings.enableNotifications}
            />
          </div>

          {localSettings.quietHours.enabled && (
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="startTime">وقت البداية</Label>
                <Input
                  id="startTime"
                  type="time"
                  value={localSettings.quietHours.startTime}
                  onChange={(e) => handleChange('quietHours.startTime', e.target.value)}
                  disabled={!canEdit}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="endTime">وقت النهاية</Label>
                <Input
                  id="endTime"
                  type="time"
                  value={localSettings.quietHours.endTime}
                  onChange={(e) => handleChange('quietHours.endTime', e.target.value)}
                  disabled={!canEdit}
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
