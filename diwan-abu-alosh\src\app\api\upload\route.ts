import { NextRequest, NextResponse } from 'next/server'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    const data = await request.formData()
    const file: File | null = data.get('file') as unknown as File
    const uploadType = data.get('type') as string || 'member' // 'member' أو 'gallery' أو 'logo' أو 'favicon'

    if (!file) {
      return NextResponse.json({ error: 'لم يتم اختيار ملف' }, { status: 400 })
    }

    // التحقق من نوع الملف
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'نوع الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG, WebP)' },
        { status: 400 }
      )
    }

    // التحقق من حجم الملف (5MB كحد أقصى)
    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت' },
        { status: 400 }
      )
    }

    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // إنشاء اسم ملف فريد
    const timestamp = Date.now()
    const randomString = Math.random().toString(36).substring(2, 15)
    const fileExtension = file.name.split('.').pop()

    // تحديد اسم الملف ومجلد الرفع حسب النوع
    let fileName: string
    let uploadDir: string
    let relativePath: string

    if (uploadType === 'gallery') {
      fileName = `gallery_${timestamp}_${randomString}.${fileExtension}`
      uploadDir = join(process.cwd(), 'public', 'uploads', 'gallery')
      relativePath = `/uploads/gallery/${fileName}`
    } else if (uploadType === 'logo' || uploadType === 'favicon') {
      fileName = `${uploadType}_${timestamp}.${fileExtension}`
      uploadDir = join(process.cwd(), 'public', 'uploads', 'branding')
      relativePath = `/uploads/branding/${fileName}`
    } else {
      fileName = `member_${timestamp}_${randomString}.${fileExtension}`
      uploadDir = join(process.cwd(), 'public', 'uploads', 'members')
      relativePath = `/uploads/members/${fileName}`
    }

    // إنشاء مجلد الرفع إذا لم يكن موجوداً
    try {
      await mkdir(uploadDir, { recursive: true })
    } catch {
      // المجلد موجود بالفعل
    }

    // حفظ الملف
    const filePath = join(uploadDir, fileName)
    await writeFile(filePath, buffer)

    return NextResponse.json({
      success: true,
      url: relativePath,
      filePath: relativePath,
      fileName: fileName,
      fileSize: file.size,
      fileType: file.type
    })

  } catch (error) {
    console.error('خطأ في رفع الملف:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في رفع الملف' },
      { status: 500 }
    )
  }
}

// حذف الملف
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const filePath = searchParams.get('path')

    if (!filePath) {
      return NextResponse.json({ error: 'مسار الملف مطلوب' }, { status: 400 })
    }

    // التحقق من أن المسار آمن
    if (!filePath.startsWith('/uploads/members/') &&
        !filePath.startsWith('/uploads/gallery/') &&
        !filePath.startsWith('/uploads/branding/')) {
      return NextResponse.json({ error: 'مسار غير صحيح' }, { status: 400 })
    }

    const fullPath = join(process.cwd(), 'public', filePath)
    
    try {
      const fs = await import('fs/promises')
      await fs.unlink(fullPath)
      return NextResponse.json({ success: true })
    } catch {
      // الملف غير موجود أو لا يمكن حذفه
      return NextResponse.json({ success: true }) // نعتبرها نجاح لأن الهدف تحقق
    }

  } catch (error) {
    console.error('خطأ في حذف الملف:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في حذف الملف' },
      { status: 500 }
    )
  }
}
