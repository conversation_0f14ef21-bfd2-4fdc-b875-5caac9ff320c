(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5589],{6736:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("chart-pie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]])},17313:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>c,av:()=>d,j7:()=>o,tU:()=>l});var a=s(95155),n=s(12115),r=s(60704),i=s(59434);let l=r.bL,o=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(r.B8,{ref:t,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...n})});o.displayName=r.B8.displayName;let c=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(r.l9,{ref:t,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...n})});c.displayName=r.l9.displayName;let d=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(r.UC,{ref:t,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...n})});d.displayName=r.UC.displayName},26126:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var a=s(95155);s(12115);var n=s(74466),r=s(59434);let i=(0,n.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-100 text-green-800 hover:bg-green-200",warning:"border-transparent bg-slate-100 text-slate-800 hover:bg-slate-200"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:s,...n}=e;return(0,a.jsx)("div",{className:(0,r.cn)(i({variant:s}),t),...n})}},30285:(e,t,s)=>{"use strict";s.d(t,{$:()=>o});var a=s(95155),n=s(12115),r=s(74466),i=s(59434);let l=(0,r.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-semibold transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-95",{variants:{variant:{default:"bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-primary-600 hover:to-primary-700",destructive:"bg-gradient-to-r from-danger-500 to-danger-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-danger-600 hover:to-danger-700",outline:"border-2 border-primary-300 bg-transparent text-primary-700 hover:bg-primary-50 hover:border-primary-400 hover:shadow-md",secondary:"bg-gradient-to-r from-secondary-100 to-secondary-200 text-secondary-800 shadow-md hover:shadow-lg hover:-translate-y-0.5 hover:from-secondary-200 hover:to-secondary-300",ghost:"text-primary-600 hover:bg-primary-100 hover:text-primary-800",link:"text-primary-600 underline-offset-4 hover:underline hover:text-primary-800",success:"bg-gradient-to-r from-success-500 to-success-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-success-600 hover:to-success-700",warning:"bg-gradient-to-r from-warning-500 to-warning-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-warning-600 hover:to-warning-700",info:"bg-gradient-to-r from-info-500 to-info-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-info-600 hover:to-info-700",accent:"bg-gradient-to-r from-gold-500 to-gold-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:from-gold-600 hover:to-gold-700"},size:{default:"h-11 px-6 py-2.5",sm:"h-9 rounded-lg px-4 text-xs",lg:"h-13 rounded-xl px-8 text-base",icon:"h-11 w-11",xs:"h-8 rounded-md px-3 text-xs"}},defaultVariants:{variant:"default",size:"default"}}),o=n.forwardRef((e,t)=>{let{className:s,variant:n,size:r,...o}=e;return(0,a.jsx)("button",{className:(0,i.cn)(l({variant:n,size:r,className:s})),ref:t,...o})});o.displayName="Button"},33109:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},49618:(e,t,s)=>{Promise.resolve().then(s.bind(s,58410))},53904:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},58410:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>M});var a=s(95155),n=s(12115),r=s(12108),i=s(30285),l=s(66695),o=s(26126),c=s(17313),d=s(53904),m=s(72713),x=s(57434),h=s(91788),g=s(17580),p=s(33109),y=s(68500),u=s(55868),v=s(79397),b=s(19946);let f=(0,b.A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);var j=s(69074);let N=(0,b.A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);var w=s(6736),A=s(59434);function M(){let{data:e}=(0,r.useSession)(),[t,b]=(0,n.useState)(null),[M,k]=(0,n.useState)(!0),[C,z]=(0,n.useState)("current-year"),[E,I]=(0,n.useState)(!1),R=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{e?I(!0):k(!0);let t=await fetch("/api/reports?period=".concat(C));if(!t.ok)throw Error("فشل في جلب بيانات التقارير");let s=await t.json();b(s)}catch(e){console.error("خطأ في جلب بيانات التقارير:",e),alert("حدث خطأ في جلب بيانات التقارير")}finally{k(!1),I(!1)}};(0,n.useEffect)(()=>{e&&R()},[C,e]);let D=async()=>{if(t)try{let e=new Date().toLocaleDateString("ar-JO",{year:"numeric",month:"long",day:"numeric"}),s='\n<!DOCTYPE html>\n<html dir="rtl" lang="ar">\n<head>\n    <meta charset="UTF-8">\n    <title>تقرير شامل - ديوان أبو علوش</title>\n    <style>\n        @import url(\'https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap\');\n\n        * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n        }\n\n        body {\n            font-family: \'Cairo\', Arial, sans-serif;\n            line-height: 1.6;\n            color: #333;\n            background: white;\n            padding: 20px;\n            direction: rtl;\n        }\n\n        .header {\n            text-align: center;\n            margin-bottom: 30px;\n            border-bottom: 3px solid #2563eb;\n            padding-bottom: 20px;\n        }\n\n        .header h1 {\n            color: #2563eb;\n            font-size: 28px;\n            font-weight: 700;\n            margin-bottom: 10px;\n        }\n\n        .header .subtitle {\n            color: #666;\n            font-size: 16px;\n            margin-bottom: 5px;\n        }\n\n        .summary-grid {\n            display: grid;\n            grid-template-columns: repeat(2, 1fr);\n            gap: 20px;\n            margin-bottom: 30px;\n        }\n\n        .summary-card {\n            background: #f8fafc;\n            border: 1px solid #e2e8f0;\n            border-radius: 8px;\n            padding: 15px;\n            text-align: center;\n        }\n\n        .summary-card h3 {\n            color: #475569;\n            font-size: 14px;\n            margin-bottom: 8px;\n            font-weight: 600;\n        }\n\n        .summary-card .value {\n            font-size: 20px;\n            font-weight: 700;\n            color: #1e293b;\n        }\n\n        .section {\n            margin-bottom: 30px;\n            page-break-inside: avoid;\n        }\n\n        .section h2 {\n            color: #2563eb;\n            font-size: 18px;\n            font-weight: 600;\n            margin-bottom: 15px;\n            border-bottom: 2px solid #e2e8f0;\n            padding-bottom: 5px;\n        }\n\n        .table {\n            width: 100%;\n            border-collapse: collapse;\n            margin-bottom: 20px;\n        }\n\n        .table th,\n        .table td {\n            padding: 8px 12px;\n            text-align: right;\n            border-bottom: 1px solid #e2e8f0;\n            font-size: 12px;\n        }\n\n        .table th {\n            background: #f1f5f9;\n            font-weight: 600;\n            color: #475569;\n        }\n\n        .income {\n            color: #059669;\n            font-weight: 600;\n        }\n\n        .expense {\n            color: #dc2626;\n            font-weight: 600;\n        }\n\n        @media print {\n            body { padding: 0; }\n            .section { page-break-inside: avoid; }\n        }\n    </style>\n</head>\n<body>\n    <div class="header">\n        <h1>تقرير شامل - ديوان أبو علوش</h1>\n        <div class="subtitle">فترة التقرير: '.concat({"current-month":"الشهر الحالي","last-month":"الشهر الماضي","current-year":"السنة الحالية","last-year":"السنة الماضية","all-time":"جميع الفترات"}[C]||C,'</div>\n        <div class="subtitle">تاريخ الإنشاء: ').concat(e,'</div>\n    </div>\n\n    <div class="summary-grid">\n        <div class="summary-card">\n            <h3>إجمالي الأعضاء</h3>\n            <div class="value">').concat(t.summary.totalMembers,'</div>\n        </div>\n        <div class="summary-card">\n            <h3>الأعضاء النشطون</h3>\n            <div class="value">').concat(t.summary.activeMembers,'</div>\n        </div>\n        <div class="summary-card">\n            <h3>إجمالي الإيرادات</h3>\n            <div class="value income">').concat((0,A.vv)(t.summary.totalIncomes),'</div>\n        </div>\n        <div class="summary-card">\n            <h3>إجمالي المصروفات</h3>\n            <div class="value expense">').concat((0,A.vv)(t.summary.totalExpenses),'</div>\n        </div>\n        <div class="summary-card">\n            <h3>الرصيد الحالي</h3>\n            <div class="value ').concat(t.summary.balance>=0?"income":"expense",'">').concat((0,A.vv)(t.summary.balance),'</div>\n        </div>\n        <div class="summary-card">\n            <h3>إجمالي الأنشطة</h3>\n            <div class="value">').concat(t.summary.totalActivities,'</div>\n        </div>\n    </div>\n\n    <div class="section">\n        <h2>أكثر الأعضاء مساهمة</h2>\n        <table class="table">\n            <thead>\n                <tr>\n                    <th>الترتيب</th>\n                    <th>اسم العضو</th>\n                    <th>عدد المساهمات</th>\n                    <th>إجمالي المساهمات</th>\n                </tr>\n            </thead>\n            <tbody>\n                ').concat(t.topContributors.slice(0,10).map((e,t)=>"\n                    <tr>\n                        <td>".concat(t+1,"</td>\n                        <td>").concat(e.name,"</td>\n                        <td>").concat(e.incomes.length,'</td>\n                        <td class="income">').concat((0,A.vv)(e.totalContributions),"</td>\n                    </tr>\n                ")).join(""),'\n            </tbody>\n        </table>\n    </div>\n\n    <div class="section">\n        <h2>آخر المعاملات</h2>\n        <table class="table">\n            <thead>\n                <tr>\n                    <th>النوع</th>\n                    <th>الوصف</th>\n                    <th>المبلغ</th>\n                    <th>التاريخ</th>\n                    <th>العضو/المستفيد</th>\n                </tr>\n            </thead>\n            <tbody>\n                ').concat(t.recentTransactions.slice(0,15).map(e=>"\n                    <tr>\n                        <td>".concat("income"===e.type?"إيراد":"مصروف","</td>\n                        <td>").concat(e.description,'</td>\n                        <td class="').concat("income"===e.type?"income":"expense",'">\n                            ').concat((0,A.vv)(e.amount),"\n                        </td>\n                        <td>").concat((0,A.Yq)(e.date),"</td>\n                        <td>").concat(e.memberName||"غير محدد","</td>\n                    </tr>\n                ")).join(""),"\n            </tbody>\n        </table>\n    </div>\n</body>\n</html>"),a=window.open("","_blank");a?(a.document.write(s),a.document.close(),setTimeout(()=>{a.print()},1e3)):alert("يرجى السماح بفتح النوافذ المنبثقة لتصدير التقرير")}catch(e){console.error("خطأ في تصدير التقرير:",e),alert("حدث خطأ في تصدير التقرير")}},T=async()=>{if(t)try{let e=(await Promise.all([s.e(4316),s.e(3930),s.e(4628),s.e(913)]).then(s.t.bind(s,64227,23))).default,a=new Date().toLocaleDateString("ar-JO",{year:"numeric",month:"long",day:"numeric"}),n=document.createElement("div");n.innerHTML='\n        <div style="font-family: \'Cairo\', Arial, sans-serif; direction: rtl; padding: 20px; color: #333;">\n          <div style="text-align: center; margin-bottom: 30px; border-bottom: 3px solid #2563eb; padding-bottom: 20px;">\n            <h1 style="color: #2563eb; font-size: 24px; font-weight: 700; margin-bottom: 10px;">\n              تقرير شامل - ديوان أبو علوش\n            </h1>\n            <div style="color: #666; font-size: 14px; margin-bottom: 5px;">\n              فترة التقرير: '.concat({"current-month":"الشهر الحالي","last-month":"الشهر الماضي","current-year":"السنة الحالية","last-year":"السنة الماضية","all-time":"جميع الفترات"}[C]||C,'\n            </div>\n            <div style="color: #666; font-size: 14px;">\n              تاريخ الإنشاء: ').concat(a,'\n            </div>\n          </div>\n\n          <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin-bottom: 30px;">\n            <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; text-align: center;">\n              <h3 style="color: #475569; font-size: 12px; margin-bottom: 8px; font-weight: 600;">إجمالي الأعضاء</h3>\n              <div style="font-size: 18px; font-weight: 700; color: #1e293b;">').concat(t.summary.totalMembers,'</div>\n            </div>\n            <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; text-align: center;">\n              <h3 style="color: #475569; font-size: 12px; margin-bottom: 8px; font-weight: 600;">الأعضاء النشطون</h3>\n              <div style="font-size: 18px; font-weight: 700; color: #1e293b;">').concat(t.summary.activeMembers,'</div>\n            </div>\n            <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; text-align: center;">\n              <h3 style="color: #475569; font-size: 12px; margin-bottom: 8px; font-weight: 600;">إجمالي الإيرادات</h3>\n              <div style="font-size: 18px; font-weight: 700; color: #059669;">').concat((0,A.vv)(t.summary.totalIncomes),'</div>\n            </div>\n            <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; text-align: center;">\n              <h3 style="color: #475569; font-size: 12px; margin-bottom: 8px; font-weight: 600;">إجمالي المصروفات</h3>\n              <div style="font-size: 18px; font-weight: 700; color: #dc2626;">').concat((0,A.vv)(t.summary.totalExpenses),'</div>\n            </div>\n            <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; text-align: center;">\n              <h3 style="color: #475569; font-size: 12px; margin-bottom: 8px; font-weight: 600;">الرصيد الحالي</h3>\n              <div style="font-size: 18px; font-weight: 700; color: ').concat(t.summary.balance>=0?"#059669":"#dc2626",';">').concat((0,A.vv)(t.summary.balance),'</div>\n            </div>\n            <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; text-align: center;">\n              <h3 style="color: #475569; font-size: 12px; margin-bottom: 8px; font-weight: 600;">إجمالي الأنشطة</h3>\n              <div style="font-size: 18px; font-weight: 700; color: #1e293b;">').concat(t.summary.totalActivities,'</div>\n            </div>\n          </div>\n\n          <div style="margin-bottom: 30px;">\n            <h2 style="color: #2563eb; font-size: 16px; font-weight: 600; margin-bottom: 15px; border-bottom: 2px solid #e2e8f0; padding-bottom: 5px;">\n              أكثر الأعضاء مساهمة\n            </h2>\n            <table style="width: 100%; border-collapse: collapse;">\n              <thead>\n                <tr style="background: #f1f5f9;">\n                  <th style="padding: 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 11px; font-weight: 600; color: #475569;">الترتيب</th>\n                  <th style="padding: 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 11px; font-weight: 600; color: #475569;">اسم العضو</th>\n                  <th style="padding: 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 11px; font-weight: 600; color: #475569;">عدد المساهمات</th>\n                  <th style="padding: 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 11px; font-weight: 600; color: #475569;">إجمالي المساهمات</th>\n                </tr>\n              </thead>\n              <tbody>\n                ').concat(t.topContributors.slice(0,10).map((e,t)=>'\n                  <tr>\n                    <td style="padding: 6px 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 10px;">'.concat(t+1,'</td>\n                    <td style="padding: 6px 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 10px;">').concat(e.name,'</td>\n                    <td style="padding: 6px 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 10px;">').concat(e.incomes.length,'</td>\n                    <td style="padding: 6px 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 10px; color: #059669; font-weight: 600;">').concat((0,A.vv)(e.totalContributions),"</td>\n                  </tr>\n                ")).join(""),'\n              </tbody>\n            </table>\n          </div>\n\n          <div style="margin-bottom: 30px;">\n            <h2 style="color: #2563eb; font-size: 16px; font-weight: 600; margin-bottom: 15px; border-bottom: 2px solid #e2e8f0; padding-bottom: 5px;">\n              آخر المعاملات\n            </h2>\n            <table style="width: 100%; border-collapse: collapse;">\n              <thead>\n                <tr style="background: #f1f5f9;">\n                  <th style="padding: 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 11px; font-weight: 600; color: #475569;">النوع</th>\n                  <th style="padding: 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 11px; font-weight: 600; color: #475569;">الوصف</th>\n                  <th style="padding: 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 11px; font-weight: 600; color: #475569;">المبلغ</th>\n                  <th style="padding: 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 11px; font-weight: 600; color: #475569;">التاريخ</th>\n                  <th style="padding: 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 11px; font-weight: 600; color: #475569;">العضو/المستفيد</th>\n                </tr>\n              </thead>\n              <tbody>\n                ').concat(t.recentTransactions.slice(0,15).map(e=>'\n                  <tr>\n                    <td style="padding: 6px 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 10px;">'.concat("income"===e.type?"إيراد":"مصروف",'</td>\n                    <td style="padding: 6px 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 10px;">').concat(e.description,'</td>\n                    <td style="padding: 6px 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 10px; color: ').concat("income"===e.type?"#059669":"#dc2626",'; font-weight: 600;">').concat((0,A.vv)(e.amount),'</td>\n                    <td style="padding: 6px 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 10px;">').concat((0,A.Yq)(e.date),'</td>\n                    <td style="padding: 6px 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 10px;">').concat(e.memberName||"غير محدد","</td>\n                  </tr>\n                ")).join(""),"\n              </tbody>\n            </table>\n          </div>\n        </div>\n      ");let r={margin:1,filename:"تقرير_شامل_".concat(C,"_").concat(new Date().toISOString().split("T")[0],".pdf"),image:{type:"jpeg",quality:.98},html2canvas:{scale:2,useCORS:!0},jsPDF:{unit:"in",format:"a4",orientation:"portrait"}};await e().set(r).from(n).save()}catch(e){console.error("خطأ في تصدير التقرير:",e),alert("حدث خطأ في تصدير التقرير")}};return M?(0,a.jsxs)("div",{className:"space-y-6 arabic-text",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",style:{fontFamily:"Cairo, Almarai, sans-serif"},children:"التقارير والإحصائيات"}),(0,a.jsx)("p",{className:"text-gray-600",style:{fontFamily:"Cairo, Almarai, sans-serif"},children:"تقارير شاملة عن أنشطة الديوان المالية والإدارية"})]}),(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("div",{className:"text-gray-500",style:{fontFamily:"Cairo, Almarai, sans-serif"},children:"جاري تحميل التقارير..."})})]}):t?(0,a.jsxs)("div",{className:"space-y-6 arabic-text",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",style:{fontFamily:"Cairo, Almarai, sans-serif"},children:"التقارير والإحصائيات"}),(0,a.jsx)("p",{className:"text-gray-600",style:{fontFamily:"Cairo, Almarai, sans-serif"},children:"تقارير شاملة عن أنشطة الديوان المالية والإدارية"})]}),(0,a.jsxs)("div",{className:"flex space-x-2 space-x-reverse",children:[(0,a.jsxs)("select",{value:C,onChange:e=>z(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-diwan-500",children:[(0,a.jsx)("option",{value:"current-month",children:"الشهر الحالي"}),(0,a.jsx)("option",{value:"last-month",children:"الشهر الماضي"}),(0,a.jsx)("option",{value:"current-year",children:"السنة الحالية"}),(0,a.jsx)("option",{value:"last-year",children:"السنة الماضية"}),(0,a.jsx)("option",{value:"all-time",children:"جميع الفترات"})]}),(0,a.jsxs)(i.$,{variant:"outline",onClick:()=>{R(!0)},disabled:E,className:"text-gray-600 border-gray-300 hover:bg-gray-50",children:[(0,a.jsx)(d.A,{className:"w-4 h-4 ml-2 ".concat(E?"animate-spin":"")}),E?"جاري التحديث...":"تحديث"]}),(0,a.jsxs)(i.$,{variant:"outline",onClick:()=>{if(!t||!t.monthlyData)return;let e=new Blob(["\uFEFF"+[["الشهر","الإيرادات","المصروفات","الرصيد"],...t.monthlyData.map(e=>[new Date(e.month+"-01").toLocaleDateString("ar-JO",{year:"numeric",month:"long"}),(0,A.vv)(e.incomes),(0,A.vv)(e.expenses),(0,A.vv)(e.balance)])].map(e=>e.join(",")).join("\n")],{type:"text/csv;charset=utf-8;"}),s=document.createElement("a");s.href=URL.createObjectURL(e),s.download="البيانات_الشهرية_".concat(C,"_").concat(new Date().toISOString().split("T")[0],".csv"),s.click()},className:"text-blue-600 border-blue-600 hover:bg-blue-50",children:[(0,a.jsx)(m.A,{className:"w-4 h-4 ml-2"}),"البيانات الشهرية"]}),(0,a.jsxs)(i.$,{onClick:D,className:"bg-red-600 hover:bg-red-700 text-white",children:[(0,a.jsx)(x.A,{className:"w-4 h-4 ml-2"}),"طباعة PDF"]}),(0,a.jsxs)(i.$,{onClick:T,className:"bg-purple-600 hover:bg-purple-700 text-white",children:[(0,a.jsx)(h.A,{className:"w-4 h-4 ml-2"}),"تحميل PDF"]}),(0,a.jsxs)(i.$,{onClick:()=>{if(!t)return;let e=new Blob(["\uFEFF"+[["نوع البيان","القيمة"],["إجمالي الأعضاء",t.summary.totalMembers.toString()],["الأعضاء النشطون",t.summary.activeMembers.toString()],["إجمالي الإيرادات",(0,A.vv)(t.summary.totalIncomes)],["إجمالي المصروفات",(0,A.vv)(t.summary.totalExpenses)],["الرصيد الحالي",(0,A.vv)(t.summary.balance)],["إجمالي الأنشطة",t.summary.totalActivities.toString()],[],["أكثر الأعضاء مساهمة"],["الترتيب","الاسم","إجمالي المساهمات"],...t.topContributors.slice(0,10).map((e,t)=>[(t+1).toString(),e.name,(0,A.vv)(e.totalContributions)]),[],["آخر المعاملات"],["النوع","الوصف","المبلغ","التاريخ","العضو/المستفيد"],...t.recentTransactions.slice(0,20).map(e=>["income"===e.type?"إيراد":"مصروف",e.description,(0,A.vv)(e.amount),(0,A.Yq)(e.date),e.memberName||"غير محدد"])].map(e=>Array.isArray(e)?e.join(","):e).join("\n")],{type:"text/csv;charset=utf-8;"}),s=document.createElement("a");s.href=URL.createObjectURL(e),s.download="تقرير_شامل_".concat(C,"_").concat(new Date().toISOString().split("T")[0],".csv"),s.click()},className:"bg-green-600 hover:bg-green-700 text-white",children:[(0,a.jsx)(h.A,{className:"w-4 h-4 ml-2"}),"تصدير CSV"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-5",children:[(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(l.ZB,{className:"text-sm font-medium",children:"إجمالي الأعضاء"}),(0,a.jsx)(g.A,{className:"h-4 w-4 text-blue-600"})]}),(0,a.jsxs)(l.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:t.summary.totalMembers}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[t.summary.activeMembers," عضو نشط"]})]})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(l.ZB,{className:"text-sm font-medium",children:"إجمالي الإيرادات"}),(0,a.jsx)(p.A,{className:"h-4 w-4 text-green-600"})]}),(0,a.jsx)(l.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:(0,A.vv)(t.summary.totalIncomes)})})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(l.ZB,{className:"text-sm font-medium",children:"إجمالي المصروفات"}),(0,a.jsx)(y.A,{className:"h-4 w-4 text-red-600"})]}),(0,a.jsx)(l.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:(0,A.vv)(t.summary.totalExpenses)})})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(l.ZB,{className:"text-sm font-medium",children:"الرصيد الحالي"}),(0,a.jsx)(u.A,{className:"h-4 w-4 text-blue-600"})]}),(0,a.jsx)(l.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold ".concat(t.summary.balance>=0?"text-green-600":"text-red-600"),children:(0,A.vv)(t.summary.balance)})})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(l.ZB,{className:"text-sm font-medium",children:"إجمالي الأنشطة"}),(0,a.jsx)(v.A,{className:"h-4 w-4 text-purple-600"})]}),(0,a.jsx)(l.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:t.summary.totalActivities})})]})]}),(0,a.jsxs)(c.tU,{defaultValue:"overview",className:"space-y-6",children:[(0,a.jsxs)(c.j7,{className:"grid w-full grid-cols-5",children:[(0,a.jsx)(c.Xi,{value:"overview",children:"نظرة عامة"}),(0,a.jsx)(c.Xi,{value:"contributors",children:"أكثر المساهمين"}),(0,a.jsx)(c.Xi,{value:"transactions",children:"المعاملات"}),(0,a.jsx)(c.Xi,{value:"analytics",children:"التحليلات"}),(0,a.jsx)(c.Xi,{value:"insights",children:"رؤى ذكية"})]}),(0,a.jsx)(c.av,{value:"overview",className:"space-y-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-2",children:[(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsxs)(l.ZB,{className:"flex items-center",children:[(0,a.jsx)(f,{className:"w-5 h-5 ml-2"}),"أكثر الأعضاء مساهمة"]})}),(0,a.jsx)(l.Wu,{children:0===t.topContributors.length?(0,a.jsx)("p",{className:"text-gray-500 text-center py-4",children:"لا توجد مساهمات"}):(0,a.jsx)("div",{className:"space-y-3",children:t.topContributors.slice(0,10).map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white ".concat(0===t?"bg-yellow-500":1===t?"bg-gray-400":2===t?"bg-orange-600":"bg-gray-300"),children:t+1}),(0,a.jsxs)("div",{className:"mr-3",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[e.incomes.length," إيراد"]})]})]}),(0,a.jsx)("div",{className:"text-sm font-medium text-green-600",children:(0,A.vv)(e.totalContributions)})]},e.id))})})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsxs)(l.ZB,{className:"flex items-center",children:[(0,a.jsx)(x.A,{className:"w-5 h-5 ml-2"}),"آخر المعاملات"]})}),(0,a.jsx)(l.Wu,{children:0===t.recentTransactions.length?(0,a.jsx)("p",{className:"text-gray-500 text-center py-4",children:"لا توجد معاملات"}):(0,a.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:t.recentTransactions.slice(0,15).map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full ".concat("income"===e.type?"bg-green-500":"bg-red-500")}),(0,a.jsxs)("div",{className:"mr-3",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.description}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[e.memberName||"غير محدد"," • ",(0,A.Yq)(e.date)]})]})]}),(0,a.jsxs)("div",{className:"text-sm font-medium ".concat("income"===e.type?"text-green-600":"text-red-600"),children:["income"===e.type?"+":"-",(0,A.vv)(e.amount)]})]},"".concat(e.type,"-").concat(e.id,"-").concat(t)))})})]})]})}),(0,a.jsx)(c.av,{value:"contributors",className:"space-y-6",children:(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsx)(l.ZB,{children:"تفاصيل المساهمين"})}),(0,a.jsx)(l.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:t.topContributors.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)(o.E,{variant:t<3?"success":"secondary",className:"ml-3",children:["#",t+1]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.incomes.length," مساهمة"]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"font-medium text-green-600",children:(0,A.vv)(e.totalContributions)}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["متوسط: ",(0,A.vv)(e.totalContributions/e.incomes.length)]})]})]},e.id))})})]})}),(0,a.jsx)(c.av,{value:"transactions",className:"space-y-6",children:(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsx)(l.ZB,{children:"جميع المعاملات الحديثة"})}),(0,a.jsx)(l.Wu,{children:(0,a.jsx)("div",{className:"space-y-3",children:t.recentTransactions.map((e,t)=>(0,a.jsx)("div",{className:"p-4 rounded-lg border-l-4 ".concat("income"===e.type?"border-green-500 bg-green-50":"border-red-500 bg-red-50"),children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.description}),(0,a.jsxs)("div",{className:"text-sm text-gray-600 mt-1",children:[(0,a.jsx)(j.A,{className:"w-3 h-3 inline ml-1"}),(0,A.Yq)(e.date)]}),e.memberName&&(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,a.jsx)(g.A,{className:"w-3 h-3 inline ml-1"}),e.memberName]})]}),(0,a.jsxs)("div",{className:"text-lg font-bold ".concat("income"===e.type?"text-green-600":"text-red-600"),children:["income"===e.type?"+":"-",(0,A.vv)(e.amount)]})]})},"".concat(e.type,"-").concat(e.id,"-").concat(t)))})})]})}),(0,a.jsxs)(c.av,{value:"analytics",className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-2",children:[(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsxs)(l.ZB,{className:"flex items-center",children:[(0,a.jsx)(N,{className:"w-5 h-5 ml-2"}),"مؤشرات الأداء الرئيسية"]})}),(0,a.jsx)(l.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2",children:[(0,a.jsxs)("div",{className:"text-center p-4 border border-gray-200 rounded-lg",children:[(0,a.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:t.summary.totalIncomes>0?(0,A.vv)(t.summary.totalIncomes/t.summary.totalMembers):(0,A.vv)(0)}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"متوسط المساهمة لكل عضو"})]}),(0,a.jsxs)("div",{className:"text-center p-4 border border-gray-200 rounded-lg",children:[(0,a.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:t.summary.totalExpenses>0&&t.summary.totalActivities>0?(0,A.vv)(t.summary.totalExpenses/t.summary.totalActivities):(0,A.vv)(0)}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"متوسط تكلفة النشاط"})]}),(0,a.jsxs)("div",{className:"text-center p-4 border border-gray-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"text-lg font-semibold text-gray-900",children:[t.summary.totalMembers>0?Math.round(t.summary.activeMembers/t.summary.totalMembers*100):0,"%"]}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"نسبة الأعضاء النشطين"})]}),(0,a.jsxs)("div",{className:"text-center p-4 border border-gray-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"text-lg font-semibold text-gray-900",children:[t.summary.totalIncomes>0?Math.round(t.summary.totalExpenses/t.summary.totalIncomes*100):0,"%"]}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"نسبة المصروفات للإيرادات"})]})]})})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsxs)(l.ZB,{className:"flex items-center",children:[(0,a.jsx)(w.A,{className:"w-5 h-5 ml-2"}),"الاتجاهات الشهرية"]})}),(0,a.jsx)(l.Wu,{children:t.monthlyData.length>0?(0,a.jsx)("div",{className:"space-y-3",children:t.monthlyData.slice(-6).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:new Date(e.month+"-01").toLocaleDateString("ar-JO",{year:"numeric",month:"long"})}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["إيرادات: ",(0,A.vv)(e.incomes)," | مصروفات: ",(0,A.vv)(e.expenses)]})]}),(0,a.jsx)("div",{className:"font-medium ".concat(e.balance>=0?"text-green-600":"text-red-600"),children:(0,A.vv)(e.balance)})]},e.month))}):(0,a.jsx)("p",{className:"text-gray-500 text-center py-4",children:"لا توجد بيانات شهرية"})})]})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsx)(l.ZB,{children:"تحليل الأداء المالي"})}),(0,a.jsx)(l.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-3",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-3xl font-bold ".concat(t.summary.balance>=0?"text-green-600":"text-red-600"),children:t.summary.balance>=0?"✓":"⚠"}),(0,a.jsx)("div",{className:"text-sm text-gray-600 mt-2",children:t.summary.balance>=0?"الوضع المالي مستقر":"يحتاج مراجعة مالية"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-3xl font-bold ".concat(t.summary.activeMembers/t.summary.totalMembers>=.7?"text-green-600":"text-yellow-600"),children:t.summary.activeMembers/t.summary.totalMembers>=.7?"\uD83D\uDCC8":"\uD83D\uDCCA"}),(0,a.jsx)("div",{className:"text-sm text-gray-600 mt-2",children:t.summary.activeMembers/t.summary.totalMembers>=.7?"مشاركة عالية للأعضاء":"يمكن تحسين مشاركة الأعضاء"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-3xl font-bold ".concat(t.summary.totalActivities>=5?"text-green-600":"text-blue-600"),children:t.summary.totalActivities>=5?"\uD83C\uDFAF":"\uD83D\uDCC5"}),(0,a.jsx)("div",{className:"text-sm text-gray-600 mt-2",children:t.summary.totalActivities>=5?"نشاط جيد للديوان":"يمكن زيادة الأنشطة"})]})]})})]})]}),(0,a.jsxs)(c.av,{value:"insights",className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-2",children:[(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsxs)(l.ZB,{className:"flex items-center",children:[(0,a.jsx)(N,{className:"w-5 h-5 ml-2"}),"توصيات ذكية"]})}),(0,a.jsx)(l.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 rounded-lg border-l-4 ".concat(t.summary.balance>=0?"border-green-500 bg-green-50":"border-red-500 bg-red-50"),children:[(0,a.jsx)("div",{className:"font-medium mb-2",children:t.summary.balance>=0?"✅ الوضع المالي مستقر":"⚠️ تحذير مالي"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:t.summary.balance>=0?"الديوان يحقق فائضاً مالياً جيداً. يُنصح بالاستثمار في أنشطة إضافية.":"الديوان يواجه عجزاً مالياً. يُنصح بمراجعة المصروفات وزيادة الإيرادات."})]}),(0,a.jsxs)("div",{className:"p-4 rounded-lg border-l-4 ".concat(t.summary.activeMembers/t.summary.totalMembers>=.7?"border-green-500 bg-green-50":"border-yellow-500 bg-yellow-50"),children:[(0,a.jsx)("div",{className:"font-medium mb-2",children:t.summary.activeMembers/t.summary.totalMembers>=.7?"\uD83D\uDC65 مشاركة ممتازة للأعضاء":"\uD83D\uDCE2 تحسين مشاركة الأعضاء"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:t.summary.activeMembers/t.summary.totalMembers>=.7?"نسبة عالية من الأعضاء النشطين. استمر في تقديم الأنشطة المفيدة.":"يمكن تحسين مشاركة الأعضاء من خلال المزيد من الأنشطة التفاعلية."})]}),(0,a.jsxs)("div",{className:"p-4 rounded-lg border-l-4 ".concat(t.summary.totalActivities>=5?"border-green-500 bg-green-50":"border-blue-500 bg-blue-50"),children:[(0,a.jsx)("div",{className:"font-medium mb-2",children:t.summary.totalActivities>=5?"\uD83C\uDFAF نشاط ممتاز":"\uD83D\uDCC5 زيادة الأنشطة"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:t.summary.totalActivities>=5?"مستوى جيد من الأنشطة. حافظ على هذا المعدل.":"يُنصح بزيادة عدد الأنشطة لتعزيز التفاعل بين الأعضاء."})]})]})})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsxs)(l.ZB,{className:"flex items-center",children:[(0,a.jsx)(m.A,{className:"w-5 h-5 ml-2"}),"إحصائيات متقدمة"]})}),(0,a.jsx)(l.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"معدل المساهمة الشهرية"}),(0,a.jsx)("span",{className:"font-bold text-blue-600",children:t.summary.totalIncomes>0&&t.monthlyData.length>0?(0,A.vv)(t.summary.totalIncomes/Math.max(t.monthlyData.length,1)):(0,A.vv)(0)})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"معدل الإنفاق الشهري"}),(0,a.jsx)("span",{className:"font-bold text-red-600",children:t.summary.totalExpenses>0&&t.monthlyData.length>0?(0,A.vv)(t.summary.totalExpenses/Math.max(t.monthlyData.length,1)):(0,A.vv)(0)})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"أعلى مساهمة فردية"}),(0,a.jsx)("span",{className:"font-bold text-green-600",children:t.topContributors.length>0?(0,A.vv)(t.topContributors[0].totalContributions):(0,A.vv)(0)})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"نسبة الادخار"}),(0,a.jsx)("span",{className:"font-bold ".concat(t.summary.totalIncomes>0?t.summary.balance/t.summary.totalIncomes>=.2?"text-green-600":"text-yellow-600":"text-gray-600"),children:t.summary.totalIncomes>0?"".concat(Math.round(t.summary.balance/t.summary.totalIncomes*100),"%"):"0%"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"متوسط المعاملات اليومية"}),(0,a.jsxs)("span",{className:"font-bold text-purple-600",children:[t.recentTransactions.length>0?Math.round(t.recentTransactions.length/30):0," معاملة/يوم"]})]})]})})]})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsx)(l.ZB,{children:"مقارنة الأداء"})}),(0,a.jsx)(l.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-3",children:[(0,a.jsxs)("div",{className:"text-center p-6 border border-gray-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-blue-600 mb-2",children:[Math.round(t.summary.activeMembers/t.summary.totalMembers*100),"%"]}),(0,a.jsx)("div",{className:"text-sm text-gray-600 mb-2",children:"معدل المشاركة"}),(0,a.jsx)("div",{className:"text-xs px-2 py-1 rounded-full ".concat(t.summary.activeMembers/t.summary.totalMembers>=.8?"bg-green-100 text-green-800":t.summary.activeMembers/t.summary.totalMembers>=.6?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:t.summary.activeMembers/t.summary.totalMembers>=.8?"ممتاز":t.summary.activeMembers/t.summary.totalMembers>=.6?"جيد":"يحتاج تحسين"})]}),(0,a.jsxs)("div",{className:"text-center p-6 border border-gray-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-green-600 mb-2",children:[t.summary.totalIncomes>0?Math.round(t.summary.balance/t.summary.totalIncomes*100):0,"%"]}),(0,a.jsx)("div",{className:"text-sm text-gray-600 mb-2",children:"معدل الادخار"}),(0,a.jsx)("div",{className:"text-xs px-2 py-1 rounded-full ".concat(t.summary.totalIncomes>0&&t.summary.balance/t.summary.totalIncomes>=.3?"bg-green-100 text-green-800":t.summary.totalIncomes>0&&t.summary.balance/t.summary.totalIncomes>=.1?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:t.summary.totalIncomes>0&&t.summary.balance/t.summary.totalIncomes>=.3?"ممتاز":t.summary.totalIncomes>0&&t.summary.balance/t.summary.totalIncomes>=.1?"جيد":"يحتاج تحسين"})]}),(0,a.jsxs)("div",{className:"text-center p-6 border border-gray-200 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600 mb-2",children:t.summary.totalActivities}),(0,a.jsx)("div",{className:"text-sm text-gray-600 mb-2",children:"عدد الأنشطة"}),(0,a.jsx)("div",{className:"text-xs px-2 py-1 rounded-full ".concat(t.summary.totalActivities>=10?"bg-green-100 text-green-800":t.summary.totalActivities>=5?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:t.summary.totalActivities>=10?"نشط جداً":t.summary.totalActivities>=5?"نشط":"يحتاج تفعيل"})]})]})})]})]})]})]}):(0,a.jsxs)("div",{className:"space-y-6 arabic-text",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",style:{fontFamily:"Cairo, Almarai, sans-serif"},children:"التقارير والإحصائيات"}),(0,a.jsx)("p",{className:"text-gray-600",style:{fontFamily:"Cairo, Almarai, sans-serif"},children:"تقارير شاملة عن أنشطة الديوان المالية والإدارية"})]}),(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("div",{className:"text-gray-500",style:{fontFamily:"Cairo, Almarai, sans-serif"},children:"لا توجد بيانات متاحة"})})]})}},59434:(e,t,s)=>{"use strict";s.d(t,{OR:()=>x,Tk:()=>c,WK:()=>m,Yq:()=>l,cn:()=>r,gr:()=>o,uF:()=>d,vv:()=>i});var a=s(52596),n=s(39688);function r(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,n.QP)((0,a.$)(t))}function i(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"JOD",minimumFractionDigits:2}).format(e).replace("JOD","د.أ")}function l(e){let t="string"==typeof e?new Date(e):e;return isNaN(t.getTime())?"تاريخ غير صحيح":new Intl.DateTimeFormat("en-GB",{year:"numeric",month:"2-digit",day:"2-digit"}).format(t)}function o(e){return({ADMIN:"مدير",DATA_ENTRY:"مدخل بيانات",VIEWER:"مطلع"})[e]||e}function c(e){return({SUBSCRIPTION:"اشتراكات",DONATION:"تبرعات",EVENT:"فعاليات",OTHER:"أخرى"})[e]||e}function d(e){return({MEETINGS:"اجتماعات",EVENTS:"مناسبات",MAINTENANCE:"إصلاحات",SOCIAL:"اجتماعية",GENERAL:"عامة"})[e]||e}function m(e){return({ACTIVE:"نشط",LATE:"متأخر",INACTIVE:"غير ملتزم",SUSPENDED:"موقوف مؤقتاً",ARCHIVED:"مؤرشف"})[e]||e}function x(e){return({ACTIVE:"bg-green-100 text-green-800",LATE:"bg-yellow-100 text-yellow-800",INACTIVE:"bg-red-100 text-red-800",SUSPENDED:"bg-orange-100 text-orange-800",ARCHIVED:"bg-gray-100 text-gray-800"})[e]||"bg-gray-100 text-gray-800"}},66695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>i,aR:()=>l});var a=s(95155),n=s(12115),r=s(59434);let i=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.cn)("rounded-xl border border-secondary-200 bg-white text-secondary-700 shadow-lg hover:shadow-xl transition-all duration-200 hover:-translate-y-1 backdrop-blur-sm",s),...n})});i.displayName="Card";let l=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.cn)("flex flex-col space-y-1.5 p-6 bg-gradient-to-r from-secondary-50 to-secondary-100 rounded-t-xl border-b border-secondary-200",s),...n})});l.displayName="CardHeader";let o=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("h3",{ref:t,className:(0,r.cn)("text-xl font-bold leading-none tracking-tight text-secondary-800",s),...n})});o.displayName="CardTitle";let c=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("p",{ref:t,className:(0,r.cn)("text-sm text-secondary-600 font-medium",s),...n})});c.displayName="CardDescription";let d=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.cn)("p-6 pt-4",s),...n})});d.displayName="CardContent",n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.cn)("flex items-center p-6 pt-0 bg-gradient-to-r from-secondary-50 to-secondary-100 rounded-b-xl border-t border-secondary-200",s),...n})}).displayName="CardFooter"},68500:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},69074:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},72713:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},79397:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[1778,2108,3942,4324,8441,1684,7358],()=>t(49618)),_N_E=e.O()}]);