'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { DollarSign, User } from 'lucide-react'

const memberIncomeSchema = z.object({
  amount: z.number().positive('المبلغ يجب أن يكون أكبر من صفر'),
  date: z.string().min(1, 'التاريخ مطلوب'),
  source: z.string().min(1, 'مصدر الإيراد مطلوب'),
  type: z.enum(['SUBSCRIPTION', 'DONATION', 'EVENT', 'OTHER']).default('SUBSCRIPTION'),
  description: z.string().optional(),
  notes: z.string().optional(),
})

type MemberIncomeInput = z.infer<typeof memberIncomeSchema>

interface Member {
  id: string
  name: string
}

interface MemberIncomeDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  member: Member | null
  onSuccess?: () => void
}

export default function MemberIncomeDialog({
  open,
  onOpenChange,
  member,
  onSuccess,
}: MemberIncomeDialogProps) {
  const [loading, setLoading] = useState(false)

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm<MemberIncomeInput>({
    resolver: zodResolver(memberIncomeSchema),
    defaultValues: {
      amount: 0,
      date: new Date().toISOString().split('T')[0], // التاريخ الحالي
      source: '',
      type: 'SUBSCRIPTION',
      description: '',
      notes: '',
    },
  })

  const selectedType = watch('type')

  useEffect(() => {
    if (open && member) {
      reset({
        amount: 0,
        date: new Date().toISOString().split('T')[0],
        source: '',
        type: 'SUBSCRIPTION',
        description: '',
        notes: '',
      })
    }
  }, [open, member, reset])

  const onSubmit = async (data: MemberIncomeInput) => {
    if (!member) return

    try {
      setLoading(true)

      // تحويل البيانات
      const submitData = {
        ...data,
        amount: Number(data.amount),
        date: new Date(data.date),
        memberId: member.id,
        description: data.description?.trim() || null,
        notes: data.notes?.trim() || null,
      }

      const response = await fetch('/api/incomes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'حدث خطأ')
      }

      alert(`تم إضافة إيراد للعضو ${member.name} بنجاح`)
      onOpenChange(false)
      onSuccess?.()
    } catch (error: any) {
      console.error('خطأ في إضافة الإيراد:', error)
      alert(error.message || 'حدث خطأ في إضافة الإيراد')
    } finally {
      setLoading(false)
    }
  }

  // const getIncomeTypeText = (type: string) => {
  //   switch (type) {
  //     case 'SUBSCRIPTION': return 'اشتراكات'
  //     case 'DONATION': return 'تبرعات'
  //     case 'EVENT': return 'فعاليات'
  //     case 'OTHER': return 'أخرى'
  //     default: return type
  //   }
  // }

  if (!member) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[50vw] max-h-[90vh] overflow-y-auto">
        <DialogHeader className="p-6 pb-4 border-b border-gray-100 bg-gradient-to-r from-diwan-50 to-blue-50">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-white rounded-xl shadow-sm border border-diwan-200">
              <DollarSign className="w-6 h-6 text-diwan-600" />
            </div>
            <div>
              <DialogTitle className="text-xl font-bold text-gray-900">
                إضافة إيراد للعضو
              </DialogTitle>
              <div className="flex items-center gap-2 mt-1">
                <User className="w-4 h-4 text-gray-500" />
                <p className="text-gray-600 font-medium">{member.name}</p>
              </div>
            </div>
          </div>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="amount">المبلغ (دينار أردني) *</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                min="0"
                {...register('amount', { valueAsNumber: true })}
                className={errors.amount ? 'border-red-500' : ''}
              />
              {errors.amount && (
                <p className="text-sm text-red-500">{errors.amount.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="date">التاريخ *</Label>
              <Input
                id="date"
                type="date"
                {...register('date')}
                className={errors.date ? 'border-red-500' : ''}
              />
              {errors.date && (
                <p className="text-sm text-red-500">{errors.date.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="source">مصدر الإيراد *</Label>
            <Input
              id="source"
              {...register('source')}
              placeholder="مثال: اشتراك شهري، تبرع، رسوم فعالية"
              className={errors.source ? 'border-red-500' : ''}
            />
            {errors.source && (
              <p className="text-sm text-red-500">{errors.source.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="type">نوع الإيراد</Label>
            <Select
              value={selectedType}
              onValueChange={(value) => setValue('type', value as any)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="SUBSCRIPTION">اشتراكات</SelectItem>
                <SelectItem value="DONATION">تبرعات</SelectItem>
                <SelectItem value="EVENT">فعاليات</SelectItem>
                <SelectItem value="OTHER">أخرى</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">الوصف (اختياري)</Label>
            <Input
              id="description"
              {...register('description')}
              placeholder="وصف إضافي للإيراد"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">ملاحظات (اختياري)</Label>
            <Textarea
              id="notes"
              {...register('notes')}
              placeholder="أي ملاحظات إضافية"
              rows={3}
            />
          </div>

          <div className="flex justify-end space-x-2 space-x-reverse pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              إلغاء
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-diwan-600 hover:bg-diwan-700"
            >
              {loading ? 'جاري الحفظ...' : 'حفظ الإيراد'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
