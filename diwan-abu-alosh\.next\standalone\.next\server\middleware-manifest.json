{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "8qx-mqGDeGNusk0mz45LN", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "WsUnalvfLC8q6thQbM7XpflHInutTa69tutWo9d2/Nc=", "__NEXT_PREVIEW_MODE_ID": "5bd43d2d0500003b34d09348f387e864", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a4a7b3d3c82be15aeaea49a2db2277757d99cfed846a6807339afc0ed54c7262", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9b9d69690b768c129da4d1d97404e8d756b184ab3acb0723ee38dd8b506440a9"}}}, "functions": {}, "sortedMiddleware": ["/"]}