(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6253],{63436:(e,s,t)=>{"use strict";t.d(s,{A:()=>u});var a=t(95155),r=t(12115),n=t(30285),l=t(62523),i=t(54165),o=t(47924),d=t(54416),c=t(71007),m=t(19420),x=t(4516),h=t(57434),p=t(26126),g=t(59434);function u(e){let{open:s,onOpenChange:t,onSelectMember:u,title:b="اختيار عضو",description:j="ابحث عن العضو المطلوب"}=e,[f,v]=(0,r.useState)(""),[N,y]=(0,r.useState)([]),[w,A]=(0,r.useState)(!1),[C,E]=(0,r.useState)(null);(0,r.useEffect)(()=>{s?S():(v(""),y([]),E(null))},[s,f]);let S=async()=>{A(!0);try{let e=await fetch("/api/members?search=".concat(encodeURIComponent(f),"&limit=20&includeIncomes=true"));if(e.ok){let s=await e.json();y(s.members||[])}}catch(e){console.error("خطأ في جلب الأعضاء:",e)}finally{A(!1)}},z=e=>{E(e)};return(0,a.jsx)(i.lG,{open:s,onOpenChange:t,children:(0,a.jsxs)(i.Cf,{className:"max-w-[50vw] max-h-[80vh] overflow-hidden flex flex-col",children:[(0,a.jsx)(i.c7,{className:"pb-4 border-b",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,a.jsx)(o.A,{className:"w-5 h-5 text-purple-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(i.L3,{className:"text-lg font-bold text-gray-900",children:b}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:j})]})]}),(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>t(!1),className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)(d.A,{className:"w-4 h-4"})})]})}),(0,a.jsxs)("div",{className:"flex-1 overflow-hidden flex flex-col",children:[(0,a.jsx)("div",{className:"p-4 border-b bg-gray-50",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(o.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)(l.p,{placeholder:"ابحث بالاسم أو الهاتف أو العنوان...",value:f,onChange:e=>v(e.target.value),className:"pr-10",autoFocus:!0})]})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto p-4",children:w?(0,a.jsx)("div",{className:"flex justify-center items-center h-32",children:(0,a.jsx)("div",{className:"text-gray-500",children:"جاري البحث..."})}):0===N.length?(0,a.jsxs)("div",{className:"flex flex-col justify-center items-center h-32 text-gray-500",children:[(0,a.jsx)(c.A,{className:"w-12 h-12 mb-2 text-gray-300"}),(0,a.jsx)("p",{children:f?"لا توجد نتائج للبحث":"ابدأ بكتابة اسم العضو للبحث"})]}):(0,a.jsx)("div",{className:"space-y-2",children:N.map(e=>(0,a.jsx)("div",{onClick:()=>z(e),className:"p-3 border rounded-lg cursor-pointer transition-all hover:shadow-md ".concat((null==C?void 0:C.id)===e.id?"border-purple-500 bg-purple-50":"border-gray-200 hover:border-gray-300"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900",children:e.name}),(0,a.jsx)(p.E,{className:(0,g.OR)(e.status),children:(0,g.WK)(e.status)})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[e.phone&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"w-3 h-3"}),e.phone]}),e.address&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(x.A,{className:"w-3 h-3"}),e.address]})]})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e._count.incomes," مساهمة"]})]})},e.id))})}),(0,a.jsxs)("div",{className:"p-4 border-t bg-gray-50 flex justify-end gap-2",children:[(0,a.jsx)(n.$,{variant:"outline",onClick:()=>t(!1),children:"إلغاء"}),(0,a.jsxs)(n.$,{onClick:()=>{C&&(u(C.id),t(!1))},disabled:!C,className:"bg-purple-600 hover:bg-purple-700 text-white",children:[(0,a.jsx)(h.A,{className:"w-4 h-4 ml-2"}),"عرض كشف الحساب"]})]})]})]})})}},71450:(e,s,t)=>{Promise.resolve().then(t.bind(t,96529))},85057:(e,s,t)=>{"use strict";t.d(s,{J:()=>l});var a=t(95155),r=t(12115),n=t(59434);let l=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("label",{ref:s,className:(0,n.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",t),...r})});l.displayName="Label"},85127:(e,s,t)=>{"use strict";t.d(s,{A0:()=>i,BF:()=>o,Hj:()=>d,XI:()=>l,nA:()=>m,nd:()=>c});var a=t(95155),r=t(12115),n=t(59434);let l=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:s,className:(0,n.cn)("w-full caption-bottom text-sm",t),...r})})});l.displayName="Table";let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("thead",{ref:s,className:(0,n.cn)("[&_tr]:border-b",t),...r})});i.displayName="TableHeader";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("tbody",{ref:s,className:(0,n.cn)("[&_tr:last-child]:border-0",t),...r})});o.displayName="TableBody",r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("tfoot",{ref:s,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",t),...r})}).displayName="TableFooter";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("tr",{ref:s,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",t),...r})});d.displayName="TableRow";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("th",{ref:s,className:(0,n.cn)("h-12 px-4 text-right align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",t),...r})});c.displayName="TableHead";let m=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("td",{ref:s,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",t),...r})});m.displayName="TableCell"},88539:(e,s,t)=>{"use strict";t.d(s,{T:()=>l});var a=t(95155),r=t(12115),n=t(59434);let l=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...r})});l.displayName="Textarea"},96529:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>eg});var a=t(95155),r=t(12115),n=t(12108),l=t(30285),i=t(62523),o=t(66695),d=t(85127),c=t(17580),m=t(91788),x=t(23837),h=t(84616),p=t(55670),g=t(9446),u=t(47924),b=t(66932),j=t(57434),f=t(4516),v=t(19420),N=t(92657),y=t(55868),w=t(13717),A=t(69803),C=t(62525),E=t(62177),S=t(48778),z=t(71153);z.z.object({name:z.z.string().min(2,"الاسم يجب أن يكون على الأقل حرفين"),email:z.z.string().email("البريد الإلكتروني غير صحيح"),password:z.z.string().min(6,"كلمة المرور يجب أن تكون على الأقل 6 أحرف"),role:z.z.enum(["ADMIN","DATA_ENTRY","VIEWER","MEMBER_VIEWER","GALLERY_VIEWER","MEMBER"]).default("VIEWER")});let T=z.z.object({name:z.z.string().min(2,"الاسم يجب أن يكون على الأقل حرفين"),phone:z.z.string().optional().or(z.z.literal("")).or(z.z.literal(null)),email:z.z.union([z.z.string().email("البريد الإلكتروني غير صحيح"),z.z.literal(""),z.z.literal(null),z.z.undefined()]).optional(),address:z.z.string().optional().or(z.z.literal("")).or(z.z.literal(null)),notes:z.z.string().optional().or(z.z.literal("")).or(z.z.literal(null)),photo:z.z.string().optional().or(z.z.literal("")).or(z.z.literal(null)),status:z.z.enum(["ACTIVE","LATE","INACTIVE","SUSPENDED","ARCHIVED"]).default("ACTIVE")});z.z.object({amount:z.z.number().positive("المبلغ يجب أن يكون أكبر من صفر"),date:z.z.date(),source:z.z.string().min(1,"مصدر الإيراد مطلوب"),type:z.z.enum(["SUBSCRIPTION","DONATION","EVENT","OTHER"]).default("SUBSCRIPTION"),description:z.z.string().optional().nullable(),notes:z.z.string().optional().nullable(),memberId:z.z.string().optional().nullable()}),z.z.object({amount:z.z.number().positive("المبلغ يجب أن يكون أكبر من صفر"),date:z.z.date(),description:z.z.string().min(1,"وصف المصروف مطلوب"),category:z.z.enum(["MEETINGS","EVENTS","MAINTENANCE","SOCIAL","GENERAL"]).default("GENERAL"),recipient:z.z.string().optional().nullable(),notes:z.z.string().optional().nullable()}),z.z.object({title:z.z.string().min(1,"عنوان النشاط مطلوب"),description:z.z.string().optional(),startDate:z.z.date(),endDate:z.z.date().optional(),location:z.z.string().optional(),organizers:z.z.string().optional(),participantIds:z.z.array(z.z.string()).optional()}),z.z.object({email:z.z.string().email("البريد الإلكتروني غير صحيح"),password:z.z.string().min(1,"كلمة المرور مطلوبة")});var k=t(54165),D=t(85057),I=t(88539),R=t(26126),F=t(40646),O=t(14186),W=t(85339),P=t(54861),V=t(39022),B=t(71007),L=t(84355),Z=t(54416),J=t(51154),$=t(27213),U=t(28883),H=t(4229);function M(e){let{open:s,onOpenChange:t,member:n,onSuccess:d}=e,[c,m]=(0,r.useState)(!1),[x,h]=(0,r.useState)(!1),[g,u]=(0,r.useState)(null),b=!!n,{register:N,handleSubmit:y,reset:w,setValue:A,watch:C,formState:{errors:z}}=(0,E.mN)({resolver:(0,S.u)(T),defaultValues:{name:"",phone:"",email:"",address:"",photo:"",notes:"",status:"ACTIVE"}}),M=C("status");(0,r.useEffect)(()=>{n?(A("name",n.name),A("phone",n.phone||""),A("email",n.email||""),A("address",n.address||""),A("photo",n.photo||""),A("notes",n.notes||""),A("status",n.status)):w({name:"",phone:"",email:"",address:"",photo:"",notes:"",status:"ACTIVE"})},[n,A,w]);let G=async e=>{if(!e)return null;if(!["image/jpeg","image/jpg","image/png","image/webp"].includes(e.type))return u("نوع الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG, WebP)"),null;if(e.size>5242880)return u("حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت"),null;u(null),h(!0);try{let s=new FormData;s.append("file",e);let t=await fetch("/api/upload",{method:"POST",body:s});if(!t.ok){let e=await t.json();throw Error(e.error||"فشل في رفع الصورة")}let a=await t.json();return A("photo",a.filePath),a.filePath}catch(e){return console.error("خطأ في رفع الصورة:",e),u(e.message||"حدث خطأ في رفع الصورة"),null}finally{h(!1)}},_=async()=>{let e=C("photo");if(e)try{await fetch("/api/upload?path=".concat(encodeURIComponent(e)),{method:"DELETE"})}catch(e){console.error("خطأ في حذف الصورة:",e)}A("photo",""),u(null)},Y=async e=>{try{var s,a,r,l;m(!0);let i={...e,phone:(null==(s=e.phone)?void 0:s.trim())||null,email:(null==(a=e.email)?void 0:a.trim())||null,address:(null==(r=e.address)?void 0:r.trim())||null,notes:(null==(l=e.notes)?void 0:l.trim())||null},o=b?"/api/members/".concat(n.id):"/api/members",c=await fetch(o,{method:b?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)});if(!c.ok){let e=await c.json();throw Error(e.error||"حدث خطأ")}alert(b?"تم تحديث بيانات العضو بنجاح":"تم إضافة العضو الجديد بنجاح"),d(),t(!1),w()}catch(e){console.error("خطأ في حفظ العضو:",e),alert(e.message||"حدث خطأ في حفظ العضو")}finally{m(!1)}};return(0,a.jsx)(k.lG,{open:s,onOpenChange:t,children:(0,a.jsxs)(k.Cf,{className:"w-[50vw] h-[85vh] max-w-none max-h-none overflow-y-auto fixed top-[1vh] left-1/2 -translate-x-1/2 translate-y-0",children:[(0,a.jsx)(k.c7,{className:"p-6 pb-4 border-b border-gray-100 bg-gradient-to-r from-diwan-50 to-blue-50",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"p-3 bg-white rounded-xl shadow-sm border border-diwan-200",children:(0,a.jsx)(B.A,{className:"w-6 h-6 text-diwan-600"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)(k.L3,{className:"text-xl font-bold text-gray-900 mb-1",children:b?"تعديل بيانات العضو":"إضافة عضو جديد"}),(0,a.jsx)(k.rr,{className:"text-gray-600",children:b?"قم بتعديل بيانات العضو في النموذج أدناه":"أدخل بيانات العضو الجديد في النموذج أدناه"}),!b&&(0,a.jsx)("div",{className:"mt-2 text-xs text-diwan-600 bg-diwan-50 px-2 py-1 rounded-md inline-block",children:"\uD83D\uDCA1 الحقول المطلوبة مميزة بعلامة *"})]})]})}),(0,a.jsxs)("form",{onSubmit:y(Y),className:"p-8 pt-0 space-y-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsx)(o.Zp,{className:"border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200",children:(0,a.jsxs)(o.Wu,{className:"p-5",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2 pb-2 border-b border-gray-100",children:[(0,a.jsx)("div",{className:"p-1 bg-indigo-100 rounded-lg",children:(0,a.jsx)(L.A,{className:"w-4 h-4 text-indigo-600"})}),"صورة العضو",(0,a.jsx)("span",{className:"text-xs text-gray-500 font-normal mr-auto",children:"اختياري"})]}),(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsxs)("div",{className:"space-y-3 w-full",children:[(0,a.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{var s;let t=null==(s=e.target.files)?void 0:s[0];t&&G(t)},className:"hidden",id:"photo-upload",disabled:c||x}),C("photo")?(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsxs)("div",{className:"relative w-32 h-32 mx-auto rounded-xl overflow-hidden border-2 border-gray-200 shadow-lg",children:[(0,a.jsx)("img",{src:C("photo")||void 0,alt:"صورة العضو",className:"w-full h-full object-cover"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center",children:(0,a.jsx)(l.$,{type:"button",variant:"destructive",size:"sm",onClick:_,disabled:c||x,className:"opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:(0,a.jsx)(Z.A,{className:"w-4 h-4"})})})]}),(0,a.jsx)("div",{className:"text-center mt-2",children:(0,a.jsxs)(l.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{var e;return null==(e=document.getElementById("photo-upload"))?void 0:e.click()},disabled:c||x,className:"text-xs",children:[(0,a.jsx)(L.A,{className:"w-3 h-3 ml-1"}),"تغيير الصورة"]})})]}):(0,a.jsx)("div",{onClick:()=>{var e;return null==(e=document.getElementById("photo-upload"))?void 0:e.click()},className:"\n                          relative w-full h-32 border-2 border-dashed rounded-xl cursor-pointer\n                          transition-all duration-200 flex flex-col items-center justify-center\n                          border-gray-300 hover:border-diwan-400 hover:bg-gray-50\n                          ".concat(c||x?"opacity-50 cursor-not-allowed":"","\n                        "),children:x?(0,a.jsxs)("div",{className:"flex flex-col items-center gap-2 text-diwan-600",children:[(0,a.jsx)(J.A,{className:"w-6 h-6 animate-spin"}),(0,a.jsx)("span",{className:"text-xs font-medium",children:"جاري الرفع..."})]}):(0,a.jsxs)("div",{className:"flex flex-col items-center gap-2 text-gray-500",children:[(0,a.jsx)("div",{className:"p-2 bg-gray-100 rounded-full",children:(0,a.jsx)($.A,{className:"w-6 h-6"})}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-xs font-medium text-gray-700",children:"اضغط لاختيار صورة"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"JPG, PNG, WebP"})]})]})}),g&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-2",children:(0,a.jsxs)("div",{className:"flex items-center gap-2 text-red-600",children:[(0,a.jsx)(W.A,{className:"w-3 h-3 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-xs",children:g})]})}),C("photo")&&!g&&(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-2",children:(0,a.jsxs)("div",{className:"flex items-center gap-2 text-green-600",children:[(0,a.jsx)(F.A,{className:"w-3 h-3 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-xs",children:"تم رفع الصورة بنجاح"})]})})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-3 text-center",children:"صورة شخصية للعضو"})]})]})}),(0,a.jsx)(o.Zp,{className:"lg:col-span-2 border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200",children:(0,a.jsxs)(o.Wu,{className:"p-5",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2 pb-2 border-b border-gray-100",children:[(0,a.jsx)("div",{className:"p-1 bg-diwan-100 rounded-lg",children:(0,a.jsx)(B.A,{className:"w-4 h-4 text-diwan-600"})}),"المعلومات الأساسية",(0,a.jsx)("span",{className:"text-xs text-gray-500 font-normal mr-auto",children:"مطلوب"})]}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(D.J,{htmlFor:"name",className:"text-sm font-medium text-gray-700 flex items-center gap-2",children:[(0,a.jsx)(B.A,{className:"w-4 h-4"}),"الاسم الكامل *"]}),(0,a.jsx)(i.p,{id:"name",...N("name"),placeholder:"أدخل الاسم الكامل للعضو",className:"h-12 text-base transition-all duration-200 ".concat(z.name?"border-red-300 focus:border-red-500 focus:ring-red-500 bg-red-50":"border-gray-300 focus:border-diwan-500 focus:ring-diwan-500")}),z.name&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mt-1",children:(0,a.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-2",children:[(0,a.jsx)(W.A,{className:"w-4 h-4 flex-shrink-0"}),z.name.message]})})]})})]})})]}),(0,a.jsx)(o.Zp,{className:"border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200",children:(0,a.jsxs)(o.Wu,{className:"p-5",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2 pb-2 border-b border-gray-100",children:[(0,a.jsx)("div",{className:"p-1 bg-blue-100 rounded-lg",children:(0,a.jsx)(v.A,{className:"w-4 h-4 text-blue-600"})}),"معلومات الاتصال",(0,a.jsx)("span",{className:"text-xs text-gray-500 font-normal mr-auto",children:"اختياري"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(D.J,{htmlFor:"phone",className:"text-sm font-medium text-gray-700 flex items-center gap-2",children:[(0,a.jsx)(v.A,{className:"w-4 h-4"}),"رقم الهاتف"]}),(0,a.jsx)(i.p,{id:"phone",...N("phone"),placeholder:"07xxxxxxxx",dir:"ltr",className:"h-12 text-base border-gray-300 focus:border-diwan-500 focus:ring-diwan-500"}),z.phone&&(0,a.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,a.jsx)(W.A,{className:"w-4 h-4"}),z.phone.message]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(D.J,{htmlFor:"email",className:"text-sm font-medium text-gray-700 flex items-center gap-2",children:[(0,a.jsx)(U.A,{className:"w-4 h-4"}),"البريد الإلكتروني"]}),(0,a.jsx)(i.p,{id:"email",type:"email",...N("email"),placeholder:"<EMAIL>",dir:"ltr",className:"h-12 text-base border-gray-300 focus:border-diwan-500 focus:ring-diwan-500"}),z.email&&(0,a.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,a.jsx)(W.A,{className:"w-4 h-4"}),z.email.message]})]})]})]})}),(0,a.jsx)(o.Zp,{className:"border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200",children:(0,a.jsxs)(o.Wu,{className:"p-5",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2 pb-2 border-b border-gray-100",children:[(0,a.jsx)("div",{className:"p-1 bg-green-100 rounded-lg",children:(0,a.jsx)(f.A,{className:"w-4 h-4 text-green-600"})}),"العنوان والملاحظات",(0,a.jsx)("span",{className:"text-xs text-gray-500 font-normal mr-auto",children:"اختياري"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(D.J,{htmlFor:"address",className:"text-sm font-medium text-gray-700 flex items-center gap-2",children:[(0,a.jsx)(f.A,{className:"w-4 h-4"}),"العنوان"]}),(0,a.jsx)(i.p,{id:"address",...N("address"),placeholder:"أدخل عنوان السكن",className:"h-12 text-base border-gray-300 focus:border-diwan-500 focus:ring-diwan-500"}),z.address&&(0,a.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,a.jsx)(W.A,{className:"w-4 h-4"}),z.address.message]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(D.J,{htmlFor:"notes",className:"text-sm font-medium text-gray-700 flex items-center gap-2",children:[(0,a.jsx)(j.A,{className:"w-4 h-4"}),"ملاحظات إضافية"]}),(0,a.jsx)(I.T,{id:"notes",...N("notes"),placeholder:"أدخل أي ملاحظات أو معلومات إضافية عن العضو",rows:4,className:"text-base border-gray-300 focus:border-diwan-500 focus:ring-diwan-500 resize-none"}),z.notes&&(0,a.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,a.jsx)(W.A,{className:"w-4 h-4"}),z.notes.message]})]})]})]})}),(0,a.jsx)(o.Zp,{className:"border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200",children:(0,a.jsxs)(o.Wu,{className:"p-5",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2 pb-2 border-b border-gray-100",children:[(0,a.jsx)("div",{className:"p-1 bg-purple-100 rounded-lg",children:(0,a.jsx)(p.A,{className:"w-4 h-4 text-purple-600"})}),"حالة العضو",(0,a.jsx)("span",{className:"text-xs text-gray-500 font-normal mr-auto",children:"مطلوب"})]}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(D.J,{htmlFor:"status",className:"text-sm font-medium text-gray-700",children:"اختر حالة العضو"}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"الحالة الحالية:"}),(0,a.jsx)(R.E,{className:"".concat((e=>{switch(e){case"ACTIVE":return"bg-green-50 text-green-700 border-green-200";case"LATE":return"bg-yellow-50 text-yellow-700 border-yellow-200";case"INACTIVE":return"bg-orange-50 text-orange-700 border-orange-200";case"SUSPENDED":return"bg-red-50 text-red-700 border-red-200";case"ARCHIVED":return"bg-gray-50 text-gray-700 border-gray-200";default:return"bg-blue-50 text-blue-700 border-blue-200"}})(M)," border"),children:(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(e=>{switch(e){case"ACTIVE":return(0,a.jsx)(F.A,{className:"w-4 h-4 text-green-500"});case"LATE":return(0,a.jsx)(O.A,{className:"w-4 h-4 text-yellow-500"});case"INACTIVE":return(0,a.jsx)(W.A,{className:"w-4 h-4 text-orange-500"});case"SUSPENDED":return(0,a.jsx)(P.A,{className:"w-4 h-4 text-red-500"});case"ARCHIVED":return(0,a.jsx)(V.A,{className:"w-4 h-4 text-gray-500"});default:return(0,a.jsx)(p.A,{className:"w-4 h-4 text-blue-500"})}})(M),(0,a.jsxs)("span",{children:["ACTIVE"===M&&"نشط","LATE"===M&&"متأخر","INACTIVE"===M&&"غير ملتزم","SUSPENDED"===M&&"موقوف مؤقتاً","ARCHIVED"===M&&"مؤرشف"]})]})})]}),(0,a.jsxs)("select",{id:"status",...N("status"),className:"w-full h-12 px-4 py-3 text-base border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-diwan-500 focus:border-diwan-500 bg-white",children:[(0,a.jsx)("option",{value:"ACTIVE",children:"✅ نشط - عضو فعال ومنتظم"}),(0,a.jsx)("option",{value:"LATE",children:"⏰ متأخر - متأخر في الدفع"}),(0,a.jsx)("option",{value:"INACTIVE",children:"⚠️ غير ملتزم - غير منتظم في الحضور"}),(0,a.jsx)("option",{value:"SUSPENDED",children:"❌ موقوف مؤقتاً - موقوف لفترة محددة"}),(0,a.jsx)("option",{value:"ARCHIVED",children:"\uD83D\uDCC1 مؤرشف - عضو سابق"})]}),z.status&&(0,a.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,a.jsx)(W.A,{className:"w-4 h-4"}),z.status.message]})]})})]})}),(0,a.jsxs)("div",{className:"flex justify-between items-center gap-4 pt-8 border-t-2 border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 -mx-6 px-8 py-6 rounded-b-xl",children:[(0,a.jsx)("div",{className:"text-sm text-gray-600 font-medium",children:b?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"تعديل بيانات العضو"]}):(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"إضافة عضو جديد"]})}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsxs)(l.$,{type:"button",variant:"outline",onClick:()=>t(!1),disabled:c,className:"px-8 py-3 h-12 border-2 border-gray-300 text-gray-700 hover:bg-white hover:border-gray-400 hover:shadow-md transition-all duration-200 flex items-center gap-2 font-medium",children:[(0,a.jsx)(Z.A,{className:"w-4 h-4"}),"إلغاء"]}),(0,a.jsx)(l.$,{type:"submit",disabled:c,className:"px-8 py-3 h-12 font-bold text-white shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2 ".concat(b?"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 border-2 border-blue-500":"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 border-2 border-green-500"),children:c?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),(0,a.jsx)("span",{className:"text-base",children:"جاري الحفظ..."})]}):(0,a.jsx)("div",{className:"flex items-center gap-2",children:b?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(F.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"text-base",children:"تحديث البيانات"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(H.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"text-base",children:"حفظ العضو الجديد"})]})})})]})]})]})]})})}var G=t(59434),_=t(69074),Y=t(79397);function q(e){let{open:s,onOpenChange:t,member:n}=e,[l,i]=(0,r.useState)(null),[o,d]=(0,r.useState)(!1);(0,r.useEffect)(()=>{s&&n&&c()},[s,n]);let c=async()=>{if(n)try{var e,s;d(!0);let t=await fetch("/api/members/".concat(n.id));if(!t.ok)throw Error("فشل في جلب تفاصيل العضو");let a=await t.json(),r=(null==(e=a.incomes)?void 0:e.reduce((e,s)=>e+s.amount,0))||0;i({member:a,incomes:a.incomes||[],activities:(null==(s=a.activityParticipants)?void 0:s.map(e=>e.activity))||[],totalContributions:r})}catch(e){console.error("خطأ في جلب تفاصيل العضو:",e)}finally{d(!1)}};return n?(0,a.jsx)(k.lG,{open:s,onOpenChange:t,children:(0,a.jsxs)(k.Cf,{className:"max-w-[50vw] max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)(k.HM,{onOpenChange:t}),(0,a.jsx)(k.c7,{className:"p-6 pb-0",children:(0,a.jsxs)(k.L3,{className:"flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(B.A,{className:"w-5 h-5 text-diwan-600"}),(0,a.jsxs)("span",{children:["تفاصيل العضو: ",n.name]})]})}),(0,a.jsx)("div",{className:"p-6 pt-0 space-y-6",children:o?(0,a.jsx)("div",{className:"flex justify-center items-center h-32",children:(0,a.jsx)("div",{className:"text-gray-500",children:"جاري التحميل..."})}):l?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"المعلومات الأساسية"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(B.A,{className:"w-4 h-4 text-gray-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"الاسم:"}),(0,a.jsx)("span",{className:"font-medium",children:l.member.name})]}),l.member.phone&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(v.A,{className:"w-4 h-4 text-gray-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"الهاتف:"}),(0,a.jsx)("span",{className:"font-medium",children:l.member.phone})]}),l.member.address&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(f.A,{className:"w-4 h-4 text-gray-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"العنوان:"}),(0,a.jsx)("span",{className:"font-medium",children:l.member.address})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(_.A,{className:"w-4 h-4 text-gray-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"تاريخ الانضمام:"}),(0,a.jsx)("span",{className:"font-medium",children:(0,G.Yq)(l.member.createdAt)})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(Y.A,{className:"w-4 h-4 text-gray-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"الحالة:"}),(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat((0,G.OR)(l.member.status)),children:(0,G.WK)(l.member.status)})]})]}),l.member.notes&&(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse mb-2",children:[(0,a.jsx)(j.A,{className:"w-4 h-4 text-gray-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"ملاحظات:"})]}),(0,a.jsx)("p",{className:"text-sm bg-white p-3 rounded border",children:l.member.notes})]})]}),(0,a.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"الإحصائيات المالية"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:(0,G.vv)(l.totalContributions)}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"إجمالي المساهمات"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:l.incomes.length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"عدد الإيرادات"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:l.incomes.length>0?(0,G.vv)(l.totalContributions/l.incomes.length):(0,G.vv)(0)}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"متوسط المساهمة"})]})]})]}),l.incomes.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"آخر الإيرادات"}),(0,a.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:l.incomes.slice(0,10).map(e=>(0,a.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.source}),e.description&&(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.description}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:(0,G.Yq)(e.date)})]}),(0,a.jsx)("div",{className:"text-green-600 font-semibold",children:(0,G.vv)(e.amount)})]},e.id))})]}),l.activities.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"الأنشطة المشارك فيها"}),(0,a.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:l.activities.slice(0,10).map(e=>(0,a.jsxs)("div",{className:"flex justify-between items-center p-3 bg-blue-50 rounded",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.title}),e.location&&(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["\uD83D\uDCCD ",e.location]})]}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:(0,G.Yq)(e.startDate)})]},e.id))})]})]}):(0,a.jsx)("div",{className:"text-center text-gray-500",children:"لا توجد تفاصيل متاحة"})})]})}):null}var K=t(59409),X=t(81586),Q=t(33109),ee=t(72713);function es(e){let{open:s,onOpenChange:t,memberId:n}=e,[i,c]=(0,r.useState)(!1),[x,h]=(0,r.useState)(null),[p,g]=(0,r.useState)(new Date().getFullYear().toString());(0,r.useEffect)(()=>{s&&n&&u()},[s,n,p]);let u=async()=>{if(n){c(!0);try{let e=await fetch("/api/members/".concat(n,"/account-statement?year=").concat(p));if(!e.ok){let s=await e.text();throw console.error("خطأ في الاستجابة:",s),Error("فشل في جلب كشف الحساب")}let s=await e.json();h(s)}catch(e){console.error("خطأ في جلب كشف الحساب:",e),alert("حدث خطأ في جلب كشف الحساب: "+(null==e?void 0:e.message))}finally{c(!1)}}},b=new Date().getFullYear(),f=Array.from({length:5},(e,s)=>b-s);return(0,r.useEffect)(()=>{x&&0===x.summary.transactionCount&&p===b.toString()&&g((b-1).toString())},[x,p,b]),(0,a.jsx)(k.lG,{open:s,onOpenChange:t,children:(0,a.jsxs)(k.Cf,{className:"max-w-[50vw] max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)(k.c7,{className:"p-6 pb-4 border-b border-gray-100 bg-gradient-to-r from-diwan-50 to-blue-50",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"p-3 bg-white rounded-xl shadow-sm border border-diwan-200",children:(0,a.jsx)(j.A,{className:"w-6 h-6 text-diwan-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(k.L3,{className:"text-xl font-bold text-gray-900",children:"كشف حساب العضو"}),x&&(0,a.jsxs)("p",{className:"text-gray-600 mt-1",children:[x.member.name," - عام ",p]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(K.l6,{value:p,onValueChange:g,children:[(0,a.jsx)(K.bq,{className:"w-32",children:(0,a.jsx)(K.yv,{})}),(0,a.jsx)(K.gC,{children:f.map(e=>(0,a.jsx)(K.eb,{value:e.toString(),children:e},e))})]}),x&&(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>{if(!x)return;let e=window.open("","_blank");if(!e)return void alert("يرجى السماح بفتح النوافذ المنبثقة لتصدير التقرير");let s='\n        <!DOCTYPE html>\n        <html dir="rtl" lang="ar">\n        <head>\n          <meta charset="UTF-8">\n          <meta name="viewport" content="width=device-width, initial-scale=1.0">\n          <title>كشف حساب العضو - '.concat(x.member.name,"</title>\n          <style>\n            @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;600;700&display=swap');\n\n            * {\n              margin: 0;\n              padding: 0;\n              box-sizing: border-box;\n            }\n\n            body {\n              font-family: 'Noto Sans Arabic', Arial, sans-serif;\n              direction: rtl;\n              text-align: right;\n              background: white;\n              color: #333;\n              line-height: 1.6;\n              padding: 20px;\n            }\n\n            .header {\n              text-align: center;\n              margin-bottom: 20px;\n              border-bottom: 2px solid #2563eb;\n              padding-bottom: 15px;\n            }\n\n            .header h1 {\n              font-size: 24px;\n              font-weight: 700;\n              color: #1e40af;\n              margin-bottom: 8px;\n            }\n\n            .header .member-name {\n              font-size: 18px;\n              font-weight: 600;\n              color: #374151;\n              margin-bottom: 4px;\n            }\n\n            .header .year {\n              font-size: 14px;\n              color: #6b7280;\n              margin-bottom: 4px;\n            }\n\n            .header .date {\n              font-size: 12px;\n              color: #9ca3af;\n            }\n\n            .section {\n              margin-bottom: 15px;\n              background: #f9fafb;\n              border: 1px solid #e5e7eb;\n              border-radius: 6px;\n              padding: 15px;\n            }\n\n            .section-title {\n              font-size: 16px;\n              font-weight: 600;\n              color: #1f2937;\n              margin-bottom: 10px;\n              border-bottom: 1px solid #e5e7eb;\n              padding-bottom: 5px;\n            }\n\n            .info-grid {\n              display: grid;\n              grid-template-columns: 1fr 1fr;\n              gap: 10px;\n              margin-bottom: 10px;\n            }\n\n            .info-item {\n              background: white;\n              padding: 8px;\n              border-radius: 4px;\n              border: 1px solid #e5e7eb;\n              font-size: 14px;\n            }\n\n            .info-label {\n              font-weight: 600;\n              color: #374151;\n              margin-left: 8px;\n            }\n\n            .info-value {\n              color: #6b7280;\n            }\n\n            .summary-grid {\n              display: grid;\n              grid-template-columns: repeat(2, 1fr);\n              gap: 15px;\n              margin-bottom: 20px;\n            }\n\n            .summary-item {\n              background: white;\n              padding: 15px;\n              border-radius: 8px;\n              border: 1px solid #e5e7eb;\n              text-align: center;\n            }\n\n            .summary-label {\n              font-size: 14px;\n              color: #6b7280;\n              margin-bottom: 8px;\n            }\n\n            .summary-value {\n              font-size: 18px;\n              font-weight: 600;\n              color: #059669;\n            }\n\n            .monthly-grid {\n              display: grid;\n              grid-template-columns: repeat(4, 1fr);\n              gap: 8px;\n            }\n\n            .month-item {\n              background: white;\n              padding: 8px;\n              border-radius: 4px;\n              border: 1px solid #e5e7eb;\n              text-align: center;\n            }\n\n            .month-item.has-amount {\n              background: #f0f9ff;\n              border-color: #0ea5e9;\n            }\n\n            .month-name {\n              font-weight: 600;\n              color: #374151;\n              margin-bottom: 3px;\n              font-size: 12px;\n            }\n\n            .month-amount {\n              font-size: 14px;\n              font-weight: 600;\n              margin-bottom: 2px;\n            }\n\n            .month-amount.positive {\n              color: #059669;\n            }\n\n            .month-amount.zero {\n              color: #9ca3af;\n            }\n\n            .month-count {\n              font-size: 10px;\n              color: #6b7280;\n            }\n\n            .type-item {\n              background: white;\n              padding: 12px;\n              border-radius: 6px;\n              border: 1px solid #e5e7eb;\n              margin-bottom: 8px;\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\n            }\n\n            .type-name {\n              font-weight: 600;\n              color: #374151;\n            }\n\n            .type-details {\n              color: #059669;\n              font-weight: 600;\n            }\n\n            @media print {\n              body {\n                padding: 10px;\n                font-size: 11px;\n              }\n              .header {\n                margin-bottom: 15px;\n                padding-bottom: 10px;\n              }\n              .section {\n                break-inside: avoid;\n                margin-bottom: 10px;\n                padding: 10px;\n              }\n              .monthly-grid {\n                grid-template-columns: repeat(6, 1fr);\n                gap: 4px;\n              }\n              .month-item {\n                padding: 4px;\n              }\n              .month-name {\n                font-size: 10px;\n              }\n              .month-amount {\n                font-size: 12px;\n              }\n              .month-count {\n                font-size: 8px;\n              }\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"header\">\n            <h1>كشف حساب العضو</h1>\n            <div class=\"member-name\">").concat(x.member.name,'</div>\n            <div class="year">عام ').concat(p,'</div>\n            <div class="date">تاريخ التقرير: ').concat(new Date().toLocaleDateString("en-GB"),'</div>\n          </div>\n\n          <div class="section">\n            <div class="section-title">معلومات العضو</div>\n            <div class="info-grid">\n              <div class="info-item">\n                <span class="info-label">الاسم:</span>\n                <span class="info-value">').concat(x.member.name,"</span>\n              </div>\n              ").concat(x.member.phone?'\n              <div class="info-item">\n                <span class="info-label">الهاتف:</span>\n                <span class="info-value">'.concat(x.member.phone,"</span>\n              </div>\n              "):"","\n              ").concat(x.member.email?'\n              <div class="info-item">\n                <span class="info-label">البريد الإلكتروني:</span>\n                <span class="info-value">'.concat(x.member.email,"</span>\n              </div>\n              "):"",'\n              <div class="info-item">\n                <span class="info-label">عضو منذ:</span>\n                <span class="info-value">').concat((0,G.Yq)(x.member.createdAt),'</span>\n              </div>\n            </div>\n          </div>\n\n          <div class="section">\n            <div class="section-title">إجمالي المساهمات</div>\n            <div style="text-align: center; padding: 15px;">\n              <div class="summary-value" style="font-size: 28px; color: #059669; font-weight: bold;">\n                ').concat((0,G.vv)(x.summary.totalAmount),'\n              </div>\n            </div>\n          </div>\n\n\n\n          <div class="section">\n            <div class="section-title">المساهمات الشهرية - ').concat(p,'</div>\n            <div class="monthly-grid">\n              ').concat(x.monthlyData.map(e=>'\n                <div class="month-item '.concat(e.count>0?"has-amount":"",'">\n                  <div class="month-name">').concat(e.monthName,'</div>\n                  <div class="month-amount ').concat(e.count>0?"positive":"zero",'">\n                    ').concat((0,G.vv)(e.amount),'\n                  </div>\n                  <div class="month-count">').concat(e.count," معاملة</div>\n                </div>\n              ")).join(""),'\n            </div>\n          </div>\n\n          <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; color: #6b7280; font-size: 12px;">\n            تم إنشاء هذا التقرير بواسطة نظام إدارة ديوان أبو علوش\n          </div>\n        </body>\n        </html>\n      ');e.document.write(s),e.document.close(),e.onload=()=>{e.focus(),setTimeout(()=>{e.print(),e.onafterprint=()=>{e.close()}},1e3)}},className:"text-green-600 border-green-600 hover:bg-green-50",children:[(0,a.jsx)(m.A,{className:"w-4 h-4 ml-1"}),"تصدير"]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>t(!1),className:"text-gray-600 border-gray-300 hover:bg-gray-50",children:[(0,a.jsx)(Z.A,{className:"w-4 h-4 ml-1"}),"خروج"]})]})]})}),(0,a.jsx)("div",{className:"p-6 space-y-6",children:i?(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("div",{className:"text-gray-500",children:"جاري تحميل كشف الحساب..."})}):x?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(o.Zp,{className:"border-gray-200",children:[(0,a.jsx)(o.aR,{className:"pb-3",children:(0,a.jsxs)(o.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,a.jsx)(B.A,{className:"w-5 h-5 text-diwan-600"}),"معلومات العضو"]})}),(0,a.jsx)(o.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(B.A,{className:"w-4 h-4 text-gray-500"}),(0,a.jsx)("span",{className:"font-medium",children:x.member.name})]}),x.member.phone&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(v.A,{className:"w-4 h-4 text-gray-500"}),(0,a.jsx)("span",{children:x.member.phone})]}),x.member.email&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(U.A,{className:"w-4 h-4 text-gray-500"}),(0,a.jsx)("span",{children:x.member.email})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(_.A,{className:"w-4 h-4 text-gray-500"}),(0,a.jsxs)("span",{children:["عضو منذ ",(0,G.Yq)(x.member.createdAt)]})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)(o.Zp,{className:"border-green-200 bg-green-50",children:[(0,a.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(o.ZB,{className:"text-sm font-medium text-green-800",children:"إجمالي المساهمات"}),(0,a.jsx)(y.A,{className:"h-4 w-4 text-green-600"})]}),(0,a.jsx)(o.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-green-700",children:(0,G.vv)(x.summary.totalAmount)})})]}),(0,a.jsxs)(o.Zp,{className:"border-blue-200 bg-blue-50",children:[(0,a.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(o.ZB,{className:"text-sm font-medium text-blue-800",children:"عدد المعاملات"}),(0,a.jsx)(X.A,{className:"h-4 w-4 text-blue-600"})]}),(0,a.jsx)(o.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-700",children:x.summary.transactionCount})})]}),(0,a.jsxs)(o.Zp,{className:"border-purple-200 bg-purple-50",children:[(0,a.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(o.ZB,{className:"text-sm font-medium text-purple-800",children:"متوسط شهري"}),(0,a.jsx)(Q.A,{className:"h-4 w-4 text-purple-600"})]}),(0,a.jsx)(o.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-700",children:(0,G.vv)(x.summary.averageMonthlyContribution)})})]}),(0,a.jsxs)(o.Zp,{className:"border-orange-200 bg-orange-50",children:[(0,a.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(o.ZB,{className:"text-sm font-medium text-orange-800",children:"آخر مساهمة"}),(0,a.jsx)(O.A,{className:"h-4 w-4 text-orange-600"})]}),(0,a.jsx)(o.Wu,{children:(0,a.jsx)("div",{className:"text-sm font-bold text-orange-700",children:x.summary.lastTransactionDate?(0,G.Yq)(x.summary.lastTransactionDate):"لا توجد مساهمات"})})]})]}),(0,a.jsxs)(o.Zp,{className:"border-gray-200",children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(ee.A,{className:"w-5 h-5 text-diwan-600"}),"التوزيع حسب نوع المساهمة"]})}),(0,a.jsx)(o.Wu,{children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:Object.entries(x.byType).map(e=>{let[s,t]=e;return(0,a.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-600",children:(0,G.Tk)(s)}),(0,a.jsx)(R.E,{variant:"secondary",children:t.count})]}),(0,a.jsx)("div",{className:"text-lg font-bold text-diwan-600",children:(0,G.vv)(t.amount)}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[(t.amount/x.summary.totalAmount*100).toFixed(1),"% من الإجمالي"]})]},s)})})})]}),(0,a.jsxs)(o.Zp,{className:"border-gray-200",children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(_.A,{className:"w-5 h-5 text-diwan-600"}),"المساهمات الشهرية - ",p]})}),(0,a.jsx)(o.Wu,{children:(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3",children:x.monthlyData.map(e=>(0,a.jsxs)("div",{className:"p-3 border rounded-lg text-center ".concat(e.count>0?"bg-green-50 border-green-200":"bg-gray-50 border-gray-200"),children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-600 mb-1",children:e.monthName}),(0,a.jsx)("div",{className:"text-lg font-bold ".concat(e.count>0?"text-green-600":"text-gray-400"),children:(0,G.vv)(e.amount)}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[e.count," معاملة"]})]},e.month))})})]}),(0,a.jsxs)(o.Zp,{className:"border-gray-200",children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(O.A,{className:"w-5 h-5 text-diwan-600"}),"آخر المعاملات"]})}),(0,a.jsx)(o.Wu,{children:x.recentTransactions.length>0?(0,a.jsxs)(d.XI,{children:[(0,a.jsx)(d.A0,{children:(0,a.jsxs)(d.Hj,{children:[(0,a.jsx)(d.nd,{children:"التاريخ"}),(0,a.jsx)(d.nd,{children:"المبلغ"}),(0,a.jsx)(d.nd,{children:"المصدر"}),(0,a.jsx)(d.nd,{children:"النوع"}),(0,a.jsx)(d.nd,{children:"الوصف"}),(0,a.jsx)(d.nd,{children:"المُدخِل"})]})}),(0,a.jsx)(d.BF,{children:x.recentTransactions.map(e=>(0,a.jsxs)(d.Hj,{children:[(0,a.jsx)(d.nA,{children:(0,G.Yq)(e.date)}),(0,a.jsx)(d.nA,{children:(0,a.jsx)("span",{className:"font-medium text-green-600",children:(0,G.vv)(e.amount)})}),(0,a.jsx)(d.nA,{children:e.source}),(0,a.jsx)(d.nA,{children:(0,a.jsx)(R.E,{variant:"outline",children:(0,G.Tk)(e.type)})}),(0,a.jsx)(d.nA,{children:e.description||"-"}),(0,a.jsx)(d.nA,{className:"text-sm text-gray-600",children:e.createdBy.name})]},e.id))})]}):(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:"لا توجد معاملات في هذه الفترة"})})]}),x.allTransactions.length>5&&(0,a.jsxs)(o.Zp,{className:"border-gray-200",children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(j.A,{className:"w-5 h-5 text-diwan-600"}),"جميع المعاملات (",x.allTransactions.length,")"]})}),(0,a.jsx)(o.Wu,{children:(0,a.jsx)("div",{className:"max-h-96 overflow-y-auto",children:(0,a.jsxs)(d.XI,{children:[(0,a.jsx)(d.A0,{children:(0,a.jsxs)(d.Hj,{children:[(0,a.jsx)(d.nd,{children:"التاريخ"}),(0,a.jsx)(d.nd,{children:"المبلغ"}),(0,a.jsx)(d.nd,{children:"المصدر"}),(0,a.jsx)(d.nd,{children:"النوع"}),(0,a.jsx)(d.nd,{children:"الوصف"}),(0,a.jsx)(d.nd,{children:"المُدخِل"})]})}),(0,a.jsx)(d.BF,{children:x.allTransactions.map(e=>(0,a.jsxs)(d.Hj,{children:[(0,a.jsx)(d.nA,{children:(0,G.Yq)(e.date)}),(0,a.jsx)(d.nA,{children:(0,a.jsx)("span",{className:"font-medium text-green-600",children:(0,G.vv)(e.amount)})}),(0,a.jsx)(d.nA,{children:e.source}),(0,a.jsx)(d.nA,{children:(0,a.jsx)(R.E,{variant:"outline",children:(0,G.Tk)(e.type)})}),(0,a.jsx)(d.nA,{children:e.description||"-"}),(0,a.jsx)(d.nA,{className:"text-sm text-gray-600",children:e.createdBy.name})]},e.id))})]})})})]})]}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(j.A,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"لا توجد بيانات لعرضها"}),(0,a.jsxs)("p",{className:"text-gray-500 mb-4",children:["لم يتم العثور على معاملات مالية لهذا العضو في عام ",p]}),(0,a.jsx)("p",{className:"text-sm text-blue-600",children:"\uD83D\uDCA1 جرب تغيير السنة من القائمة المنسدلة أعلاه لعرض بيانات سنوات أخرى"})]})})]})})}let et=z.z.object({amount:z.z.number().positive("المبلغ يجب أن يكون أكبر من صفر"),date:z.z.string().min(1,"التاريخ مطلوب"),source:z.z.string().min(1,"مصدر الإيراد مطلوب"),type:z.z.enum(["SUBSCRIPTION","DONATION","EVENT","OTHER"]).default("SUBSCRIPTION"),description:z.z.string().optional(),notes:z.z.string().optional()});function ea(e){let{open:s,onOpenChange:t,member:n,onSuccess:o}=e,[d,c]=(0,r.useState)(!1),{register:m,handleSubmit:x,reset:h,setValue:p,watch:g,formState:{errors:u}}=(0,E.mN)({resolver:(0,S.u)(et),defaultValues:{amount:0,date:new Date().toISOString().split("T")[0],source:"",type:"SUBSCRIPTION",description:"",notes:""}}),b=g("type");(0,r.useEffect)(()=>{s&&n&&h({amount:0,date:new Date().toISOString().split("T")[0],source:"",type:"SUBSCRIPTION",description:"",notes:""})},[s,n,h]);let j=async e=>{if(n)try{var s,a;c(!0);let r={...e,amount:Number(e.amount),date:new Date(e.date),memberId:n.id,description:(null==(s=e.description)?void 0:s.trim())||null,notes:(null==(a=e.notes)?void 0:a.trim())||null},l=await fetch("/api/incomes",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!l.ok){let e=await l.json();throw Error(e.error||"حدث خطأ")}alert("تم إضافة إيراد للعضو ".concat(n.name," بنجاح")),t(!1),null==o||o()}catch(e){console.error("خطأ في إضافة الإيراد:",e),alert(e.message||"حدث خطأ في إضافة الإيراد")}finally{c(!1)}};return n?(0,a.jsx)(k.lG,{open:s,onOpenChange:t,children:(0,a.jsxs)(k.Cf,{className:"max-w-[50vw] max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)(k.c7,{className:"p-6 pb-4 border-b border-gray-100 bg-gradient-to-r from-diwan-50 to-blue-50",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"p-3 bg-white rounded-xl shadow-sm border border-diwan-200",children:(0,a.jsx)(y.A,{className:"w-6 h-6 text-diwan-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(k.L3,{className:"text-xl font-bold text-gray-900",children:"إضافة إيراد للعضو"}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,a.jsx)(B.A,{className:"w-4 h-4 text-gray-500"}),(0,a.jsx)("p",{className:"text-gray-600 font-medium",children:n.name})]})]})]})}),(0,a.jsxs)("form",{onSubmit:x(j),className:"p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(D.J,{htmlFor:"amount",children:"المبلغ (دينار أردني) *"}),(0,a.jsx)(i.p,{id:"amount",type:"number",step:"0.01",min:"0",...m("amount",{valueAsNumber:!0}),className:u.amount?"border-red-500":""}),u.amount&&(0,a.jsx)("p",{className:"text-sm text-red-500",children:u.amount.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(D.J,{htmlFor:"date",children:"التاريخ *"}),(0,a.jsx)(i.p,{id:"date",type:"date",...m("date"),className:u.date?"border-red-500":""}),u.date&&(0,a.jsx)("p",{className:"text-sm text-red-500",children:u.date.message})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(D.J,{htmlFor:"source",children:"مصدر الإيراد *"}),(0,a.jsx)(i.p,{id:"source",...m("source"),placeholder:"مثال: اشتراك شهري، تبرع، رسوم فعالية",className:u.source?"border-red-500":""}),u.source&&(0,a.jsx)("p",{className:"text-sm text-red-500",children:u.source.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(D.J,{htmlFor:"type",children:"نوع الإيراد"}),(0,a.jsxs)(K.l6,{value:b,onValueChange:e=>p("type",e),children:[(0,a.jsx)(K.bq,{children:(0,a.jsx)(K.yv,{})}),(0,a.jsxs)(K.gC,{children:[(0,a.jsx)(K.eb,{value:"SUBSCRIPTION",children:"اشتراكات"}),(0,a.jsx)(K.eb,{value:"DONATION",children:"تبرعات"}),(0,a.jsx)(K.eb,{value:"EVENT",children:"فعاليات"}),(0,a.jsx)(K.eb,{value:"OTHER",children:"أخرى"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(D.J,{htmlFor:"description",children:"الوصف (اختياري)"}),(0,a.jsx)(i.p,{id:"description",...m("description"),placeholder:"وصف إضافي للإيراد"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(D.J,{htmlFor:"notes",children:"ملاحظات (اختياري)"}),(0,a.jsx)(I.T,{id:"notes",...m("notes"),placeholder:"أي ملاحظات إضافية",rows:3})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2 space-x-reverse pt-4 border-t",children:[(0,a.jsx)(l.$,{type:"button",variant:"outline",onClick:()=>t(!1),disabled:d,children:"إلغاء"}),(0,a.jsx)(l.$,{type:"submit",disabled:d,className:"bg-diwan-600 hover:bg-diwan-700",children:d?"جاري الحفظ...":"حفظ الإيراد"})]})]})]})}):null}var er=t(32919),en=t(78749);function el(e){let{open:s,onOpenChange:t,member:n,onSuccess:o}=e,[d,c]=(0,r.useState)(""),[m,x]=(0,r.useState)(""),[h,p]=(0,r.useState)(!1),[g,u]=(0,r.useState)(!1),[b,j]=(0,r.useState)(!1),[f,v]=(0,r.useState)(""),y=async e=>{if(e.preventDefault(),v(""),!d)return void v("كلمة المرور مطلوبة");if(d.length<6)return void v("كلمة المرور يجب أن تكون على الأقل 6 أحرف");if(d!==m)return void v("كلمة المرور وتأكيد كلمة المرور غير متطابقين");if(!n)return void v("لم يتم تحديد العضو");try{j(!0);let e=await fetch("/api/members/".concat(n.id,"/password"),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({password:d})});if(!e.ok){let s=await e.json();throw Error(s.message||"فشل في تحديث كلمة المرور")}alert("تم تحديث كلمة مرور العضو بنجاح"),c(""),x(""),v(""),t(!1),o()}catch(e){console.error("خطأ في تحديث كلمة المرور:",e),v(e.message||"حدث خطأ أثناء تحديث كلمة المرور")}finally{j(!1)}},w=()=>{c(""),x(""),v(""),p(!1),u(!1),t(!1)};return(0,a.jsx)(k.lG,{open:s,onOpenChange:w,children:(0,a.jsxs)(k.Cf,{className:"max-w-[50vw]",children:[(0,a.jsx)(k.c7,{children:(0,a.jsxs)(k.L3,{className:"flex items-center gap-2 text-xl",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)(A.A,{className:"w-5 h-5 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-bold",children:"إدارة كلمة المرور"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 font-normal",children:"تعيين كلمة مرور للعضو"})]})]})}),n&&(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(B.A,{className:"w-5 h-5 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-blue-900",children:n.name}),n.email&&(0,a.jsx)("p",{className:"text-sm text-blue-700",children:n.email}),n.phone&&(0,a.jsx)("p",{className:"text-sm text-blue-700",children:n.phone})]})]})}),(0,a.jsxs)("form",{onSubmit:y,className:"space-y-4",children:[f&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm",children:f}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(D.J,{htmlFor:"password",className:"text-gray-700 font-medium",children:"كلمة المرور الجديدة"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(er.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,a.jsx)(i.p,{id:"password",type:h?"text":"password",value:d,onChange:e=>c(e.target.value),placeholder:"أدخل كلمة المرور الجديدة",required:!0,disabled:b,className:"pr-10 pl-10"}),(0,a.jsx)("button",{type:"button",onClick:()=>p(!h),className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",disabled:b,children:h?(0,a.jsx)(en.A,{className:"w-5 h-5"}):(0,a.jsx)(N.A,{className:"w-5 h-5"})})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"كلمة المرور يجب أن تكون على الأقل 6 أحرف"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(D.J,{htmlFor:"confirmPassword",className:"text-gray-700 font-medium",children:"تأكيد كلمة المرور"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(er.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,a.jsx)(i.p,{id:"confirmPassword",type:g?"text":"password",value:m,onChange:e=>x(e.target.value),placeholder:"أعد إدخال كلمة المرور",required:!0,disabled:b,className:"pr-10 pl-10"}),(0,a.jsx)("button",{type:"button",onClick:()=>u(!g),className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",disabled:b,children:g?(0,a.jsx)(en.A,{className:"w-5 h-5"}):(0,a.jsx)(N.A,{className:"w-5 h-5"})})]})]}),(0,a.jsxs)(k.Es,{className:"gap-3 pt-4",children:[(0,a.jsxs)(l.$,{type:"button",variant:"outline",onClick:w,disabled:b,className:"flex-1",children:[(0,a.jsx)(Z.A,{className:"w-4 h-4 ml-2"}),"إلغاء"]}),(0,a.jsx)(l.$,{type:"submit",disabled:b||!d||!m,className:"flex-1 bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 text-white",children:b?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),"جاري الحفظ..."]}):(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(H.A,{className:"w-4 h-4"}),"حفظ كلمة المرور"]})})]})]}),(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-4",children:(0,a.jsxs)("p",{className:"text-sm text-yellow-800",children:[(0,a.jsx)("strong",{children:"ملاحظة:"})," بعد تعيين كلمة المرور، سيتمكن العضو من تسجيل الدخول باستخدام بريده الإلكتروني وكلمة المرور الجديدة لعرض كشف حسابه الشخصي ومشاهدة معرض الصور."]})})]})})}function ei(e){let{open:s,onOpenChange:t,onSearch:n,onReset:o}=e,[d,c]=(0,r.useState)({name:"",phone:"",address:"",status:"all",minContributions:"",maxContributions:"",joinedAfter:"",joinedBefore:""}),m=(e,s)=>{c(t=>({...t,[e]:s}))};return(0,a.jsx)(k.lG,{open:s,onOpenChange:t,children:(0,a.jsxs)(k.Cf,{className:"max-w-[50vw]",children:[(0,a.jsx)(k.HM,{onOpenChange:t}),(0,a.jsx)(k.c7,{className:"p-6 pb-0",children:(0,a.jsxs)(k.L3,{className:"flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(b.A,{className:"w-5 h-5 text-diwan-600"}),(0,a.jsx)("span",{children:"البحث المتقدم"})]})}),(0,a.jsxs)("div",{className:"p-6 pt-0 space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(D.J,{htmlFor:"name",children:"الاسم"}),(0,a.jsx)(i.p,{id:"name",value:d.name,onChange:e=>m("name",e.target.value),placeholder:"البحث في الاسم..."})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(D.J,{htmlFor:"phone",children:"رقم الهاتف"}),(0,a.jsx)(i.p,{id:"phone",value:d.phone,onChange:e=>m("phone",e.target.value),placeholder:"البحث في رقم الهاتف...",dir:"ltr"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(D.J,{htmlFor:"address",children:"العنوان"}),(0,a.jsx)(i.p,{id:"address",value:d.address,onChange:e=>m("address",e.target.value),placeholder:"البحث في العنوان..."})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(D.J,{htmlFor:"status",children:"حالة العضو"}),(0,a.jsxs)("select",{id:"status",value:d.status,onChange:e=>m("status",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-diwan-500",children:[(0,a.jsx)("option",{value:"all",children:"جميع الأعضاء"}),(0,a.jsx)("option",{value:"ACTIVE",children:"نشط"}),(0,a.jsx)("option",{value:"LATE",children:"متأخر"}),(0,a.jsx)("option",{value:"INACTIVE",children:"غير ملتزم"}),(0,a.jsx)("option",{value:"SUSPENDED",children:"موقوف مؤقتاً"}),(0,a.jsx)("option",{value:"ARCHIVED",children:"مؤرشف"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(D.J,{children:"نطاق المساهمات (بالدينار الأردني)"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,a.jsx)(i.p,{value:d.minContributions,onChange:e=>m("minContributions",e.target.value),placeholder:"الحد الأدنى",type:"number",min:"0",step:"0.01",dir:"ltr"}),(0,a.jsx)(i.p,{value:d.maxContributions,onChange:e=>m("maxContributions",e.target.value),placeholder:"الحد الأعلى",type:"number",min:"0",step:"0.01",dir:"ltr"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(D.J,{children:"نطاق تاريخ الانضمام"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(D.J,{htmlFor:"joinedAfter",className:"text-xs text-gray-500",children:"من تاريخ"}),(0,a.jsx)(i.p,{id:"joinedAfter",value:d.joinedAfter,onChange:e=>m("joinedAfter",e.target.value),type:"date",dir:"ltr"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(D.J,{htmlFor:"joinedBefore",className:"text-xs text-gray-500",children:"إلى تاريخ"}),(0,a.jsx)(i.p,{id:"joinedBefore",value:d.joinedBefore,onChange:e=>m("joinedBefore",e.target.value),type:"date",dir:"ltr"})]})]})]})]}),(0,a.jsxs)(k.Es,{className:"p-6 pt-0",children:[(0,a.jsxs)(l.$,{type:"button",variant:"outline",onClick:()=>{c({name:"",phone:"",address:"",status:"all",minContributions:"",maxContributions:"",joinedAfter:"",joinedBefore:""}),o(),t(!1)},className:"text-red-600 border-red-600 hover:bg-red-50",children:[(0,a.jsx)(Z.A,{className:"w-4 h-4 ml-2"}),"إعادة تعيين"]}),(0,a.jsxs)(l.$,{type:"button",onClick:()=>{n(d),t(!1)},className:"bg-diwan-600 hover:bg-diwan-700",children:[(0,a.jsx)(u.A,{className:"w-4 h-4 ml-2"}),"بحث"]})]})]})})}var eo=t(63436),ed=t(66474);let ec=r.forwardRef((e,s)=>{let{className:t,options:r,children:n,placeholder:l,size:i="md",error:o=!1,helperText:d,...c}=e;return(0,a.jsxs)("div",{className:"relative w-full",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("select",{ref:s,className:(0,G.cn)("w-full appearance-none rounded-xl border-2 bg-white font-medium transition-all duration-200 focus:outline-none focus:ring-4 disabled:cursor-not-allowed disabled:opacity-50",{sm:"h-9 px-3 text-sm",md:"h-11 px-4 text-base",lg:"h-13 px-5 text-lg"}[i],o?"border-danger-300 text-danger-700 focus:border-danger-500 focus:ring-danger-500/20":"border-secondary-200 text-secondary-700 hover:border-secondary-300 focus:border-primary-500 focus:ring-primary-500/20",t),...c,children:[l&&(0,a.jsx)("option",{value:"",disabled:!0,children:l}),r?r.map(e=>(0,a.jsx)("option",{value:e.value,disabled:e.disabled,children:e.label},e.value)):n]}),(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:(0,a.jsx)(ed.A,{className:"h-5 w-5 text-secondary-400"})})]}),d&&(0,a.jsx)("p",{className:(0,G.cn)("mt-1 text-sm",o?"text-danger-600":"text-secondary-500"),children:d})]})});function em(e){let{includeAll:s=!0,allLabel:t="جميع الحالات",...r}=e,n=[...s?[{value:"all",label:t}]:[],{value:"ACTIVE",label:"نشط"},{value:"LATE",label:"متأخر"},{value:"INACTIVE",label:"غير ملتزم"},{value:"SUSPENDED",label:"موقوف مؤقتاً"},{value:"ARCHIVED",label:"مؤرشف"}];return(0,a.jsx)(ec,{...r,options:n})}ec.displayName="NativeSelect";var ex=t(26597),eh=t(52699),ep=t.n(eh);function eg(){let{data:e}=(0,n.useSession)(),[s,t]=(0,r.useState)([]),[E,S]=(0,r.useState)(!0),[z,T]=(0,r.useState)(""),[k,D]=(0,r.useState)("all"),[I,R]=(0,r.useState)({page:1,limit:10,total:0,pages:0}),[F,O]=(0,r.useState)(!1),[W,P]=(0,r.useState)(null),[V,B]=(0,r.useState)(!1),[L,Z]=(0,r.useState)(null),[J,$]=(0,r.useState)(!1),[U,H]=(0,r.useState)(!1),[_,Y]=(0,r.useState)(null),[K,X]=(0,r.useState)(!1),[Q,ee]=(0,r.useState)(!1),[et,er]=(0,r.useState)(null),[en,ed]=(0,r.useState)(!1),[ec,eh]=(0,r.useState)(null),[eg,eu]=(0,r.useState)({total:0,active:0,late:0,inactive:0,suspended:0,archived:0}),eb=async()=>{try{S(!0);let e=new URLSearchParams({search:z,status:k,page:I.page.toString(),limit:I.limit.toString()}),s=await fetch("/api/members?".concat(e));if(!s.ok)throw Error("فشل في جلب الأعضاء");let a=await s.json();t(a.members),R(a.pagination)}catch(e){console.error("خطأ في جلب الأعضاء:",e)}finally{S(!1)}},ej=async()=>{try{let e=await fetch("/api/members?limit=1000");if(!e.ok)return;let s=await e.json(),t=s.members.length,a=s.members.filter(e=>"ACTIVE"===e.status).length,r=s.members.filter(e=>"LATE"===e.status).length,n=s.members.filter(e=>"INACTIVE"===e.status).length,l=s.members.filter(e=>"SUSPENDED"===e.status).length,i=s.members.filter(e=>"ARCHIVED"===e.status).length;eu({total:t,active:a,late:r,inactive:n,suspended:l,archived:i})}catch(e){console.error("خطأ في جلب الإحصائيات:",e)}};(0,r.useEffect)(()=>{eb()},[z,k,I.page]),(0,r.useEffect)(()=>{ej()},[]);let ef=e=>{P(e),O(!0)},ev=e=>{Z(e),B(!0)},eN=e=>{Y(e),H(!0)},ey=e=>{er(e),ed(!0)},ew=e=>{eh(e),ee(!0)},eA=async e=>{if(confirm("هل أنت متأكد من حذف هذا العضو؟"))try{let s=await fetch("/api/members/".concat(e),{method:"DELETE"});if(!s.ok){let e=await s.json();alert(e.error||"فشل في حذف العضو");return}eb(),ej()}catch(e){console.error("خطأ في حذف العضو:",e),alert("حدث خطأ في حذف العضو")}},eC=()=>{O(!1),eb(),ej()},eE=new ex.default("p","mm","a4");eE.setFont("helvetica"),eE.setFontSize(18);let eS="Diwan Abu Alosh - Members Report",ez=eE.internal.pageSize.getWidth(),eT=eE.getTextWidth(eS);eE.text(eS,(ez-eT)/2,25),eE.setFontSize(14);let ek="Members List Report",eD=eE.getTextWidth(ek);eE.text(ek,(ez-eD)/2,35),eE.setDrawColor(0,0,0),eE.line(30,40,ez-30,40),eE.setFontSize(12);let eI=new Date().toLocaleDateString("en-GB");eE.text("Report Date: ".concat(eI),20,55),eE.setFillColor(245,245,245),eE.rect(20,65,ez-40,40,"F"),eE.setDrawColor(200,200,200),eE.rect(20,65,ez-40,40),eE.setFontSize(11),eE.setFont("helvetica","bold"),eE.text("Members Statistics:",25,75),eE.setFont("helvetica","normal"),eE.setFontSize(10),eE.text("Total Members: ".concat(eg.total),25,82),eE.text("Active Members: ".concat(eg.active),25,88),eE.text("Late Members: ".concat(eg.late),100,82),eE.text("Inactive Members: ".concat(eg.inactive),100,88),eE.text("Suspended Members: ".concat(eg.suspended),25,94),eE.text("Archived Members: ".concat(eg.archived),100,94);let eR=120;eE.setFontSize(10),eE.setFont("helvetica","bold"),eE.setFillColor(70,130,180),eE.rect(15,eR-6,180,10,"F"),eE.setTextColor(255,255,255);let eF=["Member Name","Phone Number","Status","Total Income (JOD)","Join Date"],eO=[20,65,100,130,165];eF.forEach((e,s)=>{eE.text(e,eO[s],eR)}),eE.setTextColor(0,0,0),eE.setDrawColor(0,0,0),eE.line(15,eR+4,195,eR+4),eR+=15,eE.setFont("helvetica","normal"),eE.setFontSize(9),s.forEach((e,s)=>{var t;eR>270&&(eE.addPage(),eR=30,eE.setFont("helvetica","bold"),eE.setFontSize(10),eE.setFillColor(70,130,180),eE.rect(15,eR-6,180,10,"F"),eE.setTextColor(255,255,255),eF.forEach((e,s)=>{eE.text(e,eO[s],eR)}),eE.setTextColor(0,0,0),eE.line(15,eR+4,195,eR+4),eR+=15,eE.setFont("helvetica","normal"),eE.setFontSize(9)),s%2==0&&(eE.setFillColor(248,249,250),eE.rect(15,eR-5,180,9,"F"));let a=e.name.length>20?e.name.substring(0,20)+"...":e.name,r=e.phone||"Not Specified",n=(e=>{switch(e){case"ACTIVE":return"Active";case"LATE":return"Late";case"INACTIVE":return"Inactive";case"SUSPENDED":return"Suspended";case"ARCHIVED":return"Archived";default:return e}})(e.status),l=(0,G.vv)((null==(t=e.incomes)?void 0:t.reduce((e,s)=>e+s.amount,0))||0);[a,r,n,l,(0,G.Yq)(e.createdAt)].forEach((e,s)=>{eE.text(e.toString(),eO[s],eR)}),eE.setDrawColor(230,230,230),eE.line(15,eR+3,195,eR+3),eR+=9});let eW=eE.internal.getNumberOfPages();for(let e=1;e<=eW;e++)eE.setPage(e),eE.setFontSize(8),eE.setTextColor(100,100,100),eE.text("Page ".concat(e," of ").concat(eW),ez-30,285),eE.text("Generated by Diwan Abu Alosh System",20,285),eE.setDrawColor(200,200,200),eE.line(20,280,ez-20,280);let eP="Members_Report_".concat(new Date().toISOString().split("T")[0],".pdf");eE.save(eP);let eV=async()=>{let e=document.createElement("div");e.style.position="absolute",e.style.left="-9999px",e.style.top="0",e.style.width="210mm",e.style.padding="20mm",e.style.fontFamily="Arial, sans-serif",e.style.fontSize="12px",e.style.lineHeight="1.4",e.style.color="#000",e.style.backgroundColor="#fff",e.style.direction="rtl";let t=new Date().toLocaleDateString("en-GB",{year:"numeric",month:"2-digit",day:"2-digit"});e.innerHTML='\n      <div style="text-align: center; margin-bottom: 30px;">\n        <h1 style="font-size: 24px; margin: 0; color: #2c3e50;">ديوان أبو علوش</h1>\n        <h2 style="font-size: 18px; margin: 10px 0; color: #34495e;">قائمة الأعضاء</h2>\n        <hr style="border: 1px solid #bdc3c7; margin: 20px 0;">\n      </div>\n\n      <div style="margin-bottom: 25px;">\n        <p style="font-size: 14px; margin: 5px 0;"><strong>تاريخ التقرير:</strong> '.concat(t,'</p>\n      </div>\n\n      <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 25px; border: 1px solid #dee2e6;">\n        <h3 style="margin: 0 0 10px 0; color: #495057;">إحصائيات الأعضاء:</h3>\n        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">\n          <div>• إجمالي الأعضاء: <strong>').concat(eg.total,"</strong> عضو</div>\n          <div>• الأعضاء النشطون: <strong>").concat(eg.active,"</strong> عضو</div>\n          <div>• الأعضاء المتأخرون: <strong>").concat(eg.late,"</strong> عضو</div>\n          <div>• الأعضاء غير الملتزمون: <strong>").concat(eg.inactive,"</strong> عضو</div>\n          <div>• الأعضاء الموقوفون: <strong>").concat(eg.suspended,"</strong> عضو</div>\n          <div>• الأعضاء المؤرشفون: <strong>").concat(eg.archived,'</strong> عضو</div>\n        </div>\n      </div>\n\n      <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">\n        <thead>\n          <tr style="background: #4682b4; color: white;">\n            <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">اسم العضو</th>\n            <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">رقم الهاتف</th>\n            <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">حالة العضو</th>\n            <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">إجمالي الإيرادات</th>\n            <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">تاريخ الانضمام</th>\n          </tr>\n        </thead>\n        <tbody>\n          ').concat(s.map((e,s)=>{var t;return'\n            <tr style="background: '.concat(s%2==0?"#f8f9fa":"#ffffff",';">\n              <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">').concat(e.name,'</td>\n              <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">').concat(e.phone||"غير محدد",'</td>\n              <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">').concat((0,G.WK)(e.status),'</td>\n              <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">').concat((0,G.vv)((null==(t=e.incomes)?void 0:t.reduce((e,s)=>e+s.amount,0))||0),'</td>\n              <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">').concat((0,G.Yq)(e.createdAt),"</td>\n            </tr>\n          ")}).join(""),'\n        </tbody>\n      </table>\n\n      <div style="margin-top: 30px; text-align: center; font-size: 10px; color: #6c757d;">\n        <hr style="border: 1px solid #dee2e6; margin: 20px 0;">\n        <p>تم إنشاؤه بواسطة نظام ديوان أبو علوش - ').concat(t,"</p>\n      </div>\n    "),document.body.appendChild(e);try{let s=await ep()(e,{scale:2,useCORS:!0,allowTaint:!0,backgroundColor:"#ffffff"}),t=s.toDataURL("image/png"),a=new ex.default("p","mm","a4"),r=210*s.height/s.width,n=r,l=0;for(a.addImage(t,"PNG",0,l,210,r),n-=295;n>=0;)l=n-r,a.addPage(),a.addImage(t,"PNG",0,l,210,r),n-=295;let i="قائمة_اعضاء_الديوان_".concat(new Date().toISOString().split("T")[0],".pdf");a.save(i)}catch(e){console.error("خطأ في تصدير PDF:",e),alert("حدث خطأ في تصدير PDF")}finally{document.body.removeChild(e)}},eB=async(e,s)=>{try{let t=await fetch("/api/members/".concat(e.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,status:s})});if(!t.ok){let e=await t.json();alert(e.error||"فشل في تحديث حالة العضو");return}eb(),ej()}catch(e){console.error("خطأ في تحديث حالة العضو:",e),alert("حدث خطأ في تحديث حالة العضو")}},eL=(null==e?void 0:e.user.role)!=="VIEWER",eZ=(null==e?void 0:e.user.role)==="ADMIN";return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsx)("div",{className:"text-center mb-8",children:(0,a.jsxs)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl shadow-2xl p-10 text-white relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-30 animate-pulse"}),(0,a.jsxs)("div",{className:"relative z-10",children:[(0,a.jsx)("div",{className:"inline-flex items-center justify-center w-20 h-20 rounded-full mb-6 bg-white bg-opacity-20 backdrop-blur-sm",children:(0,a.jsx)(c.A,{className:"w-10 h-10 text-white"})}),(0,a.jsx)("h1",{className:"text-5xl font-black mb-4 text-white",children:"إدارة الأعضاء"}),(0,a.jsx)("p",{className:"text-xl font-semibold mb-6 text-blue-100",children:"إدارة شاملة لأعضاء ديوان أبو علوش وبياناتهم"}),(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 space-x-reverse",children:[(0,a.jsx)("div",{className:"h-1 w-16 rounded-full bg-white bg-opacity-60"}),(0,a.jsx)("div",{className:"h-1 w-8 rounded-full bg-white bg-opacity-40"}),(0,a.jsx)("div",{className:"h-1 w-16 rounded-full bg-white bg-opacity-60"})]})]})]})}),(0,a.jsx)("div",{className:"flex justify-center mb-8",children:(0,a.jsx)("div",{className:"bg-white rounded-2xl shadow-xl p-6 border border-gray-100",children:(0,a.jsxs)("div",{className:"flex flex-wrap gap-4 justify-center",children:[(0,a.jsxs)(l.$,{onClick:()=>{let e=new Blob(["\uFEFF"+[["الاسم","الهاتف","العنوان","الحالة","إجمالي الإيرادات","تاريخ الإضافة"],...s.map(e=>{var s;return[e.name,e.phone||"",e.address||"",(0,G.WK)(e.status),((null==(s=e.incomes)?void 0:s.reduce((e,s)=>e+s.amount,0))||0).toString(),(0,G.Yq)(e.createdAt)]})].map(e=>e.join(",")).join("\n")],{type:"text/csv;charset=utf-8;"}),t=document.createElement("a");t.href=URL.createObjectURL(e),t.download="اعضاء_الديوان_".concat(new Date().toISOString().split("T")[0],".csv"),t.click()},className:"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold",children:[(0,a.jsx)(m.A,{className:"w-5 h-5 ml-2"}),"تصدير CSV"]}),(0,a.jsxs)(l.$,{onClick:eV,className:"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold",children:[(0,a.jsx)(x.A,{className:"w-5 h-5 ml-2"}),"تصدير PDF"]}),eL&&(0,a.jsxs)(l.$,{onClick:()=>{P(null),O(!0)},className:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold",children:[(0,a.jsx)(h.A,{className:"w-5 h-5 ml-2"}),"إضافة عضو جديد"]})]})})}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-6 sm:grid-cols-3 lg:grid-cols-6",children:[(0,a.jsxs)(o.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-500 to-blue-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-blue-600 p-1 rounded-t-xl"}),(0,a.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,a.jsx)(o.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"إجمالي الأعضاء"}),(0,a.jsx)("div",{className:"p-3 rounded-2xl shadow-lg",style:{backgroundColor:"#007bff"},children:(0,a.jsx)(c.A,{className:"h-5 w-5 text-white"})})]}),(0,a.jsx)(o.Wu,{className:"relative z-10",children:(0,a.jsx)("div",{className:"text-3xl font-black mb-2",style:{color:"#191970"},children:eg.total})})]}),(0,a.jsxs)(o.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-green-500 to-green-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-green-500 to-green-600 p-1 rounded-t-xl"}),(0,a.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,a.jsx)(o.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"نشط"}),(0,a.jsx)("div",{className:"p-3 rounded-2xl shadow-lg",style:{backgroundColor:"#28a745"},children:(0,a.jsx)(p.A,{className:"h-5 w-5 text-white"})})]}),(0,a.jsx)(o.Wu,{className:"relative z-10",children:(0,a.jsx)("div",{className:"text-3xl font-black mb-2",style:{color:"#191970"},children:eg.active})})]}),(0,a.jsxs)(o.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-yellow-500 to-yellow-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-yellow-500 to-yellow-600 p-1 rounded-t-xl"}),(0,a.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,a.jsx)(o.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"متأخر"}),(0,a.jsx)("div",{className:"p-3 rounded-2xl shadow-lg",style:{backgroundColor:"#ffc107"},children:(0,a.jsx)("span",{className:"text-white text-sm font-bold",children:"⏰"})})]}),(0,a.jsx)(o.Wu,{className:"relative z-10",children:(0,a.jsx)("div",{className:"text-3xl font-black mb-2",style:{color:"#191970"},children:eg.late})})]}),(0,a.jsxs)(o.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-red-500 to-red-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-red-500 to-red-600 p-1 rounded-t-xl"}),(0,a.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,a.jsx)(o.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"غير ملتزم"}),(0,a.jsx)("div",{className:"p-3 rounded-2xl shadow-lg",style:{backgroundColor:"#dc3545"},children:(0,a.jsx)(g.A,{className:"h-5 w-5 text-white"})})]}),(0,a.jsx)(o.Wu,{className:"relative z-10",children:(0,a.jsx)("div",{className:"text-3xl font-black mb-2",style:{color:"#191970"},children:eg.inactive})})]}),(0,a.jsxs)(o.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-purple-500 to-purple-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-purple-600 p-1 rounded-t-xl"}),(0,a.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,a.jsx)(o.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"موقوف"}),(0,a.jsx)("div",{className:"p-3 rounded-2xl shadow-lg",style:{backgroundColor:"#800020"},children:(0,a.jsx)("span",{className:"text-white text-sm font-bold",children:"⏸️"})})]}),(0,a.jsx)(o.Wu,{className:"relative z-10",children:(0,a.jsx)("div",{className:"text-3xl font-black mb-2",style:{color:"#191970"},children:eg.suspended})})]}),(0,a.jsxs)(o.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-gray-500 to-gray-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-gray-500 to-gray-600 p-1 rounded-t-xl"}),(0,a.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,a.jsx)(o.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"مؤرشف"}),(0,a.jsx)("div",{className:"p-3 rounded-2xl shadow-lg",style:{backgroundColor:"#6c757d"},children:(0,a.jsx)("span",{className:"text-white text-sm font-bold",children:"\uD83D\uDCC1"})})]}),(0,a.jsx)(o.Wu,{className:"relative z-10",children:(0,a.jsx)("div",{className:"text-3xl font-black mb-2",style:{color:"#191970"},children:eg.archived})})]})]}),(0,a.jsx)(o.Zp,{className:"border-0 shadow-xl bg-white/80 backdrop-blur-sm",children:(0,a.jsx)(o.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(u.A,{className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5"}),(0,a.jsx)(i.p,{placeholder:"البحث في الأعضاء بالاسم أو الهاتف...",value:z,onChange:e=>T(e.target.value),className:"pr-12 h-12 text-base shadow-md"})]}),(0,a.jsx)(em,{value:k,onChange:e=>D(e.target.value),allLabel:"جميع الأعضاء",className:"shadow-md h-12"}),(0,a.jsxs)(l.$,{variant:"info",onClick:()=>$(!0),className:"shadow-lg h-12",children:[(0,a.jsx)(b.A,{className:"w-4 h-4 ml-2"}),"بحث متقدم"]}),(0,a.jsxs)(l.$,{variant:"accent",onClick:()=>{X(!0)},className:"shadow-lg h-12",children:[(0,a.jsx)(j.A,{className:"w-4 h-4 ml-2"}),"كشف حساب"]})]})})}),(0,a.jsx)(o.Zp,{className:"border-0 shadow-xl bg-white/90 backdrop-blur-sm overflow-hidden",children:(0,a.jsx)(o.Wu,{className:"p-0",children:E?(0,a.jsxs)("div",{className:"flex flex-col justify-center items-center h-64 bg-gradient-to-br from-slate-50 to-slate-100",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-slate-500 mb-4"}),(0,a.jsx)("div",{className:"text-slate-600 font-medium",children:"جاري تحميل بيانات الأعضاء..."})]}):0===s.length?(0,a.jsxs)("div",{className:"flex flex-col justify-center items-center h-64 bg-gradient-to-br from-slate-50 to-slate-100",children:[(0,a.jsx)(c.A,{className:"h-16 w-16 text-slate-300 mb-4"}),(0,a.jsx)("div",{className:"text-slate-600 font-medium",children:"لا توجد أعضاء مطابقة للبحث"})]}):(0,a.jsxs)(d.XI,{children:[(0,a.jsx)(d.A0,{children:(0,a.jsxs)(d.Hj,{style:{backgroundColor:"#191970"},children:[(0,a.jsx)(d.nd,{style:{backgroundColor:"#191970",color:"white",fontWeight:"600"},children:"الاسم"}),(0,a.jsx)(d.nd,{style:{backgroundColor:"#191970",color:"white",fontWeight:"600"},children:"الهاتف"}),(0,a.jsx)(d.nd,{style:{backgroundColor:"#191970",color:"white",fontWeight:"600"},children:"الحالة"}),(0,a.jsx)(d.nd,{style:{backgroundColor:"#191970",color:"white",fontWeight:"600"},children:"إجمالي الإيرادات"}),(0,a.jsx)(d.nd,{style:{backgroundColor:"#191970",color:"white",fontWeight:"600"},children:"تاريخ الإضافة"}),(0,a.jsx)(d.nd,{style:{backgroundColor:"#191970",color:"white",fontWeight:"600"},children:"الإجراءات"})]})}),(0,a.jsx)(d.BF,{children:s.map(e=>{var s;return(0,a.jsxs)(d.Hj,{children:[(0,a.jsx)(d.nA,{children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),e.address&&(0,a.jsxs)("div",{className:"text-sm text-gray-500 flex items-center mt-1",children:[(0,a.jsx)(f.A,{className:"w-3 h-3 ml-1"}),e.address]})]})}),(0,a.jsx)(d.nA,{children:e.phone&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(v.A,{className:"w-3 h-3 ml-1"}),e.phone]})}),(0,a.jsx)(d.nA,{children:eL?(0,a.jsxs)("select",{value:e.status,onChange:s=>eB(e,s.target.value),className:"px-2 py-1 text-xs font-semibold rounded-full border-0 cursor-pointer ".concat((0,G.OR)(e.status)),children:[(0,a.jsx)("option",{value:"ACTIVE",children:"✅ نشط"}),(0,a.jsx)("option",{value:"LATE",children:"⏰ متأخر"}),(0,a.jsx)("option",{value:"INACTIVE",children:"❌ غير ملتزم"}),(0,a.jsx)("option",{value:"SUSPENDED",children:"⏸️ موقوف مؤقتاً"}),(0,a.jsx)("option",{value:"ARCHIVED",children:"\uD83D\uDCC1 مؤرشف"})]}):(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat((0,G.OR)(e.status)),children:(0,G.WK)(e.status)})}),(0,a.jsx)(d.nA,{children:(0,G.vv)((null==(s=e.incomes)?void 0:s.reduce((e,s)=>e+s.amount,0))||0)}),(0,a.jsx)(d.nA,{children:(0,G.Yq)(e.createdAt)}),(0,a.jsx)(d.nA,{children:(0,a.jsxs)("div",{className:"flex items-center space-x-1 space-x-reverse",children:[(0,a.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>ev(e),className:"hover:bg-blue-50 border",style:{color:"#0056cc",backgroundColor:"rgba(0, 86, 204, 0.1)",borderColor:"rgba(0, 86, 204, 0.2)",fontWeight:"600"},title:"عرض التفاصيل",children:(0,a.jsx)(N.A,{className:"w-4 h-4"})}),(0,a.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>eN(e.id),className:"hover:bg-purple-50 border",style:{color:"#800020",backgroundColor:"rgba(128, 0, 32, 0.1)",borderColor:"rgba(128, 0, 32, 0.2)",fontWeight:"600"},title:"كشف الحساب",children:(0,a.jsx)(j.A,{className:"w-4 h-4"})}),eL&&(0,a.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>ew(e),className:"hover:bg-yellow-50 border",style:{color:"#b8860b",backgroundColor:"rgba(184, 134, 11, 0.1)",borderColor:"rgba(184, 134, 11, 0.2)",fontWeight:"600"},title:"إضافة إيراد",children:(0,a.jsx)(y.A,{className:"w-4 h-4"})}),eL&&(0,a.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>ef(e),className:"hover:bg-green-50 border",style:{color:"#1e7e34",backgroundColor:"rgba(30, 126, 52, 0.1)",borderColor:"rgba(30, 126, 52, 0.2)",fontWeight:"600"},title:"تعديل",children:(0,a.jsx)(w.A,{className:"w-4 h-4"})}),eL&&(0,a.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>ey(e),className:"hover:bg-purple-50 border",style:{color:"#6f42c1",backgroundColor:"rgba(111, 66, 193, 0.1)",borderColor:"rgba(111, 66, 193, 0.2)",fontWeight:"600"},title:"إدارة كلمة المرور",children:(0,a.jsx)(A.A,{className:"w-4 h-4"})}),eZ&&(0,a.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>eA(e.id),className:"hover:bg-red-50 border",style:{color:"#c82333",backgroundColor:"rgba(200, 35, 51, 0.1)",borderColor:"rgba(200, 35, 51, 0.2)",fontWeight:"600"},title:"حذف",children:(0,a.jsx)(C.A,{className:"w-4 h-4"})})]})})]},e.id)})})]})})}),I.pages>1&&(0,a.jsxs)("div",{className:"flex justify-center space-x-2 space-x-reverse",children:[(0,a.jsx)(l.$,{variant:"outline",disabled:1===I.page,onClick:()=>R(e=>({...e,page:e.page-1})),className:"border-2",style:{borderColor:"#007bff",color:1===I.page?"#6c757d":"#007bff",backgroundColor:"white"},children:"السابق"}),(0,a.jsxs)("span",{className:"flex items-center px-4",style:{color:"#333333"},children:["صفحة ",I.page," من ",I.pages]}),(0,a.jsx)(l.$,{variant:"outline",disabled:I.page===I.pages,onClick:()=>R(e=>({...e,page:e.page+1})),className:"border-2",style:{borderColor:"#007bff",color:I.page===I.pages?"#6c757d":"#007bff",backgroundColor:"white"},children:"التالي"})]}),(0,a.jsx)(M,{open:F,onOpenChange:O,member:W,onSuccess:eC}),(0,a.jsx)(q,{open:V,onOpenChange:B,member:L}),(0,a.jsx)(ei,{open:J,onOpenChange:$,onSearch:e=>{let s="";e.name&&(s+=e.name+" "),e.phone&&(s+=e.phone+" "),e.address&&(s+=e.address+" "),T(s.trim()),D(e.status),R(e=>({...e,page:1}))},onReset:()=>{T(""),D("all"),R(e=>({...e,page:1}))}}),(0,a.jsx)(es,{open:U,onOpenChange:H,memberId:_}),(0,a.jsx)(ea,{open:Q,onOpenChange:ee,member:ec,onSuccess:eC}),(0,a.jsx)(eo.A,{open:K,onOpenChange:X,onSelectMember:e=>{Y(e),X(!1),H(!0)},title:"اختيار عضو لكشف الحساب",description:"ابحث عن العضو المطلوب لعرض كشف حسابه"}),(0,a.jsx)(el,{open:en,onOpenChange:ed,member:et,onSuccess:eC})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[4316,3930,1778,2108,3942,5217,8130,913,1070,4209,3068,8441,1684,7358],()=>s(71450)),_N_E=e.O()}]);