import { NextResponse } from 'next/server'
import { cookies } from 'next/headers'

export async function POST() {
  try {
    const response = NextResponse.json({ success: true, message: 'تم مسح الجلسة بنجاح' })
    
    // مسح جميع الكوكيز المتعلقة بـ NextAuth
    const cookieStore = await cookies()
    const allCookies = cookieStore.getAll()

    allCookies.forEach((cookie: { name: string; value: string }) => {
      if (cookie.name.includes('next-auth') || cookie.name.includes('__Secure-next-auth')) {
        response.cookies.set(cookie.name, '', {
          expires: new Date(0),
          path: '/',
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax'
        })
      }
    })

    // مسح كوكيز إضافية
    const cookiesToClear = [
      'next-auth.session-token',
      'next-auth.csrf-token',
      'next-auth.callback-url',
      '__Secure-next-auth.session-token',
      '__Secure-next-auth.csrf-token',
      '__Secure-next-auth.callback-url'
    ]

    cookiesToClear.forEach(cookieName => {
      response.cookies.set(cookieName, '', {
        expires: new Date(0),
        path: '/',
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax'
      })
    })

    return response
  } catch (error) {
    console.error('خطأ في مسح الجلسة:', error)
    return NextResponse.json(
      { success: false, message: 'حدث خطأ في مسح الجلسة' },
      { status: 500 }
    )
  }
}
