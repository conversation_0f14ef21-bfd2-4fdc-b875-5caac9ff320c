"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9908],{9428:(e,n,r)=>{r.d(n,{A:()=>t});let t=(0,r(19946).A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},13052:(e,n,r)=>{r.d(n,{A:()=>t});let t=(0,r(19946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},28905:(e,n,r)=>{r.d(n,{C:()=>u});var t=r(12115),o=r(6101),a=r(52712),u=e=>{let{present:n,children:r}=e,u=function(e){var n,r;let[o,u]=t.useState(),i=t.useRef(null),s=t.useRef(e),d=t.useRef("none"),[c,p]=(n=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},t.useReducer((e,n)=>{let t=r[e][n];return null!=t?t:e},n));return t.useEffect(()=>{let e=l(i.current);d.current="mounted"===c?e:"none"},[c]),(0,a.N)(()=>{let n=i.current,r=s.current;if(r!==e){let t=d.current,o=l(n);e?p("MOUNT"):"none"===o||(null==n?void 0:n.display)==="none"?p("UNMOUNT"):r&&t!==o?p("ANIMATION_OUT"):p("UNMOUNT"),s.current=e}},[e,p]),(0,a.N)(()=>{if(o){var e;let n,r=null!=(e=o.ownerDocument.defaultView)?e:window,t=e=>{let t=l(i.current).includes(e.animationName);if(e.target===o&&t&&(p("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",n=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(d.current=l(i.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",t),o.addEventListener("animationend",t),()=>{r.clearTimeout(n),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",t),o.removeEventListener("animationend",t)}}p("ANIMATION_END")},[o,p]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:t.useCallback(e=>{i.current=e?getComputedStyle(e):null,u(e)},[])}}(n),i="function"==typeof r?r({present:u.isPresent}):t.Children.only(r),s=(0,o.s)(u.ref,function(e){var n,r;let t=null==(n=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:n.get,o=t&&"isReactWarning"in t&&t.isReactWarning;return o?e.ref:(o=(t=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof r||u.isPresent?t.cloneElement(i,{ref:s}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}u.displayName="Presence"},48698:(e,n,r)=>{r.d(n,{H_:()=>e2,UC:()=>e9,YJ:()=>e8,q7:()=>e3,VF:()=>ne,JU:()=>e6,ZL:()=>e5,z6:()=>e4,hN:()=>e7,bL:()=>e0,wv:()=>nn,Pb:()=>nr,G5:()=>no,ZP:()=>nt,l9:()=>e1});var t=r(12115),o=r(85185),a=r(6101),u=r(46081),l=r(5845),i=r(63655),s=r(37328),d=r(94315),c=r(19178),p=r(92293),f=r(25519),m=r(61285),v=r(38795),h=r(34378),g=r(28905),w=r(89196),y=r(99708),x=r(39033),b=r(38168),M=r(93795),C=r(95155),R=["Enter"," "],j=["ArrowUp","PageDown","End"],D=["ArrowDown","PageUp","Home",...j],N={ltr:[...R,"ArrowRight"],rtl:[...R,"ArrowLeft"]},_={ltr:["ArrowLeft"],rtl:["ArrowRight"]},k="Menu",[I,T,E]=(0,s.N)(k),[P,A]=(0,u.A)(k,[E,v.Bk,w.RG]),O=(0,v.Bk)(),S=(0,w.RG)(),[L,F]=P(k),[G,K]=P(k),U=e=>{let{__scopeMenu:n,open:r=!1,children:o,dir:a,onOpenChange:u,modal:l=!0}=e,i=O(n),[s,c]=t.useState(null),p=t.useRef(!1),f=(0,x.c)(u),m=(0,d.jH)(a);return t.useEffect(()=>{let e=()=>{p.current=!0,document.addEventListener("pointerdown",n,{capture:!0,once:!0}),document.addEventListener("pointermove",n,{capture:!0,once:!0})},n=()=>p.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",n,{capture:!0}),document.removeEventListener("pointermove",n,{capture:!0})}},[]),(0,C.jsx)(v.bL,{...i,children:(0,C.jsx)(L,{scope:n,open:r,onOpenChange:f,content:s,onContentChange:c,children:(0,C.jsx)(G,{scope:n,onClose:t.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:p,dir:m,modal:l,children:o})})})};U.displayName=k;var B=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e,o=O(r);return(0,C.jsx)(v.Mz,{...o,...t,ref:n})});B.displayName="MenuAnchor";var V="MenuPortal",[q,W]=P(V,{forceMount:void 0}),H=e=>{let{__scopeMenu:n,forceMount:r,children:t,container:o}=e,a=F(V,n);return(0,C.jsx)(q,{scope:n,forceMount:r,children:(0,C.jsx)(g.C,{present:r||a.open,children:(0,C.jsx)(h.Z,{asChild:!0,container:o,children:t})})})};H.displayName=V;var X="MenuContent",[z,Z]=P(X),Y=t.forwardRef((e,n)=>{let r=W(X,e.__scopeMenu),{forceMount:t=r.forceMount,...o}=e,a=F(X,e.__scopeMenu),u=K(X,e.__scopeMenu);return(0,C.jsx)(I.Provider,{scope:e.__scopeMenu,children:(0,C.jsx)(g.C,{present:t||a.open,children:(0,C.jsx)(I.Slot,{scope:e.__scopeMenu,children:u.modal?(0,C.jsx)(J,{...o,ref:n}):(0,C.jsx)(Q,{...o,ref:n})})})})}),J=t.forwardRef((e,n)=>{let r=F(X,e.__scopeMenu),u=t.useRef(null),l=(0,a.s)(n,u);return t.useEffect(()=>{let e=u.current;if(e)return(0,b.Eq)(e)},[]),(0,C.jsx)(ee,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),Q=t.forwardRef((e,n)=>{let r=F(X,e.__scopeMenu);return(0,C.jsx)(ee,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),$=(0,y.TL)("MenuContent.ScrollLock"),ee=t.forwardRef((e,n)=>{let{__scopeMenu:r,loop:u=!1,trapFocus:l,onOpenAutoFocus:i,onCloseAutoFocus:s,disableOutsidePointerEvents:d,onEntryFocus:m,onEscapeKeyDown:h,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:x,onDismiss:b,disableOutsideScroll:R,...N}=e,_=F(X,r),k=K(X,r),I=O(r),E=S(r),P=T(r),[A,L]=t.useState(null),G=t.useRef(null),U=(0,a.s)(n,G,_.onContentChange),B=t.useRef(0),V=t.useRef(""),q=t.useRef(0),W=t.useRef(null),H=t.useRef("right"),Z=t.useRef(0),Y=R?M.A:t.Fragment,J=e=>{var n,r;let t=V.current+e,o=P().filter(e=>!e.disabled),a=document.activeElement,u=null==(n=o.find(e=>e.ref.current===a))?void 0:n.textValue,l=function(e,n,r){var t;let o=n.length>1&&Array.from(n).every(e=>e===n[0])?n[0]:n,a=r?e.indexOf(r):-1,u=(t=Math.max(a,0),e.map((n,r)=>e[(t+r)%e.length]));1===o.length&&(u=u.filter(e=>e!==r));let l=u.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==r?l:void 0}(o.map(e=>e.textValue),t,u),i=null==(r=o.find(e=>e.textValue===l))?void 0:r.ref.current;!function e(n){V.current=n,window.clearTimeout(B.current),""!==n&&(B.current=window.setTimeout(()=>e(""),1e3))}(t),i&&setTimeout(()=>i.focus())};t.useEffect(()=>()=>window.clearTimeout(B.current),[]),(0,p.Oh)();let Q=t.useCallback(e=>{var n,r;return H.current===(null==(n=W.current)?void 0:n.side)&&function(e,n){return!!n&&function(e,n){let{x:r,y:t}=e,o=!1;for(let e=0,a=n.length-1;e<n.length;a=e++){let u=n[e],l=n[a],i=u.x,s=u.y,d=l.x,c=l.y;s>t!=c>t&&r<(d-i)*(t-s)/(c-s)+i&&(o=!o)}return o}({x:e.clientX,y:e.clientY},n)}(e,null==(r=W.current)?void 0:r.area)},[]);return(0,C.jsx)(z,{scope:r,searchRef:V,onItemEnter:t.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:t.useCallback(e=>{var n;Q(e)||(null==(n=G.current)||n.focus(),L(null))},[Q]),onTriggerLeave:t.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:q,onPointerGraceIntentChange:t.useCallback(e=>{W.current=e},[]),children:(0,C.jsx)(Y,{...R?{as:$,allowPinchZoom:!0}:void 0,children:(0,C.jsx)(f.n,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.m)(i,e=>{var n;e.preventDefault(),null==(n=G.current)||n.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,C.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:h,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:x,onDismiss:b,children:(0,C.jsx)(w.bL,{asChild:!0,...E,dir:k.dir,orientation:"vertical",loop:u,currentTabStopId:A,onCurrentTabStopIdChange:L,onEntryFocus:(0,o.m)(m,e=>{k.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,C.jsx)(v.UC,{role:"menu","aria-orientation":"vertical","data-state":e_(_.open),"data-radix-menu-content":"",dir:k.dir,...I,...N,ref:U,style:{outline:"none",...N.style},onKeyDown:(0,o.m)(N.onKeyDown,e=>{let n=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,t=1===e.key.length;n&&("Tab"===e.key&&e.preventDefault(),!r&&t&&J(e.key));let o=G.current;if(e.target!==o||!D.includes(e.key))return;e.preventDefault();let a=P().filter(e=>!e.disabled).map(e=>e.ref.current);j.includes(e.key)&&a.reverse(),function(e){let n=document.activeElement;for(let r of e)if(r===n||(r.focus(),document.activeElement!==n))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(B.current),V.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eT(e=>{let n=e.target,r=Z.current!==e.clientX;e.currentTarget.contains(n)&&r&&(H.current=e.clientX>Z.current?"right":"left",Z.current=e.clientX)}))})})})})})})});Y.displayName=X;var en=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,C.jsx)(i.sG.div,{role:"group",...t,ref:n})});en.displayName="MenuGroup";var er=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,C.jsx)(i.sG.div,{...t,ref:n})});er.displayName="MenuLabel";var et="MenuItem",eo="menu.itemSelect",ea=t.forwardRef((e,n)=>{let{disabled:r=!1,onSelect:u,...l}=e,s=t.useRef(null),d=K(et,e.__scopeMenu),c=Z(et,e.__scopeMenu),p=(0,a.s)(n,s),f=t.useRef(!1);return(0,C.jsx)(eu,{...l,ref:p,disabled:r,onClick:(0,o.m)(e.onClick,()=>{let e=s.current;if(!r&&e){let n=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>null==u?void 0:u(e),{once:!0}),(0,i.hO)(e,n),n.defaultPrevented?f.current=!1:d.onClose()}}),onPointerDown:n=>{var r;null==(r=e.onPointerDown)||r.call(e,n),f.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var n;f.current||null==(n=e.currentTarget)||n.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let n=""!==c.searchRef.current;r||n&&" "===e.key||R.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=et;var eu=t.forwardRef((e,n)=>{let{__scopeMenu:r,disabled:u=!1,textValue:l,...s}=e,d=Z(et,r),c=S(r),p=t.useRef(null),f=(0,a.s)(n,p),[m,v]=t.useState(!1),[h,g]=t.useState("");return t.useEffect(()=>{let e=p.current;if(e){var n;g((null!=(n=e.textContent)?n:"").trim())}},[s.children]),(0,C.jsx)(I.ItemSlot,{scope:r,disabled:u,textValue:null!=l?l:h,children:(0,C.jsx)(w.q7,{asChild:!0,...c,focusable:!u,children:(0,C.jsx)(i.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":u||void 0,"data-disabled":u?"":void 0,...s,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,eT(e=>{u?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eT(e=>d.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>v(!0)),onBlur:(0,o.m)(e.onBlur,()=>v(!1))})})})}),el=t.forwardRef((e,n)=>{let{checked:r=!1,onCheckedChange:t,...a}=e;return(0,C.jsx)(ev,{scope:e.__scopeMenu,checked:r,children:(0,C.jsx)(ea,{role:"menuitemcheckbox","aria-checked":ek(r)?"mixed":r,...a,ref:n,"data-state":eI(r),onSelect:(0,o.m)(a.onSelect,()=>null==t?void 0:t(!!ek(r)||!r),{checkForDefaultPrevented:!1})})})});el.displayName="MenuCheckboxItem";var ei="MenuRadioGroup",[es,ed]=P(ei,{value:void 0,onValueChange:()=>{}}),ec=t.forwardRef((e,n)=>{let{value:r,onValueChange:t,...o}=e,a=(0,x.c)(t);return(0,C.jsx)(es,{scope:e.__scopeMenu,value:r,onValueChange:a,children:(0,C.jsx)(en,{...o,ref:n})})});ec.displayName=ei;var ep="MenuRadioItem",ef=t.forwardRef((e,n)=>{let{value:r,...t}=e,a=ed(ep,e.__scopeMenu),u=r===a.value;return(0,C.jsx)(ev,{scope:e.__scopeMenu,checked:u,children:(0,C.jsx)(ea,{role:"menuitemradio","aria-checked":u,...t,ref:n,"data-state":eI(u),onSelect:(0,o.m)(t.onSelect,()=>{var e;return null==(e=a.onValueChange)?void 0:e.call(a,r)},{checkForDefaultPrevented:!1})})})});ef.displayName=ep;var em="MenuItemIndicator",[ev,eh]=P(em,{checked:!1}),eg=t.forwardRef((e,n)=>{let{__scopeMenu:r,forceMount:t,...o}=e,a=eh(em,r);return(0,C.jsx)(g.C,{present:t||ek(a.checked)||!0===a.checked,children:(0,C.jsx)(i.sG.span,{...o,ref:n,"data-state":eI(a.checked)})})});eg.displayName=em;var ew=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,C.jsx)(i.sG.div,{role:"separator","aria-orientation":"horizontal",...t,ref:n})});ew.displayName="MenuSeparator";var ey=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e,o=O(r);return(0,C.jsx)(v.i3,{...o,...t,ref:n})});ey.displayName="MenuArrow";var ex="MenuSub",[eb,eM]=P(ex),eC=e=>{let{__scopeMenu:n,children:r,open:o=!1,onOpenChange:a}=e,u=F(ex,n),l=O(n),[i,s]=t.useState(null),[d,c]=t.useState(null),p=(0,x.c)(a);return t.useEffect(()=>(!1===u.open&&p(!1),()=>p(!1)),[u.open,p]),(0,C.jsx)(v.bL,{...l,children:(0,C.jsx)(L,{scope:n,open:o,onOpenChange:p,content:d,onContentChange:c,children:(0,C.jsx)(eb,{scope:n,contentId:(0,m.B)(),triggerId:(0,m.B)(),trigger:i,onTriggerChange:s,children:r})})})};eC.displayName=ex;var eR="MenuSubTrigger",ej=t.forwardRef((e,n)=>{let r=F(eR,e.__scopeMenu),u=K(eR,e.__scopeMenu),l=eM(eR,e.__scopeMenu),i=Z(eR,e.__scopeMenu),s=t.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:c}=i,p={__scopeMenu:e.__scopeMenu},f=t.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return t.useEffect(()=>f,[f]),t.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),c(null)}},[d,c]),(0,C.jsx)(B,{asChild:!0,...p,children:(0,C.jsx)(eu,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":l.contentId,"data-state":e_(r.open),...e,ref:(0,a.t)(n,l.onTriggerChange),onClick:n=>{var t;null==(t=e.onClick)||t.call(e,n),e.disabled||n.defaultPrevented||(n.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eT(n=>{i.onItemEnter(n),!n.defaultPrevented&&(e.disabled||r.open||s.current||(i.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{r.onOpenChange(!0),f()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eT(e=>{var n,t;f();let o=null==(n=r.content)?void 0:n.getBoundingClientRect();if(o){let n=null==(t=r.content)?void 0:t.dataset.side,a="right"===n,u=o[a?"left":"right"],l=o[a?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:u,y:o.top},{x:l,y:o.top},{x:l,y:o.bottom},{x:u,y:o.bottom}],side:n}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,n=>{let t=""!==i.searchRef.current;if(!e.disabled&&(!t||" "!==n.key)&&N[u.dir].includes(n.key)){var o;r.onOpenChange(!0),null==(o=r.content)||o.focus(),n.preventDefault()}})})})});ej.displayName=eR;var eD="MenuSubContent",eN=t.forwardRef((e,n)=>{let r=W(X,e.__scopeMenu),{forceMount:u=r.forceMount,...l}=e,i=F(X,e.__scopeMenu),s=K(X,e.__scopeMenu),d=eM(eD,e.__scopeMenu),c=t.useRef(null),p=(0,a.s)(n,c);return(0,C.jsx)(I.Provider,{scope:e.__scopeMenu,children:(0,C.jsx)(g.C,{present:u||i.open,children:(0,C.jsx)(I.Slot,{scope:e.__scopeMenu,children:(0,C.jsx)(ee,{id:d.contentId,"aria-labelledby":d.triggerId,...l,ref:p,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var n;s.isUsingKeyboardRef.current&&(null==(n=c.current)||n.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==d.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let n=e.currentTarget.contains(e.target),r=_[s.dir].includes(e.key);if(n&&r){var t;i.onOpenChange(!1),null==(t=d.trigger)||t.focus(),e.preventDefault()}})})})})})});function e_(e){return e?"open":"closed"}function ek(e){return"indeterminate"===e}function eI(e){return ek(e)?"indeterminate":e?"checked":"unchecked"}function eT(e){return n=>"mouse"===n.pointerType?e(n):void 0}eN.displayName=eD;var eE="DropdownMenu",[eP,eA]=(0,u.A)(eE,[A]),eO=A(),[eS,eL]=eP(eE),eF=e=>{let{__scopeDropdownMenu:n,children:r,dir:o,open:a,defaultOpen:u,onOpenChange:i,modal:s=!0}=e,d=eO(n),c=t.useRef(null),[p,f]=(0,l.i)({prop:a,defaultProp:null!=u&&u,onChange:i,caller:eE});return(0,C.jsx)(eS,{scope:n,triggerId:(0,m.B)(),triggerRef:c,contentId:(0,m.B)(),open:p,onOpenChange:f,onOpenToggle:t.useCallback(()=>f(e=>!e),[f]),modal:s,children:(0,C.jsx)(U,{...d,open:p,onOpenChange:f,dir:o,modal:s,children:r})})};eF.displayName=eE;var eG="DropdownMenuTrigger",eK=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,disabled:t=!1,...u}=e,l=eL(eG,r),s=eO(r);return(0,C.jsx)(B,{asChild:!0,...s,children:(0,C.jsx)(i.sG.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":t?"":void 0,disabled:t,...u,ref:(0,a.t)(n,l.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!t&&0===e.button&&!1===e.ctrlKey&&(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!t&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eK.displayName=eG;var eU=e=>{let{__scopeDropdownMenu:n,...r}=e,t=eO(n);return(0,C.jsx)(H,{...t,...r})};eU.displayName="DropdownMenuPortal";var eB="DropdownMenuContent",eV=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...a}=e,u=eL(eB,r),l=eO(r),i=t.useRef(!1);return(0,C.jsx)(Y,{id:u.contentId,"aria-labelledby":u.triggerId,...l,...a,ref:n,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var n;i.current||null==(n=u.triggerRef.current)||n.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let n=e.detail.originalEvent,r=0===n.button&&!0===n.ctrlKey,t=2===n.button||r;(!u.modal||t)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eV.displayName=eB;var eq=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,C.jsx)(en,{...o,...t,ref:n})});eq.displayName="DropdownMenuGroup";var eW=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,C.jsx)(er,{...o,...t,ref:n})});eW.displayName="DropdownMenuLabel";var eH=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,C.jsx)(ea,{...o,...t,ref:n})});eH.displayName="DropdownMenuItem";var eX=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,C.jsx)(el,{...o,...t,ref:n})});eX.displayName="DropdownMenuCheckboxItem";var ez=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,C.jsx)(ec,{...o,...t,ref:n})});ez.displayName="DropdownMenuRadioGroup";var eZ=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,C.jsx)(ef,{...o,...t,ref:n})});eZ.displayName="DropdownMenuRadioItem";var eY=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,C.jsx)(eg,{...o,...t,ref:n})});eY.displayName="DropdownMenuItemIndicator";var eJ=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,C.jsx)(ew,{...o,...t,ref:n})});eJ.displayName="DropdownMenuSeparator",t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,C.jsx)(ey,{...o,...t,ref:n})}).displayName="DropdownMenuArrow";var eQ=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,C.jsx)(ej,{...o,...t,ref:n})});eQ.displayName="DropdownMenuSubTrigger";var e$=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,C.jsx)(eN,{...o,...t,ref:n,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e$.displayName="DropdownMenuSubContent";var e0=eF,e1=eK,e5=eU,e9=eV,e8=eq,e6=eW,e3=eH,e2=eX,e4=ez,e7=eZ,ne=eY,nn=eJ,nr=e=>{let{__scopeDropdownMenu:n,children:r,open:t,onOpenChange:o,defaultOpen:a}=e,u=eO(n),[i,s]=(0,l.i)({prop:t,defaultProp:null!=a&&a,onChange:o,caller:"DropdownMenuSub"});return(0,C.jsx)(eC,{...u,open:i,onOpenChange:s,children:r})},nt=eQ,no=e$},74783:(e,n,r)=>{r.d(n,{A:()=>t});let t=(0,r(19946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},89196:(e,n,r)=>{r.d(n,{RG:()=>b,bL:()=>I,q7:()=>T});var t=r(12115),o=r(85185),a=r(37328),u=r(6101),l=r(46081),i=r(61285),s=r(63655),d=r(39033),c=r(5845),p=r(94315),f=r(95155),m="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[g,w,y]=(0,a.N)(h),[x,b]=(0,l.A)(h,[y]),[M,C]=x(h),R=t.forwardRef((e,n)=>(0,f.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(j,{...e,ref:n})})}));R.displayName=h;var j=t.forwardRef((e,n)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:l=!1,dir:i,currentTabStopId:g,defaultCurrentTabStopId:y,onCurrentTabStopIdChange:x,onEntryFocus:b,preventScrollOnEntryFocus:C=!1,...R}=e,j=t.useRef(null),D=(0,u.s)(n,j),N=(0,p.jH)(i),[_,I]=(0,c.i)({prop:g,defaultProp:null!=y?y:null,onChange:x,caller:h}),[T,E]=t.useState(!1),P=(0,d.c)(b),A=w(r),O=t.useRef(!1),[S,L]=t.useState(0);return t.useEffect(()=>{let e=j.current;if(e)return e.addEventListener(m,P),()=>e.removeEventListener(m,P)},[P]),(0,f.jsx)(M,{scope:r,orientation:a,dir:N,loop:l,currentTabStopId:_,onItemFocus:t.useCallback(e=>I(e),[I]),onItemShiftTab:t.useCallback(()=>E(!0),[]),onFocusableItemAdd:t.useCallback(()=>L(e=>e+1),[]),onFocusableItemRemove:t.useCallback(()=>L(e=>e-1),[]),children:(0,f.jsx)(s.sG.div,{tabIndex:T||0===S?-1:0,"data-orientation":a,...R,ref:D,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{O.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let n=!O.current;if(e.target===e.currentTarget&&n&&!T){let n=new CustomEvent(m,v);if(e.currentTarget.dispatchEvent(n),!n.defaultPrevented){let e=A().filter(e=>e.focusable);k([e.find(e=>e.active),e.find(e=>e.id===_),...e].filter(Boolean).map(e=>e.ref.current),C)}}O.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>E(!1))})})}),D="RovingFocusGroupItem",N=t.forwardRef((e,n)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:u=!1,tabStopId:l,children:d,...c}=e,p=(0,i.B)(),m=l||p,v=C(D,r),h=v.currentTabStopId===m,y=w(r),{onFocusableItemAdd:x,onFocusableItemRemove:b,currentTabStopId:M}=v;return t.useEffect(()=>{if(a)return x(),()=>b()},[a,x,b]),(0,f.jsx)(g.ItemSlot,{scope:r,id:m,focusable:a,active:u,children:(0,f.jsx)(s.sG.span,{tabIndex:h?0:-1,"data-orientation":v.orientation,...c,ref:n,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?v.onItemFocus(m):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>v.onItemFocus(m)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void v.onItemShiftTab();if(e.target!==e.currentTarget)return;let n=function(e,n,r){var t;let o=(t=e.key,"rtl"!==r?t:"ArrowLeft"===t?"ArrowRight":"ArrowRight"===t?"ArrowLeft":t);if(!("vertical"===n&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===n&&["ArrowUp","ArrowDown"].includes(o)))return _[o]}(e,v.orientation,v.dir);if(void 0!==n){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===n)r.reverse();else if("prev"===n||"next"===n){"prev"===n&&r.reverse();let t=r.indexOf(e.currentTarget);r=v.loop?function(e,n){return e.map((r,t)=>e[(n+t)%e.length])}(r,t+1):r.slice(t+1)}setTimeout(()=>k(r))}}),children:"function"==typeof d?d({isCurrentTabStop:h,hasTabStop:null!=M}):d})})});N.displayName=D;var _={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function k(e){let n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let t of e)if(t===r||(t.focus({preventScroll:n}),document.activeElement!==r))return}var I=R,T=N}}]);