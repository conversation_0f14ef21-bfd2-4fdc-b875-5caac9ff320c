import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    const { id } = await params
    const { searchParams } = new URL(request.url)
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const year = searchParams.get('year')

    // التحقق من وجود العضو
    const member = await prisma.member.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        phone: true,
        email: true,
        address: true,
        photo: true,
        status: true,
        createdAt: true,
      },
    })

    if (!member) {
      return NextResponse.json({ error: 'العضو غير موجود' }, { status: 404 })
    }

    // تحديد فترة البحث
    let dateFilter: Record<string, unknown> = {}
    if (startDate && endDate) {
      dateFilter = {
        date: {
          gte: new Date(startDate),
          lte: new Date(endDate),
        },
      }
    } else if (year) {
      const yearNum = parseInt(year)
      dateFilter = {
        date: {
          gte: new Date(yearNum, 0, 1),
          lte: new Date(yearNum, 11, 31),
        },
      }
    }

    // جلب جميع الإيرادات للعضو
    const incomes = await prisma.income.findMany({
      where: {
        memberId: id,
        ...dateFilter,
      },
      orderBy: { date: 'desc' },
      include: {
        createdBy: {
          select: {
            name: true,
          },
        },
      },
    })

    // حساب الإحصائيات
    const totalAmount = incomes.reduce((sum, income) => sum + income.amount, 0)
    const transactionCount = incomes.length

    // تجميع البيانات حسب النوع
    const byType = incomes.reduce((acc, income) => {
      if (!acc[income.type]) {
        acc[income.type] = { count: 0, amount: 0 }
      }
      acc[income.type].count++
      acc[income.type].amount += income.amount
      return acc
    }, {} as Record<string, { count: number; amount: number }>)

    // تجميع البيانات حسب الشهر (للسنة الحالية أو المحددة)
    const currentYear = year ? parseInt(year) : new Date().getFullYear()
    const monthlyData = Array.from({ length: 12 }, (_, index) => {
      const month = index + 1
      const monthIncomes = incomes.filter(income => {
        const incomeDate = new Date(income.date)
        return incomeDate.getFullYear() === currentYear && incomeDate.getMonth() === index
      })
      
      return {
        month,
        monthName: getMonthName(month),
        count: monthIncomes.length,
        amount: monthIncomes.reduce((sum, income) => sum + income.amount, 0),
      }
    })

    // آخر 5 معاملات
    const recentTransactions = incomes.slice(0, 5)

    // إحصائيات إضافية
    const firstTransaction = incomes.length > 0 ? incomes[incomes.length - 1] : null
    const lastTransaction = incomes.length > 0 ? incomes[0] : null
    
    // متوسط المساهمة الشهرية
    const monthsWithTransactions = monthlyData.filter(month => month.count > 0).length
    const averageMonthlyContribution = monthsWithTransactions > 0 ? totalAmount / monthsWithTransactions : 0

    const accountStatement = {
      member,
      summary: {
        totalAmount,
        transactionCount,
        averageMonthlyContribution,
        firstTransactionDate: firstTransaction?.date || null,
        lastTransactionDate: lastTransaction?.date || null,
      },
      byType,
      monthlyData,
      recentTransactions,
      allTransactions: incomes,
      period: {
        startDate: startDate || `${currentYear}-01-01`,
        endDate: endDate || `${currentYear}-12-31`,
        year: currentYear,
      },
    }

    // console.log('إرسال كشف الحساب للعضو:', id, 'البيانات:', {
    //   memberName: member.name,
    //   totalAmount,
    //   transactionCount,
    //   incomesLength: incomes.length
    // })

    return NextResponse.json(accountStatement)
  } catch (error) {
    console.error('خطأ في جلب كشف الحساب:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في جلب كشف الحساب' },
      { status: 500 }
    )
  }
}

// دالة مساعدة للحصول على اسم الشهر
function getMonthName(month: number): string {
  return `شهر${month}`
}
