import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const expenseInputSchema = z.object({
  amount: z.number().positive('المبلغ يجب أن يكون أكبر من صفر'),
  date: z.string().transform((str) => new Date(str)),
  description: z.string().min(1, 'الوصف مطلوب'),
  category: z.enum(['MEETINGS', 'EVENTS', 'MAINTENANCE', 'SOCIAL', 'GENERAL']),
  recipient: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
})

// GET - جلب مصروف محدد
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    const { id } = await params
    const expense = await prisma.expense.findUnique({
      where: { id },
      include: {
        createdBy: {
          select: {
            name: true,
          },
        },
      },
    })

    if (!expense) {
      return NextResponse.json({ error: 'المصروف غير موجود' }, { status: 404 })
    }

    return NextResponse.json(expense)
  } catch (error) {
    console.error('خطأ في جلب المصروف:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في جلب المصروف' },
      { status: 500 }
    )
  }
}

// PUT - تحديث مصروف
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // التحقق من الصلاحيات
    if (session.user.role === 'VIEWER') {
      return NextResponse.json(
        { error: 'ليس لديك صلاحية لتعديل المصروفات' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { id } = await params

    // التحقق من صحة البيانات
    const validatedData = expenseInputSchema.parse(body)

    // التحقق من وجود المصروف
    const existingExpense = await prisma.expense.findUnique({
      where: { id },
    })

    if (!existingExpense) {
      return NextResponse.json({ error: 'المصروف غير موجود' }, { status: 404 })
    }

    // تحديث المصروف
    const updatedExpense = await prisma.expense.update({
      where: { id },
      data: validatedData,
      include: {
        createdBy: {
          select: {
            name: true,
          },
        },
      },
    })

    return NextResponse.json(updatedExpense)
  } catch (error: unknown) {
    console.error('خطأ في تحديث المصروف:', error)
    
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'بيانات غير صحيحة', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'حدث خطأ في تحديث المصروف' },
      { status: 500 }
    )
  }
}

// DELETE - حذف مصروف
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // التحقق من الصلاحيات - فقط المدير يمكنه الحذف
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'ليس لديك صلاحية لحذف المصروفات' },
        { status: 403 }
      )
    }

    const { id } = await params

    // التحقق من وجود المصروف
    const existingExpense = await prisma.expense.findUnique({
      where: { id },
    })

    if (!existingExpense) {
      return NextResponse.json({ error: 'المصروف غير موجود' }, { status: 404 })
    }

    // حذف المصروف
    await prisma.expense.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'تم حذف المصروف بنجاح' })
  } catch (error) {
    console.error('خطأ في حذف المصروف:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في حذف المصروف' },
      { status: 500 }
    )
  }
}
