(()=>{var e={};e.id=5196,e.ids=[5196],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>i});var s=r(13581),o=r(85663),a=r(31183);let i={providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"البريد الإلكتروني",type:"email"},password:{label:"كلمة المرور",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let t=await a.z.user.findUnique({where:{email:e.email}});return t&&await o.Ay.compare(e.password,t.password)?{id:t.id,email:t.email,name:t.name,role:t.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>o});var s=r(96330);let o=globalThis.prisma??new s.PrismaClient},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},51375:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>g,serverHooks:()=>w,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{GET:()=>x,POST:()=>y});var o=r(96559),a=r(48088),i=r(37719),n=r(32190),l=r(19854),u=r(12909),c=r(31183),p=r(45697);let d=p.z.object({title:p.z.string().min(1,"العنوان مطلوب"),description:p.z.string().optional(),location:p.z.string().optional(),startDate:p.z.string().optional(),endDate:p.z.string().optional()});async function x(){try{if(!await (0,l.getServerSession)(u.N))return n.NextResponse.json({error:"غير مصرح"},{status:401});let e=(await c.z.galleryFolder.findMany({include:{creator:{select:{name:!0}},photos:{take:1,orderBy:{createdAt:"desc"}},_count:{select:{photos:!0}}},orderBy:{createdAt:"desc"}})).map(e=>({id:e.id,title:e.title,description:e.description,photosCount:e._count.photos,coverPhoto:e.photos[0]||null,type:"folder",location:e.location,startDate:e.startDate,endDate:e.endDate,createdAt:e.createdAt,creator:e.creator}));return n.NextResponse.json({folders:e})}catch(e){return console.error("خطأ في جلب المجلدات:",e),n.NextResponse.json({error:"حدث خطأ في جلب المجلدات"},{status:500})}}async function y(e){try{let t=await (0,l.getServerSession)(u.N);if(!t)return n.NextResponse.json({error:"غير مصرح"},{status:401});if("VIEWER"===t.user.role)return n.NextResponse.json({error:"ليس لديك صلاحية لإنشاء مجلدات"},{status:403});let r=await e.json(),s=d.parse(r),o={title:s.title,description:s.description,location:s.location,createdBy:t.user.id};s.startDate&&(o.startDate=new Date(s.startDate)),s.endDate?o.endDate=new Date(s.endDate):s.startDate&&(o.endDate=new Date(s.startDate));let a=await c.z.galleryFolder.create({data:o,include:{creator:{select:{name:!0}},_count:{select:{photos:!0}}}}),i={id:a.id,title:a.title,description:a.description,photosCount:a._count.photos,coverPhoto:null,type:"folder",location:a.location,startDate:a.startDate,endDate:a.endDate,createdAt:a.createdAt,creator:a.creator};return n.NextResponse.json(i,{status:201})}catch(e){if(e instanceof p.z.ZodError)return n.NextResponse.json({error:"بيانات غير صحيحة",details:e.errors},{status:400});return console.error("خطأ في إنشاء المجلد:",e),n.NextResponse.json({error:"حدث خطأ في إنشاء المجلد"},{status:500})}}let g=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/gallery-folders/route",pathname:"/api/gallery-folders",filename:"route",bundlePath:"app/api/gallery-folders/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\gallery-folders\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:h,serverHooks:w}=g;function f(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:h})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,5663,4999,3412,580,5697],()=>r(51375));module.exports=s})();