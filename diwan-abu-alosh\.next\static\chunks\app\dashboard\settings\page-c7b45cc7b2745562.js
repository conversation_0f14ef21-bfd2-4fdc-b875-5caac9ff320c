(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4631],{5453:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>eS});var l=a(95155),i=a(12115),t=a(12108),c=a(17313),r=a(30285),n=a(26126),d=a(381),o=a(33127),x=a(23861),m=a(75525),h=a(54213),j=a(85339),p=a(40133),g=a(4229),u=a(81284),b=a(56671),v=a(62523),y=a(85057),N=a(88539),f=a(59409),w=a(59434);let C=i.forwardRef((e,s)=>{let{className:a,checked:i,onCheckedChange:t,...c}=e;return(0,l.jsx)("button",{ref:s,type:"button",role:"switch","aria-checked":i,onClick:()=>null==t?void 0:t(!i),className:(0,w.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50",i?"bg-primary":"bg-input",a),...c,children:(0,l.jsx)("span",{className:(0,w.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform",i?"translate-x-5":"translate-x-0")})})});C.displayName="Switch";var k=a(23227),S=a(34869),A=a(57434),E=a(69074);let J={diwanName:"ديوان آل أبو علوش",diwanDescription:"نظام إدارة ديوان آل أبو علوش",diwanAddress:"",diwanPhone:"",diwanEmail:"",diwanWebsite:"",defaultCurrency:"JOD",currencySymbol:"د.أ",timezone:"Asia/Amman",dateFormat:"DD/MM/YYYY",timeFormat:"24h",language:"ar",itemsPerPage:10,autoSave:!0,enableAuditLog:!0,sessionTimeout:30,showWelcomeMessage:!0,welcomeMessage:"مرحباً بك في ديوان آل أبو علوش",enableDashboardStats:!0,enableQuickActions:!0},R=[{value:"JOD",label:"دينار أردني (JOD)",symbol:"د.أ"},{value:"USD",label:"دولار أمريكي (USD)",symbol:"$"},{value:"EUR",label:"يورو (EUR)",symbol:"€"},{value:"SAR",label:"ريال سعودي (SAR)",symbol:"ر.س"},{value:"AED",label:"درهم إماراتي (AED)",symbol:"د.إ"},{value:"KWD",label:"دينار كويتي (KWD)",symbol:"د.ك"},{value:"QAR",label:"ريال قطري (QAR)",symbol:"ر.ق"},{value:"BHD",label:"دينار بحريني (BHD)",symbol:"د.ب"}],D=[{value:"Asia/Amman",label:"عمان (UTC+3)"},{value:"Asia/Riyadh",label:"الرياض (UTC+3)"},{value:"Asia/Dubai",label:"دبي (UTC+4)"},{value:"Asia/Kuwait",label:"الكويت (UTC+3)"},{value:"Asia/Qatar",label:"الدوحة (UTC+3)"},{value:"Asia/Bahrain",label:"المنامة (UTC+3)"}],M=[{value:"DD/MM/YYYY",label:"يوم/شهر/سنة (31/12/2024)"},{value:"MM/DD/YYYY",label:"شهر/يوم/سنة (12/31/2024)"},{value:"YYYY-MM-DD",label:"سنة-شهر-يوم (2024-12-31)"},{value:"DD-MM-YYYY",label:"يوم-شهر-سنة (31-12-2024)"}];function V(e){let{settings:s,onChange:a,canEdit:t}=e,[c,r]=(0,i.useState)(J);(0,i.useEffect)(()=>{s&&r({...J,...s})},[s]);let n=(e,s)=>{let l={...c,[e]:s};r(l),a(l)};return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"diwan-card",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,l.jsx)("div",{className:"p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg",children:(0,l.jsx)(k.A,{className:"w-5 h-5 text-white"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"معلومات الديوان الأساسية"})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"diwanName",children:"اسم الديوان"}),(0,l.jsx)(v.p,{id:"diwanName",value:c.diwanName,onChange:e=>n("diwanName",e.target.value),disabled:!t,placeholder:"اسم الديوان"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"diwanEmail",children:"البريد الإلكتروني"}),(0,l.jsx)(v.p,{id:"diwanEmail",type:"email",value:c.diwanEmail,onChange:e=>n("diwanEmail",e.target.value),disabled:!t,placeholder:"<EMAIL>"})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"diwanDescription",children:"وصف الديوان"}),(0,l.jsx)(N.T,{id:"diwanDescription",value:c.diwanDescription,onChange:e=>n("diwanDescription",e.target.value),disabled:!t,placeholder:"وصف مختصر عن الديوان",rows:3})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"diwanPhone",children:"رقم الهاتف"}),(0,l.jsx)(v.p,{id:"diwanPhone",value:c.diwanPhone,onChange:e=>n("diwanPhone",e.target.value),disabled:!t,placeholder:"+962 6 1234567"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"diwanWebsite",children:"الموقع الإلكتروني"}),(0,l.jsx)(v.p,{id:"diwanWebsite",value:c.diwanWebsite,onChange:e=>n("diwanWebsite",e.target.value),disabled:!t,placeholder:"https://example.com"})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"diwanAddress",children:"العنوان"}),(0,l.jsx)(N.T,{id:"diwanAddress",value:c.diwanAddress,onChange:e=>n("diwanAddress",e.target.value),disabled:!t,placeholder:"العنوان الكامل للديوان",rows:2})]})]})]}),(0,l.jsxs)("div",{className:"diwan-card",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,l.jsx)("div",{className:"p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg",children:(0,l.jsx)(S.A,{className:"w-5 h-5 text-white"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"الإعدادات الإقليمية"})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"العملة الافتراضية"}),(0,l.jsxs)(f.l6,{value:c.defaultCurrency,onValueChange:e=>{let s=R.find(s=>s.value===e);s&&(n("defaultCurrency",e),n("currencySymbol",s.symbol))},disabled:!t,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{placeholder:"اختر العملة"})}),(0,l.jsx)(f.gC,{children:R.map(e=>(0,l.jsx)(f.eb,{value:e.value,children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("span",{children:e.symbol}),(0,l.jsx)("span",{children:e.label})]})},e.value))})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"المنطقة الزمنية"}),(0,l.jsxs)(f.l6,{value:c.timezone,onValueChange:e=>n("timezone",e),disabled:!t,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{placeholder:"اختر المنطقة الزمنية"})}),(0,l.jsx)(f.gC,{children:D.map(e=>(0,l.jsx)(f.eb,{value:e.value,children:e.label},e.value))})]})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"تنسيق التاريخ"}),(0,l.jsxs)(f.l6,{value:c.dateFormat,onValueChange:e=>n("dateFormat",e),disabled:!t,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{placeholder:"اختر تنسيق التاريخ"})}),(0,l.jsx)(f.gC,{children:M.map(e=>(0,l.jsx)(f.eb,{value:e.value,children:e.label},e.value))})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"تنسيق الوقت"}),(0,l.jsxs)(f.l6,{value:c.timeFormat,onValueChange:e=>n("timeFormat",e),disabled:!t,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{placeholder:"اختر تنسيق الوقت"})}),(0,l.jsxs)(f.gC,{children:[(0,l.jsx)(f.eb,{value:"12h",children:"12 ساعة (1:30 PM)"}),(0,l.jsx)(f.eb,{value:"24h",children:"24 ساعة (13:30)"})]})]})]})]})]})]}),(0,l.jsxs)("div",{className:"diwan-card",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,l.jsx)("div",{className:"p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg",children:(0,l.jsx)(A.A,{className:"w-5 h-5 text-white"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"إعدادات النظام"})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"itemsPerPage",children:"عدد العناصر في الصفحة"}),(0,l.jsxs)(f.l6,{value:c.itemsPerPage.toString(),onValueChange:e=>n("itemsPerPage",parseInt(e)),disabled:!t,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{})}),(0,l.jsxs)(f.gC,{children:[(0,l.jsx)(f.eb,{value:"5",children:"5 عناصر"}),(0,l.jsx)(f.eb,{value:"10",children:"10 عناصر"}),(0,l.jsx)(f.eb,{value:"20",children:"20 عنصر"}),(0,l.jsx)(f.eb,{value:"50",children:"50 عنصر"}),(0,l.jsx)(f.eb,{value:"100",children:"100 عنصر"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"sessionTimeout",children:"مدة انتهاء الجلسة (دقيقة)"}),(0,l.jsxs)(f.l6,{value:c.sessionTimeout.toString(),onValueChange:e=>n("sessionTimeout",parseInt(e)),disabled:!t,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{})}),(0,l.jsxs)(f.gC,{children:[(0,l.jsx)(f.eb,{value:"15",children:"15 دقيقة"}),(0,l.jsx)(f.eb,{value:"30",children:"30 دقيقة"}),(0,l.jsx)(f.eb,{value:"60",children:"ساعة واحدة"}),(0,l.jsx)(f.eb,{value:"120",children:"ساعتان"}),(0,l.jsx)(f.eb,{value:"480",children:"8 ساعات"})]})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"الحفظ التلقائي"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"حفظ التغييرات تلقائياً أثناء الكتابة"})]}),(0,l.jsx)(C,{checked:c.autoSave,onCheckedChange:e=>n("autoSave",e),disabled:!t})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"سجل العمليات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"تسجيل جميع العمليات والتغييرات"})]}),(0,l.jsx)(C,{checked:c.enableAuditLog,onCheckedChange:e=>n("enableAuditLog",e),disabled:!t})]})]})]})]}),(0,l.jsxs)("div",{className:"diwan-card",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,l.jsx)("div",{className:"p-2 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg",children:(0,l.jsx)(E.A,{className:"w-5 h-5 text-white"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"إعدادات العرض"})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"رسالة الترحيب"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"عرض رسالة ترحيب في لوحة التحكم"})]}),(0,l.jsx)(C,{checked:c.showWelcomeMessage,onCheckedChange:e=>n("showWelcomeMessage",e),disabled:!t})]}),c.showWelcomeMessage&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"welcomeMessage",children:"نص رسالة الترحيب"}),(0,l.jsx)(v.p,{id:"welcomeMessage",value:c.welcomeMessage,onChange:e=>n("welcomeMessage",e.target.value),disabled:!t,placeholder:"مرحباً بك في ديوان آل أبو علوش"})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"إحصائيات لوحة التحكم"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"عرض الإحصائيات في لوحة التحكم"})]}),(0,l.jsx)(C,{checked:c.enableDashboardStats,onCheckedChange:e=>n("enableDashboardStats",e),disabled:!t})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"الإجراءات السريعة"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"عرض أزرار الإجراءات السريعة"})]}),(0,l.jsx)(C,{checked:c.enableQuickActions,onCheckedChange:e=>n("enableQuickActions",e),disabled:!t})]})]})]})]})}var P=a(66695),F=a(53311),I=a(62098),T=a(93509),L=a(14738),W=a(93500),U=a(27213),q=a(29869),B=a(46767),Z=a(92657);let O={theme:"light",primaryColor:"#3b82f6",secondaryColor:"#64748b",accentColor:"#f59e0b",backgroundColor:"#ffffff",textColor:"#1f2937",fontFamily:"Cairo",fontSize:"14px",fontWeight:"normal",lineHeight:"1.5",logo:"",favicon:"",brandName:"ديوان آل أبو علوش",brandColors:{primary:"#3b82f6",secondary:"#64748b"},sidebarStyle:"default",headerStyle:"default",cardStyle:"default",buttonStyle:"default",enableAnimations:!0,enableTransitions:!0,enableShadows:!0,enableGradients:!1,customCSS:"",enableCustomCSS:!1},$=[{name:"الأزرق الافتراضي",primary:"#3b82f6",secondary:"#64748b"},{name:"الأخضر الطبيعي",primary:"#10b981",secondary:"#6b7280"},{name:"البنفسجي الملكي",primary:"#8b5cf6",secondary:"#6b7280"},{name:"الأحمر الكلاسيكي",primary:"#ef4444",secondary:"#6b7280"},{name:"البرتقالي الدافئ",primary:"#f97316",secondary:"#6b7280"},{name:"الوردي الناعم",primary:"#ec4899",secondary:"#6b7280"},{name:"الذهبي الفاخر",primary:"#f59e0b",secondary:"#78716c"},{name:"الأزرق الداكن",primary:"#1e40af",secondary:"#4b5563"}],z=[{value:"Cairo",label:"Cairo (الافتراضي)"},{value:"Almarai",label:"Almarai"},{value:"Tajawal",label:"Tajawal"},{value:"Amiri",label:"Amiri"},{value:"Scheherazade",label:"Scheherazade"},{value:"Noto Sans Arabic",label:"Noto Sans Arabic"},{value:"IBM Plex Sans Arabic",label:"IBM Plex Sans Arabic"}],Y=[{value:"12px",label:"صغير (12px)"},{value:"14px",label:"متوسط (14px)"},{value:"16px",label:"كبير (16px)"},{value:"18px",label:"كبير جداً (18px)"}];function G(e){let{settings:s,onChange:a,canEdit:t}=e,[c,n]=(0,i.useState)(O),[d,x]=(0,i.useState)(!1),[m,h]=(0,i.useState)({logo:!1,favicon:!1});(0,i.useEffect)(()=>{s&&(n({...O,...s}),Object.entries({...O,...s}).forEach(e=>{let[s,a]=e;b(s,a)}))},[s]),(0,i.useEffect)(()=>{let e=u(),s={...O,...e};Object.entries(s).forEach(e=>{let[s,a]=e;b(s,a)}),n(s)},[]);let j=(e,s)=>{let l={...c,[e]:s};n(l),a(l),b(e,s),g(e,s)},g=(e,s)=>{try{let a=JSON.parse(localStorage.getItem("diwan-appearance-settings")||"{}");a[e]=s,localStorage.setItem("diwan-appearance-settings",JSON.stringify(a))}catch(e){console.error("خطأ في حفظ الإعدادات:",e)}},u=()=>{try{return JSON.parse(localStorage.getItem("diwan-appearance-settings")||"{}")}catch(e){return console.error("خطأ في تحميل الإعدادات:",e),{}}},b=(e,s)=>{let a=document.documentElement;switch(e){case"primaryColor":a.style.setProperty("--theme-primary-color",s),a.style.setProperty("--primary",s);break;case"secondaryColor":a.style.setProperty("--theme-secondary-color",s),a.style.setProperty("--secondary",s);break;case"accentColor":a.style.setProperty("--theme-accent-color",s),a.style.setProperty("--accent",s);break;case"backgroundColor":a.style.setProperty("--theme-background-color",s),a.style.setProperty("--background",s);break;case"textColor":a.style.setProperty("--theme-text-color",s),a.style.setProperty("--foreground",s);break;case"fontFamily":a.style.setProperty("--theme-font-family",s),document.body.style.fontFamily=s;break;case"fontSize":a.style.setProperty("--theme-font-size",s),document.body.style.fontSize=s;break;case"fontWeight":a.style.setProperty("--theme-font-weight",s),document.body.style.fontWeight=s;break;case"lineHeight":a.style.setProperty("--theme-line-height",s),document.body.style.lineHeight=s;break;case"theme":"dark"===s?(document.documentElement.classList.add("dark"),document.documentElement.classList.add("dark-theme"),document.documentElement.classList.remove("light")):(document.documentElement.classList.add("light"),document.documentElement.classList.remove("dark"),document.documentElement.classList.remove("dark-theme"));break;case"brandName":document.title=s||"ديوان آل أبو علوش"}document.body.classList.add("theme-applied")},N=e=>{let s={...c,primaryColor:e.primary,secondaryColor:e.secondary,brandColors:{primary:e.primary,secondary:e.secondary}};n(s),a(s),b("primaryColor",e.primary),b("secondaryColor",e.secondary)},w=async(e,s)=>{if(!t)return;if(!e.type.startsWith("image/"))return void alert("يرجى اختيار ملف صورة صحيح");if(e.size>5242880)return void alert("حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت");h(e=>({...e,[s]:!0}));let a=new FormData;a.append("file",e),a.append("type",s);try{let e=await fetch("/api/upload",{method:"POST",body:a});if(e.ok){let a=await e.json();j(s,a.url);let l=new CustomEvent("showToast",{detail:{message:"تم رفع الملف بنجاح",type:"success"}});window.dispatchEvent(l)}else{let s=await e.json();alert(s.error||"فشل في رفع الملف")}}catch(e){console.error("خطأ في رفع الملف:",e),alert("حدث خطأ في رفع الملف")}finally{h(e=>({...e,[s]:!1}))}},k=e=>{let s=document.createElement("input");s.type="file",s.accept="image/*",s.onchange=s=>{var a;let l=null==(a=s.target.files)?void 0:a[0];l&&w(l,e)},s.click()},S=e=>{e.preventDefault(),e.stopPropagation()},A=(e,s)=>{e.preventDefault(),e.stopPropagation();let a=e.dataTransfer.files;if(a.length>0){let e=a[0];e.type.startsWith("image/")&&w(e,s)}};return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsx)(P.Zp,{className:"border-green-200 bg-gradient-to-r from-green-50 to-emerald-50 animate-pulse",children:(0,l.jsx)(P.Wu,{className:"p-4",children:(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("div",{className:"p-2 bg-green-100 rounded-lg animate-bounce",children:(0,l.jsx)(F.A,{className:"w-5 h-5 text-green-600"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-green-800 font-semibold",children:"\uD83C\uDFA8 المعاينة المباشرة مفعلة"}),(0,l.jsx)("p",{className:"text-green-700 text-sm",children:"جميع التغييرات ستظهر فوراً في الواجهة! جرب تغيير الألوان أو الخطوط لترى النتيجة مباشرة."})]}),(0,l.jsx)("div",{className:"mr-auto",children:(0,l.jsxs)("div",{className:"flex gap-1",children:[(0,l.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full animate-ping"}),(0,l.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full animate-ping",style:{animationDelay:"0.2s"}}),(0,l.jsx)("div",{className:"w-3 h-3 bg-yellow-500 rounded-full animate-ping",style:{animationDelay:"0.4s"}})]})})]})})}),(0,l.jsxs)(P.Zp,{children:[(0,l.jsx)(P.aR,{children:(0,l.jsxs)(P.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(o.A,{className:"w-5 h-5 text-blue-600"}),"الثيم والألوان"]})}),(0,l.jsxs)(P.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)(y.J,{className:"text-sm font-medium text-gray-700",children:"نمط الثيم"}),(0,l.jsxs)("div",{className:"grid grid-cols-3 gap-3",children:[(0,l.jsxs)(r.$,{variant:"light"===c.theme?"default":"outline",onClick:()=>j("theme","light"),disabled:!t,className:"flex items-center gap-2 py-3 ".concat("light"===c.theme?"bg-gradient-to-r from-sky-500 to-sky-600 text-white shadow-lg":"hover:bg-sky-50 hover:border-sky-300"),children:[(0,l.jsx)(I.A,{className:"w-4 h-4"}),"فاتح"]}),(0,l.jsxs)(r.$,{variant:"dark"===c.theme?"default":"outline",onClick:()=>j("theme","dark"),disabled:!t,className:"flex items-center gap-2 py-3 ".concat("dark"===c.theme?"bg-gradient-to-r from-gray-700 to-gray-800 text-white shadow-lg":"hover:bg-gray-50 hover:border-gray-300"),children:[(0,l.jsx)(T.A,{className:"w-4 h-4"}),"داكن"]}),(0,l.jsxs)(r.$,{variant:"system"===c.theme?"default":"outline",onClick:()=>j("theme","system"),disabled:!t,className:"flex items-center gap-2 py-3 ".concat("system"===c.theme?"bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-lg":"hover:bg-purple-50 hover:border-purple-300"),children:[(0,l.jsx)(L.A,{className:"w-4 h-4"}),"النظام"]})]})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)(y.J,{children:"الألوان المحددة مسبقاً"}),(0,l.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2",children:$.map((e,s)=>(0,l.jsxs)(r.$,{variant:"outline",onClick:()=>N(e),disabled:!t,className:"h-auto p-3 flex flex-col items-center gap-2",children:[(0,l.jsxs)("div",{className:"flex gap-1",children:[(0,l.jsx)("div",{className:"w-4 h-4 rounded-full border",style:{backgroundColor:e.primary}}),(0,l.jsx)("div",{className:"w-4 h-4 rounded-full border",style:{backgroundColor:e.secondary}})]}),(0,l.jsx)("span",{className:"text-xs text-center",children:e.name})]},s))})]}),(0,l.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"primaryColor",children:"اللون الأساسي"}),(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsx)(v.p,{id:"primaryColor",type:"color",value:c.primaryColor,onChange:e=>j("primaryColor",e.target.value),disabled:!t,className:"w-12 h-10 p-1 border rounded"}),(0,l.jsx)(v.p,{value:c.primaryColor,onChange:e=>j("primaryColor",e.target.value),disabled:!t,placeholder:"#3b82f6"})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"secondaryColor",children:"اللون الثانوي"}),(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsx)(v.p,{id:"secondaryColor",type:"color",value:c.secondaryColor,onChange:e=>j("secondaryColor",e.target.value),disabled:!t,className:"w-12 h-10 p-1 border rounded"}),(0,l.jsx)(v.p,{value:c.secondaryColor,onChange:e=>j("secondaryColor",e.target.value),disabled:!t,placeholder:"#64748b"})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"accentColor",children:"لون التمييز"}),(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsx)(v.p,{id:"accentColor",type:"color",value:c.accentColor,onChange:e=>j("accentColor",e.target.value),disabled:!t,className:"w-12 h-10 p-1 border rounded"}),(0,l.jsx)(v.p,{value:c.accentColor,onChange:e=>j("accentColor",e.target.value),disabled:!t,placeholder:"#f59e0b"})]})]})]})]})]}),(0,l.jsxs)(P.Zp,{children:[(0,l.jsx)(P.aR,{children:(0,l.jsxs)(P.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(W.A,{className:"w-5 h-5 text-green-600"}),"الخطوط والنصوص"]})}),(0,l.jsxs)(P.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"نوع الخط"}),(0,l.jsxs)(f.l6,{value:c.fontFamily,onValueChange:e=>j("fontFamily",e),disabled:!t,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{placeholder:"اختر نوع الخط"})}),(0,l.jsx)(f.gC,{children:z.map(e=>(0,l.jsx)(f.eb,{value:e.value,children:(0,l.jsx)("span",{style:{fontFamily:e.value},children:e.label})},e.value))})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"حجم الخط"}),(0,l.jsxs)(f.l6,{value:c.fontSize,onValueChange:e=>j("fontSize",e),disabled:!t,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{placeholder:"اختر حجم الخط"})}),(0,l.jsx)(f.gC,{children:Y.map(e=>(0,l.jsx)(f.eb,{value:e.value,children:e.label},e.value))})]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"معاينة الخط"}),(0,l.jsxs)("div",{className:"p-4 border rounded-lg bg-gray-50",style:{fontFamily:c.fontFamily,fontSize:c.fontSize,lineHeight:c.lineHeight},children:[(0,l.jsx)("p",{className:"text-lg font-bold mb-2",children:"ديوان آل أبو علوش"}),(0,l.jsx)("p",{className:"mb-2",children:"هذا نص تجريبي لمعاينة الخط المختار. يمكنك رؤية كيف سيظهر النص في النظام."}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"الأرقام: 1234567890"})]})]})]})]}),(0,l.jsxs)(P.Zp,{children:[(0,l.jsx)(P.aR,{children:(0,l.jsxs)(P.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(U.A,{className:"w-5 h-5 text-purple-600"}),"الشعار والعلامة التجارية"]})}),(0,l.jsxs)(P.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"brandName",children:"اسم العلامة التجارية"}),(0,l.jsx)(v.p,{id:"brandName",value:c.brandName,onChange:e=>j("brandName",e.target.value),disabled:!t,placeholder:"ديوان آل أبو علوش"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"الشعار الرئيسي"}),(0,l.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors cursor-pointer",onDragOver:S,onDrop:e=>A(e,"logo"),onClick:()=>k("logo"),children:c.logo?(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("img",{src:c.logo,alt:"الشعار",className:"max-h-16 mx-auto"}),(0,l.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>j("logo",""),disabled:!t,children:"إزالة الشعار"})]}):(0,l.jsx)("div",{className:"space-y-2",children:m.logo?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,l.jsx)("p",{className:"text-sm text-blue-600",children:"جاري رفع الشعار..."})]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(q.A,{className:"w-8 h-8 text-gray-400 mx-auto"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"اسحب الشعار هنا أو انقر للرفع"}),(0,l.jsx)(r.$,{variant:"outline",size:"sm",disabled:!t||m.logo,onClick:e=>{e.stopPropagation(),k("logo")},children:"رفع شعار"})]})})})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"أيقونة الموقع (Favicon)"}),(0,l.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors cursor-pointer",onDragOver:S,onDrop:e=>A(e,"favicon"),onClick:()=>k("favicon"),children:c.favicon?(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("img",{src:c.favicon,alt:"الأيقونة",className:"w-8 h-8 mx-auto"}),(0,l.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>j("favicon",""),disabled:!t,children:"إزالة الأيقونة"})]}):(0,l.jsx)("div",{className:"space-y-2",children:m.favicon?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"}),(0,l.jsx)("p",{className:"text-xs text-blue-600",children:"جاري رفع الأيقونة..."})]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(q.A,{className:"w-6 h-6 text-gray-400 mx-auto"}),(0,l.jsx)("p",{className:"text-xs text-gray-600",children:"32x32 px"}),(0,l.jsx)(r.$,{variant:"outline",size:"sm",disabled:!t||m.favicon,onClick:e=>{e.stopPropagation(),k("favicon")},children:"رفع أيقونة"})]})})})]})]})]})]}),(0,l.jsxs)(P.Zp,{children:[(0,l.jsx)(P.aR,{children:(0,l.jsxs)(P.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(B.A,{className:"w-5 h-5 text-orange-600"}),"تخصيص الواجهة"]})}),(0,l.jsxs)(P.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"نمط الشريط الجانبي"}),(0,l.jsxs)(f.l6,{value:c.sidebarStyle,onValueChange:e=>j("sidebarStyle",e),disabled:!t,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{})}),(0,l.jsxs)(f.gC,{children:[(0,l.jsx)(f.eb,{value:"default",children:"افتراضي"}),(0,l.jsx)(f.eb,{value:"compact",children:"مضغوط"}),(0,l.jsx)(f.eb,{value:"minimal",children:"بسيط"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"نمط الرأس"}),(0,l.jsxs)(f.l6,{value:c.headerStyle,onValueChange:e=>j("headerStyle",e),disabled:!t,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{})}),(0,l.jsxs)(f.gC,{children:[(0,l.jsx)(f.eb,{value:"default",children:"افتراضي"}),(0,l.jsx)(f.eb,{value:"compact",children:"مضغوط"}),(0,l.jsx)(f.eb,{value:"transparent",children:"شفاف"})]})]})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"نمط البطاقات"}),(0,l.jsxs)(f.l6,{value:c.cardStyle,onValueChange:e=>j("cardStyle",e),disabled:!t,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{})}),(0,l.jsxs)(f.gC,{children:[(0,l.jsx)(f.eb,{value:"default",children:"افتراضي"}),(0,l.jsx)(f.eb,{value:"bordered",children:"محدد"}),(0,l.jsx)(f.eb,{value:"shadow",children:"ظلال"}),(0,l.jsx)(f.eb,{value:"flat",children:"مسطح"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"نمط الأزرار"}),(0,l.jsxs)(f.l6,{value:c.buttonStyle,onValueChange:e=>j("buttonStyle",e),disabled:!t,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{})}),(0,l.jsxs)(f.gC,{children:[(0,l.jsx)(f.eb,{value:"default",children:"افتراضي"}),(0,l.jsx)(f.eb,{value:"rounded",children:"دائري"}),(0,l.jsx)(f.eb,{value:"square",children:"مربع"})]})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"الحركات والانتقالات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"تفعيل الحركات المتحركة"})]}),(0,l.jsx)(C,{checked:c.enableAnimations,onCheckedChange:e=>j("enableAnimations",e),disabled:!t})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"الظلال"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إضافة ظلال للعناصر"})]}),(0,l.jsx)(C,{checked:c.enableShadows,onCheckedChange:e=>j("enableShadows",e),disabled:!t})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"التدرجات اللونية"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"استخدام التدرجات في الخلفيات"})]}),(0,l.jsx)(C,{checked:c.enableGradients,onCheckedChange:e=>j("enableGradients",e),disabled:!t})]})]})]})]}),(0,l.jsxs)(P.Zp,{children:[(0,l.jsx)(P.aR,{children:(0,l.jsxs)(P.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(Z.A,{className:"w-5 h-5 text-red-600"}),"CSS مخصص"]})}),(0,l.jsxs)(P.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"تفعيل CSS مخصص"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إضافة أنماط CSS مخصصة"})]}),(0,l.jsx)(C,{checked:c.enableCustomCSS,onCheckedChange:e=>j("enableCustomCSS",e),disabled:!t})]}),c.enableCustomCSS&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"customCSS",children:"كود CSS المخصص"}),(0,l.jsx)("textarea",{id:"customCSS",value:c.customCSS,onChange:e=>j("customCSS",e.target.value),disabled:!t,placeholder:"/* أضف كود CSS المخصص هنا */",className:"w-full h-32 p-3 border rounded-lg font-mono text-sm"}),(0,l.jsx)("p",{className:"text-xs text-gray-500",children:"تحذير: استخدم CSS مخصص بحذر. قد يؤثر على مظهر النظام."})]})]})]}),t&&(0,l.jsx)(P.Zp,{children:(0,l.jsx)(P.Wu,{className:"p-4",children:(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsxs)(r.$,{variant:"outline",onClick:()=>{n(O),a(O),Object.entries(O).forEach(e=>{let[s,a]=e;b(s,a)})},className:"text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300",children:[(0,l.jsx)(p.A,{className:"w-4 h-4 ml-2"}),"إعادة تعيين الافتراضي"]}),(0,l.jsxs)(r.$,{onClick:()=>x(!d),className:"bg-gradient-to-r from-sky-500 to-sky-600 hover:from-sky-600 hover:to-sky-700 text-white",children:[(0,l.jsx)(Z.A,{className:"w-4 h-4 ml-2"}),d?"إخفاء المعاينة":"معاينة التغييرات"]})]})})})]})}var _=a(17580),H=a(55868),K=a(1243),Q=a(14186);let X={enableNotifications:!0,enableSounds:!0,enableDesktopNotifications:!0,enableEmailNotifications:!1,enableSMSNotifications:!1,memberNotifications:{newMember:!0,memberUpdate:!0,memberStatusChange:!0,memberPayment:!0},incomeNotifications:{newIncome:!0,incomeUpdate:!0,paymentReminder:!0,paymentOverdue:!0},expenseNotifications:{newExpense:!0,expenseUpdate:!0,budgetAlert:!0,expenseApproval:!0},systemNotifications:{systemUpdate:!0,securityAlert:!0,backupComplete:!0,errorAlert:!0},quietHours:{enabled:!1,startTime:"22:00",endTime:"08:00"},emailSettings:{smtpServer:"",smtpPort:587,smtpUsername:"",smtpPassword:"",fromEmail:"",fromName:"ديوان آل أبو علوش",enableSSL:!0},smsSettings:{provider:"",apiKey:"",senderName:"ديوان آل أبو علوش"},templates:{welcomeMessage:"مرحباً بك في ديوان آل أبو علوش",paymentReminder:"تذكير: يرجى دفع الاشتراك الشهري",paymentConfirmation:"تم استلام دفعتك بنجاح",systemAlert:"تنبيه من النظام"}};function ee(e){let{settings:s,onChange:a,canEdit:t}=e,[c,r]=(0,i.useState)(X),[n,o]=(0,i.useState)(!1),[m,h]=(0,i.useState)(!1);(0,i.useEffect)(()=>{s&&r({...X,...s})},[s]);let j=(e,s)=>{let l=e.split("."),i={...c};1===l.length?i={...i,[l[0]]:s}:2===l.length&&(i={...i,[l[0]]:{...i[l[0]],[l[1]]:s}}),r(i),a(i)};return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)(P.Zp,{children:[(0,l.jsx)(P.aR,{children:(0,l.jsxs)(P.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(x.A,{className:"w-5 h-5 text-blue-600"}),"الإعدادات العامة للإشعارات"]})}),(0,l.jsx)(P.Wu,{className:"space-y-4",children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"تفعيل الإشعارات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"تفعيل أو إلغاء جميع الإشعارات"})]}),(0,l.jsx)(C,{checked:c.enableNotifications,onCheckedChange:e=>j("enableNotifications",e),disabled:!t})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"الأصوات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"تشغيل أصوات الإشعارات"})]}),(0,l.jsx)(C,{checked:c.enableSounds,onCheckedChange:e=>j("enableSounds",e),disabled:!t||!c.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"إشعارات سطح المكتب"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"عرض إشعارات في المتصفح"})]}),(0,l.jsx)(C,{checked:c.enableDesktopNotifications,onCheckedChange:e=>j("enableDesktopNotifications",e),disabled:!t||!c.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"إشعارات البريد الإلكتروني"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إرسال إشعارات عبر البريد الإلكتروني"})]}),(0,l.jsx)(C,{checked:c.enableEmailNotifications,onCheckedChange:e=>j("enableEmailNotifications",e),disabled:!t||!c.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"الرسائل النصية"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إرسال إشعارات عبر الرسائل النصية"})]}),(0,l.jsx)(C,{checked:c.enableSMSNotifications,onCheckedChange:e=>j("enableSMSNotifications",e),disabled:!t||!c.enableNotifications})]})]})})]}),(0,l.jsxs)(P.Zp,{children:[(0,l.jsx)(P.aR,{children:(0,l.jsxs)(P.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(_.A,{className:"w-5 h-5 text-green-600"}),"إشعارات الأعضاء"]})}),(0,l.jsx)(P.Wu,{className:"space-y-4",children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"عضو جديد"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إشعار عند إضافة عضو جديد"})]}),(0,l.jsx)(C,{checked:c.memberNotifications.newMember,onCheckedChange:e=>j("memberNotifications.newMember",e),disabled:!t||!c.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"تحديث بيانات العضو"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إشعار عند تحديث بيانات عضو"})]}),(0,l.jsx)(C,{checked:c.memberNotifications.memberUpdate,onCheckedChange:e=>j("memberNotifications.memberUpdate",e),disabled:!t||!c.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"تغيير حالة العضو"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إشعار عند تغيير حالة العضو"})]}),(0,l.jsx)(C,{checked:c.memberNotifications.memberStatusChange,onCheckedChange:e=>j("memberNotifications.memberStatusChange",e),disabled:!t||!c.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"دفعات الأعضاء"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إشعار عند دفع الاشتراكات"})]}),(0,l.jsx)(C,{checked:c.memberNotifications.memberPayment,onCheckedChange:e=>j("memberNotifications.memberPayment",e),disabled:!t||!c.enableNotifications})]})]})})]}),(0,l.jsxs)(P.Zp,{children:[(0,l.jsx)(P.aR,{children:(0,l.jsxs)(P.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(H.A,{className:"w-5 h-5 text-emerald-600"}),"إشعارات الإيرادات"]})}),(0,l.jsx)(P.Wu,{className:"space-y-4",children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"إيراد جديد"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إشعار عند إضافة إيراد جديد"})]}),(0,l.jsx)(C,{checked:c.incomeNotifications.newIncome,onCheckedChange:e=>j("incomeNotifications.newIncome",e),disabled:!t||!c.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"تحديث الإيراد"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إشعار عند تحديث إيراد"})]}),(0,l.jsx)(C,{checked:c.incomeNotifications.incomeUpdate,onCheckedChange:e=>j("incomeNotifications.incomeUpdate",e),disabled:!t||!c.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"تذكير الدفع"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"تذكير الأعضاء بموعد الدفع"})]}),(0,l.jsx)(C,{checked:c.incomeNotifications.paymentReminder,onCheckedChange:e=>j("incomeNotifications.paymentReminder",e),disabled:!t||!c.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"تأخير الدفع"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"تنبيه عند تأخر الدفع"})]}),(0,l.jsx)(C,{checked:c.incomeNotifications.paymentOverdue,onCheckedChange:e=>j("incomeNotifications.paymentOverdue",e),disabled:!t||!c.enableNotifications})]})]})})]}),(0,l.jsxs)(P.Zp,{children:[(0,l.jsx)(P.aR,{children:(0,l.jsxs)(P.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(K.A,{className:"w-5 h-5 text-red-600"}),"إشعارات المصروفات"]})}),(0,l.jsx)(P.Wu,{className:"space-y-4",children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"مصروف جديد"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إشعار عند إضافة مصروف جديد"})]}),(0,l.jsx)(C,{checked:c.expenseNotifications.newExpense,onCheckedChange:e=>j("expenseNotifications.newExpense",e),disabled:!t||!c.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"تحديث المصروف"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إشعار عند تحديث مصروف"})]}),(0,l.jsx)(C,{checked:c.expenseNotifications.expenseUpdate,onCheckedChange:e=>j("expenseNotifications.expenseUpdate",e),disabled:!t||!c.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"تنبيه الميزانية"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"تنبيه عند تجاوز حد الميزانية"})]}),(0,l.jsx)(C,{checked:c.expenseNotifications.budgetAlert,onCheckedChange:e=>j("expenseNotifications.budgetAlert",e),disabled:!t||!c.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"موافقة المصروف"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إشعار عند الحاجة لموافقة على مصروف"})]}),(0,l.jsx)(C,{checked:c.expenseNotifications.expenseApproval,onCheckedChange:e=>j("expenseNotifications.expenseApproval",e),disabled:!t||!c.enableNotifications})]})]})})]}),(0,l.jsxs)(P.Zp,{children:[(0,l.jsx)(P.aR,{children:(0,l.jsxs)(P.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(d.A,{className:"w-5 h-5 text-purple-600"}),"إشعارات النظام"]})}),(0,l.jsx)(P.Wu,{className:"space-y-4",children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"تحديثات النظام"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إشعار عند توفر تحديثات"})]}),(0,l.jsx)(C,{checked:c.systemNotifications.systemUpdate,onCheckedChange:e=>j("systemNotifications.systemUpdate",e),disabled:!t||!c.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"تنبيهات الأمان"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"تنبيهات أمنية مهمة"})]}),(0,l.jsx)(C,{checked:c.systemNotifications.securityAlert,onCheckedChange:e=>j("systemNotifications.securityAlert",e),disabled:!t||!c.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"اكتمال النسخ الاحتياطي"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إشعار عند اكتمال النسخ الاحتياطي"})]}),(0,l.jsx)(C,{checked:c.systemNotifications.backupComplete,onCheckedChange:e=>j("systemNotifications.backupComplete",e),disabled:!t||!c.enableNotifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"تنبيهات الأخطاء"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إشعار عند حدوث أخطاء في النظام"})]}),(0,l.jsx)(C,{checked:c.systemNotifications.errorAlert,onCheckedChange:e=>j("systemNotifications.errorAlert",e),disabled:!t||!c.enableNotifications})]})]})})]}),(0,l.jsxs)(P.Zp,{children:[(0,l.jsx)(P.aR,{children:(0,l.jsxs)(P.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(Q.A,{className:"w-5 h-5 text-orange-600"}),"ساعات الهدوء"]})}),(0,l.jsxs)(P.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"تفعيل ساعات الهدوء"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إيقاف الإشعارات في أوقات محددة"})]}),(0,l.jsx)(C,{checked:c.quietHours.enabled,onCheckedChange:e=>j("quietHours.enabled",e),disabled:!t||!c.enableNotifications})]}),c.quietHours.enabled&&(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"startTime",children:"وقت البداية"}),(0,l.jsx)(v.p,{id:"startTime",type:"time",value:c.quietHours.startTime,onChange:e=>j("quietHours.startTime",e.target.value),disabled:!t})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"endTime",children:"وقت النهاية"}),(0,l.jsx)(v.p,{id:"endTime",type:"time",value:c.quietHours.endTime,onChange:e=>j("quietHours.endTime",e.target.value),disabled:!t})]})]})]})]})]})}var es=a(71007),ea=a(84355);function el(e){let{users:s,onClose:a}=e,[t,c]=(0,i.useState)(""),[o,x]=(0,i.useState)([]),[m,h]=(0,i.useState)({userId:"",canViewAllMembers:!1,canEditMembers:!1,canDeleteMembers:!1,canViewAllIncomes:!1,canEditIncomes:!1,canDeleteIncomes:!1,canViewAllExpenses:!1,canEditExpenses:!1,canDeleteExpenses:!1,canViewGallery:!1,canUploadToGallery:!1,canDeleteFromGallery:!1,canViewReports:!1,canExportData:!1,canManageUsers:!1,canManageSettings:!1,canViewMemberAccount:!1,canViewMemberDetails:!1,galleryReadOnly:!0,canCreateGalleryFolders:!1}),[j,p]=(0,i.useState)(!1);(0,i.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/members?limit=1000");if(e.ok){let s=await e.json();x(s.members||s)}}catch(e){console.error("خطأ في جلب الأعضاء:",e)}})()},[]),(0,i.useEffect)(()=>{t&&g(t)},[t]);let g=async e=>{try{p(!0);let s=await fetch("/api/admin/user-permissions?userId=".concat(e));if(s.ok){let a=await s.json();a?h(a):h({userId:e,canViewAllMembers:!1,canEditMembers:!1,canDeleteMembers:!1,canViewAllIncomes:!1,canEditIncomes:!1,canDeleteIncomes:!1,canViewAllExpenses:!1,canEditExpenses:!1,canDeleteExpenses:!1,canViewGallery:!1,canUploadToGallery:!1,canDeleteFromGallery:!1,canViewReports:!1,canExportData:!1,canManageUsers:!1,canManageSettings:!1,canViewMemberAccount:!1,canViewMemberDetails:!1,galleryReadOnly:!0,canCreateGalleryFolders:!1})}}catch(e){console.error("خطأ في جلب صلاحيات المستخدم:",e)}finally{p(!1)}},u=(e,s)=>{h(a=>({...a,[e]:s}))},b=async()=>{if(!t)return void alert("يرجى اختيار مستخدم");try{p(!0);let e=await fetch("/api/admin/user-permissions",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:t,permissions:{canViewAllMembers:m.canViewAllMembers,canEditMembers:m.canEditMembers,canDeleteMembers:m.canDeleteMembers,canViewAllIncomes:m.canViewAllIncomes,canEditIncomes:m.canEditIncomes,canDeleteIncomes:m.canDeleteIncomes,canViewAllExpenses:m.canViewAllExpenses,canEditExpenses:m.canEditExpenses,canDeleteExpenses:m.canDeleteExpenses,canViewGallery:m.canViewGallery,canUploadToGallery:m.canUploadToGallery,canDeleteFromGallery:m.canDeleteFromGallery,canViewReports:m.canViewReports,canExportData:m.canExportData,canManageUsers:m.canManageUsers,canManageSettings:m.canManageSettings,specificMemberId:m.specificMemberId||null,canViewMemberAccount:m.canViewMemberAccount,canViewMemberDetails:m.canViewMemberDetails,galleryReadOnly:m.galleryReadOnly,canCreateGalleryFolders:m.canCreateGalleryFolders}})});if(e.ok)alert("تم حفظ الصلاحيات بنجاح");else{let s=await e.json();alert(s.message||"فشل في حفظ الصلاحيات")}}catch(e){console.error("خطأ في حفظ الصلاحيات:",e),alert("حدث خطأ أثناء حفظ الصلاحيات")}finally{p(!1)}},v=s.find(e=>e.id===t);return(0,l.jsxs)(P.Zp,{className:"border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-indigo-50 shadow-xl",children:[(0,l.jsx)(P.aR,{className:"bg-gradient-to-r from-purple-500 to-indigo-600 text-white rounded-t-lg",children:(0,l.jsxs)(P.ZB,{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("div",{className:"p-2 bg-white bg-opacity-20 rounded-lg",children:(0,l.jsx)(d.A,{className:"w-6 h-6 text-white"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-xl font-bold",children:"إدارة الصلاحيات المفصلة"}),(0,l.jsx)("p",{className:"text-purple-100 text-sm mt-1",children:"تحديد صلاحيات دقيقة لكل مستخدم"})]})]}),(0,l.jsx)(r.$,{variant:"outline",size:"sm",onClick:a,className:"bg-white bg-opacity-20 border-white border-opacity-30 text-white hover:bg-white hover:bg-opacity-30",children:"إغلاق"})]})}),(0,l.jsxs)(P.Wu,{className:"space-y-6 p-6",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"اختيار المستخدم"}),(0,l.jsxs)(f.l6,{value:t,onValueChange:c,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{placeholder:"اختر مستخدماً لتعديل صلاحياته"})}),(0,l.jsx)(f.gC,{children:s.filter(e=>"ADMIN"!==e.role).map(e=>(0,l.jsx)(f.eb,{value:e.id,children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(es.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:e.name}),(0,l.jsx)(n.E,{variant:"outline",className:"text-xs",children:"DATA_ENTRY"===e.role?"مدخل بيانات":"VIEWER"===e.role?"مطلع":"MEMBER_VIEWER"===e.role?"مطلع على عضو":"GALLERY_VIEWER"===e.role?"مطلع على المعرض":e.role})]})},e.id))})]})]}),v&&(0,l.jsxs)("div",{className:"bg-white rounded-lg p-4 border border-gray-200",children:[(0,l.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"المستخدم المحدد:"}),(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("div",{className:"w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center",children:(0,l.jsx)(es.A,{className:"w-5 h-5 text-purple-600"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium",children:v.name}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:v.email})]})]})]}),t&&!j&&(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)(P.Zp,{children:[(0,l.jsx)(P.aR,{children:(0,l.jsxs)(P.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,l.jsx)(_.A,{className:"w-5 h-5 text-blue-600"}),"صلاحيات الأعضاء"]})}),(0,l.jsxs)(P.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)(y.J,{children:"عرض جميع الأعضاء"}),(0,l.jsx)(C,{checked:m.canViewAllMembers,onCheckedChange:e=>u("canViewAllMembers",e)})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)(y.J,{children:"تعديل الأعضاء"}),(0,l.jsx)(C,{checked:m.canEditMembers,onCheckedChange:e=>u("canEditMembers",e)})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)(y.J,{children:"حذف الأعضاء"}),(0,l.jsx)(C,{checked:m.canDeleteMembers,onCheckedChange:e=>u("canDeleteMembers",e)})]})]}),(0,l.jsxs)("div",{className:"border-t pt-4",children:[(0,l.jsx)("h5",{className:"font-medium mb-3",children:"صلاحيات عضو محدد"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"اختيار عضو محدد (اختياري)"}),(0,l.jsxs)(f.l6,{value:m.specificMemberId||"",onValueChange:e=>u("specificMemberId",e),children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{placeholder:"اختر عضواً محدداً"})}),(0,l.jsxs)(f.gC,{children:[(0,l.jsx)(f.eb,{value:"",children:"لا يوجد عضو محدد"}),o.map(e=>(0,l.jsx)(f.eb,{value:e.id,children:e.name},e.id))]})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)(y.J,{children:"عرض كشف حساب العضو"}),(0,l.jsx)(C,{checked:m.canViewMemberAccount,onCheckedChange:e=>u("canViewMemberAccount",e)})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)(y.J,{children:"عرض تفاصيل العضو"}),(0,l.jsx)(C,{checked:m.canViewMemberDetails,onCheckedChange:e=>u("canViewMemberDetails",e)})]})]})]})]})]})]}),(0,l.jsxs)(P.Zp,{children:[(0,l.jsx)(P.aR,{children:(0,l.jsxs)(P.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,l.jsx)(ea.A,{className:"w-5 h-5 text-green-600"}),"صلاحيات المعرض"]})}),(0,l.jsx)(P.Wu,{className:"space-y-4",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)(y.J,{children:"عرض المعرض"}),(0,l.jsx)(C,{checked:m.canViewGallery,onCheckedChange:e=>u("canViewGallery",e)})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)(y.J,{children:"المعرض للقراءة فقط"}),(0,l.jsx)(C,{checked:m.galleryReadOnly,onCheckedChange:e=>u("galleryReadOnly",e)})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)(y.J,{children:"رفع صور للمعرض"}),(0,l.jsx)(C,{checked:m.canUploadToGallery,onCheckedChange:e=>u("canUploadToGallery",e)})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)(y.J,{children:"إنشاء مجلدات"}),(0,l.jsx)(C,{checked:m.canCreateGalleryFolders,onCheckedChange:e=>u("canCreateGalleryFolders",e)})]})]})})]}),(0,l.jsxs)("div",{className:"flex justify-end gap-3 pt-4",children:[(0,l.jsx)(r.$,{variant:"outline",onClick:a,children:"إلغاء"}),(0,l.jsx)(r.$,{onClick:b,disabled:j,className:"bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 text-white",children:j?"جاري الحفظ...":"حفظ الصلاحيات"})]})]}),j&&(0,l.jsx)("div",{className:"flex justify-center py-8",children:(0,l.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"})})]})]})}var ei=a(32919),et=a(55670),ec=a(29676),er=a(12318),en=a(28883),ed=a(19420),eo=a(13717),ex=a(62525),em=a(40646);let eh={passwordPolicy:{minLength:8,requireUppercase:!0,requireLowercase:!0,requireNumbers:!0,requireSpecialChars:!1,preventReuse:5,expirationDays:90},sessionSettings:{timeout:30,maxConcurrentSessions:3,requireReauth:!1,rememberMe:!0,rememberMeDuration:30},loginSettings:{maxFailedAttempts:5,lockoutDuration:15,enableCaptcha:!1,enableTwoFactor:!1,allowedIPs:[],blockedIPs:[]},auditSettings:{enableAuditLog:!0,logLoginAttempts:!0,logDataChanges:!0,logSystemEvents:!0,retentionDays:365,enableRealTimeAlerts:!0},permissionSettings:{defaultRole:"VIEWER",allowSelfRegistration:!1,requireAdminApproval:!0,enableRoleHierarchy:!0,maxUsersPerRole:{ADMIN:3,DATA_ENTRY:10,VIEWER:100}},advancedSecurity:{enableEncryption:!0,enableSSL:!0,enableCSRF:!0,enableXSS:!0,enableSQLInjection:!0,enableRateLimit:!0,rateLimitRequests:100,rateLimitWindow:15}};function ej(e){let{settings:s,onChange:a,canEdit:t}=e,[c,o]=(0,i.useState)(eh),[x,h]=(0,i.useState)(!1),[j,p]=(0,i.useState)(!1),[g,u]=(0,i.useState)([]),[b,N]=(0,i.useState)(!1),[w,k]=(0,i.useState)({name:"",email:"",phone:"",role:"VIEWER",password:"",confirmPassword:""}),[S,A]=(0,i.useState)(null),[E,J]=(0,i.useState)(!1),[R,D]=(0,i.useState)(!1);(0,i.useEffect)(()=>{s&&o({...eh,...s})},[s]),(0,i.useEffect)(()=>{j&&M()},[j]);let M=async()=>{try{J(!0);let e=await fetch("/api/admin/users");if(e.ok){let s=await e.json();u(s)}}catch(e){console.error("خطأ في تحميل المستخدمين:",e)}finally{J(!1)}},V=(e,s)=>{let l=e.split("."),i={...c};1===l.length?i={...i,[l[0]]:s}:2===l.length?i={...i,[l[0]]:{...i[l[0]],[l[1]]:s}}:3===l.length&&(i={...i,[l[0]]:{...i[l[0]],[l[1]]:{...i[l[0]][l[1]],[l[2]]:s}}}),o(i),a(i)},F=async()=>{if(!w.name||!w.email||!w.password)return void alert("يرجى ملء جميع الحقول المطلوبة");if(w.password!==w.confirmPassword)return void alert("كلمات المرور غير متطابقة");try{J(!0);let e=await fetch("/api/admin/users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:w.name,email:w.email,phone:w.phone,role:w.role,password:w.password})});if(e.ok)alert("تم إضافة المستخدم بنجاح"),k({name:"",email:"",phone:"",role:"VIEWER",password:"",confirmPassword:""}),N(!1),M();else{let s=await e.json();alert(s.message||"فشل في إضافة المستخدم")}}catch(e){console.error("خطأ في إضافة المستخدم:",e),alert("حدث خطأ أثناء إضافة المستخدم")}finally{J(!1)}},I=async(e,s)=>{try{J(!0);let a=await fetch("/api/admin/users/".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(a.ok)alert("تم تحديث المستخدم بنجاح"),A(null),M();else{let e=await a.json();alert(e.message||"فشل في تحديث المستخدم")}}catch(e){console.error("خطأ في تحديث المستخدم:",e),alert("حدث خطأ أثناء تحديث المستخدم")}finally{J(!1)}},T=async(e,s)=>{if(confirm('هل أنت متأكد من حذف المستخدم "'.concat(s,'"؟\n\nتحذير: سيتم حذف جميع البيانات المرتبطة بهذا المستخدم نهائياً ولا يمكن التراجع عن هذا الإجراء.')))try{J(!0);let s=await fetch("/api/admin/users/".concat(e),{method:"DELETE"});if(s.ok)alert("تم حذف المستخدم بنجاح"),M();else{let e=await s.json();alert(e.message||"فشل في حذف المستخدم")}}catch(e){console.error("خطأ في حذف المستخدم:",e),alert("حدث خطأ أثناء حذف المستخدم")}finally{J(!1)}},L=e=>{switch(e){case"ADMIN":return"مدير";case"DATA_ENTRY":return"مدخل بيانات";case"VIEWER":return"مطلع";case"MEMBER_VIEWER":return"مطلع على عضو";case"GALLERY_VIEWER":return"مطلع على المعرض";case"MEMBER":return"عضو";default:return e}},W=e=>{switch(e){case"ADMIN":return"bg-red-100 text-red-800";case"DATA_ENTRY":return"bg-blue-100 text-blue-800";case"VIEWER":return"bg-green-100 text-green-800";case"MEMBER_VIEWER":return"bg-purple-100 text-purple-800";case"GALLERY_VIEWER":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}};return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)(P.Zp,{children:[(0,l.jsx)(P.aR,{children:(0,l.jsxs)(P.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(ei.A,{className:"w-5 h-5 text-blue-600"}),"سياسة كلمات المرور"]})}),(0,l.jsxs)(P.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"minLength",children:"الحد الأدنى لطول كلمة المرور"}),(0,l.jsxs)(f.l6,{value:c.passwordPolicy.minLength.toString(),onValueChange:e=>V("passwordPolicy.minLength",parseInt(e)),disabled:!t,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{})}),(0,l.jsxs)(f.gC,{children:[(0,l.jsx)(f.eb,{value:"6",children:"6 أحرف"}),(0,l.jsx)(f.eb,{value:"8",children:"8 أحرف"}),(0,l.jsx)(f.eb,{value:"10",children:"10 أحرف"}),(0,l.jsx)(f.eb,{value:"12",children:"12 حرف"}),(0,l.jsx)(f.eb,{value:"16",children:"16 حرف"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"expirationDays",children:"انتهاء صلاحية كلمة المرور (يوم)"}),(0,l.jsxs)(f.l6,{value:c.passwordPolicy.expirationDays.toString(),onValueChange:e=>V("passwordPolicy.expirationDays",parseInt(e)),disabled:!t,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{})}),(0,l.jsxs)(f.gC,{children:[(0,l.jsx)(f.eb,{value:"30",children:"30 يوم"}),(0,l.jsx)(f.eb,{value:"60",children:"60 يوم"}),(0,l.jsx)(f.eb,{value:"90",children:"90 يوم"}),(0,l.jsx)(f.eb,{value:"180",children:"180 يوم"}),(0,l.jsx)(f.eb,{value:"365",children:"سنة واحدة"}),(0,l.jsx)(f.eb,{value:"0",children:"بدون انتهاء"})]})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"يجب أن تحتوي على أحرف كبيرة"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"A-Z"})]}),(0,l.jsx)(C,{checked:c.passwordPolicy.requireUppercase,onCheckedChange:e=>V("passwordPolicy.requireUppercase",e),disabled:!t})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"يجب أن تحتوي على أحرف صغيرة"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"a-z"})]}),(0,l.jsx)(C,{checked:c.passwordPolicy.requireLowercase,onCheckedChange:e=>V("passwordPolicy.requireLowercase",e),disabled:!t})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"يجب أن تحتوي على أرقام"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"0-9"})]}),(0,l.jsx)(C,{checked:c.passwordPolicy.requireNumbers,onCheckedChange:e=>V("passwordPolicy.requireNumbers",e),disabled:!t})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"يجب أن تحتوي على رموز خاصة"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"!@#$%^&*"})]}),(0,l.jsx)(C,{checked:c.passwordPolicy.requireSpecialChars,onCheckedChange:e=>V("passwordPolicy.requireSpecialChars",e),disabled:!t})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"preventReuse",children:"منع إعادة استخدام كلمات المرور السابقة"}),(0,l.jsxs)(f.l6,{value:c.passwordPolicy.preventReuse.toString(),onValueChange:e=>V("passwordPolicy.preventReuse",parseInt(e)),disabled:!t,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{})}),(0,l.jsxs)(f.gC,{children:[(0,l.jsx)(f.eb,{value:"0",children:"السماح بإعادة الاستخدام"}),(0,l.jsx)(f.eb,{value:"3",children:"آخر 3 كلمات مرور"}),(0,l.jsx)(f.eb,{value:"5",children:"آخر 5 كلمات مرور"}),(0,l.jsx)(f.eb,{value:"10",children:"آخر 10 كلمات مرور"})]})]})]})]})]}),(0,l.jsxs)(P.Zp,{children:[(0,l.jsx)(P.aR,{children:(0,l.jsxs)(P.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(Q.A,{className:"w-5 h-5 text-green-600"}),"إعدادات الجلسة"]})}),(0,l.jsxs)(P.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"مدة انتهاء الجلسة (دقيقة)"}),(0,l.jsxs)(f.l6,{value:c.sessionSettings.timeout.toString(),onValueChange:e=>V("sessionSettings.timeout",parseInt(e)),disabled:!t,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{})}),(0,l.jsxs)(f.gC,{children:[(0,l.jsx)(f.eb,{value:"15",children:"15 دقيقة"}),(0,l.jsx)(f.eb,{value:"30",children:"30 دقيقة"}),(0,l.jsx)(f.eb,{value:"60",children:"ساعة واحدة"}),(0,l.jsx)(f.eb,{value:"120",children:"ساعتان"}),(0,l.jsx)(f.eb,{value:"480",children:"8 ساعات"}),(0,l.jsx)(f.eb,{value:"1440",children:"24 ساعة"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"الحد الأقصى للجلسات المتزامنة"}),(0,l.jsxs)(f.l6,{value:c.sessionSettings.maxConcurrentSessions.toString(),onValueChange:e=>V("sessionSettings.maxConcurrentSessions",parseInt(e)),disabled:!t,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{})}),(0,l.jsxs)(f.gC,{children:[(0,l.jsx)(f.eb,{value:"1",children:"جلسة واحدة فقط"}),(0,l.jsx)(f.eb,{value:"2",children:"جلستان"}),(0,l.jsx)(f.eb,{value:"3",children:"3 جلسات"}),(0,l.jsx)(f.eb,{value:"5",children:"5 جلسات"}),(0,l.jsx)(f.eb,{value:"10",children:"10 جلسات"}),(0,l.jsx)(f.eb,{value:"0",children:"بدون حد"})]})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"تذكرني"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"السماح بحفظ بيانات تسجيل الدخول"})]}),(0,l.jsx)(C,{checked:c.sessionSettings.rememberMe,onCheckedChange:e=>V("sessionSettings.rememberMe",e),disabled:!t})]}),c.sessionSettings.rememberMe&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:'مدة "تذكرني" (يوم)'}),(0,l.jsxs)(f.l6,{value:c.sessionSettings.rememberMeDuration.toString(),onValueChange:e=>V("sessionSettings.rememberMeDuration",parseInt(e)),disabled:!t,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{})}),(0,l.jsxs)(f.gC,{children:[(0,l.jsx)(f.eb,{value:"7",children:"أسبوع واحد"}),(0,l.jsx)(f.eb,{value:"30",children:"شهر واحد"}),(0,l.jsx)(f.eb,{value:"90",children:"3 أشهر"}),(0,l.jsx)(f.eb,{value:"365",children:"سنة واحدة"})]})]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"إعادة المصادقة للعمليات الحساسة"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"طلب كلمة المرور مرة أخرى"})]}),(0,l.jsx)(C,{checked:c.sessionSettings.requireReauth,onCheckedChange:e=>V("sessionSettings.requireReauth",e),disabled:!t})]})]})]})]}),(0,l.jsxs)(P.Zp,{children:[(0,l.jsx)(P.aR,{children:(0,l.jsxs)(P.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(et.A,{className:"w-5 h-5 text-purple-600"}),"إعدادات تسجيل الدخول"]})}),(0,l.jsxs)(P.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"الحد الأقصى لمحاولات الدخول الفاشلة"}),(0,l.jsxs)(f.l6,{value:c.loginSettings.maxFailedAttempts.toString(),onValueChange:e=>V("loginSettings.maxFailedAttempts",parseInt(e)),disabled:!t,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{})}),(0,l.jsxs)(f.gC,{children:[(0,l.jsx)(f.eb,{value:"3",children:"3 محاولات"}),(0,l.jsx)(f.eb,{value:"5",children:"5 محاولات"}),(0,l.jsx)(f.eb,{value:"10",children:"10 محاولات"}),(0,l.jsx)(f.eb,{value:"0",children:"بدون حد"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"مدة الحظر (دقيقة)"}),(0,l.jsxs)(f.l6,{value:c.loginSettings.lockoutDuration.toString(),onValueChange:e=>V("loginSettings.lockoutDuration",parseInt(e)),disabled:!t,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{})}),(0,l.jsxs)(f.gC,{children:[(0,l.jsx)(f.eb,{value:"5",children:"5 دقائق"}),(0,l.jsx)(f.eb,{value:"15",children:"15 دقيقة"}),(0,l.jsx)(f.eb,{value:"30",children:"30 دقيقة"}),(0,l.jsx)(f.eb,{value:"60",children:"ساعة واحدة"}),(0,l.jsx)(f.eb,{value:"1440",children:"24 ساعة"})]})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"تفعيل CAPTCHA"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"التحقق من أنك لست روبوت"})]}),(0,l.jsx)(C,{checked:c.loginSettings.enableCaptcha,onCheckedChange:e=>V("loginSettings.enableCaptcha",e),disabled:!t})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"المصادقة الثنائية"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"طبقة أمان إضافية"})]}),(0,l.jsx)(C,{checked:c.loginSettings.enableTwoFactor,onCheckedChange:e=>V("loginSettings.enableTwoFactor",e),disabled:!t})]})]})]})]}),(0,l.jsxs)(P.Zp,{children:[(0,l.jsx)(P.aR,{children:(0,l.jsxs)(P.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(ec.A,{className:"w-5 h-5 text-orange-600"}),"إعدادات التدقيق والسجلات"]})}),(0,l.jsxs)(P.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"تفعيل سجل التدقيق"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"تسجيل جميع العمليات"})]}),(0,l.jsx)(C,{checked:c.auditSettings.enableAuditLog,onCheckedChange:e=>V("auditSettings.enableAuditLog",e),disabled:!t})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"تسجيل محاولات الدخول"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"الناجحة والفاشلة"})]}),(0,l.jsx)(C,{checked:c.auditSettings.logLoginAttempts,onCheckedChange:e=>V("auditSettings.logLoginAttempts",e),disabled:!t||!c.auditSettings.enableAuditLog})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"تسجيل تغييرات البيانات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إضافة، تعديل، حذف"})]}),(0,l.jsx)(C,{checked:c.auditSettings.logDataChanges,onCheckedChange:e=>V("auditSettings.logDataChanges",e),disabled:!t||!c.auditSettings.enableAuditLog})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"تسجيل أحداث النظام"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"بدء التشغيل، الأخطاء، التحديثات"})]}),(0,l.jsx)(C,{checked:c.auditSettings.logSystemEvents,onCheckedChange:e=>V("auditSettings.logSystemEvents",e),disabled:!t||!c.auditSettings.enableAuditLog})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"التنبيهات الفورية"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إشعار فوري للأحداث المهمة"})]}),(0,l.jsx)(C,{checked:c.auditSettings.enableRealTimeAlerts,onCheckedChange:e=>V("auditSettings.enableRealTimeAlerts",e),disabled:!t||!c.auditSettings.enableAuditLog})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"مدة الاحتفاظ بالسجلات (يوم)"}),(0,l.jsxs)(f.l6,{value:c.auditSettings.retentionDays.toString(),onValueChange:e=>V("auditSettings.retentionDays",parseInt(e)),disabled:!t||!c.auditSettings.enableAuditLog,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{})}),(0,l.jsxs)(f.gC,{children:[(0,l.jsx)(f.eb,{value:"30",children:"30 يوم"}),(0,l.jsx)(f.eb,{value:"90",children:"90 يوم"}),(0,l.jsx)(f.eb,{value:"180",children:"180 يوم"}),(0,l.jsx)(f.eb,{value:"365",children:"سنة واحدة"}),(0,l.jsx)(f.eb,{value:"1095",children:"3 سنوات"}),(0,l.jsx)(f.eb,{value:"0",children:"دائماً"})]})]})]})]})]}),(0,l.jsxs)(P.Zp,{className:"border-2 border-indigo-100 shadow-lg",children:[(0,l.jsx)(P.aR,{className:"bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-t-lg",children:(0,l.jsxs)(P.ZB,{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("div",{className:"p-2 bg-white bg-opacity-20 rounded-lg",children:(0,l.jsx)(er.A,{className:"w-6 h-6 text-white"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-xl font-bold",children:"إدارة المستخدمين"}),(0,l.jsx)("p",{className:"text-indigo-100 text-sm mt-1",children:"إضافة وتعديل وحذف حسابات المستخدمين"})]})]}),(0,l.jsxs)(r.$,{variant:"outline",size:"sm",onClick:()=>p(!j),disabled:!t,className:"bg-white bg-opacity-20 border-white border-opacity-30 text-white hover:bg-white hover:bg-opacity-30",children:[j?"إخفاء":"عرض"," المستخدمين"]})]})}),j&&(0,l.jsxs)(P.Wu,{className:"space-y-6 bg-gradient-to-br from-gray-50 to-indigo-50",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center p-6 bg-white rounded-xl shadow-sm border border-gray-100",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("h3",{className:"text-xl font-bold text-gray-900 flex items-center gap-2",children:[(0,l.jsx)(_.A,{className:"w-5 h-5 text-indigo-600"}),"قائمة المستخدمين"]}),(0,l.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"إدارة حسابات المستخدمين وصلاحياتهم في النظام"}),(0,l.jsxs)("div",{className:"flex items-center gap-4 mt-2 text-xs text-gray-500",children:[(0,l.jsxs)("span",{children:["إجمالي المستخدمين: ",g.length]}),(0,l.jsx)("span",{children:"•"}),(0,l.jsxs)("span",{children:["المديرون: ",g.filter(e=>"ADMIN"===e.role).length]}),(0,l.jsx)("span",{children:"•"}),(0,l.jsxs)("span",{children:["مدخلو البيانات: ",g.filter(e=>"DATA_ENTRY"===e.role).length]}),(0,l.jsx)("span",{children:"•"}),(0,l.jsxs)("span",{children:["المطلعون: ",g.filter(e=>"VIEWER"===e.role).length]})]})]}),(0,l.jsxs)("div",{className:"flex gap-3",children:[(0,l.jsxs)(r.$,{onClick:()=>D(!0),disabled:!t||E,variant:"outline",className:"border-indigo-300 text-indigo-600 hover:bg-indigo-50",children:[(0,l.jsx)(d.A,{className:"w-4 h-4 ml-2"}),"الصلاحيات المفصلة"]}),(0,l.jsxs)(r.$,{onClick:()=>N(!0),disabled:!t||E,className:"bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300",children:[(0,l.jsx)(er.A,{className:"w-4 h-4 ml-2"}),"إضافة مستخدم"]})]})]}),E?(0,l.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,l.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"})}):(0,l.jsx)("div",{className:"space-y-4",children:0===g.length?(0,l.jsxs)("div",{className:"text-center py-12 bg-white rounded-xl border-2 border-dashed border-gray-200",children:[(0,l.jsx)("div",{className:"w-20 h-20 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,l.jsx)(_.A,{className:"w-10 h-10 text-indigo-400"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"لا توجد مستخدمين مسجلين"}),(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:"ابدأ بإضافة أول مستخدم في النظام"}),(0,l.jsxs)(r.$,{onClick:()=>N(!0),disabled:!t||E,className:"bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white",children:[(0,l.jsx)(er.A,{className:"w-4 h-4 ml-2"}),"إضافة أول مستخدم"]})]}):g.map(e=>(0,l.jsxs)("div",{className:"group flex items-center justify-between p-6 border border-gray-200 rounded-xl bg-white hover:bg-gradient-to-r hover:from-indigo-50 hover:to-blue-50 hover:border-indigo-200 transition-all duration-300 shadow-sm hover:shadow-md",children:[(0,l.jsx)("div",{className:"flex-1",children:(0,l.jsxs)("div",{className:"flex items-center gap-4",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300",children:(0,l.jsx)("span",{className:"text-white font-bold text-lg",children:e.name.charAt(0).toUpperCase()})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-bold text-gray-900 text-lg group-hover:text-indigo-700 transition-colors duration-300",children:e.name}),(0,l.jsxs)("div",{className:"flex items-center gap-6 text-sm text-gray-600 mt-1",children:[(0,l.jsxs)("span",{className:"flex items-center gap-2",children:[(0,l.jsx)(en.A,{className:"w-4 h-4 text-indigo-500"}),e.email]}),e.phone&&(0,l.jsxs)("span",{className:"flex items-center gap-2",children:[(0,l.jsx)(ed.A,{className:"w-4 h-4 text-green-500"}),e.phone]}),(0,l.jsxs)("span",{className:"text-xs text-gray-500",children:["انضم في ",new Date(e.createdAt).toLocaleDateString("ar-SA")]})]})]})]})}),(0,l.jsxs)("div",{className:"flex items-center gap-4",children:[(0,l.jsx)(n.E,{className:"".concat(W(e.role)," px-3 py-1 text-sm font-semibold"),children:L(e.role)}),t&&(0,l.jsxs)("div",{className:"flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:[(0,l.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>A(e),disabled:E,className:"bg-blue-50 border-blue-200 text-blue-600 hover:bg-blue-100 hover:border-blue-300",children:(0,l.jsx)(eo.A,{className:"w-4 h-4"})}),(0,l.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>T(e.id,e.name),disabled:E,className:"bg-red-50 border-red-200 text-red-600 hover:bg-red-100 hover:border-red-300",children:(0,l.jsx)(ex.A,{className:"w-4 h-4"})})]})]})]},e.id))})]})]}),(0,l.jsxs)(P.Zp,{children:[(0,l.jsx)(P.aR,{children:(0,l.jsxs)(P.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(_.A,{className:"w-5 h-5 text-red-600"}),"إعدادات الصلاحيات"]})}),(0,l.jsxs)(P.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"الدور الافتراضي للمستخدمين الجدد"}),(0,l.jsxs)(f.l6,{value:c.permissionSettings.defaultRole,onValueChange:e=>V("permissionSettings.defaultRole",e),disabled:!t,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{})}),(0,l.jsxs)(f.gC,{children:[(0,l.jsx)(f.eb,{value:"VIEWER",children:"مطلع (عرض فقط)"}),(0,l.jsx)(f.eb,{value:"DATA_ENTRY",children:"مدخل بيانات"}),(0,l.jsx)(f.eb,{value:"ADMIN",children:"مدير (غير مستحسن)"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"السماح بالتسجيل الذاتي"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"المستخدمون يمكنهم إنشاء حسابات"})]}),(0,l.jsx)(C,{checked:c.permissionSettings.allowSelfRegistration,onCheckedChange:e=>V("permissionSettings.allowSelfRegistration",e),disabled:!t})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"يتطلب موافقة المدير"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"الحسابات الجديدة تحتاج موافقة"})]}),(0,l.jsx)(C,{checked:c.permissionSettings.requireAdminApproval,onCheckedChange:e=>V("permissionSettings.requireAdminApproval",e),disabled:!t||!c.permissionSettings.allowSelfRegistration})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"تفعيل التسلسل الهرمي للأدوار"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"المدير يمكنه إدارة جميع الأدوار"})]}),(0,l.jsx)(C,{checked:c.permissionSettings.enableRoleHierarchy,onCheckedChange:e=>V("permissionSettings.enableRoleHierarchy",e),disabled:!t})]})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)(y.J,{children:"الحد الأقصى للمستخدمين لكل دور"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"maxAdmins",children:"المديرون"}),(0,l.jsx)(v.p,{id:"maxAdmins",type:"number",min:"1",max:"10",value:c.permissionSettings.maxUsersPerRole.ADMIN,onChange:e=>V("permissionSettings.maxUsersPerRole.ADMIN",parseInt(e.target.value)),disabled:!t})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"maxDataEntry",children:"مدخلو البيانات"}),(0,l.jsx)(v.p,{id:"maxDataEntry",type:"number",min:"1",max:"50",value:c.permissionSettings.maxUsersPerRole.DATA_ENTRY,onChange:e=>V("permissionSettings.maxUsersPerRole.DATA_ENTRY",parseInt(e.target.value)),disabled:!t})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"maxViewers",children:"المطلعون"}),(0,l.jsx)(v.p,{id:"maxViewers",type:"number",min:"1",max:"1000",value:c.permissionSettings.maxUsersPerRole.VIEWER,onChange:e=>V("permissionSettings.maxUsersPerRole.VIEWER",parseInt(e.target.value)),disabled:!t})]})]})]})]})]}),(0,l.jsxs)(P.Zp,{children:[(0,l.jsx)(P.aR,{children:(0,l.jsxs)(P.ZB,{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(m.A,{className:"w-5 h-5 text-red-600"}),"إعدادات الأمان المتقدمة"]}),(0,l.jsxs)(r.$,{variant:"outline",size:"sm",onClick:()=>h(!x),children:[x?"إخفاء":"عرض"," المتقدم"]})]})}),x&&(0,l.jsxs)(P.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"تشفير البيانات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"تشفير البيانات الحساسة"})]}),(0,l.jsx)(C,{checked:c.advancedSecurity.enableEncryption,onCheckedChange:e=>V("advancedSecurity.enableEncryption",e),disabled:!t})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"إجبار SSL/HTTPS"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"اتصال آمن فقط"})]}),(0,l.jsx)(C,{checked:c.advancedSecurity.enableSSL,onCheckedChange:e=>V("advancedSecurity.enableSSL",e),disabled:!t})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"حماية CSRF"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"منع هجمات التزوير"})]}),(0,l.jsx)(C,{checked:c.advancedSecurity.enableCSRF,onCheckedChange:e=>V("advancedSecurity.enableCSRF",e),disabled:!t})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"حماية XSS"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"منع هجمات البرمجة النصية"})]}),(0,l.jsx)(C,{checked:c.advancedSecurity.enableXSS,onCheckedChange:e=>V("advancedSecurity.enableXSS",e),disabled:!t})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"حماية SQL Injection"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"منع هجمات قواعد البيانات"})]}),(0,l.jsx)(C,{checked:c.advancedSecurity.enableSQLInjection,onCheckedChange:e=>V("advancedSecurity.enableSQLInjection",e),disabled:!t})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"تحديد معدل الطلبات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"منع الهجمات المكثفة"})]}),(0,l.jsx)(C,{checked:c.advancedSecurity.enableRateLimit,onCheckedChange:e=>V("advancedSecurity.enableRateLimit",e),disabled:!t})]})]}),c.advancedSecurity.enableRateLimit&&(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"rateLimitRequests",children:"عدد الطلبات المسموحة"}),(0,l.jsx)(v.p,{id:"rateLimitRequests",type:"number",min:"10",max:"1000",value:c.advancedSecurity.rateLimitRequests,onChange:e=>V("advancedSecurity.rateLimitRequests",parseInt(e.target.value)),disabled:!t})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"rateLimitWindow",children:"النافزة الزمنية (دقيقة)"}),(0,l.jsx)(v.p,{id:"rateLimitWindow",type:"number",min:"1",max:"60",value:c.advancedSecurity.rateLimitWindow,onChange:e=>V("advancedSecurity.rateLimitWindow",parseInt(e.target.value)),disabled:!t})]})]})]})]}),b&&(0,l.jsxs)(P.Zp,{className:"border-2 border-indigo-200 bg-gradient-to-br from-indigo-50 to-blue-50 shadow-xl",children:[(0,l.jsx)(P.aR,{className:"bg-gradient-to-r from-indigo-500 to-indigo-600 text-white rounded-t-lg",children:(0,l.jsxs)(P.ZB,{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("div",{className:"p-2 bg-white bg-opacity-20 rounded-lg",children:(0,l.jsx)(er.A,{className:"w-6 h-6 text-white"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-xl font-bold",children:"إضافة مستخدم جديد"}),(0,l.jsx)("p",{className:"text-indigo-100 text-sm mt-1",children:"إنشاء حساب مستخدم جديد في النظام"})]})]}),(0,l.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>N(!1),className:"bg-white bg-opacity-20 border-white border-opacity-30 text-white hover:bg-white hover:bg-opacity-30",children:"إلغاء"})]})}),(0,l.jsxs)(P.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"newUserName",children:"الاسم الكامل *"}),(0,l.jsx)(v.p,{id:"newUserName",value:w.name,onChange:e=>k({...w,name:e.target.value}),placeholder:"أدخل الاسم الكامل",disabled:E})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"newUserEmail",children:"البريد الإلكتروني *"}),(0,l.jsx)(v.p,{id:"newUserEmail",type:"email",value:w.email,onChange:e=>k({...w,email:e.target.value}),placeholder:"أدخل البريد الإلكتروني",disabled:E})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"newUserPhone",children:"رقم الهاتف"}),(0,l.jsx)(v.p,{id:"newUserPhone",value:w.phone,onChange:e=>k({...w,phone:e.target.value}),placeholder:"أدخل رقم الهاتف",disabled:E})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"newUserRole",children:"الصلاحية *"}),(0,l.jsxs)(f.l6,{value:w.role,onValueChange:e=>k({...w,role:e}),disabled:E,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{})}),(0,l.jsxs)(f.gC,{children:[(0,l.jsx)(f.eb,{value:"VIEWER",children:"مطلع (عرض فقط)"}),(0,l.jsx)(f.eb,{value:"DATA_ENTRY",children:"مدخل بيانات"}),(0,l.jsx)(f.eb,{value:"MEMBER_VIEWER",children:"مطلع على عضو معين"}),(0,l.jsx)(f.eb,{value:"GALLERY_VIEWER",children:"مطلع على المعرض فقط"}),(0,l.jsx)(f.eb,{value:"ADMIN",children:"مدير"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"newUserPassword",children:"كلمة المرور *"}),(0,l.jsx)(v.p,{id:"newUserPassword",type:"password",value:w.password,onChange:e=>k({...w,password:e.target.value}),placeholder:"أدخل كلمة المرور",disabled:E})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"newUserConfirmPassword",children:"تأكيد كلمة المرور *"}),(0,l.jsx)(v.p,{id:"newUserConfirmPassword",type:"password",value:w.confirmPassword,onChange:e=>k({...w,confirmPassword:e.target.value}),placeholder:"أعد إدخال كلمة المرور",disabled:E})]})]}),(0,l.jsxs)("div",{className:"flex justify-end gap-3 pt-4",children:[(0,l.jsx)(r.$,{variant:"outline",onClick:()=>N(!1),disabled:E,children:"إلغاء"}),(0,l.jsxs)(r.$,{onClick:F,disabled:E||!w.name||!w.email||!w.password,className:"bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white",children:[E?(0,l.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"}):(0,l.jsx)(er.A,{className:"w-4 h-4 ml-2"}),"إضافة المستخدم"]})]})]})]}),S&&(0,l.jsxs)(P.Zp,{className:"border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50 shadow-xl",children:[(0,l.jsx)(P.aR,{className:"bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-t-lg",children:(0,l.jsxs)(P.ZB,{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("div",{className:"p-2 bg-white bg-opacity-20 rounded-lg",children:(0,l.jsx)(eo.A,{className:"w-6 h-6 text-white"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-xl font-bold",children:"تعديل المستخدم"}),(0,l.jsx)("p",{className:"text-blue-100 text-sm mt-1",children:S.name})]})]}),(0,l.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>A(null),className:"bg-white bg-opacity-20 border-white border-opacity-30 text-white hover:bg-white hover:bg-opacity-30",children:"إلغاء"})]})}),(0,l.jsxs)(P.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"editUserName",children:"الاسم الكامل"}),(0,l.jsx)(v.p,{id:"editUserName",value:S.name,onChange:e=>A({...S,name:e.target.value}),disabled:E})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"editUserEmail",children:"البريد الإلكتروني"}),(0,l.jsx)(v.p,{id:"editUserEmail",type:"email",value:S.email,onChange:e=>A({...S,email:e.target.value}),disabled:E})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"editUserPhone",children:"رقم الهاتف"}),(0,l.jsx)(v.p,{id:"editUserPhone",value:S.phone||"",onChange:e=>A({...S,phone:e.target.value}),disabled:E})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"editUserRole",children:"الصلاحية"}),(0,l.jsxs)(f.l6,{value:S.role,onValueChange:e=>A({...S,role:e}),disabled:E,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{})}),(0,l.jsxs)(f.gC,{children:[(0,l.jsx)(f.eb,{value:"VIEWER",children:"مطلع (عرض فقط)"}),(0,l.jsx)(f.eb,{value:"DATA_ENTRY",children:"مدخل بيانات"}),(0,l.jsx)(f.eb,{value:"MEMBER_VIEWER",children:"مطلع على عضو معين"}),(0,l.jsx)(f.eb,{value:"GALLERY_VIEWER",children:"مطلع على المعرض فقط"}),(0,l.jsx)(f.eb,{value:"ADMIN",children:"مدير"})]})]})]})]}),(0,l.jsxs)("div",{className:"flex justify-end gap-3 pt-4",children:[(0,l.jsx)(r.$,{variant:"outline",onClick:()=>A(null),disabled:E,children:"إلغاء"}),(0,l.jsxs)(r.$,{onClick:()=>I(S.id,{name:S.name,email:S.email,phone:S.phone,role:S.role}),disabled:E,className:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white",children:[E?(0,l.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"}):(0,l.jsx)(em.A,{className:"w-4 h-4 ml-2"}),"حفظ التغييرات"]})]})]})]}),R&&(0,l.jsx)(el,{users:g,onClose:()=>D(!1)})]})}var ep=a(55863);let eg=i.forwardRef((e,s)=>{let{className:a,value:i,...t}=e;return(0,l.jsx)(ep.bL,{ref:s,className:(0,w.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",a),...t,children:(0,l.jsx)(ep.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(i||0),"%)")}})})});eg.displayName=ep.bL.displayName;var eu=a(95880),eb=a(53904),ev=a(39022),ey=a(50589),eN=a(91788);let ef={autoBackup:{enabled:!0,frequency:"daily",time:"02:00",retentionDays:30,includeFiles:!0,includeDatabase:!0,includeSettings:!0},storage:{location:"local",localPath:"./backups",cloudProvider:"",cloudCredentials:{accessKey:"",secretKey:"",bucket:"",region:""}},importExport:{allowDataExport:!0,allowDataImport:!0,exportFormats:["json","csv","xlsx"],maxFileSize:100,requireConfirmation:!0},database:{enableOptimization:!0,autoVacuum:!0,compressionLevel:6,encryptBackups:!0}};function ew(e){let{settings:s,onChange:a,canEdit:t}=e,[c,o]=(0,i.useState)(ef),[x,m]=(0,i.useState)(0),[j,p]=(0,i.useState)(!1),[g,u]=(0,i.useState)(null),[b,N]=(0,i.useState)("0 MB");(0,i.useEffect)(()=>{s&&o({...ef,...s}),k()},[s]);let w=(e,s)=>{let l=e.split("."),i={...c};1===l.length?i={...i,[l[0]]:s}:2===l.length?i={...i,[l[0]]:{...i[l[0]],[l[1]]:s}}:3===l.length&&(i={...i,[l[0]]:{...i[l[0]],[l[1]]:{...i[l[0]][l[1]],[l[2]]:s}}}),o(i),a(i)},k=async()=>{try{u(new Date(Date.now()-864e5)),N("45.2 MB")}catch(e){console.error("خطأ في تحميل معلومات النسخ الاحتياطية:",e)}},S=async()=>{if(t){p(!0),m(0);try{for(let e=0;e<=100;e+=10)m(e),await new Promise(e=>setTimeout(e,200));u(new Date),alert("تم إنشاء النسخة الاحتياطية بنجاح!")}catch(e){alert("فشل في إنشاء النسخة الاحتياطية")}finally{p(!1),m(0)}}},J=async()=>{if(t&&confirm("هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية."))try{alert("تم استعادة النسخة الاحتياطية بنجاح!")}catch(e){alert("فشل في استعادة النسخة الاحتياطية")}},R=async e=>{if(t)try{let s=document.createElement("a");s.href="/api/export?format=".concat(e),s.download="diwan-data.".concat(e),s.click()}catch(e){alert("فشل في تصدير البيانات")}};return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)(P.Zp,{children:[(0,l.jsx)(P.aR,{children:(0,l.jsxs)(P.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(h.A,{className:"w-5 h-5 text-blue-600"}),"معلومات النسخ الاحتياطية"]})}),(0,l.jsxs)(P.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,l.jsx)(E.A,{className:"w-8 h-8 text-blue-600 mx-auto mb-2"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"آخر نسخة احتياطية"}),(0,l.jsx)("p",{className:"font-semibold",children:g?g.toLocaleDateString("ar-SA"):"لا توجد"})]}),(0,l.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,l.jsx)(eu.A,{className:"w-8 h-8 text-green-600 mx-auto mb-2"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"حجم النسخة الاحتياطية"}),(0,l.jsx)("p",{className:"font-semibold",children:b})]}),(0,l.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,l.jsx)(em.A,{className:"w-8 h-8 text-green-600 mx-auto mb-2"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"الحالة"}),(0,l.jsx)(n.E,{variant:"outline",className:"text-green-600 border-green-200",children:"جاهز"})]})]}),j&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("span",{className:"text-sm text-gray-600",children:"جاري إنشاء النسخة الاحتياطية..."}),(0,l.jsxs)("span",{className:"text-sm font-medium",children:[x,"%"]})]}),(0,l.jsx)(eg,{value:x,className:"w-full"})]}),(0,l.jsxs)("div",{className:"flex gap-3",children:[(0,l.jsxs)(r.$,{onClick:S,disabled:!t||j,className:"bg-blue-600 hover:bg-blue-700",children:[j?(0,l.jsx)(eb.A,{className:"w-4 h-4 ml-2 animate-spin"}):(0,l.jsx)(ev.A,{className:"w-4 h-4 ml-2"}),"إنشاء نسخة احتياطية"]}),(0,l.jsxs)(r.$,{variant:"outline",onClick:J,disabled:!t||!g,children:[(0,l.jsx)(q.A,{className:"w-4 h-4 ml-2"}),"استعادة النسخة الاحتياطية"]})]})]})]}),(0,l.jsxs)(P.Zp,{children:[(0,l.jsx)(P.aR,{children:(0,l.jsxs)(P.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(Q.A,{className:"w-5 h-5 text-green-600"}),"النسخ الاحتياطي التلقائي"]})}),(0,l.jsxs)(P.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"تفعيل النسخ الاحتياطي التلقائي"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إنشاء نسخ احتياطية تلقائياً"})]}),(0,l.jsx)(C,{checked:c.autoBackup.enabled,onCheckedChange:e=>w("autoBackup.enabled",e),disabled:!t})]}),c.autoBackup.enabled&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"تكرار النسخ الاحتياطي"}),(0,l.jsxs)(f.l6,{value:c.autoBackup.frequency,onValueChange:e=>w("autoBackup.frequency",e),disabled:!t,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{})}),(0,l.jsxs)(f.gC,{children:[(0,l.jsx)(f.eb,{value:"daily",children:"يومياً"}),(0,l.jsx)(f.eb,{value:"weekly",children:"أسبوعياً"}),(0,l.jsx)(f.eb,{value:"monthly",children:"شهرياً"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"backupTime",children:"وقت النسخ الاحتياطي"}),(0,l.jsx)(v.p,{id:"backupTime",type:"time",value:c.autoBackup.time,onChange:e=>w("autoBackup.time",e.target.value),disabled:!t})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"مدة الاحتفاظ بالنسخ الاحتياطية (يوم)"}),(0,l.jsxs)(f.l6,{value:c.autoBackup.retentionDays.toString(),onValueChange:e=>w("autoBackup.retentionDays",parseInt(e)),disabled:!t,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{})}),(0,l.jsxs)(f.gC,{children:[(0,l.jsx)(f.eb,{value:"7",children:"أسبوع واحد"}),(0,l.jsx)(f.eb,{value:"30",children:"شهر واحد"}),(0,l.jsx)(f.eb,{value:"90",children:"3 أشهر"}),(0,l.jsx)(f.eb,{value:"365",children:"سنة واحدة"}),(0,l.jsx)(f.eb,{value:"0",children:"دائماً"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)(y.J,{children:"محتويات النسخة الاحتياطية"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"قاعدة البيانات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"جميع البيانات والجداول"})]}),(0,l.jsx)(C,{checked:c.autoBackup.includeDatabase,onCheckedChange:e=>w("autoBackup.includeDatabase",e),disabled:!t})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"الملفات المرفوعة"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"الصور والمستندات"})]}),(0,l.jsx)(C,{checked:c.autoBackup.includeFiles,onCheckedChange:e=>w("autoBackup.includeFiles",e),disabled:!t})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"إعدادات النظام"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"التكوينات والإعدادات"})]}),(0,l.jsx)(C,{checked:c.autoBackup.includeSettings,onCheckedChange:e=>w("autoBackup.includeSettings",e),disabled:!t})]})]})]})]})]})]}),(0,l.jsxs)(P.Zp,{children:[(0,l.jsx)(P.aR,{children:(0,l.jsxs)(P.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(ey.A,{className:"w-5 h-5 text-purple-600"}),"إعدادات التخزين"]})}),(0,l.jsxs)(P.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"موقع التخزين"}),(0,l.jsxs)(f.l6,{value:c.storage.location,onValueChange:e=>w("storage.location",e),disabled:!t,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{})}),(0,l.jsxs)(f.gC,{children:[(0,l.jsx)(f.eb,{value:"local",children:"محلي فقط"}),(0,l.jsx)(f.eb,{value:"cloud",children:"سحابي فقط"}),(0,l.jsx)(f.eb,{value:"both",children:"محلي وسحابي"})]})]})]}),("local"===c.storage.location||"both"===c.storage.location)&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"localPath",children:"مسار التخزين المحلي"}),(0,l.jsx)(v.p,{id:"localPath",value:c.storage.localPath,onChange:e=>w("storage.localPath",e.target.value),disabled:!t,placeholder:"./backups"})]}),("cloud"===c.storage.location||"both"===c.storage.location)&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"مزود الخدمة السحابية"}),(0,l.jsxs)(f.l6,{value:c.storage.cloudProvider,onValueChange:e=>w("storage.cloudProvider",e),disabled:!t,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{placeholder:"اختر مزود الخدمة"})}),(0,l.jsxs)(f.gC,{children:[(0,l.jsx)(f.eb,{value:"aws",children:"Amazon S3"}),(0,l.jsx)(f.eb,{value:"google",children:"Google Cloud Storage"}),(0,l.jsx)(f.eb,{value:"azure",children:"Microsoft Azure"}),(0,l.jsx)(f.eb,{value:"digitalocean",children:"DigitalOcean Spaces"})]})]})]}),c.storage.cloudProvider&&(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"accessKey",children:"مفتاح الوصول"}),(0,l.jsx)(v.p,{id:"accessKey",type:"password",value:c.storage.cloudCredentials.accessKey,onChange:e=>w("storage.cloudCredentials.accessKey",e.target.value),disabled:!t,placeholder:"Access Key"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"secretKey",children:"المفتاح السري"}),(0,l.jsx)(v.p,{id:"secretKey",type:"password",value:c.storage.cloudCredentials.secretKey,onChange:e=>w("storage.cloudCredentials.secretKey",e.target.value),disabled:!t,placeholder:"Secret Key"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"bucket",children:"اسم الحاوية"}),(0,l.jsx)(v.p,{id:"bucket",value:c.storage.cloudCredentials.bucket,onChange:e=>w("storage.cloudCredentials.bucket",e.target.value),disabled:!t,placeholder:"bucket-name"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"region",children:"المنطقة"}),(0,l.jsx)(v.p,{id:"region",value:c.storage.cloudCredentials.region,onChange:e=>w("storage.cloudCredentials.region",e.target.value),disabled:!t,placeholder:"us-east-1"})]})]})]})]})]}),(0,l.jsxs)(P.Zp,{children:[(0,l.jsx)(P.aR,{children:(0,l.jsxs)(P.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(A.A,{className:"w-5 h-5 text-orange-600"}),"الاستيراد والتصدير"]})}),(0,l.jsxs)(P.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"السماح بتصدير البيانات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"تمكين المستخدمين من تصدير البيانات"})]}),(0,l.jsx)(C,{checked:c.importExport.allowDataExport,onCheckedChange:e=>w("importExport.allowDataExport",e),disabled:!t})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"السماح باستيراد البيانات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"تمكين المستخدمين من استيراد البيانات"})]}),(0,l.jsx)(C,{checked:c.importExport.allowDataImport,onCheckedChange:e=>w("importExport.allowDataImport",e),disabled:!t})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"يتطلب تأكيد"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"طلب تأكيد قبل الاستيراد/التصدير"})]}),(0,l.jsx)(C,{checked:c.importExport.requireConfirmation,onCheckedChange:e=>w("importExport.requireConfirmation",e),disabled:!t})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{htmlFor:"maxFileSize",children:"الحد الأقصى لحجم الملف (MB)"}),(0,l.jsx)(v.p,{id:"maxFileSize",type:"number",min:"1",max:"1000",value:c.importExport.maxFileSize,onChange:e=>w("importExport.maxFileSize",parseInt(e.target.value)),disabled:!t})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)(y.J,{children:"تصدير البيانات"}),(0,l.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2",children:[(0,l.jsxs)(r.$,{variant:"outline",onClick:()=>R("json"),disabled:!t||!c.importExport.allowDataExport,className:"flex items-center gap-2",children:[(0,l.jsx)(eN.A,{className:"w-4 h-4"}),"JSON"]}),(0,l.jsxs)(r.$,{variant:"outline",onClick:()=>R("csv"),disabled:!t||!c.importExport.allowDataExport,className:"flex items-center gap-2",children:[(0,l.jsx)(eN.A,{className:"w-4 h-4"}),"CSV"]}),(0,l.jsxs)(r.$,{variant:"outline",onClick:()=>R("xlsx"),disabled:!t||!c.importExport.allowDataExport,className:"flex items-center gap-2",children:[(0,l.jsx)(eN.A,{className:"w-4 h-4"}),"Excel"]}),(0,l.jsxs)(r.$,{variant:"outline",onClick:()=>R("pdf"),disabled:!t||!c.importExport.allowDataExport,className:"flex items-center gap-2",children:[(0,l.jsx)(eN.A,{className:"w-4 h-4"}),"PDF"]})]})]})]})]}),(0,l.jsxs)(P.Zp,{children:[(0,l.jsx)(P.aR,{children:(0,l.jsxs)(P.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(d.A,{className:"w-5 h-5 text-red-600"}),"إعدادات قاعدة البيانات"]})}),(0,l.jsxs)(P.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"تحسين قاعدة البيانات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"تحسين الأداء تلقائياً"})]}),(0,l.jsx)(C,{checked:c.database.enableOptimization,onCheckedChange:e=>w("database.enableOptimization",e),disabled:!t})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"التنظيف التلقائي"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"إزالة البيانات المحذوفة تلقائياً"})]}),(0,l.jsx)(C,{checked:c.database.autoVacuum,onCheckedChange:e=>w("database.autoVacuum",e),disabled:!t})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(y.J,{children:"تشفير النسخ الاحتياطية"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"حماية النسخ الاحتياطية بالتشفير"})]}),(0,l.jsx)(C,{checked:c.database.encryptBackups,onCheckedChange:e=>w("database.encryptBackups",e),disabled:!t})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"مستوى الضغط"}),(0,l.jsxs)(f.l6,{value:c.database.compressionLevel.toString(),onValueChange:e=>w("database.compressionLevel",parseInt(e)),disabled:!t,children:[(0,l.jsx)(f.bq,{children:(0,l.jsx)(f.yv,{})}),(0,l.jsxs)(f.gC,{children:[(0,l.jsx)(f.eb,{value:"1",children:"منخفض (سريع)"}),(0,l.jsx)(f.eb,{value:"3",children:"متوسط"}),(0,l.jsx)(f.eb,{value:"6",children:"عالي (افتراضي)"}),(0,l.jsx)(f.eb,{value:"9",children:"أقصى (بطيء)"})]})]})]}),(0,l.jsx)("div",{className:"p-4 bg-amber-50 border border-amber-200 rounded-lg",children:(0,l.jsxs)("div",{className:"flex items-start gap-3",children:[(0,l.jsx)(K.A,{className:"w-5 h-5 text-amber-600 mt-0.5"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-amber-800 font-medium",children:"تحذير مهم"}),(0,l.jsx)("p",{className:"text-amber-700 text-sm mt-1",children:"تأكد من إنشاء نسخة احتياطية قبل تغيير إعدادات قاعدة البيانات. بعض التغييرات قد تتطلب إعادة تشغيل النظام."})]})]})})]})]})]})}var eC=a(54165);function ek(e){let{onExportSettings:s,onImportSettings:a,onResetSettings:t,canEdit:c}=e,[d,o]=(0,i.useState)(null),[x,m]=(0,i.useState)(!1),[j,g]=(0,i.useState)([{id:1,timestamp:new Date(Date.now()-72e5),user:"أحمد محمد",action:"تحديث إعدادات المظهر",details:"تغيير اللون الأساسي إلى الأزرق",category:"appearance"},{id:2,timestamp:new Date(Date.now()-864e5),user:"سارة أحمد",action:"تحديث إعدادات الإشعارات",details:"تفعيل إشعارات البريد الإلكتروني",category:"notifications"},{id:3,timestamp:new Date(Date.now()-2592e5),user:"محمد علي",action:"تحديث إعدادات الأمان",details:"تغيير سياسة كلمات المرور",category:"security"}]),u=e=>{let s={general:{label:"عام",color:"bg-blue-100 text-blue-800"},appearance:{label:"مظهر",color:"bg-purple-100 text-purple-800"},notifications:{label:"إشعارات",color:"bg-green-100 text-green-800"},security:{label:"أمان",color:"bg-red-100 text-red-800"},backup:{label:"نسخ احتياطي",color:"bg-orange-100 text-orange-800"}},a=s[e]||s.general;return(0,l.jsx)(n.E,{className:a.color,children:a.label})};return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"diwan-card",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,l.jsx)("div",{className:"p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg",children:(0,l.jsx)(A.A,{className:"w-5 h-5 text-white"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"استيراد وتصدير الإعدادات"})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("h4",{className:"font-medium",children:"تصدير الإعدادات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"احفظ نسخة من إعداداتك الحالية كملف JSON"}),(0,l.jsxs)(r.$,{onClick:()=>{s(),b.oR.success("تم تصدير الإعدادات بنجاح")},disabled:!c,className:"w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg",children:[(0,l.jsx)(eN.A,{className:"w-4 h-4 ml-2"}),"تصدير الإعدادات"]})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("h4",{className:"font-medium",children:"استيراد الإعدادات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"استعد إعداداتك من ملف JSON محفوظ مسبقاً"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(v.p,{type:"file",accept:".json",onChange:e=>{var s;let a=null==(s=e.target.files)?void 0:s[0];a&&o(a)},disabled:!c}),(0,l.jsxs)(r.$,{onClick:()=>{d&&(a(d),o(null),b.oR.success("تم استيراد الإعدادات بنجاح"))},disabled:!c||!d,className:"w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-lg disabled:opacity-50",children:[(0,l.jsx)(q.A,{className:"w-4 h-4 ml-2"}),"استيراد الإعدادات"]})]})]})]}),(0,l.jsx)("div",{className:"p-4 bg-amber-50 border border-amber-200 rounded-lg",children:(0,l.jsxs)("div",{className:"flex items-start gap-3",children:[(0,l.jsx)(K.A,{className:"w-5 h-5 text-amber-600 mt-0.5"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-amber-800 font-medium",children:"تحذير"}),(0,l.jsx)("p",{className:"text-amber-700 text-sm mt-1",children:"استيراد الإعدادات سيستبدل جميع الإعدادات الحالية. تأكد من تصدير إعداداتك الحالية أولاً كنسخة احتياطية."})]})]})})]})]}),(0,l.jsxs)("div",{className:"diwan-card",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,l.jsx)("div",{className:"p-2 bg-gradient-to-r from-red-500 to-red-600 rounded-lg",children:(0,l.jsx)(p.A,{className:"w-5 h-5 text-white"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"إعادة تعيين الإعدادات"})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("p",{className:"text-gray-600",children:"إعادة تعيين جميع الإعدادات إلى القيم الافتراضية. هذا الإجراء لا يمكن التراجع عنه."}),(0,l.jsx)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,l.jsxs)("div",{className:"flex items-start gap-3",children:[(0,l.jsx)(K.A,{className:"w-5 h-5 text-red-600 mt-0.5"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-red-800 font-medium",children:"تحذير شديد"}),(0,l.jsx)("p",{className:"text-red-700 text-sm mt-1",children:"سيتم حذف جميع الإعدادات المخصصة وإعادة تعيينها إلى القيم الافتراضية. تأكد من تصدير إعداداتك أولاً إذا كنت تريد الاحتفاظ بها."})]})]})}),(0,l.jsxs)(r.$,{onClick:()=>{confirm("هل أنت متأكد من إعادة تعيين جميع الإعدادات؟ لا يمكن التراجع عن هذا الإجراء.")&&(t(),b.oR.success("تم إعادة تعيين الإعدادات بنجاح"))},disabled:!c,variant:"destructive",className:"w-full md:w-auto",children:[(0,l.jsx)(p.A,{className:"w-4 h-4 ml-2"}),"إعادة تعيين جميع الإعدادات"]})]})]}),(0,l.jsxs)("div",{className:"diwan-card",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("div",{className:"p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg",children:(0,l.jsx)(ec.A,{className:"w-5 h-5 text-white"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"سجل التغييرات"})]}),(0,l.jsxs)(eC.lG,{open:x,onOpenChange:m,children:[(0,l.jsx)(eC.zM,{asChild:!0,children:(0,l.jsx)(r.$,{variant:"outline",size:"sm",children:"عرض السجل الكامل"})}),(0,l.jsxs)(eC.Cf,{className:"max-w-[50vw] max-h-[80vh] overflow-y-auto",children:[(0,l.jsx)(eC.c7,{children:(0,l.jsx)(eC.L3,{children:"سجل التغييرات الكامل"})}),(0,l.jsx)("div",{className:"space-y-4",children:j.map(e=>(0,l.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[u(e.category),(0,l.jsx)("span",{className:"font-medium",children:e.action})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-500",children:[(0,l.jsx)(Q.A,{className:"w-4 h-4"}),e.timestamp.toLocaleString("ar-SA")]})]}),(0,l.jsx)("p",{className:"text-gray-600 mb-2",children:e.details}),(0,l.jsxs)("p",{className:"text-sm text-gray-500",children:["بواسطة: ",e.user]})]},e.id))})]})]})]}),(0,l.jsx)("div",{children:(0,l.jsx)("div",{className:"space-y-3",children:j.slice(0,3).map(e=>(0,l.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[u(e.category),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium text-sm",children:e.action}),(0,l.jsx)("p",{className:"text-xs text-gray-600",children:e.details})]})]}),(0,l.jsxs)("div",{className:"text-right",children:[(0,l.jsx)("p",{className:"text-xs text-gray-500",children:e.user}),(0,l.jsx)("p",{className:"text-xs text-gray-400",children:e.timestamp.toLocaleDateString("ar-SA")})]})]},e.id))})})]}),(0,l.jsxs)("div",{className:"diwan-card",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,l.jsx)("div",{className:"p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg",children:(0,l.jsx)(h.A,{className:"w-5 h-5 text-white"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"معلومات النظام"})]}),(0,l.jsx)("div",{children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"إصدار النظام"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"1.0.0"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"آخر تحديث"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"2024-12-21"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"قاعدة البيانات"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"SQLite"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(y.J,{children:"حالة النظام"}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(em.A,{className:"w-4 h-4 text-green-600"}),(0,l.jsx)("span",{className:"text-sm text-green-600",children:"يعمل بشكل طبيعي"})]})]})]})})]})]})}function eS(){var e;let{data:s}=(0,t.useSession)(),[a,v]=(0,i.useState)("general"),[y,N]=(0,i.useState)({general:{},appearance:{},notifications:{},security:{},backup:{},advanced:{}}),[f,w]=(0,i.useState)(!0),[C,k]=(0,i.useState)(!1),[S,A]=(0,i.useState)(!1),E=(null==s||null==(e=s.user)?void 0:e.role)==="ADMIN";(0,i.useEffect)(()=>{J()},[]);let J=async()=>{try{w(!0);let e=await fetch("/api/settings");if(e.ok){let s=await e.json();N(s)}}catch(e){console.error("خطأ في تحميل الإعدادات:",e),b.oR.error("فشل في تحميل الإعدادات")}finally{w(!1)}},R=async()=>{if(!E)return void b.oR.error("ليس لديك صلاحية لحفظ الإعدادات");try{if(k(!0),(await fetch("/api/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(y)})).ok)b.oR.success("تم حفظ الإعدادات بنجاح"),A(!1),window.location.reload();else throw Error("فشل في حفظ الإعدادات")}catch(e){console.error("خطأ في حفظ الإعدادات:",e),b.oR.error("فشل في حفظ الإعدادات")}finally{k(!1)}},D=async()=>{if(!E)return void b.oR.error("ليس لديك صلاحية لإعادة تعيين الإعدادات");if(confirm("هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟"))try{if((await fetch("/api/settings/reset",{method:"POST"})).ok)b.oR.success("تم إعادة تعيين الإعدادات بنجاح"),J(),A(!1);else throw Error("فشل في إعادة تعيين الإعدادات")}catch(e){console.error("خطأ في إعادة تعيين الإعدادات:",e),b.oR.error("فشل في إعادة تعيين الإعدادات")}},M=(e,s)=>{N(a=>({...a,[e]:s})),A(!0)},P=()=>{let e=new Blob([JSON.stringify(y,null,2)],{type:"application/json"}),s=URL.createObjectURL(e),a=document.createElement("a");a.href=s,a.download="diwan-settings-".concat(new Date().toISOString().split("T")[0],".json"),a.click(),URL.revokeObjectURL(s)},F=e=>{let s=new FileReader;s.onload=e=>{try{var s;let a=JSON.parse(null==(s=e.target)?void 0:s.result);N(a),A(!0),b.oR.success("تم استيراد الإعدادات بنجاح")}catch(e){b.oR.error("خطأ في ملف الإعدادات")}},s.readAsText(e)},I=[{id:"general",label:"الإعدادات العامة",icon:d.A,component:V,description:"إعدادات النظام الأساسية"},{id:"appearance",label:"المظهر والواجهة",icon:o.A,component:G,description:"تخصيص الألوان والخطوط"},{id:"notifications",label:"الإشعارات",icon:x.A,component:ee,description:"إعدادات الإشعارات والتنبيهات"},{id:"security",label:"الأمان والصلاحيات",icon:m.A,component:ej,description:"إعدادات الأمان وكلمات المرور",adminOnly:!0},{id:"backup",label:"النسخ الاحتياطي",icon:h.A,component:ew,description:"إدارة النسخ الاحتياطية والاستيراد/التصدير",adminOnly:!0},{id:"advanced",label:"إعدادات متقدمة",icon:d.A,component:ek,description:"إعدادات متقدمة وسجل التغييرات",adminOnly:!0}].filter(e=>!e.adminOnly||E);return f?(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"الإعدادات"}),(0,l.jsx)("p",{className:"text-gray-600",children:"إدارة إعدادات النظام والتخصيص"})]}),(0,l.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,l.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})})]}):(0,l.jsxs)("div",{className:"settings-page space-y-6 p-6",children:[(0,l.jsx)("div",{className:"settings-card diwan-card",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-4",children:[(0,l.jsx)("div",{className:"settings-header-icon",children:(0,l.jsx)(d.A,{className:"w-8 h-8 text-white"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"إعدادات النظام"}),(0,l.jsx)("p",{className:"text-gray-600 mt-1",children:"إدارة وتخصيص إعدادات ديوان آل أبو علوش"})]})]}),E&&(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[S&&(0,l.jsxs)(n.E,{className:"bg-amber-100 text-amber-800 border-amber-200",children:[(0,l.jsx)(j.A,{className:"w-3 h-3 ml-1"}),"تغييرات غير محفوظة"]}),(0,l.jsxs)(r.$,{variant:"outline",onClick:D,disabled:C,className:"text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300",children:[(0,l.jsx)(p.A,{className:"w-4 h-4 ml-2"}),"إعادة تعيين"]}),(0,l.jsxs)(r.$,{onClick:R,disabled:C||!S,className:"settings-button bg-gradient-to-r from-sky-500 to-sky-600 hover:from-sky-600 hover:to-sky-700 text-white shadow-lg",children:[C?(0,l.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"}):(0,l.jsx)(g.A,{className:"w-4 h-4 ml-2"}),"حفظ التغييرات"]})]})]})}),!E&&(0,l.jsx)("div",{className:"diwan-card border-amber-200 bg-gradient-to-r from-amber-50 to-orange-50",children:(0,l.jsxs)("div",{className:"flex items-center gap-4",children:[(0,l.jsx)("div",{className:"p-2 bg-amber-100 rounded-lg",children:(0,l.jsx)(u.A,{className:"w-5 h-5 text-amber-600"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-amber-800 font-semibold",children:"وضع العرض فقط"}),(0,l.jsx)("p",{className:"text-amber-700 text-sm mt-1",children:"ليس لديك صلاحية لتعديل الإعدادات. يمكنك عرض الإعدادات الحالية فقط."})]})]})}),(0,l.jsx)("div",{className:"settings-card diwan-card",children:(0,l.jsxs)(c.tU,{value:a,onValueChange:v,className:"space-y-6",children:[(0,l.jsx)(c.j7,{className:"settings-tabs grid w-full grid-cols-3 lg:grid-cols-6 gap-2 p-2",children:I.map(e=>{let s=e.icon;return(0,l.jsxs)(c.Xi,{value:e.id,className:"settings-tab-trigger flex items-center gap-2 text-sm px-4 py-3",children:[(0,l.jsx)(s,{className:"w-4 h-4"}),(0,l.jsx)("span",{className:"hidden sm:inline font-medium",children:e.label})]},e.id)})}),I.map(e=>{let s=e.component;return(0,l.jsx)(c.av,{value:e.id,className:"mt-6",children:(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex items-center gap-4 pb-4 border-b border-gray-200",children:[(0,l.jsx)("div",{className:"p-2 bg-gradient-to-r from-sky-500 to-sky-600 rounded-lg",children:(0,l.jsx)(e.icon,{className:"w-5 h-5 text-white"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:e.label}),(0,l.jsx)("p",{className:"text-gray-600 text-sm",children:e.description})]})]}),(0,l.jsx)("div",{children:(0,l.jsx)(s,{settings:y[e.id],onChange:s=>M(e.id,s),onExportSettings:P,onImportSettings:F,onResetSettings:D,canEdit:E})})]})},e.id)})]})})]})}},7636:(e,s,a)=>{Promise.resolve().then(a.bind(a,5453))},17313:(e,s,a)=>{"use strict";a.d(s,{Xi:()=>d,av:()=>o,j7:()=>n,tU:()=>r});var l=a(95155),i=a(12115),t=a(60704),c=a(59434);let r=t.bL,n=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,l.jsx)(t.B8,{ref:s,className:(0,c.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...i})});n.displayName=t.B8.displayName;let d=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,l.jsx)(t.l9,{ref:s,className:(0,c.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...i})});d.displayName=t.l9.displayName;let o=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,l.jsx)(t.UC,{ref:s,className:(0,c.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...i})});o.displayName=t.UC.displayName},85057:(e,s,a)=>{"use strict";a.d(s,{J:()=>c});var l=a(95155),i=a(12115),t=a(59434);let c=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,l.jsx)("label",{ref:s,className:(0,t.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",a),...i})});c.displayName="Label"},88539:(e,s,a)=>{"use strict";a.d(s,{T:()=>c});var l=a(95155),i=a(12115),t=a(59434);let c=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,l.jsx)("textarea",{className:(0,t.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...i})});c.displayName="Textarea"}},e=>{var s=s=>e(e.s=s);e.O(0,[1778,2108,3942,5217,8130,4324,2302,1271,3068,8441,1684,7358],()=>s(7636)),_N_E=e.O()}]);