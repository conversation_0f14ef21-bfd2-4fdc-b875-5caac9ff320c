(()=>{var e={};e.id=9452,e.ids=[9452],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,s,t)=>{"use strict";t.d(s,{N:()=>i});var r=t(13581),a=t(85663),n=t(31183);let i={providers:[(0,r.A)({name:"credentials",credentials:{email:{label:"البريد الإلكتروني",type:"email"},password:{label:"كلمة المرور",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let s=await n.z.user.findUnique({where:{email:e.email}});return s&&await a.Ay.compare(e.password,s.password)?{id:s.id,email:s.email,name:s.name,role:s.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:s})=>(s&&(e.role=s.role),e),session:async({session:e,token:s})=>(s&&(e.user.id=s.sub,e.user.role=s.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,s,t)=>{"use strict";t.d(s,{z:()=>a});var r=t(96330);let a=globalThis.prisma??new r.PrismaClient},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},89534:(e,s,t)=>{"use strict";t.r(s),t.d(s,{patchFetch:()=>f,routeModule:()=>x,serverHooks:()=>y,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>g});var r={};t.r(r),t.d(r,{DELETE:()=>m,GET:()=>w,PUT:()=>c});var a=t(96559),n=t(48088),i=t(37719),o=t(32190),u=t(19854),l=t(12909),p=t(31183),d=t(85663);async function c(e,{params:s}){try{let t=await (0,u.getServerSession)(l.N);if(!t?.user||"ADMIN"!==t.user.role)return o.NextResponse.json({message:"غير مصرح لك بالوصول"},{status:403});let{id:r}=await s,{name:a,email:n,phone:i,role:c,password:m}=await e.json(),w=await p.z.user.findUnique({where:{id:r}});if(!w)return o.NextResponse.json({message:"المستخدم غير موجود"},{status:404});if(t.user.id===r)return o.NextResponse.json({message:"لا يمكنك تعديل حسابك الخاص من هنا"},{status:400});if(!a||!n||!c)return o.NextResponse.json({message:"الاسم والبريد الإلكتروني والدور مطلوبة"},{status:400});if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(n))return o.NextResponse.json({message:"البريد الإلكتروني غير صحيح"},{status:400});if(n!==w.email&&await p.z.user.findUnique({where:{email:n}}))return o.NextResponse.json({message:"البريد الإلكتروني مستخدم بالفعل"},{status:400});if(!["ADMIN","DATA_ENTRY","VIEWER"].includes(c))return o.NextResponse.json({message:"الدور المحدد غير صحيح"},{status:400});let x={name:a,email:n,phone:i||null,role:c};if(m&&m.length>0){if(m.length<6)return o.NextResponse.json({message:"كلمة المرور يجب أن تكون 6 أحرف على الأقل"},{status:400});x.password=await d.Ay.hash(m,12)}let h=await p.z.user.update({where:{id:r},data:x,select:{id:!0,name:!0,email:!0,phone:!0,role:!0,updatedAt:!0}});return o.NextResponse.json(h)}catch(e){return console.error("خطأ في تحديث المستخدم:",e),o.NextResponse.json({message:"حدث خطأ في الخادم"},{status:500})}}async function m(e,{params:s}){try{let e=await (0,u.getServerSession)(l.N);if(!e?.user||"ADMIN"!==e.user.role)return o.NextResponse.json({message:"غير مصرح لك بالوصول"},{status:403});let{id:t}=await s;if(!await p.z.user.findUnique({where:{id:t}}))return o.NextResponse.json({message:"المستخدم غير موجود"},{status:404});if(e.user.id===t)return o.NextResponse.json({message:"لا يمكنك حذف حسابك الخاص"},{status:400});return await p.z.$transaction(async e=>{await e.notification.deleteMany({where:{userId:t}}),await e.memberUser.deleteMany({where:{userId:t}}),await e.member.deleteMany({where:{createdById:t}}),await e.income.deleteMany({where:{createdById:t}}),await e.expense.deleteMany({where:{createdById:t}}),await e.activity.deleteMany({where:{createdById:t}}),await e.galleryFolder.deleteMany({where:{createdBy:t}}),await e.galleryPhoto.deleteMany({where:{uploadedBy:t}}),await e.user.delete({where:{id:t}})}),o.NextResponse.json({message:"تم حذف المستخدم بنجاح"},{status:200})}catch(e){return console.error("خطأ في حذف المستخدم:",e),o.NextResponse.json({message:"حدث خطأ في الخادم"},{status:500})}}async function w(e,{params:s}){try{let e=await (0,u.getServerSession)(l.N);if(!e?.user||"ADMIN"!==e.user.role)return o.NextResponse.json({message:"غير مصرح لك بالوصول"},{status:403});let{id:t}=await s,r=await p.z.user.findUnique({where:{id:t},select:{id:!0,name:!0,email:!0,phone:!0,role:!0,createdAt:!0,updatedAt:!0,_count:{select:{createdMembers:!0,createdIncomes:!0,notifications:!0}}}});if(!r)return o.NextResponse.json({message:"المستخدم غير موجود"},{status:404});return o.NextResponse.json(r)}catch(e){return console.error("خطأ في جلب المستخدم:",e),o.NextResponse.json({message:"حدث خطأ في الخادم"},{status:500})}}let x=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/users/[id]/route",pathname:"/api/admin/users/[id]",filename:"route",bundlePath:"app/api/admin/users/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\admin\\users\\[id]\\route.ts",nextConfigOutput:"standalone",userland:r}),{workAsyncStorage:h,workUnitAsyncStorage:g,serverHooks:y}=x;function f(){return(0,i.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:g})}},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,5663,4999,3412,580],()=>t(89534));module.exports=r})();