(()=>{var e={};e.id=5413,e.ids=[5413],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,s)=>{"use strict";s.d(r,{N:()=>o});var t=s(13581),i=s(85663),a=s(31183);let o={providers:[(0,t.A)({name:"credentials",credentials:{email:{label:"البريد الإلكتروني",type:"email"},password:{label:"كلمة المرور",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await a.z.user.findUnique({where:{email:e.email}});return r&&await i.Ay.compare(e.password,r.password)?{id:r.id,email:r.email,name:r.name,role:r.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,s)=>{"use strict";s.d(r,{z:()=>i});var t=s(96330);let i=globalThis.prisma??new t.PrismaClient},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},95660:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>y,routeModule:()=>x,serverHooks:()=>f,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>w});var t={};s.r(t),s.d(t,{DELETE:()=>m,POST:()=>d});var i=s(96559),a=s(48088),o=s(37719),n=s(32190),u=s(79748),p=s(33873),l=s(19854),c=s(12909);async function d(e){try{let r,s,t;if(!await (0,l.getServerSession)(c.N))return n.NextResponse.json({error:"غير مصرح"},{status:401});let i=await e.formData(),a=i.get("file"),o=i.get("type")||"member";if(!a)return n.NextResponse.json({error:"لم يتم اختيار ملف"},{status:400});if(!["image/jpeg","image/jpg","image/png","image/webp"].includes(a.type))return n.NextResponse.json({error:"نوع الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG, WebP)"},{status:400});if(a.size>5242880)return n.NextResponse.json({error:"حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت"},{status:400});let d=await a.arrayBuffer(),m=Buffer.from(d),x=Date.now(),g=Math.random().toString(36).substring(2,15),w=a.name.split(".").pop();"gallery"===o?(r=`gallery_${x}_${g}.${w}`,s=(0,p.join)(process.cwd(),"public","uploads","gallery"),t=`/uploads/gallery/${r}`):"logo"===o||"favicon"===o?(r=`${o}_${x}.${w}`,s=(0,p.join)(process.cwd(),"public","uploads","branding"),t=`/uploads/branding/${r}`):(r=`member_${x}_${g}.${w}`,s=(0,p.join)(process.cwd(),"public","uploads","members"),t=`/uploads/members/${r}`);try{await (0,u.mkdir)(s,{recursive:!0})}catch{}let f=(0,p.join)(s,r);return await (0,u.writeFile)(f,m),n.NextResponse.json({success:!0,url:t,filePath:t,fileName:r,fileSize:a.size,fileType:a.type})}catch(e){return console.error("خطأ في رفع الملف:",e),n.NextResponse.json({error:"حدث خطأ في رفع الملف"},{status:500})}}async function m(e){try{if(!await (0,l.getServerSession)(c.N))return n.NextResponse.json({error:"غير مصرح"},{status:401});let{searchParams:r}=new URL(e.url),t=r.get("path");if(!t)return n.NextResponse.json({error:"مسار الملف مطلوب"},{status:400});if(!t.startsWith("/uploads/members/")&&!t.startsWith("/uploads/gallery/")&&!t.startsWith("/uploads/branding/"))return n.NextResponse.json({error:"مسار غير صحيح"},{status:400});let i=(0,p.join)(process.cwd(),"public",t);try{let e=await Promise.resolve().then(s.t.bind(s,79748,23));return await e.unlink(i),n.NextResponse.json({success:!0})}catch{return n.NextResponse.json({success:!0})}}catch(e){return console.error("خطأ في حذف الملف:",e),n.NextResponse.json({error:"حدث خطأ في حذف الملف"},{status:500})}}let x=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/upload/route",pathname:"/api/upload",filename:"route",bundlePath:"app/api/upload/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\upload\\route.ts",nextConfigOutput:"standalone",userland:t}),{workAsyncStorage:g,workUnitAsyncStorage:w,serverHooks:f}=x;function y(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:w})}},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4243,5663,4999,3412,580],()=>s(95660));module.exports=t})();