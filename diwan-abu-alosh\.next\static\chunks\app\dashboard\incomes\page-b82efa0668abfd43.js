(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[346],{17580:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},33109:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},57434:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},61953:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>F});var t=s(95155),r=s(12115),n=s(12108),l=s(30285),d=s(62523),o=s(66695),i=s(26126),c=s(85127),m=s(33109),x=s(57434),h=s(84616),u=s(55868),g=s(17580),b=s(47924),p=s(66932),f=s(69074),v=s(13717),j=s(62525),N=s(59434),y=s(62177),w=s(48778),k=s(71153),A=s(85057),T=s(88539),C=s(54165),S=s(59409),I=s(54416),D=s(71007);let E=k.z.object({amount:k.z.number().positive("المبلغ يجب أن يكون أكبر من صفر"),date:k.z.string().min(1,"التاريخ مطلوب"),source:k.z.string().min(1,"مصدر الإيراد مطلوب"),type:k.z.enum(["SUBSCRIPTION","DONATION","EVENT","OTHER"]).default("SUBSCRIPTION"),description:k.z.string().optional(),notes:k.z.string().optional(),memberId:k.z.string().min(1,"يجب اختيار عضو")});function z(e){let{open:a,onOpenChange:s,onSuccess:n,income:o=null}=e,[i,c]=(0,r.useState)(!1),[m,x]=(0,r.useState)([]),[h,g]=(0,r.useState)([]),[p,f]=(0,r.useState)(""),[v,j]=(0,r.useState)(null),[N,k]=(0,r.useState)(!1),z=(0,r.useRef)(null),{register:O,handleSubmit:R,reset:P,setValue:F,watch:B,formState:{errors:H}}=(0,y.mN)({resolver:(0,w.u)(E),defaultValues:{amount:0,date:new Date().toISOString().split("T")[0],source:"",type:"SUBSCRIPTION",description:"",notes:"",memberId:""}}),_=B("type"),M=async()=>{try{let e=await fetch("/api/members?limit=1000");if(e.ok){let a=await e.json();x(a.members||[])}}catch(e){console.error("خطأ في جلب الأعضاء:",e)}};(0,r.useEffect)(()=>{if(a){if(M(),o){var e;P({amount:o.amount,date:o.date.split("T")[0],source:o.source,type:o.type,description:o.description||"",notes:o.notes||"",memberId:(null==(e=o.member)?void 0:e.id)||""}),o.member?(j(o.member),f(o.member.name)):(j(null),f(""))}else P({amount:0,date:new Date().toISOString().split("T")[0],source:"",type:"SUBSCRIPTION",description:"",notes:"",memberId:""}),j(null),f("");k(!1)}},[a,o,P]),(0,r.useEffect)(()=>{""===p.trim()?g(m.slice(0,10)):g(m.filter(e=>e.name.toLowerCase().includes(p.toLowerCase())).slice(0,10))},[p,m]),(0,r.useEffect)(()=>{let e=e=>{z.current&&!z.current.contains(e.target)&&k(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]);let L=e=>{j(e),f(e.name),k(!1),F("memberId",e.id)},U=()=>{j(null),f(""),F("memberId","")},Z=async e=>{try{var a,t;c(!0);let r={...e,amount:Number(e.amount),date:new Date(e.date),memberId:e.memberId,description:(null==(a=e.description)?void 0:a.trim())||null,notes:(null==(t=e.notes)?void 0:t.trim())||null},l=!!o,d=l?"/api/incomes/".concat(o.id):"/api/incomes",i=await fetch(d,{method:l?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!i.ok){let e=await i.json();throw Error(e.error||"حدث خطأ")}alert(l?"تم تعديل الإيراد بنجاح":"تم إضافة الإيراد بنجاح"),s(!1),null==n||n()}catch(e){console.error("خطأ في حفظ الإيراد:",e),alert(e.message||"حدث خطأ في حفظ الإيراد")}finally{c(!1)}};return(0,t.jsx)(C.lG,{open:a,onOpenChange:s,children:(0,t.jsxs)(C.Cf,{className:"max-w-[50vw] max-h-[95vh] overflow-y-auto bg-white rounded-2xl shadow-2xl border-0",children:[(0,t.jsxs)(C.c7,{className:"relative p-8 pb-6 border-b border-gray-100 bg-gradient-to-br from-diwan-50 via-blue-50 to-indigo-50 rounded-t-2xl",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-diwan-600/5 via-blue-600/5 to-indigo-600/5 rounded-t-2xl"}),(0,t.jsxs)("div",{className:"relative flex items-center gap-4",children:[(0,t.jsx)("div",{className:"p-4 bg-white rounded-2xl shadow-lg border border-diwan-200/50 backdrop-blur-sm",children:(0,t.jsx)(u.A,{className:"w-7 h-7 text-diwan-600"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)(C.L3,{className:"text-2xl font-bold text-gray-900 mb-2",children:o?"تعديل الإيراد":"إضافة إيراد جديد"}),(0,t.jsx)("p",{className:"text-gray-600 text-base leading-relaxed",children:o?"قم بتعديل بيانات الإيراد بعناية لضمان دقة السجلات المالية":"قم بإدخال بيانات الإيراد الجديد بعناية لضمان دقة السجلات المالية"})]})]})]}),(0,t.jsxs)("form",{onSubmit:R(Z),className:"p-8 space-y-8 bg-gray-50/30",children:[(0,t.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border border-gray-100",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-diwan-600 rounded-full"}),"المعلومات الأساسية"]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)(A.J,{htmlFor:"amount",className:"text-sm font-medium text-gray-700 flex items-center gap-1",children:["المبلغ (دينار أردني)",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(d.p,{id:"amount",type:"number",step:"0.01",min:"0",...O("amount",{valueAsNumber:!0}),className:"h-12 text-lg font-medium transition-all duration-200 ".concat(H.amount?"border-red-300 focus:border-red-500 focus:ring-red-200":"border-gray-200 focus:border-diwan-500 focus:ring-diwan-100"),placeholder:"0.00"}),(0,t.jsx)("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm",children:"د.أ"})]}),H.amount&&(0,t.jsxs)("p",{className:"text-sm text-red-500 flex items-center gap-1",children:[(0,t.jsx)("span",{className:"w-1 h-1 bg-red-500 rounded-full"}),H.amount.message]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)(A.J,{htmlFor:"date",className:"text-sm font-medium text-gray-700 flex items-center gap-1",children:["التاريخ",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)(d.p,{id:"date",type:"date",...O("date"),className:"h-12 transition-all duration-200 ".concat(H.date?"border-red-300 focus:border-red-500 focus:ring-red-200":"border-gray-200 focus:border-diwan-500 focus:ring-diwan-100")}),H.date&&(0,t.jsxs)("p",{className:"text-sm text-red-500 flex items-center gap-1",children:[(0,t.jsx)("span",{className:"w-1 h-1 bg-red-500 rounded-full"}),H.date.message]})]})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border border-gray-100",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-gold-600 rounded-full"}),"تفاصيل الإيراد"]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)(A.J,{htmlFor:"source",className:"text-sm font-medium text-gray-700 flex items-center gap-1",children:["مصدر الإيراد",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)(d.p,{id:"source",...O("source"),placeholder:"مثال: اشتراك شهري، تبرع، رسوم فعالية",className:"h-12 transition-all duration-200 ".concat(H.source?"border-red-300 focus:border-red-500 focus:ring-red-200":"border-gray-200 focus:border-diwan-500 focus:ring-diwan-100")}),H.source&&(0,t.jsxs)("p",{className:"text-sm text-red-500 flex items-center gap-1",children:[(0,t.jsx)("span",{className:"w-1 h-1 bg-red-500 rounded-full"}),H.source.message]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(A.J,{htmlFor:"type",className:"text-sm font-medium text-gray-700",children:"نوع الإيراد"}),(0,t.jsxs)(S.l6,{value:_,onValueChange:e=>F("type",e),children:[(0,t.jsx)(S.bq,{className:"h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-100 transition-all duration-200",children:(0,t.jsx)(S.yv,{placeholder:"اختر نوع الإيراد"})}),(0,t.jsxs)(S.gC,{className:"border-gray-200 shadow-lg",children:[(0,t.jsx)(S.eb,{value:"SUBSCRIPTION",className:"hover:bg-diwan-50 focus:bg-diwan-50",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"اشتراكات"]})}),(0,t.jsx)(S.eb,{value:"DONATION",className:"hover:bg-green-50 focus:bg-green-50",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"تبرعات"]})}),(0,t.jsx)(S.eb,{value:"EVENT",className:"hover:bg-purple-50 focus:bg-purple-50",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-purple-500 rounded-full"}),"فعاليات"]})}),(0,t.jsx)(S.eb,{value:"OTHER",className:"hover:bg-gray-50 focus:bg-gray-50",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-gray-500 rounded-full"}),"أخرى"]})})]})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)(A.J,{htmlFor:"memberSearch",className:"text-sm font-medium text-gray-700 flex items-center gap-1",children:["العضو",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsxs)("div",{className:"relative",ref:z,children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(b.A,{className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,t.jsx)(d.p,{id:"memberSearch",type:"text",placeholder:"ابحث عن عضو...",value:p,onChange:e=>f(e.target.value),onFocus:()=>{k(!0),""===p.trim()&&g(m.slice(0,10))},className:"h-12 pr-12 pl-12 transition-all duration-200 ".concat(H.memberId?"border-red-300 focus:border-red-500 focus:ring-red-200":"border-gray-200 focus:border-diwan-500 focus:ring-diwan-100")}),v&&(0,t.jsx)("button",{type:"button",onClick:U,className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500 transition-colors duration-200",children:(0,t.jsx)(I.A,{className:"w-5 h-5"})})]}),N&&(0,t.jsx)("div",{className:"absolute z-50 w-full mt-2 bg-white border border-gray-200 rounded-xl shadow-xl max-h-64 overflow-y-auto",children:0===h.length?(0,t.jsx)("div",{className:"p-4 text-gray-500 text-center",children:(0,t.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,t.jsx)(b.A,{className:"w-8 h-8 text-gray-300"}),(0,t.jsx)("span",{className:"text-sm",children:""===p.trim()?"ابدأ بالكتابة للبحث عن عضو":"لا توجد نتائج مطابقة"})]})}):(0,t.jsx)(t.Fragment,{children:h.map(e=>(0,t.jsx)("div",{className:"p-4 hover:bg-diwan-50 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors duration-150",onClick:()=>L(e),children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-diwan-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(D.A,{className:"w-4 h-4 text-diwan-600"})}),(0,t.jsx)("span",{className:"font-medium text-gray-900",children:e.name})]})},e.id))})})]}),H.memberId&&(0,t.jsxs)("p",{className:"text-sm text-red-500 flex items-center gap-1",children:[(0,t.jsx)("span",{className:"w-1 h-1 bg-red-500 rounded-full"}),H.memberId.message]}),v&&(0,t.jsx)("div",{className:"mt-3 p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(D.A,{className:"w-4 h-4 text-green-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-green-800 font-semibold",children:v.name}),(0,t.jsx)("p",{className:"text-green-600 text-xs",children:"العضو المحدد"})]})]}),(0,t.jsx)("button",{type:"button",onClick:U,className:"text-green-600 hover:text-red-500 transition-colors duration-200 p-1 rounded-full hover:bg-white/50",children:(0,t.jsx)(I.A,{className:"w-4 h-4"})})]})})]})]})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border border-gray-100",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-indigo-600 rounded-full"}),"معلومات إضافية"]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(A.J,{htmlFor:"description",className:"text-sm font-medium text-gray-700",children:"الوصف (اختياري)"}),(0,t.jsx)(d.p,{id:"description",...O("description"),placeholder:"وصف إضافي للإيراد",className:"h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-100 transition-all duration-200"})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(A.J,{htmlFor:"notes",className:"text-sm font-medium text-gray-700",children:"ملاحظات (اختياري)"}),(0,t.jsx)(T.T,{id:"notes",...O("notes"),placeholder:"أي ملاحظات إضافية حول هذا الإيراد",rows:4,className:"border-gray-200 focus:border-diwan-500 focus:ring-diwan-100 transition-all duration-200 resize-none"})]})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border border-gray-100 mt-6",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row justify-end gap-4 sm:gap-3",children:[(0,t.jsxs)(l.$,{type:"button",variant:"outline",onClick:()=>s(!1),disabled:i,className:"group relative h-14 px-8 border-2 border-gray-300 text-gray-700 hover:border-red-400 hover:text-red-600 bg-white hover:bg-red-50 transition-all duration-300 font-semibold rounded-xl shadow-sm hover:shadow-md transform hover:scale-[1.02] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-5 h-5 rounded-full bg-gray-200 group-hover:bg-red-200 transition-colors duration-300 flex items-center justify-center",children:(0,t.jsx)(I.A,{className:"w-3 h-3 text-gray-600 group-hover:text-red-600 transition-colors duration-300"})}),(0,t.jsx)("span",{className:"text-base",children:"إلغاء"})]}),(0,t.jsx)("div",{className:"absolute inset-0 rounded-xl bg-gradient-to-r from-red-50 to-pink-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"})]}),(0,t.jsxs)(l.$,{type:"submit",disabled:i,className:"group relative h-14 px-10 bg-gradient-to-r from-diwan-600 via-diwan-700 to-blue-600 hover:from-diwan-700 hover:via-diwan-800 hover:to-blue-700 text-white font-bold rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] disabled:opacity-70 disabled:cursor-not-allowed disabled:transform-none overflow-hidden",children:[i?(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"w-5 h-5 border-3 border-white/30 border-t-white rounded-full animate-spin"}),(0,t.jsx)("div",{className:"absolute inset-0 w-5 h-5 border-3 border-transparent border-t-white/60 rounded-full animate-ping"})]}),(0,t.jsx)("span",{className:"text-base",children:"جاري الحفظ..."})]}):(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-6 h-6 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center group-hover:bg-white/30 transition-all duration-300",children:(0,t.jsx)(u.A,{className:"w-4 h-4 text-white"})}),(0,t.jsx)("span",{className:"text-base",children:o?"حفظ التعديلات":"حفظ الإيراد"}),(0,t.jsx)("div",{className:"w-2 h-2 rounded-full bg-white/40 group-hover:bg-white/60 transition-all duration-300"})]}),(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 transform translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 ease-out"}),(0,t.jsx)("div",{className:"absolute inset-0 rounded-xl bg-gradient-to-r from-diwan-400/20 via-blue-400/20 to-indigo-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl -z-10"})]})]}),(0,t.jsx)("div",{className:"flex items-center justify-center mt-4 pt-4 border-t border-gray-100",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 rounded-full bg-diwan-200"}),(0,t.jsx)("div",{className:"w-1 h-1 rounded-full bg-diwan-300"}),(0,t.jsx)("div",{className:"w-2 h-2 rounded-full bg-diwan-200"})]})})]})]})]})})}var O=s(26597),R=s(52699),P=s.n(R);function F(){let{data:e}=(0,n.useSession)(),[a,s]=(0,r.useState)([]),[y,w]=(0,r.useState)(!0),[k,A]=(0,r.useState)(""),[T,C]=(0,r.useState)("all"),[S,I]=(0,r.useState)({page:1,limit:10,total:0,pages:0}),[D,E]=(0,r.useState)({totalAmount:0,totalCount:0,byType:[]}),[R,F]=(0,r.useState)(!1),[B,H]=(0,r.useState)(null),_=(0,r.useCallback)(async()=>{try{w(!0);let e=new URLSearchParams({search:k,type:T,page:S.page.toString(),limit:S.limit.toString()}),a=await fetch("/api/incomes?".concat(e));if(!a.ok)throw Error("فشل في جلب الإيرادات");let t=await a.json();s(t.incomes),I(t.pagination)}catch(e){console.error("خطأ في جلب الإيرادات:",e)}finally{w(!1)}},[k,T,S.page,S.limit]),M=async()=>{try{let e=await fetch("/api/incomes?limit=1000");if(!e.ok)return;let a=await e.json(),s=a.incomes.reduce((e,a)=>e+a.amount,0),t=a.incomes.length,r=a.incomes.reduce((e,a)=>{let s=e.find(e=>e.type===a.type);return s?(s._sum.amount+=a.amount,s._count+=1):e.push({type:a.type,_sum:{amount:a.amount},_count:1}),e},[]);E({totalAmount:s,totalCount:t,byType:r})}catch(e){console.error("خطأ في جلب الإحصائيات:",e)}};(0,r.useEffect)(()=>{_()},[_]),(0,r.useEffect)(()=>{M()},[]);let L=e=>{H(e),F(!0)},U=async e=>{if(confirm("هل أنت متأكد من حذف هذا الإيراد؟\n\nهذا الإجراء لا يمكن التراجع عنه."))try{let a=await fetch("/api/incomes/".concat(e),{method:"DELETE"});if(!a.ok){let e=await a.json();alert(e.error||"فشل في حذف الإيراد");return}alert("تم حذف الإيراد بنجاح"),_(),M()}catch(e){console.error("خطأ في حذف الإيراد:",e),alert("حدث خطأ في حذف الإيراد")}},Z=async()=>{try{let e=await fetch("/api/incomes?limit=1000");if(!e.ok)throw Error("فشل في جلب البيانات");let a=(await e.json()).incomes,s=a.reduce((e,a)=>e+a.amount,0),t=a.length,r=a.reduce((e,a)=>{let s=e.find(e=>e.type===a.type);return s?(s.amount+=a.amount,s.count+=1):e.push({type:a.type,amount:a.amount,count:1}),e},[]),n=new Date().toLocaleDateString("ar-SA",{year:"numeric",month:"long",day:"numeric"}),l='\n        <!DOCTYPE html>\n        <html dir="rtl" lang="ar">\n        <head>\n          <meta charset="UTF-8">\n          <style>\n            @import url(\'https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;600;700&display=swap\');\n\n            * {\n              margin: 0;\n              padding: 0;\n              box-sizing: border-box;\n            }\n\n            body {\n              font-family: \'Noto Sans Arabic\', Arial, sans-serif;\n              direction: rtl;\n              background: white;\n              color: #333;\n              line-height: 1.6;\n              padding: 20px;\n            }\n\n            .header {\n              text-align: center;\n              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n              color: white;\n              padding: 30px;\n              border-radius: 15px;\n              margin-bottom: 30px;\n              box-shadow: 0 10px 30px rgba(0,0,0,0.1);\n            }\n\n            .header h1 {\n              font-size: 32px;\n              font-weight: 700;\n              margin-bottom: 10px;\n            }\n\n            .header h2 {\n              font-size: 24px;\n              font-weight: 600;\n              margin-bottom: 15px;\n            }\n\n            .header .date {\n              font-size: 16px;\n              opacity: 0.9;\n            }\n\n            .stats {\n              display: grid;\n              grid-template-columns: repeat(3, 1fr);\n              gap: 20px;\n              margin-bottom: 30px;\n            }\n\n            .stat-card {\n              background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n              color: white;\n              padding: 25px;\n              border-radius: 15px;\n              text-align: center;\n              box-shadow: 0 8px 25px rgba(0,0,0,0.1);\n            }\n\n            .stat-card.green {\n              background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n            }\n\n            .stat-card.blue {\n              background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\n            }\n\n            .stat-card h3 {\n              font-size: 16px;\n              margin-bottom: 10px;\n              font-weight: 600;\n            }\n\n            .stat-card .value {\n              font-size: 24px;\n              font-weight: 700;\n            }\n\n            .table-container {\n              background: white;\n              border-radius: 15px;\n              overflow: hidden;\n              box-shadow: 0 10px 30px rgba(0,0,0,0.1);\n              margin-bottom: 30px;\n            }\n\n            .table-header {\n              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n              color: white;\n              padding: 20px;\n              text-align: center;\n            }\n\n            .table-header h3 {\n              font-size: 20px;\n              font-weight: 600;\n            }\n\n            table {\n              width: 100%;\n              border-collapse: collapse;\n              font-size: 14px;\n            }\n\n            th {\n              background: #f8f9fa;\n              padding: 15px 10px;\n              text-align: center;\n              font-weight: 600;\n              color: #495057;\n              border-bottom: 2px solid #dee2e6;\n            }\n\n            td {\n              padding: 12px 10px;\n              text-align: center;\n              border-bottom: 1px solid #dee2e6;\n            }\n\n            tr:nth-child(even) {\n              background-color: #f8f9fa;\n            }\n\n            .amount {\n              font-weight: 700;\n              color: #28a745;\n            }\n\n            .type-badge {\n              padding: 5px 12px;\n              border-radius: 20px;\n              font-size: 12px;\n              font-weight: 600;\n              color: white;\n            }\n\n            .type-subscription { background: #007bff; }\n            .type-donation { background: #28a745; }\n            .type-event { background: #6f42c1; }\n            .type-other { background: #6c757d; }\n\n            .summary {\n              background: white;\n              border-radius: 15px;\n              padding: 25px;\n              box-shadow: 0 10px 30px rgba(0,0,0,0.1);\n              margin-bottom: 30px;\n            }\n\n            .summary h3 {\n              color: #495057;\n              margin-bottom: 20px;\n              font-size: 18px;\n              font-weight: 600;\n              text-align: center;\n            }\n\n            .summary-grid {\n              display: grid;\n              grid-template-columns: repeat(2, 1fr);\n              gap: 15px;\n            }\n\n            .summary-item {\n              background: #f8f9fa;\n              padding: 15px;\n              border-radius: 10px;\n              border-right: 4px solid #007bff;\n            }\n\n            .summary-item .label {\n              font-weight: 600;\n              color: #495057;\n              margin-bottom: 5px;\n            }\n\n            .summary-item .value {\n              font-size: 16px;\n              font-weight: 700;\n              color: #007bff;\n            }\n\n            .footer {\n              text-align: center;\n              margin-top: 40px;\n              padding: 20px;\n              background: #f8f9fa;\n              border-radius: 10px;\n              color: #6c757d;\n            }\n\n            @media print {\n              body { padding: 0; }\n              .header { margin-bottom: 20px; }\n              .stats { margin-bottom: 20px; }\n              .table-container { margin-bottom: 20px; }\n              .summary { margin-bottom: 20px; }\n            }\n          </style>\n        </head>\n        <body>\n          <div class="header">\n            <h1>تقرير الإيرادات</h1>\n            <h2>ديوان أبو علوش</h2>\n            <div class="date">تاريخ التقرير: '.concat(n,'</div>\n          </div>\n\n          <div class="stats">\n            <div class="stat-card">\n              <h3>إجمالي الإيرادات</h3>\n              <div class="value">').concat((0,N.vv)(s),'</div>\n            </div>\n            <div class="stat-card green">\n              <h3>عدد الإيرادات</h3>\n              <div class="value">').concat(t,' إيراد</div>\n            </div>\n            <div class="stat-card blue">\n              <h3>متوسط الإيراد</h3>\n              <div class="value">').concat((0,N.vv)(t>0?s/t:0),'</div>\n            </div>\n          </div>\n\n          <div class="table-container">\n            <div class="table-header">\n              <h3>تفاصيل الإيرادات</h3>\n            </div>\n            <table>\n              <thead>\n                <tr>\n                  <th>المصدر</th>\n                  <th>المبلغ (د.أ)</th>\n                  <th>النوع</th>\n                  <th>العضو</th>\n                  <th>التاريخ</th>\n                </tr>\n              </thead>\n              <tbody>\n                ').concat(a.map(e=>{var a;return"\n                  <tr>\n                    <td>".concat(e.source,'</td>\n                    <td class="amount">').concat(e.amount.toFixed(2),'</td>\n                    <td>\n                      <span class="type-badge type-').concat(e.type.toLowerCase(),'">\n                        ').concat((0,N.Tk)(e.type),"\n                      </span>\n                    </td>\n                    <td>").concat((null==(a=e.member)?void 0:a.name)||"غير محدد","</td>\n                    <td>").concat((0,N.Yq)(e.date),"</td>\n                  </tr>\n                ")}).join(""),'\n              </tbody>\n            </table>\n          </div>\n\n          <div class="summary">\n            <h3>ملخص الإيرادات حسب النوع</h3>\n            <div class="summary-grid">\n              ').concat(r.map(e=>'\n                <div class="summary-item">\n                  <div class="label">'.concat((0,N.Tk)(e.type),'</div>\n                  <div class="value">').concat((0,N.vv)(e.amount)," (").concat(e.count," إيراد)</div>\n                </div>\n              ")).join(""),'\n            </div>\n          </div>\n\n          <div class="footer">\n            <p>ديوان أبو علوش - نظام إدارة العائلة</p>\n            <p>تم إنشاء هذا التقرير في ').concat(n,"</p>\n          </div>\n        </body>\n        </html>\n      "),d=document.createElement("div");d.innerHTML=l,d.style.position="absolute",d.style.left="-9999px",d.style.top="0",d.style.width="210mm",document.body.appendChild(d),await new Promise(e=>setTimeout(e,1e3));let o=await P()(d,{scale:2,useCORS:!0,allowTaint:!0,backgroundColor:"#ffffff",width:794,height:1123});document.body.removeChild(d);let i=new O.default("p","mm","a4"),c=o.toDataURL("image/png"),m=210*o.height/o.width,x=m,h=0;for(i.addImage(c,"PNG",0,h,210,m),x-=297;x>=0;)h=x-m,i.addPage(),i.addImage(c,"PNG",0,h,210,m),x-=297;let u="تقرير_الإيرادات_".concat(new Date().toISOString().split("T")[0],".pdf");i.save(u),setTimeout(()=>{alert("تم إنشاء تقرير PDF بنجاح! ✅")},500)}catch(e){console.error("خطأ في تصدير PDF:",e),alert("حدث خطأ في تصدير التقرير ❌")}},J=(null==e?void 0:e.user.role)!=="VIEWER",V=(null==e?void 0:e.user.role)==="ADMIN";return(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)("div",{className:"text-center mb-8",children:(0,t.jsxs)("div",{className:"bg-gradient-to-r from-green-600 to-emerald-600 rounded-3xl shadow-2xl p-10 text-white relative overflow-hidden",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-500 opacity-30 animate-pulse"}),(0,t.jsxs)("div",{className:"relative z-10",children:[(0,t.jsx)("div",{className:"inline-flex items-center justify-center w-20 h-20 rounded-full mb-6 bg-white bg-opacity-20 backdrop-blur-sm",children:(0,t.jsx)(m.A,{className:"w-10 h-10 text-white"})}),(0,t.jsx)("h1",{className:"text-5xl font-black mb-4 text-white",children:"إدارة الإيرادات"}),(0,t.jsx)("p",{className:"text-xl font-semibold mb-6 text-green-100",children:"عرض وإدارة إيرادات الديوان بكفاءة وسهولة"}),(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2 space-x-reverse",children:[(0,t.jsx)("div",{className:"h-1 w-16 rounded-full bg-white bg-opacity-60"}),(0,t.jsx)("div",{className:"h-1 w-8 rounded-full bg-white bg-opacity-40"}),(0,t.jsx)("div",{className:"h-1 w-16 rounded-full bg-white bg-opacity-60"})]})]})]})}),(0,t.jsx)("div",{className:"flex justify-center mb-8",children:(0,t.jsx)("div",{className:"bg-white rounded-2xl shadow-xl p-6 border border-gray-100",children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-4 justify-center",children:[(0,t.jsxs)(l.$,{onClick:Z,className:"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold",children:[(0,t.jsx)(x.A,{className:"w-5 h-5 ml-2"}),"تصدير PDF"]}),J&&(0,t.jsxs)(l.$,{onClick:()=>{H(null),F(!0)},className:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold",children:[(0,t.jsx)(h.A,{className:"w-5 h-5 ml-2"}),"إضافة إيراد جديد"]})]})})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3",children:[(0,t.jsxs)(o.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-green-500 to-green-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,t.jsx)("div",{className:"bg-gradient-to-r from-green-500 to-green-600 p-1 rounded-t-xl"}),(0,t.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,t.jsx)(o.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"إجمالي الإيرادات"}),(0,t.jsx)("div",{className:"p-4 rounded-2xl shadow-lg",style:{backgroundColor:"#28a745"},children:(0,t.jsx)(u.A,{className:"h-7 w-7 text-white"})})]}),(0,t.jsxs)(o.Wu,{className:"relative z-10",children:[(0,t.jsx)("div",{className:"text-4xl font-black mb-2",style:{color:"#191970"},children:(0,N.vv)(D.totalAmount)}),(0,t.jsx)("p",{className:"text-sm font-semibold",style:{color:"#6c757d"},children:"إجمالي المبالغ"})]})]}),(0,t.jsxs)(o.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-500 to-blue-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,t.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-blue-600 p-1 rounded-t-xl"}),(0,t.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,t.jsx)(o.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"عدد الإيرادات"}),(0,t.jsx)("div",{className:"p-4 rounded-2xl shadow-lg",style:{backgroundColor:"#007bff"},children:(0,t.jsx)(m.A,{className:"h-7 w-7 text-white"})})]}),(0,t.jsxs)(o.Wu,{className:"relative z-10",children:[(0,t.jsx)("div",{className:"text-4xl font-black mb-2",style:{color:"#191970"},children:D.totalCount}),(0,t.jsx)("p",{className:"text-sm font-semibold",style:{color:"#6c757d"},children:"إجمالي العمليات"})]})]}),(0,t.jsxs)(o.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative sm:col-span-2 lg:col-span-1",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-purple-500 to-purple-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,t.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-purple-600 p-1 rounded-t-xl"}),(0,t.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,t.jsx)(o.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"متوسط الإيراد"}),(0,t.jsx)("div",{className:"p-4 rounded-2xl shadow-lg",style:{backgroundColor:"#800020"},children:(0,t.jsx)(g.A,{className:"h-7 w-7 text-white"})})]}),(0,t.jsxs)(o.Wu,{className:"relative z-10",children:[(0,t.jsx)("div",{className:"text-4xl font-black mb-2",style:{color:"#191970"},children:(0,N.vv)(D.totalCount>0?D.totalAmount/D.totalCount:0)}),(0,t.jsx)("p",{className:"text-sm font-semibold",style:{color:"#6c757d"},children:"متوسط القيمة"})]})]})]}),(0,t.jsxs)(o.Zp,{className:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden",children:[(0,t.jsxs)("div",{className:"bg-gradient-to-r from-gray-50 to-blue-50 p-6 border-b border-gray-100",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-diwan-600 rounded-full"}),"البحث والتصفية"]}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:"ابحث وصفي الإيرادات حسب المعايير المختلفة"})]}),(0,t.jsx)(o.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6",children:[(0,t.jsxs)("div",{className:"relative flex-1",children:[(0,t.jsx)("div",{className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400",children:(0,t.jsx)(b.A,{className:"w-5 h-5"})}),(0,t.jsx)(d.p,{placeholder:"البحث في الإيرادات (المصدر، الوصف، العضو...)",value:k,onChange:e=>A(e.target.value),className:"h-12 pr-12 text-base border-gray-200 focus:border-diwan-500 focus:ring-diwan-100 transition-all duration-200 rounded-xl"})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)("select",{value:T,onChange:e=>C(e.target.value),className:"h-12 px-4 pr-10 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-diwan-500 focus:ring-4 focus:ring-diwan-100 transition-all duration-200 bg-white text-gray-700 font-medium min-w-[200px] appearance-none cursor-pointer",children:[(0,t.jsx)("option",{value:"all",children:"\uD83D\uDD0D جميع الأنواع"}),(0,t.jsx)("option",{value:"SUBSCRIPTION",children:"\uD83D\uDCB3 اشتراكات"}),(0,t.jsx)("option",{value:"DONATION",children:"\uD83D\uDC9D تبرعات"}),(0,t.jsx)("option",{value:"EVENT",children:"\uD83C\uDF89 فعاليات"}),(0,t.jsx)("option",{value:"OTHER",children:"\uD83D\uDCCB أخرى"})]}),(0,t.jsx)("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none",children:(0,t.jsx)(p.A,{className:"w-4 h-4 text-gray-400"})})]})]})})]}),(0,t.jsxs)(o.Zp,{className:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden",children:[(0,t.jsxs)("div",{className:"bg-gradient-to-r from-gray-50 to-blue-50 p-6 border-b border-gray-100",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-green-600 rounded-full"}),"قائمة الإيرادات"]}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:"جميع إيرادات الديوان مع تفاصيلها الكاملة"})]}),(0,t.jsx)(o.Wu,{className:"p-0",children:y?(0,t.jsxs)("div",{className:"flex flex-col justify-center items-center h-64 bg-gray-50",children:[(0,t.jsx)("div",{className:"w-12 h-12 border-4 border-diwan-600 border-t-transparent rounded-full animate-spin mb-4"}),(0,t.jsx)("div",{className:"text-gray-600 font-medium",children:"جاري تحميل الإيرادات..."}),(0,t.jsx)("div",{className:"text-gray-400 text-sm mt-1",children:"يرجى الانتظار"})]}):0===a.length?(0,t.jsxs)("div",{className:"flex flex-col justify-center items-center h-64 bg-gray-50",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mb-4",children:(0,t.jsx)(m.A,{className:"w-8 h-8 text-gray-400"})}),(0,t.jsx)("div",{className:"text-gray-600 font-medium mb-2",children:"لا توجد إيرادات"}),(0,t.jsx)("div",{className:"text-gray-400 text-sm",children:"ابدأ بإضافة إيراد جديد"})]}):(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)(c.XI,{children:[(0,t.jsx)(c.A0,{children:(0,t.jsxs)(c.Hj,{className:"bg-gray-50 hover:bg-gray-50",children:[(0,t.jsx)(c.nd,{className:"font-semibold text-gray-700 py-4",children:"المصدر"}),(0,t.jsx)(c.nd,{className:"font-semibold text-gray-700 py-4",children:"المبلغ"}),(0,t.jsx)(c.nd,{className:"font-semibold text-gray-700 py-4",children:"النوع"}),(0,t.jsx)(c.nd,{className:"font-semibold text-gray-700 py-4",children:"العضو"}),(0,t.jsx)(c.nd,{className:"font-semibold text-gray-700 py-4",children:"التاريخ"}),(0,t.jsx)(c.nd,{className:"font-semibold text-gray-700 py-4 text-center",children:"الإجراءات"})]})}),(0,t.jsx)(c.BF,{children:a.map(e=>{var a;return(0,t.jsxs)(c.Hj,{className:"hover:bg-blue-50/50 transition-colors duration-200 border-b border-gray-100",children:[(0,t.jsx)(c.nA,{className:"py-4",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-diwan-100 to-blue-100 rounded-xl flex items-center justify-center flex-shrink-0",children:(0,t.jsx)(u.A,{className:"w-5 h-5 text-diwan-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold text-gray-900 mb-1",children:e.source}),e.description&&(0,t.jsx)("div",{className:"text-sm text-gray-500 leading-relaxed",children:e.description})]})]})}),(0,t.jsx)(c.nA,{className:"py-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full animate-pulse"}),(0,t.jsx)("div",{className:"font-bold text-lg text-green-600",children:(0,N.vv)(e.amount)})]})}),(0,t.jsx)(c.nA,{className:"py-4",children:(0,t.jsx)(i.E,{variant:"secondary",className:"px-3 py-1 rounded-full font-medium ".concat("SUBSCRIPTION"===e.type?"bg-blue-100 text-blue-700 border-blue-200":"DONATION"===e.type?"bg-green-100 text-green-700 border-green-200":"EVENT"===e.type?"bg-purple-100 text-purple-700 border-purple-200":"bg-gray-100 text-gray-700 border-gray-200"),children:(0,N.Tk)(e.type)})}),(0,t.jsx)(c.nA,{className:"py-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(g.A,{className:"w-4 h-4 text-gray-600"})}),(0,t.jsx)("span",{className:"font-medium text-gray-700",children:(null==(a=e.member)?void 0:a.name)||"غير محدد"})]})}),(0,t.jsx)(c.nA,{className:"py-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-2 text-gray-600",children:[(0,t.jsx)(f.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{className:"font-medium",children:(0,N.Yq)(e.date)})]})}),(0,t.jsx)(c.nA,{className:"py-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[J&&(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>L(e),className:"h-9 w-9 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-all duration-200",title:"تعديل",children:(0,t.jsx)(v.A,{className:"w-4 h-4"})}),V&&(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>U(e.id),className:"h-9 w-9 p-0 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-all duration-200",title:"حذف",children:(0,t.jsx)(j.A,{className:"w-4 h-4"})})]})})]},e.id)})})]})})})]}),S.pages>1&&(0,t.jsx)(o.Zp,{className:"bg-white rounded-2xl shadow-lg border border-gray-100",children:(0,t.jsx)(o.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-center gap-4",children:[(0,t.jsx)("div",{className:"flex items-center gap-2 text-gray-600",children:(0,t.jsxs)("span",{className:"text-sm font-medium",children:["عرض ",(S.page-1)*S.limit+1," - ",Math.min(S.page*S.limit,S.total)," من ",S.total," إيراد"]})}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(l.$,{variant:"outline",disabled:1===S.page,onClick:()=>I(e=>({...e,page:e.page-1})),className:"h-10 px-4 border-2 border-gray-200 hover:border-diwan-500 hover:text-diwan-600 transition-all duration-200 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed",children:(0,t.jsx)("span",{className:"font-medium",children:"السابق"})}),(0,t.jsx)("div",{className:"flex items-center gap-2",children:Array.from({length:Math.min(5,S.pages)},(e,a)=>{let s=a+1;return(0,t.jsx)("button",{onClick:()=>I(e=>({...e,page:s})),className:"w-10 h-10 rounded-lg font-semibold transition-all duration-200 ".concat(S.page===s?"bg-diwan-600 text-white shadow-lg":"bg-gray-100 text-gray-600 hover:bg-diwan-100 hover:text-diwan-600"),children:s},s)})}),(0,t.jsx)(l.$,{variant:"outline",disabled:S.page===S.pages,onClick:()=>I(e=>({...e,page:e.page+1})),className:"h-10 px-4 border-2 border-gray-200 hover:border-diwan-500 hover:text-diwan-600 transition-all duration-200 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed",children:(0,t.jsx)("span",{className:"font-medium",children:"التالي"})})]})]})})}),D.byType.length>0&&(0,t.jsxs)(o.Zp,{className:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden",children:[(0,t.jsxs)("div",{className:"bg-gradient-to-r from-gray-50 to-purple-50 p-6 border-b border-gray-100",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-purple-600 rounded-full"}),"الإيرادات حسب النوع"]}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:"توزيع الإيرادات على الأنواع المختلفة"})]}),(0,t.jsx)(o.Wu,{className:"p-6",children:(0,t.jsx)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4",children:D.byType.map((e,a)=>{let s=[{bg:"from-blue-50 to-indigo-50",border:"border-blue-200",text:"text-blue-700",icon:"bg-blue-100",dot:"bg-blue-500"},{bg:"from-green-50 to-emerald-50",border:"border-green-200",text:"text-green-700",icon:"bg-green-100",dot:"bg-green-500"},{bg:"from-purple-50 to-pink-50",border:"border-purple-200",text:"text-purple-700",icon:"bg-purple-100",dot:"bg-purple-500"},{bg:"from-orange-50 to-red-50",border:"border-orange-200",text:"text-orange-700",icon:"bg-orange-100",dot:"bg-orange-500"}],r=s[a%s.length];return(0,t.jsxs)("div",{className:"relative overflow-hidden bg-gradient-to-br ".concat(r.bg," ").concat(r.border," border-2 rounded-2xl p-6 hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02]"),children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsxs)("div",{className:"p-3 ".concat(r.icon," rounded-xl shadow-sm"),children:["SUBSCRIPTION"===e.type&&(0,t.jsx)(g.A,{className:"w-5 h-5 text-blue-600"}),"DONATION"===e.type&&(0,t.jsx)(u.A,{className:"w-5 h-5 text-green-600"}),"EVENT"===e.type&&(0,t.jsx)(f.A,{className:"w-5 h-5 text-purple-600"}),"OTHER"===e.type&&(0,t.jsx)(m.A,{className:"w-5 h-5 text-orange-600"})]}),(0,t.jsx)("div",{className:"w-3 h-3 ".concat(r.dot," rounded-full animate-pulse")})]}),(0,t.jsx)("div",{className:"text-sm font-semibold text-gray-600 mb-2",children:(0,N.Tk)(e.type)}),(0,t.jsx)("div",{className:"text-2xl font-bold ".concat(r.text," mb-2"),children:(0,N.vv)(e._sum.amount||0)}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 ".concat(r.dot," rounded-full")}),(0,t.jsxs)("span",{className:"text-sm text-gray-500 font-medium",children:[e._count," إيراد"]})]})]})]},e.type)})})})]}),(0,t.jsx)(z,{open:R,onOpenChange:()=>{F(!1),H(null)},income:B,onSuccess:()=>{_(),M()}})]})}},71007:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},71877:(e,a,s)=>{Promise.resolve().then(s.bind(s,61953))},85057:(e,a,s)=>{"use strict";s.d(a,{J:()=>l});var t=s(95155),r=s(12115),n=s(59434);let l=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("label",{ref:a,className:(0,n.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",s),...r})});l.displayName="Label"},85127:(e,a,s)=>{"use strict";s.d(a,{A0:()=>d,BF:()=>o,Hj:()=>i,XI:()=>l,nA:()=>m,nd:()=>c});var t=s(95155),r=s(12115),n=s(59434);let l=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{className:"relative w-full overflow-auto",children:(0,t.jsx)("table",{ref:a,className:(0,n.cn)("w-full caption-bottom text-sm",s),...r})})});l.displayName="Table";let d=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("thead",{ref:a,className:(0,n.cn)("[&_tr]:border-b",s),...r})});d.displayName="TableHeader";let o=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("tbody",{ref:a,className:(0,n.cn)("[&_tr:last-child]:border-0",s),...r})});o.displayName="TableBody",r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("tfoot",{ref:a,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",s),...r})}).displayName="TableFooter";let i=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("tr",{ref:a,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",s),...r})});i.displayName="TableRow";let c=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("th",{ref:a,className:(0,n.cn)("h-12 px-4 text-right align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",s),...r})});c.displayName="TableHead";let m=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("td",{ref:a,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",s),...r})});m.displayName="TableCell"},88539:(e,a,s)=>{"use strict";s.d(a,{T:()=>l});var t=s(95155),r=s(12115),n=s(59434);let l=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:a,...r})});l.displayName="Textarea"}},e=>{var a=a=>e(e.s=a);e.O(0,[4316,3930,1778,2108,3942,5217,8130,913,1070,3068,8441,1684,7358],()=>a(71877)),_N_E=e.O()}]);