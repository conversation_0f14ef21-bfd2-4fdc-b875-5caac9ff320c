(()=>{var e={};e.id=647,e.ids=[647],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>i});var a=r(13581),s=r(85663),n=r(31183);let i={providers:[(0,a.A)({name:"credentials",credentials:{email:{label:"البريد الإلكتروني",type:"email"},password:{label:"كلمة المرور",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let t=await n.z.user.findUnique({where:{email:e.email}});return t&&await s.Ay.compare(e.password,t.password)?{id:t.id,email:t.email,name:t.name,role:t.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>s});var a=r(96330);let s=globalThis.prisma??new a.PrismaClient},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84624:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>b,routeModule:()=>w,serverHooks:()=>D,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>x});var a={};r.r(a),r.d(a,{GET:()=>g});var s=r(96559),n=r(48088),i=r(37719),o=r(32190),u=r(19854),l=r(12909),c=r(31183);let m=e=>{let t=new Date,r=t.getFullYear(),a=t.getMonth();switch(e){case"current-month":return{startDate:new Date(r,a,1),endDate:new Date(r,a+1,0,23,59,59)};case"last-month":return{startDate:new Date(r,a-1,1),endDate:new Date(r,a,0,23,59,59)};case"current-year":return{startDate:new Date(r,0,1),endDate:new Date(r,11,31,23,59,59)};case"last-year":return{startDate:new Date(r-1,0,1),endDate:new Date(r-1,11,31,23,59,59)};default:return{startDate:new Date(2020,0,1),endDate:t}}},p=async(e,t)=>{let r=[],a=new Date(e.getFullYear(),e.getMonth(),1),s=new Date(t.getFullYear(),t.getMonth()+1,0);for(let e=new Date(a);e<=s;e.setMonth(e.getMonth()+1)){let t=new Date(e.getFullYear(),e.getMonth(),1),a=new Date(e.getFullYear(),e.getMonth()+1,0,23,59,59),[s,n]=await Promise.all([c.z.income.aggregate({where:{date:{gte:t,lte:a}},_sum:{amount:!0}}),c.z.expense.aggregate({where:{date:{gte:t,lte:a}},_sum:{amount:!0}})]),i=s._sum.amount||0,o=n._sum.amount||0;r.push({month:`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}`,incomes:i,expenses:o,balance:i-o})}return r},d=async e=>{let{startDate:t,endDate:r}=m(e),[a,s]=await Promise.all([c.z.member.count(),c.z.member.count({where:{status:"ACTIVE"}})]),[n,i]=await Promise.all([c.z.income.aggregate({where:{date:{gte:t,lte:r}},_sum:{amount:!0},_count:!0}),c.z.expense.aggregate({where:{date:{gte:t,lte:r}},_sum:{amount:!0},_count:!0})]),o=n._sum.amount||0,u=i._sum.amount||0;return{totalMembers:a,activeMembers:s,totalIncomes:o,totalExpenses:u,balance:o-u,totalActivities:await c.z.activity.count({where:{startDate:{gte:t,lte:r}}}),topContributors:(await c.z.member.findMany({include:{incomes:{where:{date:{gte:t,lte:r}},select:{amount:!0}}}})).map(e=>({id:e.id,name:e.name,totalContributions:e.incomes.reduce((e,t)=>e+t.amount,0),incomes:e.incomes})).filter(e=>e.totalContributions>0).sort((e,t)=>t.totalContributions-e.totalContributions).slice(0,10),startDate:t,endDate:r}};async function g(e){try{if(!await (0,u.getServerSession)(l.N))return o.NextResponse.json({error:"غير مصرح"},{status:401});let{searchParams:t}=new URL(e.url),r=t.get("period")||"current-year",a=await d(r),[s,n]=await Promise.all([c.z.income.findMany({where:{date:{gte:a.startDate,lte:a.endDate}},include:{member:{select:{name:!0}}},orderBy:{date:"desc"},take:15}),c.z.expense.findMany({where:{date:{gte:a.startDate,lte:a.endDate}},orderBy:{date:"desc"},take:15})]),i=[...s.map(e=>({type:"income",id:e.id,amount:e.amount,date:e.date.toISOString(),description:e.description||e.source,memberName:e.member?.name})),...n.map(e=>({type:"expense",id:e.id,amount:e.amount,date:e.date.toISOString(),description:e.description,memberName:e.recipient}))].sort((e,t)=>new Date(t.date).getTime()-new Date(e.date).getTime()).slice(0,20),m=await p(a.startDate,a.endDate),g={summary:{totalMembers:a.totalMembers,activeMembers:a.activeMembers,totalIncomes:a.totalIncomes,totalExpenses:a.totalExpenses,balance:a.balance,totalActivities:a.totalActivities},topContributors:a.topContributors,recentTransactions:i,monthlyData:m};return o.NextResponse.json(g)}catch(e){return console.error("خطأ في جلب بيانات التقارير:",e),o.NextResponse.json({error:"حدث خطأ في جلب بيانات التقارير"},{status:500})}}let w=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/reports/route",pathname:"/api/reports",filename:"route",bundlePath:"app/api/reports/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\reports\\route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:h,workUnitAsyncStorage:x,serverHooks:D}=w;function b(){return(0,i.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:x})}},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4243,5663,4999,3412,580],()=>r(84624));module.exports=a})();