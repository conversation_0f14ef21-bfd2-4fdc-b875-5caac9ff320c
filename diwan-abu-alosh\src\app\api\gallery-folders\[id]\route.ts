import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Schema للتحقق من صحة البيانات
const updateGalleryFolderSchema = z.object({
  title: z.string().min(1, 'العنوان مطلوب').optional(),
  description: z.string().optional(),
  location: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
})

// GET - جلب مجلد محدد
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    const folder = await prisma.galleryFolder.findUnique({
      where: { id: params.id },
      include: {
        creator: {
          select: {
            name: true,
          },
        },
        photos: {
          include: {
            uploader: {
              select: {
                name: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
        _count: {
          select: {
            photos: true,
          },
        },
      },
    })

    if (!folder) {
      return NextResponse.json({ error: 'المجلد غير موجود' }, { status: 404 })
    }

    return NextResponse.json(folder)
  } catch (error) {
    console.error('خطأ في جلب المجلد:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في جلب المجلد' },
      { status: 500 }
    )
  }
}

// PUT - تحديث مجلد
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // التحقق من الصلاحيات
    if (session.user.role === 'VIEWER') {
      return NextResponse.json({ error: 'ليس لديك صلاحية لتعديل المجلدات' }, { status: 403 })
    }

    const body = await request.json()
    
    // التحقق من صحة البيانات
    const validatedData = updateGalleryFolderSchema.parse(body)

    // التحقق من وجود المجلد
    const existingFolder = await prisma.galleryFolder.findUnique({
      where: { id: params.id },
    })

    if (!existingFolder) {
      return NextResponse.json({ error: 'المجلد غير موجود' }, { status: 404 })
    }

    // تحضير البيانات للتحديث
    const updateData: Record<string, unknown> = {}

    if (validatedData.title !== undefined) {
      updateData.title = validatedData.title
    }

    if (validatedData.description !== undefined) {
      updateData.description = validatedData.description
    }

    if (validatedData.location !== undefined) {
      updateData.location = validatedData.location
    }

    if (validatedData.startDate !== undefined) {
      updateData.startDate = validatedData.startDate ? new Date(validatedData.startDate) : null
    }

    if (validatedData.endDate !== undefined) {
      updateData.endDate = validatedData.endDate ? new Date(validatedData.endDate) : null
    }

    // تحديث المجلد
    const updatedFolder = await prisma.galleryFolder.update({
      where: { id: params.id },
      data: updateData,
      include: {
        creator: {
          select: {
            name: true,
          },
        },
        _count: {
          select: {
            photos: true,
          },
        },
      },
    })

    return NextResponse.json(updatedFolder)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'بيانات غير صحيحة', details: error.errors },
        { status: 400 }
      )
    }

    console.error('خطأ في تحديث المجلد:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في تحديث المجلد' },
      { status: 500 }
    )
  }
}

// DELETE - حذف مجلد
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // التحقق من الصلاحيات
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'ليس لديك صلاحية لحذف المجلدات' }, { status: 403 })
    }

    // التحقق من وجود المجلد
    const existingFolder = await prisma.galleryFolder.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            photos: true,
          },
        },
      },
    })

    if (!existingFolder) {
      return NextResponse.json({ error: 'المجلد غير موجود' }, { status: 404 })
    }

    // التحقق من وجود صور في المجلد
    if (existingFolder._count.photos > 0) {
      return NextResponse.json(
        { error: 'لا يمكن حذف المجلد لأنه يحتوي على صور. يرجى حذف الصور أولاً أو نقلها إلى مجلد آخر.' },
        { status: 400 }
      )
    }

    // حذف المجلد
    await prisma.galleryFolder.delete({
      where: { id: params.id },
    })

    return NextResponse.json({ message: 'تم حذف المجلد بنجاح' })
  } catch (error) {
    console.error('خطأ في حذف المجلد:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في حذف المجلد' },
      { status: 500 }
    )
  }
}
