(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[984],{4516:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},6149:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>J});var t=a(95155),l=a(12115);a(8576);var r=a(12108),i=a(30285),d=a(62523),n=a(66695),c=a(26126),o=a(54165),x=a(27213),m=a(21380),h=a(84616),g=a(29869),u=a(69074),p=a(71007),j=a(47924),b=a(54416),v=a(92657),f=a(13717),N=a(91788),y=a(62525),w=a(73054),A=a(17580),k=a(92138);function C(e){let{folders:s,onFolderClick:a,onEditFolder:l,onDeleteFolder:r,canEdit:d=!1,canDelete:o=!1,loading:g}=e;return g?(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:[...Array(8)].map((e,s)=>(0,t.jsx)(n.Zp,{className:"animate-pulse",children:(0,t.jsxs)(n.Wu,{className:"p-0",children:[(0,t.jsx)("div",{className:"aspect-video bg-gray-200 rounded-t-lg"}),(0,t.jsxs)("div",{className:"p-4 space-y-2",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]})},s))}):0===s.length?(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(m.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"لا توجد مجلدات"}),(0,t.jsx)("p",{className:"text-gray-500",children:"لم يتم العثور على أي مجلدات"})]}):(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:s.map(e=>(0,t.jsx)(n.Zp,{className:"group hover:shadow-lg transition-all duration-200 border-2 hover:border-diwan-300 relative",children:(0,t.jsxs)(n.Wu,{className:"p-0",children:[(d||o)&&("activity"===e.type||"folder"===e.type)&&(0,t.jsxs)("div",{className:"absolute top-2 left-2 z-10 flex space-x-1 space-x-reverse opacity-0 group-hover:opacity-100 transition-opacity",children:[d&&l&&(0,t.jsx)(i.$,{variant:"secondary",size:"sm",className:"h-8 w-8 p-0 bg-white/90 hover:bg-white",onClick:s=>{s.stopPropagation(),l(e)},title:"تعديل المجلد",children:(0,t.jsx)(f.A,{className:"h-3 w-3"})}),o&&r&&(0,t.jsx)(i.$,{variant:"secondary",size:"sm",className:"h-8 w-8 p-0 bg-white/90 hover:bg-white text-red-600 hover:text-red-700",onClick:s=>{s.stopPropagation(),r(e)},title:"حذف المجلد",children:(0,t.jsx)(y.A,{className:"h-3 w-3"})})]}),(0,t.jsxs)("div",{className:"relative aspect-video overflow-hidden rounded-t-lg bg-gradient-to-br from-diwan-50 to-diwan-100 cursor-pointer",onClick:()=>a(e),children:[e.coverPhoto?(0,t.jsx)("img",{src:e.coverPhoto.imagePath,alt:e.coverPhoto.title,className:"w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"}):(0,t.jsxs)("div",{className:"w-full h-full flex flex-col items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100",children:[(0,t.jsx)("div",{className:"bg-diwan-100 rounded-full p-4 mb-3",children:(0,t.jsx)(m.A,{className:"w-8 h-8 text-diwan-600"})}),(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-1",children:"مجلد فارغ"}),(0,t.jsx)("p",{className:"text-xs text-gray-500 text-center px-2",children:"folder"===e.type?"مجلد جديد":"لا توجد صور"}),0===e.photosCount&&(0,t.jsx)("div",{className:"mt-2 px-3 py-1 bg-yellow-100 text-yellow-700 rounded-full text-xs font-medium",children:"جديد"})]}),(0,t.jsx)("div",{className:"absolute top-2 right-2",children:(0,t.jsx)(c.E,{variant:"secondary",className:"text-xs ".concat("activity"===e.type?"bg-green-100 text-green-700":"folder"===e.type?"bg-purple-100 text-purple-700":"bg-blue-100 text-blue-700"),children:"activity"===e.type?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(u.A,{className:"w-3 h-3 ml-1"}),"نشاط"]}):"folder"===e.type?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(m.A,{className:"w-3 h-3 ml-1"}),"مجلد"]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(A.A,{className:"w-3 h-3 ml-1"}),"عام"]})})}),(0,t.jsx)("div",{className:"absolute bottom-2 left-2",children:(0,t.jsxs)(c.E,{className:"text-xs ".concat(0===e.photosCount?"bg-orange-500/80 text-white":"bg-black/70 text-white"),children:[(0,t.jsx)(x.A,{className:"w-3 h-3 ml-1"}),0===e.photosCount?"فارغ":"".concat(e.photosCount," صورة")]})}),(0,t.jsx)("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200 flex items-center justify-center",children:(0,t.jsx)(i.$,{variant:"secondary",size:"sm",className:"opacity-0 group-hover:opacity-100 transition-opacity duration-200",onClick:()=>a(e),children:0===e.photosCount?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(h.A,{className:"w-4 h-4 ml-2"}),"إضافة صور"]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(v.A,{className:"w-4 h-4 ml-2"}),"عرض الصور"]})})})]}),(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-900 truncate group-hover:text-diwan-700 transition-colors",children:e.title}),e.description&&(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1 line-clamp-2",children:e.description})]}),(0,t.jsx)(k.A,{className:"w-4 h-4 text-gray-400 group-hover:text-diwan-500 transition-colors flex-shrink-0 mr-2"})]}),(0,t.jsxs)("div",{className:"mt-3 flex items-center justify-between text-xs text-gray-500",children:[(0,t.jsx)("span",{children:1===e.photosCount?"صورة واحدة":2===e.photosCount?"صورتان":"".concat(e.photosCount," صور")}),(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(m.A,{className:"w-3 h-3 ml-1"}),"مجلد"]})]})]})]})},e.id))})}function S(e){let{folders:s}=e,a=s.reduce((e,s)=>e+s.photosCount,0),l=s.filter(e=>"activity"===e.type).length,r=s.filter(e=>"general"===e.type).length;return(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-4 gap-4 mb-6",children:[(0,t.jsx)(n.Zp,{children:(0,t.jsxs)(n.Wu,{className:"p-4 text-center",children:[(0,t.jsx)(m.A,{className:"w-8 h-8 text-blue-600 mx-auto mb-2"}),(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:s.length}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"إجمالي المجلدات"})]})}),(0,t.jsx)(n.Zp,{children:(0,t.jsxs)(n.Wu,{className:"p-4 text-center",children:[(0,t.jsx)(x.A,{className:"w-8 h-8 text-green-600 mx-auto mb-2"}),(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:a}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"إجمالي الصور"})]})}),(0,t.jsx)(n.Zp,{children:(0,t.jsxs)(n.Wu,{className:"p-4 text-center",children:[(0,t.jsx)(u.A,{className:"w-8 h-8 text-purple-600 mx-auto mb-2"}),(0,t.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:l}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"مجلدات الأنشطة"})]})}),(0,t.jsx)(n.Zp,{children:(0,t.jsxs)(n.Wu,{className:"p-4 text-center",children:[(0,t.jsx)(A.A,{className:"w-8 h-8 text-orange-600 mx-auto mb-2"}),(0,t.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:r}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"المجلدات العامة"})]})})]})}var D=a(85057),z=a(88539),E=a(53311),F=a(4516);function O(e){let{open:s,onOpenChange:a,onSuccess:r}=e,[n,c]=(0,l.useState)(""),[x,g]=(0,l.useState)(""),[p,j]=(0,l.useState)(""),[b,v]=(0,l.useState)(""),[f,N]=(0,l.useState)(""),[y,w]=(0,l.useState)(!1),[A,k]=(0,l.useState)(null),C=async e=>{if(e.preventDefault(),!n.trim())return void k("يرجى إدخال عنوان النشاط");if(!b)return void k("يرجى إدخال تاريخ بداية النشاط");w(!0),k(null);try{let e=await fetch("/api/gallery-folders",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:n.trim(),description:x.trim()||void 0,location:p.trim()||void 0,startDate:b||void 0,endDate:f||void 0})});if(!e.ok){let s=await e.json();throw Error(s.error||"فشل في إنشاء المجلد")}let s=await e.json();console.log("تم إنشاء المجلد بنجاح:",s),c(""),g(""),j(""),v(""),N(""),k(null),a(!1),setTimeout(()=>{r()},100)}catch(e){console.error("خطأ في إنشاء المجلد:",e),k(e.message||"حدث خطأ في إنشاء المجلد")}finally{w(!1)}};return(0,t.jsx)(o.lG,{open:s,onOpenChange:a,children:(0,t.jsxs)(o.Cf,{className:"max-w-[50vw] max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(o.c7,{className:"text-center pb-6",children:[(0,t.jsx)("div",{className:"mx-auto w-16 h-16 bg-gradient-to-br from-diwan-500 to-diwan-600 rounded-full flex items-center justify-center mb-4",children:(0,t.jsx)(m.A,{className:"w-8 h-8 text-white"})}),(0,t.jsx)(o.L3,{className:"text-2xl font-bold text-diwan-600 mb-2",children:"إنشاء مجلد جديد"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:"أنشئ مجلد جديد لتنظيم صور النشاط أو المناسبة"})]}),(0,t.jsxs)("form",{onSubmit:C,className:"space-y-8",children:[(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-gradient-to-r from-diwan-500 to-diwan-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(n.trim()&&b?"100%":n.trim()||b?"50%":"25%","%")}})}),(0,t.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3",children:(0,t.jsx)(E.A,{className:"w-4 h-4 text-white"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-blue-800",children:"معلومات المجلد"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)(D.J,{htmlFor:"title",className:"text-sm font-medium text-gray-700 flex items-center",children:[(0,t.jsx)(m.A,{className:"w-4 h-4 ml-2 text-diwan-600"}),"اسم المجلد *"]}),(0,t.jsx)(d.p,{id:"title",value:n,onChange:e=>c(e.target.value),placeholder:"مثال: حفل زفاف أحمد محمد",disabled:y,required:!0,className:"enhanced-border h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 bg-white"})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)(D.J,{htmlFor:"location",className:"text-sm font-medium text-gray-700 flex items-center",children:[(0,t.jsx)(F.A,{className:"w-4 h-4 ml-2 text-green-600"}),"الموقع (اختياري)"]}),(0,t.jsx)(d.p,{id:"location",value:p,onChange:e=>j(e.target.value),placeholder:"مثال: قاعة الأفراح الكبرى",disabled:y,className:"enhanced-border h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 bg-white"})]})]}),(0,t.jsxs)("div",{className:"space-y-3 mt-6",children:[(0,t.jsx)(D.J,{htmlFor:"description",className:"text-sm font-medium text-gray-700",children:"وصف المجلد (اختياري)"}),(0,t.jsx)(z.T,{id:"description",value:x,onChange:e=>g(e.target.value),placeholder:"أضف وصفاً مفصلاً للمجلد والمناسبة...",disabled:y,rows:4,className:"enhanced-border border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 bg-white resize-none"})]})]}),(0,t.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center mr-3",children:(0,t.jsx)(u.A,{className:"w-4 h-4 text-white"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-green-800",children:"تواريخ النشاط"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)(D.J,{htmlFor:"startDate",className:"text-sm font-medium text-gray-700 flex items-center",children:[(0,t.jsx)(u.A,{className:"w-4 h-4 ml-2 text-green-600"}),"تاريخ البداية *"]}),(0,t.jsx)(d.p,{id:"startDate",type:"date",value:b,onChange:e=>v(e.target.value),disabled:y,required:!0,className:"enhanced-border h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 bg-white"})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)(D.J,{htmlFor:"endDate",className:"text-sm font-medium text-gray-700 flex items-center",children:[(0,t.jsx)(u.A,{className:"w-4 h-4 ml-2 text-orange-600"}),"تاريخ النهاية (اختياري)"]}),(0,t.jsx)(d.p,{id:"endDate",type:"date",value:f,onChange:e=>N(e.target.value),disabled:y,min:b,className:"enhanced-border h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 bg-white"})]})]}),(0,t.jsx)("div",{className:"mt-4 p-4 bg-gradient-to-r from-green-100 to-emerald-100 rounded-lg border border-green-200",children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("svg",{className:"w-5 h-5 text-green-600 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),(0,t.jsxs)("div",{className:"mr-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-green-800",children:"نصيحة مفيدة"}),(0,t.jsx)("p",{className:"text-sm text-green-700 mt-1",children:"إذا لم تحدد تاريخ النهاية، سيتم استخدام تاريخ البداية كتاريخ النهاية تلقائياً"})]})]})})]}),n.trim()&&(0,t.jsxs)("div",{className:"bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-6 border border-purple-100",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center mr-3",children:(0,t.jsx)(m.A,{className:"w-4 h-4 text-white"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-purple-800",children:"معاينة المجلد"})]}),(0,t.jsx)("div",{className:"bg-white rounded-lg p-4 border border-purple-200",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-diwan-500 to-diwan-600 rounded-lg flex items-center justify-center mr-4",children:(0,t.jsx)(m.A,{className:"w-6 h-6 text-white"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h4",{className:"font-semibold text-gray-900",children:n}),x&&(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-1 line-clamp-2",children:x}),(0,t.jsxs)("div",{className:"flex items-center mt-2 text-xs text-gray-500",children:[p&&(0,t.jsxs)("span",{className:"flex items-center ml-4",children:[(0,t.jsx)(F.A,{className:"w-3 h-3 ml-1"}),p]}),b&&(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(u.A,{className:"w-3 h-3 ml-1"}),new Date(b).toLocaleDateString("ar-JO")]})]})]}),(0,t.jsx)("div",{className:"text-right",children:(0,t.jsx)("div",{className:"text-xs text-gray-500",children:"0 صورة"})})]})})]}),A&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-xl p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("svg",{className:"w-5 h-5 text-red-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,t.jsx)("div",{className:"mr-3",children:(0,t.jsx)("p",{className:"text-sm font-medium text-red-800",children:A})})]})}),(0,t.jsxs)(o.Es,{className:"gap-4 pt-6 border-t border-gray-100",children:[(0,t.jsx)(i.$,{type:"button",variant:"outline",onClick:()=>a(!1),disabled:y,className:"enhanced-button flex-1 h-14 border-2 border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 font-semibold text-base rounded-xl transition-all duration-200",children:"إلغاء"}),(0,t.jsx)(i.$,{type:"submit",disabled:y||!n.trim()||!b,className:"enhanced-button flex-1 h-14 bg-gradient-to-r from-purple-600 via-purple-700 to-indigo-700 hover:from-purple-700 hover:via-purple-800 hover:to-indigo-800 text-white font-bold text-base shadow-xl hover:shadow-2xl transition-all duration-300 rounded-xl border-0 transform hover:scale-105 active:scale-95",children:y?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"w-6 h-6 ml-2 border-3 border-white border-t-transparent rounded-full animate-spin"}),(0,t.jsx)("span",{className:"font-bold",children:"جاري الإنشاء..."})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"bg-white/20 rounded-full p-1 ml-2",children:(0,t.jsx)(h.A,{className:"w-5 h-5 text-white"})}),(0,t.jsx)("span",{className:"font-bold",children:"إنشاء المجلد"})]})})]})]})]})})}function $(e){let{open:s,onOpenChange:a,onSuccess:r,activity:n}=e,[c,x]=(0,l.useState)(""),[m,h]=(0,l.useState)(""),[g,p]=(0,l.useState)(""),[j,b]=(0,l.useState)(""),[v,N]=(0,l.useState)(""),[y,w]=(0,l.useState)(!1),[A,k]=(0,l.useState)(null);(0,l.useEffect)(()=>{n&&s&&(x(n.title||""),h(n.description||""),p(n.location||""),b(n.startDate?new Date(n.startDate).toISOString().split("T")[0]:""),N(n.endDate?new Date(n.endDate).toISOString().split("T")[0]:""),k(null))},[n,s]);let C=async e=>{if(e.preventDefault(),n){if(!c.trim())return void k("يرجى إدخال عنوان النشاط");if(!j)return void k("يرجى إدخال تاريخ بداية النشاط");w(!0),k(null);try{let e=await fetch("/api/activities/".concat(n.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:c.trim(),description:m.trim()||void 0,location:g.trim()||void 0,startDate:new Date(j).toISOString(),endDate:v?new Date(v).toISOString():new Date(j).toISOString()})});if(!e.ok){let s=await e.json();throw Error(s.error||"فشل في تحديث النشاط")}r(),a(!1)}catch(e){console.error("خطأ في تحديث النشاط:",e),k(e.message||"حدث خطأ في تحديث النشاط")}finally{w(!1)}}};return n?(0,t.jsx)(o.lG,{open:s,onOpenChange:a,children:(0,t.jsxs)(o.Cf,{className:"max-w-md",children:[(0,t.jsx)(o.c7,{children:(0,t.jsxs)(o.L3,{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"w-5 h-5 ml-2"}),"تعديل النشاط"]})}),(0,t.jsxs)("form",{onSubmit:C,className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(D.J,{htmlFor:"title",children:"عنوان النشاط *"}),(0,t.jsx)(d.p,{id:"title",value:c,onChange:e=>x(e.target.value),placeholder:"مثال: حفل زفاف أحمد محمد",disabled:y,required:!0})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(D.J,{htmlFor:"description",children:"وصف النشاط"}),(0,t.jsx)(z.T,{id:"description",value:m,onChange:e=>h(e.target.value),placeholder:"وصف مختصر للنشاط (اختياري)",disabled:y,rows:3})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(D.J,{htmlFor:"location",children:"الموقع"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(F.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,t.jsx)(d.p,{id:"location",value:g,onChange:e=>p(e.target.value),placeholder:"موقع إقامة النشاط (اختياري)",disabled:y,className:"pr-10"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(D.J,{htmlFor:"startDate",children:"تاريخ البداية *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(u.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,t.jsx)(d.p,{id:"startDate",type:"date",value:j,onChange:e=>b(e.target.value),disabled:y,required:!0,className:"pr-10"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(D.J,{htmlFor:"endDate",children:"تاريخ النهاية"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(u.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,t.jsx)(d.p,{id:"endDate",type:"date",value:v,onChange:e=>N(e.target.value),disabled:y,min:j,className:"pr-10"})]})]}),A&&(0,t.jsx)("div",{className:"text-red-600 text-sm bg-red-50 p-3 rounded-md",children:A}),(0,t.jsxs)(o.Es,{children:[(0,t.jsx)(i.$,{type:"button",variant:"outline",onClick:()=>a(!1),disabled:y,children:"إلغاء"}),(0,t.jsx)(i.$,{type:"submit",disabled:y||!c.trim()||!j,className:"bg-diwan-600 hover:bg-diwan-700",children:y?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(f.A,{className:"w-4 h-4 ml-2 animate-spin"}),"جاري التحديث..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(f.A,{className:"w-4 h-4 ml-2"}),"حفظ التغييرات"]})})]})]})]})}):null}var M=a(35695);function J(){let{data:e}=(0,r.useSession)(),s=(0,M.useRouter)(),[a,A]=(0,l.useState)([]),[k,D]=(0,l.useState)([]),[z,E]=(0,l.useState)(!0),[F,J]=(0,l.useState)(""),[L,Z]=(0,l.useState)("all"),[I,P]=(0,l.useState)("folders"),[W,R]=(0,l.useState)(null),[T,q]=(0,l.useState)(!1),[B,_]=(0,l.useState)(!1),[H,G]=(0,l.useState)(!1),[U,V]=(0,l.useState)(!1),[K,Q]=(0,l.useState)(null),X=async()=>{try{E(!0);let e=await fetch("/api/gallery?groupBy=activity");if(!e.ok)throw Error("فشل في جلب المجلدات");let s=await e.json();D(s.folders||[])}catch(e){console.error("خطأ في جلب المجلدات:",e),D([])}finally{E(!1)}},Y=(0,l.useCallback)(async()=>{try{E(!0);let e=new URLSearchParams({search:F,category:L}),s=await fetch("/api/gallery?".concat(e));if(!s.ok)throw Error("فشل في جلب الصور");let a=await s.json();A(a.photos||[])}catch(e){console.error("خطأ في جلب الصور:",e),A([])}finally{E(!1)}},[F,L]);(0,l.useEffect)(()=>{e&&("folders"===I?X():Y())},[F,L,e,I]);let ee=e=>{R(e),q(!0)},es=async e=>{if(confirm("هل أنت متأكد من حذف هذه الصورة؟"))try{if(!(await fetch("/api/gallery/".concat(e),{method:"DELETE"})).ok)throw Error("فشل في حذف الصورة");Y()}catch(e){console.error("خطأ في حذف الصورة:",e),alert("حدث خطأ في حذف الصورة")}},ea=async e=>{try{if("activity"===e.type){let s=await fetch("/api/activities/".concat(e.id));if(s.ok){let e=await s.json();Q(e),V(!0)}}else if("folder"===e.type){let s=await fetch("/api/gallery-folders/".concat(e.id));if(s.ok){let e=await s.json();Q(e),V(!0)}}}catch(e){console.error("خطأ في جلب بيانات المجلد:",e),alert("حدث خطأ في جلب بيانات المجلد")}},et=async e=>{if("general"!==e.type&&confirm(e.photosCount>0?"هذا المجلد يحتوي على ".concat(e.photosCount," صورة. لا يمكن حذفه إلا بعد حذف جميع الصور أو نقلها إلى مجلد آخر."):'هل أنت متأكد من حذف المجلد "'.concat(e.title,'"؟')))try{let s="activity"===e.type?"/api/activities/".concat(e.id):"/api/gallery-folders/".concat(e.id),a=await fetch(s,{method:"DELETE"});if(!a.ok){let e=await a.json();throw Error(e.error||"فشل في حذف المجلد")}X()}catch(e){console.error("خطأ في حذف المجلد:",e),alert(e.message||"حدث خطأ في حذف المجلد")}},el=(null==e?void 0:e.user.role)!=="VIEWER",er=(null==e?void 0:e.user.role)==="ADMIN";return z?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"معرض الصور"}),(0,t.jsx)("p",{className:"text-gray-600",children:"عرض وإدارة صور الديوان والأنشطة"})]}),(0,t.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,t.jsx)("div",{className:"text-gray-500",children:"جاري التحميل..."})})]}):(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)("div",{className:"text-center mb-8",children:(0,t.jsxs)("div",{className:"bg-gradient-to-r from-purple-600 to-pink-600 rounded-3xl shadow-2xl p-10 text-white relative overflow-hidden",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-500 opacity-30 animate-pulse"}),(0,t.jsxs)("div",{className:"relative z-10",children:[(0,t.jsx)("div",{className:"inline-flex items-center justify-center w-20 h-20 rounded-full mb-6 bg-white bg-opacity-20 backdrop-blur-sm",children:(0,t.jsx)(x.A,{className:"w-10 h-10 text-white"})}),(0,t.jsx)("h1",{className:"text-5xl font-black mb-4 text-white",children:"معرض الصور"}),(0,t.jsx)("p",{className:"text-xl font-semibold mb-6 text-purple-100",children:"عرض وإدارة صور الديوان والأنشطة والمناسبات"}),(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2 space-x-reverse",children:[(0,t.jsx)("div",{className:"h-1 w-16 rounded-full bg-white bg-opacity-60"}),(0,t.jsx)("div",{className:"h-1 w-8 rounded-full bg-white bg-opacity-40"}),(0,t.jsx)("div",{className:"h-1 w-16 rounded-full bg-white bg-opacity-60"})]})]})]})}),(0,t.jsx)("div",{className:"flex justify-center mb-8",children:(0,t.jsx)("div",{className:"bg-white rounded-2xl shadow-xl p-6 border border-gray-100",children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-4 justify-center",children:[(0,t.jsxs)("div",{className:"flex bg-gray-100 rounded-xl p-1 border border-gray-200",children:[(0,t.jsxs)(i.$,{variant:"folders"===I?"default":"ghost",size:"sm",onClick:()=>P("folders"),className:"text-sm transition-all px-4 py-2 rounded-lg ".concat("folders"===I?"bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-md":"text-gray-600 hover:bg-gray-200"),children:[(0,t.jsx)(m.A,{className:"w-4 h-4 ml-2"}),"المجلدات"]}),(0,t.jsxs)(i.$,{variant:"photos"===I?"default":"ghost",size:"sm",onClick:()=>P("photos"),className:"text-sm transition-all px-4 py-2 rounded-lg ".concat("photos"===I?"bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-md":"text-gray-600 hover:bg-gray-200"),children:[(0,t.jsx)(x.A,{className:"w-4 h-4 ml-2"}),"جميع الصور"]})]}),el&&(0,t.jsxs)("div",{className:"flex gap-3",children:["folders"===I&&(0,t.jsxs)(i.$,{onClick:()=>G(!0),className:"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold",children:[(0,t.jsx)(h.A,{className:"w-5 h-5 ml-2"}),"إنشاء مجلد جديد"]}),(0,t.jsxs)(i.$,{onClick:()=>_(!0),className:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold",children:[(0,t.jsx)(g.A,{className:"w-5 h-5 ml-2"}),"رفع صور جديدة"]})]})]})})}),"folders"===I?(0,t.jsx)(S,{folders:k}):(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4",children:[(0,t.jsxs)(n.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-500 to-blue-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,t.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-blue-600 p-1 rounded-t-xl"}),(0,t.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,t.jsx)(n.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"إجمالي الصور"}),(0,t.jsx)("div",{className:"p-4 rounded-2xl shadow-lg",style:{backgroundColor:"#007bff"},children:(0,t.jsx)(x.A,{className:"h-7 w-7 text-white"})})]}),(0,t.jsxs)(n.Wu,{className:"relative z-10",children:[(0,t.jsx)("div",{className:"text-4xl font-black mb-2",style:{color:"#191970"},children:a.length}),(0,t.jsx)("p",{className:"text-sm font-semibold",style:{color:"#6c757d"},children:"صورة في المعرض"})]})]}),(0,t.jsxs)(n.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-green-500 to-green-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,t.jsx)("div",{className:"bg-gradient-to-r from-green-500 to-green-600 p-1 rounded-t-xl"}),(0,t.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,t.jsx)(n.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"صور الأنشطة"}),(0,t.jsx)("div",{className:"p-4 rounded-2xl shadow-lg",style:{backgroundColor:"#28a745"},children:(0,t.jsx)(u.A,{className:"h-7 w-7 text-white"})})]}),(0,t.jsxs)(n.Wu,{className:"relative z-10",children:[(0,t.jsx)("div",{className:"text-4xl font-black mb-2",style:{color:"#191970"},children:a.filter(e=>e.activityId).length}),(0,t.jsx)("p",{className:"text-sm font-semibold",style:{color:"#6c757d"},children:"صورة مرتبطة بالأنشطة"})]})]}),(0,t.jsxs)(n.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-purple-500 to-purple-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,t.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-purple-600 p-1 rounded-t-xl"}),(0,t.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,t.jsx)(n.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"صور عامة"}),(0,t.jsx)("div",{className:"p-4 rounded-2xl shadow-lg",style:{backgroundColor:"#800020"},children:(0,t.jsx)(p.A,{className:"h-7 w-7 text-white"})})]}),(0,t.jsxs)(n.Wu,{className:"relative z-10",children:[(0,t.jsx)("div",{className:"text-4xl font-black mb-2",style:{color:"#191970"},children:a.filter(e=>!e.activityId).length}),(0,t.jsx)("p",{className:"text-sm font-semibold",style:{color:"#6c757d"},children:"صورة غير مصنفة"})]})]}),(0,t.jsxs)(n.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-orange-500 to-orange-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,t.jsx)("div",{className:"bg-gradient-to-r from-orange-500 to-orange-600 p-1 rounded-t-xl"}),(0,t.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,t.jsx)(n.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"المجلدات"}),(0,t.jsx)("div",{className:"p-4 rounded-2xl shadow-lg",style:{backgroundColor:"#fd7e14"},children:(0,t.jsx)(m.A,{className:"h-7 w-7 text-white"})})]}),(0,t.jsxs)(n.Wu,{className:"relative z-10",children:[(0,t.jsx)("div",{className:"text-4xl font-black mb-2",style:{color:"#191970"},children:k.length}),(0,t.jsx)("p",{className:"text-sm font-semibold",style:{color:"#6c757d"},children:"مجلد منظم"})]})]})]}),"photos"===I&&(0,t.jsx)(n.Zp,{className:"border-0 shadow-md",children:(0,t.jsxs)(n.Wu,{className:"pt-6",children:[(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4",children:[(0,t.jsxs)("div",{className:"relative flex-1",children:[(0,t.jsx)(j.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,t.jsx)(d.p,{placeholder:"البحث في الصور بالعنوان أو الوصف...",value:F,onChange:e=>J(e.target.value),className:"search-input pr-12 h-11 border-gray-200 focus:border-diwan-500 focus:ring-diwan-500"})]}),(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsx)("select",{value:L,onChange:e=>Z(e.target.value),className:"px-4 py-2 h-11 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-diwan-500 focus:border-diwan-500 bg-white min-w-[150px]",children:[{value:"all",label:"جميع الفئات"},{value:"activities",label:"الأنشطة"},{value:"members",label:"الأعضاء"},{value:"events",label:"المناسبات"},{value:"meetings",label:"الاجتماعات"},{value:"other",label:"أخرى"}].map(e=>(0,t.jsx)("option",{value:e.value,children:e.label},e.value))}),(F||"all"!==L)&&(0,t.jsxs)(i.$,{variant:"outline",onClick:()=>{J(""),Z("all")},className:"h-11 px-4 border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300 font-medium",children:[(0,t.jsx)(b.A,{className:"w-4 h-4 ml-2"}),"مسح الفلاتر"]})]})]}),F&&(0,t.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:[(0,t.jsx)("span",{className:"font-medium",children:a.length}),' نتيجة للبحث عن "',F,'"']})]})}),(0,t.jsx)(n.Zp,{className:"border-0 shadow-md",children:(0,t.jsx)(n.Wu,{className:"p-6",children:"folders"===I?(0,t.jsx)(C,{folders:k,onFolderClick:e=>{"folder"===e.type?s.push("/dashboard/gallery/folder/".concat(e.id,"?type=folder")):"activity"===e.type?s.push("/dashboard/gallery/folder/".concat(e.id,"?type=activity")):s.push("/dashboard/gallery/folder/general")},onEditFolder:ea,onDeleteFolder:et,canEdit:el,canDelete:er,loading:z}):(0,t.jsx)(t.Fragment,{children:0===a.length?(0,t.jsxs)("div",{className:"text-center py-16",children:[(0,t.jsx)("div",{className:"bg-gray-50 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6",children:(0,t.jsx)(x.A,{className:"w-12 h-12 text-gray-400"})}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"لا توجد صور"}),(0,t.jsx)("p",{className:"text-gray-500 mb-6",children:F?"لم يتم العثور على صور تطابق البحث":"ابدأ بإضافة صور إلى المعرض"}),el&&!F&&(0,t.jsxs)(i.$,{onClick:()=>_(!0),className:"bg-diwan-600 hover:bg-diwan-700 text-white font-semibold px-8 py-3 h-12 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200",children:[(0,t.jsx)(g.A,{className:"w-5 h-5 ml-2"}),"رفع أول صورة"]})]}):(0,t.jsx)("div",{className:"gallery-grid",children:a.map(e=>(0,t.jsxs)("div",{className:"gallery-card group relative bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden border border-gray-100",children:[(0,t.jsxs)("div",{className:"aspect-square overflow-hidden bg-gray-100 relative",children:[(0,t.jsx)("img",{src:e.imagePath,alt:e.title,className:"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500 cursor-pointer",onClick:()=>ee(e)}),(0,t.jsxs)("div",{className:"floating-buttons absolute top-3 left-3 flex gap-2",children:[(0,t.jsx)(i.$,{variant:"secondary",size:"sm",onClick:()=>ee(e),className:"h-9 w-9 p-0 bg-blue-600 hover:bg-blue-700 text-white shadow-lg border-0 rounded-full",title:"عرض الصورة",children:(0,t.jsx)(v.A,{className:"w-4 h-4"})}),el&&(0,t.jsx)(i.$,{variant:"secondary",size:"sm",className:"h-9 w-9 p-0 bg-green-600 hover:bg-green-700 text-white shadow-lg border-0 rounded-full",title:"تعديل الصورة",children:(0,t.jsx)(f.A,{className:"w-4 h-4"})}),(0,t.jsx)(i.$,{variant:"secondary",size:"sm",className:"h-9 w-9 p-0 bg-purple-600 hover:bg-purple-700 text-white shadow-lg border-0 rounded-full",title:"تحميل الصورة",onClick:()=>{let s=document.createElement("a");s.href=e.imagePath,s.download=e.title,s.click()},children:(0,t.jsx)(N.A,{className:"w-4 h-4"})}),er&&(0,t.jsx)(i.$,{variant:"secondary",size:"sm",onClick:()=>es(e.id),className:"h-9 w-9 p-0 bg-red-600 hover:bg-red-700 text-white shadow-lg border-0 rounded-full",title:"حذف الصورة",children:(0,t.jsx)(y.A,{className:"w-4 h-4"})})]}),(0,t.jsx)("div",{className:"absolute bottom-2 right-2",children:(0,t.jsx)(c.E,{variant:e.activityId?"default":"secondary",className:"text-xs ".concat(e.activityId?"bg-diwan-600":"bg-gray-500"),children:e.activityId?"نشاط":"عام"})})]}),(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsx)("h3",{className:"text-sm font-semibold text-gray-900 truncate mb-1",children:e.title}),e.description&&(0,t.jsx)("p",{className:"text-xs text-gray-500 line-clamp-2 mb-2",children:e.description}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-400",children:[(0,t.jsx)("span",{children:e.uploader.name}),(0,t.jsx)("span",{children:new Date(e.uploadedAt).toLocaleDateString("ar-JO")})]}),e.activity&&(0,t.jsx)("div",{className:"mt-2 text-xs text-diwan-600 bg-diwan-50 px-2 py-1 rounded truncate",children:e.activity.title})]})]},e.id))})})})}),(0,t.jsx)(o.lG,{open:T,onOpenChange:q,children:(0,t.jsx)(o.Cf,{className:"max-w-6xl max-h-[95vh] overflow-hidden p-0",children:W&&(0,t.jsxs)("div",{className:"flex flex-col h-full",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-6 border-b bg-white",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:W.title}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["رفعت بواسطة ",W.uploader.name," في ",new Date(W.uploadedAt).toLocaleDateString("ar-JO")]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>{let e=document.createElement("a");e.href=W.imagePath,e.download=W.title,e.click()},className:"bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100 hover:border-blue-300 font-medium px-4 py-2 h-9",children:[(0,t.jsx)(N.A,{className:"w-4 h-4 ml-2"}),"تحميل"]}),el&&(0,t.jsxs)(i.$,{variant:"outline",size:"sm",className:"bg-green-50 border-green-200 text-green-700 hover:bg-green-100 hover:border-green-300 font-medium px-4 py-2 h-9",children:[(0,t.jsx)(f.A,{className:"w-4 h-4 ml-2"}),"تعديل"]}),er&&(0,t.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>{q(!1),es(W.id)},className:"bg-red-50 border-red-200 text-red-700 hover:bg-red-100 hover:border-red-300 font-medium px-4 py-2 h-9",children:[(0,t.jsx)(y.A,{className:"w-4 h-4 ml-2"}),"حذف"]})]})]}),(0,t.jsx)("div",{className:"flex-1 bg-gray-900 flex items-center justify-center p-4",children:(0,t.jsx)("img",{src:W.imagePath,alt:W.title,className:"max-w-full max-h-full object-contain rounded-lg shadow-2xl"})}),(0,t.jsx)("div",{className:"p-6 bg-gray-50 border-t",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2",children:[(0,t.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"الوصف"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed",children:W.description||"لا يوجد وصف لهذه الصورة"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-gray-900 mb-3",children:"التفاصيل"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-gray-500",children:"الفئة:"}),(0,t.jsx)(c.E,{variant:W.activityId?"default":"secondary",className:"text-xs",children:W.activityId?"نشاط":"عام"})]}),W.activity&&(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-gray-500",children:"النشاط:"}),(0,t.jsx)("span",{className:"text-diwan-600 font-medium",children:W.activity.title})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-gray-500",children:"تاريخ الرفع:"}),(0,t.jsx)("span",{className:"text-gray-900",children:new Date(W.uploadedAt).toLocaleDateString("ar-JO")})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-gray-500",children:"رفعت بواسطة:"}),(0,t.jsx)("span",{className:"text-gray-900",children:W.uploader.name})]})]})]})]})})]})})}),(0,t.jsx)(w.A,{open:B,onOpenChange:_,onSuccess:()=>{"folders"===I?X():Y()}}),(0,t.jsx)(O,{open:H,onOpenChange:G,onSuccess:X}),(0,t.jsx)($,{open:U,onOpenChange:V,onSuccess:X,activity:K})]})}},8576:()=>{},17580:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},53311:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},68075:(e,s,a)=>{Promise.resolve().then(a.bind(a,6149))},84616:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},91788:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[4561,1778,2108,3942,5217,8130,3068,5110,8441,1684,7358],()=>s(68075)),_N_E=e.O()}]);