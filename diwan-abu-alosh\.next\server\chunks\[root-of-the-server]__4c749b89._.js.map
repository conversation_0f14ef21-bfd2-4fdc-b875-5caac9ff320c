{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Cred<PERSON><PERSON><PERSON><PERSON>ider from 'next-auth/providers/credentials'\nimport bcrypt from 'bcryptjs'\nimport { prisma } from './prisma'\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'البريد الإلكتروني', type: 'email' },\n        password: { label: 'كلمة المرور', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          role: user.role,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAqB,MAAM;gBAAQ;gBACnD,UAAU;oBAAE,OAAO;oBAAe,MAAM;gBAAW;YACrD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;YACxB;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;YAChC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/app/api/expenses/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\nimport { z } from 'zod'\n\n// Schema للتحقق من البيانات الواردة من النموذج\nconst expenseInputSchema = z.object({\n  amount: z.number().positive('المبلغ يجب أن يكون أكبر من صفر'),\n  date: z.string().transform((str) => new Date(str)),\n  description: z.string().min(1, 'الوصف مطلوب'),\n  category: z.enum(['MEETINGS', 'EVENTS', 'MAINTENANCE', 'SOCIAL', 'GENERAL']),\n  recipient: z.string().optional().nullable(),\n  notes: z.string().optional().nullable(),\n})\n\n// GET - جلب جميع المصروفات مع البحث والتصفية\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    if (!session) {\n      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })\n    }\n\n    const { searchParams } = new URL(request.url)\n    const search = searchParams.get('search') || ''\n    const category = searchParams.get('category') || 'all'\n    const page = parseInt(searchParams.get('page') || '1')\n    const limit = parseInt(searchParams.get('limit') || '10')\n    const skip = (page - 1) * limit\n\n    // بناء شروط البحث\n    const where: Record<string, unknown> = {}\n    \n    if (search) {\n      where.OR = [\n        { description: { contains: search } },\n        { recipient: { contains: search } },\n        { notes: { contains: search } },\n      ]\n    }\n\n    if (category !== 'all') {\n      where.category = category\n    }\n\n    // جلب المصروفات مع العد الكلي\n    const [expenses, total] = await Promise.all([\n      prisma.expense.findMany({\n        where,\n        skip,\n        take: limit,\n        orderBy: { date: 'desc' },\n        include: {\n          createdBy: {\n            select: {\n              name: true,\n            },\n          },\n        },\n      }),\n      prisma.expense.count({ where }),\n    ])\n\n    return NextResponse.json({\n      expenses,\n      pagination: {\n        page,\n        limit,\n        total,\n        pages: Math.ceil(total / limit),\n      },\n    })\n  } catch (error) {\n    console.error('خطأ في جلب المصروفات:', error)\n    return NextResponse.json(\n      { error: 'حدث خطأ في جلب المصروفات' },\n      { status: 500 }\n    )\n  }\n}\n\n// POST - إضافة مصروف جديد\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    if (!session) {\n      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })\n    }\n\n    // التحقق من الصلاحيات\n    if (session.user.role === 'VIEWER') {\n      return NextResponse.json(\n        { error: 'ليس لديك صلاحية لإضافة المصروفات' },\n        { status: 403 }\n      )\n    }\n\n    const body = await request.json()\n\n    // التحقق من صحة البيانات\n    const validatedData = expenseInputSchema.parse(body)\n\n    // إنشاء المصروف\n    const expense = await prisma.expense.create({\n      data: {\n        ...validatedData,\n        createdById: session.user.id,\n      },\n      include: {\n        createdBy: {\n          select: {\n            name: true,\n          },\n        },\n      },\n    })\n\n    return NextResponse.json(expense, { status: 201 })\n  } catch (error: unknown) {\n    console.error('خطأ في إضافة المصروف:', error)\n    \n    if (error.name === 'ZodError') {\n      return NextResponse.json(\n        { error: 'بيانات غير صحيحة', details: error.errors },\n        { status: 400 }\n      )\n    }\n\n    return NextResponse.json(\n      { error: 'حدث خطأ في إضافة المصروف' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;;;;;;AAEA,+CAA+C;AAC/C,MAAM,qBAAqB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC5B,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,MAAQ,IAAI,KAAK;IAC7C,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,UAAU,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAY;QAAU;QAAe;QAAU;KAAU;IAC3E,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACzC,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;AACvC;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAW,GAAG;gBAAE,QAAQ;YAAI;QAChE;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,WAAW,aAAa,GAAG,CAAC,eAAe;QACjD,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,kBAAkB;QAClB,MAAM,QAAiC,CAAC;QAExC,IAAI,QAAQ;YACV,MAAM,EAAE,GAAG;gBACT;oBAAE,aAAa;wBAAE,UAAU;oBAAO;gBAAE;gBACpC;oBAAE,WAAW;wBAAE,UAAU;oBAAO;gBAAE;gBAClC;oBAAE,OAAO;wBAAE,UAAU;oBAAO;gBAAE;aAC/B;QACH;QAEA,IAAI,aAAa,OAAO;YACtB,MAAM,QAAQ,GAAG;QACnB;QAEA,8BAA8B;QAC9B,MAAM,CAAC,UAAU,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC1C,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;gBACtB;gBACA;gBACA,MAAM;gBACN,SAAS;oBAAE,MAAM;gBAAO;gBACxB,SAAS;oBACP,WAAW;wBACT,QAAQ;4BACN,MAAM;wBACR;oBACF;gBACF;YACF;YACA,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;gBAAE;YAAM;SAC9B;QAED,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA,YAAY;gBACV;gBACA;gBACA;gBACA,OAAO,KAAK,IAAI,CAAC,QAAQ;YAC3B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2B,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAW,GAAG;gBAAE,QAAQ;YAAI;QAChE;QAEA,sBAAsB;QACtB,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,UAAU;YAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmC,GAC5C;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,yBAAyB;QACzB,MAAM,gBAAgB,mBAAmB,KAAK,CAAC;QAE/C,gBAAgB;QAChB,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1C,MAAM;gBACJ,GAAG,aAAa;gBAChB,aAAa,QAAQ,IAAI,CAAC,EAAE;YAC9B;YACA,SAAS;gBACP,WAAW;oBACT,QAAQ;wBACN,MAAM;oBACR;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,SAAS;YAAE,QAAQ;QAAI;IAClD,EAAE,OAAO,OAAgB;QACvB,QAAQ,KAAK,CAAC,yBAAyB;QAEvC,IAAI,MAAM,IAAI,KAAK,YAAY;YAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAoB,SAAS,MAAM,MAAM;YAAC,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2B,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}