(()=>{var e={};e.id=8237,e.ids=[8237],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},11726:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>h,tree:()=>d});var r=t(65239),a=t(48088),i=t(88170),l=t.n(i),n=t(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(s,o);let d={children:["",{children:["dashboard",{children:["gallery",{children:["folder",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,54155)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\gallery\\folder\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,63144)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\gallery\\folder\\[id]\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/gallery/folder/[id]/page",pathname:"/dashboard/gallery/folder/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},12412:e=>{"use strict";e.exports=require("assert")},16202:(e,s,t)=>{Promise.resolve().then(t.bind(t,33637))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33637:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>w});var r=t(60687),a=t(43210),i=t(82136),l=t(16189),n=t(29523),o=t(89667),d=t(44493),c=t(96834),p=t(63503),h=t(70334),x=t(82570),m=t(16023),u=t(40228),g=t(58869),v=t(9005),j=t(99270),f=t(13861),y=t(63143),N=t(88233),b=t(93372);function w(){let{data:e}=(0,i.useSession)(),s=(0,l.useParams)(),t=(0,l.useRouter)(),w=(0,l.useSearchParams)(),C=s.id,k=w.get("type")||"activity",[A,q]=(0,a.useState)([]),[P,D]=(0,a.useState)(null),[E,_]=(0,a.useState)(!0),[$,S]=(0,a.useState)(""),[I,O]=(0,a.useState)(null),[G,U]=(0,a.useState)(!1),[z,R]=(0,a.useState)(!1),F=(0,a.useCallback)(async()=>{try{let e;_(!0);let s="/api/gallery?";if("general"===C)s+="activityId=null&folderId=null",e={id:"general",title:"الصور العامة",description:"صور غير مرتبطة بأنشطة أو مجلدات محددة",type:"general",photosCount:0};else if("folder"===k){s+=`folderId=${C}`;try{let s=await fetch(`/api/gallery-folders/${C}`);if(s.ok){let t=await s.json();e={id:C,title:t.title,description:t.description,type:"folder",photosCount:0}}else throw Error("فشل في جلب معلومات المجلد")}catch(s){console.error("خطأ في جلب معلومات المجلد:",s),e={id:C,title:"مجلد غير معروف",description:"لا يمكن جلب معلومات المجلد",type:"folder",photosCount:0}}}else{s+=`activityId=${C}`;try{let s=await fetch(`/api/activities/${C}`);if(s.ok){let t=await s.json();e={id:C,title:t.title,description:t.description,type:"activity",photosCount:0}}else throw Error("فشل في جلب معلومات النشاط")}catch(s){console.error("خطأ في جلب معلومات النشاط:",s),e={id:C,title:"نشاط غير معروف",description:"لا يمكن جلب معلومات النشاط",type:"activity",photosCount:0}}}D(e),$&&(s+=`&search=${encodeURIComponent($)}`);let t=await fetch(s);if(!t.ok)throw Error("فشل في جلب الصور");let r=await t.json();q(r.photos||[]),D(e=>e?{...e,photosCount:r.photos?.length||0}:null)}catch(e){console.error("خطأ في جلب بيانات المجلد:",e),q([])}finally{_(!1)}},[$,C,k]),M=e=>{O(e),U(!0)},L=async e=>{if(confirm("هل أنت متأكد من حذف هذه الصورة؟"))try{if(!(await fetch(`/api/gallery/${e}`,{method:"DELETE"})).ok)throw Error("فشل في حذف الصورة");F()}catch(e){console.error("خطأ في حذف الصورة:",e),alert("حدث خطأ في حذف الصورة")}},W=e?.user.role!=="VIEWER",T=e?.user.role==="ADMIN";return E?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3 mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:[...Array(8)].map((e,s)=>(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"aspect-square bg-gray-200 rounded-lg mb-3"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]},s))})]}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4 space-x-reverse",children:[(0,r.jsxs)(n.$,{variant:"outline",onClick:()=>t.push("/dashboard/gallery"),className:"flex items-center",children:[(0,r.jsx)(h.A,{className:"w-4 h-4 ml-2"}),"العودة للمعرض"]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,r.jsx)(x.A,{className:"w-6 h-6 text-diwan-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:P?.title||"مجلد الصور"}),P?.description&&(0,r.jsx)("p",{className:"text-gray-600",children:P.description})]})]})]}),(0,r.jsx)("div",{className:"flex space-x-2 space-x-reverse",children:W&&(0,r.jsxs)(n.$,{onClick:()=>R(!0),className:"bg-diwan-600 hover:bg-diwan-700",children:[(0,r.jsx)(m.A,{className:"w-4 h-4 ml-2"}),"إضافة صور"]})})]}),(0,r.jsx)(d.Zp,{children:(0,r.jsx)(d.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4 space-x-reverse",children:[(0,r.jsx)(c.E,{variant:"secondary",className:`${P?.type==="activity"?"bg-green-100 text-green-700":P?.type==="folder"?"bg-purple-100 text-purple-700":"bg-blue-100 text-blue-700"}`,children:P?.type==="activity"?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u.A,{className:"w-3 h-3 ml-1"}),"نشاط"]}):P?.type==="folder"?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.A,{className:"w-3 h-3 ml-1"}),"مجلد"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(g.A,{className:"w-3 h-3 ml-1"}),"عام"]})}),(0,r.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,r.jsx)(v.A,{className:"w-4 h-4 ml-1"}),A.length," صورة"]})]}),(0,r.jsxs)("div",{className:"relative w-64",children:[(0,r.jsx)(j.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,r.jsx)(o.p,{placeholder:"البحث في الصور...",value:$,onChange:e=>S(e.target.value),className:"pr-10"})]})]})})}),(0,r.jsx)(d.Zp,{children:(0,r.jsx)(d.Wu,{className:"p-6",children:0===A.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(v.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-500",children:"لا توجد صور في هذا المجلد"}),W&&(0,r.jsxs)(n.$,{onClick:()=>R(!0),className:"mt-4",variant:"outline",children:[(0,r.jsx)(m.A,{className:"w-4 h-4 ml-2"}),"إضافة أول صورة"]})]}):(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:A.map(e=>(0,r.jsxs)("div",{className:"group relative",children:[(0,r.jsx)("div",{className:"aspect-square overflow-hidden rounded-lg bg-gray-100",children:(0,r.jsx)("img",{src:e.imagePath,alt:e.title,className:"w-full h-full object-cover group-hover:scale-105 transition-transform duration-200 cursor-pointer",onClick:()=>M(e)})}),(0,r.jsxs)("div",{className:"mt-3",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-gray-900 truncate",children:e.title}),e.description&&(0,r.jsx)("p",{className:"text-xs text-gray-500 truncate mt-1",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:e.uploader.name}),(0,r.jsxs)("div",{className:"flex space-x-1 space-x-reverse",children:[(0,r.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>M(e),className:"text-blue-600 hover:text-blue-700",title:"عرض",children:(0,r.jsx)(f.A,{className:"w-3 h-3"})}),W&&(0,r.jsx)(n.$,{variant:"ghost",size:"sm",className:"text-green-600 hover:text-green-700",title:"تعديل",children:(0,r.jsx)(y.A,{className:"w-3 h-3"})}),T&&(0,r.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>L(e.id),className:"text-red-600 hover:text-red-700",title:"حذف",children:(0,r.jsx)(N.A,{className:"w-3 h-3"})})]})]})]})]},e.id))})})}),(0,r.jsx)(p.lG,{open:G,onOpenChange:U,children:(0,r.jsxs)(p.Cf,{className:"max-w-4xl",children:[(0,r.jsx)(p.c7,{children:(0,r.jsx)(p.L3,{children:I?.title})}),I&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"aspect-video overflow-hidden rounded-lg bg-gray-100",children:(0,r.jsx)("img",{src:I.imagePath,alt:I.title,className:"w-full h-full object-contain"})}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"تفاصيل الصورة"}),(0,r.jsxs)("div",{className:"space-y-1 text-gray-600",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"الوصف:"})," ",I.description||"لا يوجد"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"رفعت بواسطة:"})," ",I.uploader.name]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"تاريخ الرفع:"})," ",new Date(I.uploadedAt).toLocaleDateString("ar-JO")]}),I.activity&&(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"النشاط:"})," ",I.activity.title]}),I.folder&&(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"المجلد:"})," ",I.folder.title]})]})]})})]})]})}),(0,r.jsx)(b.A,{open:z,onOpenChange:R,onSuccess:F,defaultFolderId:"folder"===k?C:void 0,defaultActivityId:"activity"===k?C:void 0})]})}},33873:e=>{"use strict";e.exports=require("path")},54155:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\ابوعلوش\\\\diwan-abu-alosh\\\\src\\\\app\\\\dashboard\\\\gallery\\\\folder\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\gallery\\folder\\[id]\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},98050:(e,s,t)=>{Promise.resolve().then(t.bind(t,54155))}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,5663,4999,3412,5442,7934,5498,1726,2131,5662,2635,5977,6154,3352],()=>t(11726));module.exports=r})();