import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { incomeSchema } from '@/lib/validations'

// تعديل إيراد
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // التحقق من الصلاحيات
    if (session.user.role === 'VIEWER') {
      return NextResponse.json({ error: 'ليس لديك صلاحية لتعديل الإيرادات' }, { status: 403 })
    }

    const { id } = await params
    const body = await request.json()

    // تحويل التاريخ من string إلى Date
    if (body.date) {
      body.date = new Date(body.date)
    }

    // تنظيف البيانات قبل التحقق
    const cleanedBody = {
      ...body,
      description: body.description?.trim() || undefined,
      notes: body.notes?.trim() || undefined,
      memberId: body.memberId === 'none' || !body.memberId ? undefined : body.memberId,
    }

    // التحقق من وجود الإيراد
    const existingIncome = await prisma.income.findUnique({
      where: { id }
    })

    if (!existingIncome) {
      return NextResponse.json({ error: 'الإيراد غير موجود' }, { status: 404 })
    }

    // التحقق من صحة البيانات باستخدام schema
    const validatedData = incomeSchema.parse(cleanedBody)

    // التحقق من وجود العضو إذا تم تحديده
    if (validatedData.memberId) {
      const member = await prisma.member.findUnique({
        where: { id: validatedData.memberId }
      })

      if (!member) {
        return NextResponse.json({ error: 'العضو المحدد غير موجود' }, { status: 400 })
      }
    }

    // التحقق من وجود المستخدم أو إنشاؤه إذا لم يكن موجوداً
    let user = await prisma.user.findUnique({
      where: { id: session.user.id },
    })

    if (!user) {
      // إنشاء المستخدم إذا لم يكن موجوداً
      user = await prisma.user.create({
        data: {
          id: session.user.id,
          name: session.user.name || 'مستخدم غير معروف',
          email: session.user.email || '',
          password: 'temp-password', // كلمة مرور مؤقتة
          role: (session.user.role as 'ADMIN' | 'EDITOR' | 'VIEWER') || 'VIEWER',
        },
      })
    }

    // تعديل الإيراد
    const updatedIncome = await prisma.income.update({
      where: { id },
      data: {
        amount: validatedData.amount,
        date: validatedData.date,
        source: validatedData.source,
        type: validatedData.type,
        description: validatedData.description || null,
        notes: validatedData.notes || null,
        memberId: validatedData.memberId || null,
        updatedById: session.user.id,
      },
      include: {
        member: {
          select: {
            id: true,
            name: true,
          }
        },
        createdBy: {
          select: {
            name: true,
          }
        },
        updatedBy: {
          select: {
            name: true,
          }
        }
      }
    })

    return NextResponse.json(updatedIncome)
  } catch (error: unknown) {
    console.error('خطأ في تعديل الإيراد:', error)

    if (error.name === 'ZodError') {
      console.log('أخطاء التحقق من البيانات:', error.errors)
      const errorMessages = (error as { errors: Array<{ path: string[]; message: string }> }).errors.map((err) => `${err.path.join('.')}: ${err.message}`).join(', ')
      return NextResponse.json(
        {
          error: `بيانات غير صحيحة: ${errorMessages}`,
          details: error.errors
        },
        { status: 400 }
      )
    }

    // خطأ في قاعدة البيانات
    if (error.code === 'P2003') {
      return NextResponse.json(
        { error: 'خطأ في المرجع: تأكد من صحة معرف العضو أو المستخدم' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'حدث خطأ في تعديل الإيراد: ' + error.message },
      { status: 500 }
    )
  }
}

// حذف إيراد
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // التحقق من الصلاحيات - فقط الأدمن يمكنه الحذف
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'ليس لديك صلاحية لحذف الإيرادات' }, { status: 403 })
    }

    const { id } = await params

    // التحقق من وجود الإيراد
    const existingIncome = await prisma.income.findUnique({
      where: { id }
    })

    if (!existingIncome) {
      return NextResponse.json({ error: 'الإيراد غير موجود' }, { status: 404 })
    }

    // حذف الإيراد
    await prisma.income.delete({
      where: { id }
    })

    return NextResponse.json({ message: 'تم حذف الإيراد بنجاح' })
  } catch (error) {
    console.error('خطأ في حذف الإيراد:', error)
    return NextResponse.json({ error: 'حدث خطأ في حذف الإيراد' }, { status: 500 })
  }
}

// جلب إيراد محدد
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    const { id } = await params

    const income = await prisma.income.findUnique({
      where: { id },
      include: {
        member: {
          select: {
            id: true,
            name: true,
          }
        },
        createdBy: {
          select: {
            name: true,
          }
        },
        updatedBy: {
          select: {
            name: true,
          }
        }
      }
    })

    if (!income) {
      return NextResponse.json({ error: 'الإيراد غير موجود' }, { status: 404 })
    }

    return NextResponse.json(income)
  } catch (error) {
    console.error('خطأ في جلب الإيراد:', error)
    return NextResponse.json({ error: 'حدث خطأ في جلب الإيراد' }, { status: 500 })
  }
}
