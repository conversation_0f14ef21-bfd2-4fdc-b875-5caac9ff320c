(()=>{var e={};e.id=9844,e.ids=[9844],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{N:()=>o});var s=t(13581),n=t(85663),i=t(31183);let o={providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"البريد الإلكتروني",type:"email"},password:{label:"كلمة المرور",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await i.z.user.findUnique({where:{email:e.email}});return r&&await n.Ay.compare(e.password,r.password)?{id:r.id,email:r.email,name:r.name,role:r.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>n});var s=t(96330);let n=globalThis.prisma??new s.PrismaClient},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61886:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>N,routeModule:()=>g,serverHooks:()=>x,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>w});var s={};t.r(s),t.d(s,{DELETE:()=>m,GET:()=>z,PUT:()=>c});var n=t(96559),i=t(48088),o=t(37719),a=t(32190),u=t(19854),l=t(12909),p=t(31183),d=t(85463);async function c(e,{params:r}){try{let t=await (0,u.getServerSession)(l.N);if(!t)return a.NextResponse.json({error:"غير مصرح"},{status:401});if("VIEWER"===t.user.role)return a.NextResponse.json({error:"ليس لديك صلاحية لتعديل الإيرادات"},{status:403});let{id:s}=await r,n=await e.json();n.date&&(n.date=new Date(n.date));let i={...n,description:n.description?.trim()||void 0,notes:n.notes?.trim()||void 0,memberId:"none"!==n.memberId&&n.memberId?n.memberId:void 0};if(!await p.z.income.findUnique({where:{id:s}}))return a.NextResponse.json({error:"الإيراد غير موجود"},{status:404});let o=d.J8.parse(i);if(o.memberId&&!await p.z.member.findUnique({where:{id:o.memberId}}))return a.NextResponse.json({error:"العضو المحدد غير موجود"},{status:400});let c=await p.z.user.findUnique({where:{id:t.user.id}});c||(c=await p.z.user.create({data:{id:t.user.id,name:t.user.name||"مستخدم غير معروف",email:t.user.email||"",password:"temp-password",role:t.user.role||"VIEWER"}}));let m=await p.z.income.update({where:{id:s},data:{amount:o.amount,date:o.date,source:o.source,type:o.type,description:o.description||null,notes:o.notes||null,memberId:o.memberId||null,updatedById:t.user.id},include:{member:{select:{id:!0,name:!0}},createdBy:{select:{name:!0}},updatedBy:{select:{name:!0}}}});return a.NextResponse.json(m)}catch(e){if(console.error("خطأ في تعديل الإيراد:",e),"ZodError"===e.name){console.log("أخطاء التحقق من البيانات:",e.errors);let r=e.errors.map(e=>`${e.path.join(".")}: ${e.message}`).join(", ");return a.NextResponse.json({error:`بيانات غير صحيحة: ${r}`,details:e.errors},{status:400})}if("P2003"===e.code)return a.NextResponse.json({error:"خطأ في المرجع: تأكد من صحة معرف العضو أو المستخدم"},{status:400});return a.NextResponse.json({error:"حدث خطأ في تعديل الإيراد: "+e.message},{status:500})}}async function m(e,{params:r}){try{let e=await (0,u.getServerSession)(l.N);if(!e)return a.NextResponse.json({error:"غير مصرح"},{status:401});if("ADMIN"!==e.user.role)return a.NextResponse.json({error:"ليس لديك صلاحية لحذف الإيرادات"},{status:403});let{id:t}=await r;if(!await p.z.income.findUnique({where:{id:t}}))return a.NextResponse.json({error:"الإيراد غير موجود"},{status:404});return await p.z.income.delete({where:{id:t}}),a.NextResponse.json({message:"تم حذف الإيراد بنجاح"})}catch(e){return console.error("خطأ في حذف الإيراد:",e),a.NextResponse.json({error:"حدث خطأ في حذف الإيراد"},{status:500})}}async function z(e,{params:r}){try{if(!await (0,u.getServerSession)(l.N))return a.NextResponse.json({error:"غير مصرح"},{status:401});let{id:e}=await r,t=await p.z.income.findUnique({where:{id:e},include:{member:{select:{id:!0,name:!0}},createdBy:{select:{name:!0}},updatedBy:{select:{name:!0}}}});if(!t)return a.NextResponse.json({error:"الإيراد غير موجود"},{status:404});return a.NextResponse.json(t)}catch(e){return console.error("خطأ في جلب الإيراد:",e),a.NextResponse.json({error:"حدث خطأ في جلب الإيراد"},{status:500})}}let g=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/incomes/[id]/route",pathname:"/api/incomes/[id]",filename:"route",bundlePath:"app/api/incomes/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\incomes\\[id]\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:E,workUnitAsyncStorage:w,serverHooks:x}=g;function N(){return(0,o.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:w})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85463:(e,r,t)=>{"use strict";t.d(r,{J8:()=>i,QZ:()=>n});var s=t(45697);s.z.object({name:s.z.string().min(2,"الاسم يجب أن يكون على الأقل حرفين"),email:s.z.string().email("البريد الإلكتروني غير صحيح"),password:s.z.string().min(6,"كلمة المرور يجب أن تكون على الأقل 6 أحرف"),role:s.z.enum(["ADMIN","DATA_ENTRY","VIEWER","MEMBER_VIEWER","GALLERY_VIEWER","MEMBER"]).default("VIEWER")});let n=s.z.object({name:s.z.string().min(2,"الاسم يجب أن يكون على الأقل حرفين"),phone:s.z.string().optional().or(s.z.literal("")).or(s.z.literal(null)),email:s.z.union([s.z.string().email("البريد الإلكتروني غير صحيح"),s.z.literal(""),s.z.literal(null),s.z.undefined()]).optional(),address:s.z.string().optional().or(s.z.literal("")).or(s.z.literal(null)),notes:s.z.string().optional().or(s.z.literal("")).or(s.z.literal(null)),photo:s.z.string().optional().or(s.z.literal("")).or(s.z.literal(null)),status:s.z.enum(["ACTIVE","LATE","INACTIVE","SUSPENDED","ARCHIVED"]).default("ACTIVE")}),i=s.z.object({amount:s.z.number().positive("المبلغ يجب أن يكون أكبر من صفر"),date:s.z.date(),source:s.z.string().min(1,"مصدر الإيراد مطلوب"),type:s.z.enum(["SUBSCRIPTION","DONATION","EVENT","OTHER"]).default("SUBSCRIPTION"),description:s.z.string().optional().nullable(),notes:s.z.string().optional().nullable(),memberId:s.z.string().optional().nullable()});s.z.object({amount:s.z.number().positive("المبلغ يجب أن يكون أكبر من صفر"),date:s.z.date(),description:s.z.string().min(1,"وصف المصروف مطلوب"),category:s.z.enum(["MEETINGS","EVENTS","MAINTENANCE","SOCIAL","GENERAL"]).default("GENERAL"),recipient:s.z.string().optional().nullable(),notes:s.z.string().optional().nullable()}),s.z.object({title:s.z.string().min(1,"عنوان النشاط مطلوب"),description:s.z.string().optional(),startDate:s.z.date(),endDate:s.z.date().optional(),location:s.z.string().optional(),organizers:s.z.string().optional(),participantIds:s.z.array(s.z.string()).optional()}),s.z.object({email:s.z.string().email("البريد الإلكتروني غير صحيح"),password:s.z.string().min(1,"كلمة المرور مطلوبة")})},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,5663,4999,3412,580,5697],()=>t(61886));module.exports=s})();