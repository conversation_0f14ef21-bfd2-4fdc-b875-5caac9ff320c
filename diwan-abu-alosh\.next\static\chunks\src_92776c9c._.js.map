{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  // This interface extends the base input props\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-11 w-full rounded-xl border-2 border-secondary-200 bg-white px-4 py-3 text-sm font-medium text-secondary-700 transition-all duration-200 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-secondary-400 focus-visible:outline-none focus-visible:border-primary-500 focus-visible:ring-4 focus-visible:ring-primary-500/20 disabled:cursor-not-allowed disabled:opacity-50 hover:border-secondary-300\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gbACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/table.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Table = React.forwardRef<\n  HTMLTableElement,\n  React.HTMLAttributes<HTMLTableElement>\n>(({ className, ...props }, ref) => (\n  <div className=\"relative w-full overflow-auto\">\n    <table\n      ref={ref}\n      className={cn(\"w-full caption-bottom text-sm\", className)}\n      {...props}\n    />\n  </div>\n))\nTable.displayName = \"Table\"\n\nconst TableHeader = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\n))\nTableHeader.displayName = \"TableHeader\"\n\nconst TableBody = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tbody\n    ref={ref}\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\n    {...props}\n  />\n))\nTableBody.displayName = \"TableBody\"\n\nconst TableFooter = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tfoot\n    ref={ref}\n    className={cn(\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableFooter.displayName = \"TableFooter\"\n\nconst TableRow = React.forwardRef<\n  HTMLTableRowElement,\n  React.HTMLAttributes<HTMLTableRowElement>\n>(({ className, ...props }, ref) => (\n  <tr\n    ref={ref}\n    className={cn(\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nTableRow.displayName = \"TableRow\"\n\nconst TableHead = React.forwardRef<\n  HTMLTableCellElement,\n  React.ThHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <th\n    ref={ref}\n    className={cn(\n      \"h-12 px-4 text-right align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableHead.displayName = \"TableHead\"\n\nconst TableCell = React.forwardRef<\n  HTMLTableCellElement,\n  React.TdHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <td\n    ref={ref}\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\n    {...props}\n  />\n))\nTableCell.displayName = \"TableCell\"\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAM,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qGACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;QAC/D,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/lib/validations.ts"], "sourcesContent": ["import { z } from 'zod'\n\n// تحقق من صحة بيانات المستخدم\nexport const userSchema = z.object({\n  name: z.string().min(2, 'الاسم يجب أن يكون على الأقل حرفين'),\n  email: z.string().email('البريد الإلكتروني غير صحيح'),\n  password: z.string().min(6, 'كلمة المرور يجب أن تكون على الأقل 6 أحرف'),\n  role: z.enum(['ADMIN', 'DATA_ENTRY', 'VIEWER', 'MEMBER_VIEWER', 'GALLERY_VIEWER', 'MEMBER']).default('VIEWER'),\n})\n\n// تحقق من صحة بيانات العضو\nexport const memberSchema = z.object({\n  name: z.string().min(2, 'الاسم يجب أن يكون على الأقل حرفين'),\n  phone: z.string().optional().or(z.literal('')).or(z.literal(null)),\n  email: z.union([\n    z.string().email('البريد الإلكتروني غير صحيح'),\n    z.literal(''),\n    z.literal(null),\n    z.undefined()\n  ]).optional(),\n  address: z.string().optional().or(z.literal('')).or(z.literal(null)),\n  notes: z.string().optional().or(z.literal('')).or(z.literal(null)),\n  photo: z.string().optional().or(z.literal('')).or(z.literal(null)),\n  status: z.enum(['ACTIVE', 'LATE', 'INACTIVE', 'SUSPENDED', 'ARCHIVED']).default('ACTIVE'),\n})\n\n// تحقق من صحة بيانات الإيراد\nexport const incomeSchema = z.object({\n  amount: z.number().positive('المبلغ يجب أن يكون أكبر من صفر'),\n  date: z.date(),\n  source: z.string().min(1, 'مصدر الإيراد مطلوب'),\n  type: z.enum(['SUBSCRIPTION', 'DONATION', 'EVENT', 'OTHER']).default('SUBSCRIPTION'),\n  description: z.string().optional().nullable(),\n  notes: z.string().optional().nullable(),\n  memberId: z.string().optional().nullable(),\n})\n\n// تحقق من صحة بيانات المصروف\nexport const expenseSchema = z.object({\n  amount: z.number().positive('المبلغ يجب أن يكون أكبر من صفر'),\n  date: z.date(),\n  description: z.string().min(1, 'وصف المصروف مطلوب'),\n  category: z.enum(['MEETINGS', 'EVENTS', 'MAINTENANCE', 'SOCIAL', 'GENERAL']).default('GENERAL'),\n  recipient: z.string().optional().nullable(),\n  notes: z.string().optional().nullable(),\n})\n\n// تحقق من صحة بيانات النشاط\nexport const activitySchema = z.object({\n  title: z.string().min(1, 'عنوان النشاط مطلوب'),\n  description: z.string().optional(),\n  startDate: z.date(),\n  endDate: z.date().optional(),\n  location: z.string().optional(),\n  organizers: z.string().optional(),\n  participantIds: z.array(z.string()).optional(),\n})\n\n// تحقق من صحة بيانات تسجيل الدخول\nexport const loginSchema = z.object({\n  email: z.string().email('البريد الإلكتروني غير صحيح'),\n  password: z.string().min(1, 'كلمة المرور مطلوبة'),\n})\n\n// أنواع TypeScript المستخرجة من المخططات\nexport type UserInput = z.infer<typeof userSchema>\nexport type MemberInput = z.infer<typeof memberSchema>\nexport type IncomeInput = z.infer<typeof incomeSchema>\nexport type ExpenseInput = z.infer<typeof expenseSchema>\nexport type ActivityInput = z.infer<typeof activitySchema>\nexport type LoginInput = z.infer<typeof loginSchema>\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;;AAGO,MAAM,aAAa,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,MAAM,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,MAAM,oLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAS;QAAc;QAAU;QAAiB;QAAkB;KAAS,EAAE,OAAO,CAAC;AACvG;AAGO,MAAM,eAAe,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,MAAM,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,EAAE,CAAC,oLAAA,CAAA,IAAC,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,oLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAC5D,OAAO,oLAAA,CAAA,IAAC,CAAC,KAAK,CAAC;QACb,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;QACjB,oLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;QACV,oLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;QACV,oLAAA,CAAA,IAAC,CAAC,SAAS;KACZ,EAAE,QAAQ;IACX,SAAS,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,EAAE,CAAC,oLAAA,CAAA,IAAC,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,oLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAC9D,OAAO,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,EAAE,CAAC,oLAAA,CAAA,IAAC,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,oLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAC5D,OAAO,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,EAAE,CAAC,oLAAA,CAAA,IAAC,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,oLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAC5D,QAAQ,oLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAU;QAAQ;QAAY;QAAa;KAAW,EAAE,OAAO,CAAC;AAClF;AAGO,MAAM,eAAe,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,QAAQ,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC5B,MAAM,oLAAA,CAAA,IAAC,CAAC,IAAI;IACZ,QAAQ,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,MAAM,oLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAgB;QAAY;QAAS;KAAQ,EAAE,OAAO,CAAC;IACrE,aAAa,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC3C,OAAO,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACrC,UAAU,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;AAC1C;AAGO,MAAM,gBAAgB,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,QAAQ,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC5B,MAAM,oLAAA,CAAA,IAAC,CAAC,IAAI;IACZ,aAAa,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,UAAU,oLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAY;QAAU;QAAe;QAAU;KAAU,EAAE,OAAO,CAAC;IACrF,WAAW,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACzC,OAAO,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;AACvC;AAGO,MAAM,iBAAiB,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,OAAO,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,aAAa,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,WAAW,oLAAA,CAAA,IAAC,CAAC,IAAI;IACjB,SAAS,oLAAA,CAAA,IAAC,CAAC,IAAI,GAAG,QAAQ;IAC1B,UAAU,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,YAAY,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,gBAAgB,oLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;AAC9C;AAGO,MAAM,cAAc,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,OAAO,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B", "debugId": null}}, {"offset": {"line": 261, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\nimport { X } from \"lucide-react\"\n\ninterface DialogProps {\n  open?: boolean\n  onOpenChange?: (open: boolean) => void\n  children: React.ReactNode\n}\n\ninterface DialogContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\nconst Dialog = ({ open, onOpenChange, children }: DialogProps) => {\n  React.useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape') {\n        onOpenChange?.(false)\n      }\n    }\n\n    if (open) {\n      document.addEventListener('keydown', handleEscape)\n      document.body.style.overflow = 'hidden'\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape)\n      document.body.style.overflow = 'unset'\n    }\n  }, [open, onOpenChange])\n\n  if (!open) return null\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4\">\n      <div\n        className=\"fixed inset-0 bg-black/60 backdrop-blur-md transition-opacity duration-300\"\n        onClick={() => onOpenChange?.(false)}\n      />\n      <div className=\"relative z-50 max-h-[90vh] overflow-y-auto animate-in fade-in-0 zoom-in-95 duration-300\">\n        {children}\n      </div>\n    </div>\n  )\n}\n\nconst DialogContent = React.forwardRef<HTMLDivElement, DialogContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        \"relative w-full max-w-[50vw] mx-4 bg-white rounded-2xl shadow-2xl border border-slate-200 p-0 overflow-hidden\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n)\nDialogContent.displayName = \"DialogContent\"\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-right p-6 bg-gradient-to-r from-slate-50 to-slate-100 border-b border-slate-200\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 sm:space-x-reverse p-6 bg-gradient-to-r from-slate-50 to-slate-100 border-t border-slate-200\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-xl font-bold leading-none tracking-tight text-slate-800\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = \"DialogTitle\"\n\nconst DialogDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-slate-600 font-medium\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = \"DialogDescription\"\n\nconst DialogTrigger = React.forwardRef<\n  HTMLButtonElement,\n  React.ButtonHTMLAttributes<HTMLButtonElement>\n>(({ className, ...props }, ref) => (\n  <button\n    ref={ref}\n    className={className}\n    {...props}\n  />\n))\nDialogTrigger.displayName = \"DialogTrigger\"\n\nconst DialogClose = React.forwardRef<\n  HTMLButtonElement,\n  React.ButtonHTMLAttributes<HTMLButtonElement> & { onOpenChange?: (open: boolean) => void }\n>(({ className, onOpenChange, ...props }, ref) => (\n  <button\n    ref={ref}\n    type=\"button\"\n    onClick={() => onOpenChange?.(false)}\n    className={cn(\n      \"absolute left-4 top-4 rounded-full p-2 bg-white shadow-lg opacity-80 ring-offset-background transition-all duration-200 hover:opacity-100 hover:shadow-xl hover:scale-110 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 disabled:pointer-events-none z-10\",\n      className\n    )}\n    {...props}\n  >\n    <X className=\"h-4 w-4 text-slate-600\" />\n    <span className=\"sr-only\">إغلاق</span>\n  </button>\n))\nDialogClose.displayName = \"DialogClose\"\n\nexport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n  DialogTrigger,\n  DialogClose,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AACA;AACA;;;AAJA;;;;AAgBA,MAAM,SAAS,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAe;;IAC3D,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;4BAAE;YACd,MAAM;iDAAe,CAAC;oBACpB,IAAI,EAAE,GAAG,KAAK,UAAU;wBACtB,eAAe;oBACjB;gBACF;;YAEA,IAAI,MAAM;gBACR,SAAS,gBAAgB,CAAC,WAAW;gBACrC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;oCAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;oBACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;2BAAG;QAAC;QAAM;KAAa;IAEvB,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAEhC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT;GAhCM;KAAA;AAkCN,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QACnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iHACA;QAED,GAAG,KAAK;kBAER;;;;;;;AAIP,cAAc,WAAW,GAAG;AAE5B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+HACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gEACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW;QACV,GAAG,KAAK;;;;;;;AAGb,cAAc,WAAW,GAAG;AAE5B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGjC,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,OAAO,EAAE,oBACxC,6LAAC;QACC,KAAK;QACL,MAAK;QACL,SAAS,IAAM,eAAe;QAC9B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wRACA;QAED,GAAG,KAAK;;0BAET,6LAAC,+LAAA,CAAA,IAAC;gBAAC,WAAU;;;;;;0BACb,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;;AAG9B,YAAY,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 455, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface LabelProps\n  extends React.LabelHTMLAttributes<HTMLLabelElement> {\n  // This interface extends the base label props\n}\n\nconst Label = React.forwardRef<HTMLLabelElement, LabelProps>(\n  ({ className, ...props }, ref) => (\n    <label\n      ref={ref}\n      className={cn(\n        \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nLabel.displayName = \"Label\"\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAIf,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 488, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {\n  // This interface extends the base textarea props\n}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n        success:\n          \"border-transparent bg-green-100 text-green-800 hover:bg-green-200\",\n        warning:\n          \"border-transparent bg-slate-100 text-slate-800 hover:bg-slate-200\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 573, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/members/member-dialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { memberSchema, type MemberInput } from '@/lib/validations'\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n  DialogClose,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Switch } from '@/components/ui/switch'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  User,\n  Phone,\n  Mail,\n  MapPin,\n  FileText,\n  UserCheck,\n  Calendar,\n  AlertCircle,\n  CheckCircle,\n  Clock,\n  XCircle,\n  Archive,\n  Save,\n  X,\n  Camera,\n  Image as ImageIcon,\n  Upload,\n  Loader2\n} from 'lucide-react'\n\ninterface Member {\n  id: string\n  name: string\n  phone?: string\n  email?: string\n  address?: string\n  photo?: string\n  notes?: string\n  status: string\n}\n\ninterface MemberDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  member?: Member | null\n  onSuccess: () => void\n}\n\nexport default function MemberDialog({\n  open,\n  onOpenChange,\n  member,\n  onSuccess,\n}: MemberDialogProps) {\n  const [loading, setLoading] = useState(false)\n  const [uploading, setUploading] = useState(false)\n  const [uploadError, setUploadError] = useState<string | null>(null)\n  const isEditing = !!member\n\n  const {\n    register,\n    handleSubmit,\n    reset,\n    setValue,\n    watch,\n    formState: { errors },\n  } = useForm<MemberInput>({\n    resolver: zodResolver(memberSchema),\n    defaultValues: {\n      name: '',\n      phone: '',\n      email: '',\n      address: '',\n      photo: '',\n      notes: '',\n      status: 'ACTIVE',\n    },\n  })\n\n  const status = watch('status')\n\n  // دالة للحصول على أيقونة الحالة\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'ACTIVE':\n        return <CheckCircle className=\"w-4 h-4 text-green-500\" />\n      case 'LATE':\n        return <Clock className=\"w-4 h-4 text-yellow-500\" />\n      case 'INACTIVE':\n        return <AlertCircle className=\"w-4 h-4 text-orange-500\" />\n      case 'SUSPENDED':\n        return <XCircle className=\"w-4 h-4 text-red-500\" />\n      case 'ARCHIVED':\n        return <Archive className=\"w-4 h-4 text-gray-500\" />\n      default:\n        return <UserCheck className=\"w-4 h-4 text-blue-500\" />\n    }\n  }\n\n  // دالة للحصول على لون الحالة\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'ACTIVE':\n        return 'bg-green-50 text-green-700 border-green-200'\n      case 'LATE':\n        return 'bg-yellow-50 text-yellow-700 border-yellow-200'\n      case 'INACTIVE':\n        return 'bg-orange-50 text-orange-700 border-orange-200'\n      case 'SUSPENDED':\n        return 'bg-red-50 text-red-700 border-red-200'\n      case 'ARCHIVED':\n        return 'bg-gray-50 text-gray-700 border-gray-200'\n      default:\n        return 'bg-blue-50 text-blue-700 border-blue-200'\n    }\n  }\n\n  // تحديث النموذج عند تغيير العضو\n  useEffect(() => {\n    if (member) {\n      setValue('name', member.name)\n      setValue('phone', member.phone || '')\n      setValue('email', member.email || '')\n      setValue('address', member.address || '')\n      setValue('photo', member.photo || '')\n      setValue('notes', member.notes || '')\n      setValue('status', member.status as any)\n    } else {\n      reset({\n        name: '',\n        phone: '',\n        email: '',\n        address: '',\n        photo: '',\n        notes: '',\n        status: 'ACTIVE',\n      })\n    }\n  }, [member, setValue, reset])\n\n  // دالة رفع الصورة\n  const handleImageUpload = async (file: File) => {\n    if (!file) return null\n\n    // التحقق من نوع الملف\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']\n    if (!allowedTypes.includes(file.type)) {\n      setUploadError('نوع الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG, WebP)')\n      return null\n    }\n\n    // التحقق من حجم الملف (5MB)\n    const maxSize = 5 * 1024 * 1024\n    if (file.size > maxSize) {\n      setUploadError('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت')\n      return null\n    }\n\n    setUploadError(null)\n    setUploading(true)\n\n    try {\n      const formData = new FormData()\n      formData.append('file', file)\n\n      const response = await fetch('/api/upload', {\n        method: 'POST',\n        body: formData,\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json()\n        throw new Error(errorData.error || 'فشل في رفع الصورة')\n      }\n\n      const data = await response.json()\n      setValue('photo', data.filePath)\n      return data.filePath\n    } catch (error: any) {\n      console.error('خطأ في رفع الصورة:', error)\n      setUploadError(error.message || 'حدث خطأ في رفع الصورة')\n      return null\n    } finally {\n      setUploading(false)\n    }\n  }\n\n  // دالة حذف الصورة\n  const handleImageRemove = async () => {\n    const currentPhoto = watch('photo')\n    if (currentPhoto) {\n      try {\n        await fetch(`/api/upload?path=${encodeURIComponent(currentPhoto)}`, {\n          method: 'DELETE',\n        })\n      } catch (error) {\n        console.error('خطأ في حذف الصورة:', error)\n      }\n    }\n    setValue('photo', '')\n    setUploadError(null)\n  }\n\n  const onSubmit = async (data: MemberInput) => {\n    try {\n      setLoading(true)\n\n      // تنظيف البيانات قبل الإرسال\n      const cleanData = {\n        ...data,\n        phone: data.phone?.trim() || null,\n        email: data.email?.trim() || null,\n        address: data.address?.trim() || null,\n        notes: data.notes?.trim() || null,\n      }\n\n      const url = isEditing ? `/api/members/${member.id}` : '/api/members'\n      const method = isEditing ? 'PUT' : 'POST'\n\n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(cleanData),\n      })\n\n      if (!response.ok) {\n        const error = await response.json()\n        throw new Error(error.error || 'حدث خطأ')\n      }\n\n      // إظهار رسالة نجاح\n      const successMessage = isEditing\n        ? 'تم تحديث بيانات العضو بنجاح'\n        : 'تم إضافة العضو الجديد بنجاح'\n\n      // يمكن استبدال alert برسالة toast أكثر جمالاً\n      alert(successMessage)\n\n      onSuccess()\n      onOpenChange(false)\n      reset()\n    } catch (error: any) {\n      console.error('خطأ في حفظ العضو:', error)\n      alert(error.message || 'حدث خطأ في حفظ العضو')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"w-[50vw] h-[85vh] max-w-none max-h-none overflow-y-auto fixed top-[1vh] left-1/2 -translate-x-1/2 translate-y-0\">\n\n        <DialogHeader className=\"p-6 pb-4 border-b border-gray-100 bg-gradient-to-r from-diwan-50 to-blue-50\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"p-3 bg-white rounded-xl shadow-sm border border-diwan-200\">\n              <User className=\"w-6 h-6 text-diwan-600\" />\n            </div>\n            <div className=\"flex-1\">\n              <DialogTitle className=\"text-xl font-bold text-gray-900 mb-1\">\n                {isEditing ? 'تعديل بيانات العضو' : 'إضافة عضو جديد'}\n              </DialogTitle>\n              <DialogDescription className=\"text-gray-600\">\n                {isEditing\n                  ? 'قم بتعديل بيانات العضو في النموذج أدناه'\n                  : 'أدخل بيانات العضو الجديد في النموذج أدناه'}\n              </DialogDescription>\n              {!isEditing && (\n                <div className=\"mt-2 text-xs text-diwan-600 bg-diwan-50 px-2 py-1 rounded-md inline-block\">\n                  💡 الحقول المطلوبة مميزة بعلامة *\n                </div>\n              )}\n            </div>\n          </div>\n        </DialogHeader>\n\n        <form onSubmit={handleSubmit(onSubmit)} className=\"p-8 pt-0 space-y-8\">\n          {/* صورة العضو والمعلومات الأساسية */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            {/* صورة العضو */}\n            <Card className=\"border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200\">\n              <CardContent className=\"p-5\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2 pb-2 border-b border-gray-100\">\n                  <div className=\"p-1 bg-indigo-100 rounded-lg\">\n                    <Camera className=\"w-4 h-4 text-indigo-600\" />\n                  </div>\n                  صورة العضو\n                  <span className=\"text-xs text-gray-500 font-normal mr-auto\">اختياري</span>\n                </h3>\n\n                <div className=\"flex flex-col items-center\">\n                  {/* مكون رفع الصورة */}\n                  <div className=\"space-y-3 w-full\">\n                    <input\n                      type=\"file\"\n                      accept=\"image/*\"\n                      onChange={(e) => {\n                        const file = e.target.files?.[0]\n                        if (file) {\n                          handleImageUpload(file)\n                        }\n                      }}\n                      className=\"hidden\"\n                      id=\"photo-upload\"\n                      disabled={loading || uploading}\n                    />\n\n                    {watch('photo') ? (\n                      // عرض الصورة المرفوعة\n                      <div className=\"relative group\">\n                        <div className=\"relative w-32 h-32 mx-auto rounded-xl overflow-hidden border-2 border-gray-200 shadow-lg\">\n                          <img\n                            src={watch('photo') || undefined}\n                            alt=\"صورة العضو\"\n                            className=\"w-full h-full object-cover\"\n                          />\n                          <div className=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center\">\n                            <Button\n                              type=\"button\"\n                              variant=\"destructive\"\n                              size=\"sm\"\n                              onClick={handleImageRemove}\n                              disabled={loading || uploading}\n                              className=\"opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\n                            >\n                              <X className=\"w-4 h-4\" />\n                            </Button>\n                          </div>\n                        </div>\n                        <div className=\"text-center mt-2\">\n                          <Button\n                            type=\"button\"\n                            variant=\"outline\"\n                            size=\"sm\"\n                            onClick={() => document.getElementById('photo-upload')?.click()}\n                            disabled={loading || uploading}\n                            className=\"text-xs\"\n                          >\n                            <Camera className=\"w-3 h-3 ml-1\" />\n                            تغيير الصورة\n                          </Button>\n                        </div>\n                      </div>\n                    ) : (\n                      // منطقة رفع الصورة\n                      <div\n                        onClick={() => document.getElementById('photo-upload')?.click()}\n                        className={`\n                          relative w-full h-32 border-2 border-dashed rounded-xl cursor-pointer\n                          transition-all duration-200 flex flex-col items-center justify-center\n                          border-gray-300 hover:border-diwan-400 hover:bg-gray-50\n                          ${(loading || uploading) ? 'opacity-50 cursor-not-allowed' : ''}\n                        `}\n                      >\n                        {uploading ? (\n                          <div className=\"flex flex-col items-center gap-2 text-diwan-600\">\n                            <Loader2 className=\"w-6 h-6 animate-spin\" />\n                            <span className=\"text-xs font-medium\">جاري الرفع...</span>\n                          </div>\n                        ) : (\n                          <div className=\"flex flex-col items-center gap-2 text-gray-500\">\n                            <div className=\"p-2 bg-gray-100 rounded-full\">\n                              <ImageIcon className=\"w-6 h-6\" />\n                            </div>\n                            <div className=\"text-center\">\n                              <p className=\"text-xs font-medium text-gray-700\">\n                                اضغط لاختيار صورة\n                              </p>\n                              <p className=\"text-xs text-gray-500\">\n                                JPG, PNG, WebP\n                              </p>\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                    )}\n\n                    {uploadError && (\n                      <div className=\"bg-red-50 border border-red-200 rounded-lg p-2\">\n                        <div className=\"flex items-center gap-2 text-red-600\">\n                          <AlertCircle className=\"w-3 h-3 flex-shrink-0\" />\n                          <span className=\"text-xs\">{uploadError}</span>\n                        </div>\n                      </div>\n                    )}\n\n                    {watch('photo') && !uploadError && (\n                      <div className=\"bg-green-50 border border-green-200 rounded-lg p-2\">\n                        <div className=\"flex items-center gap-2 text-green-600\">\n                          <CheckCircle className=\"w-3 h-3 flex-shrink-0\" />\n                          <span className=\"text-xs\">تم رفع الصورة بنجاح</span>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n\n                  <p className=\"text-xs text-gray-500 mt-3 text-center\">\n                    صورة شخصية للعضو\n                  </p>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* معلومات أساسية */}\n            <Card className=\"lg:col-span-2 border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200\">\n              <CardContent className=\"p-5\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2 pb-2 border-b border-gray-100\">\n                  <div className=\"p-1 bg-diwan-100 rounded-lg\">\n                    <User className=\"w-4 h-4 text-diwan-600\" />\n                  </div>\n                  المعلومات الأساسية\n                  <span className=\"text-xs text-gray-500 font-normal mr-auto\">مطلوب</span>\n                </h3>\n\n                <div className=\"space-y-4\">\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"name\" className=\"text-sm font-medium text-gray-700 flex items-center gap-2\">\n                      <User className=\"w-4 h-4\" />\n                      الاسم الكامل *\n                    </Label>\n                    <Input\n                      id=\"name\"\n                      {...register('name')}\n                      placeholder=\"أدخل الاسم الكامل للعضو\"\n                      className={`h-12 text-base transition-all duration-200 ${\n                        errors.name\n                          ? 'border-red-300 focus:border-red-500 focus:ring-red-500 bg-red-50'\n                          : 'border-gray-300 focus:border-diwan-500 focus:ring-diwan-500'\n                      }`}\n                    />\n                    {errors.name && (\n                      <div className=\"bg-red-50 border border-red-200 rounded-lg p-3 mt-1\">\n                        <p className=\"text-sm text-red-600 flex items-center gap-2\">\n                          <AlertCircle className=\"w-4 h-4 flex-shrink-0\" />\n                          {errors.name.message}\n                        </p>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* معلومات الاتصال */}\n          <Card className=\"border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200\">\n            <CardContent className=\"p-5\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2 pb-2 border-b border-gray-100\">\n                <div className=\"p-1 bg-blue-100 rounded-lg\">\n                  <Phone className=\"w-4 h-4 text-blue-600\" />\n                </div>\n                معلومات الاتصال\n                <span className=\"text-xs text-gray-500 font-normal mr-auto\">اختياري</span>\n              </h3>\n\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"phone\" className=\"text-sm font-medium text-gray-700 flex items-center gap-2\">\n                    <Phone className=\"w-4 h-4\" />\n                    رقم الهاتف\n                  </Label>\n                  <Input\n                    id=\"phone\"\n                    {...register('phone')}\n                    placeholder=\"07xxxxxxxx\"\n                    dir=\"ltr\"\n                    className=\"h-12 text-base border-gray-300 focus:border-diwan-500 focus:ring-diwan-500\"\n                  />\n                  {errors.phone && (\n                    <p className=\"text-sm text-red-600 flex items-center gap-1\">\n                      <AlertCircle className=\"w-4 h-4\" />\n                      {errors.phone.message}\n                    </p>\n                  )}\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"email\" className=\"text-sm font-medium text-gray-700 flex items-center gap-2\">\n                    <Mail className=\"w-4 h-4\" />\n                    البريد الإلكتروني\n                  </Label>\n                  <Input\n                    id=\"email\"\n                    type=\"email\"\n                    {...register('email')}\n                    placeholder=\"<EMAIL>\"\n                    dir=\"ltr\"\n                    className=\"h-12 text-base border-gray-300 focus:border-diwan-500 focus:ring-diwan-500\"\n                  />\n                  {errors.email && (\n                    <p className=\"text-sm text-red-600 flex items-center gap-1\">\n                      <AlertCircle className=\"w-4 h-4\" />\n                      {errors.email.message}\n                    </p>\n                  )}\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* العنوان والملاحظات */}\n          <Card className=\"border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200\">\n            <CardContent className=\"p-5\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2 pb-2 border-b border-gray-100\">\n                <div className=\"p-1 bg-green-100 rounded-lg\">\n                  <MapPin className=\"w-4 h-4 text-green-600\" />\n                </div>\n                العنوان والملاحظات\n                <span className=\"text-xs text-gray-500 font-normal mr-auto\">اختياري</span>\n              </h3>\n\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"address\" className=\"text-sm font-medium text-gray-700 flex items-center gap-2\">\n                    <MapPin className=\"w-4 h-4\" />\n                    العنوان\n                  </Label>\n                  <Input\n                    id=\"address\"\n                    {...register('address')}\n                    placeholder=\"أدخل عنوان السكن\"\n                    className=\"h-12 text-base border-gray-300 focus:border-diwan-500 focus:ring-diwan-500\"\n                  />\n                  {errors.address && (\n                    <p className=\"text-sm text-red-600 flex items-center gap-1\">\n                      <AlertCircle className=\"w-4 h-4\" />\n                      {errors.address.message}\n                    </p>\n                  )}\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"notes\" className=\"text-sm font-medium text-gray-700 flex items-center gap-2\">\n                    <FileText className=\"w-4 h-4\" />\n                    ملاحظات إضافية\n                  </Label>\n                  <Textarea\n                    id=\"notes\"\n                    {...register('notes')}\n                    placeholder=\"أدخل أي ملاحظات أو معلومات إضافية عن العضو\"\n                    rows={4}\n                    className=\"text-base border-gray-300 focus:border-diwan-500 focus:ring-diwan-500 resize-none\"\n                  />\n                  {errors.notes && (\n                    <p className=\"text-sm text-red-600 flex items-center gap-1\">\n                      <AlertCircle className=\"w-4 h-4\" />\n                      {errors.notes.message}\n                    </p>\n                  )}\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* حالة العضو */}\n          <Card className=\"border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200\">\n            <CardContent className=\"p-5\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2 pb-2 border-b border-gray-100\">\n                <div className=\"p-1 bg-purple-100 rounded-lg\">\n                  <UserCheck className=\"w-4 h-4 text-purple-600\" />\n                </div>\n                حالة العضو\n                <span className=\"text-xs text-gray-500 font-normal mr-auto\">مطلوب</span>\n              </h3>\n\n              <div className=\"space-y-4\">\n                <div className=\"space-y-3\">\n                  <Label htmlFor=\"status\" className=\"text-sm font-medium text-gray-700\">\n                    اختر حالة العضو\n                  </Label>\n\n                  {/* عرض الحالة الحالية */}\n                  <div className=\"flex items-center gap-2 mb-3\">\n                    <span className=\"text-sm text-gray-600\">الحالة الحالية:</span>\n                    <Badge className={`${getStatusColor(status)} border`}>\n                      <div className=\"flex items-center gap-1\">\n                        {getStatusIcon(status)}\n                        <span>\n                          {status === 'ACTIVE' && 'نشط'}\n                          {status === 'LATE' && 'متأخر'}\n                          {status === 'INACTIVE' && 'غير ملتزم'}\n                          {status === 'SUSPENDED' && 'موقوف مؤقتاً'}\n                          {status === 'ARCHIVED' && 'مؤرشف'}\n                        </span>\n                      </div>\n                    </Badge>\n                  </div>\n\n                  <select\n                    id=\"status\"\n                    {...register('status')}\n                    className=\"w-full h-12 px-4 py-3 text-base border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-diwan-500 focus:border-diwan-500 bg-white\"\n                  >\n                    <option value=\"ACTIVE\">✅ نشط - عضو فعال ومنتظم</option>\n                    <option value=\"LATE\">⏰ متأخر - متأخر في الدفع</option>\n                    <option value=\"INACTIVE\">⚠️ غير ملتزم - غير منتظم في الحضور</option>\n                    <option value=\"SUSPENDED\">❌ موقوف مؤقتاً - موقوف لفترة محددة</option>\n                    <option value=\"ARCHIVED\">📁 مؤرشف - عضو سابق</option>\n                  </select>\n                  {errors.status && (\n                    <p className=\"text-sm text-red-600 flex items-center gap-1\">\n                      <AlertCircle className=\"w-4 h-4\" />\n                      {errors.status.message}\n                    </p>\n                  )}\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* أزرار الحفظ والإلغاء */}\n          <div className=\"flex justify-between items-center gap-4 pt-8 border-t-2 border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 -mx-6 px-8 py-6 rounded-b-xl\">\n            <div className=\"text-sm text-gray-600 font-medium\">\n              {isEditing ? (\n                <div className=\"flex items-center gap-2\">\n                  <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                  تعديل بيانات العضو\n                </div>\n              ) : (\n                <div className=\"flex items-center gap-2\">\n                  <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                  إضافة عضو جديد\n                </div>\n              )}\n            </div>\n            <div className=\"flex gap-4\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => onOpenChange(false)}\n                disabled={loading}\n                className=\"px-8 py-3 h-12 border-2 border-gray-300 text-gray-700 hover:bg-white hover:border-gray-400 hover:shadow-md transition-all duration-200 flex items-center gap-2 font-medium\"\n              >\n                <X className=\"w-4 h-4\" />\n                إلغاء\n              </Button>\n              <Button\n                type=\"submit\"\n                disabled={loading}\n                className={`px-8 py-3 h-12 font-bold text-white shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2 ${\n                  isEditing\n                    ? 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 border-2 border-blue-500'\n                    : 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 border-2 border-green-500'\n                }`}\n              >\n                {loading ? (\n                  <div className=\"flex items-center gap-2\">\n                    <div className=\"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                    <span className=\"text-base\">جاري الحفظ...</span>\n                  </div>\n                ) : (\n                  <div className=\"flex items-center gap-2\">\n                    {isEditing ? (\n                      <>\n                        <CheckCircle className=\"w-5 h-5\" />\n                        <span className=\"text-base\">تحديث البيانات</span>\n                      </>\n                    ) : (\n                      <>\n                        <Save className=\"w-5 h-5\" />\n                        <span className=\"text-base\">حفظ العضو الجديد</span>\n                      </>\n                    )}\n                  </div>\n                )}\n              </Button>\n            </div>\n          </div>\n        </form>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AASA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAtBA;;;;;;;;;;;;;AA6De,SAAS,aAAa,EACnC,IAAI,EACJ,YAAY,EACZ,MAAM,EACN,SAAS,EACS;;IAClB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,YAAY,CAAC,CAAC;IAEpB,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAe;QACvB,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE,4HAAA,CAAA,eAAY;QAClC,eAAe;YACb,MAAM;YACN,OAAO;YACP,OAAO;YACP,SAAS;YACT,OAAO;YACP,OAAO;YACP,QAAQ;QACV;IACF;IAEA,MAAM,SAAS,MAAM;IAErB,gCAAgC;IAChC,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6LAAC,2MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B;gBACE,qBAAO,6LAAC,mNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;QAChC;IACF;IAEA,6BAA6B;IAC7B,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,QAAQ;gBACV,SAAS,QAAQ,OAAO,IAAI;gBAC5B,SAAS,SAAS,OAAO,KAAK,IAAI;gBAClC,SAAS,SAAS,OAAO,KAAK,IAAI;gBAClC,SAAS,WAAW,OAAO,OAAO,IAAI;gBACtC,SAAS,SAAS,OAAO,KAAK,IAAI;gBAClC,SAAS,SAAS,OAAO,KAAK,IAAI;gBAClC,SAAS,UAAU,OAAO,MAAM;YAClC,OAAO;gBACL,MAAM;oBACJ,MAAM;oBACN,OAAO;oBACP,OAAO;oBACP,SAAS;oBACT,OAAO;oBACP,OAAO;oBACP,QAAQ;gBACV;YACF;QACF;iCAAG;QAAC;QAAQ;QAAU;KAAM;IAE5B,kBAAkB;IAClB,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,MAAM,OAAO;QAElB,sBAAsB;QACtB,MAAM,eAAe;YAAC;YAAc;YAAa;YAAa;SAAa;QAC3E,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;YACrC,eAAe;YACf,OAAO;QACT;QAEA,4BAA4B;QAC5B,MAAM,UAAU,IAAI,OAAO;QAC3B,IAAI,KAAK,IAAI,GAAG,SAAS;YACvB,eAAe;YACf,OAAO;QACT;QAEA,eAAe;QACf,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,SAAS,SAAS,KAAK,QAAQ;YAC/B,OAAO,KAAK,QAAQ;QACtB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,sBAAsB;YACpC,eAAe,MAAM,OAAO,IAAI;YAChC,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,kBAAkB;IAClB,MAAM,oBAAoB;QACxB,MAAM,eAAe,MAAM;QAC3B,IAAI,cAAc;YAChB,IAAI;gBACF,MAAM,MAAM,CAAC,iBAAiB,EAAE,mBAAmB,eAAe,EAAE;oBAClE,QAAQ;gBACV;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sBAAsB;YACtC;QACF;QACA,SAAS,SAAS;QAClB,eAAe;IACjB;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,WAAW;YAEX,6BAA6B;YAC7B,MAAM,YAAY;gBAChB,GAAG,IAAI;gBACP,OAAO,KAAK,KAAK,EAAE,UAAU;gBAC7B,OAAO,KAAK,KAAK,EAAE,UAAU;gBAC7B,SAAS,KAAK,OAAO,EAAE,UAAU;gBACjC,OAAO,KAAK,KAAK,EAAE,UAAU;YAC/B;YAEA,MAAM,MAAM,YAAY,CAAC,aAAa,EAAE,OAAO,EAAE,EAAE,GAAG;YACtD,MAAM,SAAS,YAAY,QAAQ;YAEnC,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI;YACjC;YAEA,mBAAmB;YACnB,MAAM,iBAAiB,YACnB,gCACA;YAEJ,8CAA8C;YAC9C,MAAM;YAEN;YACA,aAAa;YACb;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM,MAAM,OAAO,IAAI;QACzB,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BAEvB,6LAAC,qIAAA,CAAA,eAAY;oBAAC,WAAU;8BACtB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,cAAW;wCAAC,WAAU;kDACpB,YAAY,uBAAuB;;;;;;kDAEtC,6LAAC,qIAAA,CAAA,oBAAiB;wCAAC,WAAU;kDAC1B,YACG,4CACA;;;;;;oCAEL,CAAC,2BACA,6LAAC;wCAAI,WAAU;kDAA4E;;;;;;;;;;;;;;;;;;;;;;;8BAQnG,6LAAC;oBAAK,UAAU,aAAa;oBAAW,WAAU;;sCAEhD,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;oDACd;kEAEN,6LAAC;wDAAK,WAAU;kEAA4C;;;;;;;;;;;;0DAG9D,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,MAAK;gEACL,QAAO;gEACP,UAAU,CAAC;oEACT,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;oEAChC,IAAI,MAAM;wEACR,kBAAkB;oEACpB;gEACF;gEACA,WAAU;gEACV,IAAG;gEACH,UAAU,WAAW;;;;;;4DAGtB,MAAM,WACL,sBAAsB;0EACtB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFACC,KAAK,MAAM,YAAY;gFACvB,KAAI;gFACJ,WAAU;;;;;;0FAEZ,6LAAC;gFAAI,WAAU;0FACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oFACL,MAAK;oFACL,SAAQ;oFACR,MAAK;oFACL,SAAS;oFACT,UAAU,WAAW;oFACrB,WAAU;8FAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kFAInB,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4EACL,MAAK;4EACL,SAAQ;4EACR,MAAK;4EACL,SAAS,IAAM,SAAS,cAAc,CAAC,iBAAiB;4EACxD,UAAU,WAAW;4EACrB,WAAU;;8FAEV,6LAAC,yMAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;;;;;;;;;;;uEAMzC,mBAAmB;0EACnB,6LAAC;gEACC,SAAS,IAAM,SAAS,cAAc,CAAC,iBAAiB;gEACxD,WAAW,CAAC;;;;0BAIV,EAAE,AAAC,WAAW,YAAa,kCAAkC,GAAG;wBAClE,CAAC;0EAEA,0BACC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,oNAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;sFACnB,6LAAC;4EAAK,WAAU;sFAAsB;;;;;;;;;;;yFAGxC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC,uMAAA,CAAA,QAAS;gFAAC,WAAU;;;;;;;;;;;sFAEvB,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAE,WAAU;8FAAoC;;;;;;8FAGjD,6LAAC;oFAAE,WAAU;8FAAwB;;;;;;;;;;;;;;;;;;;;;;;4DAS9C,6BACC,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,uNAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;sFACvB,6LAAC;4EAAK,WAAU;sFAAW;;;;;;;;;;;;;;;;;4DAKhC,MAAM,YAAY,CAAC,6BAClB,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,8NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;sFACvB,6LAAC;4EAAK,WAAU;sFAAU;;;;;;;;;;;;;;;;;;;;;;;kEAMlC,6LAAC;wDAAE,WAAU;kEAAyC;;;;;;;;;;;;;;;;;;;;;;;8CAQ5D,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;oDACZ;kEAEN,6LAAC;wDAAK,WAAU;kEAA4C;;;;;;;;;;;;0DAG9D,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAO,WAAU;;8EAC9B,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAY;;;;;;;sEAG9B,6LAAC,oIAAA,CAAA,QAAK;4DACJ,IAAG;4DACF,GAAG,SAAS,OAAO;4DACpB,aAAY;4DACZ,WAAW,CAAC,2CAA2C,EACrD,OAAO,IAAI,GACP,qEACA,+DACJ;;;;;;wDAEH,OAAO,IAAI,kBACV,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAU;;kFACX,6LAAC,uNAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;oEACtB,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAWpC,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;4CACb;0DAEN,6LAAC;gDAAK,WAAU;0DAA4C;;;;;;;;;;;;kDAG9D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAQ,WAAU;;0EAC/B,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAG/B,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACF,GAAG,SAAS,QAAQ;wDACrB,aAAY;wDACZ,KAAI;wDACJ,WAAU;;;;;;oDAEX,OAAO,KAAK,kBACX,6LAAC;wDAAE,WAAU;;0EACX,6LAAC,uNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DACtB,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;;0DAK3B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAQ,WAAU;;0EAC/B,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAG9B,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACJ,GAAG,SAAS,QAAQ;wDACrB,aAAY;wDACZ,KAAI;wDACJ,WAAU;;;;;;oDAEX,OAAO,KAAK,kBACX,6LAAC;wDAAE,WAAU;;0EACX,6LAAC,uNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DACtB,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASjC,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;4CACd;0DAEN,6LAAC;gDAAK,WAAU;0DAA4C;;;;;;;;;;;;kDAG9D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;;0EACjC,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAGhC,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACF,GAAG,SAAS,UAAU;wDACvB,aAAY;wDACZ,WAAU;;;;;;oDAEX,OAAO,OAAO,kBACb,6LAAC;wDAAE,WAAU;;0EACX,6LAAC,uNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DACtB,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;;0DAK7B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAQ,WAAU;;0EAC/B,6LAAC,iNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAGlC,6LAAC,uIAAA,CAAA,WAAQ;wDACP,IAAG;wDACF,GAAG,SAAS,QAAQ;wDACrB,aAAY;wDACZ,MAAM;wDACN,WAAU;;;;;;oDAEX,OAAO,KAAK,kBACX,6LAAC;wDAAE,WAAU;;0EACX,6LAAC,uNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DACtB,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASjC,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;4CACjB;0DAEN,6LAAC;gDAAK,WAAU;0DAA4C;;;;;;;;;;;;kDAG9D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAS,WAAU;8DAAoC;;;;;;8DAKtE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,WAAW,GAAG,eAAe,QAAQ,OAAO,CAAC;sEAClD,cAAA,6LAAC;gEAAI,WAAU;;oEACZ,cAAc;kFACf,6LAAC;;4EACE,WAAW,YAAY;4EACvB,WAAW,UAAU;4EACrB,WAAW,cAAc;4EACzB,WAAW,eAAe;4EAC1B,WAAW,cAAc;;;;;;;;;;;;;;;;;;;;;;;;8DAMlC,6LAAC;oDACC,IAAG;oDACF,GAAG,SAAS,SAAS;oDACtB,WAAU;;sEAEV,6LAAC;4DAAO,OAAM;sEAAS;;;;;;sEACvB,6LAAC;4DAAO,OAAM;sEAAO;;;;;;sEACrB,6LAAC;4DAAO,OAAM;sEAAW;;;;;;sEACzB,6LAAC;4DAAO,OAAM;sEAAY;;;;;;sEAC1B,6LAAC;4DAAO,OAAM;sEAAW;;;;;;;;;;;;gDAE1B,OAAO,MAAM,kBACZ,6LAAC;oDAAE,WAAU;;sEACX,6LAAC,uNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDACtB,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASlC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,0BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;4CAAyC;;;;;;6DAI1D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;4CAA0C;;;;;;;;;;;;8CAK/D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,SAAS,IAAM,aAAa;4CAC5B,UAAU;4CACV,WAAU;;8DAEV,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAG3B,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,UAAU;4CACV,WAAW,CAAC,6IAA6I,EACvJ,YACI,8GACA,kHACJ;sDAED,wBACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAAY;;;;;;;;;;;qEAG9B,6LAAC;gDAAI,WAAU;0DACZ,0BACC;;sEACE,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6LAAC;4DAAK,WAAU;sEAAY;;;;;;;iFAG9B;;sEACE,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;sEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYpD;GAlnBwB;;QAkBlB,iKAAA,CAAA,UAAO;;;KAlBW", "debugId": null}}, {"offset": {"line": 2063, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/members/member-details-dialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  <PERSON>alogContent,\n  <PERSON>alogHeader,\n  DialogTitle,\n  DialogClose,\n} from '@/components/ui/dialog'\nimport { Badge } from '@/components/ui/badge'\nimport { formatDate, formatCurrency, getMemberStatusText, getMemberStatusColor } from '@/lib/utils'\nimport { \n  User, \n  Phone, \n  Mail, \n  MapPin, \n  Calendar, \n  DollarSign,\n  Activity,\n  FileText\n} from 'lucide-react'\n\ninterface Member {\n  id: string\n  name: string\n  phone?: string\n  address?: string\n  notes?: string\n  status: string\n  createdAt: string\n  _count: {\n    incomes: number\n  }\n}\n\ninterface MemberDetailsDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  member?: Member | null\n}\n\ninterface MemberDetails {\n  member: Member\n  incomes: Array<{\n    id: string\n    amount: number\n    date: string\n    source: string\n    type: string\n    description?: string\n  }>\n  activities: Array<{\n    id: string\n    title: string\n    startDate: string\n    location?: string\n  }>\n  totalContributions: number\n}\n\nexport default function MemberDetailsDialog({\n  open,\n  onOpenChange,\n  member,\n}: MemberDetailsDialogProps) {\n  const [details, setDetails] = useState<MemberDetails | null>(null)\n  const [loading, setLoading] = useState(false)\n\n  useEffect(() => {\n    if (open && member) {\n      fetchMemberDetails()\n    }\n  }, [open, member])\n\n  const fetchMemberDetails = async () => {\n    if (!member) return\n\n    try {\n      setLoading(true)\n      const response = await fetch(`/api/members/${member.id}`)\n      if (!response.ok) throw new Error('فشل في جلب تفاصيل العضو')\n\n      const data = await response.json()\n      \n      // حساب إجمالي المساهمات\n      const totalContributions = data.incomes?.reduce((sum: number, income: any) => sum + income.amount, 0) || 0\n      \n      setDetails({\n        member: data,\n        incomes: data.incomes || [],\n        activities: data.activityParticipants?.map((p: any) => p.activity) || [],\n        totalContributions,\n      })\n    } catch (error) {\n      console.error('خطأ في جلب تفاصيل العضو:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (!member) return null\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-[50vw] max-h-[90vh] overflow-y-auto\">\n        <DialogClose onOpenChange={onOpenChange} />\n        <DialogHeader className=\"p-6 pb-0\">\n          <DialogTitle className=\"flex items-center space-x-2 space-x-reverse\">\n            <User className=\"w-5 h-5 text-diwan-600\" />\n            <span>تفاصيل العضو: {member.name}</span>\n          </DialogTitle>\n        </DialogHeader>\n\n        <div className=\"p-6 pt-0 space-y-6\">\n          {loading ? (\n            <div className=\"flex justify-center items-center h-32\">\n              <div className=\"text-gray-500\">جاري التحميل...</div>\n            </div>\n          ) : details ? (\n            <>\n              {/* المعلومات الأساسية */}\n              <div className=\"bg-gray-50 rounded-lg p-4\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">المعلومات الأساسية</h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div className=\"flex items-center space-x-2 space-x-reverse\">\n                    <User className=\"w-4 h-4 text-gray-500\" />\n                    <span className=\"text-sm text-gray-600\">الاسم:</span>\n                    <span className=\"font-medium\">{details.member.name}</span>\n                  </div>\n                  \n                  {details.member.phone && (\n                    <div className=\"flex items-center space-x-2 space-x-reverse\">\n                      <Phone className=\"w-4 h-4 text-gray-500\" />\n                      <span className=\"text-sm text-gray-600\">الهاتف:</span>\n                      <span className=\"font-medium\">{details.member.phone}</span>\n                    </div>\n                  )}\n\n                  {details.member.address && (\n                    <div className=\"flex items-center space-x-2 space-x-reverse\">\n                      <MapPin className=\"w-4 h-4 text-gray-500\" />\n                      <span className=\"text-sm text-gray-600\">العنوان:</span>\n                      <span className=\"font-medium\">{details.member.address}</span>\n                    </div>\n                  )}\n                  \n                  <div className=\"flex items-center space-x-2 space-x-reverse\">\n                    <Calendar className=\"w-4 h-4 text-gray-500\" />\n                    <span className=\"text-sm text-gray-600\">تاريخ الانضمام:</span>\n                    <span className=\"font-medium\">{formatDate(details.member.createdAt)}</span>\n                  </div>\n                  \n                  <div className=\"flex items-center space-x-2 space-x-reverse\">\n                    <Activity className=\"w-4 h-4 text-gray-500\" />\n                    <span className=\"text-sm text-gray-600\">الحالة:</span>\n                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getMemberStatusColor(details.member.status)}`}>\n                      {getMemberStatusText(details.member.status)}\n                    </span>\n                  </div>\n                </div>\n                \n                {details.member.notes && (\n                  <div className=\"mt-4\">\n                    <div className=\"flex items-center space-x-2 space-x-reverse mb-2\">\n                      <FileText className=\"w-4 h-4 text-gray-500\" />\n                      <span className=\"text-sm text-gray-600\">ملاحظات:</span>\n                    </div>\n                    <p className=\"text-sm bg-white p-3 rounded border\">{details.member.notes}</p>\n                  </div>\n                )}\n              </div>\n\n              {/* الإحصائيات المالية */}\n              <div className=\"bg-green-50 rounded-lg p-4\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">الإحصائيات المالية</h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-green-600\">\n                      {formatCurrency(details.totalContributions)}\n                    </div>\n                    <div className=\"text-sm text-gray-600\">إجمالي المساهمات</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-blue-600\">\n                      {details.incomes.length}\n                    </div>\n                    <div className=\"text-sm text-gray-600\">عدد الإيرادات</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-purple-600\">\n                      {details.incomes.length > 0 ? formatCurrency(details.totalContributions / details.incomes.length) : formatCurrency(0)}\n                    </div>\n                    <div className=\"text-sm text-gray-600\">متوسط المساهمة</div>\n                  </div>\n                </div>\n              </div>\n\n              {/* آخر الإيرادات */}\n              {details.incomes.length > 0 && (\n                <div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">آخر الإيرادات</h3>\n                  <div className=\"space-y-2 max-h-48 overflow-y-auto\">\n                    {details.incomes.slice(0, 10).map((income) => (\n                      <div key={income.id} className=\"flex justify-between items-center p-3 bg-gray-50 rounded\">\n                        <div>\n                          <div className=\"font-medium\">{income.source}</div>\n                          {income.description && (\n                            <div className=\"text-sm text-gray-600\">{income.description}</div>\n                          )}\n                          <div className=\"text-xs text-gray-500\">{formatDate(income.date)}</div>\n                        </div>\n                        <div className=\"text-green-600 font-semibold\">\n                          {formatCurrency(income.amount)}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {/* الأنشطة المشارك فيها */}\n              {details.activities.length > 0 && (\n                <div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">الأنشطة المشارك فيها</h3>\n                  <div className=\"space-y-2 max-h-48 overflow-y-auto\">\n                    {details.activities.slice(0, 10).map((activity) => (\n                      <div key={activity.id} className=\"flex justify-between items-center p-3 bg-blue-50 rounded\">\n                        <div>\n                          <div className=\"font-medium\">{activity.title}</div>\n                          {activity.location && (\n                            <div className=\"text-sm text-gray-600\">📍 {activity.location}</div>\n                          )}\n                        </div>\n                        <div className=\"text-sm text-gray-500\">\n                          {formatDate(activity.startDate)}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </>\n          ) : (\n            <div className=\"text-center text-gray-500\">لا توجد تفاصيل متاحة</div>\n          )}\n        </div>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAZA;;;;;AA6De,SAAS,oBAAoB,EAC1C,IAAI,EACJ,YAAY,EACZ,MAAM,EACmB;;IACzB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,QAAQ,QAAQ;gBAClB;YACF;QACF;wCAAG;QAAC;QAAM;KAAO;IAEjB,MAAM,qBAAqB;QACzB,IAAI,CAAC,QAAQ;QAEb,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,OAAO,EAAE,EAAE;YACxD,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,wBAAwB;YACxB,MAAM,qBAAqB,KAAK,OAAO,EAAE,OAAO,CAAC,KAAa,SAAgB,MAAM,OAAO,MAAM,EAAE,MAAM;YAEzG,WAAW;gBACT,QAAQ;gBACR,SAAS,KAAK,OAAO,IAAI,EAAE;gBAC3B,YAAY,KAAK,oBAAoB,EAAE,IAAI,CAAC,IAAW,EAAE,QAAQ,KAAK,EAAE;gBACxE;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,cAAW;oBAAC,cAAc;;;;;;8BAC3B,6LAAC,qIAAA,CAAA,eAAY;oBAAC,WAAU;8BACtB,cAAA,6LAAC,qIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;;oCAAK;oCAAe,OAAO,IAAI;;;;;;;;;;;;;;;;;;8BAIpC,6LAAC;oBAAI,WAAU;8BACZ,wBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCAAgB;;;;;;;;;;+BAE/B,wBACF;;0CAEE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6LAAC;wDAAK,WAAU;kEAAe,QAAQ,MAAM,CAAC,IAAI;;;;;;;;;;;;4CAGnD,QAAQ,MAAM,CAAC,KAAK,kBACnB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6LAAC;wDAAK,WAAU;kEAAe,QAAQ,MAAM,CAAC,KAAK;;;;;;;;;;;;4CAItD,QAAQ,MAAM,CAAC,OAAO,kBACrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6LAAC;wDAAK,WAAU;kEAAe,QAAQ,MAAM,CAAC,OAAO;;;;;;;;;;;;0DAIzD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6LAAC;wDAAK,WAAU;kEAAe,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,MAAM,CAAC,SAAS;;;;;;;;;;;;0DAGpE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6LAAC;wDAAK,WAAW,CAAC,yDAAyD,EAAE,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ,MAAM,CAAC,MAAM,GAAG;kEACvH,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,MAAM,CAAC,MAAM;;;;;;;;;;;;;;;;;;oCAK/C,QAAQ,MAAM,CAAC,KAAK,kBACnB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;0DAE1C,6LAAC;gDAAE,WAAU;0DAAuC,QAAQ,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;0CAM9E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,kBAAkB;;;;;;kEAE5C,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,QAAQ,OAAO,CAAC,MAAM;;;;;;kEAEzB,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,QAAQ,OAAO,CAAC,MAAM,GAAG,IAAI,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,kBAAkB,GAAG,QAAQ,OAAO,CAAC,MAAM,IAAI,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;kEAErH,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;4BAM5C,QAAQ,OAAO,CAAC,MAAM,GAAG,mBACxB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,uBACjC,6LAAC;gDAAoB,WAAU;;kEAC7B,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;0EAAe,OAAO,MAAM;;;;;;4DAC1C,OAAO,WAAW,kBACjB,6LAAC;gEAAI,WAAU;0EAAyB,OAAO,WAAW;;;;;;0EAE5D,6LAAC;gEAAI,WAAU;0EAAyB,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,IAAI;;;;;;;;;;;;kEAEhE,6LAAC;wDAAI,WAAU;kEACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,MAAM;;;;;;;+CATvB,OAAO,EAAE;;;;;;;;;;;;;;;;4BAkB1B,QAAQ,UAAU,CAAC,MAAM,GAAG,mBAC3B,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,UAAU,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,yBACpC,6LAAC;gDAAsB,WAAU;;kEAC/B,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;0EAAe,SAAS,KAAK;;;;;;4DAC3C,SAAS,QAAQ,kBAChB,6LAAC;gEAAI,WAAU;;oEAAwB;oEAAI,SAAS,QAAQ;;;;;;;;;;;;;kEAGhE,6LAAC;wDAAI,WAAU;kEACZ,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,SAAS,SAAS;;;;;;;+CARxB,SAAS,EAAE;;;;;;;;;;;;;;;;;qDAiB/B,6LAAC;wBAAI,WAAU;kCAA4B;;;;;;;;;;;;;;;;;;;;;;AAMvD;GA7LwB;KAAA", "debugId": null}}, {"offset": {"line": 2689, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-11 w-full items-center justify-between rounded-xl border-2 border-secondary-200 bg-white px-4 py-3 text-base font-medium text-secondary-700 transition-all duration-200 hover:border-secondary-300 focus:border-primary-500 focus:outline-none focus:ring-4 focus:ring-primary-500/20 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-5 w-5 text-secondary-400 transition-transform duration-200 data-[state=open]:rotate-180\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-xl border border-secondary-200 bg-white text-secondary-700 shadow-xl backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-2\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-2 pl-10 pr-3 text-sm font-bold text-secondary-600 bg-secondary-50\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-pointer select-none items-center rounded-lg py-3 pl-10 pr-3 text-sm font-medium text-secondary-700 transition-colors hover:bg-primary-50 focus:bg-primary-100 focus:text-primary-800 data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[state=checked]:bg-primary-100 data-[state=checked]:text-primary-800 data-[state=checked]:font-semibold\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-3 flex h-4 w-4 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4 text-primary-600\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sWACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,qKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,qKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,seACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wEAAwE;QACrF,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gYACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,qKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2904, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/members/account-statement-dialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  Dialog,\n  DialogContent,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from '@/components/ui/table'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport {\n  DollarSign,\n  TrendingUp,\n  Calendar,\n  FileText,\n  Download,\n  User,\n  Phone,\n  Mail,\n  MapPin,\n  CreditCard,\n  BarChart3,\n  Clock,\n  CheckCircle,\n  X\n} from 'lucide-react'\nimport { formatCurrency, formatDate, getIncomeTypeText } from '@/lib/utils'\nimport jsPDF from 'jspdf'\n\ninterface Member {\n  id: string\n  name: string\n  phone?: string\n  email?: string\n  address?: string\n  photo?: string\n  status: string\n  createdAt: string\n}\n\ninterface Transaction {\n  id: string\n  amount: number\n  date: string\n  source: string\n  type: string\n  description?: string\n  notes?: string\n  createdBy: {\n    name: string\n  }\n}\n\ninterface AccountStatement {\n  member: Member\n  summary: {\n    totalAmount: number\n    transactionCount: number\n    averageMonthlyContribution: number\n    firstTransactionDate?: string\n    lastTransactionDate?: string\n  }\n  byType: Record<string, { count: number; amount: number }>\n  monthlyData: Array<{\n    month: number\n    monthName: string\n    count: number\n    amount: number\n  }>\n  recentTransactions: Transaction[]\n  allTransactions: Transaction[]\n  period: {\n    startDate: string\n    endDate: string\n    year: number\n  }\n}\n\ninterface AccountStatementDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  memberId: string | null\n}\n\nexport default function AccountStatementDialog({\n  open,\n  onOpenChange,\n  memberId,\n}: AccountStatementDialogProps) {\n  const [loading, setLoading] = useState(false)\n  const [data, setData] = useState<AccountStatement | null>(null)\n  const [selectedYear, setSelectedYear] = useState<string>(new Date().getFullYear().toString())\n\n  // console.log('AccountStatementDialog rendered - open:', open, 'memberId:', memberId, 'data:', data, 'loading:', loading)\n\n  useEffect(() => {\n    if (open && memberId) {\n      // console.log('useEffect triggered - open:', open, 'memberId:', memberId, 'selectedYear:', selectedYear)\n      fetchAccountStatement()\n    }\n  }, [open, memberId, selectedYear])\n\n  const fetchAccountStatement = async () => {\n    if (!memberId) return\n\n    setLoading(true)\n    try {\n      // console.log('جاري جلب كشف الحساب للعضو:', memberId, 'للسنة:', selectedYear)\n      const response = await fetch(\n        `/api/members/${memberId}/account-statement?year=${selectedYear}`\n      )\n\n      if (!response.ok) {\n        const errorText = await response.text()\n        console.error('خطأ في الاستجابة:', errorText)\n        throw new Error('فشل في جلب كشف الحساب')\n      }\n\n      const result = await response.json()\n      // console.log('البيانات المستلمة:', result)\n      setData(result)\n    } catch (error) {\n      console.error('خطأ في جلب كشف الحساب:', error)\n      alert('حدث خطأ في جلب كشف الحساب: ' + (error as any)?.message)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n\n\n  const handleExport = () => {\n    if (!data) return\n\n    // إنشاء HTML للطباعة كـ PDF\n    const createPrintableHTML = () => {\n      return `\n        <!DOCTYPE html>\n        <html dir=\"rtl\" lang=\"ar\">\n        <head>\n          <meta charset=\"UTF-8\">\n          <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n          <title>كشف حساب العضو - ${data.member.name}</title>\n          <style>\n            @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;600;700&display=swap');\n\n            * {\n              margin: 0;\n              padding: 0;\n              box-sizing: border-box;\n            }\n\n            body {\n              font-family: 'Noto Sans Arabic', Arial, sans-serif;\n              direction: rtl;\n              text-align: right;\n              background: white;\n              color: #333;\n              line-height: 1.6;\n              padding: 20px;\n            }\n\n            .header {\n              text-align: center;\n              margin-bottom: 20px;\n              border-bottom: 2px solid #2563eb;\n              padding-bottom: 15px;\n            }\n\n            .header h1 {\n              font-size: 24px;\n              font-weight: 700;\n              color: #1e40af;\n              margin-bottom: 8px;\n            }\n\n            .header .member-name {\n              font-size: 18px;\n              font-weight: 600;\n              color: #374151;\n              margin-bottom: 4px;\n            }\n\n            .header .year {\n              font-size: 14px;\n              color: #6b7280;\n              margin-bottom: 4px;\n            }\n\n            .header .date {\n              font-size: 12px;\n              color: #9ca3af;\n            }\n\n            .section {\n              margin-bottom: 15px;\n              background: #f9fafb;\n              border: 1px solid #e5e7eb;\n              border-radius: 6px;\n              padding: 15px;\n            }\n\n            .section-title {\n              font-size: 16px;\n              font-weight: 600;\n              color: #1f2937;\n              margin-bottom: 10px;\n              border-bottom: 1px solid #e5e7eb;\n              padding-bottom: 5px;\n            }\n\n            .info-grid {\n              display: grid;\n              grid-template-columns: 1fr 1fr;\n              gap: 10px;\n              margin-bottom: 10px;\n            }\n\n            .info-item {\n              background: white;\n              padding: 8px;\n              border-radius: 4px;\n              border: 1px solid #e5e7eb;\n              font-size: 14px;\n            }\n\n            .info-label {\n              font-weight: 600;\n              color: #374151;\n              margin-left: 8px;\n            }\n\n            .info-value {\n              color: #6b7280;\n            }\n\n            .summary-grid {\n              display: grid;\n              grid-template-columns: repeat(2, 1fr);\n              gap: 15px;\n              margin-bottom: 20px;\n            }\n\n            .summary-item {\n              background: white;\n              padding: 15px;\n              border-radius: 8px;\n              border: 1px solid #e5e7eb;\n              text-align: center;\n            }\n\n            .summary-label {\n              font-size: 14px;\n              color: #6b7280;\n              margin-bottom: 8px;\n            }\n\n            .summary-value {\n              font-size: 18px;\n              font-weight: 600;\n              color: #059669;\n            }\n\n            .monthly-grid {\n              display: grid;\n              grid-template-columns: repeat(4, 1fr);\n              gap: 8px;\n            }\n\n            .month-item {\n              background: white;\n              padding: 8px;\n              border-radius: 4px;\n              border: 1px solid #e5e7eb;\n              text-align: center;\n            }\n\n            .month-item.has-amount {\n              background: #f0f9ff;\n              border-color: #0ea5e9;\n            }\n\n            .month-name {\n              font-weight: 600;\n              color: #374151;\n              margin-bottom: 3px;\n              font-size: 12px;\n            }\n\n            .month-amount {\n              font-size: 14px;\n              font-weight: 600;\n              margin-bottom: 2px;\n            }\n\n            .month-amount.positive {\n              color: #059669;\n            }\n\n            .month-amount.zero {\n              color: #9ca3af;\n            }\n\n            .month-count {\n              font-size: 10px;\n              color: #6b7280;\n            }\n\n            .type-item {\n              background: white;\n              padding: 12px;\n              border-radius: 6px;\n              border: 1px solid #e5e7eb;\n              margin-bottom: 8px;\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\n            }\n\n            .type-name {\n              font-weight: 600;\n              color: #374151;\n            }\n\n            .type-details {\n              color: #059669;\n              font-weight: 600;\n            }\n\n            @media print {\n              body {\n                padding: 10px;\n                font-size: 11px;\n              }\n              .header {\n                margin-bottom: 15px;\n                padding-bottom: 10px;\n              }\n              .section {\n                break-inside: avoid;\n                margin-bottom: 10px;\n                padding: 10px;\n              }\n              .monthly-grid {\n                grid-template-columns: repeat(6, 1fr);\n                gap: 4px;\n              }\n              .month-item {\n                padding: 4px;\n              }\n              .month-name {\n                font-size: 10px;\n              }\n              .month-amount {\n                font-size: 12px;\n              }\n              .month-count {\n                font-size: 8px;\n              }\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"header\">\n            <h1>كشف حساب العضو</h1>\n            <div class=\"member-name\">${data.member.name}</div>\n            <div class=\"year\">عام ${selectedYear}</div>\n            <div class=\"date\">تاريخ التقرير: ${new Date().toLocaleDateString('en-GB')}</div>\n          </div>\n\n          <div class=\"section\">\n            <div class=\"section-title\">معلومات العضو</div>\n            <div class=\"info-grid\">\n              <div class=\"info-item\">\n                <span class=\"info-label\">الاسم:</span>\n                <span class=\"info-value\">${data.member.name}</span>\n              </div>\n              ${data.member.phone ? `\n              <div class=\"info-item\">\n                <span class=\"info-label\">الهاتف:</span>\n                <span class=\"info-value\">${data.member.phone}</span>\n              </div>\n              ` : ''}\n              ${data.member.email ? `\n              <div class=\"info-item\">\n                <span class=\"info-label\">البريد الإلكتروني:</span>\n                <span class=\"info-value\">${data.member.email}</span>\n              </div>\n              ` : ''}\n              <div class=\"info-item\">\n                <span class=\"info-label\">عضو منذ:</span>\n                <span class=\"info-value\">${formatDate(data.member.createdAt)}</span>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"section\">\n            <div class=\"section-title\">إجمالي المساهمات</div>\n            <div style=\"text-align: center; padding: 15px;\">\n              <div class=\"summary-value\" style=\"font-size: 28px; color: #059669; font-weight: bold;\">\n                ${formatCurrency(data.summary.totalAmount)}\n              </div>\n            </div>\n          </div>\n\n\n\n          <div class=\"section\">\n            <div class=\"section-title\">المساهمات الشهرية - ${selectedYear}</div>\n            <div class=\"monthly-grid\">\n              ${data.monthlyData.map(month => `\n                <div class=\"month-item ${month.count > 0 ? 'has-amount' : ''}\">\n                  <div class=\"month-name\">${month.monthName}</div>\n                  <div class=\"month-amount ${month.count > 0 ? 'positive' : 'zero'}\">\n                    ${formatCurrency(month.amount)}\n                  </div>\n                  <div class=\"month-count\">${month.count} معاملة</div>\n                </div>\n              `).join('')}\n            </div>\n          </div>\n\n          <div style=\"text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; color: #6b7280; font-size: 12px;\">\n            تم إنشاء هذا التقرير بواسطة نظام إدارة ديوان أبو علوش\n          </div>\n        </body>\n        </html>\n      `\n    }\n\n    // إنشاء نافذة جديدة للطباعة\n    const printWindow = window.open('', '_blank')\n    if (!printWindow) {\n      alert('يرجى السماح بفتح النوافذ المنبثقة لتصدير التقرير')\n      return\n    }\n\n    const htmlContent = createPrintableHTML()\n    printWindow.document.write(htmlContent)\n    printWindow.document.close()\n\n    // انتظار تحميل المحتوى ثم فتح حوار الطباعة\n    printWindow.onload = () => {\n      printWindow.focus()\n      // إعطاء وقت إضافي لتحميل الخطوط\n      setTimeout(() => {\n        printWindow.print()\n        // إغلاق النافذة بعد الطباعة (اختياري)\n        printWindow.onafterprint = () => {\n          printWindow.close()\n        }\n      }, 1000)\n    }\n  }\n\n  // إنشاء قائمة السنوات (آخر 5 سنوات)\n  const currentYear = new Date().getFullYear()\n  const years = Array.from({ length: 5 }, (_, i) => currentYear - i)\n\n  // تحديث السنة المحددة إذا كانت هناك بيانات في سنة أخرى\n  useEffect(() => {\n    if (data && data.summary.transactionCount === 0 && selectedYear === currentYear.toString()) {\n      // إذا لم توجد بيانات في السنة الحالية، جرب السنة الماضية\n      setSelectedYear((currentYear - 1).toString())\n    }\n  }, [data, selectedYear, currentYear])\n\n  // لا نخفي الحوار إذا لم تكن هناك بيانات، بل نعرض رسالة مناسبة\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-[50vw] max-h-[90vh] overflow-y-auto\">\n        <DialogHeader className=\"p-6 pb-4 border-b border-gray-100 bg-gradient-to-r from-diwan-50 to-blue-50\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"p-3 bg-white rounded-xl shadow-sm border border-diwan-200\">\n                <FileText className=\"w-6 h-6 text-diwan-600\" />\n              </div>\n              <div>\n                <DialogTitle className=\"text-xl font-bold text-gray-900\">\n                  كشف حساب العضو\n                </DialogTitle>\n                {data && (\n                  <p className=\"text-gray-600 mt-1\">\n                    {data.member.name} - عام {selectedYear}\n                  </p>\n                )}\n              </div>\n            </div>\n\n            <div className=\"flex items-center gap-2\">\n              <Select value={selectedYear} onValueChange={setSelectedYear}>\n                <SelectTrigger className=\"w-32\">\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  {years.map(year => (\n                    <SelectItem key={year} value={year.toString()}>\n                      {year}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n              \n              {data && (\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={handleExport}\n                  className=\"text-green-600 border-green-600 hover:bg-green-50\"\n                >\n                  <Download className=\"w-4 h-4 ml-1\" />\n                  تصدير\n                </Button>\n              )}\n\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => onOpenChange(false)}\n                className=\"text-gray-600 border-gray-300 hover:bg-gray-50\"\n              >\n                <X className=\"w-4 h-4 ml-1\" />\n                خروج\n              </Button>\n            </div>\n          </div>\n        </DialogHeader>\n\n        <div className=\"p-6 space-y-6\">\n          {loading ? (\n            <div className=\"flex justify-center items-center h-64\">\n              <div className=\"text-gray-500\">جاري تحميل كشف الحساب...</div>\n            </div>\n          ) : data ? (\n            <>\n              {/* {console.log('عرض البيانات:', data)} */}\n              {/* معلومات العضو */}\n              <Card className=\"border-gray-200\">\n                <CardHeader className=\"pb-3\">\n                  <CardTitle className=\"flex items-center gap-2 text-lg\">\n                    <User className=\"w-5 h-5 text-diwan-600\" />\n                    معلومات العضو\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                    <div className=\"flex items-center gap-2\">\n                      <User className=\"w-4 h-4 text-gray-500\" />\n                      <span className=\"font-medium\">{data.member.name}</span>\n                    </div>\n                    {data.member.phone && (\n                      <div className=\"flex items-center gap-2\">\n                        <Phone className=\"w-4 h-4 text-gray-500\" />\n                        <span>{data.member.phone}</span>\n                      </div>\n                    )}\n                    {data.member.email && (\n                      <div className=\"flex items-center gap-2\">\n                        <Mail className=\"w-4 h-4 text-gray-500\" />\n                        <span>{data.member.email}</span>\n                      </div>\n                    )}\n                    <div className=\"flex items-center gap-2\">\n                      <Calendar className=\"w-4 h-4 text-gray-500\" />\n                      <span>عضو منذ {formatDate(data.member.createdAt)}</span>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* الملخص المالي */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                <Card className=\"border-green-200 bg-green-50\">\n                  <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                    <CardTitle className=\"text-sm font-medium text-green-800\">\n                      إجمالي المساهمات\n                    </CardTitle>\n                    <DollarSign className=\"h-4 w-4 text-green-600\" />\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"text-2xl font-bold text-green-700\">\n                      {formatCurrency(data.summary.totalAmount)}\n                    </div>\n                  </CardContent>\n                </Card>\n\n                <Card className=\"border-blue-200 bg-blue-50\">\n                  <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                    <CardTitle className=\"text-sm font-medium text-blue-800\">\n                      عدد المعاملات\n                    </CardTitle>\n                    <CreditCard className=\"h-4 w-4 text-blue-600\" />\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"text-2xl font-bold text-blue-700\">\n                      {data.summary.transactionCount}\n                    </div>\n                  </CardContent>\n                </Card>\n\n                <Card className=\"border-purple-200 bg-purple-50\">\n                  <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                    <CardTitle className=\"text-sm font-medium text-purple-800\">\n                      متوسط شهري\n                    </CardTitle>\n                    <TrendingUp className=\"h-4 w-4 text-purple-600\" />\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"text-2xl font-bold text-purple-700\">\n                      {formatCurrency(data.summary.averageMonthlyContribution)}\n                    </div>\n                  </CardContent>\n                </Card>\n\n                <Card className=\"border-orange-200 bg-orange-50\">\n                  <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                    <CardTitle className=\"text-sm font-medium text-orange-800\">\n                      آخر مساهمة\n                    </CardTitle>\n                    <Clock className=\"h-4 w-4 text-orange-600\" />\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"text-sm font-bold text-orange-700\">\n                      {data.summary.lastTransactionDate \n                        ? formatDate(data.summary.lastTransactionDate)\n                        : 'لا توجد مساهمات'\n                      }\n                    </div>\n                  </CardContent>\n                </Card>\n              </div>\n\n              {/* التوزيع حسب النوع */}\n              <Card className=\"border-gray-200\">\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2\">\n                    <BarChart3 className=\"w-5 h-5 text-diwan-600\" />\n                    التوزيع حسب نوع المساهمة\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                    {Object.entries(data.byType).map(([type, stats]) => (\n                      <div key={type} className=\"p-4 border rounded-lg\">\n                        <div className=\"flex items-center justify-between mb-2\">\n                          <span className=\"text-sm font-medium text-gray-600\">\n                            {getIncomeTypeText(type)}\n                          </span>\n                          <Badge variant=\"secondary\">{stats.count}</Badge>\n                        </div>\n                        <div className=\"text-lg font-bold text-diwan-600\">\n                          {formatCurrency(stats.amount)}\n                        </div>\n                        <div className=\"text-xs text-gray-500\">\n                          {((stats.amount / data.summary.totalAmount) * 100).toFixed(1)}% من الإجمالي\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* المساهمات الشهرية */}\n              <Card className=\"border-gray-200\">\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2\">\n                    <Calendar className=\"w-5 h-5 text-diwan-600\" />\n                    المساهمات الشهرية - {selectedYear}\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3\">\n                    {data.monthlyData.map((month) => (\n                      <div\n                        key={month.month}\n                        className={`p-3 border rounded-lg text-center ${\n                          month.count > 0 ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200'\n                        }`}\n                      >\n                        <div className=\"text-sm font-medium text-gray-600 mb-1\">\n                          {month.monthName}\n                        </div>\n                        <div className={`text-lg font-bold ${\n                          month.count > 0 ? 'text-green-600' : 'text-gray-400'\n                        }`}>\n                          {formatCurrency(month.amount)}\n                        </div>\n                        <div className=\"text-xs text-gray-500\">\n                          {month.count} معاملة\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* آخر المعاملات */}\n              <Card className=\"border-gray-200\">\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2\">\n                    <Clock className=\"w-5 h-5 text-diwan-600\" />\n                    آخر المعاملات\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  {data.recentTransactions.length > 0 ? (\n                    <Table>\n                      <TableHeader>\n                        <TableRow>\n                          <TableHead>التاريخ</TableHead>\n                          <TableHead>المبلغ</TableHead>\n                          <TableHead>المصدر</TableHead>\n                          <TableHead>النوع</TableHead>\n                          <TableHead>الوصف</TableHead>\n                          <TableHead>المُدخِل</TableHead>\n                        </TableRow>\n                      </TableHeader>\n                      <TableBody>\n                        {data.recentTransactions.map((transaction) => (\n                          <TableRow key={transaction.id}>\n                            <TableCell>\n                              {formatDate(transaction.date)}\n                            </TableCell>\n                            <TableCell>\n                              <span className=\"font-medium text-green-600\">\n                                {formatCurrency(transaction.amount)}\n                              </span>\n                            </TableCell>\n                            <TableCell>{transaction.source}</TableCell>\n                            <TableCell>\n                              <Badge variant=\"outline\">\n                                {getIncomeTypeText(transaction.type)}\n                              </Badge>\n                            </TableCell>\n                            <TableCell>\n                              {transaction.description || '-'}\n                            </TableCell>\n                            <TableCell className=\"text-sm text-gray-600\">\n                              {transaction.createdBy.name}\n                            </TableCell>\n                          </TableRow>\n                        ))}\n                      </TableBody>\n                    </Table>\n                  ) : (\n                    <div className=\"text-center py-8 text-gray-500\">\n                      لا توجد معاملات في هذه الفترة\n                    </div>\n                  )}\n                </CardContent>\n              </Card>\n\n              {/* جميع المعاملات */}\n              {data.allTransactions.length > 5 && (\n                <Card className=\"border-gray-200\">\n                  <CardHeader>\n                    <CardTitle className=\"flex items-center gap-2\">\n                      <FileText className=\"w-5 h-5 text-diwan-600\" />\n                      جميع المعاملات ({data.allTransactions.length})\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"max-h-96 overflow-y-auto\">\n                      <Table>\n                        <TableHeader>\n                          <TableRow>\n                            <TableHead>التاريخ</TableHead>\n                            <TableHead>المبلغ</TableHead>\n                            <TableHead>المصدر</TableHead>\n                            <TableHead>النوع</TableHead>\n                            <TableHead>الوصف</TableHead>\n                            <TableHead>المُدخِل</TableHead>\n                          </TableRow>\n                        </TableHeader>\n                        <TableBody>\n                          {data.allTransactions.map((transaction) => (\n                            <TableRow key={transaction.id}>\n                              <TableCell>\n                                {formatDate(transaction.date)}\n                              </TableCell>\n                              <TableCell>\n                                <span className=\"font-medium text-green-600\">\n                                  {formatCurrency(transaction.amount)}\n                                </span>\n                              </TableCell>\n                              <TableCell>{transaction.source}</TableCell>\n                              <TableCell>\n                                <Badge variant=\"outline\">\n                                  {getIncomeTypeText(transaction.type)}\n                                </Badge>\n                              </TableCell>\n                              <TableCell>\n                                {transaction.description || '-'}\n                              </TableCell>\n                              <TableCell className=\"text-sm text-gray-600\">\n                                {transaction.createdBy.name}\n                              </TableCell>\n                            </TableRow>\n                          ))}\n                        </TableBody>\n                      </Table>\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n            </>\n          ) : (\n            <div className=\"text-center py-12\">\n              <FileText className=\"w-16 h-16 text-gray-300 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                لا توجد بيانات لعرضها\n              </h3>\n              <p className=\"text-gray-500 mb-4\">\n                لم يتم العثور على معاملات مالية لهذا العضو في عام {selectedYear}\n              </p>\n              <p className=\"text-sm text-blue-600\">\n                💡 جرب تغيير السنة من القائمة المنسدلة أعلاه لعرض بيانات سنوات أخرى\n              </p>\n            </div>\n          )}\n        </div>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAMA;AAQA;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;;;AA3CA;;;;;;;;;;AAqGe,SAAS,uBAAuB,EAC7C,IAAI,EACJ,YAAY,EACZ,QAAQ,EACoB;;IAC5B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,IAAI,OAAO,WAAW,GAAG,QAAQ;IAE1F,0HAA0H;IAE1H,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,IAAI,QAAQ,UAAU;gBACpB,yGAAyG;gBACzG;YACF;QACF;2CAAG;QAAC;QAAM;QAAU;KAAa;IAEjC,MAAM,wBAAwB;QAC5B,IAAI,CAAC,UAAU;QAEf,WAAW;QACX,IAAI;YACF,8EAA8E;YAC9E,MAAM,WAAW,MAAM,MACrB,CAAC,aAAa,EAAE,SAAS,wBAAwB,EAAE,cAAc;YAGnE,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,qBAAqB;gBACnC,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,4CAA4C;YAC5C,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,gCAAiC,OAAe;QACxD,SAAU;YACR,WAAW;QACb;IACF;IAIA,MAAM,eAAe;QACnB,IAAI,CAAC,MAAM;QAEX,4BAA4B;QAC5B,MAAM,sBAAsB;YAC1B,OAAO,CAAC;;;;;;kCAMoB,EAAE,KAAK,MAAM,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;qCA+NhB,EAAE,KAAK,MAAM,CAAC,IAAI,CAAC;kCACtB,EAAE,aAAa;6CACJ,EAAE,IAAI,OAAO,kBAAkB,CAAC,SAAS;;;;;;;;yCAQ7C,EAAE,KAAK,MAAM,CAAC,IAAI,CAAC;;cAE9C,EAAE,KAAK,MAAM,CAAC,KAAK,GAAG,CAAC;;;yCAGI,EAAE,KAAK,MAAM,CAAC,KAAK,CAAC;;cAE/C,CAAC,GAAG,GAAG;cACP,EAAE,KAAK,MAAM,CAAC,KAAK,GAAG,CAAC;;;yCAGI,EAAE,KAAK,MAAM,CAAC,KAAK,CAAC;;cAE/C,CAAC,GAAG,GAAG;;;yCAGoB,EAAE,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,MAAM,CAAC,SAAS,EAAE;;;;;;;;;gBAS7D,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,WAAW,EAAE;;;;;;;;2DAQA,EAAE,aAAa;;cAE5D,EAAE,KAAK,WAAW,CAAC,GAAG,CAAC,CAAA,QAAS,CAAC;uCACR,EAAE,MAAM,KAAK,GAAG,IAAI,eAAe,GAAG;0CACnC,EAAE,MAAM,SAAS,CAAC;2CACjB,EAAE,MAAM,KAAK,GAAG,IAAI,aAAa,OAAO;oBAC/D,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,MAAM,EAAE;;2CAER,EAAE,MAAM,KAAK,CAAC;;cAE3C,CAAC,EAAE,IAAI,CAAC,IAAI;;;;;;;;;MASpB,CAAC;QACH;QAEA,4BAA4B;QAC5B,MAAM,cAAc,OAAO,IAAI,CAAC,IAAI;QACpC,IAAI,CAAC,aAAa;YAChB,MAAM;YACN;QACF;QAEA,MAAM,cAAc;QACpB,YAAY,QAAQ,CAAC,KAAK,CAAC;QAC3B,YAAY,QAAQ,CAAC,KAAK;QAE1B,2CAA2C;QAC3C,YAAY,MAAM,GAAG;YACnB,YAAY,KAAK;YACjB,gCAAgC;YAChC,WAAW;gBACT,YAAY,KAAK;gBACjB,sCAAsC;gBACtC,YAAY,YAAY,GAAG;oBACzB,YAAY,KAAK;gBACnB;YACF,GAAG;QACL;IACF;IAEA,oCAAoC;IACpC,MAAM,cAAc,IAAI,OAAO,WAAW;IAC1C,MAAM,QAAQ,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAE,GAAG,CAAC,GAAG,IAAM,cAAc;IAEhE,uDAAuD;IACvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,IAAI,QAAQ,KAAK,OAAO,CAAC,gBAAgB,KAAK,KAAK,iBAAiB,YAAY,QAAQ,IAAI;gBAC1F,yDAAyD;gBACzD,gBAAgB,CAAC,cAAc,CAAC,EAAE,QAAQ;YAC5C;QACF;2CAAG;QAAC;QAAM;QAAc;KAAY;IAEpC,8DAA8D;IAE9D,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;oBAAC,WAAU;8BACtB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;;0DACC,6LAAC,qIAAA,CAAA,cAAW;gDAAC,WAAU;0DAAkC;;;;;;4CAGxD,sBACC,6LAAC;gDAAE,WAAU;;oDACV,KAAK,MAAM,CAAC,IAAI;oDAAC;oDAAQ;;;;;;;;;;;;;;;;;;;0CAMlC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAc,eAAe;;0DAC1C,6LAAC,qIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;0DAEd,6LAAC,qIAAA,CAAA,gBAAa;0DACX,MAAM,GAAG,CAAC,CAAA,qBACT,6LAAC,qIAAA,CAAA,aAAU;wDAAY,OAAO,KAAK,QAAQ;kEACxC;uDADc;;;;;;;;;;;;;;;;oCAOtB,sBACC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAKzC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,aAAa;wCAC5B,WAAU;;0DAEV,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;8BAOtC,6LAAC;oBAAI,WAAU;8BACZ,wBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCAAgB;;;;;;;;;;+BAE/B,qBACF;;0CAGE,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAA2B;;;;;;;;;;;;kDAI/C,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;sEAAe,KAAK,MAAM,CAAC,IAAI;;;;;;;;;;;;gDAEhD,KAAK,MAAM,CAAC,KAAK,kBAChB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;sEAAM,KAAK,MAAM,CAAC,KAAK;;;;;;;;;;;;gDAG3B,KAAK,MAAM,CAAC,KAAK,kBAChB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;sEAAM,KAAK,MAAM,CAAC,KAAK;;;;;;;;;;;;8DAG5B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;;gEAAK;gEAAS,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,MAAM,CAAC,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOvD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,6LAAC,mIAAA,CAAA,aAAU;gDAAC,WAAU;;kEACpB,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAqC;;;;;;kEAG1D,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;0DAExB,6LAAC,mIAAA,CAAA,cAAW;0DACV,cAAA,6LAAC;oDAAI,WAAU;8DACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,WAAW;;;;;;;;;;;;;;;;;kDAK9C,6LAAC,mIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,6LAAC,mIAAA,CAAA,aAAU;gDAAC,WAAU;;kEACpB,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAoC;;;;;;kEAGzD,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;0DAExB,6LAAC,mIAAA,CAAA,cAAW;0DACV,cAAA,6LAAC;oDAAI,WAAU;8DACZ,KAAK,OAAO,CAAC,gBAAgB;;;;;;;;;;;;;;;;;kDAKpC,6LAAC,mIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,6LAAC,mIAAA,CAAA,aAAU;gDAAC,WAAU;;kEACpB,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAsC;;;;;;kEAG3D,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;0DAExB,6LAAC,mIAAA,CAAA,cAAW;0DACV,cAAA,6LAAC;oDAAI,WAAU;8DACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,0BAA0B;;;;;;;;;;;;;;;;;kDAK7D,6LAAC,mIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,6LAAC,mIAAA,CAAA,aAAU;gDAAC,WAAU;;kEACpB,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAsC;;;;;;kEAG3D,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;;0DAEnB,6LAAC,mIAAA,CAAA,cAAW;0DACV,cAAA,6LAAC;oDAAI,WAAU;8DACZ,KAAK,OAAO,CAAC,mBAAmB,GAC7B,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,OAAO,CAAC,mBAAmB,IAC3C;;;;;;;;;;;;;;;;;;;;;;;0CAQZ,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,qNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAA2B;;;;;;;;;;;;kDAIpD,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;sDACZ,OAAO,OAAO,CAAC,KAAK,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,MAAM,iBAC7C,6LAAC;oDAAe,WAAU;;sEACxB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EACb,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE;;;;;;8EAErB,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAa,MAAM,KAAK;;;;;;;;;;;;sEAEzC,6LAAC;4DAAI,WAAU;sEACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,MAAM;;;;;;sEAE9B,6LAAC;4DAAI,WAAU;;gEACZ,CAAC,AAAC,MAAM,MAAM,GAAG,KAAK,OAAO,CAAC,WAAW,GAAI,GAAG,EAAE,OAAO,CAAC;gEAAG;;;;;;;;mDAXxD;;;;;;;;;;;;;;;;;;;;;0CAoBlB,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAA2B;gDAC1B;;;;;;;;;;;;kDAGzB,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;sDACZ,KAAK,WAAW,CAAC,GAAG,CAAC,CAAC,sBACrB,6LAAC;oDAEC,WAAW,CAAC,kCAAkC,EAC5C,MAAM,KAAK,GAAG,IAAI,iCAAiC,8BACnD;;sEAEF,6LAAC;4DAAI,WAAU;sEACZ,MAAM,SAAS;;;;;;sEAElB,6LAAC;4DAAI,WAAW,CAAC,kBAAkB,EACjC,MAAM,KAAK,GAAG,IAAI,mBAAmB,iBACrC;sEACC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,MAAM;;;;;;sEAE9B,6LAAC;4DAAI,WAAU;;gEACZ,MAAM,KAAK;gEAAC;;;;;;;;mDAdV,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;0CAuB1B,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAA2B;;;;;;;;;;;;kDAIhD,6LAAC,mIAAA,CAAA,cAAW;kDACT,KAAK,kBAAkB,CAAC,MAAM,GAAG,kBAChC,6LAAC,oIAAA,CAAA,QAAK;;8DACJ,6LAAC,oIAAA,CAAA,cAAW;8DACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;0EACP,6LAAC,oIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,6LAAC,oIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,6LAAC,oIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,6LAAC,oIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,6LAAC,oIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,6LAAC,oIAAA,CAAA,YAAS;0EAAC;;;;;;;;;;;;;;;;;8DAGf,6LAAC,oIAAA,CAAA,YAAS;8DACP,KAAK,kBAAkB,CAAC,GAAG,CAAC,CAAC,4BAC5B,6LAAC,oIAAA,CAAA,WAAQ;;8EACP,6LAAC,oIAAA,CAAA,YAAS;8EACP,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,YAAY,IAAI;;;;;;8EAE9B,6LAAC,oIAAA,CAAA,YAAS;8EACR,cAAA,6LAAC;wEAAK,WAAU;kFACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,MAAM;;;;;;;;;;;8EAGtC,6LAAC,oIAAA,CAAA,YAAS;8EAAE,YAAY,MAAM;;;;;;8EAC9B,6LAAC,oIAAA,CAAA,YAAS;8EACR,cAAA,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;kFACZ,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY,IAAI;;;;;;;;;;;8EAGvC,6LAAC,oIAAA,CAAA,YAAS;8EACP,YAAY,WAAW,IAAI;;;;;;8EAE9B,6LAAC,oIAAA,CAAA,YAAS;oEAAC,WAAU;8EAClB,YAAY,SAAS,CAAC,IAAI;;;;;;;2DAnBhB,YAAY,EAAE;;;;;;;;;;;;;;;iEA0BnC,6LAAC;4CAAI,WAAU;sDAAiC;;;;;;;;;;;;;;;;;4BAQrD,KAAK,eAAe,CAAC,MAAM,GAAG,mBAC7B,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAA2B;gDAC9B,KAAK,eAAe,CAAC,MAAM;gDAAC;;;;;;;;;;;;kDAGjD,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;kEACJ,6LAAC,oIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;8EACP,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;;;;;;;;;;;;kEAGf,6LAAC,oIAAA,CAAA,YAAS;kEACP,KAAK,eAAe,CAAC,GAAG,CAAC,CAAC,4BACzB,6LAAC,oIAAA,CAAA,WAAQ;;kFACP,6LAAC,oIAAA,CAAA,YAAS;kFACP,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,YAAY,IAAI;;;;;;kFAE9B,6LAAC,oIAAA,CAAA,YAAS;kFACR,cAAA,6LAAC;4EAAK,WAAU;sFACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,MAAM;;;;;;;;;;;kFAGtC,6LAAC,oIAAA,CAAA,YAAS;kFAAE,YAAY,MAAM;;;;;;kFAC9B,6LAAC,oIAAA,CAAA,YAAS;kFACR,cAAA,6LAAC,oIAAA,CAAA,QAAK;4EAAC,SAAQ;sFACZ,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY,IAAI;;;;;;;;;;;kFAGvC,6LAAC,oIAAA,CAAA,YAAS;kFACP,YAAY,WAAW,IAAI;;;;;;kFAE9B,6LAAC,oIAAA,CAAA,YAAS;wEAAC,WAAU;kFAClB,YAAY,SAAS,CAAC,IAAI;;;;;;;+DAnBhB,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;qDA+B7C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CAGvD,6LAAC;gCAAE,WAAU;;oCAAqB;oCACmB;;;;;;;0CAErD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD;GAruBwB;KAAA", "debugId": null}}, {"offset": {"line": 4450, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/members/member-income-dialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport {\n  Dialog,\n  DialogContent,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { DollarSign, User } from 'lucide-react'\n\nconst memberIncomeSchema = z.object({\n  amount: z.number().positive('المبلغ يجب أن يكون أكبر من صفر'),\n  date: z.string().min(1, 'التاريخ مطلوب'),\n  source: z.string().min(1, 'مصدر الإيراد مطلوب'),\n  type: z.enum(['SUBSCRIPTION', 'DONATION', 'EVENT', 'OTHER']).default('SUBSCRIPTION'),\n  description: z.string().optional(),\n  notes: z.string().optional(),\n})\n\ntype MemberIncomeInput = z.infer<typeof memberIncomeSchema>\n\ninterface Member {\n  id: string\n  name: string\n}\n\ninterface MemberIncomeDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  member: Member | null\n  onSuccess?: () => void\n}\n\nexport default function MemberIncomeDialog({\n  open,\n  onOpenChange,\n  member,\n  onSuccess,\n}: MemberIncomeDialogProps) {\n  const [loading, setLoading] = useState(false)\n\n  const {\n    register,\n    handleSubmit,\n    reset,\n    setValue,\n    watch,\n    formState: { errors },\n  } = useForm<MemberIncomeInput>({\n    resolver: zodResolver(memberIncomeSchema),\n    defaultValues: {\n      amount: 0,\n      date: new Date().toISOString().split('T')[0], // التاريخ الحالي\n      source: '',\n      type: 'SUBSCRIPTION',\n      description: '',\n      notes: '',\n    },\n  })\n\n  const selectedType = watch('type')\n\n  useEffect(() => {\n    if (open && member) {\n      reset({\n        amount: 0,\n        date: new Date().toISOString().split('T')[0],\n        source: '',\n        type: 'SUBSCRIPTION',\n        description: '',\n        notes: '',\n      })\n    }\n  }, [open, member, reset])\n\n  const onSubmit = async (data: MemberIncomeInput) => {\n    if (!member) return\n\n    try {\n      setLoading(true)\n\n      // تحويل البيانات\n      const submitData = {\n        ...data,\n        amount: Number(data.amount),\n        date: new Date(data.date),\n        memberId: member.id,\n        description: data.description?.trim() || null,\n        notes: data.notes?.trim() || null,\n      }\n\n      const response = await fetch('/api/incomes', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(submitData),\n      })\n\n      if (!response.ok) {\n        const error = await response.json()\n        throw new Error(error.error || 'حدث خطأ')\n      }\n\n      alert(`تم إضافة إيراد للعضو ${member.name} بنجاح`)\n      onOpenChange(false)\n      onSuccess?.()\n    } catch (error: any) {\n      console.error('خطأ في إضافة الإيراد:', error)\n      alert(error.message || 'حدث خطأ في إضافة الإيراد')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const getIncomeTypeText = (type: string) => {\n    switch (type) {\n      case 'SUBSCRIPTION': return 'اشتراكات'\n      case 'DONATION': return 'تبرعات'\n      case 'EVENT': return 'فعاليات'\n      case 'OTHER': return 'أخرى'\n      default: return type\n    }\n  }\n\n  if (!member) return null\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-[50vw] max-h-[90vh] overflow-y-auto\">\n        <DialogHeader className=\"p-6 pb-4 border-b border-gray-100 bg-gradient-to-r from-diwan-50 to-blue-50\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"p-3 bg-white rounded-xl shadow-sm border border-diwan-200\">\n              <DollarSign className=\"w-6 h-6 text-diwan-600\" />\n            </div>\n            <div>\n              <DialogTitle className=\"text-xl font-bold text-gray-900\">\n                إضافة إيراد للعضو\n              </DialogTitle>\n              <div className=\"flex items-center gap-2 mt-1\">\n                <User className=\"w-4 h-4 text-gray-500\" />\n                <p className=\"text-gray-600 font-medium\">{member.name}</p>\n              </div>\n            </div>\n          </div>\n        </DialogHeader>\n\n        <form onSubmit={handleSubmit(onSubmit)} className=\"p-6 space-y-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"amount\">المبلغ (دينار أردني) *</Label>\n              <Input\n                id=\"amount\"\n                type=\"number\"\n                step=\"0.01\"\n                min=\"0\"\n                {...register('amount', { valueAsNumber: true })}\n                className={errors.amount ? 'border-red-500' : ''}\n              />\n              {errors.amount && (\n                <p className=\"text-sm text-red-500\">{errors.amount.message}</p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"date\">التاريخ *</Label>\n              <Input\n                id=\"date\"\n                type=\"date\"\n                {...register('date')}\n                className={errors.date ? 'border-red-500' : ''}\n              />\n              {errors.date && (\n                <p className=\"text-sm text-red-500\">{errors.date.message}</p>\n              )}\n            </div>\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"source\">مصدر الإيراد *</Label>\n            <Input\n              id=\"source\"\n              {...register('source')}\n              placeholder=\"مثال: اشتراك شهري، تبرع، رسوم فعالية\"\n              className={errors.source ? 'border-red-500' : ''}\n            />\n            {errors.source && (\n              <p className=\"text-sm text-red-500\">{errors.source.message}</p>\n            )}\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"type\">نوع الإيراد</Label>\n            <Select\n              value={selectedType}\n              onValueChange={(value) => setValue('type', value as any)}\n            >\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"SUBSCRIPTION\">اشتراكات</SelectItem>\n                <SelectItem value=\"DONATION\">تبرعات</SelectItem>\n                <SelectItem value=\"EVENT\">فعاليات</SelectItem>\n                <SelectItem value=\"OTHER\">أخرى</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"description\">الوصف (اختياري)</Label>\n            <Input\n              id=\"description\"\n              {...register('description')}\n              placeholder=\"وصف إضافي للإيراد\"\n            />\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"notes\">ملاحظات (اختياري)</Label>\n            <Textarea\n              id=\"notes\"\n              {...register('notes')}\n              placeholder=\"أي ملاحظات إضافية\"\n              rows={3}\n            />\n          </div>\n\n          <div className=\"flex justify-end space-x-2 space-x-reverse pt-4 border-t\">\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={() => onOpenChange(false)}\n              disabled={loading}\n            >\n              إلغاء\n            </Button>\n            <Button\n              type=\"submit\"\n              disabled={loading}\n              className=\"bg-diwan-600 hover:bg-diwan-700\"\n            >\n              {loading ? 'جاري الحفظ...' : 'حفظ الإيراد'}\n            </Button>\n          </div>\n        </form>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAMA;AAOA;AAAA;;;AAvBA;;;;;;;;;;;;AAyBA,MAAM,qBAAqB,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,QAAQ,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC5B,MAAM,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,QAAQ,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,MAAM,oLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAgB;QAAY;QAAS;KAAQ,EAAE,OAAO,CAAC;IACrE,aAAa,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,OAAO,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC5B;AAgBe,SAAS,mBAAmB,EACzC,IAAI,EACJ,YAAY,EACZ,MAAM,EACN,SAAS,EACe;;IACxB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAqB;QAC7B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,QAAQ;YACR,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC5C,QAAQ;YACR,MAAM;YACN,aAAa;YACb,OAAO;QACT;IACF;IAEA,MAAM,eAAe,MAAM;IAE3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,QAAQ,QAAQ;gBAClB,MAAM;oBACJ,QAAQ;oBACR,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBAC5C,QAAQ;oBACR,MAAM;oBACN,aAAa;oBACb,OAAO;gBACT;YACF;QACF;uCAAG;QAAC;QAAM;QAAQ;KAAM;IAExB,MAAM,WAAW,OAAO;QACtB,IAAI,CAAC,QAAQ;QAEb,IAAI;YACF,WAAW;YAEX,iBAAiB;YACjB,MAAM,aAAa;gBACjB,GAAG,IAAI;gBACP,QAAQ,OAAO,KAAK,MAAM;gBAC1B,MAAM,IAAI,KAAK,KAAK,IAAI;gBACxB,UAAU,OAAO,EAAE;gBACnB,aAAa,KAAK,WAAW,EAAE,UAAU;gBACzC,OAAO,KAAK,KAAK,EAAE,UAAU;YAC/B;YAEA,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI;YACjC;YAEA,MAAM,CAAC,qBAAqB,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC;YACjD,aAAa;YACb;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,MAAM,OAAO,IAAI;QACzB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;oBAAC,WAAU;8BACtB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;0CAExB,6LAAC;;kDACC,6LAAC,qIAAA,CAAA,cAAW;wCAAC,WAAU;kDAAkC;;;;;;kDAGzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;gDAAE,WAAU;0DAA6B,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAM7D,6LAAC;oBAAK,UAAU,aAAa;oBAAW,WAAU;;sCAChD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAS;;;;;;sDACxB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,KAAI;4CACH,GAAG,SAAS,UAAU;gDAAE,eAAe;4CAAK,EAAE;4CAC/C,WAAW,OAAO,MAAM,GAAG,mBAAmB;;;;;;wCAE/C,OAAO,MAAM,kBACZ,6LAAC;4CAAE,WAAU;sDAAwB,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;8CAI9D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAO;;;;;;sDACtB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACJ,GAAG,SAAS,OAAO;4CACpB,WAAW,OAAO,IAAI,GAAG,mBAAmB;;;;;;wCAE7C,OAAO,IAAI,kBACV,6LAAC;4CAAE,WAAU;sDAAwB,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;;;;;;;sCAK9D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAS;;;;;;8CACxB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACF,GAAG,SAAS,SAAS;oCACtB,aAAY;oCACZ,WAAW,OAAO,MAAM,GAAG,mBAAmB;;;;;;gCAE/C,OAAO,MAAM,kBACZ,6LAAC;oCAAE,WAAU;8CAAwB,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;sCAI9D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAO;;;;;;8CACtB,6LAAC,qIAAA,CAAA,SAAM;oCACL,OAAO;oCACP,eAAe,CAAC,QAAU,SAAS,QAAQ;;sDAE3C,6LAAC,qIAAA,CAAA,gBAAa;sDACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sDAEd,6LAAC,qIAAA,CAAA,gBAAa;;8DACZ,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAe;;;;;;8DACjC,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAW;;;;;;8DAC7B,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAQ;;;;;;8DAC1B,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAQ;;;;;;;;;;;;;;;;;;;;;;;;sCAKhC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAc;;;;;;8CAC7B,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACF,GAAG,SAAS,cAAc;oCAC3B,aAAY;;;;;;;;;;;;sCAIhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAQ;;;;;;8CACvB,6LAAC,uIAAA,CAAA,WAAQ;oCACP,IAAG;oCACF,GAAG,SAAS,QAAQ;oCACrB,aAAY;oCACZ,MAAM;;;;;;;;;;;;sCAIV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS,IAAM,aAAa;oCAC5B,UAAU;8CACX;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3C;GAxNwB;;QAelB,iKAAA,CAAA,UAAO;;;KAfW", "debugId": null}}, {"offset": {"line": 4970, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/members/member-password-dialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  Di<PERSON>Header,\n  <PERSON><PERSON>Title,\n  DialogFooter,\n} from '@/components/ui/dialog'\nimport { Eye, EyeOff, Key, User, Lock, Save, X } from 'lucide-react'\n\ninterface Member {\n  id: string\n  name: string\n  email?: string\n  phone?: string\n}\n\ninterface MemberPasswordDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  member?: Member | null\n  onSuccess: () => void\n}\n\nexport default function MemberPasswordDialog({\n  open,\n  onOpenChange,\n  member,\n  onSuccess\n}: MemberPasswordDialogProps) {\n  const [password, setPassword] = useState('')\n  const [confirmPassword, setConfirmPassword] = useState('')\n  const [showPassword, setShowPassword] = useState(false)\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setError('')\n\n    if (!password) {\n      setError('كلمة المرور مطلوبة')\n      return\n    }\n\n    if (password.length < 6) {\n      setError('كلمة المرور يجب أن تكون على الأقل 6 أحرف')\n      return\n    }\n\n    if (password !== confirmPassword) {\n      setError('كلمة المرور وتأكيد كلمة المرور غير متطابقين')\n      return\n    }\n\n    if (!member) {\n      setError('لم يتم تحديد العضو')\n      return\n    }\n\n    try {\n      setLoading(true)\n\n      const response = await fetch(`/api/members/${member.id}/password`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ password }),\n      })\n\n      if (!response.ok) {\n        const error = await response.json()\n        throw new Error(error.message || 'فشل في تحديث كلمة المرور')\n      }\n\n      // إظهار رسالة نجاح\n      alert('تم تحديث كلمة مرور العضو بنجاح')\n      \n      // إعادة تعيين النموذج\n      setPassword('')\n      setConfirmPassword('')\n      setError('')\n      \n      // إغلاق النافذة وتحديث البيانات\n      onOpenChange(false)\n      onSuccess()\n    } catch (error: any) {\n      console.error('خطأ في تحديث كلمة المرور:', error)\n      setError(error.message || 'حدث خطأ أثناء تحديث كلمة المرور')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleClose = () => {\n    setPassword('')\n    setConfirmPassword('')\n    setError('')\n    setShowPassword(false)\n    setShowConfirmPassword(false)\n    onOpenChange(false)\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent className=\"max-w-[50vw]\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2 text-xl\">\n            <div className=\"w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center\">\n              <Key className=\"w-5 h-5 text-white\" />\n            </div>\n            <div>\n              <h3 className=\"font-bold\">إدارة كلمة المرور</h3>\n              <p className=\"text-sm text-gray-600 font-normal\">تعيين كلمة مرور للعضو</p>\n            </div>\n          </DialogTitle>\n        </DialogHeader>\n\n        {member && (\n          <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\">\n                <User className=\"w-5 h-5 text-blue-600\" />\n              </div>\n              <div>\n                <h4 className=\"font-semibold text-blue-900\">{member.name}</h4>\n                {member.email && (\n                  <p className=\"text-sm text-blue-700\">{member.email}</p>\n                )}\n                {member.phone && (\n                  <p className=\"text-sm text-blue-700\">{member.phone}</p>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm\">\n              {error}\n            </div>\n          )}\n\n          {/* كلمة المرور */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"password\" className=\"text-gray-700 font-medium\">\n              كلمة المرور الجديدة\n            </Label>\n            <div className=\"relative\">\n              <Lock className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n              <Input\n                id=\"password\"\n                type={showPassword ? 'text' : 'password'}\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n                placeholder=\"أدخل كلمة المرور الجديدة\"\n                required\n                disabled={loading}\n                className=\"pr-10 pl-10\"\n              />\n              <button\n                type=\"button\"\n                onClick={() => setShowPassword(!showPassword)}\n                className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\"\n                disabled={loading}\n              >\n                {showPassword ? <EyeOff className=\"w-5 h-5\" /> : <Eye className=\"w-5 h-5\" />}\n              </button>\n            </div>\n            <p className=\"text-xs text-gray-500\">\n              كلمة المرور يجب أن تكون على الأقل 6 أحرف\n            </p>\n          </div>\n\n          {/* تأكيد كلمة المرور */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"confirmPassword\" className=\"text-gray-700 font-medium\">\n              تأكيد كلمة المرور\n            </Label>\n            <div className=\"relative\">\n              <Lock className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n              <Input\n                id=\"confirmPassword\"\n                type={showConfirmPassword ? 'text' : 'password'}\n                value={confirmPassword}\n                onChange={(e) => setConfirmPassword(e.target.value)}\n                placeholder=\"أعد إدخال كلمة المرور\"\n                required\n                disabled={loading}\n                className=\"pr-10 pl-10\"\n              />\n              <button\n                type=\"button\"\n                onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\"\n                disabled={loading}\n              >\n                {showConfirmPassword ? <EyeOff className=\"w-5 h-5\" /> : <Eye className=\"w-5 h-5\" />}\n              </button>\n            </div>\n          </div>\n\n          <DialogFooter className=\"gap-3 pt-4\">\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={handleClose}\n              disabled={loading}\n              className=\"flex-1\"\n            >\n              <X className=\"w-4 h-4 ml-2\" />\n              إلغاء\n            </Button>\n            <Button\n              type=\"submit\"\n              disabled={loading || !password || !confirmPassword}\n              className=\"flex-1 bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 text-white\"\n            >\n              {loading ? (\n                <div className=\"flex items-center gap-2\">\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n                  جاري الحفظ...\n                </div>\n              ) : (\n                <div className=\"flex items-center gap-2\">\n                  <Save className=\"w-4 h-4\" />\n                  حفظ كلمة المرور\n                </div>\n              )}\n            </Button>\n          </DialogFooter>\n        </form>\n\n        {/* معلومات إضافية */}\n        <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-4\">\n          <p className=\"text-sm text-yellow-800\">\n            <strong>ملاحظة:</strong> بعد تعيين كلمة المرور، سيتمكن العضو من تسجيل الدخول باستخدام بريده الإلكتروني وكلمة المرور الجديدة لعرض كشف حسابه الشخصي ومشاهدة معرض الصور.\n          </p>\n        </div>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAbA;;;;;;;AA6Be,SAAS,qBAAqB,EAC3C,IAAI,EACJ,YAAY,EACZ,MAAM,EACN,SAAS,EACiB;;IAC1B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QAET,IAAI,CAAC,UAAU;YACb,SAAS;YACT;QACF;QAEA,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,SAAS;YACT;QACF;QAEA,IAAI,aAAa,iBAAiB;YAChC,SAAS;YACT;QACF;QAEA,IAAI,CAAC,QAAQ;YACX,SAAS;YACT;QACF;QAEA,IAAI;YACF,WAAW;YAEX,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,OAAO,EAAE,CAAC,SAAS,CAAC,EAAE;gBACjE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAS;YAClC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;YACnC;YAEA,mBAAmB;YACnB,MAAM;YAEN,sBAAsB;YACtB,YAAY;YACZ,mBAAmB;YACnB,SAAS;YAET,gCAAgC;YAChC,aAAa;YACb;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS,MAAM,OAAO,IAAI;QAC5B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,YAAY;QACZ,mBAAmB;QACnB,SAAS;QACT,gBAAgB;QAChB,uBAAuB;QACvB,aAAa;IACf;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAEjB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAY;;;;;;kDAC1B,6LAAC;wCAAE,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;;;;;;gBAKtD,wBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA+B,OAAO,IAAI;;;;;;oCACvD,OAAO,KAAK,kBACX,6LAAC;wCAAE,WAAU;kDAAyB,OAAO,KAAK;;;;;;oCAEnD,OAAO,KAAK,kBACX,6LAAC;wCAAE,WAAU;kDAAyB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;8BAO5D,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;wBACrC,uBACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;sCAKL,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAW,WAAU;8CAA4B;;;;;;8CAGhE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAM,eAAe,SAAS;4CAC9B,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,aAAY;4CACZ,QAAQ;4CACR,UAAU;4CACV,WAAU;;;;;;sDAEZ,6LAAC;4CACC,MAAK;4CACL,SAAS,IAAM,gBAAgB,CAAC;4CAChC,WAAU;4CACV,UAAU;sDAET,6BAAe,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;qEAAe,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGpE,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAMvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAkB,WAAU;8CAA4B;;;;;;8CAGvE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAM,sBAAsB,SAAS;4CACrC,OAAO;4CACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;4CAClD,aAAY;4CACZ,QAAQ;4CACR,UAAU;4CACV,WAAU;;;;;;sDAEZ,6LAAC;4CACC,MAAK;4CACL,SAAS,IAAM,uBAAuB,CAAC;4CACvC,WAAU;4CACV,UAAU;sDAET,oCAAsB,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;qEAAe,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAK7E,6LAAC,qIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;oCACV,WAAU;;sDAEV,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGhC,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAU,WAAW,CAAC,YAAY,CAAC;oCACnC,WAAU;8CAET,wBACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;4CAAkE;;;;;;6DAInF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;;;;;;8BAStC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;0CACX,6LAAC;0CAAO;;;;;;4BAAgB;;;;;;;;;;;;;;;;;;;;;;;AAMpC;GA7NwB;KAAA", "debugId": null}}, {"offset": {"line": 5471, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/members/advanced-search.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport {\n  Dialog,\n  DialogContent,\n  DialogHeader,\n  DialogTitle,\n  DialogFooter,\n  DialogClose,\n} from '@/components/ui/dialog'\nimport { Search, Filter, X } from 'lucide-react'\n\ninterface SearchFilters {\n  name: string\n  phone: string\n  address: string\n  status: string\n  minContributions: string\n  maxContributions: string\n  joinedAfter: string\n  joinedBefore: string\n}\n\ninterface AdvancedSearchProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  onSearch: (filters: SearchFilters) => void\n  onReset: () => void\n}\n\nexport default function AdvancedSearch({\n  open,\n  onOpenChange,\n  onSearch,\n  onReset,\n}: AdvancedSearchProps) {\n  const [filters, setFilters] = useState<SearchFilters>({\n    name: '',\n    phone: '',\n    address: '',\n    status: 'all',\n    minContributions: '',\n    maxContributions: '',\n    joinedAfter: '',\n    joinedBefore: '',\n  })\n\n  const handleInputChange = (field: keyof SearchFilters, value: string) => {\n    setFilters(prev => ({ ...prev, [field]: value }))\n  }\n\n  const handleSearch = () => {\n    onSearch(filters)\n    onOpenChange(false)\n  }\n\n  const handleReset = () => {\n    const resetFilters: SearchFilters = {\n      name: '',\n      phone: '',\n      address: '',\n      status: 'all',\n      minContributions: '',\n      maxContributions: '',\n      joinedAfter: '',\n      joinedBefore: '',\n    }\n    setFilters(resetFilters)\n    onReset()\n    onOpenChange(false)\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-[50vw]\">\n        <DialogClose onOpenChange={onOpenChange} />\n        <DialogHeader className=\"p-6 pb-0\">\n          <DialogTitle className=\"flex items-center space-x-2 space-x-reverse\">\n            <Filter className=\"w-5 h-5 text-diwan-600\" />\n            <span>البحث المتقدم</span>\n          </DialogTitle>\n        </DialogHeader>\n\n        <div className=\"p-6 pt-0 space-y-4\">\n          {/* البحث في البيانات الأساسية */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"name\">الاسم</Label>\n              <Input\n                id=\"name\"\n                value={filters.name}\n                onChange={(e) => handleInputChange('name', e.target.value)}\n                placeholder=\"البحث في الاسم...\"\n              />\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"phone\">رقم الهاتف</Label>\n              <Input\n                id=\"phone\"\n                value={filters.phone}\n                onChange={(e) => handleInputChange('phone', e.target.value)}\n                placeholder=\"البحث في رقم الهاتف...\"\n                dir=\"ltr\"\n              />\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"address\">العنوان</Label>\n              <Input\n                id=\"address\"\n                value={filters.address}\n                onChange={(e) => handleInputChange('address', e.target.value)}\n                placeholder=\"البحث في العنوان...\"\n              />\n            </div>\n          </div>\n\n          {/* حالة العضو */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"status\">حالة العضو</Label>\n            <select\n              id=\"status\"\n              value={filters.status}\n              onChange={(e) => handleInputChange('status', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-diwan-500\"\n            >\n              <option value=\"all\">جميع الأعضاء</option>\n              <option value=\"ACTIVE\">نشط</option>\n              <option value=\"LATE\">متأخر</option>\n              <option value=\"INACTIVE\">غير ملتزم</option>\n              <option value=\"SUSPENDED\">موقوف مؤقتاً</option>\n              <option value=\"ARCHIVED\">مؤرشف</option>\n            </select>\n          </div>\n\n          {/* نطاق المساهمات */}\n          <div className=\"space-y-2\">\n            <Label>نطاق المساهمات (بالدينار الأردني)</Label>\n            <div className=\"grid grid-cols-2 gap-2\">\n              <Input\n                value={filters.minContributions}\n                onChange={(e) => handleInputChange('minContributions', e.target.value)}\n                placeholder=\"الحد الأدنى\"\n                type=\"number\"\n                min=\"0\"\n                step=\"0.01\"\n                dir=\"ltr\"\n              />\n              <Input\n                value={filters.maxContributions}\n                onChange={(e) => handleInputChange('maxContributions', e.target.value)}\n                placeholder=\"الحد الأعلى\"\n                type=\"number\"\n                min=\"0\"\n                step=\"0.01\"\n                dir=\"ltr\"\n              />\n            </div>\n          </div>\n\n          {/* نطاق تاريخ الانضمام */}\n          <div className=\"space-y-2\">\n            <Label>نطاق تاريخ الانضمام</Label>\n            <div className=\"grid grid-cols-2 gap-2\">\n              <div>\n                <Label htmlFor=\"joinedAfter\" className=\"text-xs text-gray-500\">من تاريخ</Label>\n                <Input\n                  id=\"joinedAfter\"\n                  value={filters.joinedAfter}\n                  onChange={(e) => handleInputChange('joinedAfter', e.target.value)}\n                  type=\"date\"\n                  dir=\"ltr\"\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"joinedBefore\" className=\"text-xs text-gray-500\">إلى تاريخ</Label>\n                <Input\n                  id=\"joinedBefore\"\n                  value={filters.joinedBefore}\n                  onChange={(e) => handleInputChange('joinedBefore', e.target.value)}\n                  type=\"date\"\n                  dir=\"ltr\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <DialogFooter className=\"p-6 pt-0\">\n          <Button\n            type=\"button\"\n            variant=\"outline\"\n            onClick={handleReset}\n            className=\"text-red-600 border-red-600 hover:bg-red-50\"\n          >\n            <X className=\"w-4 h-4 ml-2\" />\n            إعادة تعيين\n          </Button>\n          <Button\n            type=\"button\"\n            onClick={handleSearch}\n            className=\"bg-diwan-600 hover:bg-diwan-700\"\n          >\n            <Search className=\"w-4 h-4 ml-2\" />\n            بحث\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAQA;AAAA;AAAA;;;AAdA;;;;;;;AAkCe,SAAS,eAAe,EACrC,IAAI,EACJ,YAAY,EACZ,QAAQ,EACR,OAAO,EACa;;IACpB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QACpD,MAAM;QACN,OAAO;QACP,SAAS;QACT,QAAQ;QACR,kBAAkB;QAClB,kBAAkB;QAClB,aAAa;QACb,cAAc;IAChB;IAEA,MAAM,oBAAoB,CAAC,OAA4B;QACrD,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;IACjD;IAEA,MAAM,eAAe;QACnB,SAAS;QACT,aAAa;IACf;IAEA,MAAM,cAAc;QAClB,MAAM,eAA8B;YAClC,MAAM;YACN,OAAO;YACP,SAAS;YACT,QAAQ;YACR,kBAAkB;YAClB,kBAAkB;YAClB,aAAa;YACb,cAAc;QAChB;QACA,WAAW;QACX;QACA,aAAa;IACf;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,cAAW;oBAAC,cAAc;;;;;;8BAC3B,6LAAC,qIAAA,CAAA,eAAY;oBAAC,WAAU;8BACtB,cAAA,6LAAC,qIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;8BAIV,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAO;;;;;;sDACtB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,QAAQ,IAAI;4CACnB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;4CACzD,aAAY;;;;;;;;;;;;8CAIhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,QAAQ,KAAK;4CACpB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;4CAC1D,aAAY;4CACZ,KAAI;;;;;;;;;;;;8CAIR,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;sDACzB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,QAAQ,OAAO;4CACtB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;4CAC5D,aAAY;;;;;;;;;;;;;;;;;;sCAMlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAS;;;;;;8CACxB,6LAAC;oCACC,IAAG;oCACH,OAAO,QAAQ,MAAM;oCACrB,UAAU,CAAC,IAAM,kBAAkB,UAAU,EAAE,MAAM,CAAC,KAAK;oCAC3D,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,6LAAC;4CAAO,OAAM;sDAAS;;;;;;sDACvB,6LAAC;4CAAO,OAAM;sDAAO;;;;;;sDACrB,6LAAC;4CAAO,OAAM;sDAAW;;;;;;sDACzB,6LAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,6LAAC;4CAAO,OAAM;sDAAW;;;;;;;;;;;;;;;;;;sCAK7B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;8CAAC;;;;;;8CACP,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CACJ,OAAO,QAAQ,gBAAgB;4CAC/B,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;4CACrE,aAAY;4CACZ,MAAK;4CACL,KAAI;4CACJ,MAAK;4CACL,KAAI;;;;;;sDAEN,6LAAC,oIAAA,CAAA,QAAK;4CACJ,OAAO,QAAQ,gBAAgB;4CAC/B,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;4CACrE,aAAY;4CACZ,MAAK;4CACL,KAAI;4CACJ,MAAK;4CACL,KAAI;;;;;;;;;;;;;;;;;;sCAMV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;8CAAC;;;;;;8CACP,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAc,WAAU;8DAAwB;;;;;;8DAC/D,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,QAAQ,WAAW;oDAC1B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;oDAChE,MAAK;oDACL,KAAI;;;;;;;;;;;;sDAGR,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAe,WAAU;8DAAwB;;;;;;8DAChE,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,QAAQ,YAAY;oDAC3B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;oDACjE,MAAK;oDACL,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOd,6LAAC,qIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,6LAAC,qIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,SAAS;4BACT,WAAU;;8CAEV,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAGhC,6LAAC,qIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAS;4BACT,WAAU;;8CAEV,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAO/C;GArLwB;KAAA", "debugId": null}}, {"offset": {"line": 5954, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/members/member-search-dialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport {\n  Dialog,\n  DialogContent,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport {\n  Search,\n  User,\n  Phone,\n  MapPin,\n  FileText,\n  X\n} from 'lucide-react'\nimport { Badge } from '@/components/ui/badge'\nimport { getMemberStatusText, getMemberStatusColor } from '@/lib/utils'\n\ninterface Member {\n  id: string\n  name: string\n  phone?: string\n  address?: string\n  status: string\n  _count: {\n    incomes: number\n  }\n}\n\ninterface MemberSearchDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  onSelectMember: (memberId: string) => void\n  title?: string\n  description?: string\n}\n\nexport default function MemberSearchDialog({\n  open,\n  onOpenChange,\n  onSelectMember,\n  title = \"اختيار عضو\",\n  description = \"ابحث عن العضو المطلوب\"\n}: MemberSearchDialogProps) {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [members, setMembers] = useState<Member[]>([])\n  const [loading, setLoading] = useState(false)\n  const [selectedMember, setSelectedMember] = useState<Member | null>(null)\n\n  // جلب الأعضاء عند البحث\n  useEffect(() => {\n    if (open) {\n      fetchMembers()\n    } else {\n      // إعادة تعيين البيانات عند إغلاق النافذة\n      setSearchTerm('')\n      setMembers([])\n      setSelectedMember(null)\n    }\n  }, [open, searchTerm])\n\n  const fetchMembers = async () => {\n    setLoading(true)\n    try {\n      const response = await fetch(\n        `/api/members?search=${encodeURIComponent(searchTerm)}&limit=20&includeIncomes=true`\n      )\n      if (response.ok) {\n        const data = await response.json()\n        setMembers(data.members || [])\n      }\n    } catch (error) {\n      console.error('خطأ في جلب الأعضاء:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleSelectMember = (member: Member) => {\n    setSelectedMember(member)\n  }\n\n  const handleConfirmSelection = () => {\n    if (selectedMember) {\n      onSelectMember(selectedMember.id)\n      onOpenChange(false)\n    }\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-[50vw] max-h-[80vh] overflow-hidden flex flex-col\">\n        <DialogHeader className=\"pb-4 border-b\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"p-2 bg-purple-100 rounded-lg\">\n                <Search className=\"w-5 h-5 text-purple-600\" />\n              </div>\n              <div>\n                <DialogTitle className=\"text-lg font-bold text-gray-900\">\n                  {title}\n                </DialogTitle>\n                <p className=\"text-sm text-gray-600 mt-1\">\n                  {description}\n                </p>\n              </div>\n            </div>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => onOpenChange(false)}\n              className=\"text-gray-500 hover:text-gray-700\"\n            >\n              <X className=\"w-4 h-4\" />\n            </Button>\n          </div>\n        </DialogHeader>\n\n        <div className=\"flex-1 overflow-hidden flex flex-col\">\n          {/* شريط البحث */}\n          <div className=\"p-4 border-b bg-gray-50\">\n            <div className=\"relative\">\n              <Search className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n              <Input\n                placeholder=\"ابحث بالاسم أو الهاتف أو العنوان...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"pr-10\"\n                autoFocus\n              />\n            </div>\n          </div>\n\n          {/* قائمة الأعضاء */}\n          <div className=\"flex-1 overflow-y-auto p-4\">\n            {loading ? (\n              <div className=\"flex justify-center items-center h-32\">\n                <div className=\"text-gray-500\">جاري البحث...</div>\n              </div>\n            ) : members.length === 0 ? (\n              <div className=\"flex flex-col justify-center items-center h-32 text-gray-500\">\n                <User className=\"w-12 h-12 mb-2 text-gray-300\" />\n                <p>\n                  {searchTerm ? 'لا توجد نتائج للبحث' : 'ابدأ بكتابة اسم العضو للبحث'}\n                </p>\n              </div>\n            ) : (\n              <div className=\"space-y-2\">\n                {members.map((member) => (\n                  <div\n                    key={member.id}\n                    onClick={() => handleSelectMember(member)}\n                    className={`p-3 border rounded-lg cursor-pointer transition-all hover:shadow-md ${\n                      selectedMember?.id === member.id\n                        ? 'border-purple-500 bg-purple-50'\n                        : 'border-gray-200 hover:border-gray-300'\n                    }`}\n                  >\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center gap-2 mb-1\">\n                          <h3 className=\"font-medium text-gray-900\">{member.name}</h3>\n                          <Badge className={getMemberStatusColor(member.status)}>\n                            {getMemberStatusText(member.status)}\n                          </Badge>\n                        </div>\n                        <div className=\"flex items-center gap-4 text-sm text-gray-600\">\n                          {member.phone && (\n                            <div className=\"flex items-center gap-1\">\n                              <Phone className=\"w-3 h-3\" />\n                              {member.phone}\n                            </div>\n                          )}\n                          {member.address && (\n                            <div className=\"flex items-center gap-1\">\n                              <MapPin className=\"w-3 h-3\" />\n                              {member.address}\n                            </div>\n                          )}\n                        </div>\n                      </div>\n                      <div className=\"text-sm text-gray-500\">\n                        {member._count.incomes} مساهمة\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n\n          {/* أزرار التحكم */}\n          <div className=\"p-4 border-t bg-gray-50 flex justify-end gap-2\">\n            <Button\n              variant=\"outline\"\n              onClick={() => onOpenChange(false)}\n            >\n              إلغاء\n            </Button>\n            <Button\n              onClick={handleConfirmSelection}\n              disabled={!selectedMember}\n              className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n            >\n              <FileText className=\"w-4 h-4 ml-2\" />\n              عرض كشف الحساب\n            </Button>\n          </div>\n        </div>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;;;AApBA;;;;;;;;AAyCe,SAAS,mBAAmB,EACzC,IAAI,EACJ,YAAY,EACZ,cAAc,EACd,QAAQ,YAAY,EACpB,cAAc,uBAAuB,EACb;;IACxB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,wBAAwB;IACxB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,MAAM;gBACR;YACF,OAAO;gBACL,yCAAyC;gBACzC,cAAc;gBACd,WAAW,EAAE;gBACb,kBAAkB;YACpB;QACF;uCAAG;QAAC;QAAM;KAAW;IAErB,MAAM,eAAe;QACnB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MACrB,CAAC,oBAAoB,EAAE,mBAAmB,YAAY,6BAA6B,CAAC;YAEtF,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,WAAW,KAAK,OAAO,IAAI,EAAE;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;QACvC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB;IACpB;IAEA,MAAM,yBAAyB;QAC7B,IAAI,gBAAgB;YAClB,eAAe,eAAe,EAAE;YAChC,aAAa;QACf;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;oBAAC,WAAU;8BACtB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,6LAAC;;0DACC,6LAAC,qIAAA,CAAA,cAAW;gDAAC,WAAU;0DACpB;;;;;;0DAEH,6LAAC;gDAAE,WAAU;0DACV;;;;;;;;;;;;;;;;;;0CAIP,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,aAAa;gCAC5B,WAAU;0CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8BAKnB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC,oIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;wCACV,SAAS;;;;;;;;;;;;;;;;;sCAMf,6LAAC;4BAAI,WAAU;sCACZ,wBACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;uCAE/B,QAAQ,MAAM,KAAK,kBACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;kDACE,aAAa,wBAAwB;;;;;;;;;;;qDAI1C,6LAAC;gCAAI,WAAU;0CACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;wCAEC,SAAS,IAAM,mBAAmB;wCAClC,WAAW,CAAC,oEAAoE,EAC9E,gBAAgB,OAAO,OAAO,EAAE,GAC5B,mCACA,yCACJ;kDAEF,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAA6B,OAAO,IAAI;;;;;;8EACtD,6LAAC,oIAAA,CAAA,QAAK;oEAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO,MAAM;8EACjD,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;;;;;;;;;;;;sEAGtC,6LAAC;4DAAI,WAAU;;gEACZ,OAAO,KAAK,kBACX,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAChB,OAAO,KAAK;;;;;;;gEAGhB,OAAO,OAAO,kBACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,6MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEACjB,OAAO,OAAO;;;;;;;;;;;;;;;;;;;8DAKvB,6LAAC;oDAAI,WAAU;;wDACZ,OAAO,MAAM,CAAC,OAAO;wDAAC;;;;;;;;;;;;;uCAhCtB,OAAO,EAAE;;;;;;;;;;;;;;;sCA0CxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,aAAa;8CAC7B;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,CAAC;oCACX,WAAU;;sDAEV,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnD;GA/KwB;KAAA", "debugId": null}}, {"offset": {"line": 6371, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/native-select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { ChevronDown } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface NativeSelectOption {\n  value: string\n  label: string\n  disabled?: boolean\n}\n\ninterface NativeSelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'size'> {\n  options?: NativeSelectOption[]\n  children?: React.ReactNode\n  placeholder?: string\n  size?: 'sm' | 'md' | 'lg'\n  error?: boolean\n  helperText?: string\n}\n\nexport const NativeSelect = React.forwardRef<HTMLSelectElement, NativeSelectProps>(\n  ({ \n    className, \n    options, \n    children, \n    placeholder, \n    size = 'md', \n    error = false, \n    helperText,\n    ...props \n  }, ref) => {\n    const sizeClasses = {\n      sm: 'h-9 px-3 text-sm',\n      md: 'h-11 px-4 text-base',\n      lg: 'h-13 px-5 text-lg'\n    }\n\n    return (\n      <div className=\"relative w-full\">\n        <div className=\"relative\">\n          <select\n            ref={ref}\n            className={cn(\n              \"w-full appearance-none rounded-xl border-2 bg-white font-medium transition-all duration-200 focus:outline-none focus:ring-4 disabled:cursor-not-allowed disabled:opacity-50\",\n              sizeClasses[size],\n              error\n                ? \"border-danger-300 text-danger-700 focus:border-danger-500 focus:ring-danger-500/20\"\n                : \"border-secondary-200 text-secondary-700 hover:border-secondary-300 focus:border-primary-500 focus:ring-primary-500/20\",\n              className\n            )}\n            {...props}\n          >\n            {placeholder && (\n              <option value=\"\" disabled>\n                {placeholder}\n              </option>\n            )}\n            {options ? (\n              options.map((option) => (\n                <option \n                  key={option.value} \n                  value={option.value} \n                  disabled={option.disabled}\n                >\n                  {option.label}\n                </option>\n              ))\n            ) : (\n              children\n            )}\n          </select>\n          \n          <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\n            <ChevronDown className=\"h-5 w-5 text-secondary-400\" />\n          </div>\n        </div>\n\n        {helperText && (\n          <p className={cn(\n            \"mt-1 text-sm\",\n            error ? \"text-danger-600\" : \"text-secondary-500\"\n          )}>\n            {helperText}\n          </p>\n        )}\n      </div>\n    )\n  }\n)\n\nNativeSelect.displayName = \"NativeSelect\"\n\n// مكون مبسط للاستخدام السريع\ninterface SimpleNativeSelectProps extends Omit<NativeSelectProps, 'options'> {\n  options: string[] | NativeSelectOption[]\n}\n\nexport function SimpleNativeSelect({ options, ...props }: SimpleNativeSelectProps) {\n  const selectOptions: NativeSelectOption[] = options.map(option => \n    typeof option === 'string' \n      ? { value: option, label: option }\n      : option\n  )\n\n  return <NativeSelect {...props} options={selectOptions} />\n}\n\n// مكون للحالات (Status Select)\ninterface StatusSelectProps extends Omit<NativeSelectProps, 'options'> {\n  includeAll?: boolean\n  allLabel?: string\n}\n\nexport function StatusSelect({ includeAll = true, allLabel = \"جميع الحالات\", ...props }: StatusSelectProps) {\n  const statusOptions: NativeSelectOption[] = [\n    ...(includeAll ? [{ value: \"all\", label: allLabel }] : []),\n    { value: \"ACTIVE\", label: \"نشط\" },\n    { value: \"LATE\", label: \"متأخر\" },\n    { value: \"INACTIVE\", label: \"غير ملتزم\" },\n    { value: \"SUSPENDED\", label: \"موقوف مؤقتاً\" },\n    { value: \"ARCHIVED\", label: \"مؤرشف\" }\n  ]\n\n  return <NativeSelect {...props} options={statusOptions} />\n}\n\n// مكون للأشهر\nexport function MonthSelect(props: Omit<NativeSelectProps, 'options'>) {\n  const monthOptions: NativeSelectOption[] = [\n    { value: \"1\", label: \"يناير\" },\n    { value: \"2\", label: \"فبراير\" },\n    { value: \"3\", label: \"مارس\" },\n    { value: \"4\", label: \"أبريل\" },\n    { value: \"5\", label: \"مايو\" },\n    { value: \"6\", label: \"يونيو\" },\n    { value: \"7\", label: \"يوليو\" },\n    { value: \"8\", label: \"أغسطس\" },\n    { value: \"9\", label: \"سبتمبر\" },\n    { value: \"10\", label: \"أكتوبر\" },\n    { value: \"11\", label: \"نوفمبر\" },\n    { value: \"12\", label: \"ديسمبر\" }\n  ]\n\n  return <NativeSelect {...props} options={monthOptions} />\n}\n\n// مكون للسنوات\ninterface YearSelectProps extends Omit<NativeSelectProps, 'options'> {\n  startYear?: number\n  endYear?: number\n}\n\nexport function YearSelect({ startYear = 2020, endYear = new Date().getFullYear() + 5, ...props }: YearSelectProps) {\n  const yearOptions: NativeSelectOption[] = []\n  \n  for (let year = endYear; year >= startYear; year--) {\n    yearOptions.push({ value: year.toString(), label: year.toString() })\n  }\n\n  return <NativeSelect {...props} options={yearOptions} />\n}\n\n// مكون لأنواع الإيرادات\nexport function IncomeTypeSelect(props: Omit<NativeSelectProps, 'options'>) {\n  const incomeTypeOptions: NativeSelectOption[] = [\n    { value: \"MONTHLY_CONTRIBUTION\", label: \"اشتراك شهري\" },\n    { value: \"ANNUAL_CONTRIBUTION\", label: \"اشتراك سنوي\" },\n    { value: \"DONATION\", label: \"تبرع\" },\n    { value: \"EVENT_REVENUE\", label: \"إيرادات فعالية\" },\n    { value: \"OTHER\", label: \"أخرى\" }\n  ]\n\n  return <NativeSelect {...props} options={incomeTypeOptions} />\n}\n\n// مكون لأنواع المصروفات\nexport function ExpenseTypeSelect(props: Omit<NativeSelectProps, 'options'>) {\n  const expenseTypeOptions: NativeSelectOption[] = [\n    { value: \"OPERATIONAL\", label: \"تشغيلية\" },\n    { value: \"EVENT\", label: \"فعالية\" },\n    { value: \"MAINTENANCE\", label: \"صيانة\" },\n    { value: \"CHARITY\", label: \"خيرية\" },\n    { value: \"ADMINISTRATIVE\", label: \"إدارية\" },\n    { value: \"OTHER\", label: \"أخرى\" }\n  ]\n\n  return <NativeSelect {...props} options={expenseTypeOptions} />\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAqBO,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EACzC,CAAC,EACC,SAAS,EACT,OAAO,EACP,QAAQ,EACR,WAAW,EACX,OAAO,IAAI,EACX,QAAQ,KAAK,EACb,UAAU,EACV,GAAG,OACJ,EAAE;IACD,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,KAAK;wBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+KACA,WAAW,CAAC,KAAK,EACjB,QACI,uFACA,yHACJ;wBAED,GAAG,KAAK;;4BAER,6BACC,6LAAC;gCAAO,OAAM;gCAAG,QAAQ;0CACtB;;;;;;4BAGJ,UACC,QAAQ,GAAG,CAAC,CAAC,uBACX,6LAAC;oCAEC,OAAO,OAAO,KAAK;oCACnB,UAAU,OAAO,QAAQ;8CAExB,OAAO,KAAK;mCAJR,OAAO,KAAK;;;;4CAQrB;;;;;;;kCAIJ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;YAI1B,4BACC,6LAAC;gBAAE,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACb,gBACA,QAAQ,oBAAoB;0BAE3B;;;;;;;;;;;;AAKX;KAnEW;AAsEb,aAAa,WAAW,GAAG;AAOpB,SAAS,mBAAmB,EAAE,OAAO,EAAE,GAAG,OAAgC;IAC/E,MAAM,gBAAsC,QAAQ,GAAG,CAAC,CAAA,SACtD,OAAO,WAAW,WACd;YAAE,OAAO;YAAQ,OAAO;QAAO,IAC/B;IAGN,qBAAO,6LAAC;QAAc,GAAG,KAAK;QAAE,SAAS;;;;;;AAC3C;MARgB;AAgBT,SAAS,aAAa,EAAE,aAAa,IAAI,EAAE,WAAW,cAAc,EAAE,GAAG,OAA0B;IACxG,MAAM,gBAAsC;WACtC,aAAa;YAAC;gBAAE,OAAO;gBAAO,OAAO;YAAS;SAAE,GAAG,EAAE;QACzD;YAAE,OAAO;YAAU,OAAO;QAAM;QAChC;YAAE,OAAO;YAAQ,OAAO;QAAQ;QAChC;YAAE,OAAO;YAAY,OAAO;QAAY;QACxC;YAAE,OAAO;YAAa,OAAO;QAAe;QAC5C;YAAE,OAAO;YAAY,OAAO;QAAQ;KACrC;IAED,qBAAO,6LAAC;QAAc,GAAG,KAAK;QAAE,SAAS;;;;;;AAC3C;MAXgB;AAcT,SAAS,YAAY,KAAyC;IACnE,MAAM,eAAqC;QACzC;YAAE,OAAO;YAAK,OAAO;QAAQ;QAC7B;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAQ;QAC7B;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAQ;QAC7B;YAAE,OAAO;YAAK,OAAO;QAAQ;QAC7B;YAAE,OAAO;YAAK,OAAO;QAAQ;QAC7B;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAM,OAAO;QAAS;QAC/B;YAAE,OAAO;YAAM,OAAO;QAAS;QAC/B;YAAE,OAAO;YAAM,OAAO;QAAS;KAChC;IAED,qBAAO,6LAAC;QAAc,GAAG,KAAK;QAAE,SAAS;;;;;;AAC3C;MAjBgB;AAyBT,SAAS,WAAW,EAAE,YAAY,IAAI,EAAE,UAAU,IAAI,OAAO,WAAW,KAAK,CAAC,EAAE,GAAG,OAAwB;IAChH,MAAM,cAAoC,EAAE;IAE5C,IAAK,IAAI,OAAO,SAAS,QAAQ,WAAW,OAAQ;QAClD,YAAY,IAAI,CAAC;YAAE,OAAO,KAAK,QAAQ;YAAI,OAAO,KAAK,QAAQ;QAAG;IACpE;IAEA,qBAAO,6LAAC;QAAc,GAAG,KAAK;QAAE,SAAS;;;;;;AAC3C;MARgB;AAWT,SAAS,iBAAiB,KAAyC;IACxE,MAAM,oBAA0C;QAC9C;YAAE,OAAO;YAAwB,OAAO;QAAc;QACtD;YAAE,OAAO;YAAuB,OAAO;QAAc;QACrD;YAAE,OAAO;YAAY,OAAO;QAAO;QACnC;YAAE,OAAO;YAAiB,OAAO;QAAiB;QAClD;YAAE,OAAO;YAAS,OAAO;QAAO;KACjC;IAED,qBAAO,6LAAC;QAAc,GAAG,KAAK;QAAE,SAAS;;;;;;AAC3C;MAVgB;AAaT,SAAS,kBAAkB,KAAyC;IACzE,MAAM,qBAA2C;QAC/C;YAAE,OAAO;YAAe,OAAO;QAAU;QACzC;YAAE,OAAO;YAAS,OAAO;QAAS;QAClC;YAAE,OAAO;YAAe,OAAO;QAAQ;QACvC;YAAE,OAAO;YAAW,OAAO;QAAQ;QACnC;YAAE,OAAO;YAAkB,OAAO;QAAS;QAC3C;YAAE,OAAO;YAAS,OAAO;QAAO;KACjC;IAED,qBAAO,6LAAC;QAAc,GAAG,KAAK;QAAE,SAAS;;;;;;AAC3C;MAXgB", "debugId": null}}, {"offset": {"line": 6687, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/app/dashboard/members/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useSession } from 'next-auth/react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\n\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from '@/components/ui/table'\nimport {\n  Plus,\n  Search,\n  Edit,\n  Trash2,\n  Eye,\n  Users,\n  UserCheck,\n  UserX,\n  Phone,\n  Filter,\n  DollarSign,\n  FileText,\n  FileDown,\n  Key,\n  Download,\n  MapPin\n} from 'lucide-react'\nimport MemberDialog from '@/components/members/member-dialog'\nimport MemberDetailsDialog from '@/components/members/member-details-dialog'\nimport AccountStatementDialog from '@/components/members/account-statement-dialog'\nimport MemberIncomeDialog from '@/components/members/member-income-dialog'\nimport MemberPasswordDialog from '@/components/members/member-password-dialog'\nimport AdvancedSearch from '@/components/members/advanced-search'\nimport MemberSearchDialog from '@/components/members/member-search-dialog'\nimport { formatDate, formatCurrency, getMemberStatusText, getMemberStatusColor } from '@/lib/utils'\nimport { StatusSelect } from '@/components/ui/native-select'\nimport jsPDF from 'jspdf'\nimport html2canvas from 'html2canvas'\n\ninterface Member {\n  id: string\n  name: string\n  phone?: string\n  address?: string\n  notes?: string\n  status: string\n  createdAt: string\n  _count: {\n    incomes: number\n  }\n  incomes?: {\n    amount: number\n  }[]\n  totalContributions?: number\n}\n\ninterface MembersResponse {\n  members: Member[]\n  pagination: {\n    page: number\n    limit: number\n    total: number\n    pages: number\n  }\n}\n\nexport default function MembersPage() {\n  const { data: session } = useSession()\n  const [members, setMembers] = useState<Member[]>([])\n  const [loading, setLoading] = useState(true)\n  const [search, setSearch] = useState('')\n  const [status, setStatus] = useState('all')\n  const [pagination, setPagination] = useState({\n    page: 1,\n    limit: 10,\n    total: 0,\n    pages: 0,\n  })\n  const [isDialogOpen, setIsDialogOpen] = useState(false)\n  const [selectedMember, setSelectedMember] = useState<Member | null>(null)\n  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false)\n  const [selectedMemberForDetails, setSelectedMemberForDetails] = useState<Member | null>(null)\n  const [isAdvancedSearchOpen, setIsAdvancedSearchOpen] = useState(false)\n  const [isAccountStatementOpen, setIsAccountStatementOpen] = useState(false)\n  const [selectedMemberForStatement, setSelectedMemberForStatement] = useState<string | null>(null)\n  const [isMemberSearchOpen, setIsMemberSearchOpen] = useState(false)\n  const [isMemberIncomeOpen, setIsMemberIncomeOpen] = useState(false)\n  const [selectedMemberForPassword, setSelectedMemberForPassword] = useState<Member | null>(null)\n  const [isPasswordDialogOpen, setIsPasswordDialogOpen] = useState(false)\n  const [selectedMemberForIncome, setSelectedMemberForIncome] = useState<Member | null>(null)\n  const [stats, setStats] = useState({\n    total: 0,\n    active: 0,\n    late: 0,\n    inactive: 0,\n    suspended: 0,\n    archived: 0,\n  })\n\n  // جلب الأعضاء\n  const fetchMembers = async () => {\n    try {\n      setLoading(true)\n      const params = new URLSearchParams({\n        search,\n        status,\n        page: pagination.page.toString(),\n        limit: pagination.limit.toString(),\n      })\n\n      const response = await fetch(`/api/members?${params}`)\n      if (!response.ok) throw new Error('فشل في جلب الأعضاء')\n\n      const data: MembersResponse = await response.json()\n      setMembers(data.members)\n      setPagination(data.pagination)\n    } catch (error) {\n      console.error('خطأ في جلب الأعضاء:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // جلب الإحصائيات\n  const fetchStats = async () => {\n    try {\n      const response = await fetch('/api/members?limit=1000')\n      if (!response.ok) return\n\n      const data: MembersResponse = await response.json()\n      const total = data.members.length\n      const active = data.members.filter(m => m.status === 'ACTIVE').length\n      const late = data.members.filter(m => m.status === 'LATE').length\n      const inactive = data.members.filter(m => m.status === 'INACTIVE').length\n      const suspended = data.members.filter(m => m.status === 'SUSPENDED').length\n      const archived = data.members.filter(m => m.status === 'ARCHIVED').length\n\n      setStats({ total, active, late, inactive, suspended, archived })\n    } catch (error) {\n      console.error('خطأ في جلب الإحصائيات:', error)\n    }\n  }\n\n  useEffect(() => {\n    fetchMembers()\n  }, [search, status, pagination.page])\n\n  useEffect(() => {\n    fetchStats()\n  }, [])\n\n  // فتح نافذة إضافة عضو جديد\n  const handleAddMember = () => {\n    setSelectedMember(null)\n    setIsDialogOpen(true)\n  }\n\n  // فتح نافذة تعديل عضو\n  const handleEditMember = (member: Member) => {\n    setSelectedMember(member)\n    setIsDialogOpen(true)\n  }\n\n  // فتح نافذة تفاصيل العضو\n  const handleViewMember = (member: Member) => {\n    setSelectedMemberForDetails(member)\n    setIsDetailsDialogOpen(true)\n  }\n\n  // فتح كشف حساب العضو\n  const handleViewAccountStatement = (memberId: string) => {\n    setSelectedMemberForStatement(memberId)\n    setIsAccountStatementOpen(true)\n  }\n\n  // فتح نافذة البحث عن عضو لكشف الحساب\n  const handleOpenMemberSearch = () => {\n    setIsMemberSearchOpen(true)\n  }\n\n  // فتح نافذة إدارة كلمة مرور العضو\n  const handleManagePassword = (member: Member) => {\n    setSelectedMemberForPassword(member)\n    setIsPasswordDialogOpen(true)\n  }\n\n  // اختيار عضو من نافذة البحث لكشف الحساب\n  const handleSelectMemberForStatement = (memberId: string) => {\n    setSelectedMemberForStatement(memberId)\n    setIsMemberSearchOpen(false)\n    setIsAccountStatementOpen(true)\n  }\n\n  // فتح نافذة إضافة إيراد للعضو\n  const handleAddMemberIncome = (member: Member) => {\n    setSelectedMemberForIncome(member)\n    setIsMemberIncomeOpen(true)\n  }\n\n  // حذف عضو\n  const handleDeleteMember = async (id: string) => {\n    if (!confirm('هل أنت متأكد من حذف هذا العضو؟')) return\n\n    try {\n      const response = await fetch(`/api/members/${id}`, {\n        method: 'DELETE',\n      })\n\n      if (!response.ok) {\n        const error = await response.json()\n        alert(error.error || 'فشل في حذف العضو')\n        return\n      }\n\n      fetchMembers()\n      fetchStats()\n    } catch (error) {\n      console.error('خطأ في حذف العضو:', error)\n      alert('حدث خطأ في حذف العضو')\n    }\n  }\n\n  // عند نجاح العملية\n  const handleSuccess = () => {\n    setIsDialogOpen(false)\n    fetchMembers()\n    fetchStats()\n  }\n\n  // تصدير البيانات CSV\n  const handleExportMembers = () => {\n    const csvContent = [\n      ['الاسم', 'الهاتف', 'العنوان', 'الحالة', 'إجمالي الإيرادات', 'تاريخ الإضافة'],\n      ...members.map(member => [\n        member.name,\n        member.phone || '',\n        member.address || '',\n        getMemberStatusText(member.status),\n        (member.incomes?.reduce((total, income) => total + income.amount, 0) || 0).toString(),\n        formatDate(member.createdAt)\n      ])\n    ].map(row => row.join(',')).join('\\n')\n\n    const blob = new Blob(['\\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })\n    const link = document.createElement('a')\n    link.href = URL.createObjectURL(blob)\n    link.download = `اعضاء_الديوان_${new Date().toISOString().split('T')[0]}.csv`\n    link.click()\n  }\n\n  // تصدير البيانات PDF\n  // const handleExportMembersPDF = () => {\n    const doc = new jsPDF('p', 'mm', 'a4')\n\n    // إعداد الخط\n    doc.setFont('helvetica')\n    doc.setFontSize(18)\n\n    // العنوان باللغة الإنجليزية (لضمان الوضوح)\n    const title = 'Diwan Abu Alosh - Members Report'\n    const pageWidth = doc.internal.pageSize.getWidth()\n    const titleWidth = doc.getTextWidth(title)\n    doc.text(title, (pageWidth - titleWidth) / 2, 25)\n\n    // العنوان الفرعي\n    doc.setFontSize(14)\n    const subtitle = 'Members List Report'\n    const subtitleWidth = doc.getTextWidth(subtitle)\n    doc.text(subtitle, (pageWidth - subtitleWidth) / 2, 35)\n\n    // خط تحت العنوان\n    doc.setDrawColor(0, 0, 0)\n    doc.line(30, 40, pageWidth - 30, 40)\n\n    // معلومات التقرير\n    doc.setFontSize(12)\n    const currentDate = new Date().toLocaleDateString('en-GB')\n    doc.text(`Report Date: ${currentDate}`, 20, 55)\n\n    // الإحصائيات في صندوق\n    doc.setFillColor(245, 245, 245)\n    doc.rect(20, 65, pageWidth - 40, 40, 'F')\n    doc.setDrawColor(200, 200, 200)\n    doc.rect(20, 65, pageWidth - 40, 40)\n\n    doc.setFontSize(11)\n    doc.setFont('helvetica', 'bold')\n    doc.text('Members Statistics:', 25, 75)\n\n    doc.setFont('helvetica', 'normal')\n    doc.setFontSize(10)\n    doc.text(`Total Members: ${stats.total}`, 25, 82)\n    doc.text(`Active Members: ${stats.active}`, 25, 88)\n    doc.text(`Late Members: ${stats.late}`, 100, 82)\n    doc.text(`Inactive Members: ${stats.inactive}`, 100, 88)\n    doc.text(`Suspended Members: ${stats.suspended}`, 25, 94)\n    doc.text(`Archived Members: ${stats.archived}`, 100, 94)\n\n    // رأس الجدول\n    let yPosition = 120\n    doc.setFontSize(10)\n    doc.setFont('helvetica', 'bold')\n\n    // رسم مستطيل للرأس\n    doc.setFillColor(70, 130, 180)\n    doc.rect(15, yPosition - 6, 180, 10, 'F')\n    doc.setTextColor(255, 255, 255)\n\n    const headers = ['Member Name', 'Phone Number', 'Status', 'Total Income (JOD)', 'Join Date']\n    const columnPositions = [20, 65, 100, 130, 165]\n\n    headers.forEach((header, index) => {\n      doc.text(header, columnPositions[index], yPosition)\n    })\n\n    // خط تحت الرأس\n    doc.setTextColor(0, 0, 0)\n    doc.setDrawColor(0, 0, 0)\n    doc.line(15, yPosition + 4, 195, yPosition + 4)\n\n    // بيانات الأعضاء\n    yPosition += 15\n    doc.setFont('helvetica', 'normal')\n    doc.setFontSize(9)\n\n    members.forEach((member, index) => {\n      if (yPosition > 270) { // صفحة جديدة\n        doc.addPage()\n        yPosition = 30\n\n        // إعادة رسم رأس الجدول في الصفحة الجديدة\n        doc.setFont('helvetica', 'bold')\n        doc.setFontSize(10)\n        doc.setFillColor(70, 130, 180)\n        doc.rect(15, yPosition - 6, 180, 10, 'F')\n        doc.setTextColor(255, 255, 255)\n\n        headers.forEach((header, index) => {\n          doc.text(header, columnPositions[index], yPosition)\n        })\n\n        doc.setTextColor(0, 0, 0)\n        doc.line(15, yPosition + 4, 195, yPosition + 4)\n        yPosition += 15\n        doc.setFont('helvetica', 'normal')\n        doc.setFontSize(9)\n      }\n\n      // تلوين الصفوف بالتناوب\n      if (index % 2 === 0) {\n        doc.setFillColor(248, 249, 250)\n        doc.rect(15, yPosition - 5, 180, 9, 'F')\n      }\n\n      // إعداد البيانات - استخدام النصوص الإنجليزية لضمان الوضوح\n      const memberName = member.name.length > 20 ? member.name.substring(0, 20) + '...' : member.name\n      const memberPhone = member.phone || 'Not Specified'\n\n      // تحويل حالة العضو للإنجليزية\n      const getStatusInEnglish = (status: string) => {\n        switch (status) {\n          case 'ACTIVE': return 'Active'\n          case 'LATE': return 'Late'\n          case 'INACTIVE': return 'Inactive'\n          case 'SUSPENDED': return 'Suspended'\n          case 'ARCHIVED': return 'Archived'\n          default: return status\n        }\n      }\n\n      const memberStatus = getStatusInEnglish(member.status)\n      const memberIncome = formatCurrency(member.incomes?.reduce((total, income) => total + income.amount, 0) || 0)\n      const memberJoinDate = formatDate(member.createdAt)\n\n      const rowData = [memberName, memberPhone, memberStatus, memberIncome, memberJoinDate]\n\n      rowData.forEach((data, colIndex) => {\n        doc.text(data.toString(), columnPositions[colIndex], yPosition)\n      })\n\n      // خط فاصل خفيف\n      doc.setDrawColor(230, 230, 230)\n      doc.line(15, yPosition + 3, 195, yPosition + 3)\n\n      yPosition += 9\n    })\n\n    // إضافة تذييل\n    const pageCount = (doc.internal as { getNumberOfPages: () => number }).getNumberOfPages()\n    for (let i = 1; i <= pageCount; i++) {\n      doc.setPage(i)\n      doc.setFontSize(8)\n      doc.setTextColor(100, 100, 100)\n      doc.text(`Page ${i} of ${pageCount}`, pageWidth - 30, 285)\n      doc.text('Generated by Diwan Abu Alosh System', 20, 285)\n\n      // خط في أسفل الصفحة\n      doc.setDrawColor(200, 200, 200)\n      doc.line(20, 280, pageWidth - 20, 280)\n    }\n\n    // حفظ الملف\n    const fileName = `Members_Report_${new Date().toISOString().split('T')[0]}.pdf`\n    doc.save(fileName)\n  // }\n\n  // تصدير PDF باستخدام HTML (يدعم العربية بشكل أفضل)\n  const handleExportMembersPDFArabic = async () => {\n    // إنشاء عنصر HTML مؤقت\n    const printElement = document.createElement('div')\n    printElement.style.position = 'absolute'\n    printElement.style.left = '-9999px'\n    printElement.style.top = '0'\n    printElement.style.width = '210mm'\n    printElement.style.padding = '20mm'\n    printElement.style.fontFamily = 'Arial, sans-serif'\n    printElement.style.fontSize = '12px'\n    printElement.style.lineHeight = '1.4'\n    printElement.style.color = '#000'\n    printElement.style.backgroundColor = '#fff'\n    printElement.style.direction = 'rtl'\n\n    const currentDate = new Date().toLocaleDateString('en-GB', {\n      year: 'numeric',\n      month: '2-digit',\n      day: '2-digit'\n    })\n\n    printElement.innerHTML = `\n      <div style=\"text-align: center; margin-bottom: 30px;\">\n        <h1 style=\"font-size: 24px; margin: 0; color: #2c3e50;\">ديوان أبو علوش</h1>\n        <h2 style=\"font-size: 18px; margin: 10px 0; color: #34495e;\">قائمة الأعضاء</h2>\n        <hr style=\"border: 1px solid #bdc3c7; margin: 20px 0;\">\n      </div>\n\n      <div style=\"margin-bottom: 25px;\">\n        <p style=\"font-size: 14px; margin: 5px 0;\"><strong>تاريخ التقرير:</strong> ${currentDate}</p>\n      </div>\n\n      <div style=\"background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 25px; border: 1px solid #dee2e6;\">\n        <h3 style=\"margin: 0 0 10px 0; color: #495057;\">إحصائيات الأعضاء:</h3>\n        <div style=\"display: grid; grid-template-columns: 1fr 1fr; gap: 10px;\">\n          <div>• إجمالي الأعضاء: <strong>${stats.total}</strong> عضو</div>\n          <div>• الأعضاء النشطون: <strong>${stats.active}</strong> عضو</div>\n          <div>• الأعضاء المتأخرون: <strong>${stats.late}</strong> عضو</div>\n          <div>• الأعضاء غير الملتزمون: <strong>${stats.inactive}</strong> عضو</div>\n          <div>• الأعضاء الموقوفون: <strong>${stats.suspended}</strong> عضو</div>\n          <div>• الأعضاء المؤرشفون: <strong>${stats.archived}</strong> عضو</div>\n        </div>\n      </div>\n\n      <table style=\"width: 100%; border-collapse: collapse; margin-top: 20px;\">\n        <thead>\n          <tr style=\"background: #4682b4; color: white;\">\n            <th style=\"border: 1px solid #ddd; padding: 10px; text-align: center;\">اسم العضو</th>\n            <th style=\"border: 1px solid #ddd; padding: 10px; text-align: center;\">رقم الهاتف</th>\n            <th style=\"border: 1px solid #ddd; padding: 10px; text-align: center;\">حالة العضو</th>\n            <th style=\"border: 1px solid #ddd; padding: 10px; text-align: center;\">إجمالي الإيرادات</th>\n            <th style=\"border: 1px solid #ddd; padding: 10px; text-align: center;\">تاريخ الانضمام</th>\n          </tr>\n        </thead>\n        <tbody>\n          ${members.map((member, index) => `\n            <tr style=\"background: ${index % 2 === 0 ? '#f8f9fa' : '#ffffff'};\">\n              <td style=\"border: 1px solid #ddd; padding: 8px; text-align: center;\">${member.name}</td>\n              <td style=\"border: 1px solid #ddd; padding: 8px; text-align: center;\">${member.phone || 'غير محدد'}</td>\n              <td style=\"border: 1px solid #ddd; padding: 8px; text-align: center;\">${getMemberStatusText(member.status)}</td>\n              <td style=\"border: 1px solid #ddd; padding: 8px; text-align: center;\">${formatCurrency(member.incomes?.reduce((total, income) => total + income.amount, 0) || 0)}</td>\n              <td style=\"border: 1px solid #ddd; padding: 8px; text-align: center;\">${formatDate(member.createdAt)}</td>\n            </tr>\n          `).join('')}\n        </tbody>\n      </table>\n\n      <div style=\"margin-top: 30px; text-align: center; font-size: 10px; color: #6c757d;\">\n        <hr style=\"border: 1px solid #dee2e6; margin: 20px 0;\">\n        <p>تم إنشاؤه بواسطة نظام ديوان أبو علوش - ${currentDate}</p>\n      </div>\n    `\n\n    document.body.appendChild(printElement)\n\n    try {\n      const canvas = await html2canvas(printElement, {\n        scale: 2,\n        useCORS: true,\n        allowTaint: true,\n        backgroundColor: '#ffffff'\n      })\n\n      const imgData = canvas.toDataURL('image/png')\n      const pdf = new jsPDF('p', 'mm', 'a4')\n\n      const imgWidth = 210\n      const pageHeight = 295\n      const imgHeight = (canvas.height * imgWidth) / canvas.width\n      let heightLeft = imgHeight\n\n      let position = 0\n\n      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)\n      heightLeft -= pageHeight\n\n      while (heightLeft >= 0) {\n        position = heightLeft - imgHeight\n        pdf.addPage()\n        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)\n        heightLeft -= pageHeight\n      }\n\n      const fileName = `قائمة_اعضاء_الديوان_${new Date().toISOString().split('T')[0]}.pdf`\n      pdf.save(fileName)\n\n    } catch (error) {\n      console.error('خطأ في تصدير PDF:', error)\n      alert('حدث خطأ في تصدير PDF')\n    } finally {\n      document.body.removeChild(printElement)\n    }\n  }\n\n  // تغيير حالة العضو\n  const handleChangeStatus = async (member: Member, newStatus: string) => {\n    try {\n      const response = await fetch(`/api/members/${member.id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          ...member,\n          status: newStatus,\n        }),\n      })\n\n      if (!response.ok) {\n        const error = await response.json()\n        alert(error.error || 'فشل في تحديث حالة العضو')\n        return\n      }\n\n      fetchMembers()\n      fetchStats()\n    } catch (error) {\n      console.error('خطأ في تحديث حالة العضو:', error)\n      alert('حدث خطأ في تحديث حالة العضو')\n    }\n  }\n\n  // البحث المتقدم\n  const handleAdvancedSearch = (filters: { name?: string; phone?: string; address?: string; status?: string }) => {\n    // تطبيق الفلاتر المتقدمة\n    let searchTerm = ''\n    if (filters.name) searchTerm += filters.name + ' '\n    if (filters.phone) searchTerm += filters.phone + ' '\n    if (filters.address) searchTerm += filters.address + ' '\n\n    setSearch(searchTerm.trim())\n    setStatus(filters.status)\n    setPagination(prev => ({ ...prev, page: 1 }))\n  }\n\n  // إعادة تعيين البحث\n  const handleResetSearch = () => {\n    setSearch('')\n    setStatus('all')\n    setPagination(prev => ({ ...prev, page: 1 }))\n  }\n\n  const canEdit = session?.user.role !== 'VIEWER'\n  const canDelete = session?.user.role === 'ADMIN'\n\n  return (\n    <div className=\"space-y-8\">\n      {/* رأس الصفحة المحسن */}\n      <div className=\"text-center mb-8\">\n        <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl shadow-2xl p-10 text-white relative overflow-hidden\">\n          {/* خلفية متحركة */}\n          <div className=\"absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-30 animate-pulse\"></div>\n\n          {/* المحتوى */}\n          <div className=\"relative z-10\">\n            <div className=\"inline-flex items-center justify-center w-20 h-20 rounded-full mb-6 bg-white bg-opacity-20 backdrop-blur-sm\">\n              <Users className=\"w-10 h-10 text-white\" />\n            </div>\n\n            <h1 className=\"text-5xl font-black mb-4 text-white\">\n              إدارة الأعضاء\n            </h1>\n\n            <p className=\"text-xl font-semibold mb-6 text-blue-100\">\n              إدارة شاملة لأعضاء ديوان أبو علوش وبياناتهم\n            </p>\n\n            <div className=\"flex items-center justify-center space-x-2 space-x-reverse\">\n              <div className=\"h-1 w-16 rounded-full bg-white bg-opacity-60\"></div>\n              <div className=\"h-1 w-8 rounded-full bg-white bg-opacity-40\"></div>\n              <div className=\"h-1 w-16 rounded-full bg-white bg-opacity-60\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* أزرار الإجراءات */}\n      <div className=\"flex justify-center mb-8\">\n        <div className=\"bg-white rounded-2xl shadow-xl p-6 border border-gray-100\">\n          <div className=\"flex flex-wrap gap-4 justify-center\">\n            <Button\n              onClick={handleExportMembers}\n              className=\"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold\"\n            >\n              <Download className=\"w-5 h-5 ml-2\" />\n              تصدير CSV\n            </Button>\n            <Button\n              onClick={handleExportMembersPDFArabic}\n              className=\"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold\"\n            >\n              <FileDown className=\"w-5 h-5 ml-2\" />\n              تصدير PDF\n            </Button>\n            {canEdit && (\n              <Button\n                onClick={handleAddMember}\n                className=\"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold\"\n              >\n                <Plus className=\"w-5 h-5 ml-2\" />\n                إضافة عضو جديد\n              </Button>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* إحصائيات سريعة محسنة */}\n      <div className=\"grid grid-cols-2 gap-6 sm:grid-cols-3 lg:grid-cols-6\">\n        <Card className=\"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative\">\n          <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500 to-blue-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500\"></div>\n\n          <div className=\"bg-gradient-to-r from-blue-500 to-blue-600 p-1 rounded-t-xl\"></div>\n\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10\">\n            <CardTitle className=\"text-sm font-bold\" style={{ color: '#333333' }}>إجمالي الأعضاء</CardTitle>\n            <div className=\"p-3 rounded-2xl shadow-lg\" style={{ backgroundColor: '#007bff' }}>\n              <Users className=\"h-5 w-5 text-white\" />\n            </div>\n          </CardHeader>\n\n          <CardContent className=\"relative z-10\">\n            <div className=\"text-3xl font-black mb-2\" style={{ color: '#191970' }}>\n              {stats.total}\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card className=\"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative\">\n          <div className=\"absolute inset-0 bg-gradient-to-br from-green-500 to-green-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500\"></div>\n\n          <div className=\"bg-gradient-to-r from-green-500 to-green-600 p-1 rounded-t-xl\"></div>\n\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10\">\n            <CardTitle className=\"text-sm font-bold\" style={{ color: '#333333' }}>نشط</CardTitle>\n            <div className=\"p-3 rounded-2xl shadow-lg\" style={{ backgroundColor: '#28a745' }}>\n              <UserCheck className=\"h-5 w-5 text-white\" />\n            </div>\n          </CardHeader>\n\n          <CardContent className=\"relative z-10\">\n            <div className=\"text-3xl font-black mb-2\" style={{ color: '#191970' }}>\n              {stats.active}\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card className=\"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative\">\n          <div className=\"absolute inset-0 bg-gradient-to-br from-yellow-500 to-yellow-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500\"></div>\n\n          <div className=\"bg-gradient-to-r from-yellow-500 to-yellow-600 p-1 rounded-t-xl\"></div>\n\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10\">\n            <CardTitle className=\"text-sm font-bold\" style={{ color: '#333333' }}>متأخر</CardTitle>\n            <div className=\"p-3 rounded-2xl shadow-lg\" style={{ backgroundColor: '#ffc107' }}>\n              <span className=\"text-white text-sm font-bold\">⏰</span>\n            </div>\n          </CardHeader>\n\n          <CardContent className=\"relative z-10\">\n            <div className=\"text-3xl font-black mb-2\" style={{ color: '#191970' }}>\n              {stats.late}\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card className=\"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative\">\n          <div className=\"absolute inset-0 bg-gradient-to-br from-red-500 to-red-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500\"></div>\n\n          <div className=\"bg-gradient-to-r from-red-500 to-red-600 p-1 rounded-t-xl\"></div>\n\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10\">\n            <CardTitle className=\"text-sm font-bold\" style={{ color: '#333333' }}>غير ملتزم</CardTitle>\n            <div className=\"p-3 rounded-2xl shadow-lg\" style={{ backgroundColor: '#dc3545' }}>\n              <UserX className=\"h-5 w-5 text-white\" />\n            </div>\n          </CardHeader>\n\n          <CardContent className=\"relative z-10\">\n            <div className=\"text-3xl font-black mb-2\" style={{ color: '#191970' }}>\n              {stats.inactive}\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card className=\"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative\">\n          <div className=\"absolute inset-0 bg-gradient-to-br from-purple-500 to-purple-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500\"></div>\n\n          <div className=\"bg-gradient-to-r from-purple-500 to-purple-600 p-1 rounded-t-xl\"></div>\n\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10\">\n            <CardTitle className=\"text-sm font-bold\" style={{ color: '#333333' }}>موقوف</CardTitle>\n            <div className=\"p-3 rounded-2xl shadow-lg\" style={{ backgroundColor: '#800020' }}>\n              <span className=\"text-white text-sm font-bold\">⏸️</span>\n            </div>\n          </CardHeader>\n\n          <CardContent className=\"relative z-10\">\n            <div className=\"text-3xl font-black mb-2\" style={{ color: '#191970' }}>\n              {stats.suspended}\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card className=\"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative\">\n          <div className=\"absolute inset-0 bg-gradient-to-br from-gray-500 to-gray-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500\"></div>\n\n          <div className=\"bg-gradient-to-r from-gray-500 to-gray-600 p-1 rounded-t-xl\"></div>\n\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10\">\n            <CardTitle className=\"text-sm font-bold\" style={{ color: '#333333' }}>مؤرشف</CardTitle>\n            <div className=\"p-3 rounded-2xl shadow-lg\" style={{ backgroundColor: '#6c757d' }}>\n              <span className=\"text-white text-sm font-bold\">📁</span>\n            </div>\n          </CardHeader>\n\n          <CardContent className=\"relative z-10\">\n            <div className=\"text-3xl font-black mb-2\" style={{ color: '#191970' }}>\n              {stats.archived}\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* أدوات البحث والتصفية */}\n      <Card className=\"border-0 shadow-xl bg-white/80 backdrop-blur-sm\">\n        <CardContent className=\"p-6\">\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <div className=\"relative flex-1\">\n              <Search className=\"absolute right-4 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5\" />\n              <Input\n                placeholder=\"البحث في الأعضاء بالاسم أو الهاتف...\"\n                value={search}\n                onChange={(e) => setSearch(e.target.value)}\n                className=\"pr-12 h-12 text-base shadow-md\"\n              />\n            </div>\n            <StatusSelect\n              value={status}\n              onChange={(e) => setStatus(e.target.value)}\n              allLabel=\"جميع الأعضاء\"\n              className=\"shadow-md h-12\"\n            />\n            <Button\n              variant=\"info\"\n              onClick={() => setIsAdvancedSearchOpen(true)}\n              className=\"shadow-lg h-12\"\n            >\n              <Filter className=\"w-4 h-4 ml-2\" />\n              بحث متقدم\n            </Button>\n            <Button\n              variant=\"accent\"\n              onClick={handleOpenMemberSearch}\n              className=\"shadow-lg h-12\"\n            >\n              <FileText className=\"w-4 h-4 ml-2\" />\n              كشف حساب\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* جدول الأعضاء */}\n      <Card className=\"border-0 shadow-xl bg-white/90 backdrop-blur-sm overflow-hidden\">\n        <CardContent className=\"p-0\">\n          {loading ? (\n            <div className=\"flex flex-col justify-center items-center h-64 bg-gradient-to-br from-slate-50 to-slate-100\">\n              <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-slate-500 mb-4\"></div>\n              <div className=\"text-slate-600 font-medium\">جاري تحميل بيانات الأعضاء...</div>\n            </div>\n          ) : members.length === 0 ? (\n            <div className=\"flex flex-col justify-center items-center h-64 bg-gradient-to-br from-slate-50 to-slate-100\">\n              <Users className=\"h-16 w-16 text-slate-300 mb-4\" />\n              <div className=\"text-slate-600 font-medium\">لا توجد أعضاء مطابقة للبحث</div>\n            </div>\n          ) : (\n            <Table>\n              <TableHeader>\n                <TableRow style={{ backgroundColor: '#191970' }}>\n                  <TableHead style={{ backgroundColor: '#191970', color: 'white', fontWeight: '600' }}>الاسم</TableHead>\n                  <TableHead style={{ backgroundColor: '#191970', color: 'white', fontWeight: '600' }}>الهاتف</TableHead>\n                  <TableHead style={{ backgroundColor: '#191970', color: 'white', fontWeight: '600' }}>الحالة</TableHead>\n                  <TableHead style={{ backgroundColor: '#191970', color: 'white', fontWeight: '600' }}>إجمالي الإيرادات</TableHead>\n                  <TableHead style={{ backgroundColor: '#191970', color: 'white', fontWeight: '600' }}>تاريخ الإضافة</TableHead>\n                  <TableHead style={{ backgroundColor: '#191970', color: 'white', fontWeight: '600' }}>الإجراءات</TableHead>\n                </TableRow>\n              </TableHeader>\n              <TableBody>\n                {members.map((member) => (\n                  <TableRow key={member.id}>\n                    <TableCell>\n                      <div>\n                        <div className=\"font-medium\">{member.name}</div>\n                        {member.address && (\n                          <div className=\"text-sm text-gray-500 flex items-center mt-1\">\n                            <MapPin className=\"w-3 h-3 ml-1\" />\n                            {member.address}\n                          </div>\n                        )}\n                      </div>\n                    </TableCell>\n                    <TableCell>\n                      {member.phone && (\n                        <div className=\"flex items-center\">\n                          <Phone className=\"w-3 h-3 ml-1\" />\n                          {member.phone}\n                        </div>\n                      )}\n                    </TableCell>\n                    <TableCell>\n                      {canEdit ? (\n                        <select\n                          value={member.status}\n                          onChange={(e) => handleChangeStatus(member, e.target.value)}\n                          className={`px-2 py-1 text-xs font-semibold rounded-full border-0 cursor-pointer ${getMemberStatusColor(member.status)}`}\n                        >\n                          <option value=\"ACTIVE\">✅ نشط</option>\n                          <option value=\"LATE\">⏰ متأخر</option>\n                          <option value=\"INACTIVE\">❌ غير ملتزم</option>\n                          <option value=\"SUSPENDED\">⏸️ موقوف مؤقتاً</option>\n                          <option value=\"ARCHIVED\">📁 مؤرشف</option>\n                        </select>\n                      ) : (\n                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getMemberStatusColor(member.status)}`}>\n                          {getMemberStatusText(member.status)}\n                        </span>\n                      )}\n                    </TableCell>\n                    <TableCell>\n                      {formatCurrency(\n                        member.incomes?.reduce((total, income) => total + income.amount, 0) || 0\n                      )}\n                    </TableCell>\n                    <TableCell>{formatDate(member.createdAt)}</TableCell>\n                    <TableCell>\n                      <div className=\"flex items-center space-x-1 space-x-reverse\">\n                        <Button\n                          variant=\"ghost\"\n                          size=\"sm\"\n                          onClick={() => handleViewMember(member)}\n                          className=\"hover:bg-blue-50 border\"\n                          style={{\n                            color: '#0056cc',\n                            backgroundColor: 'rgba(0, 86, 204, 0.1)',\n                            borderColor: 'rgba(0, 86, 204, 0.2)',\n                            fontWeight: '600'\n                          }}\n                          title=\"عرض التفاصيل\"\n                        >\n                          <Eye className=\"w-4 h-4\" />\n                        </Button>\n                        <Button\n                          variant=\"ghost\"\n                          size=\"sm\"\n                          onClick={() => handleViewAccountStatement(member.id)}\n                          className=\"hover:bg-purple-50 border\"\n                          style={{\n                            color: '#800020',\n                            backgroundColor: 'rgba(128, 0, 32, 0.1)',\n                            borderColor: 'rgba(128, 0, 32, 0.2)',\n                            fontWeight: '600'\n                          }}\n                          title=\"كشف الحساب\"\n                        >\n                          <FileText className=\"w-4 h-4\" />\n                        </Button>\n                        {canEdit && (\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            onClick={() => handleAddMemberIncome(member)}\n                            className=\"hover:bg-yellow-50 border\"\n                            style={{\n                              color: '#b8860b',\n                              backgroundColor: 'rgba(184, 134, 11, 0.1)',\n                              borderColor: 'rgba(184, 134, 11, 0.2)',\n                              fontWeight: '600'\n                            }}\n                            title=\"إضافة إيراد\"\n                          >\n                            <DollarSign className=\"w-4 h-4\" />\n                          </Button>\n                        )}\n                        {canEdit && (\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            onClick={() => handleEditMember(member)}\n                            className=\"hover:bg-green-50 border\"\n                            style={{\n                              color: '#1e7e34',\n                              backgroundColor: 'rgba(30, 126, 52, 0.1)',\n                              borderColor: 'rgba(30, 126, 52, 0.2)',\n                              fontWeight: '600'\n                            }}\n                            title=\"تعديل\"\n                          >\n                            <Edit className=\"w-4 h-4\" />\n                          </Button>\n                        )}\n                        {canEdit && (\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            onClick={() => handleManagePassword(member)}\n                            className=\"hover:bg-purple-50 border\"\n                            style={{\n                              color: '#6f42c1',\n                              backgroundColor: 'rgba(111, 66, 193, 0.1)',\n                              borderColor: 'rgba(111, 66, 193, 0.2)',\n                              fontWeight: '600'\n                            }}\n                            title=\"إدارة كلمة المرور\"\n                          >\n                            <Key className=\"w-4 h-4\" />\n                          </Button>\n                        )}\n                        {canDelete && (\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            onClick={() => handleDeleteMember(member.id)}\n                            className=\"hover:bg-red-50 border\"\n                            style={{\n                              color: '#c82333',\n                              backgroundColor: 'rgba(200, 35, 51, 0.1)',\n                              borderColor: 'rgba(200, 35, 51, 0.2)',\n                              fontWeight: '600'\n                            }}\n                            title=\"حذف\"\n                          >\n                            <Trash2 className=\"w-4 h-4\" />\n                          </Button>\n                        )}\n                      </div>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* التصفح */}\n      {pagination.pages > 1 && (\n        <div className=\"flex justify-center space-x-2 space-x-reverse\">\n          <Button\n            variant=\"outline\"\n            disabled={pagination.page === 1}\n            onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}\n            className=\"border-2\"\n            style={{\n              borderColor: '#007bff',\n              color: pagination.page === 1 ? '#6c757d' : '#007bff',\n              backgroundColor: 'white'\n            }}\n          >\n            السابق\n          </Button>\n          <span className=\"flex items-center px-4\" style={{ color: '#333333' }}>\n            صفحة {pagination.page} من {pagination.pages}\n          </span>\n          <Button\n            variant=\"outline\"\n            disabled={pagination.page === pagination.pages}\n            onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}\n            className=\"border-2\"\n            style={{\n              borderColor: '#007bff',\n              color: pagination.page === pagination.pages ? '#6c757d' : '#007bff',\n              backgroundColor: 'white'\n            }}\n          >\n            التالي\n          </Button>\n        </div>\n      )}\n\n      {/* نافذة إضافة/تعديل العضو */}\n      <MemberDialog\n        open={isDialogOpen}\n        onOpenChange={setIsDialogOpen}\n        member={selectedMember}\n        onSuccess={handleSuccess}\n      />\n\n      {/* نافذة تفاصيل العضو */}\n      <MemberDetailsDialog\n        open={isDetailsDialogOpen}\n        onOpenChange={setIsDetailsDialogOpen}\n        member={selectedMemberForDetails}\n      />\n\n      {/* نافذة البحث المتقدم */}\n      <AdvancedSearch\n        open={isAdvancedSearchOpen}\n        onOpenChange={setIsAdvancedSearchOpen}\n        onSearch={handleAdvancedSearch}\n        onReset={handleResetSearch}\n      />\n\n      {/* نافذة كشف حساب العضو */}\n      <AccountStatementDialog\n        open={isAccountStatementOpen}\n        onOpenChange={setIsAccountStatementOpen}\n        memberId={selectedMemberForStatement}\n      />\n\n      {/* نافذة إضافة إيراد للعضو */}\n      <MemberIncomeDialog\n        open={isMemberIncomeOpen}\n        onOpenChange={setIsMemberIncomeOpen}\n        member={selectedMemberForIncome}\n        onSuccess={handleSuccess}\n      />\n\n      {/* نافذة البحث عن عضو لكشف الحساب */}\n      <MemberSearchDialog\n        open={isMemberSearchOpen}\n        onOpenChange={setIsMemberSearchOpen}\n        onSelectMember={handleSelectMemberForStatement}\n        title=\"اختيار عضو لكشف الحساب\"\n        description=\"ابحث عن العضو المطلوب لعرض كشف حسابه\"\n      />\n\n      {/* نافذة إدارة كلمة مرور العضو */}\n      <MemberPasswordDialog\n        open={isPasswordDialogOpen}\n        onOpenChange={setIsPasswordDialogOpen}\n        member={selectedMemberForPassword}\n        onSuccess={handleSuccess}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AA5CA;;;;;;;;;;;;;;;;;;;AAyEe,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxF,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,4BAA4B,8BAA8B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5F,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,2BAA2B,6BAA6B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1F,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACtF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACjC,OAAO;QACP,QAAQ;QACR,MAAM;QACN,UAAU;QACV,WAAW;QACX,UAAU;IACZ;IAEA,cAAc;IACd,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,IAAI,gBAAgB;gBACjC;gBACA;gBACA,MAAM,WAAW,IAAI,CAAC,QAAQ;gBAC9B,OAAO,WAAW,KAAK,CAAC,QAAQ;YAClC;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,QAAQ;YACrD,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAwB,MAAM,SAAS,IAAI;YACjD,WAAW,KAAK,OAAO;YACvB,cAAc,KAAK,UAAU;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;QACvC,SAAU;YACR,WAAW;QACb;IACF;IAEA,iBAAiB;IACjB,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAElB,MAAM,OAAwB,MAAM,SAAS,IAAI;YACjD,MAAM,QAAQ,KAAK,OAAO,CAAC,MAAM;YACjC,MAAM,SAAS,KAAK,OAAO,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;YACrE,MAAM,OAAO,KAAK,OAAO,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;YACjE,MAAM,WAAW,KAAK,OAAO,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;YACzE,MAAM,YAAY,KAAK,OAAO,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;YAC3E,MAAM,WAAW,KAAK,OAAO,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;YAEzE,SAAS;gBAAE;gBAAO;gBAAQ;gBAAM;gBAAU;gBAAW;YAAS;QAChE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG;QAAC;QAAQ;QAAQ,WAAW,IAAI;KAAC;IAEpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG,EAAE;IAEL,2BAA2B;IAC3B,MAAM,kBAAkB;QACtB,kBAAkB;QAClB,gBAAgB;IAClB;IAEA,sBAAsB;IACtB,MAAM,mBAAmB,CAAC;QACxB,kBAAkB;QAClB,gBAAgB;IAClB;IAEA,yBAAyB;IACzB,MAAM,mBAAmB,CAAC;QACxB,4BAA4B;QAC5B,uBAAuB;IACzB;IAEA,qBAAqB;IACrB,MAAM,6BAA6B,CAAC;QAClC,8BAA8B;QAC9B,0BAA0B;IAC5B;IAEA,qCAAqC;IACrC,MAAM,yBAAyB;QAC7B,sBAAsB;IACxB;IAEA,kCAAkC;IAClC,MAAM,uBAAuB,CAAC;QAC5B,6BAA6B;QAC7B,wBAAwB;IAC1B;IAEA,wCAAwC;IACxC,MAAM,iCAAiC,CAAC;QACtC,8BAA8B;QAC9B,sBAAsB;QACtB,0BAA0B;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,wBAAwB,CAAC;QAC7B,2BAA2B;QAC3B,sBAAsB;IACxB;IAEA,UAAU;IACV,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,QAAQ,mCAAmC;QAEhD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,IAAI,EAAE;gBACjD,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,MAAM,KAAK,IAAI;gBACrB;YACF;YAEA;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM;QACR;IACF;IAEA,mBAAmB;IACnB,MAAM,gBAAgB;QACpB,gBAAgB;QAChB;QACA;IACF;IAEA,qBAAqB;IACrB,MAAM,sBAAsB;QAC1B,MAAM,aAAa;YACjB;gBAAC;gBAAS;gBAAU;gBAAW;gBAAU;gBAAoB;aAAgB;eAC1E,QAAQ,GAAG,CAAC,CAAA,SAAU;oBACvB,OAAO,IAAI;oBACX,OAAO,KAAK,IAAI;oBAChB,OAAO,OAAO,IAAI;oBAClB,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;oBACjC,CAAC,OAAO,OAAO,EAAE,OAAO,CAAC,OAAO,SAAW,QAAQ,OAAO,MAAM,EAAE,MAAM,CAAC,EAAE,QAAQ;oBACnF,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,SAAS;iBAC5B;SACF,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC;QAEjC,MAAM,OAAO,IAAI,KAAK;YAAC,WAAW;SAAW,EAAE;YAAE,MAAM;QAA0B;QACjF,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG,IAAI,eAAe,CAAC;QAChC,KAAK,QAAQ,GAAG,CAAC,cAAc,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;QAC7E,KAAK,KAAK;IACZ;IAEA,qBAAqB;IACrB,yCAAyC;IACvC,MAAM,MAAM,IAAI,sJAAA,CAAA,UAAK,CAAC,KAAK,MAAM;IAEjC,aAAa;IACb,IAAI,OAAO,CAAC;IACZ,IAAI,WAAW,CAAC;IAEhB,2CAA2C;IAC3C,MAAM,QAAQ;IACd,MAAM,YAAY,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ;IAChD,MAAM,aAAa,IAAI,YAAY,CAAC;IACpC,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,UAAU,IAAI,GAAG;IAE9C,iBAAiB;IACjB,IAAI,WAAW,CAAC;IAChB,MAAM,WAAW;IACjB,MAAM,gBAAgB,IAAI,YAAY,CAAC;IACvC,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,aAAa,IAAI,GAAG;IAEpD,iBAAiB;IACjB,IAAI,YAAY,CAAC,GAAG,GAAG;IACvB,IAAI,IAAI,CAAC,IAAI,IAAI,YAAY,IAAI;IAEjC,kBAAkB;IAClB,IAAI,WAAW,CAAC;IAChB,MAAM,cAAc,IAAI,OAAO,kBAAkB,CAAC;IAClD,IAAI,IAAI,CAAC,CAAC,aAAa,EAAE,aAAa,EAAE,IAAI;IAE5C,sBAAsB;IACtB,IAAI,YAAY,CAAC,KAAK,KAAK;IAC3B,IAAI,IAAI,CAAC,IAAI,IAAI,YAAY,IAAI,IAAI;IACrC,IAAI,YAAY,CAAC,KAAK,KAAK;IAC3B,IAAI,IAAI,CAAC,IAAI,IAAI,YAAY,IAAI;IAEjC,IAAI,WAAW,CAAC;IAChB,IAAI,OAAO,CAAC,aAAa;IACzB,IAAI,IAAI,CAAC,uBAAuB,IAAI;IAEpC,IAAI,OAAO,CAAC,aAAa;IACzB,IAAI,WAAW,CAAC;IAChB,IAAI,IAAI,CAAC,CAAC,eAAe,EAAE,MAAM,KAAK,EAAE,EAAE,IAAI;IAC9C,IAAI,IAAI,CAAC,CAAC,gBAAgB,EAAE,MAAM,MAAM,EAAE,EAAE,IAAI;IAChD,IAAI,IAAI,CAAC,CAAC,cAAc,EAAE,MAAM,IAAI,EAAE,EAAE,KAAK;IAC7C,IAAI,IAAI,CAAC,CAAC,kBAAkB,EAAE,MAAM,QAAQ,EAAE,EAAE,KAAK;IACrD,IAAI,IAAI,CAAC,CAAC,mBAAmB,EAAE,MAAM,SAAS,EAAE,EAAE,IAAI;IACtD,IAAI,IAAI,CAAC,CAAC,kBAAkB,EAAE,MAAM,QAAQ,EAAE,EAAE,KAAK;IAErD,aAAa;IACb,IAAI,YAAY;IAChB,IAAI,WAAW,CAAC;IAChB,IAAI,OAAO,CAAC,aAAa;IAEzB,mBAAmB;IACnB,IAAI,YAAY,CAAC,IAAI,KAAK;IAC1B,IAAI,IAAI,CAAC,IAAI,YAAY,GAAG,KAAK,IAAI;IACrC,IAAI,YAAY,CAAC,KAAK,KAAK;IAE3B,MAAM,UAAU;QAAC;QAAe;QAAgB;QAAU;QAAsB;KAAY;IAC5F,MAAM,kBAAkB;QAAC;QAAI;QAAI;QAAK;QAAK;KAAI;IAE/C,QAAQ,OAAO,CAAC,CAAC,QAAQ;QACvB,IAAI,IAAI,CAAC,QAAQ,eAAe,CAAC,MAAM,EAAE;IAC3C;IAEA,eAAe;IACf,IAAI,YAAY,CAAC,GAAG,GAAG;IACvB,IAAI,YAAY,CAAC,GAAG,GAAG;IACvB,IAAI,IAAI,CAAC,IAAI,YAAY,GAAG,KAAK,YAAY;IAE7C,iBAAiB;IACjB,aAAa;IACb,IAAI,OAAO,CAAC,aAAa;IACzB,IAAI,WAAW,CAAC;IAEhB,QAAQ,OAAO,CAAC,CAAC,QAAQ;QACvB,IAAI,YAAY,KAAK;YACnB,IAAI,OAAO;YACX,YAAY;YAEZ,yCAAyC;YACzC,IAAI,OAAO,CAAC,aAAa;YACzB,IAAI,WAAW,CAAC;YAChB,IAAI,YAAY,CAAC,IAAI,KAAK;YAC1B,IAAI,IAAI,CAAC,IAAI,YAAY,GAAG,KAAK,IAAI;YACrC,IAAI,YAAY,CAAC,KAAK,KAAK;YAE3B,QAAQ,OAAO,CAAC,CAAC,QAAQ;gBACvB,IAAI,IAAI,CAAC,QAAQ,eAAe,CAAC,MAAM,EAAE;YAC3C;YAEA,IAAI,YAAY,CAAC,GAAG,GAAG;YACvB,IAAI,IAAI,CAAC,IAAI,YAAY,GAAG,KAAK,YAAY;YAC7C,aAAa;YACb,IAAI,OAAO,CAAC,aAAa;YACzB,IAAI,WAAW,CAAC;QAClB;QAEA,wBAAwB;QACxB,IAAI,QAAQ,MAAM,GAAG;YACnB,IAAI,YAAY,CAAC,KAAK,KAAK;YAC3B,IAAI,IAAI,CAAC,IAAI,YAAY,GAAG,KAAK,GAAG;QACtC;QAEA,0DAA0D;QAC1D,MAAM,aAAa,OAAO,IAAI,CAAC,MAAM,GAAG,KAAK,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,MAAM,QAAQ,OAAO,IAAI;QAC/F,MAAM,cAAc,OAAO,KAAK,IAAI;QAEpC,8BAA8B;QAC9B,MAAM,qBAAqB,CAAC;YAC1B,OAAQ;gBACN,KAAK;oBAAU,OAAO;gBACtB,KAAK;oBAAQ,OAAO;gBACpB,KAAK;oBAAY,OAAO;gBACxB,KAAK;oBAAa,OAAO;gBACzB,KAAK;oBAAY,OAAO;gBACxB;oBAAS,OAAO;YAClB;QACF;QAEA,MAAM,eAAe,mBAAmB,OAAO,MAAM;QACrD,MAAM,eAAe,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,EAAE,OAAO,CAAC,OAAO,SAAW,QAAQ,OAAO,MAAM,EAAE,MAAM;QAC3G,MAAM,iBAAiB,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,SAAS;QAElD,MAAM,UAAU;YAAC;YAAY;YAAa;YAAc;YAAc;SAAe;QAErF,QAAQ,OAAO,CAAC,CAAC,MAAM;YACrB,IAAI,IAAI,CAAC,KAAK,QAAQ,IAAI,eAAe,CAAC,SAAS,EAAE;QACvD;QAEA,eAAe;QACf,IAAI,YAAY,CAAC,KAAK,KAAK;QAC3B,IAAI,IAAI,CAAC,IAAI,YAAY,GAAG,KAAK,YAAY;QAE7C,aAAa;IACf;IAEA,cAAc;IACd,MAAM,YAAY,AAAC,IAAI,QAAQ,CAAwC,gBAAgB;IACvF,IAAK,IAAI,IAAI,GAAG,KAAK,WAAW,IAAK;QACnC,IAAI,OAAO,CAAC;QACZ,IAAI,WAAW,CAAC;QAChB,IAAI,YAAY,CAAC,KAAK,KAAK;QAC3B,IAAI,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,YAAY,IAAI;QACtD,IAAI,IAAI,CAAC,uCAAuC,IAAI;QAEpD,oBAAoB;QACpB,IAAI,YAAY,CAAC,KAAK,KAAK;QAC3B,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,IAAI;IACpC;IAEA,YAAY;IACZ,MAAM,WAAW,CAAC,eAAe,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;IAC/E,IAAI,IAAI,CAAC;IACX,IAAI;IAEJ,mDAAmD;IACnD,MAAM,+BAA+B;QACnC,uBAAuB;QACvB,MAAM,eAAe,SAAS,aAAa,CAAC;QAC5C,aAAa,KAAK,CAAC,QAAQ,GAAG;QAC9B,aAAa,KAAK,CAAC,IAAI,GAAG;QAC1B,aAAa,KAAK,CAAC,GAAG,GAAG;QACzB,aAAa,KAAK,CAAC,KAAK,GAAG;QAC3B,aAAa,KAAK,CAAC,OAAO,GAAG;QAC7B,aAAa,KAAK,CAAC,UAAU,GAAG;QAChC,aAAa,KAAK,CAAC,QAAQ,GAAG;QAC9B,aAAa,KAAK,CAAC,UAAU,GAAG;QAChC,aAAa,KAAK,CAAC,KAAK,GAAG;QAC3B,aAAa,KAAK,CAAC,eAAe,GAAG;QACrC,aAAa,KAAK,CAAC,SAAS,GAAG;QAE/B,MAAM,cAAc,IAAI,OAAO,kBAAkB,CAAC,SAAS;YACzD,MAAM;YACN,OAAO;YACP,KAAK;QACP;QAEA,aAAa,SAAS,GAAG,CAAC;;;;;;;;mFAQqD,EAAE,YAAY;;;;;;yCAMxD,EAAE,MAAM,KAAK,CAAC;0CACb,EAAE,MAAM,MAAM,CAAC;4CACb,EAAE,MAAM,IAAI,CAAC;gDACT,EAAE,MAAM,QAAQ,CAAC;4CACrB,EAAE,MAAM,SAAS,CAAC;4CAClB,EAAE,MAAM,QAAQ,CAAC;;;;;;;;;;;;;;;UAenD,EAAE,QAAQ,GAAG,CAAC,CAAC,QAAQ,QAAU,CAAC;mCACT,EAAE,QAAQ,MAAM,IAAI,YAAY,UAAU;oFACO,EAAE,OAAO,IAAI,CAAC;oFACd,EAAE,OAAO,KAAK,IAAI,WAAW;oFAC7B,EAAE,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM,EAAE;oFACrC,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,EAAE,OAAO,CAAC,OAAO,SAAW,QAAQ,OAAO,MAAM,EAAE,MAAM,GAAG;oFAC3F,EAAE,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,SAAS,EAAE;;UAEzG,CAAC,EAAE,IAAI,CAAC,IAAI;;;;;;kDAM4B,EAAE,YAAY;;IAE5D,CAAC;QAED,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,qJAAA,CAAA,UAAW,AAAD,EAAE,cAAc;gBAC7C,OAAO;gBACP,SAAS;gBACT,YAAY;gBACZ,iBAAiB;YACnB;YAEA,MAAM,UAAU,OAAO,SAAS,CAAC;YACjC,MAAM,MAAM,IAAI,sJAAA,CAAA,UAAK,CAAC,KAAK,MAAM;YAEjC,MAAM,WAAW;YACjB,MAAM,aAAa;YACnB,MAAM,YAAY,AAAC,OAAO,MAAM,GAAG,WAAY,OAAO,KAAK;YAC3D,IAAI,aAAa;YAEjB,IAAI,WAAW;YAEf,IAAI,QAAQ,CAAC,SAAS,OAAO,GAAG,UAAU,UAAU;YACpD,cAAc;YAEd,MAAO,cAAc,EAAG;gBACtB,WAAW,aAAa;gBACxB,IAAI,OAAO;gBACX,IAAI,QAAQ,CAAC,SAAS,OAAO,GAAG,UAAU,UAAU;gBACpD,cAAc;YAChB;YAEA,MAAM,WAAW,CAAC,oBAAoB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;YACpF,IAAI,IAAI,CAAC;QAEX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM;QACR,SAAU;YACR,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B;IACF;IAEA,mBAAmB;IACnB,MAAM,qBAAqB,OAAO,QAAgB;QAChD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,OAAO,EAAE,EAAE,EAAE;gBACxD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,GAAG,MAAM;oBACT,QAAQ;gBACV;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,MAAM,KAAK,IAAI;gBACrB;YACF;YAEA;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA,gBAAgB;IAChB,MAAM,uBAAuB,CAAC;QAC5B,yBAAyB;QACzB,IAAI,aAAa;QACjB,IAAI,QAAQ,IAAI,EAAE,cAAc,QAAQ,IAAI,GAAG;QAC/C,IAAI,QAAQ,KAAK,EAAE,cAAc,QAAQ,KAAK,GAAG;QACjD,IAAI,QAAQ,OAAO,EAAE,cAAc,QAAQ,OAAO,GAAG;QAErD,UAAU,WAAW,IAAI;QACzB,UAAU,QAAQ,MAAM;QACxB,cAAc,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,MAAM;YAAE,CAAC;IAC7C;IAEA,oBAAoB;IACpB,MAAM,oBAAoB;QACxB,UAAU;QACV,UAAU;QACV,cAAc,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,MAAM;YAAE,CAAC;IAC7C;IAEA,MAAM,UAAU,SAAS,KAAK,SAAS;IACvC,MAAM,YAAY,SAAS,KAAK,SAAS;IAEzC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAGnB,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAIpD,6LAAC;oCAAE,WAAU;8CAA2C;;;;;;8CAIxD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;;kDAEV,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;4BAGtC,yBACC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;0BAS3C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;wCAAoB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;kDACtE,6LAAC;wCAAI,WAAU;wCAA4B,OAAO;4CAAE,iBAAiB;wCAAU;kDAC7E,cAAA,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIrB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;oCAA2B,OAAO;wCAAE,OAAO;oCAAU;8CACjE,MAAM,KAAK;;;;;;;;;;;;;;;;;kCAKlB,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;wCAAoB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;kDACtE,6LAAC;wCAAI,WAAU;wCAA4B,OAAO;4CAAE,iBAAiB;wCAAU;kDAC7E,cAAA,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIzB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;oCAA2B,OAAO;wCAAE,OAAO;oCAAU;8CACjE,MAAM,MAAM;;;;;;;;;;;;;;;;;kCAKnB,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;wCAAoB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;kDACtE,6LAAC;wCAAI,WAAU;wCAA4B,OAAO;4CAAE,iBAAiB;wCAAU;kDAC7E,cAAA,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;;;;;;;0CAInD,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;oCAA2B,OAAO;wCAAE,OAAO;oCAAU;8CACjE,MAAM,IAAI;;;;;;;;;;;;;;;;;kCAKjB,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;wCAAoB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;kDACtE,6LAAC;wCAAI,WAAU;wCAA4B,OAAO;4CAAE,iBAAiB;wCAAU;kDAC7E,cAAA,6LAAC,2MAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIrB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;oCAA2B,OAAO;wCAAE,OAAO;oCAAU;8CACjE,MAAM,QAAQ;;;;;;;;;;;;;;;;;kCAKrB,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;wCAAoB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;kDACtE,6LAAC;wCAAI,WAAU;wCAA4B,OAAO;4CAAE,iBAAiB;wCAAU;kDAC7E,cAAA,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;;;;;;;0CAInD,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;oCAA2B,OAAO;wCAAE,OAAO;oCAAU;8CACjE,MAAM,SAAS;;;;;;;;;;;;;;;;;kCAKtB,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;wCAAoB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;kDACtE,6LAAC;wCAAI,WAAU;wCAA4B,OAAO;4CAAE,iBAAiB;wCAAU;kDAC7E,cAAA,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;;;;;;;0CAInD,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;oCAA2B,OAAO;wCAAE,OAAO;oCAAU;8CACjE,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC,oIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;wCACzC,WAAU;;;;;;;;;;;;0CAGd,6LAAC,+IAAA,CAAA,eAAY;gCACX,OAAO;gCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gCACzC,UAAS;gCACT,WAAU;;;;;;0CAEZ,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS,IAAM,wBAAwB;gCACvC,WAAU;;kDAEV,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGrC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,WAAU;;kDAEV,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;0BAQ7C,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACpB,wBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;0CAA6B;;;;;;;;;;;+BAE5C,QAAQ,MAAM,KAAK,kBACrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAI,WAAU;0CAA6B;;;;;;;;;;;6CAG9C,6LAAC,oIAAA,CAAA,QAAK;;0CACJ,6LAAC,oIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;oCAAC,OAAO;wCAAE,iBAAiB;oCAAU;;sDAC5C,6LAAC,oIAAA,CAAA,YAAS;4CAAC,OAAO;gDAAE,iBAAiB;gDAAW,OAAO;gDAAS,YAAY;4CAAM;sDAAG;;;;;;sDACrF,6LAAC,oIAAA,CAAA,YAAS;4CAAC,OAAO;gDAAE,iBAAiB;gDAAW,OAAO;gDAAS,YAAY;4CAAM;sDAAG;;;;;;sDACrF,6LAAC,oIAAA,CAAA,YAAS;4CAAC,OAAO;gDAAE,iBAAiB;gDAAW,OAAO;gDAAS,YAAY;4CAAM;sDAAG;;;;;;sDACrF,6LAAC,oIAAA,CAAA,YAAS;4CAAC,OAAO;gDAAE,iBAAiB;gDAAW,OAAO;gDAAS,YAAY;4CAAM;sDAAG;;;;;;sDACrF,6LAAC,oIAAA,CAAA,YAAS;4CAAC,OAAO;gDAAE,iBAAiB;gDAAW,OAAO;gDAAS,YAAY;4CAAM;sDAAG;;;;;;sDACrF,6LAAC,oIAAA,CAAA,YAAS;4CAAC,OAAO;gDAAE,iBAAiB;gDAAW,OAAO;gDAAS,YAAY;4CAAM;sDAAG;;;;;;;;;;;;;;;;;0CAGzF,6LAAC,oIAAA,CAAA,YAAS;0CACP,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,oIAAA,CAAA,WAAQ;;0DACP,6LAAC,oIAAA,CAAA,YAAS;0DACR,cAAA,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAe,OAAO,IAAI;;;;;;wDACxC,OAAO,OAAO,kBACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEACjB,OAAO,OAAO;;;;;;;;;;;;;;;;;;0DAKvB,6LAAC,oIAAA,CAAA,YAAS;0DACP,OAAO,KAAK,kBACX,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB,OAAO,KAAK;;;;;;;;;;;;0DAInB,6LAAC,oIAAA,CAAA,YAAS;0DACP,wBACC,6LAAC;oDACC,OAAO,OAAO,MAAM;oDACpB,UAAU,CAAC,IAAM,mBAAmB,QAAQ,EAAE,MAAM,CAAC,KAAK;oDAC1D,WAAW,CAAC,qEAAqE,EAAE,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO,MAAM,GAAG;;sEAExH,6LAAC;4DAAO,OAAM;sEAAS;;;;;;sEACvB,6LAAC;4DAAO,OAAM;sEAAO;;;;;;sEACrB,6LAAC;4DAAO,OAAM;sEAAW;;;;;;sEACzB,6LAAC;4DAAO,OAAM;sEAAY;;;;;;sEAC1B,6LAAC;4DAAO,OAAM;sEAAW;;;;;;;;;;;yEAG3B,6LAAC;oDAAK,WAAW,CAAC,yDAAyD,EAAE,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO,MAAM,GAAG;8DAC/G,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;;;;;;;;;;;0DAIxC,6LAAC,oIAAA,CAAA,YAAS;0DACP,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EACZ,OAAO,OAAO,EAAE,OAAO,CAAC,OAAO,SAAW,QAAQ,OAAO,MAAM,EAAE,MAAM;;;;;;0DAG3E,6LAAC,oIAAA,CAAA,YAAS;0DAAE,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,SAAS;;;;;;0DACvC,6LAAC,oIAAA,CAAA,YAAS;0DACR,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,iBAAiB;4DAChC,WAAU;4DACV,OAAO;gEACL,OAAO;gEACP,iBAAiB;gEACjB,aAAa;gEACb,YAAY;4DACd;4DACA,OAAM;sEAEN,cAAA,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;sEAEjB,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,2BAA2B,OAAO,EAAE;4DACnD,WAAU;4DACV,OAAO;gEACL,OAAO;gEACP,iBAAiB;gEACjB,aAAa;gEACb,YAAY;4DACd;4DACA,OAAM;sEAEN,cAAA,6LAAC,iNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;wDAErB,yBACC,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,sBAAsB;4DACrC,WAAU;4DACV,OAAO;gEACL,OAAO;gEACP,iBAAiB;gEACjB,aAAa;gEACb,YAAY;4DACd;4DACA,OAAM;sEAEN,cAAA,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;wDAGzB,yBACC,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,iBAAiB;4DAChC,WAAU;4DACV,OAAO;gEACL,OAAO;gEACP,iBAAiB;gEACjB,aAAa;gEACb,YAAY;4DACd;4DACA,OAAM;sEAEN,cAAA,6LAAC,8MAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;wDAGnB,yBACC,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,qBAAqB;4DACpC,WAAU;4DACV,OAAO;gEACL,OAAO;gEACP,iBAAiB;gEACjB,aAAa;gEACb,YAAY;4DACd;4DACA,OAAM;sEAEN,cAAA,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;wDAGlB,2BACC,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,mBAAmB,OAAO,EAAE;4DAC3C,WAAU;4DACV,OAAO;gEACL,OAAO;gEACP,iBAAiB;gEACjB,aAAa;gEACb,YAAY;4DACd;4DACA,OAAM;sEAEN,cAAA,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uCA9Ib,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;YA4JnC,WAAW,KAAK,GAAG,mBAClB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,UAAU,WAAW,IAAI,KAAK;wBAC9B,SAAS,IAAM,cAAc,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,MAAM,KAAK,IAAI,GAAG;gCAAE,CAAC;wBACtE,WAAU;wBACV,OAAO;4BACL,aAAa;4BACb,OAAO,WAAW,IAAI,KAAK,IAAI,YAAY;4BAC3C,iBAAiB;wBACnB;kCACD;;;;;;kCAGD,6LAAC;wBAAK,WAAU;wBAAyB,OAAO;4BAAE,OAAO;wBAAU;;4BAAG;4BAC9D,WAAW,IAAI;4BAAC;4BAAK,WAAW,KAAK;;;;;;;kCAE7C,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,UAAU,WAAW,IAAI,KAAK,WAAW,KAAK;wBAC9C,SAAS,IAAM,cAAc,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,MAAM,KAAK,IAAI,GAAG;gCAAE,CAAC;wBACtE,WAAU;wBACV,OAAO;4BACL,aAAa;4BACb,OAAO,WAAW,IAAI,KAAK,WAAW,KAAK,GAAG,YAAY;4BAC1D,iBAAiB;wBACnB;kCACD;;;;;;;;;;;;0BAOL,6LAAC,oJAAA,CAAA,UAAY;gBACX,MAAM;gBACN,cAAc;gBACd,QAAQ;gBACR,WAAW;;;;;;0BAIb,6LAAC,+JAAA,CAAA,UAAmB;gBAClB,MAAM;gBACN,cAAc;gBACd,QAAQ;;;;;;0BAIV,6LAAC,sJAAA,CAAA,UAAc;gBACb,MAAM;gBACN,cAAc;gBACd,UAAU;gBACV,SAAS;;;;;;0BAIX,6LAAC,kKAAA,CAAA,UAAsB;gBACrB,MAAM;gBACN,cAAc;gBACd,UAAU;;;;;;0BAIZ,6LAAC,8JAAA,CAAA,UAAkB;gBACjB,MAAM;gBACN,cAAc;gBACd,QAAQ;gBACR,WAAW;;;;;;0BAIb,6LAAC,8JAAA,CAAA,UAAkB;gBACjB,MAAM;gBACN,cAAc;gBACd,gBAAgB;gBAChB,OAAM;gBACN,aAAY;;;;;;0BAId,6LAAC,gKAAA,CAAA,UAAoB;gBACnB,MAAM;gBACN,cAAc;gBACd,QAAQ;gBACR,WAAW;;;;;;;;;;;;AAInB;GAr+BwB;;QACI,iJAAA,CAAA,aAAU;;;KADd", "debugId": null}}]}