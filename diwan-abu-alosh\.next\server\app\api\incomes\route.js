(()=>{var e={};e.id=8286,e.ids=[8286],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{N:()=>o});var s=t(13581),i=t(85663),n=t(31183);let o={providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"البريد الإلكتروني",type:"email"},password:{label:"كلمة المرور",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await n.z.user.findUnique({where:{email:e.email}});return r&&await i.Ay.compare(e.password,r.password)?{id:r.id,email:r.email,name:r.name,role:r.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>i});var s=t(96330);let i=globalThis.prisma??new s.PrismaClient},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85463:(e,r,t)=>{"use strict";t.d(r,{J8:()=>n,QZ:()=>i});var s=t(45697);s.z.object({name:s.z.string().min(2,"الاسم يجب أن يكون على الأقل حرفين"),email:s.z.string().email("البريد الإلكتروني غير صحيح"),password:s.z.string().min(6,"كلمة المرور يجب أن تكون على الأقل 6 أحرف"),role:s.z.enum(["ADMIN","DATA_ENTRY","VIEWER","MEMBER_VIEWER","GALLERY_VIEWER","MEMBER"]).default("VIEWER")});let i=s.z.object({name:s.z.string().min(2,"الاسم يجب أن يكون على الأقل حرفين"),phone:s.z.string().optional().or(s.z.literal("")).or(s.z.literal(null)),email:s.z.union([s.z.string().email("البريد الإلكتروني غير صحيح"),s.z.literal(""),s.z.literal(null),s.z.undefined()]).optional(),address:s.z.string().optional().or(s.z.literal("")).or(s.z.literal(null)),notes:s.z.string().optional().or(s.z.literal("")).or(s.z.literal(null)),photo:s.z.string().optional().or(s.z.literal("")).or(s.z.literal(null)),status:s.z.enum(["ACTIVE","LATE","INACTIVE","SUSPENDED","ARCHIVED"]).default("ACTIVE")}),n=s.z.object({amount:s.z.number().positive("المبلغ يجب أن يكون أكبر من صفر"),date:s.z.date(),source:s.z.string().min(1,"مصدر الإيراد مطلوب"),type:s.z.enum(["SUBSCRIPTION","DONATION","EVENT","OTHER"]).default("SUBSCRIPTION"),description:s.z.string().optional().nullable(),notes:s.z.string().optional().nullable(),memberId:s.z.string().optional().nullable()});s.z.object({amount:s.z.number().positive("المبلغ يجب أن يكون أكبر من صفر"),date:s.z.date(),description:s.z.string().min(1,"وصف المصروف مطلوب"),category:s.z.enum(["MEETINGS","EVENTS","MAINTENANCE","SOCIAL","GENERAL"]).default("GENERAL"),recipient:s.z.string().optional().nullable(),notes:s.z.string().optional().nullable()}),s.z.object({title:s.z.string().min(1,"عنوان النشاط مطلوب"),description:s.z.string().optional(),startDate:s.z.date(),endDate:s.z.date().optional(),location:s.z.string().optional(),organizers:s.z.string().optional(),participantIds:s.z.array(s.z.string()).optional()}),s.z.object({email:s.z.string().email("البريد الإلكتروني غير صحيح"),password:s.z.string().min(1,"كلمة المرور مطلوبة")})},94610:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>g,serverHooks:()=>I,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>b});var s={};t.r(s),t.d(s,{GET:()=>m,POST:()=>z});var i=t(96559),n=t(48088),o=t(37719),a=t(32190),l=t(19854),u=t(12909),d=t(31183),p=t(85463),c=t(85663);async function m(e){try{if(!await (0,l.getServerSession)(u.N))return a.NextResponse.json({error:"غير مصرح"},{status:401});let{searchParams:r}=new URL(e.url),t=r.get("search")||"",s=r.get("type")||"all",i=r.get("memberId")||"",n=parseInt(r.get("page")||"1"),o=parseInt(r.get("limit")||"10"),p=(n-1)*o,c={};t&&(c.OR=[{source:{contains:t}},{description:{contains:t}},{member:{name:{contains:t}}}]),"all"!==s&&(c.type=s),i&&(c.memberId=i);let[m,z]=await Promise.all([d.z.income.findMany({where:c,skip:p,take:o,orderBy:{date:"desc"},include:{member:{select:{id:!0,name:!0}},createdBy:{select:{name:!0}}}}),d.z.income.count({where:c})]);return a.NextResponse.json({incomes:m,pagination:{page:n,limit:o,total:z,pages:Math.ceil(z/o)}})}catch(e){return console.error("خطأ في جلب الإيرادات:",e),a.NextResponse.json({error:"حدث خطأ في جلب الإيرادات"},{status:500})}}async function z(e){try{let r=await (0,l.getServerSession)(u.N);if(!r)return a.NextResponse.json({error:"غير مصرح"},{status:401});if("VIEWER"===r.user.role)return a.NextResponse.json({error:"ليس لديك صلاحية لإضافة الإيرادات"},{status:403});let t=await e.json();t.date&&(t.date=new Date(t.date));let s={...t,description:t.description?.trim()||void 0,notes:t.notes?.trim()||void 0,memberId:"none"!==t.memberId&&t.memberId?t.memberId:void 0};console.log("البيانات المنظفة قبل التحقق:",s);let i=p.J8.parse(s);if(i.memberId){console.log("التحقق من العضو:",i.memberId);let e=await d.z.member.findUnique({where:{id:i.memberId}});if(!e)return console.log("العضو غير موجود:",i.memberId),a.NextResponse.json({error:"العضو المحدد غير موجود"},{status:400});console.log("العضو موجود:",e.name)}else console.log("لا يوجد عضو محدد");let n=await d.z.user.findUnique({where:{id:r.user.id}});if(!n){let e=await c.Ay.hash("123456",10);n=await d.z.user.create({data:{id:r.user.id,email:r.user.email||"<EMAIL>",name:r.user.name||"مدير النظام",password:e,role:"ADMIN"}}),console.log("تم إنشاء مستخدم جديد:",n.email)}console.log("إنشاء إيراد جديد:",{...i,createdById:r.user.id,userId:n.id,userName:n.name});let o=await d.z.income.create({data:{amount:i.amount,date:i.date,source:i.source,type:i.type,description:i.description||null,notes:i.notes||null,memberId:i.memberId||null,createdById:r.user.id},include:{member:{select:{id:!0,name:!0}}}});return a.NextResponse.json(o,{status:201})}catch(e){if(console.error("خطأ في إضافة الإيراد:",e),"ZodError"===e.name){console.log("أخطاء التحقق من البيانات:",e.errors);let r=e.errors.map(e=>`${e.path.join(".")}: ${e.message}`).join(", ");return a.NextResponse.json({error:`بيانات غير صحيحة: ${r}`,details:e.errors},{status:400})}if("P2003"===e.code)return a.NextResponse.json({error:"خطأ في المرجع: تأكد من صحة معرف العضو أو المستخدم"},{status:400});return a.NextResponse.json({error:"حدث خطأ في إضافة الإيراد: "+e.message},{status:500})}}let g=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/incomes/route",pathname:"/api/incomes",filename:"route",bundlePath:"app/api/incomes/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\api\\incomes\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:E,workUnitAsyncStorage:b,serverHooks:I}=g;function x(){return(0,o.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:b})}},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,5663,4999,3412,580,5697],()=>t(94610));module.exports=s})();