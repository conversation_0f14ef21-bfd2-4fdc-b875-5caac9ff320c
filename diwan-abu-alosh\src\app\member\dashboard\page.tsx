'use client'

import { useEffect } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useMemberAuthState } from '@/hooks/use-member-auth'
import {
  User,
  FileText,
  Camera,
  LogOut,
  Shield,
  ChevronRight,
  Clock,
  Loader2
} from 'lucide-react'

export default function MemberDashboard() {
  const { user, loading, signingOut, signOut } = useMemberAuthState()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user) {
      router.replace('/member/signin')
    }
  }, [user, loading, router])

  const handleLogout = async () => {
    await signOut()
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  const member = {
    id: user.member.id,
    name: user.member.name,
    email: user.email,
    phone: user.member.phone
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      {/* الهيدر */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                <User className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-bold text-gray-900">ديوان أبو علوش</h1>
                <p className="text-sm text-gray-600">حساب العضو</p>
              </div>
            </div>
            <Button
              onClick={handleLogout}
              disabled={signingOut}
              variant="outline"
              size="sm"
              className="text-red-600 border-red-200 hover:bg-red-50 disabled:opacity-50"
            >
              {signingOut ? (
                <>
                  <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                  جاري الخروج...
                </>
              ) : (
                <>
                  <LogOut className="w-4 h-4 ml-2" />
                  تسجيل الخروج
                </>
              )}
            </Button>
          </div>
        </div>
      </header>

      {/* المحتوى الرئيسي */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* ترحيب */}
        <div className="mb-8">
          <Card className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white border-0 shadow-xl">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                  <User className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold mb-1">مرحباً، {member.name}</h2>
                  <p className="text-indigo-100 mb-2">{member.email}</p>
                  {member.phone && (
                    <p className="text-indigo-100 text-sm">{member.phone}</p>
                  )}
                  <div className="flex items-center gap-2 mt-3 text-sm text-indigo-100">
                    <Clock className="w-4 h-4" />
                    <span>آخر دخول: {new Date().toLocaleDateString('ar-SA')}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* الخدمات المتاحة */}
        <div className="mb-8">
          <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center gap-2">
            <Shield className="w-6 h-6 text-indigo-600" />
            الخدمات المتاحة لك
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* كشف الحساب */}
            <Card className="group hover:shadow-xl transition-all duration-300 border-2 border-transparent hover:border-indigo-200 cursor-pointer">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-lg">
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <FileText className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h4 className="text-gray-900 group-hover:text-indigo-700 transition-colors">كشف حسابي</h4>
                    <p className="text-sm text-gray-600 font-normal">عرض كشف الحساب الشخصي</p>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4 text-sm">
                  يمكنك عرض جميع المساهمات والمدفوعات الخاصة بك مع إمكانية الطباعة والتصدير
                </p>
                <Button
                  onClick={() => router.push('/member/account-statement')}
                  className="w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <span>عرض كشف الحساب</span>
                  <ChevronRight className="w-4 h-4 mr-2" />
                </Button>
              </CardContent>
            </Card>

            {/* معرض الصور */}
            <Card className="group hover:shadow-xl transition-all duration-300 border-2 border-transparent hover:border-indigo-200 cursor-pointer">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-lg">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Camera className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h4 className="text-gray-900 group-hover:text-indigo-700 transition-colors">معرض الصور</h4>
                    <p className="text-sm text-gray-600 font-normal">مشاهدة صور الفعاليات</p>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4 text-sm">
                  تصفح مجموعة من الصور والذكريات من فعاليات وأنشطة الديوان
                </p>
                <Button
                  onClick={() => router.push('/member/gallery')}
                  className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <span>مشاهدة الصور</span>
                  <ChevronRight className="w-4 h-4 mr-2" />
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* معلومات إضافية */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* إرشادات */}
          <Card className="bg-blue-50 border-blue-200">
            <CardHeader>
              <CardTitle className="text-blue-800 text-lg">إرشادات الاستخدام</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm text-blue-700">
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
                  يمكنك عرض كشف حسابك الشخصي في أي وقت
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
                  تصفح صور الفعاليات والأنشطة
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
                  يمكنك طباعة أو تصدير كشف الحساب
                </li>
              </ul>
            </CardContent>
          </Card>

          {/* معلومات الاتصال */}
          <Card className="bg-gray-50 border-gray-200">
            <CardHeader>
              <CardTitle className="text-gray-800 text-lg">تحتاج مساعدة؟</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-3">
                في حالة وجود أي استفسارات أو مشاكل تقنية
              </p>
              <p className="text-sm text-gray-800 font-medium">
                يرجى التواصل مع إدارة الديوان
              </p>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
