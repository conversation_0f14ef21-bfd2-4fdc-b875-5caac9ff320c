"use strict";exports.id=3352,exports.ids=[3352],exports.modules={13861:(e,a,s)=>{s.d(a,{A:()=>r});let r=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},16023:(e,a,s)=>{s.d(a,{A:()=>r});let r=(0,s(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},34729:(e,a,s)=>{s.d(a,{T:()=>d});var r=s(60687),l=s(43210),t=s(4780);let d=l.forwardRef(({className:e,...a},s)=>(0,r.jsx)("textarea",{className:(0,t.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...a}));d.displayName="Textarea"},63143:(e,a,s)=>{s.d(a,{A:()=>r});let r=(0,s(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},70334:(e,a,s)=>{s.d(a,{A:()=>r});let r=(0,s(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},80013:(e,a,s)=>{s.d(a,{J:()=>d});var r=s(60687),l=s(43210),t=s(4780);let d=l.forwardRef(({className:e,...a},s)=>(0,r.jsx)("label",{ref:s,className:(0,t.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e),...a}));d.displayName="Label"},82570:(e,a,s)=>{s.d(a,{A:()=>r});let r=(0,s(62688).A)("folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]])},88233:(e,a,s)=>{s.d(a,{A:()=>r});let r=(0,s(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},93372:(e,a,s)=>{s.d(a,{A:()=>b});var r=s(60687),l=s(43210),t=s(29523),d=s(89667),i=s(80013),n=s(34729),o=s(63503),c=s(15079),x=s(16023),m=s(9005),h=s(11860),u=s(82570),g=s(40228),p=s(96834);function b({open:e,onOpenChange:a,onSuccess:s,defaultFolderId:b,defaultActivityId:v}){let[f,j]=(0,l.useState)(""),[y,N]=(0,l.useState)(""),[w,k]=(0,l.useState)("none"),[A,z]=(0,l.useState)("none"),[C,M]=(0,l.useState)(null),[S,R]=(0,l.useState)(null),[J,P]=(0,l.useState)(!1),[H,L]=(0,l.useState)(null),[V,D]=(0,l.useState)([]),[E,F]=(0,l.useState)([]),[T,q]=(0,l.useState)(!1),[B,G]=(0,l.useState)(!1),O=(0,l.useRef)(null),$=e=>{if(!["image/jpeg","image/jpg","image/png","image/webp"].includes(e.type))return void L("نوع الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG, WebP)");if(e.size>5242880)return void L("حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت");M(e),L(null);let a=new FileReader;a.onload=e=>{R(e.target?.result)},a.readAsDataURL(e)},I=async e=>{if(e.preventDefault(),!C)return void L("يرجى اختيار صورة");if(!f.trim())return void L("يرجى إدخال عنوان للصورة");P(!0),L(null);try{let e=new FormData;e.append("file",C),e.append("type","gallery");let r=await fetch("/api/upload",{method:"POST",body:e});if(!r.ok){let e=await r.json();throw Error(e.error||"فشل في رفع الصورة")}let l=await r.json(),t=await fetch("/api/gallery",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:f.trim(),description:y.trim()||void 0,imagePath:l.filePath,activityId:w&&"none"!==w?w:void 0,folderId:A&&"none"!==A?A:void 0})});if(!t.ok){let e=await t.json();throw Error(e.error||"فشل في إضافة الصورة إلى المعرض")}j(""),N(""),k("none"),z("none"),M(null),R(null),s(),a(!1)}catch(e){console.error("خطأ في رفع الصورة:",e),L(e.message||"حدث خطأ في رفع الصورة")}finally{P(!1)}};return(0,r.jsx)(o.lG,{open:e,onOpenChange:a,children:(0,r.jsxs)(o.Cf,{className:"max-w-[50vw] max-h-[95vh] overflow-y-auto bg-gradient-to-br from-white to-gray-50",children:[(0,r.jsx)(o.c7,{className:"pb-6 border-b border-gray-100",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"bg-diwan-100 rounded-full p-3",children:(0,r.jsx)(x.A,{className:"w-6 h-6 text-diwan-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)(o.L3,{className:"text-2xl font-bold text-diwan-700",children:"رفع صورة جديدة"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"أضف صورة جديدة إلى معرض الصور مع إمكانية ربطها بمجلد أو نشاط"})]})]})}),(0,r.jsxs)("form",{onSubmit:I,className:"space-y-8 pt-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,r.jsx)(m.A,{className:"w-5 h-5 text-diwan-600"}),(0,r.jsx)(i.J,{className:"text-lg font-semibold text-gray-800",children:"اختيار الصورة *"})]}),C?(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"relative group",children:(0,r.jsxs)("div",{className:"relative w-full h-80 rounded-2xl overflow-hidden border-2 border-diwan-200 bg-gray-100 shadow-lg",children:[(0,r.jsx)("img",{src:S,alt:"معاينة الصورة",className:"w-full h-full object-cover"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-end justify-center pb-6",children:(0,r.jsxs)(t.$,{type:"button",variant:"destructive",size:"sm",onClick:()=>{M(null),R(null),O.current&&(O.current.value="")},className:"bg-red-600 hover:bg-red-700 text-white font-semibold px-6 py-3 h-12 shadow-xl hover:shadow-2xl transition-all duration-200 rounded-xl",disabled:J,children:[(0,r.jsx)(h.A,{className:"w-5 h-5 ml-2"}),"إزالة الصورة"]})})]})}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"bg-green-100 rounded-full p-2",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-green-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-semibold text-gray-900",children:C.name}),(0,r.jsxs)("p",{className:"text-xs text-gray-600",children:[(C.size/1048576).toFixed(2)," ميجابايت • تم اختيار الصورة بنجاح"]})]})]}),(0,r.jsx)(p.E,{className:"bg-green-100 text-green-800 border-green-200 font-medium",children:"جاهز للرفع"})]})})]}):(0,r.jsxs)("div",{className:"drop-zone border-2 border-dashed border-diwan-300 rounded-2xl p-12 text-center cursor-pointer hover:border-diwan-500 hover:bg-gradient-to-br hover:from-diwan-50 hover:to-blue-50 transition-all duration-300 bg-gradient-to-br from-gray-50 via-white to-gray-50 group",onDrop:e=>{e.preventDefault();let a=e.dataTransfer.files[0];a&&$(a)},onDragOver:e=>{e.preventDefault()},onClick:()=>{O.current?.click()},children:[(0,r.jsx)("div",{className:"bg-gradient-to-br from-diwan-100 to-diwan-200 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg",children:(0,r.jsx)(m.A,{className:"w-10 h-10 text-diwan-700"})}),(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3",children:"اختر صورة للرفع"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6 text-lg",children:"اسحب الصورة هنا أو انقر للاختيار من جهازك"}),(0,r.jsxs)("div",{className:"bg-gray-50 rounded-xl p-4 mb-4",children:[(0,r.jsx)("div",{className:"flex items-center justify-center gap-3 text-sm text-gray-600 mb-3",children:(0,r.jsx)("span",{className:"font-medium",children:"الأنواع المدعومة:"})}),(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsx)(p.E,{variant:"outline",className:"text-sm font-medium bg-green-50 text-green-700 border-green-200",children:"JPG"}),(0,r.jsx)(p.E,{variant:"outline",className:"text-sm font-medium bg-blue-50 text-blue-700 border-blue-200",children:"PNG"}),(0,r.jsx)(p.E,{variant:"outline",className:"text-sm font-medium bg-purple-50 text-purple-700 border-purple-200",children:"WebP"})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2 text-sm text-gray-500",children:[(0,r.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})}),(0,r.jsx)("span",{children:"حد أقصى: 5 ميجابايت"})]})]}),(0,r.jsx)("input",{ref:O,type:"file",accept:"image/*",onChange:e=>{let a=e.target.files?.[0];a&&$(a)},className:"hidden",disabled:J})]}),(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-6",children:[(0,r.jsx)("svg",{className:"w-5 h-5 text-diwan-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z",clipRule:"evenodd"})}),(0,r.jsx)(i.J,{className:"text-lg font-semibold text-gray-800",children:"معلومات الصورة"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(i.J,{htmlFor:"title",className:"text-sm font-semibold text-gray-700 flex items-center gap-2",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-red-500 rounded-full"}),"العنوان"]}),(0,r.jsx)(d.p,{id:"title",value:f,onChange:e=>j(e.target.value),placeholder:"أدخل عنوان واضح ومميز للصورة",disabled:J,required:!0,className:"h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 text-base"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(i.J,{className:"text-sm font-semibold text-gray-700 flex items-center gap-2",children:[(0,r.jsx)(u.A,{className:"w-4 h-4 text-purple-600"}),"ربط بمجلد (اختياري)"]}),(0,r.jsxs)(c.l6,{value:A,onValueChange:e=>{z(e),"none"!==e&&k("none")},disabled:J||B,children:[(0,r.jsx)(c.bq,{className:"h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 text-base",children:(0,r.jsx)(c.yv,{placeholder:B?"جاري التحميل...":"اختر مجلد لتنظيم الصورة"})}),(0,r.jsxs)(c.gC,{children:[(0,r.jsx)(c.eb,{value:"none",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(m.A,{className:"w-4 h-4 ml-2 text-gray-400"}),"بدون ربط (صور عامة)"]})}),E.map(e=>(0,r.jsx)(c.eb,{value:e.id,children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(u.A,{className:"w-4 h-4 ml-2 text-purple-600"}),e.title]})},e.id))]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(i.J,{className:"text-sm font-semibold text-gray-700 flex items-center gap-2",children:[(0,r.jsx)(g.A,{className:"w-4 h-4 text-green-600"}),"ربط بنشاط (اختياري)"]}),(0,r.jsxs)(c.l6,{value:w,onValueChange:e=>{k(e),"none"!==e&&z("none")},disabled:J||T||"none"!==A,children:[(0,r.jsx)(c.bq,{className:"h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 text-base",children:(0,r.jsx)(c.yv,{placeholder:T?"جاري التحميل...":"none"!==A?"تم اختيار مجلد":"اختر نشاط مرتبط"})}),(0,r.jsxs)(c.gC,{children:[(0,r.jsx)(c.eb,{value:"none",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(m.A,{className:"w-4 h-4 ml-2 text-gray-400"}),"بدون ربط"]})}),V.map(e=>(0,r.jsx)(c.eb,{value:e.id,children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(g.A,{className:"w-4 h-4 ml-2 text-green-600"}),e.title]})},e.id))]})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(i.J,{htmlFor:"description",className:"text-sm font-semibold text-gray-700 flex items-center gap-2",children:[(0,r.jsx)("svg",{className:"w-4 h-4 text-blue-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z",clipRule:"evenodd"})}),"الوصف (اختياري)"]}),(0,r.jsx)(n.T,{id:"description",value:y,onChange:e=>N(e.target.value),placeholder:"أضف وصفاً تفصيلياً للصورة، المناسبة، أو الحدث المصور...",disabled:J,rows:4,className:"border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 resize-none text-base"})]})]})]}),H&&(0,r.jsx)("div",{className:"bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-xl p-5 shadow-sm",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0 bg-red-100 rounded-full p-2",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-red-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-semibold text-red-800 mb-1",children:"حدث خطأ"}),(0,r.jsx)("p",{className:"text-sm text-red-700",children:H})]})]})}),(0,r.jsxs)(o.Es,{className:"gap-4 pt-8 border-t border-gray-100",children:[(0,r.jsxs)(t.$,{type:"button",variant:"outline",onClick:()=>a(!1),disabled:J,className:"flex-1 h-14 border-2 border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 font-semibold text-base rounded-xl transition-all duration-200",children:[(0,r.jsx)(h.A,{className:"w-5 h-5 ml-2"}),"إلغاء"]}),(0,r.jsx)(t.$,{type:"submit",disabled:J||!C||!f.trim(),className:"flex-1 h-14 bg-gradient-to-r from-emerald-600 via-green-600 to-teal-600 hover:from-emerald-700 hover:via-green-700 hover:to-teal-700 text-white font-bold text-base shadow-xl hover:shadow-2xl transition-all duration-300 rounded-xl border-0 transform hover:scale-105 active:scale-95",children:J?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-6 h-6 ml-2 border-3 border-white border-t-transparent rounded-full animate-spin"}),(0,r.jsx)("span",{className:"font-bold",children:"جاري الرفع..."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"bg-white/20 rounded-full p-1 ml-2",children:(0,r.jsx)(x.A,{className:"w-5 h-5 text-white"})}),(0,r.jsx)("span",{className:"font-bold",children:"رفع الصورة"})]})})]})]})]})})}},99270:(e,a,s)=>{s.d(a,{A:()=>r});let r=(0,s(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};