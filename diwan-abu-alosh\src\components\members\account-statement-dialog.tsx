'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  DollarSign,
  TrendingUp,
  Calendar,
  FileText,
  Download,
  User,
  Phone,
  Mail,
  MapPin,
  CreditCard,
  BarChart3,
  Clock,
  CheckCircle,
  X
} from 'lucide-react'
import { formatCurrency, formatDate, getIncomeTypeText } from '@/lib/utils'
import jsPDF from 'jspdf'

interface Member {
  id: string
  name: string
  phone?: string
  email?: string
  address?: string
  photo?: string
  status: string
  createdAt: string
}

interface Transaction {
  id: string
  amount: number
  date: string
  source: string
  type: string
  description?: string
  notes?: string
  createdBy: {
    name: string
  }
}

interface AccountStatement {
  member: Member
  summary: {
    totalAmount: number
    transactionCount: number
    averageMonthlyContribution: number
    firstTransactionDate?: string
    lastTransactionDate?: string
  }
  byType: Record<string, { count: number; amount: number }>
  monthlyData: Array<{
    month: number
    monthName: string
    count: number
    amount: number
  }>
  recentTransactions: Transaction[]
  allTransactions: Transaction[]
  period: {
    startDate: string
    endDate: string
    year: number
  }
}

interface AccountStatementDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  memberId: string | null
}

export default function AccountStatementDialog({
  open,
  onOpenChange,
  memberId,
}: AccountStatementDialogProps) {
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<AccountStatement | null>(null)
  const [selectedYear, setSelectedYear] = useState<string>(new Date().getFullYear().toString())

  // console.log('AccountStatementDialog rendered - open:', open, 'memberId:', memberId, 'data:', data, 'loading:', loading)

  useEffect(() => {
    if (open && memberId) {
      // console.log('useEffect triggered - open:', open, 'memberId:', memberId, 'selectedYear:', selectedYear)
      fetchAccountStatement()
    }
  }, [open, memberId, selectedYear])

  const fetchAccountStatement = async () => {
    if (!memberId) return

    setLoading(true)
    try {
      // console.log('جاري جلب كشف الحساب للعضو:', memberId, 'للسنة:', selectedYear)
      const response = await fetch(
        `/api/members/${memberId}/account-statement?year=${selectedYear}`
      )

      if (!response.ok) {
        const errorText = await response.text()
        console.error('خطأ في الاستجابة:', errorText)
        throw new Error('فشل في جلب كشف الحساب')
      }

      const result = await response.json()
      // console.log('البيانات المستلمة:', result)
      setData(result)
    } catch (error) {
      console.error('خطأ في جلب كشف الحساب:', error)
      alert('حدث خطأ في جلب كشف الحساب: ' + (error as any)?.message)
    } finally {
      setLoading(false)
    }
  }



  const handleExport = () => {
    if (!data) return

    // إنشاء HTML للطباعة كـ PDF
    const createPrintableHTML = () => {
      return `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>كشف حساب العضو - ${data.member.name}</title>
          <style>
            @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;600;700&display=swap');

            * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
            }

            body {
              font-family: 'Noto Sans Arabic', Arial, sans-serif;
              direction: rtl;
              text-align: right;
              background: white;
              color: #333;
              line-height: 1.6;
              padding: 20px;
            }

            .header {
              text-align: center;
              margin-bottom: 20px;
              border-bottom: 2px solid #2563eb;
              padding-bottom: 15px;
            }

            .header h1 {
              font-size: 24px;
              font-weight: 700;
              color: #1e40af;
              margin-bottom: 8px;
            }

            .header .member-name {
              font-size: 18px;
              font-weight: 600;
              color: #374151;
              margin-bottom: 4px;
            }

            .header .year {
              font-size: 14px;
              color: #6b7280;
              margin-bottom: 4px;
            }

            .header .date {
              font-size: 12px;
              color: #9ca3af;
            }

            .section {
              margin-bottom: 15px;
              background: #f9fafb;
              border: 1px solid #e5e7eb;
              border-radius: 6px;
              padding: 15px;
            }

            .section-title {
              font-size: 16px;
              font-weight: 600;
              color: #1f2937;
              margin-bottom: 10px;
              border-bottom: 1px solid #e5e7eb;
              padding-bottom: 5px;
            }

            .info-grid {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 10px;
              margin-bottom: 10px;
            }

            .info-item {
              background: white;
              padding: 8px;
              border-radius: 4px;
              border: 1px solid #e5e7eb;
              font-size: 14px;
            }

            .info-label {
              font-weight: 600;
              color: #374151;
              margin-left: 8px;
            }

            .info-value {
              color: #6b7280;
            }

            .summary-grid {
              display: grid;
              grid-template-columns: repeat(2, 1fr);
              gap: 15px;
              margin-bottom: 20px;
            }

            .summary-item {
              background: white;
              padding: 15px;
              border-radius: 8px;
              border: 1px solid #e5e7eb;
              text-align: center;
            }

            .summary-label {
              font-size: 14px;
              color: #6b7280;
              margin-bottom: 8px;
            }

            .summary-value {
              font-size: 18px;
              font-weight: 600;
              color: #059669;
            }

            .monthly-grid {
              display: grid;
              grid-template-columns: repeat(4, 1fr);
              gap: 8px;
            }

            .month-item {
              background: white;
              padding: 8px;
              border-radius: 4px;
              border: 1px solid #e5e7eb;
              text-align: center;
            }

            .month-item.has-amount {
              background: #f0f9ff;
              border-color: #0ea5e9;
            }

            .month-name {
              font-weight: 600;
              color: #374151;
              margin-bottom: 3px;
              font-size: 12px;
            }

            .month-amount {
              font-size: 14px;
              font-weight: 600;
              margin-bottom: 2px;
            }

            .month-amount.positive {
              color: #059669;
            }

            .month-amount.zero {
              color: #9ca3af;
            }

            .month-count {
              font-size: 10px;
              color: #6b7280;
            }

            .type-item {
              background: white;
              padding: 12px;
              border-radius: 6px;
              border: 1px solid #e5e7eb;
              margin-bottom: 8px;
              display: flex;
              justify-content: space-between;
              align-items: center;
            }

            .type-name {
              font-weight: 600;
              color: #374151;
            }

            .type-details {
              color: #059669;
              font-weight: 600;
            }

            @media print {
              body {
                padding: 10px;
                font-size: 11px;
              }
              .header {
                margin-bottom: 15px;
                padding-bottom: 10px;
              }
              .section {
                break-inside: avoid;
                margin-bottom: 10px;
                padding: 10px;
              }
              .monthly-grid {
                grid-template-columns: repeat(6, 1fr);
                gap: 4px;
              }
              .month-item {
                padding: 4px;
              }
              .month-name {
                font-size: 10px;
              }
              .month-amount {
                font-size: 12px;
              }
              .month-count {
                font-size: 8px;
              }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>كشف حساب العضو</h1>
            <div class="member-name">${data.member.name}</div>
            <div class="year">عام ${selectedYear}</div>
            <div class="date">تاريخ التقرير: ${new Date().toLocaleDateString('en-GB')}</div>
          </div>

          <div class="section">
            <div class="section-title">معلومات العضو</div>
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">الاسم:</span>
                <span class="info-value">${data.member.name}</span>
              </div>
              ${data.member.phone ? `
              <div class="info-item">
                <span class="info-label">الهاتف:</span>
                <span class="info-value">${data.member.phone}</span>
              </div>
              ` : ''}
              ${data.member.email ? `
              <div class="info-item">
                <span class="info-label">البريد الإلكتروني:</span>
                <span class="info-value">${data.member.email}</span>
              </div>
              ` : ''}
              <div class="info-item">
                <span class="info-label">عضو منذ:</span>
                <span class="info-value">${formatDate(data.member.createdAt)}</span>
              </div>
            </div>
          </div>

          <div class="section">
            <div class="section-title">إجمالي المساهمات</div>
            <div style="text-align: center; padding: 15px;">
              <div class="summary-value" style="font-size: 28px; color: #059669; font-weight: bold;">
                ${formatCurrency(data.summary.totalAmount)}
              </div>
            </div>
          </div>



          <div class="section">
            <div class="section-title">المساهمات الشهرية - ${selectedYear}</div>
            <div class="monthly-grid">
              ${data.monthlyData.map(month => `
                <div class="month-item ${month.count > 0 ? 'has-amount' : ''}">
                  <div class="month-name">${month.monthName}</div>
                  <div class="month-amount ${month.count > 0 ? 'positive' : 'zero'}">
                    ${formatCurrency(month.amount)}
                  </div>
                  <div class="month-count">${month.count} معاملة</div>
                </div>
              `).join('')}
            </div>
          </div>

          <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; color: #6b7280; font-size: 12px;">
            تم إنشاء هذا التقرير بواسطة نظام إدارة ديوان أبو علوش
          </div>
        </body>
        </html>
      `
    }

    // إنشاء نافذة جديدة للطباعة
    const printWindow = window.open('', '_blank')
    if (!printWindow) {
      alert('يرجى السماح بفتح النوافذ المنبثقة لتصدير التقرير')
      return
    }

    const htmlContent = createPrintableHTML()
    printWindow.document.write(htmlContent)
    printWindow.document.close()

    // انتظار تحميل المحتوى ثم فتح حوار الطباعة
    printWindow.onload = () => {
      printWindow.focus()
      // إعطاء وقت إضافي لتحميل الخطوط
      setTimeout(() => {
        printWindow.print()
        // إغلاق النافذة بعد الطباعة (اختياري)
        printWindow.onafterprint = () => {
          printWindow.close()
        }
      }, 1000)
    }
  }

  // إنشاء قائمة السنوات (آخر 5 سنوات)
  const currentYear = new Date().getFullYear()
  const years = Array.from({ length: 5 }, (_, i) => currentYear - i)

  // تحديث السنة المحددة إذا كانت هناك بيانات في سنة أخرى
  useEffect(() => {
    if (data && data.summary.transactionCount === 0 && selectedYear === currentYear.toString()) {
      // إذا لم توجد بيانات في السنة الحالية، جرب السنة الماضية
      setSelectedYear((currentYear - 1).toString())
    }
  }, [data, selectedYear, currentYear])

  // لا نخفي الحوار إذا لم تكن هناك بيانات، بل نعرض رسالة مناسبة

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[50vw] max-h-[90vh] overflow-y-auto">
        <DialogHeader className="p-6 pb-4 border-b border-gray-100 bg-gradient-to-r from-diwan-50 to-blue-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-3 bg-white rounded-xl shadow-sm border border-diwan-200">
                <FileText className="w-6 h-6 text-diwan-600" />
              </div>
              <div>
                <DialogTitle className="text-xl font-bold text-gray-900">
                  كشف حساب العضو
                </DialogTitle>
                {data && (
                  <p className="text-gray-600 mt-1">
                    {data.member.name} - عام {selectedYear}
                  </p>
                )}
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Select value={selectedYear} onValueChange={setSelectedYear}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {years.map(year => (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              {data && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleExport}
                  className="text-green-600 border-green-600 hover:bg-green-50"
                >
                  <Download className="w-4 h-4 ml-1" />
                  تصدير
                </Button>
              )}

              <Button
                variant="outline"
                size="sm"
                onClick={() => onOpenChange(false)}
                className="text-gray-600 border-gray-300 hover:bg-gray-50"
              >
                <X className="w-4 h-4 ml-1" />
                خروج
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="p-6 space-y-6">
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="text-gray-500">جاري تحميل كشف الحساب...</div>
            </div>
          ) : data ? (
            <>
              {/* {console.log('عرض البيانات:', data)} */}
              {/* معلومات العضو */}
              <Card className="border-gray-200">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <User className="w-5 h-5 text-diwan-600" />
                    معلومات العضو
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="flex items-center gap-2">
                      <User className="w-4 h-4 text-gray-500" />
                      <span className="font-medium">{data.member.name}</span>
                    </div>
                    {data.member.phone && (
                      <div className="flex items-center gap-2">
                        <Phone className="w-4 h-4 text-gray-500" />
                        <span>{data.member.phone}</span>
                      </div>
                    )}
                    {data.member.email && (
                      <div className="flex items-center gap-2">
                        <Mail className="w-4 h-4 text-gray-500" />
                        <span>{data.member.email}</span>
                      </div>
                    )}
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-gray-500" />
                      <span>عضو منذ {formatDate(data.member.createdAt)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* الملخص المالي */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card className="border-green-200 bg-green-50">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium text-green-800">
                      إجمالي المساهمات
                    </CardTitle>
                    <DollarSign className="h-4 w-4 text-green-600" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-700">
                      {formatCurrency(data.summary.totalAmount)}
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-blue-200 bg-blue-50">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium text-blue-800">
                      عدد المعاملات
                    </CardTitle>
                    <CreditCard className="h-4 w-4 text-blue-600" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-blue-700">
                      {data.summary.transactionCount}
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-purple-200 bg-purple-50">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium text-purple-800">
                      متوسط شهري
                    </CardTitle>
                    <TrendingUp className="h-4 w-4 text-purple-600" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-purple-700">
                      {formatCurrency(data.summary.averageMonthlyContribution)}
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-orange-200 bg-orange-50">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium text-orange-800">
                      آخر مساهمة
                    </CardTitle>
                    <Clock className="h-4 w-4 text-orange-600" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-sm font-bold text-orange-700">
                      {data.summary.lastTransactionDate 
                        ? formatDate(data.summary.lastTransactionDate)
                        : 'لا توجد مساهمات'
                      }
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* التوزيع حسب النوع */}
              <Card className="border-gray-200">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="w-5 h-5 text-diwan-600" />
                    التوزيع حسب نوع المساهمة
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {Object.entries(data.byType).map(([type, stats]) => (
                      <div key={type} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-600">
                            {getIncomeTypeText(type)}
                          </span>
                          <Badge variant="secondary">{stats.count}</Badge>
                        </div>
                        <div className="text-lg font-bold text-diwan-600">
                          {formatCurrency(stats.amount)}
                        </div>
                        <div className="text-xs text-gray-500">
                          {((stats.amount / data.summary.totalAmount) * 100).toFixed(1)}% من الإجمالي
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* المساهمات الشهرية */}
              <Card className="border-gray-200">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="w-5 h-5 text-diwan-600" />
                    المساهمات الشهرية - {selectedYear}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
                    {data.monthlyData.map((month) => (
                      <div
                        key={month.month}
                        className={`p-3 border rounded-lg text-center ${
                          month.count > 0 ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200'
                        }`}
                      >
                        <div className="text-sm font-medium text-gray-600 mb-1">
                          {month.monthName}
                        </div>
                        <div className={`text-lg font-bold ${
                          month.count > 0 ? 'text-green-600' : 'text-gray-400'
                        }`}>
                          {formatCurrency(month.amount)}
                        </div>
                        <div className="text-xs text-gray-500">
                          {month.count} معاملة
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* آخر المعاملات */}
              <Card className="border-gray-200">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="w-5 h-5 text-diwan-600" />
                    آخر المعاملات
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {data.recentTransactions.length > 0 ? (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>التاريخ</TableHead>
                          <TableHead>المبلغ</TableHead>
                          <TableHead>المصدر</TableHead>
                          <TableHead>النوع</TableHead>
                          <TableHead>الوصف</TableHead>
                          <TableHead>المُدخِل</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {data.recentTransactions.map((transaction) => (
                          <TableRow key={transaction.id}>
                            <TableCell>
                              {formatDate(transaction.date)}
                            </TableCell>
                            <TableCell>
                              <span className="font-medium text-green-600">
                                {formatCurrency(transaction.amount)}
                              </span>
                            </TableCell>
                            <TableCell>{transaction.source}</TableCell>
                            <TableCell>
                              <Badge variant="outline">
                                {getIncomeTypeText(transaction.type)}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              {transaction.description || '-'}
                            </TableCell>
                            <TableCell className="text-sm text-gray-600">
                              {transaction.createdBy.name}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      لا توجد معاملات في هذه الفترة
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* جميع المعاملات */}
              {data.allTransactions.length > 5 && (
                <Card className="border-gray-200">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="w-5 h-5 text-diwan-600" />
                      جميع المعاملات ({data.allTransactions.length})
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="max-h-96 overflow-y-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>التاريخ</TableHead>
                            <TableHead>المبلغ</TableHead>
                            <TableHead>المصدر</TableHead>
                            <TableHead>النوع</TableHead>
                            <TableHead>الوصف</TableHead>
                            <TableHead>المُدخِل</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {data.allTransactions.map((transaction) => (
                            <TableRow key={transaction.id}>
                              <TableCell>
                                {formatDate(transaction.date)}
                              </TableCell>
                              <TableCell>
                                <span className="font-medium text-green-600">
                                  {formatCurrency(transaction.amount)}
                                </span>
                              </TableCell>
                              <TableCell>{transaction.source}</TableCell>
                              <TableCell>
                                <Badge variant="outline">
                                  {getIncomeTypeText(transaction.type)}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                {transaction.description || '-'}
                              </TableCell>
                              <TableCell className="text-sm text-gray-600">
                                {transaction.createdBy.name}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                </Card>
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                لا توجد بيانات لعرضها
              </h3>
              <p className="text-gray-500 mb-4">
                لم يتم العثور على معاملات مالية لهذا العضو في عام {selectedYear}
              </p>
              <p className="text-sm text-blue-600">
                💡 جرب تغيير السنة من القائمة المنسدلة أعلاه لعرض بيانات سنوات أخرى
              </p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
