{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { withAuth } from 'next-auth/middleware'\nimport { NextResponse } from 'next/server'\nimport type { NextRequest } from 'next/server'\n\nexport default function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl\n\n  // التعامل مع مسارات NextAuth والمشرفين\n  if (pathname.startsWith('/dashboard') ||\n      (pathname.startsWith('/api/') &&\n       !pathname.startsWith('/api/auth') &&\n       !pathname.startsWith('/api/member/'))) {\n\n    // استخدام NextAuth middleware للمشرفين\n    return withAuth(\n      function authMiddleware() {\n        return NextResponse.next()\n      },\n      {\n        callbacks: {\n          authorized: ({ token }) => !!token,\n        },\n      }\n    )(request as NextRequest, {} as Record<string, unknown>)\n  }\n\n  return NextResponse.next()\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public files (public folder)\n     */\n    '/((?!_next/static|_next/image|favicon.ico|.*\\\\.png$|.*\\\\.jpg$|.*\\\\.jpeg$|.*\\\\.gif$|.*\\\\.svg$).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;AAGe,SAAS,WAAW,OAAoB;IACrD,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,uCAAuC;IACvC,IAAI,SAAS,UAAU,CAAC,iBACnB,SAAS,UAAU,CAAC,YACpB,CAAC,SAAS,UAAU,CAAC,gBACrB,CAAC,SAAS,UAAU,CAAC,iBAAkB;QAE1C,uCAAuC;QACvC,OAAO,CAAA,GAAA,kJAAA,CAAA,WAAQ,AAAD,EACZ,SAAS;YACP,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;QAC1B,GACA;YACE,WAAW;gBACT,YAAY,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC,CAAC;YAC/B;QACF,GACA,SAAwB,CAAC;IAC7B;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}