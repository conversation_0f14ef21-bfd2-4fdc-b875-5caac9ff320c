import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { hasPermission, canAccessMember, canAccessGallery } from '@/lib/permissions'

// دالة للتحقق من المصادقة والصلاحيات
export async function checkAuth(request: NextRequest, requiredRole?: string) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { message: 'يجب تسجيل الدخول أولاً' },
        { status: 401 }
      )
    }

    // التحقق من الدور المطلوب
    if (requiredRole && session.user.role !== requiredRole && session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'غير مصرح لك بالوصول' },
        { status: 403 }
      )
    }

    return { session, user: session.user }
  } catch (error) {
    console.error('خطأ في التحقق من المصادقة:', error)
    return NextResponse.json(
      { message: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// دالة للتحقق من صلاحية معينة
export async function checkPermission(
  userId: string,
  permission: string,
  targetMemberId?: string
) {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        userPermissions: true,
        memberUser: true
      }
    })

    if (!user) {
      return { hasPermission: false, error: 'المستخدم غير موجود' }
    }

    // الحصول على الصلاحيات المخصصة
    const userPermission = user.userPermissions?.[0] // أول صلاحية للمستخدم
    const customPermissions = userPermission ? {
      canViewAllMembers: userPermission.canViewAllMembers,
      canEditMembers: userPermission.canEditMembers,
      canDeleteMembers: userPermission.canDeleteMembers,
      canViewAllIncomes: userPermission.canViewAllIncomes,
      canEditIncomes: userPermission.canEditIncomes,
      canDeleteIncomes: userPermission.canDeleteIncomes,
      canViewAllExpenses: userPermission.canViewAllExpenses,
      canEditExpenses: userPermission.canEditExpenses,
      canDeleteExpenses: userPermission.canDeleteExpenses,
      canViewGallery: userPermission.canViewGallery,
      canUploadToGallery: userPermission.canUploadToGallery,
      canDeleteFromGallery: userPermission.canDeleteFromGallery,
      canViewReports: userPermission.canViewReports,
      canExportData: userPermission.canExportData,
      canManageUsers: userPermission.canManageUsers,
      canManageSettings: userPermission.canManageSettings,
      specificMemberId: userPermission.specificMemberId || undefined,
      canViewMemberAccount: userPermission.canViewMemberAccount,
      canViewMemberDetails: userPermission.canViewMemberDetails,
      galleryReadOnly: userPermission.galleryReadOnly,
      canCreateGalleryFolders: userPermission.canCreateGalleryFolders
    } : undefined

    // التحقق من الصلاحية العامة
    const hasGeneralPermission = hasPermission(user.role, permission as keyof typeof permissions, customPermissions)

    // التحقق من صلاحيات الوصول للعضو المحدد
    if (targetMemberId && (permission === 'canViewMemberAccount' || permission === 'canViewMemberDetails')) {
      const canAccess = canAccessMember(
        user.role,
        user.id,
        targetMemberId,
        user.memberUser?.memberId,
        user.userPermissions?.[0]?.specificMemberId || undefined
      )
      
      return { 
        hasPermission: hasGeneralPermission && canAccess,
        user,
        customPermissions
      }
    }

    return { 
      hasPermission: hasGeneralPermission,
      user,
      customPermissions
    }
  } catch (error) {
    console.error('خطأ في التحقق من الصلاحية:', error)
    return { hasPermission: false, error: 'حدث خطأ في الخادم' }
  }
}

// دالة للتحقق من صلاحيات المعرض
export async function checkGalleryPermission(
  userId: string,
  action: 'view' | 'upload' | 'delete' | 'create_folder'
) {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        userPermissions: true
      }
    })

    if (!user) {
      return { hasPermission: false, error: 'المستخدم غير موجود' }
    }

    const userPermission = user.userPermissions?.[0]
    const customPermissions = userPermission ? {
      canViewGallery: userPermission.canViewGallery,
      canUploadToGallery: userPermission.canUploadToGallery,
      canDeleteFromGallery: userPermission.canDeleteFromGallery,
      galleryReadOnly: userPermission.galleryReadOnly,
      canCreateGalleryFolders: userPermission.canCreateGalleryFolders
    } : undefined

    const hasGalleryPermission = canAccessGallery(user.role, action, customPermissions as {
      canViewGallery?: boolean
      canUploadToGallery?: boolean
      canDeleteFromGallery?: boolean
      galleryReadOnly?: boolean
      canCreateGalleryFolders?: boolean
    })

    return { 
      hasPermission: hasGalleryPermission,
      user,
      customPermissions
    }
  } catch (error) {
    console.error('خطأ في التحقق من صلاحيات المعرض:', error)
    return { hasPermission: false, error: 'حدث خطأ في الخادم' }
  }
}

// دالة مساعدة للتحقق من الصلاحيات في API routes
export async function requirePermission(
  request: NextRequest,
  permission: string,
  targetMemberId?: string
) {
  const authResult = await checkAuth(request)
  
  if (authResult instanceof NextResponse) {
    return authResult
  }

  const { user } = authResult
  const permissionResult = await checkPermission(user.id, permission, targetMemberId)

  if (!permissionResult.hasPermission) {
    return NextResponse.json(
      { message: permissionResult.error || 'غير مصرح لك بهذا الإجراء' },
      { status: 403 }
    )
  }

  return { user, permissionResult }
}

// دالة للتحقق من صلاحيات المدير فقط
export async function requireAdmin(request: NextRequest) {
  const authResult = await checkAuth(request, 'ADMIN')
  
  if (authResult instanceof NextResponse) {
    return authResult
  }

  return authResult
}

// دالة للحصول على قائمة الأعضاء المسموح للمستخدم بالوصول إليها
export async function getAccessibleMembers(userId: string) {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        userPermissions: true,
        memberUser: true
      }
    })

    if (!user) {
      return []
    }

    // المدير ومدخل البيانات والمطلع يمكنهم الوصول لجميع الأعضاء
    if (['ADMIN', 'DATA_ENTRY', 'VIEWER'].includes(user.role)) {
      return await prisma.member.findMany({
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
          status: true
        },
        orderBy: { name: 'asc' }
      })
    }

    // مطلع على عضو معين
    if (user.role === 'MEMBER_VIEWER' && user.userPermissions?.[0]?.specificMemberId) {
      return await prisma.member.findMany({
        where: { id: user.userPermissions[0].specificMemberId },
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
          status: true
        }
      })
    }

    // العضو يمكنه الوصول فقط لحسابه الشخصي
    if (user.role === 'MEMBER' && user.memberUser?.memberId) {
      return await prisma.member.findMany({
        where: { id: user.memberUser.memberId },
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
          status: true
        }
      })
    }

    return []
  } catch (error) {
    console.error('خطأ في الحصول على الأعضاء المتاحين:', error)
    return []
  }
}
